#hlztpg1 {
  font-size: 14px;
  color: #303133;
}
#hlztpg1 .hlztpg-content{
  /* margin: 16px; */
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#hlztpg1 .metal-content{
  display: none;
}
#hlztpg1 .preview-content{
  padding: 12px 16px;
}
#hlztpg1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  margin-bottom: 4px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}
#hlztpg1 .second-title{
  width: 126px;
  min-width: 130px;
  display: inline-block;
  text-align: right;
}
#hlztpg1 .allergy-content .allergy-item {
  display: flex;
  align-items: center;
  margin-left: 16px;
}
#hlztpg1 .allergy-content .allergy-item .layui-input {
  width: calc(100% - 50px);
}
#hlztpg1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#hlztpg1 .rt-sr-lb {
  margin-left: 4px;
}
#hlztpg1 .gray-item {
  margin-left: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 6px;
}
#hlztpg1 .layui-inline {
  margin-top: 5px;
  margin-left: 33px;
  margin-bottom: 12px;
  display: block;
  width: calc(100% - 28px);
}
#hlztpg1 .sign-content {
  display: flex;
  align-items: center;
}
#hlztpg1 .required{
  color: #E64545;
}
#hlztpg1 .sign-content .temperature {
  display: flex;
  align-items: center;
  margin-top: 5px;
}
#hlztpg1 .sign-content .unit {
  width: 48px;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #DCDFE6;
  border-left: 0;
  line-height: 38px;
  text-align: center;
  color: #606266;
}

#hlztpg1 .sign-content .showInt {
  position: relative;
  background: #fff;
  width: 200px;
}
#hlztpg1 .sign-content .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
/* #hlztpg1 .metal-content{
  width: calc(100% - 230px);
  height: 36px;
  background: #FAFAFA;
  padding-left: 12px;
  border-radius: 3px;
} */
#hlztpg1 .item-content{
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
  margin: 6px 16px 12px 16px;
}