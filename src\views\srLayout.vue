<template>
  <div class="sr-layout" v-loading="loading"></div>
</template>

<script>
import {allSreportData} from '@/config/allSreportData.js'
import * as constant from '@/config/constant.js'
export default {
  name: 'srLayout',
  data() {
    return {
      loading: false,
      docNo: '',
      viewId: '',
      patDocId: '',
      flags: '',
      fileParam: null,
      queryParam: {},
      isDevelopment: process.env.NODE_ENV === 'development',  //开发环境
      localKey: '',  //前端加密的参数
      winName: '',
      getFromOtherUrlFlag: false,  //从第三方入口进
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
  },
  async mounted() {
    this.loading = true;
    let {param, frontendParam, winName, token} = this.$route.query || {};
    if(winName) {
      this.winName = winName;
    }
    if(token) {
      this.$store.commit('setToken', token);
    }
    // 前端缓存的key值
    this.localKey = frontendParam;
    if(frontendParam && this.isEncryptFun(frontendParam)) {
      this.localKey = this.decryptFun(frontendParam);
    } 
    if(this.localKey && !param){
      let localData = JSON.parse(window.localStorage.getItem(constant.SR_QUERY_PARAMS) || '{}');
      this.readRouteQuery(localData[this.localKey] || {});    //解析参数并缓存
    } else {
      if(param) {
        // window.localStorage.removeItem(constant.SR_QUERY_PARAMS)
        let res = await this.toLogin(param);
        if(!res) {
          return;
        }
        this.getFromOtherUrlFlag = true;
        this.readRouteQuery(res || {}, true);    //解析参数并缓存
      } else {
        this.readRouteQuery({});    //解析参数并缓存
      }
    }
    this.queryFormatHandler();
  },
  methods: {
    // 解析路由参数，解密后的参数
    readRouteQuery(decryptData, fromLogin) {
      // 从缓存里读取报告相关参数数据
      let temp = {};
      if(process.env.NODE_ENV === 'development') {
        temp = this.$route.query;
        delete temp.frontendParam;
        delete temp.param;
      } else {
        // 正式环境不读取地址栏的个别参数
        temp = {
          busType: this.$route.query.busType,
          busId: this.$route.query.busId,
          viewId: this.$route.query.viewId,
          patDocId: this.$route.query.patDocId,
          patternId: this.$route.query.patternId,
          child: this.$route.query.child,
          trace: this.$route.query.trace,
          print: this.$route.query.print,
          preview: this.$route.query.preview,
          printView: this.$route.query.printView,  //1为打印预览
          fromYunPacs: this.$route.query.fromYunPacs,
          pv: this.$route.query.pv,
          sysCode: this.$route.query.sysCode,
          webpacs: this.$route.query.webpacs,
          outParam: decodeURIComponent(this.$route.query.outParam || ''),
          type: this.$route.query.type,
          changePattern: this.$route.query.changePattern,
          newOne: this.$route.query.newOne,
          entryType: this.$route.query.entryType,
          winName: this.$route.query.winName,
          signPageMode: this.$route.query.signPageMode,
        }
      }
      if(fromLogin) {
        let decryParam = decryptData.param || {};
        delete decryptData.param;
        decryptData = {
          ...decryParam,
          ...decryptData,
        }
      } else {
        decryptData = {...temp, ...decryptData};
        let singleParam = {};
        if(temp.outParam) {
          singleParam = JSON.parse(window.atob(temp.outParam));
          if(Object.prototype.toString.call(singleParam) === '[object Object]') {
            decryptData = {...decryptData, ...singleParam};
          }
        }
      }
      let {busId = '', busType = '', docNo, reportNo, viewId = '', patDocId = '', fileParam, entryType, flags} = decryptData;
      this.busId = busId;
      this.busType = busType;
      this.docNo = docNo || reportNo || '1';
      this.viewId = !allSreportData[viewId] ? '1' : viewId;
      this.patDocId = patDocId;
      this.fileParam = fileParam;
      this.flags = flags;
      this.queryParam = decryptData;
      if(this.getFromOtherUrlFlag && !decryptData.fromYunPacs && !decryptData.webpacs && !decryptData.source) {
        decryptData.source = '1';
      }
      // entryType 0编辑1预览2打开痕迹
      if(entryType === '2') {  
        // 痕迹下viewId固定成-1
        this.viewId = '-1';
      } else if(entryType === '3' || entryType === '4') {  
        // 打印预览/静默打印下viewId固定成-2
        this.viewId = '-2';
      }

      if(!decryptData.patternId) {
        decryptData.patternId = allSreportData[this.patDocId] ? this.patDocId.substr(0, 4) : ''
      }
      // 开始缓存
      let localData = JSON.parse(window.localStorage.getItem(constant.SR_QUERY_PARAMS) || '{}');
      let curBus = this.localKey;  //解密后的
      if(!this.localKey) {
        curBus = `${busType}_${busId}_${docNo || reportNo || '1'}_${this.viewId}_${Date.now()}`;
        this.localKey = this.encryptFun(curBus);   //加密
      }
      if(this.winName) {
        decryptData.winName = this.winName;
      }
      localData[curBus] = decryptData;
      window.localStorage.setItem(constant.SR_QUERY_PARAMS, JSON.stringify(localData));
    },

    // 对解析后的参数做前提判断，然后跳到指定页面
    queryFormatHandler() {
      if(!this.fileParam && !this.viewId && !this.patDocId) {
        this.$message.error('viewId或patDocId不能为空');
        this.loading = false;
        return;
      }
      if(!allSreportData[this.viewId] && !allSreportData[this.patDocId]) {
        this.$message.error('找不到相关模板');
        this.loading = false;
        return;
      }
      if(!this.token && !this.ticket) {
        this.$message.error('ticket不能为空');
        this.loading = false;
        return;
      }
      this.toPageByDocId();
    },
    // 前往对应页面
    toPageByDocId() {
      let proName = '/sreport';  //跟云pacs本地联调时，改成这个
      // let proName = this.isDevelopment ? '' : '/sreport';
      // entryType 0编辑1预览2打开痕迹
      let {entryType} = this.queryParam;
      let {editUrl, viewUrl} = ['-1', '-2'].includes(this.viewId) ? 
        allSreportData[this.viewId] : 
        (allSreportData[this.patDocId] || allSreportData[this.viewId]);
      if (this.viewId === '3' && this.flags && this.flags[0] === '1') {
        // 知情同意书处理地址参数flags
        editUrl = allSreportData['3'].flags1Page.editUrl;
      }
      let query = this.urlParamHandler();
      let newUrl = proName + (entryType !== '0' && viewUrl ? viewUrl : editUrl) + '?' + query;
      window.location.replace(newUrl);
      this.loading = false;
    },

    urlParamHandler() {
      // 兼容知情同意书的重定向路径问题
      let [url, str = ''] = location.hash.split('#/srPage?') || [];
      str = str.replace(/&?param=[^&]*/g, '');
      str = str.replace(/&?frontendParam=[^&]*/g, '');
      str += '&frontendParam=' + (this.isEncryptFun(this.localKey) ? this.localKey : this.encryptFun(this.localKey));
      return str;
    },

    // 通过ticket进行登录
    async toLogin(postParam) {
      let params = {
        param: postParam
      }
      let res = await this.$store.dispatch('srLoginFun', {params, vm: this});
      if(!res) {
        this.loading = false;
        return false;
      }
      let data = res || {};
      let {userInfo, param = {}} = data;
      let resObj = {
        userInfo: data,
        param
      };
      return resObj || {};
    }
  }
}
</script>

<style lang="scss" scoped>
.sr-layout {
  width: 100%;
  height: 100%;
}
</style>