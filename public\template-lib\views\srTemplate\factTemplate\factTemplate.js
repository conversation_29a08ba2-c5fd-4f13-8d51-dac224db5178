$(function () {
  window.initHtmlScript = initHtmlScript;
})

// 全局变量定义
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  // 公共报告属性内容
var idAndDomMap = {};  // 节点id和值对应的关系
var isSavedReport = false; // 模版是否保存
var resultData = []; // 结果集，用于判断是否为新报告
var bzTitleClone = ''; // 病灶标题模板
var bzConClone = ''; // 病灶内容模板
var zy1Map = {
  'fact-rt-5-1-2': '同一肺叶转移',
  'fact-rt-5-2-2': '同侧不同肺叶转移',
  'fact-rt-5-3-2': '对侧肺叶转移',
  'fact-rt-6-1-2': '同侧胸膜转移',
  'fact-rt-6-2-2': '胸膜积液',
  'fact-rt-7-1-2': '心包转移',
  'fact-rt-7-2-2': '心包积液',
} // 各项转移相关
var lbjList = [{
  label: '1',
  selector: ['#fact-rt-8-1-1'],
  num: ['#fact-rt-8-1-1-1'],
  size: ['#fact-rt-8-1-1-2']
},
{
  label: '2',
  selector: ['#fact-rt-8-2-1', '#fact-rt-8-2-2'],
  num: ['#fact-rt-8-2-1-1', '#fact-rt-8-2-2-1'],
  size: ['#fact-rt-8-2-1-2', '#fact-rt-8-2-2-2']
},
{
  label: '3A',
  selector: ['#fact-rt-8-3-1'],
  num: ['#fact-rt-8-3-1-1'],
  size: ['#fact-rt-8-3-1-2']
},
{
  label: '3P',
  selector: ['#fact-rt-8-4-1'],
  num: ['#fact-rt-8-4-1-1'],
  size: ['#fact-rt-8-4-1-2']

},
{
  label: '4',
  selector: ['#fact-rt-8-5-1', '#fact-rt-8-5-2'],
  num: ['#fact-rt-8-5-1-1', '#fact-rt-8-5-2-1'],
  size: ['#fact-rt-8-5-1-2', '#fact-rt-8-5-2-2']

},
{
  label: '5',
  selector: ['#fact-rt-8-6-1'],
  num: ['#fact-rt-8-6-1-1'],
  size: ['#fact-rt-8-6-1-2']

},
{
  label: '6',
  selector: ['#fact-rt-8-7-1'],
  num: ['#fact-rt-8-7-1-1'],
  size: ['#fact-rt-8-7-1-2']

},
{
  label: '7',
  selector: ['#fact-rt-8-8-1'],
  num: ['#fact-rt-8-8-1-1'],
  size: ['#fact-rt-8-8-1-2']
},
{
  label: '8',
  selector: ['#fact-rt-8-9-1', '#fact-rt-8-9-2'],
  num: ['#fact-rt-8-9-3'],
  size: ['#fact-rt-8-9-4'],
  subLabel: '食管旁'
},
{
  label: '9',
  selector: ['#fact-rt-8-10-1', '#fact-rt-8-10-2'],
  num: ['#fact-rt-8-10-3'],
  size: ['#fact-rt-8-10-4'],
  subLabel: '肺韧带'
},
{
  label: '10',
  selector: ['#fact-rt-8-11-1', '#fact-rt-8-11-2'],
  num: ['#fact-rt-8-11-3'],
  size: ['#fact-rt-8-11-4'],
  subLabel: '肺门'
},
{
  label: '11-14',
  selector: ['#fact-rt-8-12-1', '#fact-rt-8-12-2'],
  num: ['#fact-rt-8-12-3'],
  size: ['#fact-rt-8-12-4'],
  subLabel: '叶间、叶、段、亚段'
},
{
  label: '其他',
  selector: ['#fact-rt-8-13'],
  isOther: true,
  otherSelector: ['#fact-rt-8-13-1'],
  num: ['#fact-rt-8-13-1-1'],
  size: ['#fact-rt-8-13-1-2']
}
] // 淋巴结相关
// DOM选择器缓存
var DOM_CACHE = {
  fact1: null,
  bzWrap: null,
  bzTitLs: null,
  bzCon: null,
  bzItems: null,
  bzTitItems: null,

  // 初始化缓存
  init: function () {
    this.fact1 = $('#fact1');
    this.bzWrap = this.fact1.find('.bz-wrap');
    this.bzTitLs = this.bzWrap.find('.bz-tit-ls');
    this.bzCon = this.bzWrap.find('.bz-con');
    this.refresh();
  },

  // 刷新动态元素缓存
  refresh: function () {
    if (this.bzTitLs) {
      this.bzTitItems = this.bzTitLs.find('.bz-tit-i');
    }
    if (this.bzCon) {
      this.bzItems = this.bzCon.find('.bz-item');
    }
  }
};

// 公共工具函数
var UTILS = {
  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   * createUUidFun全局导入,不需要再次定义
   */
  generateId: createUUidFun,

  /**
   * 设置表单控件值的通用方法
   * @param {jQuery} $element - jQuery元素
   * @param {string} value - 要设置的值
   */
  setFormValue: function ($element, value) {
    if (!$element || !$element.length) return;

    var tagName = $element.prop('tagName').toLowerCase();
    var type = $element.attr('type');

    if (tagName === 'input') {
      if (type === 'radio' || type === 'checkbox') {
        $element.prop('checked', $element.val() === value);
      } else {
        $element.val(value);
      }
    } else if (tagName === 'select') {
      $element.val(value);
    } else if (tagName === 'textarea') {
      $element.val(value);
    } else {
      $element.text(value);
    }
  },

  /**
   * 获取表单控件值的通用方法
   * @param {jQuery} $element - jQuery元素
   * @returns {string} 表单控件的值
   */
  getFormValue: function ($element) {
    if (!$element || !$element.length) return '';

    var tagName = $element.prop('tagName').toLowerCase();
    var type = $element.attr('type');

    if (tagName === 'input') {
      if (type === 'radio' || type === 'checkbox') {
        return $element.is(':checked') ? $element.val() : '';
      } else {
        return $element.val() || '';
      }
    } else if (tagName === 'select') {
      return $element.val() || '';
    } else if (tagName === 'textarea') {
      return $element.val() || '';
    } else {
      return $element.text() || '';
    }
  },

  /**
   * 切换元素的激活状态
   * @param {jQuery} $elements - 要操作的元素集合
   * @param {jQuery} $activeElement - 要激活的元素
   * @param {string} activeClass - 激活状态的CSS类名
   */
  toggleActiveState: function ($elements, $activeElement, activeClass) {
    activeClass = activeClass || 'act';
    $elements.removeClass(activeClass);
    if ($activeElement && $activeElement.length) {
      $activeElement.addClass(activeClass);
    }
  },

  /**
   * 处理rtStructure的idAndDomMap数据的通用方法
   * @param {string} id - 元素ID
   * @param {Object} baseConfig - 基础配置
   * @param {Object} oldIdAndDom - 旧的ID和DOM映射数据
   * @returns {Object} 处理后的配置对象
   */
  createIdAndDomMapItem: function (id, baseConfig, oldIdAndDom) {
    return {
      id: id,
      desc: baseConfig.desc || '',
      name: baseConfig.name || '',
      pid: baseConfig.pid || '',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldIdAndDom && oldIdAndDom.value ? oldIdAndDom.value : (baseConfig.defaultValue || ''),
      wt: '',
      vt: '',
      itemList: oldIdAndDom ? oldIdAndDom.itemList : undefined,
      lastVal: oldIdAndDom ? oldIdAndDom.lastVal : undefined
    };
  }
};

/**
 * 初始化HTML脚本
 * @param {HTMLElement} ele - 当前元素
 */
function initHtmlScript(ele) {
  curElem = $(ele);

  // 初始化DOM缓存
  DOM_CACHE.init();

  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList);
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  // true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  // true确认报告时不上传pdf
      checkboxSort: false,  // true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  // 转成pdf的区域，默认是整个页面
    };
  }

  if (rtStructure) {
    // 初始化配置数据
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  // 报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  // 检查信息
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false; // 是否保存
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || []) : []; // 结果集
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};


    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      // 预览页面的特殊处理
      previewHandler();

    } else {
      // 获取推导结果

      pageInit();
      // if (!isSavedReport) {
      //   getImpressionText();
      // }
      // 使用事件委托确保新添加的病灶中的表单元素也能触发getImpressionText
      $(document).on('change', '#fact1 .rt-sr-w', function () {
        getImpressionText();
      });
    }
  }
}
/**
 * 创建描述和印象文本处理器
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescription();
  rtStructure.impression = getImpressionText();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  // 检查部位，多个用','号隔开
    reporter: '', // 报告医生
    reportDate: '',  // 报告日期
    examTechnician: '', // 检查技师
    priReporter: '',  // 初步报告医生
    priReporterNo: '',  // 初步报告医生流水号
    affirmReporter: '',  // 审核医生
    affirmReporterNo: '',  // 审核医生流水号
    affirmDate: '',  // 审核日期
    reDiagReporter: '',  // 复诊报告医生
    reDiagReporterNo: '',  // 复诊报告医生流水号，多个逗号隔开
    examParam: '',  // 检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  // 肉眼所见
    rptImageList: rptImageList,   // 报告图像
  };
}
function previewHandler() {
  renderBzList()
  renderFzyl()
  renderTcxmzy()
  renderXbzy()
  renderLbjzy()
  renderZtpg()
  renderYczy()
  renderQtzx()
}
/**
 * 渲染病灶列表
 * 根据 resultData 中的病灶数据生成预览HTML
 */
function renderBzList() {
  let bzList = resultData.find(item => item.id === 'fact-rtlist-1') || {}
  let html = ''

  if (bzList.child) {
    bzList.child.forEach(bzItem => {
      // 获取病灶类型信息
      let typeInfo = getBzTypeInfo(bzItem);
      // 获取病灶数目信息
      let numberInfo = getBzNumberInfo(bzItem);
      // 获取病灶位置信息
      let locationInfo = getBzLocationInfo(bzItem);
      // 获取病灶大小信息
      let sizeInfo = getBzSizeInfo(bzItem);
      // 获取病灶形态信息
      let densityInfo = getBzDensityInfo(bzItem);
      // 获取病灶边缘信息
      let edgeInfo = getBzEdgeInfo(bzItem);
      // 获取平扫密度信息
      let scanDensityInfo = getBzScanDensityInfo(bzItem);
      // 获取增强扫描信息
      let enhancementInfo = getBzEnhancementInfo(bzItem);
      // 获取内部结构信息
      let internalStructureInfo = getBzInternalStructureInfo(bzItem);
      // 获取病灶周围继发及伴随征象信息
      let secondarySignsInfo = getBzSecondarySignsInfo(bzItem);
      // 获取局部侵犯信息
      let localInvasionInfo = getBzLocalInvasionInfo(bzItem);

      html += `
      <div class="preview-item">
        <div class="preview-item-label pt-10">
          ${bzItem.val}：
        </div>
        <div class="preview-item-content base-card p-8">
            ${typeInfo ? `<div class="flex items-center ">
                <div class="w-80 text-r fw-600">类型：</div>
                <div class="f-1">${typeInfo}</div>
            </div>` : ''}
            ${numberInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">病灶数目：</div>
                <div class="f-1">${numberInfo}</div>
            </div>` : ''}
            ${locationInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">位置：</div>
                <div class="f-1">${locationInfo}</div>
            </div>` : ''}
            ${sizeInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">大小约：</div>
                <div class="f-1">${sizeInfo}</div>
            </div>` : ''}
            ${densityInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">形态：</div>
                <div class="f-1">${densityInfo}</div>
            </div>` : ''}
            ${edgeInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">边缘：</div>
                <div class="f-1">${edgeInfo}</div>
            </div>` : ''}
            ${(scanDensityInfo.scanInfo || scanDensityInfo.densityInfo) ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">平扫：</div>
                <div class="f-1 flex-column">
                  ${scanDensityInfo.scanInfo ? `<div>${scanDensityInfo.scanInfo}</div>` : ''}
                  ${scanDensityInfo.densityInfo ? `<div>密度：${scanDensityInfo.densityInfo}</div>` : ''}
                </div>
            </div>` : ''}
            ${(enhancementInfo.scanInfo || enhancementInfo.enhancementInfo) ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">增强扫描：</div>
                <div class="f-1 flex-column">
                  ${enhancementInfo.scanInfo ? `<div>${enhancementInfo.scanInfo}</div>` : ''}
                  ${enhancementInfo.enhancementInfo ? `<div>${enhancementInfo.enhancementInfo}</div>` : ''}
                </div>
            </div>` : ''}
            ${internalStructureInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">内部结构：</div>
                <div class="f-1">${internalStructureInfo}</div>
            </div>` : ''}
            ${secondarySignsInfo ? `<div class="flex items-center">
                <div class="text-r fw-600" style="width:177px">病灶周围继发及伴随征象：</div>
                <div class="f-1">${secondarySignsInfo}</div>
            </div>` : ''}
            ${localInvasionInfo ? `<div class="flex items-center">
                <div class="w-80 text-r fw-600">局部侵犯：</div>
                <div class="f-1">${localInvasionInfo}</div>
            </div>` : ''}
        </div>
      </div>
      `
    })
  }
  // 替换掉
  // $('#render-bzlist').html(html)
  $('#render-bzlist').append(html)
}
/**
 * 渲染肺转移瘤信息
 * 处理复杂的嵌套数据结构，将每个子项单独一行显示
 */
function renderFzyl() {
  let fzyl = resultData.find(item => item.id === 'fact-rt-5') || {}
  let html = ''
  if (fzyl.child) {
    html += ` <div class="preview-item">
                  <div class="preview-item-label">肺内转移瘤：</div>
                  <div class="preview-item-content">
                     ${getFzylContent(fzyl)}
                  </div>
            </div>`
  }
  $('#render-fzyl').append(html)
}
function renderTcxmzy() {
  let tcxmzy = resultData.find(item => item.id === 'fact-rt-6') || {}
  let html = ''
  if (tcxmzy.child) {
    html += ` <div class="preview-item">
                  <div class="preview-item-label">
                    同侧胸膜转移：
                  </div>
                  <div class="preview-item-content">
                  ${getFzylContent(tcxmzy)}
                  </div>
            </div>`
  }
  $('#render-tcxmzy').append(html)
}
function renderXbzy() {
  let xbzy = resultData.find(item => item.id === 'fact-rt-7') || {}
  let html = ''
  if (xbzy.child) {
    html += ` <div class="preview-item">
                  <div class="preview-item-label">
                    心包转移：
                  </div>
                  <div class="preview-item-content">
                  ${getFzylContent(xbzy)}
                  </div>
            </div>`
  }
  $('#render-xbzy').append(html)
}


function renderLbjzy() {
  let lbjzy = resultData.find(item => item.id === 'fact-rt-8') || {}
  let html = ''
  if (lbjzy.child) {
    html += ` <div class="preview-item">
                  <div class="preview-item-label">
                    淋巴结转移：
                  </div>
                  <div class="preview-item-content">
                    ${getBzLymphNodeMetastasisInfo(lbjzy)}
                  </div>
            </div>`
  }
  $('#render-lbjzy').append(html)

}
/**
 * 获取整体评估信息
 * @param {Object} ztpgItem - 整体评估数据项
 * @returns {String} 格式化的整体评估信息
 */
function getZtpgInfo(ztpgItem) {
  if (!ztpgItem.child || !ztpgItem.child.length) {
    return '';
  }

  let contentHtml = '';

  // 遍历每个评估项目
  ztpgItem.child.forEach(item => {
    if (!item.child || !item.child.length) {
      return;
    }

    // 获取项目名称（去掉冒号）
    let itemName = item.desc || item.name || '';
    if (itemName.endsWith('：')) {
      itemName = itemName.slice(0, -1);
    }

    // 获取状态值（直接从child[0].val中获取）
    let statusValue = item.child[0].val || '';

    if (statusValue) {
      if (statusValue === '异常') {
        // 查找异常描述
        let descNode = item.child[0].child && item.child[0].child.find(desc =>
          desc.val && desc.val.trim() !== ''
        );

        if (descNode && descNode.val) {
          contentHtml += `${itemName}：异常（${descNode.val}）；<br/>`;
        } else {
          contentHtml += `${itemName}：异常；<br/>`;
        }
      } else {
        // 显示原值（如通畅、未见积液等）
        contentHtml += `${itemName}：${statusValue}；<br/>`;
      }
    }
  });

  return contentHtml;
}

function renderZtpg() {
  let ztpg = resultData.find(item => item.id === 'fact-rt-9') || {}
  let html = ''
  if (ztpg.child) {
    let ztpgContent = getZtpgInfo(ztpg);
    html += ` <div class="preview-item">
                  <div class="preview-item-label">
                    整体评估：
                  </div>
                  <div class="preview-item-content">
                    ${ztpgContent}
                  </div>
            </div>`
  }
  $('#render-ztpg').append(html)

}
/**
 * 获取远处转移信息
 * @param {Object} yczyItem - 远处转移数据项
 * @returns {String} 格式化的远处转移信息
 */
function getYczyInfo(yczyItem) {
  if (!yczyItem.child || !yczyItem.child.length) {
    return '';
  }

  let contentHtml = '';

  // 遍历每个转移部位
  yczyItem.child.forEach(site => {
    let siteName = site.val || ''; // 部位名称
    let status = ''; // 单发/多发状态
    let description = ''; // 影像所见描述

    // 如果有child数组，从中获取状态和描述信息
    if (site.child && site.child.length > 0) {
      site.child.forEach(item => {
        // 查找单发/多发状态
        if (item.val === '单发' || item.val === '多发') {
          status = item.val;
        }
        // 查找影像所见描述
        else if (item.desc && item.desc.includes('影像所见描述') && item.val && item.val.trim() !== '') {
          description = item.val;
        }
      });
    }

    // 构建显示文本
    if (siteName) {
      let displayText = siteName;

      // 添加状态信息（如果有）
      if (status) {
        displayText += `（${status}`;

        // 添加描述信息（如果有）
        if (description) {
          displayText += `；${description}`;
        }

        displayText += '）';
      }

      contentHtml += `<div>${displayText}</div>`;
    }
  });

  return contentHtml;
}

function renderYczy() {
  let yczy = resultData.find(item => item.id === 'fact-rt-10') || {}
  let html = ''
  console.log(yczy);

  if (yczy.child) {
    let yczyContent = getYczyInfo(yczy);
    html += ` <div class="preview-item">
                  <div class="preview-item-label">
                    远处转移：
                  </div>
                  <div class="preview-item-content">
                    ${yczyContent}
                  </div>
            </div>`
  } else {
    html += `<div class="preview-item">
                  <div class="preview-item-label">
                    远处转移：
                  </div>
                  <div class="preview-item-content">无</div>
                    </div>
     </div>`
  }
  $('#render-yczy').append(html)

}

function renderQtzx() {
  let qtzx = resultData.find(item => item.id === 'fact-rt-11') || {}
  let html = ''
  if (qtzx.child) {
    html += ` <div class="preview-item">
                  <div class="preview-item-label">
                    其他：
                  </div>
                  <div class="preview-item-content">
                    ${qtzx.child[0].val}
                  </div>
            </div>`
  }
  $('#render-qtzx').append(html)
}
/**
 * 获取淋巴结转移信息
 * @param {Object} lbjzyItem - 淋巴结转移数据项
 * @returns {String} 格式化的淋巴结转移信息
 */
function getBzLymphNodeMetastasisInfo(lbjzyItem) {
  if (!lbjzyItem.child || !lbjzyItem.child.length) {
    return '';
  }

  // 组别到部位名称的映射
  const groupNameMapping = {
    'fact-rt-8-9': '食管旁',   // 8组
    'fact-rt-8-10': '肺韧带', // 9组
    'fact-rt-8-11': '肺门',   // 10组
    'fact-rt-8-12': '叶间、叶、段、亚段' // 11-14组
  };

  let contentHtml = '';

  // 遍历每个淋巴结组
  lbjzyItem.child.forEach(group => {
    if (!group.child || !group.child.length) {
      return;
    }

    // 处理"其他"淋巴结组的特殊情况
    if (group.id === 'fact-rt-8-13') {
      let nodeName = '';
      let count = '';
      let maxDiameter = '';

      // "其他"组的数据结构：名称、数量和最大短径直接在group.child中
      group.child.forEach(item => {
        if (item.id === 'fact-rt-8-13-1') {
          nodeName = item.val || '';
        } else if (item.desc && item.desc.includes('数量')) {
          count = item.val || '';
        } else if (item.desc && item.desc.includes('最大短径')) {
          maxDiameter = item.val || '';
        }
      });

      if (count && count.trim() !== '' && maxDiameter && maxDiameter.trim() !== '') {
        contentHtml += `<div>${nodeName}，${count}枚，最大短径：${maxDiameter}mm</div>`;
      }
      return;
    }

    // 处理8-14组的特殊数据结构
    if (groupNameMapping[group.id]) {
      let count = '';
      let maxDiameter = '';
      let leftSelected = false;
      let rightSelected = false;

      // 在group.child中直接查找数量、最大短径和左右侧信息
      group.child.forEach(item => {
        // 查找左右侧选择
        if (item.val === '左') {
          leftSelected = true;
        } else if (item.val === '右') {
          rightSelected = true;
        }
        // 查找数量（通常desc包含"数量"）
        else if (item.desc && item.desc.includes('数量') && item.val) {
          count = item.val;
        }
        // 查找最大短径（通常desc包含"最大短径"）
        else if (item.desc && item.desc.includes('最大短径') && item.val) {
          maxDiameter = item.val;
        }
      });

      if (count && count.trim() !== '' && maxDiameter && maxDiameter.trim() !== '') {
        let sidePrefix = '';
        if (leftSelected && rightSelected) {
          sidePrefix = '双侧';
        } else if (leftSelected) {
          sidePrefix = '左侧';
        } else if (rightSelected) {
          sidePrefix = '右侧';
        } else {
          sidePrefix = '双侧';
        }

        const nodeName = groupNameMapping[group.id];
        contentHtml += `<div>${sidePrefix}${nodeName}，${count}枚，最大短径：${maxDiameter}mm</div>`;
      }
      return;
    }

    // 处理1-4组的常规嵌套结构
    group.child.forEach(lymphNode => {
      if (!lymphNode.child || !lymphNode.child.length) {
        return;
      }

      let nodeName = lymphNode.desc || '';
      let count = '';
      let maxDiameter = '';
      let leftSide = false;
      let rightSide = false;

      lymphNode.child.forEach(item => {
        if (item.desc && item.desc.includes('数量')) {
          count = item.val || '';
        } else if (item.desc && item.desc.includes('最大短径')) {
          maxDiameter = item.val || '';
        } else if (item.desc && item.desc.includes('左侧')) {
          leftSide = item.val === 'true' || item.val === true;
        } else if (item.desc && item.desc.includes('右侧')) {
          rightSide = item.val === 'true' || item.val === true;
        }
      });

      if (count && count.trim() !== '' && maxDiameter && maxDiameter.trim() !== '') {
        let sidePrefix = '';
        if (leftSide && rightSide) {
          sidePrefix = '双侧';
        } else if (leftSide) {
          sidePrefix = '左侧';
        } else if (rightSide) {
          sidePrefix = '右侧';
        }
        contentHtml += `<div>${sidePrefix}${nodeName}，${count}枚，最大短径：${maxDiameter}mm</div>`;
      }
    });
  });

  return contentHtml;
}

/**
 * 获取肺转移瘤内容信息
 * @param {Object} fzylItem - 肺转移瘤数据项
 * @returns {String} 格式化的肺转移瘤内容
 */
function getFzylContent(fzylItem) {
  if (!fzylItem.child || !fzylItem.child.length) {
    return '';
  }

  let contentHtml = '';
  // 遍历每个子项
  fzylItem.child.forEach(item => {
    if (item.child && item.child.length > 0) {
      // 获取子项的值和描述
      let childItem = item.child[0];
      let mainText = childItem.desc || '';

      // 检查是否有更深层的子项描述
      let additionalDesc = '';
      if (childItem.child && childItem.child.length > 0) {
        let deepChild = childItem.child[0];
        if (deepChild.val && deepChild.val.trim() !== '') {
          additionalDesc = `（${deepChild.val}）`;
        }
      }

      // 构建完整的文本
      let fullText = mainText + additionalDesc;

      if (fullText.trim() !== '') {
        contentHtml += `<div>${fullText}</div>`;
      }
    }
  });

  return contentHtml;
}
/**
 * 获取病灶类型信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的类型信息，如果值为空则返回空字符串
 */
function getBzTypeInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找类型信息节点（通常 id 包含 '-1' 且 desc 包含 '病灶类型'）
  let typeNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('病灶类型') &&
    item.child && item.child.length > 0
  );

  if (typeNode && typeNode.child && typeNode.child.length > 0) {
    let typeValue = typeNode.child[0].val || '';
    // 检查值是否存在且不为空
    if (typeValue && typeValue.trim() !== '') {
      return typeValue.trim();
    }
  }

  return '';
}

/**
 * 获取病灶数目信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的数目信息，如果值为空则返回空字符串
 */
function getBzNumberInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找数目信息节点（通常 desc 包含 '病灶数目'）
  let numberNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('病灶数目') &&
    item.child && item.child.length > 0
  );

  if (numberNode && numberNode.child && numberNode.child.length > 0) {
    let numberValue = numberNode.child[0].val || '';
    // 检查值是否存在且不为空
    if (numberValue && numberValue.trim() !== '') {
      return numberValue.trim();
    }
  }

  return '';
}

/**
 * 获取病灶位置信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的位置信息，如果值为空则返回空字符串
 */
function getBzLocationInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找位置信息节点（通常 desc 包含 '位置'）
  let locationNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('位置') &&
    item.child && item.child.length > 0
  );

  if (locationNode && locationNode.child && locationNode.child.length > 0) {
    // 如果是层级结构的位置数据，进行格式化处理
    if (locationNode.child[0].child && locationNode.child[0].child.length > 0) {
      return formatLocationData(locationNode);
    }

    let locationValue = locationNode.child[0].val || '';
    // 检查值是否存在且不为空
    if (locationValue && locationValue.trim() !== '') {
      return locationValue.trim();
    }
  }

  return '';
}

/**
 * 获取病灶大小信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的大小信息，如果值为空则返回空字符串
 */
function getBzSizeInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找大小信息节点（通常 desc 包含 '大小'）
  let sizeNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('大小')
  );

  if (sizeNode && sizeNode.child && sizeNode.child.length > 0) {
    // 按照顺序提取左右径、前后径、上下径的值
    let leftRight = '';
    let frontBack = '';
    let upDown = '';

    // 查找各个径向的值
    sizeNode.child.forEach(item => {
      if (item.desc && item.desc.includes('左右径')) {
        leftRight = item.val || '0';
      } else if (item.desc && item.desc.includes('前后径')) {
        frontBack = item.val || '0';
      } else if (item.desc && item.desc.includes('上下径')) {
        upDown = item.val || '0';
      }
    });

    // 如果缺少值就用0替代
    leftRight = leftRight || '0';
    frontBack = frontBack || '0';
    upDown = upDown || '0';

    // 如果三个值都是0或空，则不显示
    if ((leftRight === '0' || leftRight === '') &&
      (frontBack === '0' || frontBack === '') &&
      (upDown === '0' || upDown === '')) {
      return '';
    }

    // 格式化为 41mm×213mm×23mm
    return `${leftRight}mm×${frontBack}mm×${upDown}mm`;
  }

  return '';
}

/**
 * 获取病灶形态信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的形态信息，如果值为空则返回空字符串
 */
function getBzDensityInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找形态信息节点（通常 desc 包含 '形态'）
  let densityNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('形态')
  );

  if (densityNode && densityNode.child && densityNode.child.length > 0) {
    // 从child数组中提取val值
    let densityValue = densityNode.child[0].val || '';
    // 检查值是否存在且不为空
    if (densityValue && densityValue.trim() !== '') {
      return densityValue.trim();
    }
  }

  return '';
}

/**
 * 获取病灶边缘信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的边缘信息，如果值为空则返回空字符串
 */
function getBzEdgeInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找边缘信息节点（通常 desc 包含 '边缘'）
  let edgeNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('边缘')
  );

  if (edgeNode && edgeNode.child && edgeNode.child.length > 0) {
    return formatEdgeData(edgeNode.child[0]);
  }

  return '';
}

/**
 * 获取病灶平扫密度信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {Object} 包含scanInfo和densityInfo的对象
 */
function getBzScanDensityInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return { scanInfo: '', densityInfo: '' };
  }

  // 查找平扫节点（desc 包含 '平扫'）
  let scanNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('平扫')
  );

  if (!scanNode || !scanNode.child || !scanNode.child.length) {
    return { scanInfo: '', densityInfo: '' };
  }

  let scanInfo = '';
  let densityInfo = '';

  // 处理平扫节点的子项
  scanNode.child.forEach(childItem => {
    // 处理平扫密度信息（第一行）
    if (childItem.desc && childItem.desc.includes('平扫密度') &&
      childItem.child && childItem.child.length > 0) {
      let scanValues = [];
      childItem.child.forEach(densityItem => {
        if (densityItem.val && densityItem.val.trim() !== '') {
          scanValues.push(densityItem.val.trim());
        }
      });
      if (scanValues.length > 0) {
        scanInfo = scanValues.join('、');
      }
    }

    // 处理密度信息（第二行）
    if (childItem.desc && childItem.desc.includes('密度：') &&
      childItem.child && childItem.child.length > 0) {
      let densityNode = childItem.child[0];
      if (densityNode && densityNode.val) {
        let densityValue = densityNode.val;

        // 处理密度的子项（如空洞、坏死、出血、脂肪）
        if (densityNode.child && densityNode.child.length > 0) {
          let subItems = [];
          densityNode.child.forEach(subItem => {
            if (subItem.val && subItem.val.trim() !== '') {
              subItems.push(subItem.val.trim());
            }
          });

          if (subItems.length > 0) {
            densityInfo = `${densityValue}（${subItems.join('、')}）`;
          } else {
            densityInfo = densityValue;
          }
        } else {
          densityInfo = densityValue;
        }
      }
    }
  });

  return { scanInfo, densityInfo };
}

/**
 * 获取病灶增强扫描信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {Object} 包含scanInfo和enhancementInfo的对象
 */
function getBzEnhancementInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return { scanInfo: '', enhancementInfo: '' };
  }

  // 查找增强扫描节点（desc 包含 '增强扫描'）
  let enhancementNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('增强扫描')
  );

  if (!enhancementNode || !enhancementNode.child || !enhancementNode.child.length) {
    return { scanInfo: '', enhancementInfo: '' };
  }

  let scanInfo = '';
  let enhancementInfo = '';

  // 存储各期扫描的数值
  let scanValues = [];

  // 处理增强扫描节点的子项
  enhancementNode.child.forEach(childItem => {
    // 处理平扫、动脉期、静脉期等扫描数值
    if (childItem.desc && childItem.val &&
      (childItem.desc.includes('增强扫描：') ||
        childItem.desc.includes('增强动脉期：') ||
        childItem.desc.includes('静脉期：'))) {
      let label = childItem.desc.replace('：', '');
      let value = childItem.val;
      if (value && value.trim() !== '') {
        if (label === '增强扫描') {
          scanValues.push(`平扫：${value}HU`);
        } else {
          scanValues.push(`${label}：${value}HU`);
        }
      }
    }

    // 处理强化程度信息
    if (childItem.desc && childItem.desc.includes('强化程度：') &&
      childItem.child && childItem.child.length > 0) {
      let enhancementItems = [];
      childItem.child.forEach(enhancementItem => {
        if (enhancementItem.val && enhancementItem.val.trim() !== '') {
          enhancementItems.push(enhancementItem.val.trim());
        }
      });
      if (enhancementItems.length > 0) {
        enhancementInfo = `强化程度：${enhancementItems.join('、')}；`;
      }
    }
  });

  // 格式化扫描信息
  if (scanValues.length > 0) {
    scanInfo = scanValues.join('；');
  }

  return { scanInfo, enhancementInfo };
}

/**
 * 获取病灶内部结构信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的内部结构信息，如果值为空则返回空字符串
 */
function getBzInternalStructureInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找内部结构节点（desc 包含 '内部结构'）
  let internalStructureNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('内部结构')
  );

  if (!internalStructureNode || !internalStructureNode.child || !internalStructureNode.child.length) {
    return '';
  }

  // 提取所有子项的值
  let structureItems = [];
  internalStructureNode.child.forEach(childItem => {
    if (childItem.val && childItem.val.trim() !== '') {
      structureItems.push(childItem.val.trim());
    }
  });

  // 如果有内容，返回格式化的字符串
  if (structureItems.length > 0) {
    return structureItems.join('、');
  }

  return '';
}

/**
 * 获取病灶周围继发及伴随征象信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的病灶周围继发及伴随征象信息，如果值为空则返回空字符串
 */
function getBzSecondarySignsInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找病灶周围继发及伴随征象节点（desc 包含 '病灶周围继发及伴随征象'）
  let secondarySignsNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('病灶周围继发及伴随征象')
  );

  if (!secondarySignsNode || !secondarySignsNode.child || !secondarySignsNode.child.length) {
    return '';
  }

  // 提取所有子项的值，处理有子项和无子项的情况
  let signItems = [];
  secondarySignsNode.child.forEach(childItem => {
    if (childItem.val && childItem.val.trim() !== '') {
      let itemText = childItem.val.trim();

      // 如果有子项，提取子项的值并用括号包围
      if (childItem.child && childItem.child.length > 0) {
        let subValues = [];
        childItem.child.forEach(subItem => {
          if (subItem.val && subItem.val.trim() !== '') {
            subValues.push(subItem.val.trim());
          }
        });

        if (subValues.length > 0) {
          itemText += `(${subValues.join('、')})`;
        }
      }

      signItems.push(itemText);
    }
  });

  // 如果有内容，返回格式化的字符串
  if (signItems.length > 0) {
    return signItems.join('、');
  }

  return '';
}

/**
 * 获取病灶局部侵犯信息
 * @param {Object} bzItem - 病灶数据项
 * @returns {String} 格式化的局部侵犯信息，如果值为空则返回空字符串
 */
function getBzLocalInvasionInfo(bzItem) {
  if (!bzItem.child || !bzItem.child.length) {
    return '';
  }

  // 查找局部侵犯节点（desc 包含 '局部侵犯'）
  let localInvasionNode = bzItem.child.find(item =>
    item.desc && item.desc.includes('局部侵犯')
  );

  if (!localInvasionNode || !localInvasionNode.child || !localInvasionNode.child.length) {
    return '';
  }

  // 分离侵犯部位和描述项
  let invasionParts = [];
  let description = '';

  localInvasionNode.child.forEach(childItem => {
    if (childItem.val && childItem.val.trim() !== '') {
      // 如果是描述项（通常desc包含'描述'）
      if (childItem.desc && childItem.desc.includes('描述')) {
        description = childItem.val.trim();
      } else {
        // 否则是侵犯部位
        invasionParts.push(childItem.val.trim());
      }
    }
  });

  // 格式化输出
  let result = '';
  if (invasionParts.length > 0) {
    result = invasionParts.join('、');

    // 如果有描述，添加到括号中
    if (description) {
      result += `（${description}）`;
    }
  }

  return result;
}

/**
 * 递归格式化边缘数据为指定格式
 * @param {Object} edgeData - 边缘数据对象
 * @returns {string} 格式化的边缘字符串，如"不光滑（分叶、毛刺(长毛刺)）"
 */
function formatEdgeData(edgeData) {
  if (!edgeData || !edgeData.val) {
    return '';
  }

  let result = edgeData.val;

  // 如果有子项，递归处理
  if (edgeData.child && edgeData.child.length > 0) {
    let childItems = [];

    edgeData.child.forEach(child => {
      if (child.val) {
        let childText = child.val;

        // 如果子项还有子项，递归处理并用小括号包围
        if (child.child && child.child.length > 0) {
          let grandChildItems = [];
          child.child.forEach(grandChild => {
            if (grandChild.val) {
              grandChildItems.push(grandChild.val);
            }
          });

          if (grandChildItems.length > 0) {
            childText += '(' + grandChildItems.join('、') + ')';
          }
        }

        childItems.push(childText);
      }
    });

    if (childItems.length > 0) {
      result += '（' + childItems.join('、') + '）';
    }
  }

  return result;
}

/**
 * 格式化位置数据为指定格式
 * @param {Object} locationData - 位置数据对象
 * @returns {string} 格式化的位置字符串
 */
function formatLocationData(locationData) {
  if (!locationData || !locationData.child) return '';

  const lungParts = [];

  // 遍历肺部（左肺、右肺）
  locationData.child.forEach(lung => {
    if (!lung.child || lung.child.length === 0) return;

    const lungName = lung.val.replace('：', ''); // 去掉冒号
    const lobes = [];

    // 遍历叶部（上叶、中叶、下叶）
    lung.child.forEach(lobe => {
      if (!lobe.child || lobe.child.length === 0) return;

      const lobeName = lobe.val; // 去掉冒号
      const segments = [];

      // 遍历段部
      lobe.child.forEach(segment => {
        if (segment.val && segment.val.trim()) {
          segments.push(segment.val);
        }
      });

      if (segments.length > 0) {
        lobes.push(`${lobeName}${segments.join('、')}`);
      }
    });

    if (lobes.length > 0) {
      lungParts.push(`${lungName}(${lobes.join('，')})`);
    }
  });

  return lungParts.join('，');
}




/**
 * 页面初始化
 */
function pageInit() {
  // 初始化将病灶参照代码复制存储起来
  initBzCloneEle(); // 首先清空内容，顺便保存起来
  // 初始化浅色和深色块的显示关系
  // toggleHightBlock(curElem, true);
  // initLigthItemChange(curElem);
  // 切换病灶
  toggleBzHandler(); // 切换事件
  // 绑定复选框和textarea的联动事件
  initCheckboxTextareaToggle();
  // 绑定单选框和textarea的联动事件
  initRadioTextareaToggle();
  // 初始化基于linkage属性的输入框联动
  initLinkageInputToggle();
  // 初始化远处转移区域的级联联动
  initRemoteMetastasisCascade();

  // 检查初始化时的linkage状态
  setTimeout(function () {
    checkInitialLinkageStates();
    checkInitialRemoteMetastasisStates();
  }, 100);

  // 回显病灶内容
  displayBzContent(); // 回显病灶事件
}
/**
 * 复制暂存病灶的具体模板示例内容，为后续添加病灶做准备
 */
function initBzCloneEle() {
  // 从现有HTML结构中获取病灶标题和内容模板，参考jcactTemplate的实现
  var bzTitle = DOM_CACHE.bzWrap.find('.bz-tit-i').clone(true);  // 作为病灶示例模板的title
  bzTitleClone = '<div class="bz-tit-i act" tab-id="fact-rt-000">' + bzTitle.html() + '</div>';
  var bzCon = DOM_CACHE.bzWrap.find('.bz-item').clone(true);
  bzConClone = '<div class="bz-item act" tab-target="fact-rt-000">' + bzCon.html() + '</div>';

  // 清空病灶容器，为动态添加做准备
  DOM_CACHE.bzTitLs.html('');
  DOM_CACHE.bzCon.html('');
}
/**
 * 添加病灶
 * @param {string} oldPaneId - 已保存过的病灶ID
 */
function addBzHandler(oldPaneId) {
  DOM_CACHE.bzWrap.show();
  var paneId = oldPaneId || UTILS.generateId();
  var activeTab = 'fact-rt-' + paneId;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ?
    JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  var { newPaneBlock } = appendBzHtml(paneId, oldIdAndDom);

  // 刷新DOM缓存
  DOM_CACHE.refresh();
  var bzLen = DOM_CACHE.bzTitItems.length;

  if (rtStructure) {
    // 清理空的idAndDomMap项
    if (rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }

    // 设置病灶主节点数据
    rtStructure.idAndDomMap[activeTab] = UTILS.createIdAndDomMapItem(activeTab, {
      desc: '病灶',
      name: '病灶title',
      pid: 'fact-rtlist-1',
      defaultValue: '病灶' + (bzLen + 1)
    }, oldIdAndDom);

    // 处理病灶内的表单控件
    var wCon = DOM_CACHE.fact1.find('.bz-item[tab-target="' + activeTab + '"]').find('[rt-sc]');
    wCon.each(function (wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ?
        JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};

      var scObj = UTILS.createIdAndDomMapItem(id, {
        desc: '',
        name: groupId,
        pid: pid,
        defaultValue: ''
      }, childOldIdAndDom);

      if (groupId) {
        scObj['groupId'] = groupId;
      }

      scArr.forEach(function (scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if (key) {
          if (key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="' + id + '"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      });
      rtStructure.idAndDomMap[id] = scObj;
    });

    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);

    // 滚动到最新添加的病灶
    DOM_CACHE.bzTitLs[0].scrollLeft = DOM_CACHE.bzTitLs[0].scrollWidth;
  }
}
/**
 * 回显病灶内容
 */
function displayBzContent() {
  // 判断回显内容，检查是否有保存的数据
  console.log(resultData);

  if (resultData && resultData.length) {
    var bzData = resultData.filter(bzItem => bzItem.id === 'fact-rtlist-1');
    if (bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      if (!bzList.length) {
        DOM_CACHE.bzWrap.hide();
      } else {
        // 遍历保存的病灶数据，逐个回显
        bzList.forEach((item) => {
          var paneId = item.id.replace('fact-rt-', '');
          addBzHandler(paneId);
        });
        // 显示病灶容器
        DOM_CACHE.bzWrap.show();

        // 检查复选框、单选框和linkage状态并设置textarea显示
        setTimeout(function () {
          checkInitialCheckboxStates();
          checkInitialRadioStates();
          checkInitialLinkageStates();
        }, 100);
      }
    } else {
      // 没有病灶数据时隐藏病灶容器
      DOM_CACHE.bzWrap.hide();
    }
  } else {
    // 没有保存过的数据，添加一个默认病灶
    addBzHandler();

    // 检查复选框、单选框和linkage状态并设置textarea显示
    setTimeout(function () {
      checkInitialCheckboxStates();
      checkInitialRadioStates();
      checkInitialLinkageStates();
    }, 100);
  }
}
/**
 * 处理新增病灶的HTML
 * @param {string} paneId - 病灶面板ID
 * @param {Object} oldIdAndDom - 旧的ID和DOM映射数据
 * @returns {Object} 包含新病灶块的对象
 */
function appendBzHtml(paneId, oldIdAndDom) {
  // 移除所有病灶的激活状态
  UTILS.toggleActiveState(DOM_CACHE.bzTitLs.find('.bz-tit-i'), null, 'act');
  UTILS.toggleActiveState(DOM_CACHE.bzCon.find('.bz-item'), null, 'act');
  DOM_CACHE.bzCon.find('.bz-item').hide();

  var reg = new RegExp('000', 'ig');
  var title = bzTitleClone.replace(reg, paneId);   // 作为病灶示例模板的title
  var content = bzConClone.replace(reg, paneId);  // 作为病灶示例模板具体内容

  // 添加新的病灶HTML
  DOM_CACHE.bzTitLs.append(title);
  DOM_CACHE.bzCon.append(content);

  var newPaneBlock = DOM_CACHE.fact1.find('.bz-item[tab-target="fact-rt-' + paneId + '"]');

  // 激活新添加的病灶tab
  var newTitleTab = DOM_CACHE.bzTitLs.find('.bz-tit-i[tab-id="fact-rt-' + paneId + '"]');
  UTILS.toggleActiveState(DOM_CACHE.bzTitLs.find('.bz-tit-i'), newTitleTab, 'act');
  UTILS.toggleActiveState(DOM_CACHE.bzCon.find('.bz-item'), newPaneBlock, 'act');
  newPaneBlock.show();

  setBzTitleNo(paneId);
  return { newPaneBlock };
}
/**
 * 病灶的交互初始化
 * @param {jQuery} newPaneBlock - 新的病灶面板块
 * @param {boolean} oldFlag - 是否为回显的旧数据
 */
function initBzTigger(newPaneBlock, oldFlag) {
  if (oldFlag) {
    // 回显保存的表单值
    newPaneBlock.find('.rt-sr-w').each(function (i, widget) {
      var id = $(widget).attr('id');
      if (rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    });
  }
  setTimeout(function () {
    checkInitialCheckboxStates();
    checkInitialRadioStates();
    checkInitialLinkageStates();
  }, 100);
}
/**
 * 设置病灶标题序号
 * @param {string} paneId - 病灶面板ID
 */
function setBzTitleNo(paneId) {
  DOM_CACHE.refresh();
  DOM_CACHE.bzTitItems.each(function (i, dom) {
    var tabId = $(dom).attr('tab-id');
    var titleIndex = i + 1;
    if (tabId === 'fact-rt-' + paneId) {
      DOM_CACHE.bzTitLs.find('.bz-tit-i[tab-id="fact-rt-' + paneId + '"] .bz-name').html('病灶' + titleIndex);
    }
  });
}
/**
 * 切换病灶处理器
 * 使用事件委托确保动态添加的病灶标签页也能响应点击事件
 */
function toggleBzHandler() {
  // 使用事件委托处理动态生成的病灶标签页点击事件
  $(document).on('click', '.bz-wrap .bz-tit-i', function (e) {
    var target = $(this).attr('tab-id');
    var $this = $(this);
    var targetBlock = $('.bz-con .bz-item[tab-target="' + target + '"]');

    // 使用工具函数切换激活状态
    UTILS.toggleActiveState($this.siblings('.bz-tit-i').add($this), $this, 'act');
    UTILS.toggleActiveState($('.bz-con .bz-item'), targetBlock, 'act');
    $('.bz-con .bz-item').hide();
    targetBlock.show();
  });
}
// 删除病灶相关变量和函数
var willBzVm = null;

/**
 * 删除病灶标签页
 * @param {HTMLElement} vm - 触发删除的元素
 * @param {string} paneId - 病灶面板ID
 */
function delTab(vm, paneId) {
  willBzVm = vm;
  DOM_CACHE.refresh();
  var idx = DOM_CACHE.fact1.find('.bz-tit-i .close-icon').index(vm);
  var dialogContent = '<div style="">确认删除病灶' + (idx + 1) + '?</div>';
  dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  dialogContent += '<button onclick="removeBzHandler(\'' + paneId + '\')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  dialogContent += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消</button>';
  dialogContent += '</div>';
  drawDialog({
    title: '提示',
    content: dialogContent,
    modal: true
  });
}

/**
 * 确认删除病灶
 * @param {string} paneId - 病灶面板ID
 */
function removeBzHandler(paneId) {
  var vm = willBzVm;
  DOM_CACHE.refresh();
  var idx = DOM_CACHE.fact1.find('.bz-tit-i .close-icon').index(vm);
  var isAct = $(vm).closest('.bz-tit-i').hasClass('act');

  // 删除对应的标题和内容元素
  DOM_CACHE.bzTitItems.eq(idx).remove();
  DOM_CACHE.bzItems.eq(idx).remove();

  // 刷新缓存并获取剩余病灶数量
  DOM_CACHE.refresh();
  var resetBzLen = DOM_CACHE.bzItems.length;

  if (resetBzLen > 0 && isAct) {
    let nextId = idx >= 1 ? idx - 1 : idx;
    // 先隐藏所有病灶内容容器
    DOM_CACHE.bzCon.find('.bz-item').removeClass('act').hide();
    // 激活下一个tab标题
    DOM_CACHE.bzTitItems.eq(nextId).addClass('act');
    // 显示对应的病灶内容容器
    DOM_CACHE.bzItems.eq(nextId).addClass('act').show();
  }

  // 清理相关的idAndDomMap数据
  if (rtStructure && rtStructure.idAndDomMap) {
    for (var key in rtStructure.idAndDomMap) {
      if (key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }

  removeRtDialog();
  getImpressionText();

  if (resetBzLen > 0) {
    // 重新编号所有病灶
    DOM_CACHE.fact1.find('.bz-name').each(function (i, dom) {
      $(dom).text('病灶' + (i + 1));
    });
    DOM_CACHE.bzWrap.show();
  } else {
    DOM_CACHE.bzWrap.hide();
  }
}
/**
 * 清除表单的值
 * @param {HTMLElement} widget - 表单控件元素
 */
function clearFormItemVal(widget) {
  var type = getWgtType(widget) || widget.type;
  if (type === 'text') {
    $(widget).val('');
  } else if (['radio', 'checkbox'].indexOf(type) > -1) {
    $(widget).attr('checked', false);
  } else if (type === 'select') {
    $(widget).find('option').each(function (i, option) {
      $(option).attr('selected', false);
    });
  } else {
    $(widget).val('');
  }
}

/**
 * 判断控件类型
 * @param {HTMLElement} widget - 表单控件元素
 * @returns {string} 控件类型
 */
function getWgtType(widget) {
  var widgetTypes = ['ck-checkbox', 'r-radio', 'tit-title', 't-text', 's-select'];
  for (var i = 0; i < widgetTypes.length; i++) {
    var name = widgetTypes[i].split('-')[0];
    var type = widgetTypes[i].split('-')[1];
    if ($(widget).hasClass('rt-sr-' + name)) {
      return type;
    }
  }
}

/**
 * 将NodeList转换为数组
 * @param {NodeList} nodeList - 节点列表
 * @returns {Array} 转换后的数组
 */
function nodesToArray(nodeList) {
  return Array.prototype.slice.call(nodeList);
}




/**
 * 获取表单元素的值（保持向后兼容）
 * @param {string} selector - 选择器
 * @returns {string} 表单元素的值
 */
function getVal(selector) {
  return UTILS.getFormValue($(selector));
}

/**
 * 绘制对话框
 * @param {Object} options - 对话框选项
 * @param {string} options.title - 对话框标题
 * @param {string} options.content - 对话框内容
 * @param {boolean} options.modal - 是否为模态对话框
 */
function drawDialog(options) {
  // 简单的对话框实现
  var dialogHtml = '<div id="rt-dialog" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9999;display:flex;align-items:center;justify-content:center;">';
  dialogHtml += '<div style="background:#fff;border-radius:4px;min-width:300px;max-width:500px;">';
  if (options.title) {
    dialogHtml += '<div style="padding:15px 20px;border-bottom:1px solid #eee;font-weight:bold;">' + options.title + '</div>';
  }
  dialogHtml += '<div style="padding:20px;">' + options.content + '</div>';
  dialogHtml += '</div></div>';

  $('body').append(dialogHtml);
}

/**
 * 移除对话框
 */
function removeRtDialog() {
  $('#rt-dialog').remove();
}

/**
 * 初始化复选框和textarea的联动事件
 * 当复选框未选中时隐藏对应的textarea，选中时显示
 * 只针对具有data-toggle-group="bzzwjfjbszx"标识的区域生效
 */
function initCheckboxTextareaToggle() {
  // 使用事件委托处理动态生成的内容，只针对特定区域
  $(document).on('change', '[data-toggle-group="bzzwjfjbszx"] input[type="checkbox"].rt-sr-w', function () {
    var $checkbox = $(this);
    var checkboxId = $checkbox.attr('id');

    // 查找对应的textarea（ID规则：复选框ID + '-1'）
    var textareaId = checkboxId + '-1';
    var $textarea = $('#' + textareaId);

    if ($textarea.length > 0) {
      if ($checkbox.is(':checked')) {
        let checkboxVal = $checkbox.val()
        console.log(checkboxVal);
        if (checkboxVal === '阻塞性肺不张') {
          $textarea.val('病变远端可见片状渗出影、实变影，边缘模糊')
        } else if (checkboxVal === '阻塞性肺气肿') {
          $textarea.val('病变远端肺体积缩小，呈片状实变影，边界清楚')
        } else if (checkboxVal === '阻塞性肺炎') {
          $textarea.val('病变远端肺实质密度减低，血管支气管束稀疏')
        }
        $textarea.show();
      } else {
        $textarea.hide();
        // 清空textarea的值
        $textarea.val('');
      }
    }
  });

  // 页面初始化时检查现有复选框状态
  checkInitialCheckboxStates();
}

/**
 * 初始化单选框和textarea的联动事件
 * 当单选框选中"可见"或"异常"时显示对应的textarea，选中其他值时隐藏
 * 处理肺转移瘤、同侧胸膜转移、心包转移、整体评估等区域的单选框和textarea联动
 * 只监听具有listen-type="toggleRadio"属性的区域内的单选框
 */
function initRadioTextareaToggle() {
  // 使用事件委托处理动态生成的内容，只监听具有listen-type="toggleRadio"的区域
  $(document).on('change', '[listen-type="toggleRadio"] input[type="radio"].rt-sr-w', function () {
    var $radio = $(this);
    var radioValue = $radio.val();
    var radioPid = $radio.attr('pid');

    // 查找对应的textarea（pid规则：单选框的pid + '-2-1'）
    var textareaId = radioPid + '-2-1';
    var $textarea = $('#' + textareaId);
    if ($textarea.length > 0) {
      // 对于整体评估区域，当选择"异常"时显示textarea
      // 对于其他区域，当选择"可见"时显示textarea
      if (radioValue === '可见' || radioValue === '异常') {
        $textarea.show();
      } else {
        // 选择"未见"、"未见异常"、"通畅"、"未见积液"、"形态未见明显异常"等时隐藏
        $textarea.hide();
        // 清空textarea的值
        $textarea.val('');
        // 如果有对应的rtStructure数据，也清空
        if (rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[textareaId]) {
          rtStructure.idAndDomMap[textareaId].value = '';
        }
      }
    }
  });

  // 页面初始化时检查现有单选框状态
  checkInitialRadioStates();
}

/**
 * 检查页面初始化时的复选框状态并设置对应textarea的显示状态
 * 只针对具有data-toggle-group="bzzwjfjbszx"标识的区域生效
 */
function checkInitialCheckboxStates() {
  $('[data-toggle-group="bzzwjfjbszx"] input[type="checkbox"].rt-sr-w').each(function () {
    var $checkbox = $(this);
    var checkboxId = $checkbox.attr('id');

    // 查找对应的textarea（ID规则：复选框ID + '-1'）
    var textareaId = checkboxId + '-1';
    var $textarea = $('#' + textareaId);

    if ($textarea.length > 0) {
      if ($checkbox.is(':checked')) {
        $textarea.show();
      } else {
        $textarea.hide();
      }
    }
  });
}

/**
 * 检查页面初始化时的单选框状态并设置对应textarea的显示状态
 * 处理肺转移瘤、同侧胸膜转移、心包转移、整体评估等区域的单选框和textarea联动
 * 如果单选框没有选中任何值或选中的值不是"可见"或"异常"，则隐藏对应的textarea
 * 只检查具有listen-type="toggleRadio"属性的区域内的单选框
 */
function checkInitialRadioStates() {
  // 查找所有相关的单选框组，包括整体评估区域的单选框组
  var radioGroups = ['dfawj', 'tcbtfynglx', 'dcfycxzy', 'zcycx', 'xmjy', 'xbzcycx', 'xbjy', 'radio-9-1', 'radio-9-2', 'radio-9-3', 'radio-9-4'];

  radioGroups.forEach(function (groupName) {
    // 只检查具有listen-type="toggleRadio"的区域内的单选框
    var $checkedRadio = $('[listen-type="toggleRadio"] input[type="radio"][name="' + groupName + '"].rt-sr-w:checked');

    if ($checkedRadio.length > 0) {
      // 有选中的单选框，检查其值
      $checkedRadio.each(function () {
        var $radio = $(this);
        var radioValue = $radio.val();
        var radioPid = $radio.attr('pid');

        // 查找对应的textarea（pid规则：单选框的pid + '-2-1'）
        var textareaId = radioPid + '-2-1';
        var $textarea = $('#' + textareaId);

        if ($textarea.length > 0) {
          // 对于整体评估区域，当选择"异常"时显示textarea
          // 对于其他区域，当选择"可见"时显示textarea
          if (radioValue === '可见' || radioValue === '异常') {
            $textarea.show();
          } else {
            // 选择"未见"、"未见异常"、"通畅"、"未见积液"、"形态未见明显异常"等时隐藏
            $textarea.hide();
          }
        }
      });
    } else {
      // 没有选中的单选框，隐藏所有相关的textarea
      $('[listen-type="toggleRadio"] input[type="radio"][name="' + groupName + '"].rt-sr-w').each(function () {
        var $radio = $(this);
        var radioPid = $radio.attr('pid');

        // 查找对应的textarea（pid规则：单选框的pid + '-2-1'）
        var textareaId = radioPid + '-2-1';
        var $textarea = $('#' + textareaId);

        if ($textarea.length > 0) {
          $textarea.hide();
        }
      });
    }
  });
}

/**
 * 初始化基于linkage属性的输入框联动事件
 * 当linkage关联的复选框选中时启用输入框，未选中时禁用输入框并清空值
 * 当linkage关联的单选框选中时显示textarea，未选中时隐藏textarea并清空值
 */
function initLinkageInputToggle() {
  // 使用事件委托处理动态生成的内容
  $(document).on('change', 'input[type="checkbox"].rt-sr-w', function () {
    updateLinkageInputs();
  });

  // 处理单选框change事件来控制textarea的显示隐藏
  $(document).on('change', 'input[type="radio"].rt-sr-w', function () {
    updateLinkageTextareas();
  });
}

/**
 * 更新所有具有linkage属性的输入框状态
 */
function updateLinkageInputs() {
  // 查找所有具有linkage属性的输入框
  $('input[linkage].rt-sr-w').each(function () {
    var $input = $(this);
    var linkageIds = $input.attr('linkage');

    if (linkageIds) {
      // 解析linkage属性中的复选框ID列表
      var checkboxIds = linkageIds.split(',');
      var hasCheckedCheckbox = false;

      // 检查是否有任何关联的复选框被选中
      for (var i = 0; i < checkboxIds.length; i++) {
        var checkboxId = checkboxIds[i].trim();
        var $checkbox = $('#' + checkboxId);

        if ($checkbox.length > 0 && $checkbox.is(':checked')) {
          hasCheckedCheckbox = true;
          break;
        }
      }

      // 根据复选框状态设置输入框的启用/禁用状态
      if (hasCheckedCheckbox) {
        $input.prop('disabled', false);
        $input.removeClass('disabled-input');
      } else {
        $input.prop('disabled', true);
        $input.addClass('disabled-input');
        // 清空输入框的值
        $input.val('');

        // 如果有对应的rtStructure数据，也清空
        var inputId = $input.attr('id');
        if (inputId && rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[inputId]) {
          rtStructure.idAndDomMap[inputId].value = '';
        }
      }
    }
  });
}

/**
 * 更新所有具有linkage属性的textarea状态
 * 当linkage关联的单选框被选中时显示textarea，未选中时隐藏并清空值
 */
function updateLinkageTextareas() {
  // 查找所有具有linkage属性的textarea
  $('textarea[linkage].rt-sr-w').each(function () {
    var $textarea = $(this);
    var linkageIds = $textarea.attr('linkage');

    if (linkageIds) {
      // 解析linkage属性中的单选框ID列表
      var radioIds = linkageIds.split(',');
      var hasCheckedRadio = false;

      // 检查是否有任何关联的单选框被选中
      for (var i = 0; i < radioIds.length; i++) {
        var radioId = radioIds[i].trim();
        var $radio = $('#' + radioId);

        if ($radio.length > 0 && $radio.is(':checked')) {
          hasCheckedRadio = true;
          break;
        }
      }

      // 根据单选框状态设置textarea的显示/隐藏状态
      if (hasCheckedRadio) {
        $textarea.show();
      } else {
        $textarea.hide();
        // 清空textarea的值
        $textarea.val('');

        // 如果有对应的rtStructure数据，也清空
        var textareaId = $textarea.attr('id');
        if (textareaId && rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[textareaId]) {
          rtStructure.idAndDomMap[textareaId].value = '';
        }
      }
    }
  });
}

/**
 * 检查页面初始化时的linkage状态并设置对应输入框的启用/禁用状态
 * 同时检查textarea的显示/隐藏状态
 */
function checkInitialLinkageStates() {
  updateLinkageInputs();
  updateLinkageTextareas();
}

// 将主要函数暴露到全局作用域
window.addBzHandler = addBzHandler;
window.delTab = delTab;
window.removeBzHandler = removeBzHandler;
window.initCheckboxTextareaToggle = initCheckboxTextareaToggle;
window.checkInitialCheckboxStates = checkInitialCheckboxStates;
window.initRadioTextareaToggle = initRadioTextareaToggle;
window.checkInitialRadioStates = checkInitialRadioStates;
window.initLinkageInputToggle = initLinkageInputToggle;
window.updateLinkageInputs = updateLinkageInputs;
window.updateLinkageTextareas = updateLinkageTextareas;
window.checkInitialLinkageStates = checkInitialLinkageStates;
/**
 * 获取所有病灶数据
 * @returns {Array} 所有病灶数据数组
 */
function getAllBzData() {
  var allData = [];

  // 遍历所有病灶标题
  DOM_CACHE.bzTitItems.each(function (index, element) {
    var bzId = $(element).attr('tab-id');
    if (bzId) {
      var bzData = {
        id: bzId,
        title: $(element).find('.bz-name').text().trim(),
        data: {},
        formData: {}
      };

      // 获取对应的病灶内容
      var bzItem = DOM_CACHE.bzCon.find('.bz-item[tab-target="' + bzId + '"]');
      if (bzItem.length > 0) {
        // 收集表单数据
        bzItem.find('.rt-sr-w').each(function () {
          var $this = $(this);
          var id = $this.attr('id');
          var name = $this.attr('name');

          if (id && rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id]) {
            bzData.formData[id] = rtStructure.idAndDomMap[id].value || '';
          }

          if (name) {
            if ($this.is(':radio') || $this.is(':checkbox')) {
              if ($this.is(':checked')) {
                bzData.data[name] = $this.val();
              }
            } else {
              bzData.data[name] = $this.val() || '';
            }
          }
        });
      }

      allData.push(bzData);
    }
  });

  return allData;
}

/**
 * 初始化远处转移区域的级联联动事件
 * 复选框控制转移灶数目选项的显示，转移灶数目单选框控制textarea的显示
 * 取消复选框时清除下级选项和值
 */
function initRemoteMetastasisCascade() {
  // 使用事件委托处理远处转移区域的复选框change事件
  $(document).on('change', 'input[type="checkbox"][name="yczy"].rt-sr-w', function () {
    var $checkbox = $(this);
    var checkboxId = $checkbox.attr('id');
    var isChecked = $checkbox.is(':checked');

    // 查找对应的转移灶数目选项容器（复选框的下一个div）
    var $metastasisContainer = $checkbox.closest('label').next('div');

    if (isChecked) {
      // 选中复选框时显示转移灶数目选项
      $metastasisContainer.show();
    } else {
      // 取消选中复选框时隐藏转移灶数目选项并清除选中状态和textarea值
      $metastasisContainer.hide();

      // 清除转移灶数目单选框的选中状态
      $metastasisContainer.find('input[type="radio"].rt-sr-w').each(function () {
        var $radio = $(this);
        $radio.prop('checked', false);

        // 如果有对应的rtStructure数据，也清空
        var radioId = $radio.attr('id');
        if (radioId && rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[radioId]) {
          rtStructure.idAndDomMap[radioId].value = '';
        }
      });

      // 隐藏并清空对应的textarea
      var $textarea = $metastasisContainer.next('textarea[linkage]');
      if ($textarea.length > 0) {
        $textarea.hide();
        $textarea.val('');

        // 如果有对应的rtStructure数据，也清空
        var textareaId = $textarea.attr('id');
        if (textareaId && rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[textareaId]) {
          rtStructure.idAndDomMap[textareaId].value = '';
        }
      }
    }
  });
}

/**
 * 检查页面初始化时远处转移区域的级联状态
 * 根据复选框状态设置转移灶数目选项和textarea的显示状态
 */
function checkInitialRemoteMetastasisStates() {
  // 检查所有远处转移复选框的状态
  $('input[type="checkbox"][name="yczy"].rt-sr-w').each(function () {
    var $checkbox = $(this);
    var isChecked = $checkbox.is(':checked');

    // 查找对应的转移灶数目选项容器
    var $metastasisContainer = $checkbox.closest('label').next('div');

    if (isChecked) {
      // 复选框选中时显示转移灶数目选项
      $metastasisContainer.show();
    } else {
      // 复选框未选中时隐藏转移灶数目选项和textarea
      $metastasisContainer.hide();

      // 隐藏对应的textarea
      var $textarea = $metastasisContainer.next('textarea[linkage]');
      if ($textarea.length > 0) {
        $textarea.hide();
      }
    }
  });
}
/**
 * 获取印象文本
 * @returns {string} 印象文本
 */
function getImpressionText() {
  // 这里可以根据具体业务需求实现印象文本的生成逻辑
  // 暂时返回空字符串
  let impressionText = ''
  let bzResult = []
  let indexFlag = false
  DOM_CACHE.bzItems.each(function () {
    // 找到id为5结尾的元素
    // 找到所有的checkbox
    let textResult = []
    let leftMap = {} // 左肺
    let rightMap = {} // 右肺
    // 序号


    // 转为数组
    let leftUp = Array.from($(this).find('[id$="-5-1-1"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    let leftDown = Array.from($(this).find('[id$="-5-1-2"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    if (leftUp.length) {
      leftMap['leftUp'] = leftUp
    }
    if (leftDown.length) {
      leftMap['leftDown'] = leftDown
    }
    if (leftMap.leftUp && leftMap.leftDown) {
      textResult.push(`左肺上叶${leftMap.leftUp.join('、')},下叶${leftMap.leftDown.join('、')}占位`)
    } else {
      if (leftMap.leftUp) {
        textResult.push(`左肺上叶${leftMap.leftUp.join('、')}占位`)
      }
      if (leftMap.leftDown) {
        textResult.push(`左肺下叶${leftMap.leftDown.join('、')}占位`)
      }
    }


    let rightUp = Array.from($(this).find('[id$="-5-2-1"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    let rightMid = Array.from($(this).find('[id$="-5-2-2"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    let rightDown = Array.from($(this).find('[id$="-5-2-3"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    if (rightUp.length) {
      rightMap['rightUp'] = rightUp
    }
    if (rightMid.length) {
      rightMap['rightMid'] = rightMid
    }
    if (rightDown.length) {
      rightMap['rightDown'] = rightDown
    }

    if (rightMap.rightUp && rightMap.rightMid && rightMap.rightDown) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')},中叶${rightMap.rightMid.join('、')},下叶${rightMap.rightDown.join('、')}占位`)
    } else if (rightMap.rightUp && rightMap.rightMid) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')},中叶${rightMap.rightMid.join('、')}占位`)
    } else if (rightMap.rightUp && rightMap.rightDown) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')},下叶${rightMap.rightDown.join('、')}占位`)
    } else if (rightMap.rightMid && rightMap.rightDown) {
      textResult.push(`右肺中叶${rightMap.rightMid.join('、')},下叶${rightMap.rightDown.join('、')}占位`)
    } else if (rightMap.rightUp) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')}占位`)
    } else if (rightMap.rightMid) {
      textResult.push(`右肺中叶${rightMap.rightMid.join('、')}占位`)
    } else if (rightMap.rightDown) {
      textResult.push(`右肺下叶${rightMap.rightDown.join('、')}占位`)
    }
    // 类型
    // let type = 
    $(this).find('.bz-form-item').first().find('input:checked').each(function () {
      textResult.push('考虑' + $(this).val() + '肺癌可能性大')
    })
    let localInvasion = []
    $(this).find('[data-srname="局部侵犯"]').parent().find('input:checked,textarea').each(function () {
      if ($(this).val()) {
        localInvasion.push($(this).val())
      }
    })
    if (localInvasion.length) {
      textResult.push('局部侵犯' + localInvasion.filter(Boolean).join('、'))
    }
    let bzzwjfjbszx = []

    $(this).find('[data-toggle-group="bzzwjfjbszx"] input:checked[value="阻塞性肺不张"], [data-toggle-group="bzzwjfjbszx"] input:checked[value="阻塞性肺气肿"], [data-toggle-group="bzzwjfjbszx"] input:checked[value="阻塞性肺炎"], [data-toggle-group="bzzwjfjbszx"] input:checked[value="卫星灶"]').each(function () {
      bzzwjfjbszx.push($(this).val())
    });
    if (bzzwjfjbszx.length) {
      textResult.push('伴' + bzzwjfjbszx.join('、'))
    }
    if (textResult.join(',')) {
      bzResult.push(textResult.join(','))
    }
  });
  if (bzResult.length > 1) {
    indexFlag = true
  }
  let zy = getZy()
  if (zy) {
    bzResult.push(zy)
  }
  // 获取整体评估
  let ztpg = getZtpg()
  if (ztpg) {
    bzResult.push(ztpg)
  }
  let ctFq = getCtFq()
  if (ctFq) {
    bzResult.push('CT分期：' + ctFq)
  }
  if (indexFlag) {
    bzResult.forEach((item, index) => {
      if (index === bzResult.length - 1) {
        impressionText += (index + 1) + '.' + item
      } else {
        impressionText += (index + 1) + '.' + item + '\n'
      }
    })
  } else {
    impressionText = bzResult.join(',')
  }
  if (impressionText) {
    $('#fact-rt-12').val(impressionText)
  }
  return '';
}
function getCtFq() {
  let t = getTHandler()
  let n = getNHanlder()
  let m = getMHandler()
  return t + n + m
}
function getTHandler() {
  let result = 'T0'
  let maxSize = 0
  let localInvasion = [] // 局部侵犯
  DOM_CACHE.bzItems.each(function () {
    $(this).find('[data-srname="大小约："]').parent().find('input').each(function () {
      let sizeVal = $(this).val()
      if (sizeVal && Number(sizeVal) > Number(maxSize)) {
        maxSize = sizeVal
      }
    })

    // 查找局部侵犯所有内容,多选框是要被选中的，输入框得有内容
    $(this).find('[data-srname="局部侵犯"]').parent().find('input:checked,textarea').each(function () {
      if ($(this).val()) {
        localInvasion.push($(this).val())
      }
    })
    $(this).find('[data-toggle-group="bzzwjfjbszx"]').find('input:checked').each(function () {
      localInvasion.push($(this).val())
    })
  })
  for (let key in zy1Map) {
    $('#' + key + ':checked').each(function () {
      localInvasion.push(zy1Map[key])
    })
  }
  maxSize = maxSize / 10
  if (!maxSize && localInvasion.length === 0) return result
  if ((localInvasion.includes('气管') && !localInvasion.includes('主支气管')) || !localInvasion.includes('胸膜') || (0 < maxSize && maxSize <= 3)) {
    result = 'T1'
    if (0 < maxSize && maxSize <= 1) {
      result = 'T1a'
    } else if (1 < maxSize && maxSize <= 2) {
      result = 'T1b'
    } else if (2 < maxSize && maxSize <= 3) {
      result = 'T1c'
    }
  }
  // 包含胸膜或者（主支气管并且没有气管隆突） 或者 阻塞性肺不张 或者 阻塞性肺炎
  if (localInvasion.includes('胸膜') || (localInvasion.includes('主支气管') && !localInvasion.includes('气管隆突')) || localInvasion.includes('阻塞性肺不张') || localInvasion.includes('阻塞性肺炎') || (maxSize > 3 && maxSize <= 5)) {
    result = 'T2'
    if (3 < maxSize && maxSize <= 4) {
      result = 'T2a'
    } else if (4 < maxSize && maxSize <= 5) {
      result = 'T2b'
    }
  }
  if (localInvasion.includes('同一肺叶转移') || (localInvasion.includes('胸膜') && localInvasion.includes('胸壁') && localInvasion.includes('膈神经') && localInvasion.includes('心包')) || (5 < maxSize && maxSize <= 7)) {
    result = 'T3'
  }

  if (localInvasion.includes('同侧不同肺叶转移') ||
    (localInvasion.includes('膈肌') &&
      localInvasion.includes('纵隔')
      && localInvasion.includes('心脏')
      && localInvasion.includes('大血管')
      && localInvasion.includes('气管')
      && localInvasion.includes('喉返神经')
      && localInvasion.includes('食管')
      && localInvasion.includes('椎体')
      && localInvasion.includes('气管隆突'))
    || (maxSize > 7)) {
    result = 'T4'
  }
  return result
}
function getNHanlder() {
  let errorFlag = false;
  let resultList = []
  DOM_CACHE.bzItems.each(function () {
    let leftGroup = Array.from($(this).find('[data-group="left"]').find('input:checked'))
    let rightGroup = Array.from($(this).find('[data-group="right"]').find('input:checked'))
    let nCalc = 0
    if (!leftGroup.length && !rightGroup.length) {
      return 'NX'
    }
    if (leftGroup.length && rightGroup.length) {
      errorFlag = true
      return;
    }
    if (leftGroup.length) {
      lbjList.forEach(item => {
        if (item.label === '10' || item.label === '11-14') {
          let val = $(item.selector[0] + ':checked').val()
          if (val && nCalc < 1) {
            nCalc = 1
          }
        }
        if (item.label === '2' || item.label === '3A' || item.label === '3P' || item.label === '4' || item.label === '7' || item.label === '8' || item.label === '9') {
          let selector = item.selector[0]
          if (item.label === '2' || item.label === '4') {
            selector = item.selector[1]
          }
          let val = $(selector + ':checked').val()
          if (val && nCalc < 2) {
            nCalc = 2
          }
        }
        if (item.label === '1' || item.label === '2' || item.label === '3A' || item.label == '3P' || item.label === '4' || item.label === '8' || item.label === '9' || item.label === '10' || item.label === '11-14') {
          let selector = item.selector.length > 1 ? item.selector[1] : item.selector[0]
          if (item.label === '2' || item.label === '4') {
            selector = item.selector[0]
          }
          let val = $(selector + ':checked').val()
          if (val && nCalc < 3) {
            nCalc = 3
          }
        }
      })
    }
    //  同上面相反
    if (rightGroup.length) {
      lbjList.forEach(item => {
        if (item.label === '10' || item.label === '11-14') {
          let val = $(item.selector[1] + ':checked').val()
          if (val && nCalc < 1) {
            nCalc = 1
          }
        }
        if (item.label === '2' || item.label === '3A' || item.label === '3P' || item.label === '4' || item.label === '7' || item.label === '8' || item.label === '9') {
          let selector = item.selector.length > 1 ? item.selector[1] : item.selector[0]
          if (item.label === '2' || item.label === '4') {
            selector = item.selector[0]
          }
          let val = $(selector + ':checked').val()
          if (val && nCalc < 2) {
            nCalc = 2
          }
        }
        if (item.label === '1' || item.label === '2' || item.label === '3A' || item.label == '3P' || item.label === '4' || item.label === '8' || item.label === '9' || item.label === '10' || item.label === '11-14') {
          let selector = item.selector[0]
          if (item.label === '2' || item.label === '4') {
            selector = item.selector[1]
          }
          let val = $(selector + ':checked').val()
          if (val && nCalc < 3) {
            nCalc = 3
          }
        }
      })
    }
    resultList.push(nCalc)
  })
  if (errorFlag) {
    return 'NX'
  }
  return 'N' + Math.max(resultList)
}
function getMHandler() {
  let result = 'M0'
  let m1aList = ['#fact-rt-5-3-2', '#fact-rt-6-1-2', '#fact-rt-7-1-2']
  let m1cList = ['#fact-rt-8-13-1-1', '#fact-rt-10-1-1', '#fact-rt-10-1-2', '#fact-rt-10-2-1', '#fact-rt-10-2-2', '#fact-rt-10-3-1', '#fact-rt-10-3-2', '#fact-rt-10-4-1', '#fact-rt-10-4-2']
  let m2c = 0
  m1aList.forEach(item => {
    if (checkLymphNodeSelection([item])) {
      result = 'M1a'
    }
  })

  if ((checkLymphNodeSelection(['#fact-rt-8-13']) && $('#fact-rt-8-13-1-1').val() == 1) || $('#fact-rt-10-1-1:checked').val() || $('#fact-rt-10-2-1:checked').val() || $('#fact-rt-10-3-1:checked').val() || $('#fact-rt-10-4-1:checked').val()) {
    result = 'M1b'
  }
  if ((checkLymphNodeSelection(['#fact-rt-8-13']) && $('#fact-rt-8-13-1-1').val() >= 2) || $('#fact-rt-10-1-2:checked').val() || $('#fact-rt-10-2-2:checked').val() || $('#fact-rt-10-3-2:checked').val() || $('#fact-rt-10-4-2:checked').val()) {
    result = 'M1c1'
  }
  m1cList.forEach((item, index) => {
    if (index === 0) {
      $(item).val() && m2c++
    } else {
      $(item).is(':checked') && m2c++
    }
  })
  if (m2c >= 2) {
    result = 'M1c2'
  }
  return result
}
function getZtpg() {
  let ztpgMap = {
    '#fact-rt-9-1-2': '余双肺支气管血管束走行分布异常',
    '#fact-rt-9-2-2': '段及段以上支气管异常',
    '#fact-rt-9-3-2': '双侧胸腔异常',
    '#fact-rt-9-4-2': '心脏形态可见明显异常',
  }
  let ztpgRes = []
  for (let key in ztpgMap) {
    if (checkLymphNodeSelection([key])) {
      ztpgRes.push(ztpgMap[key])
    }
  }
  if (ztpgRes.length) {
    return '整体评估：' + ztpgRes.join('、')
  }
  return ''
}
/**
 * 获取转移相关信息
 * @returns {string} 转移信息的描述文本
 */
function getZy() {
  const result = [];
  // 获取各类转移数据
  const zy1Result = getZy1TransferData();
  const lbjResult = getLymphNodeTransferData();
  const yczyResult = getRemoteTransferData();

  // 组装结果
  if (zy1Result) result.push(zy1Result);
  if (lbjResult) result.push(lbjResult);
  if (yczyResult) result.push(yczyResult);

  return result.join(',');
}

/**
 * 获取zy1相关转移数据（同一肺叶、同侧不同肺叶、对侧肺叶、胸膜、心包转移等）
 * @returns {string} zy1转移数据描述
 */
function getZy1TransferData() {
  const zy1Prefixes = ['fact-rt-5-', 'fact-rt-6-', 'fact-rt-7-'];
  const zy1Results = [];

  // 遍历所有zy1相关的前缀
  zy1Prefixes.forEach(prefix => {
    // 查找所有以该前缀开头且被选中的复选框
    $(`input[id^="${prefix}"]:checked`).each(function () {
      const elementId = $(this).attr('id');
      const transferDescription = zy1Map[elementId];

      if (transferDescription) {
        zy1Results.push(transferDescription);
      }
    });
  });

  return zy1Results.join(',');
}

/**
 * 获取淋巴结转移数据
 * @returns {string} 淋巴结转移数据描述
 */
function getLymphNodeTransferData() {
  const lbjResults = [];

  // 遍历所有淋巴结配置项
  lbjList.forEach(item => {
    if (item.isOther) {
      // 处理"其他"类型的淋巴结
      const otherValue = getOtherLymphNodeValue(item);
      if (otherValue) {
        lbjResults.push(otherValue);
      }
    } else {
      // 处理普通淋巴结
      const isSelected = checkLymphNodeSelection(item.selector);
      if (isSelected) {
        lbjResults.push(item.label);
      }
    }
  });

  return lbjResults.length > 0 ? `淋巴结转移：${lbjResults.join('、')}组` : '';
}

/**
 * 获取"其他"类型淋巴结的值
 * @param {Object} item - 淋巴结配置项
 * @returns {string} 其他淋巴结的值
 */
function getOtherLymphNodeValue(item) {
  // 首先检查其他选择器中是否有值
  for (const selector of item.otherSelector) {
    const otherValue = $(selector).val();
    if (otherValue) {
      return otherValue;
    }
  }

  // 如果其他选择器没有值，检查普通选择器是否被选中
  const isSelected = checkLymphNodeSelection(item.selector);
  return isSelected ? item.label : '';
}

/**
 * 检查淋巴结选择器是否被选中
 * @param {Array} selectors - 选择器数组
 * @returns {boolean} 是否被选中
 */
function checkLymphNodeSelection(selectors) {
  return selectors.some(selector => $(selector + ':checked').length > 0);
}

/**
 * 获取远处转移数据
 * @returns {string} 远处转移数据描述
 */
function getRemoteTransferData() {
  const remoteTransferSelectors = [
    '#fact-rt-10-1',
    '#fact-rt-10-2',
    '#fact-rt-10-3',
    '#fact-rt-10-4'
  ];

  const remoteTransferResults = [];

  // 遍历所有远处转移选择器
  remoteTransferSelectors.forEach(selector => {
    const $element = $(selector + ':checked');
    if ($element.length > 0 && $element.val()) {
      remoteTransferResults.push($element.val());
    }
  });

  return remoteTransferResults.length > 0 ? `远处转移：${remoteTransferResults.join('、')}` : '';
}

function getDescription() {
  let result = []
  let fisrtContent = getFisrtContent()
  result = result.concat(fisrtContent)
  let secondContent = getSecondContent()
  result.push(secondContent)
  let thirdContent = getThirdContent()
  result.push(thirdContent)
  let fourthContent = getFourthContent()
  result.push(fourthContent)
  let fifthContent = getFifthContent()
  result.push(fifthContent)
  let sixthContent = getSixthContent()
  result.push(sixthContent)

  result = result.filter(Boolean)
  return result.join('\n')
}
function getFisrtContent() {
  // 找到id为5结尾的元素
  // 找到所有的checkbox
  let result = []
  // 序号
  // 转为数组
  DOM_CACHE.bzItems.each(function () {
    let leftMap = {} // 左肺
    let rightMap = {} // 右肺
    let lastResult = [] // 最后的总和
    // 识别肺部
    let textResult = [] // 肺部识别
    let leftUp = Array.from($(this).find('[id$="-5-1-1"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    let leftDown = Array.from($(this).find('[id$="-5-1-2"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    if (leftUp.length) {
      leftMap['leftUp'] = leftUp
    }
    if (leftDown.length) {
      leftMap['leftDown'] = leftDown
    }
    if (leftMap.leftUp && leftMap.leftDown) {
      textResult.push(`左肺上叶${leftMap.leftUp.join('、')},下叶${leftMap.leftDown.join('、')}`)
    } else {
      if (leftMap.leftUp) {
        textResult.push(`左肺上叶${leftMap.leftUp.join('、')}`)
      }
      if (leftMap.leftDown) {
        textResult.push(`左肺下叶${leftMap.leftDown.join('、')}`)
      }
    }
    let rightUp = Array.from($(this).find('[id$="-5-2-1"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    let rightMid = Array.from($(this).find('[id$="-5-2-2"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    let rightDown = Array.from($(this).find('[id$="-5-2-3"]').parent().find('input:checked').map(function () {
      return $(this).val()
    }));
    if (rightUp.length) {
      rightMap['rightUp'] = rightUp
    }
    if (rightMid.length) {
      rightMap['rightMid'] = rightMid
    }
    if (rightDown.length) {
      rightMap['rightDown'] = rightDown
    }
    if (rightMap.rightUp && rightMap.rightMid && rightMap.rightDown) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')},中叶${rightMap.rightMid.join('、')},下叶${rightMap.rightDown.join('、')}`)
    } else if (rightMap.rightUp && rightMap.rightMid) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')},中叶${rightMap.rightMid.join('、')}`)
    } else if (rightMap.rightUp && rightMap.rightDown) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')},下叶${rightMap.rightDown.join('、')}`)
    } else if (rightMap.rightMid && rightMap.rightDown) {
      textResult.push(`右肺中叶${rightMap.rightMid.join('、')},下叶${rightMap.rightDown.join('、')}`)
    } else if (rightMap.rightUp) {
      textResult.push(`右肺上叶${rightMap.rightUp.join('、')}`)
    } else if (rightMap.rightMid) {
      textResult.push(`右肺中叶${rightMap.rightMid.join('、')}`)
    } else if (rightMap.rightDown) {
      textResult.push(`右肺下叶${rightMap.rightDown.join('、')}`)
    }
    textResult = [textResult.join(',')]
    if (textResult.length) {
      textResult.push('见')
    }
    $(this).find('[data-group="bzsm"]').find('input:checked').each(function (index, element) {
      textResult.push($(element).val() === '单发' ? '一' : '多发');
    })
    $(this).find('[data-group="bzxt"]').find('input:checked').each(function (index, element) {
      textResult.push($(element).val());
    })
    textResult.push('病灶')
    textResult = [textResult.join('')] // 第一句
    $(this).find('[data-group="bzby"]').find('input:checked').each(function (index, element) {
      let bzby = '边缘'
      let val = $(element).val()
      if (val === '光滑') {
        bzby += val
      } else {
        bzby += val
        let bzbybgh = $(element).parent().parent().find('[data-group="bzbybgh"]').find('input:checked').map(function () {
          return $(this).val()
        }).get()
        if (bzbybgh.includes('毛刺')) {
          let bzbybghmc = $(element).parent().parent().find('[data-group="bzbybghmc"]').find('input:checked').val()
          if (bzbybghmc) {
            let checkIndex = bzbybgh.indexOf('毛刺')
            checkIndex != -1 ? bzbybgh[checkIndex] = bzbybghmc : ''
          }
        }
        if (bzbybgh.length) {
          bzby += `（${bzbybgh.join('、')}）`
        }
      }
      textResult.push(bzby)
    })
    let size = ''
    $(this).find('[data-srname="大小约："]').parent().find('input').map(function () {
      return $(this).val()
    }).get().forEach((item, index) => {
      item = item === '' ? '0' : item
      if (index === 0) {
        size += `大小约${item}mmx`
      } else if (index === 1) {
        size += `${item}mmx`
      } else {
        size += `${item}mm`
      }
    })
    if (size) {
      textResult.push(size)
    }
    if (textResult.length) {
      lastResult.push(textResult.join(',') + '。')
    }
    textResult = []
    let psmd = $(this).find('[data-group="psmd"]').find('input:checked').map(function () {
      return $(this).val().replace('密度', '')
    }).get()
    if (psmd.length) {
      textResult.push(`病灶平扫呈${psmd.join('、')}${psmd.length > 1 ? '混杂' : ''}密度`)
    }
    let density = $(this).find('[data-group="density"]').find('input:checked').val()
    let density_bjy = $(this).find('[data-group="density_bjy"]').find('input:checked').map(function () {
      return $(this).val()
    }).get()
    if (density === '均匀') {
      textResult.push('密度均匀')
    } else if (density === '不均匀') {
      if (density_bjy.length) {
        textResult.push(`密度不均匀(${density_bjy.join('、')})`)
      } else {
        textResult.push('密度不均匀')
      }
    }
    let psCt = $(this).find('[data-srname="平扫：CT"]').val()
    if (psCt) {
      textResult.push('平扫CT值' + psCt + 'HU')
    }
    let zqdmCt = $(this).find('[data-srname="增强动脉期："]').val()
    if (zqdmCt) {
      textResult.push('增强动脉期CT值' + zqdmCt + 'HU')
      let qhcd = $(this).find('[data-group="qhcd"]').find('input:checked').val()
      if (qhcd) {
        textResult.push(`呈${qhcd}`)
      }
    }
    let zmqCt = $(this).find('[data-srname="静脉期："]').val()
    if (zmqCt) {
      textResult.push('静脉期CT值' + zmqCt + 'HU')
    }
    let nbjg = $(this).find('[data-group="nbjg"]').find('input:checked').map(function () {
      return $(this).val()
    }).get()
    if (nbjg.length) {
      textResult.push(`病灶内可见${nbjg.join('、')}`)
    }
    if (textResult.length) {
      lastResult.push(textResult.join('，') + '。')
    }
    textResult = []
    let bzzwjfjbszx = $(this).find('[data-toggle-group="bzzwjfjbszx"]')
    let bzzwjfjbszxList = $(bzzwjfjbszx).find('input:checked').map(function () {
      let subVal = $(this).parent().siblings('textarea').val()
      if (subVal) {
        return subVal
      } else {
        return $(this).val()
      }
    }).get()
    if (bzzwjfjbszxList.length) {
      textResult.push(`病灶周围继发及伴随征象：${bzzwjfjbszxList.join('、')}`)
    }
    if (textResult.length) {
      lastResult.push(textResult.join('，') + '。')
    }
    let jbqf = $(this).find('[data-group="jbqf"]').find('input:checked,textarea').map(function () {
      let val = $(this).val()
      if (val) {
        return val
      }
    }).get()
    if (jbqf.length) {
      lastResult.push(`病灶局部侵犯${jbqf.join('、')}` + '。')
    }
    textResult = []
    result.push('　　' + lastResult.join(''))
  })
  return result
}
function getSecondContent() {
  let result = []
  let dfawj = $('[name="dfawj"]:checked').val()
  if (dfawj) {
    if (dfawj === '未见') {
      result.push('同一肺叶未见转移')
    } else {
      let dfawjVal = $('[name="dfawj"]').parent().parent().siblings('textarea').val()
      if (dfawjVal) {
        result.push(dfawjVal)
      } else {
        result.push('同一肺叶可见转移')
      }
    }
  }
  let tcbtfynglx = $('[name="tcbtfynglx"]:checked').val()
  if (tcbtfynglx) {
    if (tcbtfynglx === '未见') {
      result.push('同侧肺内不同肺叶未见转移')
    } else {
      let tcbtfynglxVal = $('[name="tcbtfynglx"]').parent().parent().siblings('textarea').val()
      if (tcbtfynglxVal) {
        result.push(tcbtfynglxVal)
      } else {
        result.push('同侧肺内不同肺叶可见转移')
      }
    }
  }
  let dcfycxzy = $('[name="dcfycxzy"]:checked').val()
  if (dcfycxzy) {
    if (dcfycxzy === '未见') {
      result.push('同侧肺内不同肺叶未见转移')
    } else {
      let dcfycxzyVal = $('[name="dcfycxzy"]').parent().parent().siblings('textarea').val()
      if (dcfycxzyVal) {
        result.push(dcfycxzyVal)
      } else {
        result.push('同侧肺内不同肺叶可见转移')
      }
    }
  }
  // 同侧胸膜
  let zcycx = $('[name="zcycx"]:checked').val()
  if (zcycx) {
    if (zcycx === '未见') {
      result.push('同侧胸膜未见转移')
    } else {
      let zcycxVal = $('[name="zcycx"]').parent().parent().siblings('textarea').val()
      if (zcycxVal) {
        result.push(zcycxVal)
      } else {
        result.push(`同侧胸膜可见转移结节`)
      }
    }
  }
  let xmjy = $('[name="xmjy"]:checked').val()
  if (xmjy) {
    if (xmjy === '未见') {
      result.push('未见胸膜积液')
    } else {
      let xmjyVal = $('[name="xmjy"]').parent().parent().siblings('textarea').val()
      if (xmjyVal) {
        result.push(xmjyVal)
      } else {
        result.push('可见胸膜积液')
      }
    }
  }
  let xbzcycx = $('[name="xbzcycx"]:checked').val()
  if (xbzcycx) {
    if (xbzcycx === '未见') {
      result.push('心包未见转移')
    } else {
      let xbzcycxVal = $('[name="xbzcycxVal"]').parent().parent().siblings('textarea').val()
      if (xbzcycxVal) {
        result.push(xbzcycxVal)
      } else {
        result.push('心包可见转移结节')
      }
    }
  }
  let xbjy = $('[name="xbjy"]:checked').val()
  if (xbjy) {
    if (xbjy === '未见') {
      result.push('未见心包积液')
    } else {
      let xbjyVal = $('[name="xbjy"]').parent().parent().siblings('textarea').val()
      if (xbjyVal) {
        result.push(xbjyVal)
      } else {
        result.push('心包可见积液')
      }
    }
  }
  if (result.length) {
    return `　　${result.join(',')}。`
  }
}
function getThirdContent() {
  let result = []
  let isAreaLbjList = lbjList.filter(item => !item.isOther)
  let notIsAreaLbjList = lbjList.filter(item => item.isOther)
  let lbjQyTotal = isAreaLbjList.reduce((pre, cur) => {
    let total = cur.num.map(item => $(item).val()).filter(Boolean).reduce((pre, cur) => pre + Number(cur), 0)
    pre += total
    return pre
  }, 0)
  if (lbjQyTotal) {
    result.push(lbjQyTotal + '枚可疑区域淋巴结转移，分别位于')
    let areaList = []
    isAreaLbjList.forEach(item => {
      if (item.subLabel) {
        let checked1 = $(item.selector[0]).is(':checked') // 左
        let checked2 = $(item.selector[1]).is(':checked') // 右
        let localNum = $(item.num[0]).val()
        let localSize = $(item.size[0]).val()
        let localStr = ''
        let textList = []
        if (checked1) {
          localStr = '左侧'
        }
        if (checked2) {
          localStr = '右侧'
        }
        if (checked1 && checked2) {
          localStr = '双侧'
        }
        if(checked1 || checked2){
          textList.push(localStr+item.subLabel)
          if(localNum){
            textList.push(`${localNum}枚`)
          }
          if(localSize){
            textList.push(`最大短径：${localSize}mm`)
          }
          areaList.push(textList.join('，'))
        }
      } else {
        item.selector.forEach((selector, index) => {
          let checked = $(selector).is(':checked')
          let checkedVal = $(selector).val()
          if (checked) {
            let localNum = $(item.num[index]).val()
            let localSize = $(item.size[index]).val()
            let textList = []
            textList.push(checkedVal)
            if(localNum){
              textList.push(`${localNum}枚`)
            }
            if(localSize){
              textList.push(`最大短径：${localSize}mm`)
            }
            areaList.push(textList.join('，'))
          }
        })
      }
    })
    if (areaList.length) {
      result.push(areaList.join('；') + '。')
    }
  }
  let notLbjQyTotal = notIsAreaLbjList.reduce((pre, cur) => {
    let total = cur.num.map(item => $(item).val()).filter(Boolean).reduce((pre, cur) => pre + Number(cur), 0)
    pre += total
    return pre
  }, 0)
  if (notLbjQyTotal) {
    result.push(notLbjQyTotal + '枚可疑非区域淋巴结转移，位于')
    notIsAreaLbjList.forEach(item=>{
      let isChecked = $(item.selector[0]).is(':checked')
      let checkedVal = $(item.selector[0]).val()
      let otherVal = $(item.otherSelector[0]).val()
      let localStr = otherVal ? otherVal: checkedVal
      let localNum = $(item.num[0]).val()
      let localSize = $(item.size[0]).val()
      let textList = []
      if(isChecked){
        textList.push(localStr)
        if(localNum){
          textList.push(`${localNum}枚`)
        }
        if(localSize){
          textList.push(`最大短径：${localSize}mm`)
        }
        result.push(textList.join('，') + '。')
      }
    })
  }
  if(result.join('')){
    return '　　淋巴结：'+ result.join('')
  }

}
function getFourthContent() {
  let result = []
  let radio91 = $('[name="radio-9-1"]:checked').val()
  if (radio91) {
    if (radio91 === '未见异常') {
      result.push('余双肺支气管血管束走行分布未见异常')
    } else {
      let radio91Val = $('[name="radio-9-1"]').parent().parent().siblings('textarea').val()
      if (radio91Val) {
        result.push(radio91Val)
      } else {
        result.push('余双肺支气管血管束走行分布异常')
      }
    }
  }
  let radio92 = $('[name="radio-9-2"]:checked').val()
  if (radio92) {
    if (radio92 === '通畅') {
      result.push('段及段以上支气管通畅')
    } else {
      let radio92Val = $('[name="radio-9-2"]').parent().parent().siblings('textarea').val()
      if (radio92Val) {
        result.push(radio92Val)
      } else {
        result.push('段及段以上支气管异常')
      }
    }
    let radio93 = $('[name="radio-9-3"]:checked').val()
    if (radio93) {
      if (radio93 === '未见积液') {
        result.push('双侧未见胸腔积液')
      } else {
        let radio93Val = $('[name="radio-9-3"]').parent().parent().siblings('textarea').val()
        if (radio93Val) {
          result.push(radio93Val)
        } else {
          result.push('双侧可见胸腔积液')
        }
      }
    }
    let radio94 = $('[name="radio-9-4"]:checked').val()
    if (radio94) {
      if (radio94 === '形态未见明显异常') {
        result.push('心脏形态未见明显异常')
      } else {
        let radio94Val = $('[name="radio-9-4"]').parent().parent().siblings('textarea').val()
        if (radio94Val) {
          result.push(radio94Val)
        } else {
          result.push('心脏形态异常')
        }

      }
    }
  }
  if (result.length) {
    return `　　${result.join(',')}。`
  }
}
function getFifthContent() {
  let result = []
  $('[name="yczy"]:checked').each(function () {
    let $checkedVal = $(this).val()
    let currentVal = $(this).parent().siblings('textarea').val()
    if (currentVal) {
      result.push(currentVal)
    } else {
      result.push($checkedVal + '可见异常')
    }
  })
  if (result.length) {
    return `　　远处转移：${result.join(',')}。`
  }
}
function getSixthContent() {
  let result = $('#fact-rt-11-1').val()
  if (result) {
    return `　　其他征象：${result}`
  }
}
// 将函数暴露到全局作用域
window.getAllBzData = getAllBzData;
window.initRemoteMetastasisCascade = initRemoteMetastasisCascade;
window.checkInitialRemoteMetastasisStates = checkInitialRemoteMetastasisStates;
window.getZy = getZy;
window.getZy1TransferData = getZy1TransferData;
window.getLymphNodeTransferData = getLymphNodeTransferData;
window.getOtherLymphNodeValue = getOtherLymphNodeValue;
window.checkLymphNodeSelection = checkLymphNodeSelection;
window.getRemoteTransferData = getRemoteTransferData;
