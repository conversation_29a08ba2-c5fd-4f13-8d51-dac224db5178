$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });


  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let strList = [];
  //感染性心内膜炎-瓣
  $('#xnmy-wz .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length) {
        for (var i = 0; i < child.length; i++) {
          let bchild = child[i].child;
          if (bchild && bchild.length) {
            strList.push(child[i].val + bchild[0].val)
          } else {
            strList.push(child[i].val)
          }
        }
      }
    }
  })
  //感染性心内膜炎-赘生物
  $('#zsw .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length) {
        strList.push(curPid.val + child[0].val)
      } else {
        strList.push(curPid.val)
      }
    }
  })
  curKeyValData['xnmy-rt-22'] ? strList.push(curKeyValData['xnmy-rt-22'].val) : '';
  curKeyValData['xnmy-rt-23'] ? strList.push(curKeyValData['xnmy-rt-23'].val) : '';
  curKeyValData['xnmy-rt-24'] ? strList.push(curKeyValData['xnmy-rt-24'].val) : '';

  rtStructure.impression = '感染性心内膜炎' + strList.join('，')
}