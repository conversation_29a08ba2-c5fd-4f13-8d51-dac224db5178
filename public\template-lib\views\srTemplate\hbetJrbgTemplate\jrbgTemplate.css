.singleDisEditReport.main-page{
  min-width: unset;
}
#jrbg1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#jrbg1 * {
  font-family: '宋体';
}
#jrbg1 .jrbg-edit{
  padding: 8px 12px;
}
#jrbg1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#jrbg1 .black-lb {
  color: #303133;
}
#jrbg1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#jrbg1 .blue-lb:hover {
  opacity: 0.8;
}
#jrbg1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#jrbg1 .text-size, .diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#jrbg1 .text-size img, .diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#jrbg1 .text-size img:hover, .diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#jrbg1 .text-size .on, .diag-text-size .text-size .on {
  display: none;
}
#jrbg1 .editor-area {
  padding: 4px 0;
  min-height: 100px;
}
.editor-area textarea {
  width: 100%;
  height: 100px;
  border: none;
  font-size: 16px;
}
#jrbg1 .p-item + .p-item {
  margin-top: 12px;
}
.editor-area.default textarea{
  font-size: 16px;
}
.editor-area.large textarea{
  font-size: 18px;
}
.editor-area.larger textarea{
  font-size: 20px;
}
#jrbg1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#jrbg1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#jrbg1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#jrbg1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#jrbg1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#jrbg1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

*:disabled {
  background: #fff;
  color: unset;
}
textarea[readonly] {
  cursor: not-allowed;
}
#jrbg1 .jrbg-view {
  display: none;
}
[isview="true"] #jrbg1 .jrbg-edit {
  display: none;
}
[isview="true"] #jrbg1 .jrbg-view {
  display: flex;
}
#jrbg1 .jrbg-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 116px 56px;
  flex-direction: column;
  position: relative;
}
#jrbg1 .jrbg-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#jrbg1 .jrbg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}
#jrbg1 .jrbg-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
 span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
 }
}
#jrbg1 .jrbg-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
#jrbg1 .jrbg-view .hos-tit{
  font-family: '仿宋';
  font-size: 21px;
  line-height: 26px;
  font-style: normal;
  color: #000;
}
#jrbg1 .jrbg-view .sub-tit{
  font-size: 26px;
  color: #000;
  font-family: '宋体';
  margin-top: 20px;
  line-height: 30px;
}
#jrbg1 .jrbg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#jrbg1 .jrbg-view .gray-txt {
  color: #000;
  font-size: 16px;
  font-family: '宋体';
  white-space: pre-line;
  word-break: break-all;
}
.flex-column {
  margin: 8px auto;
  width: 520px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: 10px;
}
#jrbg1 .jrbg-view .report-img img {
  height: 180px;
  width: 240px;
  display: block;
  object-fit: contain;
}
#jrbg1 .jrbg-view .black-txt {
  color: #000;
  font-size: 16px;
  font-family: '宋体';
}
#jrbg1 .jrbg-view .red-txt {
  color: #000000;
  font-size: 16px;
}
#jrbg1 .jrbg-view .bold {
  font-weight: bold;
}
#jrbg1 .jrbg-view .info-i {
  width: 160px;
  display: flex;
  flex-wrap: wrap;
}
#jrbg1 .jrbg-view .info-i + .info-i {
  margin-left: 8px;
}
#jrbg1 .jrbg-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#jrbg1 .jrbg-view .view-patient .p-item {
  margin-top: 10px;
}
#jrbg1 .jrbg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#jrbg1 .jrbg-view .rt-sr-body {
  padding-top: 8px;
}
#jrbg1 .jrbg-view .desc-con {
  /* padding: 8px 0; */
  display: none;
  margin-bottom: 20px;
}
#jrbg1 .jrbg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#jrbg1 .jrbg-view .desc-con {
  display: flex;
  align-items: baseline;
}
#jrbg1 .jrbg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#jrbg1 .jrbg-view .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#jrbg1 .jrbg-view .reporter-i.w120 {
  width: 120px;
}
#jrbg1 .jrbg-view .reporter-i.w150 {
  width: 150px;
}
#jrbg1 .jrbg-view .reporter-i span {
  width: 70px;
}
#jrbg1 .jrbg-view .reporter-i span:last-child {
  flex: 1;
}
#jrbg1 .jrbg-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#jrbg1 .jrbg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#jrbg1 .jrbg-view .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}
#jrbg1 .jrbg-view .tip-wrap .tip-text {
  flex: 1;
}
#jrbg1 .jrbg-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  margin-bottom: 12px;
  display: none;
  padding-left: 86px;
}
#jrbg1 .jrbg-view .item-img {
  width: 240px;
  height: 180px;
  border: 1px solid #eee;
  margin-bottom: 8px;
}
#jrbg1 .jrbg-view .item-img:nth-child(odd){
  margin-right: 12px;
}
#jrbg1 .jrbg-view .item-img:nth-child(even){
  margin-left: 12px;
}
#jrbg1 .jrbg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#jrbg1 .p-item:nth-child(2) .text-size,#jrbg1 .p-item:nth-child(3) .text-size,#jrbg1 .p-item:nth-child(4) .text-size {
  display: none;
}
#jrbg1 .p-item:nth-child(2) .editor-area,#jrbg1 .p-item:nth-child(3) .editor-area {
  min-height: 72px;
}
#jrbg1 .p-item:nth-child(2) textarea,#jrbg1 .p-item:nth-child(3) textarea {
  height: 72px;
}
#jrbg1 .jrbg-view .blh-tit{
  text-align: right;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #jrbg1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #jrbg1 .jrbg-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #jrbg1 .jrbg-view .view-head,
[entry-type="5"] #jrbg1 .jrbg-view .view-patient,
[entry-type="5"] #jrbg1 .jrbg-view .tip-wrap {
  display: none;
}
[entry-type="5"] #jrbg1 .jrbg-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #jrbg1 .jrbg-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
