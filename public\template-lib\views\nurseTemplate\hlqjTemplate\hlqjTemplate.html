<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/nurseTemplate/hlqjTemplate/hlqjTemplate.css">
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/nurseTemplate/hlqjTemplate/hlqjTemplate.js"></script>

<ul class="t-pg" style="height: 100%;overflow: auto;">
  <li class="page" id="hlqj1">
    <div class="hlqj-content edit">
      <div class="ct-title rt-sr-w" id="hlqj-rt-0001" rt-sc="pageId:hlqj1;name:抢救信息;wt:;desc:抢救信息;vt:;pvf:1;">抢救信息</div>
      <div class="item-content form-content" style="padding: 12px 0 0 16px;">
        <div class="form-item">
          <div class="form-item-title">开始抢救时间：</div>
          <div class="layui-inline layui-form">
            <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-rt-0001-001" pid="hlqj-rt-0001"
              data-type="edit" placeholder="选择日期" rt-sc="req:1;pageId:hlqj1;name:开始抢救时间;wt:1;desc:开始抢救时间;vt:;pvf:;code:450"
              rt-req="1">
          </div>
        </div>
        <div class="form-item">
          <div class="form-item-title">医生到达时间：</div>
          <div class="layui-inline layui-form">
            <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-rt-0001-002" pid="hlqj-rt-0001"
              placeholder="选择日期" rt-sc="req:1;pageId:hlqj1;name:医生到达时间;wt:1;desc:医生到达时间;vt:;pvf:;code:451" rt-req="1">
          </div>
        </div>
        <div class="form-item">
          <div class="form-item-title">参与抢救人员：</div>
          <div class="layui-inline layui-form">
            <input type="text" class="rt-sr-w layui-input" style="width: 522px;" id="hlqj-rt-0001-003"
              pid="hlqj-rt-0001" placeholder="请输入" rt-sc="pageId:hlqj1;name:参与抢救人员;wt:1;desc:参与抢救人员;vt:;pvf:;code:452">
          </div>
        </div>
      </div>
      <div class="item-content" style="padding-top: 0;">
        <div class="form-item">
          <div class="form-item-title rt-sr-w" id="hlqj-rt-0002"
            rt-sc="pageId:hlqj1;name:抢救前处理;wt:;desc:抢救前处理;vt:;pvf:1;code:464">抢救前处理：</div>
          <div class="gray-item" id="rescueHowToHandle" data-type="edit">

          </div>
          <span data-type="preview" data-key="hlqj-rt-0002"></span>
        </div>
        <div class="gray-item" style="margin-left: 84px;" data-type="edit">
          <label class="radio-label" for="hlqj-rt-0002-900">
            <input type="checkbox" class="rt-sr-w" name="ck-hlcd" value="插管-气囊通气" id="hlqj-rt-0002-900"
              pid="hlqj-rt-0002" rt-sc="pageId:hlqj1;name:插管-气囊通气;wt:4;desc:插管-气囊通气;vt:;pvf:;">
            <span class="rt-sr-lb">插管-气囊通气</span>
          </label>
          <div class="second-content">
            <div class="form-item" style="margin-bottom: 0;">
              <div class="form-item-title">气管插管型号：</div>
              <div class="layui-inline layui-form">
                <input type="text" class="rt-sr-w layui-input" style="width: 120px;" id="hlqj-rt-0002-900-001"
                  pid="hlqj-rt-0002-900" placeholder="请输入" rt-sc="pageId:hlqj1;name:气管插管型号;wt:1;desc:气管插管型号;vt:;pvf:;">
              </div>
            </div>
            <div class="form-item" style="margin-bottom: 0;">
              <div class="form-item-title">插入长度：</div>
              <div class="layui-inline layui-form">
                <input type="text" class="rt-sr-w layui-input" style="width: 120px;" id="hlqj-rt-0002-900-002"
                  pid="hlqj-rt-0002-900" placeholder="请输入" rt-sc="pageId:hlqj1;name:插入长度：;wt:1;desc:插入长度：;vt:;pvf:;">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ct-title" data-type="edit">
        <div>生命体征</div>
        <div class="add-btn" onclick="addzjHandler()"><i class="layui-icon"></i> 添加记录</div>
      </div>
      <div data-type="preview" class="ct-title">生命体征</div>
      <div class="item-content" style="padding: 2px 16px;">
        <div class="page-label rt-sr-w" id="hlqj-zjlist-1" style="display: none;"
          rt-sc="pageId:hlqj1;name:生命体征;wt:;desc:生命体征;vt:;pvf:1;">生命体征</div>
        <div class="table-content">
          <table class="layui-table">
            <colgroup>
              <col width="62">
              <col width="200">
              <col width="120">
              <col width="120">
              <col>
              <col>
              <col>
              <col data-type="edit" width="60">
            </colgroup>
            <thead>
              <tr>
                <th>序号</th>
                <th>记录时间</th>
                <th>心率 (次/分)</th>
                <th>呼吸 (次/分)</th>
                <th>血压 (mmHg)</th>
                <th>氧饱和度 (%)</th>
                <th>瞳孔 (mm)</th>
                <th data-type="edit">操作</th>
              </tr>
            </thead>
            <tbody class="live-con" data-type="edit" tab-target="hlqj-zjlist-000">
              <tr class="bz-tr rt-sr-w" id="hlqj-zjlist-000" pid="hlqj-zjlist-1"
                rt-sc="pageId:wsjl1;name:生命体征记录;wt:;desc:生命体征记录;vt:;pvf:2;">

                <td class="rt-sr-w" id="hlqj-zjlist-000-1" pid="hlqj-zjlist-000"
                  rt-sc="pageId:wsjl1;name:序号;wt:;desc:序号;vt:;pvf:1;">1
                </td>
                <td>
                  <input class="rt-sr-w layui-input" id="hlqj-zjlist-000-2" pid="hlqj-zjlist-000"
                    rt-sc="pageId:wsjl1;name:表格记录时间;wt:1;desc:表格记录时间;vt:;pvf:;">
                </td>
                <td>
                  <input class="rt-sr-w layui-input" id="hlqj-zjlist-000-3" pid="hlqj-zjlist-000"
                    rt-sc="req:1;pageId:wsjl1;name:表格心率;wt:1;desc:表格心率;vt:;pvf:;" rt-req="1">
                </td>
                <td>
                  <input class="rt-sr-w layui-input" id="hlqj-zjlist-000-4" pid="hlqj-zjlist-000"
                    rt-sc="req:1;pageId:wsjl1;name:表格呼吸;wt:1;desc:表格呼吸;vt:;pvf:;" rt-req="1">
                </td>
                <td>
                  <div class="layui-inline layui-form form-box">
                    <input type="text" class="rt-sr-w layui-input input-width" style="width: 50%;"
                      id="hlqj-zjlist-000-5" pid="hlqj-zjlist-000" placeholder="收缩压"
                      rt-sc="pageId:hlqj1;name:表格收缩压;wt:1;desc:表格收缩压;vt:;pvf:;">
                    <div>/</div>
                    <input type="text" class="rt-sr-w layui-input input-width" style="width: 50%;"
                      id="hlqj-zjlist-000-6" pid="hlqj-zjlist-000" placeholder="舒张压"
                      rt-sc="pageId:hlqj1;name:表格舒张压;wt:1;desc:表格舒张压;vt:;pvf:;">
                  </div>
                </td>
                <td>
                  <input class="rt-sr-w layui-input" id="hlqj-zjlist-000-7" pid="hlqj-zjlist-000"
                    rt-sc="req:1;pageId:wsjl1;name:表格氧饱和度;wt:1;desc:表格氧饱和度;vt:;pvf:;" rt-req="1">
                </td>
                <td>
                  <input class="rt-sr-w layui-input" id="hlqj-zjlist-000-8" pid="hlqj-zjlist-000"
                    rt-sc="req:1;pageId:wsjl1;name:瞳孔;wt:1;desc:瞳孔;vt:;pvf:;" rt-req="1">
                </td>
                <td data-type="edit"><span style="color: #1885F2;cursor: pointer;"
                    onclick="delzjFuc(this,'000')">删除</span></td>
              </tr>
            </tbody>
            <tbody class="live-con" data-type="preview"></tbody>
          </table>
        </div>
      </div>
      <div class="ct-title rt-sr-w" id="hlqj-rt-0004" rt-sc="pageId:hlqj1;name:抢救前处理;wt:;desc:抢救前处理;vt:;pvf:1;">病人情况
      </div>
      <div class="item-content">
        <textarea name="desc" placeholder="请输入" class="rt-sr-w layui-textarea" id="hlqj-rt-0004-001" pid="hlqj-rt-0004"
          rt-sc="pageId:hlqj1;name:病人情况;wt:1;desc:病人情况;vt:;pvf:;code:453"></textarea>
      </div>
      <div class="ct-title ">
        <div>处置医嘱</div>
        <div class="add-btn" data-type="edit" onclick="addBzHandler()"><i class="layui-icon"></i> 添加记录</div>
      </div>
      <div class="rt-sr-w" id="hlqj-bzlist-1" rt-sc="pageId:hlqj1;name:处置医嘱;wt:;desc:处置医嘱;vt:;pvf:1;code:454"
        style="display: none;">处置医嘱</div>
      <div class="advice-list item-content" style="padding-bottom: 0;">
        <div class="advice-item" tab-target="hlqj-bzlist-000">
          <div class="advice-content" id="hlqj-bzlist-000" rt-sc="pageId:hlqj1;name:医嘱记录;wt:;desc:医嘱记录;vt:;pvf:1;"
            pid="hlqj-bzlist-1">
            <div class="advice-title">
              <div class="rt-sr-w" id="hlqj-bzlist-000-1" pid="hlqj-bzlist-000"
                rt-sc="pageId:hlqj1;name:医嘱记录 1;wt:;desc:医嘱记录 1;vt:;pvf:;">医嘱记录 1</div>
              <div data-type="edit" style="color: #606266;cursor: pointer;" onclick="delTab(this, '000')"><i
                  class="layui-icon"></i> 删除
              </div>
            </div>
            <div class="gray-item" style="padding: 12px 12px 0 12px;">
              <div class="form-item">
                <div class="form-item-title">执行时间：</div>
                <div class="layui-inline layui-form">
                  <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-bzlist-000-2"
                    pid="hlqj-bzlist-000" placeholder="请选择" readonly=""
                    rt-sc="pageId:hlqj1;name:执行时间;wt:1;desc:执行时间;vt:;pvf:;">
                </div>
              </div>
              <div class="form-item">
                <div class="form-item-title">确认医生：</div>
                <div class="layui-inline layui-form showInt">
                  <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-bzlist-000-3"
                    pid="hlqj-bzlist-000" placeholder="请输入" rt-sc="pageId:hlqj1;name:确认医生;wt:1;desc:确认医生;vt:;pvf:;">
                </div>
              </div>
              <div class="form-item">
                <div class="form-item-title">确认护士：</div>
                <div class="layui-inline layui-form showInt">
                  <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-bzlist-000-4"
                    pid="hlqj-bzlist-000" placeholder="请输入" rt-sc="pageId:hlqj1;name:确认护士;wt:1;desc:确认护士;vt:;pvf:;">
                </div>
              </div>
            </div>
            <div class="gray-item" style="padding: 0px 12px 0 12px;align-items: start;">
              <div class="form-item-title">医嘱内容：</div>
              <div class="layui-form" style="flex: 1;">
                <textarea name="desc" placeholder="请输入" class="rt-sr-w layui-textarea" id="hlqj-bzlist-000-5"
                  pid="hlqj-bzlist-000" rt-sc="pageId:hlqj1;name:医嘱内容;wt:1;desc:医嘱内容;vt:;pvf:;"></textarea>
              </div>
            </div>
            <div class="gray-item" style="padding: 12px;">
              <div class="form-item-title" style="width: 70px;text-align: right;">用法：</div>
              <div class="layui-form" style="flex: 1;">
                <input name="desc" placeholder="请输入" class="rt-sr-w layui-input" id="hlqj-bzlist-000-6"
                  pid="hlqj-bzlist-000" rt-sc="pageId:hlqj1;name:用法;wt:1;desc:用法;vt:;pvf:;">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ct-title rt-sr-w" id="hlqj-rt-0006" rt-sc="pageId:hlqj1;name:处理结果;wt:;desc:处理结果;vt:;pvf:1;">处理结果</div>
      <div class="item-content">
        <div class="gray-item">
          <div class="gray-item" id="rescueHandleResult">
          </div>
          <span data-type="preview" data-key="hlqj-rt-0006"></span>
        </div>
        <div class="layui-form" style="margin-top: 8px;" data-type="edit">

          <input type="text" class="rt-sr-w layui-input" id="hlqj-rt-0006-900" pid="hlqj-rt-0006" placeholder="请输入"
            rt-sc="pageId:hlqj1;name:其他处理结果;wt:1;desc:其他处理结果;vt:;pvf:;code:455">
        </div>
      </div>
      <div class="ct-title rt-sr-w" id="hlqj-rt-0007" rt-sc="pageId:hlqj1;name:抢救效果;wt:;desc:抢救效果;vt:;pvf:1;">抢救效果</div>
      <input type="text" style="display: none;" class="rt-sr-w layui-input input-width" id="qiangjiuEffect" 
              placeholder="请选择" rt-sc="pageId:hlqj1;name:抢救效果;wt:1;desc:抢救效果;vt:;pvf:1;code:465">
      <div class="item-content form-content" style="justify-content: space-between;padding-bottom: 0;">
        <div class="result-content">
          <label class="radio-label" for="hlqj-rt-0007-001">
            <input type="checkbox" class="rt-sr-w" name="ck-cpr" value="停止CPR" id="hlqj-rt-0007-001" pid="hlqj-rt-0007"
              rt-sc="pageId:hlqj1;name:停止CPR;wt:4;desc:停止CPR;vt:;pvf:;code:456">
            <span class="rt-sr-lb">停止CPR</span>
          </label>
          <div class="layui-form">
            <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-rt-0007-001-001" pid="hlqj-rt-0007-001"
              placeholder="请选择" rt-sc="pageId:hlqj1;name:停止CPR日期;wt:1;desc:停止CPR日期;vt:;pvf:;code:457">
          </div>
        </div>
        <div class="result-content">
          <label class="radio-label" for="hlqj-rt-0007-002">
            <input type="checkbox" class="rt-sr-w" name="ck-zzxh" value="自主循环恢复" id="hlqj-rt-0007-002"
              pid="hlqj-rt-0007" rt-sc="pageId:hlqj1;name:自主循环恢复;wt:4;desc:自主循环恢复;vt:;pvf:;code:458">
            <span class="rt-sr-lb">自主循环恢复</span>
          </label>
          <div class="layui-form">
            <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-rt-0007-002-001" pid="hlqj-rt-0007-002"
              placeholder="请选择" rt-sc="pageId:hlqj1;name:自主循环恢复日期;wt:1;desc:自主循环恢复日期;vt:;pvf:;code:459">
          </div>
        </div>
        <div class="result-content">
          <label class="radio-label" for="hlqj-rt-0007-003">
            <input type="checkbox" class="rt-sr-w" name="ck-xgsw" value="宣告临床死亡" id="hlqj-rt-0007-003"
              pid="hlqj-rt-0007" rt-sc="pageId:hlqj1;name:宣告临床死亡;wt:4;desc:宣告临床死亡;vt:;pvf:;code:460">
            <span class="rt-sr-lb">宣告临床死亡</span>
          </label>
          <div class="layui-form">
            <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-rt-0007-003-001" pid="hlqj-rt-0007-003"
              placeholder="请选择" rt-sc="pageId:hlqj1;name:宣告临床死亡日期;wt:1;desc:宣告临床死亡日期;vt:;pvf:;code:461">
          </div>
        </div>
        <div class="result-content">
          <label class="radio-label" for="hlqj-rt-0007-004">
            <input type="checkbox" class="rt-sr-w" name="ck-fqqj" value="家属要求放弃抢救" id="hlqj-rt-0007-004"
              pid="hlqj-rt-0007" rt-sc="pageId:hlqj1;name:家属要求放弃抢救;wt:4;desc:家属要求放弃抢救;vt:;pvf:;code:462">
            <span class="rt-sr-lb">家属要求放弃抢救</span>
          </label>
          <div class="layui-form">
            <input type="text" class="rt-sr-w layui-input input-width" id="hlqj-rt-0007-004-001" pid="hlqj-rt-0007-004"
              placeholder="请选择" rt-sc="pageId:hlqj1;name:家属要求放弃抢救日期;wt:1;desc:家属要求放弃抢救日期;vt:;pvf:;code:463">
          </div>
        </div>
      </div>
      <div class="ct-title">记录信息</div>
      <div class="item-content">
        <div class="gray-item" style="padding-bottom: 6px;">
          <span style="width: 63px;">记录人：</span>
          <div class="showInt">
            <input class="layui-input rt-sr-w" placeholder="请选择" autocomplete="off" readonly style="width: 200px;"
              id="hlqj-rt-0003" rt-sc="pageId:ctfzq1;name:记录人;wt:1;desc:记录人;vt:;pvf:;">
          </div>
          <span class="second-title" style="margin-left: 24px;display: none;">记录时间：</span>
          <input type="text" id="hlqj-rt-0005" placeholder="请选择" style="width: 200px;display: none;"
            class="rt-sr-w layui-input" rt-sc="pageId:hlqj1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:1;">
        </div>
      </div>
      <input class="rt-sr-w" id="optInfo" style="display: none;" rt-sc="pageId:hlqj1;name:记录人;wt:1;desc:记录人;vt:;pvf:1;">
    </div>
  </li>
</ul>