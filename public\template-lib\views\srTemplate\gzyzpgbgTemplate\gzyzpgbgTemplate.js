$(function() {
  window.initHtmlScript = initHtmlScript;
  
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var dbIdMap = {
  // 'xbbwa-rt-67': '淋巴结分组',
}; // 添加双击事件节点
var lbjMap = {
  'gzyzpg-rt-42': 'G:',
  'gzyzpg-rt-43': 'S:',
};
var lbjMap2 = {
  'gzyzpg-rt-6': '楔形活检(W)：',
  'gzyzpg-rt-7': '穿刺活检(N)：',
  'gzyzpg-rt-8': '大小/长度：',
};
var gmOrderResultList = {}; // 医嘱结果列表


function resizeTextarea(event) {
  event.target.style.height = 'auto';
  event.target.style.height = (event.target.scrollHeight) + 'px';
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#gzblpg1 .view-wrap"),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      resetLogoStyle();
      // var operateUsers = getOperatorName(publicInfo, $('.view-wrap .bb'));
      // if(operateUsers) {
      //   setTimeout(() => {
      //     $('.view-wrap').css('padding-bottom', $('.view-wrap .rt-sr-footer').outerHeight() + 10);
      //   }, 0);
      // }
      getCloudFilmQrCodeUrl(publicInfo.examNo, $('.view-wrap .view-head'));
      initViewCon()
    } else {
      initEdit()
      // changeTextSize();
    }
  }
}

function initEdit() {
  if(publicInfo.freezeFlag === '1') {
    $('#gzyzpg-rt-4').prop('checked', true);
    return
  }
  if(publicInfo.sampleType === '快速石蜡' || publicInfo.sampleType === '快速石蜡,处理包埋'  || publicInfo.sampleType === '快速石蜡，处理包埋') {
    $('#gzyzpg-rt-3').prop('checked', true);
    return
  }
}
// 字号切换
function changeTextSize() {
  var localSize = getOrSetSizeType('get');
  setSizeDom(localSize);
  $('body').on('click', '.text-size img', function () {
    if ($(this).hasClass('on')) {
      return;
    }
    var size = $(this).closest('.img-wrap').attr('data-size');
    setSizeDom(size);
    getOrSetSizeType('set', size);
  })
}

// 设置字体大小元素展示
function setSizeDom(size) {
  $('.edit-wrap .on').hide();
  $('.edit-wrap .off').show();
  $('.edit-wrap .img-wrap[data-size="' + size + '"] .on').show();
  $('.edit-wrap .img-wrap[data-size="' + size + '"] .off').hide();
  $('.edit-wrap').removeClass('default large larger');
  $('.edit-wrap').addClass(size);
}

// 获取、设置缓存字体大小数据
function getOrSetSizeType(type, size) {
  var userId = publicInfo && publicInfo.userInfo ? publicInfo.userInfo.staffNo : publicInfo.optId;
  var sizeTypeList = getLocalStorage('rt_size_type') || {};
  if(type === 'get') {
    var sizeType = sizeTypeList[userId] || 'default';
    return sizeType;
  }
  if(type === 'set') {
    sizeTypeList[userId] = size;
    setLocalStorage('rt_size_type', sizeTypeList);
  }
}


function blurHandler(id) {
  var editContent = rtDialog.find('#edit-inp').val();
  $('[id="'+id+'"]').val(editContent);
}
function closeDiaHander() {
  $(window.frames.frameElement).removeClass('fixed-iframe');
  removeRtDialog();
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = curElem.find('#gzyzpg-rt-44').val();
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    rptImageList: rptImageList,   //报告图像
    // sampleSeen: curElem.find('#gzyzpg-rt-1').val(),  //肉眼所见
    examParam: '',   //特殊检查
  }
  // console.log(rtStructure);
}

function initInpAndSel(idList, optionList,lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click:function(obj){
    this.elem.val(obj.title);
    if(lenVal === 0){
      // changeSel(obj.title);
    }
    },
    style: lenVal !== 0 ? `width: ${lenVal}px;` : 'width: calc(100% - 146px)'
  })
}


// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  console.log(idAndDomMap);
  
  curElem.find('.view-wrap [data-key]').each(function(){
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var dataValue = publicInfo[key] || idAnVal;
    if(!dataValue) {
      let className = $(this).parent().attr('class');
      if(className && className.includes('parent')) {
        $(this).parent().hide()
      }
    }
    if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
      dataValue = dataValue + ' ' + publicInfo['affirmTime'];
    }
    if(key === 'gzyzpg-rt-41') {
      var lbjArr = [];
      for(let inpId in lbjMap) {
        var lbjInpVal = $(`[id=${inpId}]`).val() || '';
        lbjInpVal && lbjArr.push(lbjMap[inpId] + lbjInpVal);
      }
      if(lbjArr.length) {
        dataValue = lbjArr.join('       ') + '';
        $(this).parent().show();
      }
    }
    if(key === 'gzyzpg-rt-5') {
      var lbjArr = [];
      for(let inpId in lbjMap2) {
        var lbjInpVal = $(`[id=${inpId}]`).val() || '';
        if(inpId === 'gzyzpg-rt-8') {
          lbjInpVal+='cm'
        }
        lbjInpVal && lbjArr.push(lbjMap2[inpId] + lbjInpVal);
        
      }
      if(lbjArr.length) {
        dataValue = lbjArr.join('      ') + '';
        $(this).parent().show();
      }
    }
    
    $(this).html(dataValue)
    addIdToNodeByView(this, key, idAndDomMap);
  })
  curElem.find('.view-wrap [data-name]').each(function(){
    var key = $(this).attr('data-name');
    const selectedValue = $('input[name="'+ key+'"]:checked').val();
    if(!selectedValue) {
      let className = $(this).parent().attr('class');
      if(className.includes('parent')) {
        $(this).parent().hide()
      }
    }
    $(this).html(selectedValue)
  })
  curElem.find('.view-wrap [data-img]').each(function(){
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if(value) {
      var src = getSignImgHandler({staffNo:value});
      if(src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if(rptImageList && rptImageList.length) {
    var imgHtml = '';
    for(var image of rptImageList) {
      if(image.src) {
        imgHtml += `
        <div class="view_img">
          <img src="${image.src}" alt="">
        </div>
        `
      }
    }
    if(imgHtml) {
      curElem.find('.view-wrap .view_img_box').html(imgHtml);
      curElem.find('.view-wrap .view_img_box').show();
    }
  }
}