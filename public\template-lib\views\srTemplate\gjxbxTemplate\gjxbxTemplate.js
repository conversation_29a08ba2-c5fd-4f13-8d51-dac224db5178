$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; //结果集，用于判断是否为新报告
var yjxbxSelList = [
  {
    idList: ['yjxbx-rt-1'],
    lenVal: 80,
    optionList: [
      {title: '满意', id: 1},
      {title: '不满意', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['yjxbx-rt-2'],
    lenVal: 236,
    optionList: [
      {title: '炎症细胞覆盖', id: 1},
      {title: '鳞状细胞少', id: 2},
      {title: '出血过多', id: 3},
      {title: '抹片过厚或人为假象22', id: 4},
      {title: '', id: 5},
    ]
  },
  {
    idList: ['yjxbx-rt-4','yjxbx-rt-5','yjxbx-rt-6'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '无', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['yjxbx-rt-8','yjxbx-rt-9','yjxbx-rt-10','yjxbx-rt-11','yjxbx-rt-12'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '未见', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['yjxbx-rt-7'],
    lenVal: 160,
    optionList: [
      {title: '正常', id: 1},
      {title: '轻度发炎', id: 2},
      {title: '中度发炎', id: 3},
      {title: '重度发炎', id: 4},
    ]
  },
]; // 液基细胞学下拉列表
var xbdnabtSelList = [
  {
    idList: ['xbdnabt-rt-1'],
    lenVal: 80,
    optionList: [
      {title: '满意', id: 1},
      {title: '不满意', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['xbdnabt-rt-2'],
    lenVal: 236,
    optionList: [
      {title: '炎症细胞覆盖', id: 1},
      {title: '鳞状细胞少', id: 2},
      {title: '出血过多', id: 3},
      {title: '抹片过厚或人为假象', id: 4},
      {title: '', id: 5},
    ]
  },
  {
    idList: ['xbdnabt-rt-4'],
    lenVal: 320,
    optionList: [
      {title: '未见DNA倍体异常细胞', id: 1},
      {title: '细胞数量少，未见DNA倍体异常细胞', id: 2},
      {title: '可见少量DNA倍体异常细胞', id: 3},
      {title: '可见DNA倍体异常细胞', id: 3},
      {title: '可见大量DNA倍体异常细胞', id: 3},
    ]
  },
  {
    idList: ['xbdnabt-rt-5'],
    lenVal: 320,
    optionList: [
      {title: '定期复查', id: 1},
      {title: '6-12个月复查', id: 2},
      {title: '请结合临床处理，6-12个月复查', id: 3},
      {title: '请作阴道镜检查及活体组织检查', id: 4},
      {title: '请结合TBS诊断结果', id: 5},
      {title: '请结合TBS诊断结果，定期复查', id: 6},
      {title: '请结合TBS诊断结果，作阴道镜检查及活体组织检查', id: 7},
      {title: '请结合TBS诊断结果，3-6个月复诊', id: 8},
    ]
  },
  {
    idList: ['xbdnabt-rt-7','xbdnabt-rt-8','xbdnabt-rt-9'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '无', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['xbdnabt-rt-11','xbdnabt-rt-12','xbdnabt-rt-13','xbdnabt-rt-14','xbdnabt-rt-15'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '未见', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['xbdnabt-rt-10'],
    lenVal: 160,
    optionList: [
      {title: '正常', id: 1},
      {title: '轻度发炎', id: 2},
      {title: '中度发炎', id: 3},
      {title: '重度发炎', id: 4},
    ]
  },
]; // 细胞DNA倍体检测下拉列表
var myxbhxSelList = [
  {
    idList: ['myxbhx-rt-1'],
    lenVal: 80,
    optionList: [
      {title: '满意', id: 1},
      {title: '不满意', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['myxbhx-rt-2'],
    lenVal: 236,
    optionList: [
      {title: '炎症细胞覆盖', id: 1},
      {title: '鳞状细胞少', id: 2},
      {title: '出血过多', id: 3},
      {title: '抹片过厚或人为假象', id: 4},
      {title: '', id: 5},
    ]
  },
  {
    idList: ['myxbhx-rt-8','myxbhx-rt-9','myxbhx-rt-10'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '无', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['myxbhx-rt-12','myxbhx-rt-13','myxbhx-rt-14','myxbhx-rt-15','myxbhx-rt-16'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '未见', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['myxbhx-rt-11'],
    lenVal: 160,
    optionList: [
      {title: '正常', id: 1},
      {title: '轻度发炎', id: 2},
      {title: '中度发炎', id: 3},
      {title: '重度发炎', id: 4},
    ]
  },
]; // 免疫细胞化学(P16+LCT)下拉列表
var xbxfxOptList = ['无上皮内病变或恶性病变（NILM）','无上皮内病变或恶性病变（NILM）-反应性改变','无上皮内病变或恶性病变（NILM）-萎缩反应性改变','微生物感染-念珠菌','微生物感染-滴虫','微生物感染-放线菌','微生物感染-球杆菌','微生物感染-疱疹病毒','非典型鳞状上皮细胞-意义不明确（ASC-US）','非典型鳞状上皮细胞-不除外高级别鳞状上皮内病变（ASC-H）','低级别鳞状上皮内病变（LSIL）','高级别鳞状上皮内病变（HSIL）','高级别鳞状上皮内病变（HSIL）-具有可疑的侵袭特点','鳞状细胞癌（SCC）','腺细胞（非特异）（AGC-NOS）','腺细胞，倾向于肿瘤性（AGC-FN）','子宫颈管腺细胞（非特异）（AGC-NOS）','子宫颈管腺细胞，倾向于肿瘤性（AGC-FN）','子宫内膜腺细胞（非特异）（AGC-NOS）','子宫内膜腺细胞，倾向于肿瘤性（AGC-FN）','子宫颈管原位癌（AIS）','腺癌','宫颈腺癌','子宫内膜腺癌','查见子宫内膜细胞']; // 细胞学分析下拉列表
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#gjxbx1 .gjxbx-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || []) : []; // 结果集
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon();
    } else {
      pageInit();
    }
  }
}
// 初始化
function pageInit() {
  changePattrenType();
  uploadImg();
  // 多选赋值回显
  xmSelect.get().forEach(function(opt) {
    var vm = $(opt.options.dom);
    var val = vm.next('.hide-val').val();
    if(val) {
      var valArr = val.split('；').map(function(name){
        return {title: name, id: name};
      })
      opt.setValue(valArr)
    }
  })
}

// 切换模板
function changePattrenType() {
  var patternPage = '';
  if(!resultData.length) {
    patternPage = 'yjxbx-edit';
    $('.' + patternPage).show();
    fillSelectOption(patternPage);
    initAllSel(patternPage);
    setDefaultVal(patternPage);
  }else {
    let patternTypeEditMap = {
      '液基细胞学': 'yjxbx-edit',
      '细胞DNA倍体检测': 'xbdnabt-edit',
      '免疫细胞化学(P16+LCT)': 'myxbhx-edit',
    };
    let patternVal = idAndDomMap['gjxbx-rt-1'].value || '液基细胞学';
    patternPage = patternTypeEditMap[patternVal];
    $('.' + patternPage).show();
    $('.' + patternPage).siblings().hide();
    fillSelectOption(patternPage);
    initAllSel(patternPage);
  }
  $('#gjxbx-rt-1').change(function(e) {
    clearPatternVal();
    patternPage = $("#gjxbx-rt-1 option:selected").attr('pattern-page');
    $('.' + patternPage).show();
    $('.' + patternPage).siblings().hide();
    initAllSel(patternPage);
    setDefaultVal(patternPage);
    if(patternPage == 'xbdnabt-edit') {
      uploadImg();
    }
  })
}

// 设置初始化默认值
function setDefaultVal(patternPage) {
  fillSelectOption(patternPage,true);
  if(patternPage === 'yjxbx-edit') {
    $('#yjxbx-rt-1').val('满意');
    $('#yjxbx-rt-3').val('无上皮内病变或恶性病变（NILM）');
    // “有”默认值id列表
    let yIdList = ['yjxbx-rt-4','yjxbx-rt-5','yjxbx-rt-6'];
    for(let id in yIdList) {
      $('#'+yIdList[id]).val('有');
    }
    // “未见”默认值id列表
    let wjIdList = ['yjxbx-rt-8','yjxbx-rt-9','yjxbx-rt-10','yjxbx-rt-11','yjxbx-rt-12'];
    for(let id in wjIdList) {
      $('#'+wjIdList[id]).val('未见');
    }
    $('#yjxbx-rt-7').val('轻度发炎');
    $('#yjxbx-rt-13').val('未见上皮内病变及癌变');
  }
  if(patternPage === 'xbdnabt-edit') {
    $('#xbdnabt-rt-1').val('满意');
    $('#xbdnabt-rt-4').val('未见DNA倍体异常细胞');
    $('#xbdnabt-rt-5').val('定期复查');
    $('#yjxbx-rt-6').val('无上皮内病变或恶性病变（NILM）');
    // “有”默认值id列表
    let yIdList = ['xbdnabt-rt-7','xbdnabt-rt-8','xbdnabt-rt-9'];
    for(let id in yIdList) {
      $('#'+yIdList[id]).val('有');
    }
    // “未见”默认值id列表
    let wjIdList = ['xbdnabt-rt-11','xbdnabt-rt-12','xbdnabt-rt-13','xbdnabt-rt-14','xbdnabt-rt-15'];
    for(let id in wjIdList) {
      $('#'+wjIdList[id]).val('未见');
    }
    $('#xbdnabt-rt-10').val('轻度发炎');
  }
  if(patternPage === 'myxbhx-edit') {
    $('#myxbhx-rt-1').val('满意');
    $('#myxbhx-rt-3').prop('checked',true);
    $('#myxbhx-rt-7').val('无上皮内病变或恶性病变（NILM）');
    // “有”默认值id列表
    let yIdList = ['myxbhx-rt-8','myxbhx-rt-9','myxbhx-rt-10'];
    for(let id in yIdList) {
      $('#'+yIdList[id]).val('有');
    }
    // “未见”默认值id列表
    let wjIdList = ['myxbhx-rt-12','myxbhx-rt-13','myxbhx-rt-14','myxbhx-rt-15','myxbhx-rt-16'];
    for(let id in wjIdList) {
      $('#'+wjIdList[id]).val('未见');
    }
    $('#myxbhx-rt-11').val('轻度发炎');
    $('#myxbhx-rt-17').val('未见上皮内病变及癌变\n\nP16- 未见P16蛋白表达');
  }
}

// 清空模板节点值
function clearPatternVal() {
  var domList = $('#gjxbx1 .pattern-page').find('.rt-sr-w');
  domList.each(function(index,dom) {
    if($(dom).attr('type') === 'radio' || $(dom).attr('type') === 'checkbox') {
      $(dom).prop('checked',false);
    }else if($(dom).attr('type') === 'file'){
      $('.preview-img').html('');
    }else {
      $(dom).val('');
    }
  })
}

// 将下拉数据补全和初始化多选，下拉编辑
function fillSelectOption(key,isNeedDefaultVal) {
let dropdown = layui.dropdown;
  var curSelect = curElem.find('[opt-name="'+key+'-dropdown"]');
  var multi = $(curSelect).attr('multi');
  var data = xbxfxOptList.map(function(item) {
    return {
      title: item,
      id: item || Date.now()
    }
  })
  if(multi === '1') {  //多选
    var optXmSel = xmSelect.render({
      el: '[opt-name="'+key+'-dropdown"]', 
      tips: '',
      prop: {
        name: 'title',
        value: 'id',
      },
      model: {
        label: {
          type: 'text',
          //使用字符串拼接的方式
          text: {
            //左边拼接的字符
            left: '',
            //右边拼接的字符
            right: '',
            //中间的分隔符
            separator: '；',
          },
        }
      },
      data: data,
      hide: function() {
        xmSelect.get().forEach(function(opt) {
          var vm = $(opt.options.dom);
          var text = vm.find('.label-content').text() || "";
          vm.next('.hide-val').val(text);
        })
      }
    })
    if(!resultData.length || isNeedDefaultVal) {
      optXmSel.setValue([{ title:'无上皮内病变或恶性病变（NILM）', id: '无上皮内病变或恶性病变（NILM）' }]);
    }
  } else {
    dropdown.render({
      elem: '[opt-name="'+key+'-dropdown"]', 
      data: data, 
      className: 'laySelLab', 
      click: function (obj) {
        this.elem.val(obj.title);
      },
    });
  }
}

// 初始化所有下拉框
function initAllSel(patternType) {
  var patternTypeMap = {
    'yjxbx-edit': yjxbxSelList,
    'xbdnabt-edit': xbdnabtSelList,
    'myxbhx-edit': myxbhxSelList,
  }
  var selList = patternTypeMap[patternType];
  selList.map(item => {
    let { idList = [], lenVal = 0, optionList = [] } = item;
    idList.map(id => {
      initInpAndSel(id,optionList,lenVal);
    })
  })
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: lenVal !== 0 ? `width: ${lenVal}px;` : 'width:30%'
  })
}

// 上传图像
function uploadImg() {
  var rtEl=$('.fileids');
  if (!publicInfo.examNo && !publicInfo.busId) {
    return;
  }
  var idAnVal = idAndDomMap['xbdnabt-rt-3'] ? idAndDomMap['xbdnabt-rt-3'].value : '';
  if(idAnVal){
    rtEl.val(idAnVal);
    showImgList(rtEl);
  }

  $('#imageInput').on('input',function(e){
    if(this.files.length) {
      for(let fileIndex in this.files) {
        var file = this.files[fileIndex];
        if(!file || !file.size){
          e.target.value = '';
          return;
        }
        imageUploader(publicInfo.examNo,file, rtEl);
        showImgList(rtEl);
      }
    }
  })
}

// 获取图片并回显
function showImgList(rtEl) {
  getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
    if(uris&&uris.length){
      let imgHtml = '';
      for (let i in uris) {
        let url = uris[i];
        // console.log('回显url--->',url);
        if (url) {
          imgHtml += `
          <div class="preview">
            <img classs="divisionImg" id="divisionImg-${i}" src="${url}" alt=""  onclick="viewImg(this,${i})">
            <img class="divisionPreview" id="divisionPreview-${i}" style="display: none;width: 590px;height: 440px;margin-left: 5px;margin-top: 5px;" src="${url}" alt="">
            <div id="divisionDel-${i}" class="delImg" onclick="deleteImg(this,${i})">x</div>
          </div>
          `;
        }
      }
      $('.preview-img').html(imgHtml);
    }else {
      $('.preview-img').html('');
    }
  })
}

// 删除图片
function deleteImg(ele,index) {
  var rtEl =$('.fileids');
  var pidList = $('.fileids').val().split(',');
  var pid = pidList[index];
  // console.log('pid',pid)
  deleteUploadedImage(publicInfo.examNo,pid, rtEl);
  showImgList(rtEl);
}

// 预览图片
function viewImg(ele,index) {
  layer.open({
    title: '预览',
    type: 1,
    area: ['600px','500px'],
    content: $('#divisionPreview-'+index) //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
  });
}

// 获取诊断
function getImpression() {
  var patternPage = $("#gjxbx-rt-1 option:selected").attr('pattern-page');
  var strArr = [], impression = '';
  if(patternPage === 'yjxbx-edit') {
    let xbxfxVal = $('#yjxbx-rt-3').val() || '';
    xbxfxVal && strArr.push('细胞学分析：' + xbxfxVal);
    let bgyjVal = $('#yjxbx-rt-13').val() || '';
    bgyjVal && strArr.push('报告意见和建议：' + bgyjVal);
  }
  if(patternPage === 'xbdnabt-edit') {
    let dnayjVal = $('#xbdnabt-rt-4').val() || '';
    dnayjVal && strArr.push('DNA意见：' + dnayjVal);
    let dnajyVal = $('#xbdnabt-rt-5').val() || '';
    dnajyVal && strArr.push('DNA建议：' + dnajyVal);
    let xbxfxVal = $('#xbdnabt-rt-6').val() || '';
    xbxfxVal && strArr.push('细胞学分析：' + xbxfxVal);
    let bgyjVal = $('#xbdnabt-rt-16').val() || '';
    bgyjVal && strArr.push('报告意见和建议：' + bgyjVal);
  }
  if(patternPage === 'myxbhx-edit') {
    let myxbhxrsVal = getVal('[name="yyx"]:checked');
    let yxxbStr = '';
    if(myxbhxrsVal === '阳性') {
      let yxxbVal = getVal('[name="yx-xb"]:checked');
      yxxbStr = yxxbVal ? '（' +yxxbVal+ '）' : '';
    }
    myxbhxrsVal && strArr.push('免疫细胞化学染色P16：' + myxbhxrsVal + yxxbStr);
    let xbxfxVal = $('#myxbhx-rt-7').val() || '';
    xbxfxVal && strArr.push('细胞学分析：' + xbxfxVal);
    let bgyjVal = $('#myxbhx-rt-17').val() || '';
    bgyjVal && strArr.push('报告意见和建议：' + bgyjVal);
  }
  impression = strArr.length ? strArr.join('，') : '';
  return impression;
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  var rtEl=$('.fileids');
  let patternTypeViewMap = {
    '液基细胞学': 'yjxbx-view',
    '细胞DNA倍体检测': 'xbdnabt-view',
    '免疫细胞化学(P16+LCT)': 'myxbhx-view',
  };
  var patternTypeVal = idAndDomMap['gjxbx-rt-1'].value || '液基细胞学';
  var patternPage = patternTypeViewMap[patternTypeVal];
  $('.'+patternPage).show();
  curElem.find(`.${patternPage} [data-key]`).each(function () {
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      var value = publicInfo[key] || idAnVal;
      // 液基细胞学
      if(patternPage === 'yjxbx-view') {
        if(key === 'bbmyd') {
          let bbxbVal = idAndDomMap['yjxbx-rt-2'].value || '';
          value = idAndDomMap['yjxbx-rt-1'].value + (bbxbVal ? '，' + bbxbVal : '');
        }
      }
      // 细胞DNA倍体检测页面
      if(patternPage === 'xbdnabt-view') {
        if(key ==='xbdnabt-rt-3') {
          rtEl.val(value);
          getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
            // console.log('预览--->',uris);
            if(uris&&uris.length){
              let imgHtml = '';
              for (let i in uris) {
                let url = uris[i];
                // console.log('预览url-->',url);
                if (url) {
                  imgHtml += `
                  <div class="preview-item-img">
                    <img id="divisionPreviewImg-${i}" src="${url}" alt="">
                  </div>
                  `;
                }
              }
              $('.preview-img-ls').css('display','flex');
              $('.preview-img-ls').html(imgHtml);
            }
          })
        }
        if(key === 'bbmyd') {
          let bbxbVal = idAndDomMap['xbdnabt-rt-2'].value || '';
          value = idAndDomMap['xbdnabt-rt-1'].value + (bbxbVal ? '，' + bbxbVal : '');
        }
      }
      // 免疫细胞化学(P16+LCT)
      if(patternPage === 'myxbhx-view') {
        if(key === 'bbmyd') {
          let bbxbVal = idAndDomMap['myxbhx-rt-2'].value || '';
          value = idAndDomMap['myxbhx-rt-1'].value + (bbxbVal ? '，' + bbxbVal : '');
        }
        if(key === 'myxbhxrs') {
          let yinVal = idAndDomMap['myxbhx-rt-3'].value || '';
          let yangVal = idAndDomMap['myxbhx-rt-4'].value || '';
          if(yinVal) {
            value = yinVal;
          }else {
            let yxxbArr = [];
            idAndDomMap['myxbhx-rt-5'].value && yxxbArr.push(idAndDomMap['myxbhx-rt-5'].value);
            idAndDomMap['myxbhx-rt-6'].value && yxxbArr.push(idAndDomMap['myxbhx-rt-6'].value);
            value = yangVal + (yxxbArr.length ? '（' + yxxbArr.join('，') + '）' : '');
          }
        }
      }

      if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
        value = value + ' ' + publicInfo['affirmTime'];
      }
      if(value) {
        $(this).html(value);
      }
    }
  })
  curElem.find(`.${patternPage} [data-img]`).each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if (rptImageList && rptImageList.length) {
    rptImageList = rptImageList.slice(0,2);
    var imgHtml = '';
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="item-img">
          <img src="${image.src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find(`.${patternPage} .rpt-img-ls`).html(imgHtml);
      curElem.find(`.${patternPage} .rpt-img-ls`).css('display', 'block');
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}