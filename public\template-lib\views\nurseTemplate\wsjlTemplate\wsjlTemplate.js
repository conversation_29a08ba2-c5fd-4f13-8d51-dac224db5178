$(function() {
  window.initHtmlScript = initHtmlScript;
})
var liveConClone = null;
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否已填写
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false;
    
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initBzCloneEle()
      displayLiveContent()
      togglePageType('preview')
    } else {
      togglePageType('edit')
      toNurseList()
      pageInit()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function togglePageType(type) {
  $(`[data-type]`).hide()
  $(`[data-type=${type}]`).show()
  if(type==='edit'){
    $('#removeNode').remove()
  }
  if (type === 'preview') {
    $('[data-key]').each(function () {
      let key = $(this).attr('data-key')
      if (idAndDomMap) {
        let result = []
        Object.keys(idAndDomMap).forEach(idKey => {
          if (idKey.startsWith(key)) {
            let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value : '';
            if (value) {
              result.push(value)
            }
          }
        })
        if (key === 'hlhdyy-rt-0006-') {
          $(this).html(result.join(':'))
        } else if (key === 'hlhdyy-rt-0008-' || key === 'hlhdyy-rt-0009-') {
          $(this).html(result.join('、'))
        } else {
          $(this).html(result.join(','))
        }
        this.style.color = '#000'
      }
    })
  }
}
function pageInit(){
  $('input').attr('autocomplete','off')
  initBzCloneEle()
  window.getExamImageAgentList ? getExamAgentList() : '';
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    curElem.find('.def-ck').click();
    addLiveHandler();
    useServerTime('wsjl-rt-39')
  } else {
    displayLiveContent()
  }
  layInit()
}
function getExamAgentList(){
  console.log('publicInfo',publicInfo);
  let agentRes = window.getExamImageAgentList({examNo: publicInfo.examNo});
  if(agentRes&&agentRes.length){
    $('#wsjl-rt-1').val(agentRes[0].agentName);
  }
}
function layInit(){
  layui.use('laydate', function(){
    var laydate = layui.laydate;
    //执行一个laydate实例
    laydate.render({
      elem: '#wsjl-rt-3', //指定元素
      type:'date'
    });
    laydate.render({
      elem:'#wsjl-rt-11',
      type:'datetime'
    })
    // laydate.render({
    //   elem:'#wsjl-rt-33',
    //   type:'datetime'
    // })
    laydate.render({
      elem:'#wsjl-rt-15',
      type:'datetime'
    })
    laydate.render({
      elem:'#wsjl-rt-21',
      type:'datetime'
    })
  });
}
// 添加处理医嘱,oldPaneId已保存过的
function addLiveHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.table-content .live-con .bz-tr').length;
  var activeTab = 'wsjl-rtlist-' + paneId; // 表格每列的id
  var newPaneBlock = appendLiveHtml(paneId, bzLen,oldPaneId);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '过程追踪',
      name: '过程追踪',
      pid: 'wsjl-rtlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('过程追踪' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('.table-content .live-con .bz-tr').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc'); // 获取rt-sc的节点
      var scArr = sc.split(';');//获取属性值
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      }; // 值放在此
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    newPaneBlock.find('.def-ck').click();
  }
}
function appendLiveHtml(paneId, bzLen,oldPaneId) {
  var reg = new RegExp('000', 'ig');
  var content = liveConClone.replace(reg, paneId);  
  $('.table-content .live-con').append(content);
  $('#wsjl-rtlist-'+paneId+'-1').html((bzLen + 1));
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#wsjl-rtlist-'+paneId+'-5',//指定元素
      type: 'datetime',
      value: oldPaneId ? '' : new Date()
    });
  })
  var newPaneBlock =  $('.table-content .live-con .bz-tr[tab-target="wsjl-rtlist-'+paneId+'"]');
  return newPaneBlock;
}
// 复制暂存处理医嘱的具体模板示例内容，为后续添加处理医嘱做准备
function initBzCloneEle() {
  var liveCon = curElem.find('.table-content .live-con .bz-tr').clone(true);
  liveConClone = '<tr class="bz-tr rt-sr-w"  tab-target="wsjl-rtlist-000" id="wsjl-rtlist-000" pid="wsjl-rtlist-1" rt-sc="pageId:wsjl1;name:过程%3A外渗记录;wt:;desc:过程%3A外渗记录;vt:;pvf:;">'+liveCon.html()+'</tr>';
  curElem.find('.table-content .live-con').html('');
  // 将初始化的表格模板暂存，清空掉原来的表格内容
}
function delTrFuc(vm, paneId){
  var allSiblings = $(vm).parent().parent().siblings();
  allSiblings.each(function(index) {
      // console.log($(this).children().first(),index); // 输出：first 和 third
      $(this).children().first().html(index + 1)
  });
  $(vm).parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}
function displayLiveContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'wsjl-rtlist-1')
    // console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('wsjl-rtlist-', '');
        addLiveHandler(paneId)
      })
    }
  } else {
    curElem.find('.bz-list .bz-wrap').hide();
  }
}
function toNurseList(){
  let list = window.getNurseList({deptCode: publicInfo.userInfo&&publicInfo.userInfo.param&&publicInfo.userInfo.param.deptCode || ''});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
  initInpAndSel('wsjl-rt-38', userList,optHandler  );
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#wsjl-rt-38').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#wsjl-rt-38').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}

function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }
 function initInpAndSel(idList, optionList, cb,id) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList,dropdown)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
     typeof cb === 'function' && cb(obj)
      // console.log('title',obj.title,id)
      if(id){
        $(`#${id}`).val('');
        if(obj.title === '口服'){
          //先拿到dropdown.render对象
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentOralList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }else if(obj.title === '滴鼻'){
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentSnortList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }else if(obj.title === '静脉'){
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentMainlineList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }
      }
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
  // if(id){
    // 使用dropdown.setData方法重新赋值
    
  // }
}