$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var cursorInfo = {};  //光标位置信息
var codeMap = {
  '312': '肉眼所见',
  '201': '镜下所见',
  '211': '辅助检查',
  '202': '病理诊断',
}; // 添加双击事件节点
var viewConList = []; // 补充报告
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#fzblBc1 .fzblBc-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      resetLogoStyle();
      getCloudFilmQrCodeUrl(publicInfo.examNo, $('.fzblBc-view .view-head'));
      replenishReviewContent();
      initViewCon();
      replaceViewPatternByValue($('[data-key="inpatientNo"]').siblings('.gray-txt'), $('[data-key="inpatientNo"]'), publicInfo);
    } else {
      pageInit();
    }
  }
}
function pageInit() {
  showBcRptBtn();
  changeTextSize();
  getRelativeCon();
  showRptWordDialog();
  for(let code in codeMap) {
    $('[code="'+code+'"]').on('blur',function() {
      blurTextAreaHandler(this, $(this).attr('data-key'));
    })
  }
}

// 字号切换
function changeTextSize() {
  var localSize = getOrSetSizeType('get');
  setSizeDom(localSize);
  $('.text-size').on('click', 'img', function () {
    if ($(this).hasClass('on')) {
      return;
    }
    var size = $(this).closest('.img-wrap').attr('data-size');
    setSizeDom(size);
    getOrSetSizeType('set', size);
  })
}

// 设置字体大小元素展示
function setSizeDom(size) {
  $('.editor-wrap .on').hide();
  $('.editor-wrap .off').show();
  $('.editor-wrap .img-wrap[data-size="' + size + '"] .on').show();
  $('.editor-wrap .img-wrap[data-size="' + size + '"] .off').hide();
  $('.editor-area').removeClass('default large larger');
  $('.editor-area').addClass(size);
}

// 获取、设置缓存字体大小数据
function getOrSetSizeType(type, size) {
  var userId = publicInfo && publicInfo.userInfo ? publicInfo.userInfo.staffNo : publicInfo.optId;
  var sizeTypeList = getLocalStorage('rt_size_type') || {};
  if(type === 'get') {
    var sizeType = sizeTypeList[userId] || 'default';
    return sizeType;
  }
  if(type === 'set') {
    sizeTypeList[userId] = size;
    setLocalStorage('rt_size_type', sizeTypeList);
  }
}

// 提取相关内容
function getRelativeCon() {
  curElem.find('[data-get]').click(function() {
    var [type, valKey] = $(this).attr('data-get').split(',');
    getConFromApi(type, valKey);
  })
}

// 显示补充报告按钮
function showBcRptBtn() {
  let arr = window.getExamRptList(publicInfo.examNo, '3') || [];
  arr = arr.filter(item => item.patternId === '2020');
  if(!arr.length) {
    $('#fzblBc1 .bcRptImp').hide();
  }else {
    $('#fzblBc1 .label-wrap').css('width', '95px');
    let html = '';
    arr.forEach((item, idx) => {
      html += `<div class="blue-lb bcRptImp"  data-get="bcImpress-${item.reportNo},impression">提补充诊断${idx + 1}</div>`;
    })
    $('#fzblBc1 .bcRptImp').append(html);
  }
}

// 获取相关接口内容
function getConFromApi(type, valKey) {
  if(!publicInfo.examNo && !publicInfo.busId) {
    return;
  }
  var apiMap = {
    'sample': api.gmSampleForRpt,   //提标本
    'doctorAdvice': api.getGmOrderForRpt,  //提医嘱
    'sampleSeen': publicInfo.sampleSeen || '',  //提肉眼所见
    'impress': api.getFrozenForRpt,  //提冰冻诊断
    'cgDescription': api.getRptDescriptionContent, // 查询镜下所见内容
    'cgExamParam': api.getRptExamParamContent, // 查询辅助检查内容
  }
  var url = apiMap[type];
  var originVal = $('.fzblBc-edit [data-key="'+valKey+'"]').val();
  var wrapText = '';
  if(originVal && originVal[originVal.length-1] !== '\n') {
    wrapText = '\n';
  }
  if(type === 'sampleSeen') {
    if(publicInfo.sampleSeen) {
      curElem.find('.fzblBc-edit [data-key="'+valKey+'"]').val(originVal + wrapText + publicInfo.sampleSeen);
    }
    return;
  }
  var params = {
    examNo: publicInfo.examNo || publicInfo.busId
  }
  if(type === 'impress' || type === 'cgDescription' || type === 'cgExamParam') {
    params.rptType = '0';
  }
  if(type.includes('bcImpress')) {
    url = apiMap['impress'];
    let reportNo = type.split('-')[1];
    params.rptType = '3';
    params.reportNo = reportNo;
  }
  fetchAjax({
    url: url,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        if(res.result) {
          // curElem.find('.fzblBc-edit [data-key="'+valKey+'"]').val(originVal + wrapText + res.result);
          let resText = res.result;
          if(cursorInfo && cursorInfo.dataKey === valKey) {
            insertTextAtCaret(resText, cursorInfo)
          } else {
            cursorInfo = {};
            let text = originVal + wrapText + resText;
            curElem.find('.fzblBc-edit [data-key="' + valKey + '"]').val(text);
            curElem.find('.fzblBc-edit [data-key="' + valKey + '"]').focus();
          }
        }
      }
    },
  })
}
// 插入节点的光标位置
function insertTextAtCaret(value, srCursorInfo) {
  let {textarea, cursorPosition, selectionEnd} = srCursorInfo;
  let textBefore = textarea.value.substring(0, cursorPosition);
  let textAfter = textarea.value.substring(selectionEnd, textarea.value.length);
  textarea.value = textBefore + value + textAfter; // 插入文本
  // 重新定位光标位置
  textarea.selectionStart = cursorPosition + value.length;
  textarea.selectionEnd = cursorPosition + value.length;
  textarea.focus();
}

// 失去焦点获取光标位置
function blurTextAreaHandler(vm, dataKey) {
  let selection = getSelection();
  cursorInfo = {
    selection,
    cursorPosition: vm.selectionStart,  //记录最后光标对象
    selectionEnd: vm.selectionEnd,  //记录最后光标对象
    textarea: vm,
    dataKey
  }
}

// 获取补充报告预览所需的内容
// rptType: 0 常规 1冰冻 2 免疫 3补充
function replenishReviewContent() {
  let {examNo='',reportNo='',docNo=''} = publicInfo;
  if (!examNo || (!reportNo && !docNo)) {
    return null
  }
  fetchAjax({
    url: api.replenishReviewContent,
    data: JSON.stringify({
      examNo: examNo,
      reportNo: reportNo || docNo,
      withExamStaff: true,
    }),
    async: false,
    successFn: function (res) {
      if (res.status === '0') {
        viewConList = res.result || [];
      }
    }
  })
  return viewConList;
}
// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.fzblBc-view [data-key]').each(function(){
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      var value = publicInfo[key] || idAnVal;
      if(value) {
        if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
          value = value + ' ' + publicInfo['affirmTime'];
        }
        $(this).html(value);
        // $(this).closest('.desc-con').show();
        break;
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  curElem.find('.fzblBc-view [data-img]').each(function(){
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if(value) {
      var src = getSignImgHandler({staffNo:value});
      if(src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
  // 显示病理号
  let blhVal = publicInfo['patLocalId'] || '';
  $('.fzblBc-view .blh-tit .black-txt').html(blhVal);
  // 显示常规、冰冻、补充报告内容
  var rptNameMap = {
    "0": "常规诊断",
    "1": "冰冻诊断",
    "2": "免疫诊断",
    "3": "补充诊断",
  }
  // 报告内容及展示的顺序
  var rptConListMap = {
    '0': [
      {key: 'sampleSeen', name: '肉眼所见'},
      {key: 'examParam', name: '辅助检查'},
      {key: 'description', name: '镜下所见'},
      {key: 'impression', name: '常规诊断'},
    ],
    '1': [
      {key: 'impression', name: '冰冻诊断'},
    ],
    '2': [
      {key: 'sampleSeen', name: '肉眼所见'},
      {key: 'examParam', name: '辅助检查'},
      {key: 'description', name: '镜下所见'},
      {key: 'impression', name: '免疫诊断'},
    ],
    '3': [
      {key: 'description', name: '镜下所见'},
      {key: 'examParam', name: '辅助检查'},
      {key: 'sampleSeen', name: '肉眼所见'},
      {key: 'impression', name: '补充诊断'},
    ],
  }
  // 通过new-report-wrap兼容旧模板，req18545，只显示当前一份补充报告
  if($('.new-report-wrap').length){
    viewConList = viewConList.filter(item => item.rptType !== '3' || item.reportNo === Number(publicInfo.reportNo));
  }
  var bcSort = 0;
  var bcRptData = viewConList.filter(item => item.rptType === '3' || !item.rptType);  //补充诊断的数据
  var examSubClass = publicInfo.examSubClass || '';
  viewConList.forEach((item, index) => {
    var rptType = item.rptType || '3';  //没有报告类型就当做3处理
    if(rptConListMap[rptType]) {
      var cloneDesCon = $('.fzblBc-view .desc-clone').clone();
      cloneDesCon.removeClass('desc-clone');
      var conLen = 0;
      var contentList = rptConListMap[rptType];
      var conHtml = '';
      var bcTitleFlag = false;  //补充诊断的标题
      contentList.forEach(conItem => {
        // 子类为尸检，诊断改成“病理解剖诊断”，镜下所见不显示
        if(examSubClass === '尸检') {
          if(conItem['key'] === 'impression') {
            conItem['name'] = '病理解剖诊断';
          }
          if(conItem['key'] === 'description') {
            return;
          }
        }
        var itemVal = item[conItem['key']];
        if(conItem['key'] === 'sampleSeen') {
          itemVal = replaceSeenConByJh(itemVal);
        }
        if(itemVal) {
          conLen++;
          conHtml += '<div class="desc-item '+(rptType==='3'?'bc-item':'')+'">';
          if(!bcTitleFlag || rptType !== '3') {
            if(rptType === '3') {
              bcSort++;
            }
            var title = rptType === '3' ? `补充报告${bcRptData.length > 1 ? bcSort : ''}` : conItem['name'];
            conHtml += '<div class="black-txt bold rpt-title" rtp-type="'+rptType+'">'+title+'：</div>';
          }
          bcTitleFlag = true;
          if(rptType === '3') {
            conHtml += '<div class="gray-txt bold sub-title">'+(conItem['name'])+'：</div>';
          }
          conHtml += '<div class="gray-txt">'+itemVal+'</div>';
          conHtml += '</div>';
        }
      })
      if(conLen > 0) {
        cloneDesCon.find('.con-reporter [con-data]').each(function(i, dom) {
          var valKey = $(dom).attr('con-data');
          $(dom).html(item[valKey] || '');
        })
        cloneDesCon.find('.con-wrap').html(conHtml);
        cloneDesCon.show();
        $('.fzblBc-view .report-wrap').before(cloneDesCon);
        // 只有一个补充报告且只填了补充诊断去掉大标题“补充报告”
        if(bcRptData.length === 1 && conLen === 1 && item.impression) {
          $('.fzblBc-view .rpt-title[rtp-type="3"]').remove();
        }
      }
      if($('.new-report-wrap').length && rptType === '3') {
        cloneDesCon.find('.con-reporter').remove();
      }
    }
  })
  $('.fzblBc-view .desc-clone').remove();
}

// 报告常用词
function showRptWordDialog() {
  $('#fzblBc1 .bcRptCommondWord').click(function() {
    window.top.showComWordDiaFun(true);
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = curElem.find('.fzblBc-edit [data-key="description"]').val() || '';
  rtStructure.impression = curElem.find('.fzblBc-edit [data-key="impression"]').val() || '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: curElem.find('[opt-name="priReporter"]').val() || '',  //初步报告医生
    priReporterNo: curElem.find('.priReporterNo').val() || '',  //初步报告医生流水号
    affirmReporter: curElem.find('[opt-name="affirmReporter"]').val() || '',  //审核医生
    affirmReporterNo: curElem.find('.affirmReporterNo').val() || '',  //审核医生
    affirmDate: '',  //审核日期
    reDiagReporter: curElem.find('[id="fzblBc-rt-14"]').val() || '',  //复诊报告医生
    reDiagReporterNo: curElem.find('.reDiagReporterNo').val() || '',  //复诊报告医生流水号，多个逗号隔开
    examParam: curElem.find('.fzblBc-edit [data-key="examParam"]').val() || '',  //检查过程中记录的有关内容，如测量值、辅助检查
    // rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}