<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/srTemplate/hbethpvTemplate/hbethpvTemplate.css">
<script src="/template-lib/plugins/jquery.min.js"></script>
<!-- <link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css"> -->
<!-- <script src="/template-lib/plugins/layui/layui.js"></script> -->
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/srTemplate/hbethpvTemplate/hbethpvTemplate.js"></script>

<ul class="t-pg" style="height: 100%;overflow: auto;">
  <li class="page" id="hbethpv1">
    <div class="edit-wrap">
      <div class="mb-8 item-flex">
        <div class="flex_1 view-flex">
          <span class="text_t_l">标本类型：</span>
          <input class="rt-sr-w" style="width: 240px;" id="hbethpv-rt-1" value="液基细胞" rt-sc="pageId:hbethpv1;name:标本类型;wt:1;desc:标本类型;vt:;pvf:;">
        </div>
        <div class="flex_1 view-flex" style="justify-content:flex-end">
          <span class="text_t_l">标本状态：</span>
          <input class="rt-sr-w" style="width: 240px;" id="hbethpv-rt-2" value="可用" rt-sc="pageId:hbethpv1;name:标本状态;wt:1;desc:标本状态;vt:;pvf:;">
        </div>
      </div>
      <div class="mb-8 view-flex">
        <span class="text_t_l">检测方法：</span>
        <input class="rt-sr-w" style="width: calc(100% - 80px);" id="hbethpv-rt-3" value="PCR-反向点杂交法" rt-sc="pageId:hbethpv1;name:检测方法;wt:1;desc:检测方法;vt:;pvf:;">
      </div>
      <div class="mb-8" style="overflow: hidden;">
        <span class="text_t_l fl" style="width: 80px;text-align: left;">检测内容：</span>
        <textarea class="rt-sr-w layui-input textarea fl" style="width:calc(100% - 80px);height: 104px;padding: 5px 5px 0;line-height: 1.4;" id="hbethpv-rt-4" rt-sc="pageId:hbethpv1;name:检测内容;wt:1;desc:检测内容;vt:;pvf:;">低危型：HPV6、11、42、43、81
高危型：HPV16、18、31、33、35、39、45、51、52、53、56、58、59、66、68、73、82、83</textarea>
      </div>
      <div class="mb-8" style="overflow: hidden;">
        <span class="text_t_l fl" style="width: 80px;text-align: left;">检测结果：</span>
        <textarea class="rt-sr-w layui-input textarea fl" style="width:calc(100% - 80px);height: 104px;padding: 5px 5px 0;line-height: 1.4;" id="hbethpv-rt-5" rt-sc="pageId:hbethpv1;name:检测结果;wt:1;desc:检测结果;vt:;pvf:;">低危型：阴性
高危型：HPV33，为阳性</textarea>
      </div>
      <div style="padding: 12px 0;">
        <div class="item-flex labelBox" style="border-top: solid 1px #C0C4CC;">
          <label class="checkbox-label" for="hbethpv-rt-6">
            <span class="text">6</span>
            <input type="checkbox" name="6" class="rt-sr-w" id="hbethpv-rt-6" rt-sc="pageId:hbethpv1;name:6;wt:4;desc:6;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-7">
            <span class="text">11</span>
            <input type="checkbox" name="11" class="rt-sr-w" id="hbethpv-rt-7" rt-sc="pageId:hbethpv1;name:11;wt:4;desc:11;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-8">
            <span class="text">16</span>
            <input type="checkbox" name="16" class="rt-sr-w" id="hbethpv-rt-8" rt-sc="pageId:hbethpv1;name:16;wt:4;desc:16;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-9">
            <span class="text">18</span>
            <input type="checkbox" name="18" class="rt-sr-w" id="hbethpv-rt-9" rt-sc="pageId:hbethpv1;name:18;wt:4;desc:18;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-10">
            <span class="text">31</span>
            <input type="checkbox" name="31" class="rt-sr-w" id="hbethpv-rt-10" rt-sc="pageId:hbethpv1;name:31;wt:4;desc:31;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-11">
            <span class="text">33</span>
            <input type="checkbox" name="33" class="rt-sr-w" id="hbethpv-rt-11" rt-sc="pageId:hbethpv1;name:33;wt:4;desc:33;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-12">
            <span class="text">35</span>
            <input type="checkbox" name="35" class="rt-sr-w" id="hbethpv-rt-12" rt-sc="pageId:hbethpv1;name:35;wt:4;desc:35;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-13">
            <span class="text">39</span>
            <input type="checkbox" name="39" class="rt-sr-w" id="hbethpv-rt-13" rt-sc="pageId:hbethpv1;name:39;wt:4;desc:39;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label">
            <span class="text">编号</span>
          </label>
        </div>
        <div class="item-flex labelBox">
          <label class="checkbox-label" for="hbethpv-rt-14">
            <span class="text">42</span>
            <input type="checkbox" name="42" class="rt-sr-w" id="hbethpv-rt-14" rt-sc="pageId:hbethpv1;name:42;wt:4;desc:42;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-15">
            <span class="text">43</span>
            <input type="checkbox" name="43" class="rt-sr-w" id="hbethpv-rt-15" rt-sc="pageId:hbethpv1;name:43;wt:4;desc:43;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-16">
            <span class="text">45</span>
            <input type="checkbox" name="45" class="rt-sr-w" id="hbethpv-rt-16" rt-sc="pageId:hbethpv1;name:45;wt:4;desc:45;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-17">
            <span class="text">51</span>
            <input type="checkbox" name="51" class="rt-sr-w" id="hbethpv-rt-17" rt-sc="pageId:hbethpv1;name:51;wt:4;desc:51;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-18">
            <span class="text">52</span>
            <input type="checkbox" name="52" class="rt-sr-w" id="hbethpv-rt-18" rt-sc="pageId:hbethpv1;name:52;wt:4;desc:52;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-19">
            <span class="text">53</span>
            <input type="checkbox" name="53" class="rt-sr-w" id="hbethpv-rt-19" rt-sc="pageId:hbethpv1;name:53;wt:4;desc:53;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-20">
            <span class="text">56</span>
            <input type="checkbox" name="56" class="rt-sr-w" id="hbethpv-rt-20" rt-sc="pageId:hbethpv1;name:56;wt:4;desc:56;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-21">
            <span class="text">58</span>
            <input type="checkbox" name="58" class="rt-sr-w" id="hbethpv-rt-21" rt-sc="pageId:hbethpv1;name:58;wt:4;desc:58;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label">
            <span class="text lineBreak rt-sr-w" style="font-size: 16px;" id="hbethpv-rt-22" rt-sc="pageId:hbethpv1;name:病理号;wt:;desc:病理号;vt:;pvf:;code:145;"></span>
          </label>
        </div>
        <div class="item-flex labelBox">
          <label class="checkbox-label" for="hbethpv-rt-23">
            <span class="text">59</span>
            <input type="checkbox" name="59" class="rt-sr-w" id="hbethpv-rt-23" rt-sc="pageId:hbethpv1;name:59;wt:4;desc:59;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-24">
            <span class="text">66</span>
            <input type="checkbox" name="66" class="rt-sr-w" id="hbethpv-rt-24" rt-sc="pageId:hbethpv1;name:66;wt:4;desc:66;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-25">
            <span class="text">68</span>
            <input type="checkbox" name="68" class="rt-sr-w" id="hbethpv-rt-25" rt-sc="pageId:hbethpv1;name:68;wt:4;desc:68;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-26">
            <span class="text">73</span>
            <input type="checkbox" name="73" class="rt-sr-w" id="hbethpv-rt-26" rt-sc="pageId:hbethpv1;name:73;wt:4;desc:73;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-27">
            <span class="text">81</span>
            <input type="checkbox" name="81" class="rt-sr-w" id="hbethpv-rt-27" rt-sc="pageId:hbethpv1;name:81;wt:4;desc:81;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-28">
            <span class="text">82</span>
            <input type="checkbox" name="82" class="rt-sr-w" id="hbethpv-rt-28" rt-sc="pageId:hbethpv1;name:82;wt:4;desc:82;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-29">
            <span class="text">83</span>
            <input type="checkbox" name="83" class="rt-sr-w" id="hbethpv-rt-29" rt-sc="pageId:hbethpv1;name:83;wt:4;desc:83;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label" for="hbethpv-rt-30">
            <span class="text">IC</span>
            <input type="checkbox" name="IC" class="rt-sr-w" id="hbethpv-rt-30" rt-sc="pageId:hbethpv1;name:IC;wt:4;desc:IC;vt:;pvf:;">
            <i class="bui-radios"></i>
          </label>
          <label class="checkbox-label">
            <span class="text">HPV</span>
          </label>
        </div>
      </div>
      <div class="mb-8" style="overflow: hidden;">
        <span class="text_t_l" style="text-align: left;margin-bottom: 10px;">结果提示及分析：</span>
        <textarea class="rt-sr-w layui-input textarea" style="width:100%;height: 104px;padding: 5px 5px 0;line-height: 1.4;" id="hbethpv-rt-31" rt-sc="pageId:hbethpv1;name:结果提示及分析;wt:1;desc:结果提示及分析;vt:;pvf:;">●HPV感染是宫颈癌的主要病因，大部分HPV感染会自行消退，与年龄及个人免疫力有关。
●HPV基因分析型检测是宫颈病变及官颈癌筛查的重要手段。
●低危型HPV感染可能引起良性病变，如生殖器湿疣。 
●高危型HPV感染可导致恶性病变，最终可发展为浸润性宫颈癌。</textarea>
      </div>
    </div>
    <div class="view-wrap">
      <div class="rt-sr-header">
        <div class="view-head">
          <div class="logo-tit">
            <div>
              <div class="page-tit hos-tit">河北省儿童医院</div>
              <div class="page-tit hos-tit">河北省第五人民医院</div>
              <div class="page-tit sub-tit">人乳头瘤病毒（HPV）基因分型检测报告</div>
            </div>
            <div class="code">
              <img class="rt-sr-w rt-sr-bcode" id="hbethpv-rt-32" rt-sc="pageId:hbethpv1;name:病理条形码;wt:6;desc:病理条形码;vt:;pvf:1;code:145;">
              <span class="rt-sr-w" id="hbethpv-rt-33" rt-sc="pageId:hbethpv1;name:条形码文字;wt:;desc:条形码文字;vt:;pvf:;code:145;"></span>
            </div>
          </div>
          <div class="blh-tit">
            <div>
              <span class="gray-txt">病理号：</span>
              <span class="red-txt" data-key="patLocalId"></span>
            </div>
          </div>
        </div>
        <div class="v_hzxx view-patient">
          <div class="view-flex mb-10">
            <div class="flex_1">
              <span class="rt_zt_l">姓  名：</span>
              <span class="rt_zt_r" data-key="name"></span>
            </div>
            <div class="flex_1">
              <span class="rt_zt_l">性  别：</span>
              <span class="rt_zt_r" data-key="sex"></span>
            </div>
            <div class="flex_1-5">
              <span class="rt_zt_l">年  龄：</span>
              <span class="rt_zt_r" data-key="age"></span>
            </div>
            <div class="flex_1-5">
              <span class="rt_zt_l">门诊号：</span>
              <span class="rt_zt_r" data-key="outpatientNo"></span>
            </div>
          </div>
          <div class="view-flex mb-10">
            <div class="flex_2">
              <span class="rt_zt_l">送检单位：</span>
              <span class="rt_zt_r" data-key="reqHospital"></span>
            </div>
            <div class="flex_1-5">
              <span class="rt_zt_l">床  号：</span>
              <span class="rt_zt_r" data-key="bedNo"></span>
            </div>
            <div class="flex_1-5">
              <span class="rt_zt_l">住院号：</span>
              <span class="rt_zt_r" data-key="inpatientNo"></span>
            </div>
          </div>
          <div class="view-flex mb-10">
            <div class="flex_2">
              <span class="rt_zt_l">申请科室：</span>
              <span class="rt_zt_r" data-key="reqDeptName"></span>
            </div>
            <div class="flex_1-5">
              <span class="rt_zt_l">送检医生：</span>
              <span class="rt_zt_r" data-key="reqPhysician"></span>
            </div>
            <div class="flex_1-5">
              <span class="rt_zt_l">送检日期：</span>
              <span class="rt_zt_r" data-key="registerDate"></span>
            </div>
          </div>
          <div class="view-flex mb-10">
            <div class="flex_1">
              <span class="rt_zt_l">标本名称：</span>
              <span class="rt_zt_r" data-key="sampleName"></span>
            </div>
          </div>
          <div class="view-flex mb-10">
            <div class="flex_1">
              <span class="rt_zt_l">临床诊断：</span>
              <span class="rt_zt_r" data-key="clinDiag"></span>
            </div>
          </div>
        </div>
      </div>
      <div class="rt-sr-body">
        <div class="item view-flex">
          <div class="flex_1 view-flex">
            <div class="item-title" style="width: 80px;">标本类型：</div>
            <div class="itme_l" style="width: calc(100% - 80px);">
              <p style="width:100%;padding: 0;line-height: 1.4;" data-key="hbethpv-rt-1"></p>
            </div>
          </div>
          <div class="flex_1 view-flex">
            <div class="item-title" style="width: 80px;">标本状态：</div>
            <div class="itme_l" style="width: calc(100% - 80px);">
              <p style="width:100%;padding: 0;line-height: 1.4;" data-key="hbethpv-rt-2"></p>
            </div>
          </div>
        </div>
        <div class="item view-flex">
          <div class="item-title" style="width: 80px;">检测方法：</div>
          <div class="itme_l" style="width: calc(100% - 80px);">
            <p style="width:100%;padding: 0;line-height: 1.4;" data-key="hbethpv-rt-3"></p>
          </div>
        </div>
        
        <div class="item view-flex bb">
          <div class="item-title" style="width: 80px;">检测内容：</div>
          <div class="itme_l" style="width: calc(100% - 80px);">
            <p style="width:100%;padding: 0;line-height: 1.4;" data-key="hbethpv-rt-4"></p>
          </div>
        </div>
        <div class="item view-flex">
          <div class="item-title border_Bold" style="width: 87px;">检测结果：</div>
          <div class="itme_l" style="width: calc(100% - 87px);">
            <p style="width:100%;padding: 0;line-height: 1.4;" data-key="hbethpv-rt-5"></p>
          </div>
        </div>
        <div style="padding: 10px 0;">
          <div class="item-flex labelBox" style="border-top: solid 1px #C0C4CC;">
            <label class="checkbox-label" data-name="hbethpv-rt-6">
              <span class="text">6</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-7">
              <span class="text">11</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-8">
              <span class="text">16</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-9">
              <span class="text">18</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-10">
              <span class="text">31</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-11">
              <span class="text">33</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-12">
              <span class="text">35</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-13">
              <span class="text">39</span>
            </label>
            <label class="checkbox-label">
              <span class="text">编号</span>
            </label>
          </div>
          <div class="item-flex labelBox">
            <label class="checkbox-label" data-name="hbethpv-rt-14">
              <span class="text">42</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-15">
              <span class="text">43</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-16">
              <span class="text">45</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-17">
              <span class="text">51</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-18">
              <span class="text">52</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-19">
              <span class="text">53</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-20">
              <span class="text">56</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-21">
              <span class="text">58</span>
            </label>
            <label class="checkbox-label">
              <span class="text lineBreak" data-key="patLocalId"></span>
            </label>
          </div>
          <div class="item-flex labelBox">
            <label class="checkbox-label" data-name="hbethpv-rt-23">
              <span class="text">59</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-24">
              <span class="text">66</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-25">
              <span class="text">68</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-26">
              <span class="text">73</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-27">
              <span class="text">81</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-28">
              <span class="text">82</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-29">
              <span class="text">83</span>
            </label>
            <label class="checkbox-label" data-name="hbethpv-rt-30">
              <span class="text">IC</span>
            </label>
            <label class="checkbox-label">
              <span class="text">HPV</span>
            </label>
          </div>
        </div>
        <div class="item">
          <div class="item-title border_Bold" style="margin-bottom: 10px;">结果提示及分析：</div>
          <div class="itme_l" style="width: 100%">
            <p class="text_p" style="width:100%;padding: 0;line-height: 1.4;" data-key="hbethpv-rt-31"></p>
          </div>
        </div>
      </div>
      <div class="rt-sr-footer">
        <div class="report-wrap view-flex">
          <div class="flex_1-5 view-flex">
            <span>报告日期：</span>
            <span data-key="affirmDate"></span>
          </div>
          <div class="flex_1 view-flex">
            <span>检验者：</span>
            <img data-img="reporterNo">
          </div>
          <div class="flex_1 view-flex">
            <span>审核者：</span>
            <img data-img="affirmReporterNo">
          </div>
          
        </div>
        <div class="tip-wrap">
          <div class="tip-text">
            <div>备注：本报告仅对所送检标本负责，结果分析和提示仅供临床参考。</div>
          </div>
        </div>
      </div>
    </div>
  </li>
</ul>
