/* 宽度 */
.wd-80 {
  width: 80px!important;
}
.wd-128 {
  width: 128px!important;
}
.wd-160 {
  width: 160px!important;
}
.wd-236 {
  width: 236px!important;
}
.wd-320 {
  width: 320px!important;
}
/* 间距 */
.ml-4 {
  margin-left: 4px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-20 {
  margin-left: 20px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-29 {
  margin-top: 29px;
}
/* 字体 */
.fs-12 {
  font-size: 12px;
}
.wacl{
  width: 100%;
  margin-bottom: 4px;
}

#gfeyjxbx1 {
  width: 100%;
  min-height: 100%;
  font-family: "宋体";
  font-size: 16px;
  color: #000;
  background: #F5F7FA;
  overflow: auto;
}
#gfeyjxbx1 * {
  font-family: "宋体";
}
#gfeyjxbx1 .pattern-type {
  padding: 8px 24px;
  border-bottom: 1px solid #C0C4CC;
}
#gfeyjxbx1 input[type="text"], #gfeyjxbx1 .inp-sty {
  height: 28px;
  background: #FFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 15px 0  11px;
  font-size: 16px;
}
#gfeyjxbx1 input[type="radio"],#gfeyjxbx1 input[type="checkbox"] {
  vertical-align: middle;
}
#gfeyjxbx1 .item-box {
  padding: 8px 24px;
}
#gfeyjxbx1 .row-item {
  display: flex;
  margin-bottom: 5px;
}
#gfeyjxbx1 .con-flex {
  display: flex;
}
#gfeyjxbx1 .flex-item {
  flex: 1;
}
#gfeyjxbx1 .item-tit {
  font-weight: bold;
  margin-bottom: 8px;
}
#gfeyjxbx1 .item-lb {
  display: inline-block;
  width: 96px;
  text-align: right;
}
#gfeyjxbx1 .bor-b {
  border-bottom: 1px solid #C0C4CC;
}
#gfeyjxbx1 .gfeyjxbx-edit .showInt {
  position: relative;
  background: #fff;
  height: 28px;
}
#gfeyjxbx1 .gfeyjxbx-edit .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
/* 预览 */
[isview="true"] #gfeyjxbx1 .gfeyjxbx-edit {
  display: none;
}
[isview="true"] #gfeyjxbx1 .gfeyjxbx-view {
  display: flex;
}
#gfeyjxbx1 .gfeyjxbx-view {
  display: none;
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 134px 56px;
  flex-direction: column;
  position: relative;
}
#gfeyjxbx1 .gfeyjxbx-view .view-head {
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#gfeyjxbx1 .gfeyjxbx-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
}
#gfeyjxbx1 .gfeyjxbx-view .logo-tit img {
  width: 68px;
  height: 68px;
  margin-right: 20px;
}
#gfeyjxbx1 .gfeyjxbx-view .blh-tit {
  display: flex;
  align-items: end;
  justify-content: flex-end;
  line-height: 22px;
  margin-left: 6px;
}
#gfeyjxbx1 .gfeyjxbx-view .hos-tit{
  font-size: 24px;
  color: #000;
  line-height: 32px;
  font-weight: bold;
}
#gfeyjxbx1 .gfeyjxbx-view .eng-tit{
  font-size: 14px;
  line-height: 20px;
}
#gfeyjxbx1 .gfeyjxbx-view .sub-tit{
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
}
#gfeyjxbx1 .gfeyjxbx-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#gfeyjxbx1 .gfeyjxbx-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
}
#gfeyjxbx1 .gfeyjxbx-view .black-txt {
  color: #000;
  font-size: 16px;
}
#gfeyjxbx1 .gfeyjxbx-view .info-i {
  width: 210px;
  display: flex;
  flex-wrap: wrap;
}
#gfeyjxbx1 .gfeyjxbx-view .info-i + .info-i {
  margin-left: 8px;
}
#gfeyjxbx1 .gfeyjxbx-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#gfeyjxbx1 .gfeyjxbx-view .view-patient .p-item {
  margin-top: 8px;
}
#gfeyjxbx1 .gfeyjxbx-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#gfeyjxbx1 .gfeyjxbx-view .desc-con {
  display: flex;
  align-items: baseline;
  padding-top: 8px;
}
#gfeyjxbx1 .gfeyjxbx-view .desc-tit {
  width: 96px;
  text-align: right;
}
#gfeyjxbx1 .gfeyjxbx-view .yz-tip {
  margin-left: 96px;
  margin-bottom: 20px;
}
#gfeyjxbx1 .gfeyjxbx-view .bold {
  font-weight: bold;
}
#gfeyjxbx1 .gfeyjxbx-view .rpt-img-ls {
  display: none;
}
#gfeyjxbx1 .gfeyjxbx-view .item-img {
  width: 220px;
  height: 165px;
  border: 1px solid #eee;
  margin-top: 12px;
}
#gfeyjxbx1 .gfeyjxbx-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#gfeyjxbx1 .gfeyjxbx-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 27px;
  right: 27px;
}
#gfeyjxbx1 .gfeyjxbx-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#gfeyjxbx1 .gfeyjxbx-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#gfeyjxbx1 .gfeyjxbx-view .tip-wrap {
  margin-top: 8px;
  font-size: 12px;
}
#gfeyjxbx1 .gfeyjxbx-view .flex-column {
  margin: 8px auto;
  width: 520px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: 10px;
}
#gfeyjxbx1 .gfeyjxbx-view .preview-img-ls {
  display: none;
  flex-wrap: wrap;
  padding: 8px 0 0 54px;
}
#gfeyjxbx1 .gfeyjxbx-view .preview-img-ls .preview-item-img {
  width: 267px;
  height: 200px;
  margin-right: 25px;
  margin-bottom: 20px;
}
#gfeyjxbx1 .gfeyjxbx-view .preview-img-ls .preview-item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#gfeyjxbx1 .gfeyjxbx-view .report-img {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0!important;
  margin-top: 0!important;
}
#gfeyjxbx1 .gfeyjxbx-view .report-img img {
  height: 205px;
  width: 202px;
  display: block;
  object-fit: contain;
}
/* 细胞学分析下拉框多选 */
#gfeyjxbx1 .multi-w {
  position: relative;
  flex: 1;
  height: 30px;
  border: 1px solid #DCDFE6;
  padding: 0 18px 0 8px;
  border-radius: 3px;
  background: #fff;
}
#gfeyjxbx1 .multi-w + input {
  display: none;
}
#gfeyjxbx1 .multi-w::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#gfeyjxbx1 xm-select {
  height: 100%;
  min-height: unset;
  line-height: 26px;
  border: none;
}
#gfeyjxbx1 xm-select .xm-icon,
#gfeyjxbx1 xm-select .xm-label-block {
  display: none;
}
#gfeyjxbx1 xm-select .xm-label {
  right: 0;
}
#gfeyjxbx1 xm-select .label-content {
  padding: 0;
  line-height: 26px;
  font-size: 16px;
}