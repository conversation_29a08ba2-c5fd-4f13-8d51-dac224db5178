$(function() {
  window.initHtmlScript = initHtmlScript;
})
var dplBlockShow = false;
var mmUnit = 'mm', perUnit = '%',mDevSUnit = 'm/s', mmHgUnit = 'mmHg';
var resultData = null;
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }

  resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  if(resultData.length) {
    $('.dplBlock').css('display','block');
    dplBlockShow = true;
  }
  setDplInp();

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });

  // 多普勒按钮
  $('.dplBtn').click(function() {
    dplBlockShow = !dplBlockShow;
    dplBlockShow ? $('.dplBlock').css('display','block') : $('.dplBlock').css('display','none');
    setDplInp();
  })
}
// 点击多普勒按钮
function setDplInp() {
  let dplInpChild = $('.dplBlock').find('input[type="text"]');
  let dplCRChild = $('.dplBlock').find('input:not([type="text"])');
  if(!dplBlockShow) {
    dplInpChild.each(function(i,dom) {
      $(dom).val('');
      $(dom).attr('n-enterjump','1');
    })
    dplCRChild.each(function(i,dom) {
      $(dom).attr('checked',false);
    })
  }else {
    dplInpChild.each(function(i,dom) {
      $(dom).removeAttr('n-enterjump');
    })
  }
  keydown_to_tab('jzdm1');
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 获取描述
function getDescription() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let description = '';
  let blockParId1 = ['jzdm-rt-1','jzdm-rt-7','jzdm-rt-12','jzdm-rt-15'];
  let blockParId2 = ['jzdm-rt-20','jzdm-rt-27','jzdm-rt-34','jzdm-rt-41'];
  let blockParId3 = ['jzdm-rt-42'];
  let blockIdArr1 = ['jzdm-rt-1','jzdm-rt-15'];
  let blockIdArr2 = ['jzdm-rt-12'];
  let strList = [],str = '',blockStrList1 = [],blockStrList2 = [],blockStrList3 = [];
  $('#jzdm1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      // 第一块
      if(blockParId1.includes(parData.id)) {
        let childD1 = parData.child || [];
        if(!childD1.length) {
          blockStrList1.push(parData.val);
        }else {
          if(parData.id === 'jzdm-rt-7' && childD1.length) {
            blockStrList1.push(parData.val);
          }
          childD1.map(function(cItem1) {
            // 内径、居肺动脉
            if(blockIdArr1.includes(parData.id)) {
              blockStrList1.push(cItem1.name);
            }
            // 真假腔
            else if(blockIdArr2.includes(parData.id)) {
              blockStrList1.push(parData.val);
              blockStrList1.push(cItem1.name);
            }
            else {
              let childD2 = cItem1.child || [];
              childD2.map(function(cItem2) {
                blockStrList1.push(cItem1.val + cItem2.val + mmUnit);
              })
            }
          })
        }
      }
      // 第二块
      if(blockParId2.includes(parData.id)) {
        let childD1 = parData.child || [];
        if(!childD1.length) {
          blockStrList2.push(parData.val);
        }else {
          childD1.map(function(cItem1) {
            if(cItem1.child && cItem1.child.length) {
              let childD2 = cItem1.child || [];
              childD2.map(function(cItem2) {
                blockStrList2.push(parData.val + cItem2.name);
              })
            }else {
              blockStrList2.push(parData.val + cItem1.val);
            }
          })
        }
      }

      // 第三块
      if(blockParId3.includes(parData.id)) {
        let childD1 = parData.child || [];
        if(!childD1.length) {
          blockStrList3.push(parData.val);
        }else {
          childD1.map(function(cItem1) {
            if(cItem1.child && cItem1.child.length) {
              let childD2 = cItem1.child || [];
              childD2.map(function(cItem2) {
                if(cItem1.name.includes('PG')) {
                  blockStrList3.push(cItem1.val + cItem2.val + mmHgUnit);
                }else if(cItem1.name.includes('Vmax')) {
                  blockStrList3.push(cItem1.val + cItem2.val + mDevSUnit);
                }else {
                  blockStrList3.push(cItem1.val + cItem2.val + mmUnit);
                }
              })
            }
          })
          blockStrList3.unshift(parData.val);
        }
      }
    }
  })
  if(blockStrList2.length) {
    str = 'CDFI可在该处探及';
    str += blockStrList2[0];
    blockStrList2.splice(0,1);
    blockStrList2.unshift(str);
  }
  if(blockStrList3.length) {
    str = blockStrList3[1] ? blockStrList3[0] + blockStrList3[1] : blockStrList3[0];
    blockStrList3.splice(0,2);
    blockStrList3.unshift(str);
  }
  strList.push(blockStrList1,blockStrList2,blockStrList3);
  strList = strList.filter(function(item) { return item.length });
  description = strList.length ? strList.join('。') : '';
  // console.log('description-->',description);
  return description;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = getDescription();
  // rtStructure.impression = '';
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}