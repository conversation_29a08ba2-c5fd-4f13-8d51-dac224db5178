$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var isSavedReport = false; //模板是否填写过
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: $('#hbetjybl1 .preview'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // 仅为当前模板的特殊条件判断，其他常规模板仍取rtStructure.enterOptions.isSavedReport
    isSavedReport = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.resultData && rtStructure.enterOptions.resultData.length > 0;
    if(rtStructure.enterOptions.type==='view') {
      initPreview()
    } else {
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description =getDescription();
  rtStructure.impression = getImpressionStr();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
    memo:getMemo(), // 项目
  }
  // console.log(rtStructure);
}
function initPage(){
//   // 清理字符串函数，用于移除括号及其内容
// function cleanString(str) {
//     return str.replace(/\(.*\)|（.*）/g, '').trim(); // 正则表达式匹配括号及其内容
// }
  $('input,textarea').attr('autocomplete','off')
  let { dictExamItemList=[] } =  publicInfo
  let dictExamItem = dictExamItemList.shift() || {}
  let examArr = [
    {
      "floor": "500",
      "label": "EB病毒PCR检测(血)",
      "value": "EB病毒(EBV)"
    },
    {
      "floor": "500",
      "label": "巨细胞病毒PCR检测(血)",
      "value": "人巨细胞病毒(HCMV)"
    },
    {
      "floor": "1000",
      "label": "肺炎支原体PCR检测(血)",
      "value": "肺炎支原体(MP)"
    },
    {
      "floor": "500",
      "label": "结核杆菌PCR检测",
      "value": "结核分枝杆菌复合群(TB)"
    },
    {
      "floor": "1000",
      "label": "单纯疱疹病毒I型 II型PCR检测",
      "value": "单纯疱疹病毒I型(HSV I)"
    },
    {
      "floor": "",
      "label": "沙眼衣原体、肺炎支原体、肺炎衣原体PCR检测",
      "value": ""
    },
    {
      "floor": "1000",
      "label": "肠道病毒三联PCR检测",
      "value": "肠道病毒"
    },
    {
      "floor": "20",
      "label": "乙型肝炎病毒PCR检测(血液)",
      "value": "乙型肝炎病毒(HBV)"
    },
    {
      "floor": "5000",
      "label": "肺炎支原体及耐药突变位点PCR检测",
      "value": "耐药突变位点"
    },
    {
      "floor": "1000",
      "label": "腺病毒PCR检测",
      "value": "腺病毒(ADV)"
    },
    {
      "floor": "500",
      "label": "巨细胞病毒PCR检测(尿)",
      "value": "人巨细胞病毒(HCMV)"
    },
    {
      "floor": "500",
      "label": "巨细胞病毒PCR检测(脑脊液)",
      "value": "人巨细胞病毒(HCMV)"
    },
    {
      "floor": "500",
      "label": "EB病毒PCR检测(脑脊液)",
      "value": "EB病毒(EBV)"
    },
    {
        'floor':'500',
        'label':'EB病毒PCR检测(肺泡灌洗液)',
        "value": "EB病毒(EBV)"
    },
    {
      "floor": "1000",
      "label": "肺炎支原体PCR检测(脑脊液)",
      "value": "肺炎支原体(MP)"
    },
    {
      "floor": "1000",
      "label": "肺炎支原体PCR检测(肺泡灌洗液)",
      "value": "肺炎支原体(MP)"
    },
    {
      "floor": "1000",
      "label": "肺炎支原体PCR检测(胸水)",
      "value": "肺炎支原体(MP)"
    }
  ]
  let examObj = examArr.find(item=>item.label===dictExamItem.itemName || item.value===dictExamItem.itemName)
  if(!isSavedReport){
    if(examObj){
      $('#hbetjybl-rt-2').val(examObj.value)
      $('#hbetjybl-rt-7').val(examObj.floor)
    }else{
      let value = dictExamItem.itemName || ''
      $('#hbetjybl-rt-2').val(value)
    }
  }
  $('#hbetjybl-rt-2').on('blur',function(){
   let textValue =  $(this).val()
   let examObj = examArr.find(item=>item.value===textValue || item.label === textValue)
   if(examObj){
    $('#hbetjybl-rt-7').val(examObj.floor)
   }
  })
}
function initPreview(){
  // 签名
  curElem.find('.preview [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } 
    }else {
      $(this).hide();
    }
  });
  curElem.find('.preview [data-key]').each(function(){
    var key = $(this).attr('data-key')
    var idAnVal,value;
    var keyList = key.split(',');
    if(keyList.length>1){
      let result = []
      keyList.forEach(item=>{
        result.push(publicInfo[item])
      })
      $(this).html(result.join(' '))
      return
    }
    // 报告图片
    if(key==='report-img'){
      if(rptImageList && rptImageList.length){
        rptImageList = rptImageList.slice(0,2)
        let html = ''
        rptImageList.forEach(item=>
            html+=`<img src="${item.src}" alt=""">`
        )
        $(this).html(html)
      }else{
        $(this).hide()
      }
      return
    }
    // 兜底
    idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
    value = publicInfo[key] || idAnVal;
    
    this.style.whiteSpace = 'pre-line'
    $(this).html(value)
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  $('#hbetjybl-rt-15').html('本院')
}
function getDescription (){
  let keyObj = {
    '#hbetjybl-rt-2':'检测项目：',
    '#hbetjybl-rt-3':'浓度：',
    '#hbetjybl-rt-4':'CT值：'
  }
  let result = []
  Object.entries(keyObj).forEach(([key,value],)=>{
    let str =  $(key).val()
    if(str){
      result.push(`${value}${str}`)
    }
  })
  return result.join('、')
}
// IMPRESSION 存入诊断
function getImpressionStr(){
  let keyObj = {
    '#hbetjybl-rt-5':'检测结果：',
  }
  let result = []
  Object.entries(keyObj).forEach(([key,value],)=>{
    let str =  $(key).val()
    if(str){
      result.push(`${value}${str}`)
    }
  })
  return result.join('\n')
}
function getMemo(){
  let keyObj = {
    '#hbetjybl-rt-6':'参考范围：',
    '#hbetjybl-rt-7':'检测下限：',
  }
  let result = []
  Object.entries(keyObj).forEach(([key,value],)=>{
    let str =  $(key).val()
    if(str){
      result.push(`${value}${str}`)
    }
  })
  return result.join(' ')
}