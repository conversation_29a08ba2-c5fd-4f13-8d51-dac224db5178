$(function() {
  window.initHtmlScript = initHtmlScript;
})

var jcData = [
  {title: '阴性（-）', id: '阴性（-）'},
  {title: '阳性（+）', id: '阳性（+）'},
]
var qhlData = [
  {title: ' ', id: ' '},
  {title: '高', id: '高'},
  {title: '低', id: '低'},
]
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; // 报告是否被填写过
var isSavedReport = false; //报告是否填写过
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#hbetebbd1 .hbetebbd-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    resultData = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.resultData : [];
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon();
    } else {
      initPage();
    }
  }
}
// 初始化
function initPage() {
  setDefaultVal();
  let textID = ['hbetebbd-rt-6','hbetebbd-rt-8','hbetebbd-rt-9','hbetebbd-rt-10']
  textID.forEach(e => {
    initInpAndSel(e, jcData);
  })
  initInpAndSel('hbetebbd-rt-7', qhlData);
  if(!isSavedReport){
    textID.forEach(item => {
      let value = $('#'+item).val().replace(/(\+|\-)/g,(keyword)=>{
        if(keyword==='+'){
          return '阳性（+）'
        }else{
          return '阴性（-）'
        }
      })
      value ? value : value = '阴性（-）'
      $('#'+item).val(value)
    })
  }
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal) {
  let selLen = 200;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    className: 'laySelLab',
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: `width: ${selLen}px;`
  });
}
// 设置初始值
function setDefaultVal() {
  if(!resultData.length) {
    $('#hbetebbd-rt-1').val('EB病毒衣壳抗原（CA）IgG');
    $('#hbetebbd-rt-2').val('衣壳抗原Ig抗体亲和力');
    $('#hbetebbd-rt-3').val('EB病毒衣壳抗原（CA）IgM');
    $('#hbetebbd-rt-4').val('EB病毒早期抗原（EA）IgG');
    $('#hbetebbd-rt-5').val('EB病毒核抗原（NA）抗体');
  }
}
// 获取诊断
function getImpression() {
  let impression = '',strArr = [];
  var jcjgMap = {
    '#hbetebbd-rt-1': '#hbetebbd-rt-6',
    '#hbetebbd-rt-2': '#hbetebbd-rt-7',
    '#hbetebbd-rt-3': '#hbetebbd-rt-8',
    '#hbetebbd-rt-4': '#hbetebbd-rt-9',
    '#hbetebbd-rt-5': '#hbetebbd-rt-10',
  }
  for(let key in jcjgMap) {
    let jcxmVal = $(key).val();
    let jcjgVal = $(jcjgMap[key]).val();
    if(jcxmVal && jcjgVal) {
      strArr.push(jcxmVal + '：' + jcjgVal);
    }
  }
  impression = strArr.length ? strArr.join('；') : '';
  return impression;
}
// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }

  // 文字值
  var mhArr = ['hbetebbd-rt-1','hbetebbd-rt-2','hbetebbd-rt-3','hbetebbd-rt-4','hbetebbd-rt-5']; // 需要添加冒号的节点
  curElem.find('.hbetebbd-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var keyList = key.split(',');
    if (keyList.length > 1) {
      let result = [];
      keyList.forEach(item => {
        result.push(publicInfo[item]);
      });
      $(this).html(result.join(' '));
      return;
    }
    
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'sampleName') {
      value = value.replace(/,/g, '；');
    }
    if(key === 'reqHospital') {
      // 特殊处理，默认显示“本院”
      value = '本院';
    }
    // 添加冒号
    if(mhArr.includes(key)) {
      value = value + '：';
    }
    $(this).html(value);
    addIdToNodeByView(this, keyList, idAndDomMap);
  })

  // 签名
  curElem.find('.hbetebbd-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}