$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer:  document.querySelector('#sjbg1 .preview'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPreview()
    } else {
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpressionStr();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function initPage(){
  initDate()
  initSelect()
  $('input,textarea').attr('autocomplete','off')
  $('input,textarea').attr('placeholder','')
}
function initDate(){
  const arr = ['#sjbg-rt-2','#sjbg-rt-3']
  arr.forEach(item=>{
    layui.use('laydate', function(){
      var laydate = layui.laydate;
      //执行一个laydate实例
      laydate.render({
        elem: item //指定元素
      });
    });
  })
}
function initPreview(){
  curElem.find('.preview [data-key]').each(function(){
    var key = $(this).attr('data-key')
    var idAnVal,value;
 
    // 兜底
    idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
    value = publicInfo[key] || idAnVal;
    this.style.whiteSpace = 'pre-wrap'
    $(this).html(value)
    addIdToNodeByView(this, key, idAndDomMap);
  })
  curElem.find('.preview [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } 
    }else {
      $(this).hide();
    }
  });
}
function getImpressionStr(){
  let keyObj = {
    '#sjbg-rt-6':'病理解剖诊断：',
  }
  let result = []
  Object.entries(keyObj).forEach(([key,value],)=>{
    let str =  $(key).val()
    if(str){
      result.push(`${value}${str}`)
    }
  })
  return result.join('\n')
}
function initSelect(){
 const id = '#sjbg-rt-1'
 const deptList = getAllUserList({
  deptCode:userInfo.deptCode || '',
  staffType:'5'
 }).map(item=>{
  let { staffNo,name } = item
  return {
    title:name,
    id:staffNo
  }
 })
 initInpAndSel(id,deptList)
}
function initInpAndSel(idList, optionList, lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `${idList}`,
    data: optionList,
    click: function(obj) {
      this.elem.val(obj.title);
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.height = '200px'
      }
    }
  });
}