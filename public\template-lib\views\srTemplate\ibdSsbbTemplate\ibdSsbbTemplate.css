.singleDisEditReport.main-page {
  min-width: unset;
}

#ibdSsbb1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  /* background: #F5F7FA; */
  overflow: auto;
}

#ibdSsbb1 * {
  font-family: '宋体';
}

.text-blod {
  font-weight: bold;
}

#ibdSsbb1 .p-item + .p-item{
  margin-top: 4px;
}
#ibdSsbb1 .disease-item{
  padding: 8px 24px;
  margin-bottom: 8px;
  background: #F5F7FA;
}

.c-item{
  display: flex;
  align-items: center;
  margin-right: 8px;
}

#ibdSsbb1 input[type="text"]{
  height: 28px;
  border: 1px solid #C0C4CC;
  border-radius: 3px;
}

#ibdSsbb1 .layui-inline input{
  position: relative;
  z-index: 10;
  background: transparent;
  padding: 0 16px;
}
#ibdSsbb1 .p-item label+label{
  margin-left: 16px;
}
#ibdSsbb1 .p-item label input{
  margin-right: 4px;
}
#ibdSsbb1 .layui-inline input:disabled{
  background-color: rgba(240,242,245,1)
}
#ibdSsbb1 .showInt {
  background: #fff;
}
#ibdSsbb1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}
#ibdSsbb1 .showInt.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}
.laySelLab li{
  min-height: 26px;
}

#ibdSsbb1 .ssbb-view {
  display: none;
}

[isview="true"] #ibdSsbb1 .ssbb-edit {
  display: none;
}

[isview="true"] #ibdSsbb1 .ssbb-view {
  display: block;
}

#ibdSsbb1 .ssbb-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 16px;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 130px;
  color: #000;
}

#ibdSsbb1 .ssbb-view .view-head {
  width: 100%;
  border-bottom: 1px solid #999;
  padding-bottom: 24px;
  text-align: center;
}

#ibdSsbb1 .ssbb-view .head-img {
  margin: 0 auto 10px;
  width: 308px;
  height: 40px;
}

#ibdSsbb1 .ssbb-view .head-img img {
  width: 100%;
  height: 100%;
}

#ibdSsbb1 .ssbb-view .right-num {
  display: flex;
  position: absolute;
  top: 110px;
  right: 56px;
  color: #41898A;
  line-height: 24px;
}

#ibdSsbb1 .ssbb-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#ibdSsbb1 .ssbb-view .black-txt {
  color: #000;
  font-size: 16px;
  line-height: 24px;
}

#ibdSsbb1 .ssbb-view .page-tit {
  font-size: 24px;
  font-weight: 800;
}

#ibdSsbb1 .ssbb-view .page-tit.sub-tit {
  font-size: 28px;
}

#ibdSsbb1 .ssbb-view .gray-txt {
  color: #000;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 24px;
}

#ibdSsbb1 .ssbb-view .info-i {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

#ibdSsbb1 .ssbb-view .info-i+.info-i {
  margin-left: 8px;
}

#ibdSsbb1 .ssbb-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

#ibdSsbb1 .ssbb-view .view-patient .p-item {
  align-items: center;
}

#ibdSsbb1 .ssbb-view .rpt-img-ls {
  display: none;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 0;
  justify-content: center;
}

#ibdSsbb1 .ssbb-view .item-img {
  /* display: inline-block; */
  width: 214px;
  height: 160px;
  box-sizing: border-box;
  border: 1px solid #eee;
  margin-right: 12px;
  margin-top: 12px;
}

#ibdSsbb1 .ssbb-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

#ibdSsbb1 .ssbb-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}

#ibdSsbb1 .ssbb-view .report-wrap {
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#ibdSsbb1 .ssbb-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

#ibdSsbb1 .ssbb-view .reporter-i.flex-1 {
  flex: 1.5;
}

#ibdSsbb1 .ssbb-view .reporter-i > span:first-child {
  width: 80px;
}
#ibdSsbb1 .ssbb-view .reporter-i > span:last-child {
  flex: 1;
}

#ibdSsbb1 .ssbb-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}

#ibdSsbb1 .ssbb-view .reporter-i+.reporter-i {
  margin-left: 8px;
}

/* 宽度 */
.wd-60{width: 60px;}
.wd-64{width: 64px;}
.wd-72{width: 72px;}
.wd-104{width: 104px;}
.wd-120{width: 120px;}
.wd-124{width: 124px;}
.wd-132{width: 132px;}
.wd-140{width: 140px;}
.wd-160{width: 160px;}
.wd-164{width: 164px;}
.wd-208{width: 208px;}
.wd-224{width: 224px;}
.wd-240{width: 240px;}
.wd-256{width: 256px;}
/* 间距 */
.mt-4{
  margin-top: 4px;
}
.mb-12{margin-bottom: 12px;}
.mlr-4{margin: 0 4px;}
.mr-8{margin-right: 8px;}
.mr-16{margin-right: 16px;}
.mr-20{margin-right: 20px;}
.mr-32{
  margin-right: 32px;
}
.mr-24{margin-right: 24px;}
.mr-52{margin-right: 52px;}
.mr-58{margin-right: 58px;}
.mr-88{margin-right: 88px;}