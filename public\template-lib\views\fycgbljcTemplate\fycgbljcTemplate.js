// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
    'cgbljc-0006.001': { label: '职业', wt: '', hisKey: 'degree'},
    'cgbljc-0008.001': { label: '身份证', wt: '', hisKey: 'identity' },
    // 'cgbljc-0012.001': { label: '门诊/住院号', wt: '', hisKey: 'outPatientNo,inpatientNo' },
    'cgbljc-0030.001': { label: '送检医生', wt: '', hisKey: 'optName' },
    'cgbljc-0022.001-rItem--1': { label: '绝经', wt: '5', hisKey: 'flags', index: 10, value: '1', sex: '女' },
    'cgbljc-0022.001-rItem--2': { label: '未绝经', wt: '5', hisKey: 'flags', index: 10, value: '0', sex: '女' },
    // 'cgbljc-0028': { label: '离体时间', wt: '1', hisKey: 'momentTime' },
    // 'cgbljc-0029': { label: '固定时间', wt: '1', hisKey: 'momentTime' },
}
var saveApplyParams = {
    'degree': { label: '职业', id: 'cgbljc-0006.001'},
    'phoneNumber': { label: '联系电话', id: 'cgbljc-0007.001'},
    'medRecord': { label: '病史摘要', id: 'cgbljc-0020.001'},
    'clinDiag': { label: '临床诊断', id: 'cgbljc-0026.001'},
    'applyItemList': { label: '检查项目', id: ''},
    'gmSampleList': { label: '标本信息', id: ''},
    'flags': { label: '是否绝经', ids: [
        {id:'cgbljc-0022.001-rItem--1', index: 10, value: '1'},
        {id:'cgbljc-0022.001-rItem--2', index: 10, value: '0'},
      ]
    }
}
function initHtmlScript(ele) {
    // 编辑
    if($(ele).attr('isview') !== 'true') {
        initDatePicker();
        if($(".sampleViewPart").length) {
            specialBlockViewHandler('sample', false)
        }
        if($(".eItemViewPart").length) {
            specialBlockViewHandler('examItem', false)
        }
        setDisabledByConfig && setDisabledByConfig(applyStatusRes);
    } else { // 预览
        if($(".sampleViewPart").length) {
            specialBlockViewHandler('sample', true)
        }
        if($(".eItemViewPart").length) {
            specialBlockViewHandler('examItem', true)
        }
    }
    getExamSubClassList(applyInfo);  //检查子类
    getGmSamplePartByLevelList();   //检查部位
    getSampleList(applyInfo);  //标本
    setValByHis(hisAndFormMap)
}

// 初始化日期时间插件
function initDatePicker() {
    var path = location.href.split('template-lib/')[0]
    layui.config({dir: path + 'template-lib/plugins/layui/'})
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //执行一个laydate实例
        laydate.render({
            elem: '.date-wrap01', //指定元素
            type: 'date',
            trigger: 'click',
            format: 'yyyy-MM-dd'
        });
        laydate.render({
            elem: '.date-wrap02', //指定元素
            type: 'date',
            trigger: 'click',
            format: 'yyyy-MM-dd'
        });
        laydate.render({
            elem: '.time-wrap01', //指定元素
            type: 'time',
            trigger: 'click',
            format: 'HH时mm分'
        });
        laydate.render({
            elem: '.time-wrap02', //指定元素
            type: 'time',
            trigger: 'click',
            format: 'HH时mm分'
        });
    });
}

$(function () {
    window.initHtmlScript = initHtmlScript;
})

// 