/**
 * 单病种结构化报告新标准格式相关方法
 */

; (function () {
  var instanceList = [];
  window.HTMLElement = window.HTMLElement || Element;
  // 兼容IE8
  if (typeof Array.prototype.indexOf != 'function') {
    Array.prototype.indexOf = function(searchElement, fromIndex) {
      var k;
      if (this == null) {
        throw new TypeError('"this" is null or not defined');
      }
  
      var o = Object(this);
      var len = o.length >>> 0;
      if (len === 0) {
        return -1;
      }
  
      var n = fromIndex | 0;
  
      if (n >= len) {
        return -1;
      }
      k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
      while (k < len) {
        if (k in o && o[k] === searchElement) {
          return k;
        }
        k++;
      }
      return -1;
    };
  }
  
  var isTrace = location.href.indexOf('trace') > -1;
  var scriptCount = 0;
  var preURL = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
  function addScript(url, type){
    if(type === 'style') {
      var link = document.createElement('link');
      link.rel = "stylesheet";
      link.href = preURL + url;
      document.getElementsByTagName('head')[0].appendChild(link);
      scriptCount++;
    } else {
      var script = document.createElement('script');
      script.src = preURL + url;
      document.getElementsByTagName('head')[0].appendChild(script);
      script.onload = script.onreadystatechange = function() {
        scriptCount++;
      }
    }
  }
  var innerScriptArr = [
    {src: "/template-lib/plugins/jquery.min.js", type: 'script'},
    {src: "/template-lib/plugins/crypto-js.min.js", type: 'script'},
    {src: "/template-lib/controls/publicCode.js", type: 'script'},
    {src: "/template-lib/plugins/dayjs.min.js", type: 'script'},
  ];
  for(var inIdx = 0; inIdx < innerScriptArr.length; inIdx++) {
    addScript(innerScriptArr[inIdx].src, innerScriptArr[inIdx].type);
  }
  
  // 兼容IE8
  if (typeof Array.prototype.forEach != 'function') {
    Array.prototype.forEach = function(callback){
      for (var i = 0; i < this.length; i++){
        callback.apply(this, [this[i], i, this]);
      }
    };
  }
  String.prototype.trim = function(){
    return this.replace(/(^\s*)|(\s*$)/g, "");
  }

  if (!("classList" in document.documentElement)) {
    Object.defineProperty(HTMLElement.prototype, 'classList', {
      get: function() {
        var self = this;
        function update(fn) {
          return function(value) {
            var classes = self.className.split(/\s+/g),
            index = classes.indexOf(value);

            fn(classes, index, value);
            self.className = classes.join(" ");
          }
        }

        return {
          add: update(function(classes, index, value) {
            if (!~index) classes.push(value);
          }),

          remove: update(function(classes, index) {
            if (~index) classes.splice(index, 1);
          }),

          toggle: update(function(classes, index, value) {
            if (~index)
              classes.splice(index, 1);
            else
              classes.push(value);
          }),

          contains: function(value) {
            return !!~self.className.split(/\s+/g).indexOf(value);
          },

          item: function(i) {
            return self.className.split(/\s+/g)[i] || null;
          }
        };
      }
    });
  }

  function addListenerHandler(target, type, handler) {
    if(target.addEventListener){
      target.addEventListener(type, handler, false);
    }else if(target.attachEvent){
      target.attachEvent("on"+ type, handler);
    }else {
      target["on"+type] = handler;
    }
  }

  // 合并对象的方法兼容IE8
  function objMerge(target, source) {
    target = JSON.parse(JSON.stringify(target));
    for (var key in source) {
      target[key] = source[key];
    }
    return target;
  }

  // 删除带有.rt-hide的元素兼容IE8
  function removeEleBySelector(eleArr) {
    for(var i = 0; i < eleArr.length; i++) {
      var ele = eleArr[i];
      if(ele.classList.contains('rt-hide')) {
        eleArr.splice(i, 1);
        i--;
      }
    }
    return eleArr;
  }

  // 获取选中的radio的值，兼容IE8
  function getCheckedSelector(eleArr) {
    var node = null;
    for(var i = 0; i < eleArr.length; i++) {
      var ele = eleArr[i];
      if(ele.checked) {
        node = ele;
        break;
      }
    }
    return node;
  }

  // 获取所有控件属性
  function getWidgetAttr(code) {
    var _this = this;
    // rt-structure-data
    _this.widgetPropMap = [];
    var reg = /<script id="rt-structure-data">(.*)<\/script>/;
    var matchArr = code.match(reg);
    var srData = []
    if (matchArr && matchArr[1]) {
      srData = JSON.parse(matchArr[1]);
    }
    var widgetPropMap = sortNodeByTop.call(_this, srData);
    _this.widgetPropMap = widgetPropMap;
  }
  // 按节点的top值排序,由于多页的问题top做计算:原top+页面的高度
  function sortNodeByTop(data) {
    var _this = this;
    var pageProp = data[0] || {};
    // 通过dom节点属性遍历存储控件的属性
    var widgetProp = [];
    var pageLi = querySelectorAll.call(_this, 'li.page');
    nodeListToArray(pageLi).forEach(function (page, index) {
      var rtScDom = querySelectorAll('[rt-sc]', page);
      nodeListToArray(rtScDom).forEach(function (node, i) {
        var id = node.id;
        var pid = node.getAttribute('pid') || '';
        var sc = node.getAttribute('rt-sc');
        var scArr = sc.split(';');
        var scObj = {
          id: id,
          pid: pid,
          rtScPageNo: index + 1,
        };
        scArr.forEach(function(scItem) {
          var key = scItem.split(':')[0];
          var value = scItem.split(':')[1];
          if(key) {
            if(key === 'itemList') {
              scObj[key] = eval(decodeURIComponent(value));
              scObj['groupId'] = id;
            } else {
              var numberList = ['left', 'top', 'wt'];
              scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
            }
          }
        })
        widgetProp.push(scObj);
      })
    })
    
    var dataMap = [];
    var sortByTop = true;
    widgetProp.forEach(function(widget, index) {
      if(widget.top === undefined) {
        sortByTop = false;
      }
      var computedTop = widget.rtScPageNo > 1 ? widget.top + (pageProp.pH || 900) : widget.top;  //用于排序

      var widgetObj = objMerge(widget, {computedTop: computedTop, pH: pageProp.pH || 900});
      dataMap.push(widgetObj);
      if(widget.itemList && widget.itemList.length) {
        widget.itemList.forEach(function(wItem) {
          var wItemObj = objMerge(widget, wItem);
          dataMap.push(objMerge(wItemObj, {computedTop: computedTop, isItemList: true}));
        })
      }
    })
    if(sortByTop) {
      dataMap = dataMap.sort(function(a, b){
        // top差值在-10~10则默认y相同,根据left排序
        if((a.computedTop - b.computedTop >= -10) && (a.computedTop - b.computedTop <= 10) ) {
          return a.left - b.left;
        } else {
          return a.computedTop - b.computedTop;
        }
      })
    }
    return dataMap;
  }

  // 判断控件类型
  function getWidgetType(widget) {
    var widgetTypes = ['ck-checkbox', 'r-radio', 'tit-title', 't-text', 's-select', 'bcode-barcode', 'qcode-qrcode'];
    for (var i = 0; i < widgetTypes.length; i++) {
      var name = widgetTypes[i].split('-')[0];
      var type = widgetTypes[i].split('-')[1];
      if (widget.className.indexOf('rt-sr-' + name) > -1) {
        return type;
      }
    }
  }

  function nodeListToArray(nodeList) {
    var list = [];
    for(var i = 0; i < nodeList.length; i++) {
      list.push(nodeList[i]);
    }
    // return Array.prototype.slice.call(nodeList);
    return list;
  }

  function querySelector(selector, rootDom) {
    if(!rootDom && !this.ele) {
      return null;
    }
    return (rootDom || this.ele).querySelector(selector);
  }

  function querySelectorAll(selector, rootDom) {
    if(!rootDom && !this.ele) {
      return [];
    }
    return nodeListToArray((rootDom || this.ele).querySelectorAll(selector));
  }

  function setChildrenEnabled(parent) {
    var _this = this;
    querySelectorAll.call(_this, '[parent-id="' + parent.id + '"]').forEach(function (child) {
      if (getWidgetType(child) === 'title') {
        setChildrenEnabled.call(_this, child);
      }
      child.disabled = false;
    });
  }

  function setChildrenDisabled(parent) {
    var _this = this;
    var type = getWidgetType(parent);
    var parentId = parent.id;
    querySelectorAll.call(_this, '[parent-id="' + parentId + '"]').forEach(function (child) {
      if (['checkbox', 'radio'].indexOf(getWidgetType(child)) > -1) {
        child.checked = false;
      }
      if (getWidgetType(child) === 'text') {
        child.value = '';
      }
      child.disabled = true;
      if (child.children) {
        setChildrenDisabled.call(_this, child);
      }
    });
  }

  function init(noFirst) {
    var _this = this;
    // handlerCss.call(_this, _this.enterOptions.htmlContent)
    setTimeout(function() {
      if(typeof noFirst !== 'boolean' || !noFirst) {
        if(window.initHtmlScript) {
          window.initHtmlScript(_this.ele)
        }
        if(querySelector.call(_this, '.t-pg')) {
          querySelector.call(_this, '.t-pg').style.display = 'block';
        }
      }
      if(!isTrace) {
        querySelectorAll.call(_this, '.rt-sr-ck, .rt-sr-r').forEach(function (item) {
          // change不兼容IE8
          addListenerHandler(item, 'click', function (e) {
            // item.onchange = function (ev) {
            var ev = e || window.event;
            var target = ev.target || ev.srcElement;
            var type = getWidgetType(target);
            var parentId = target.id || findWidgetRoot(target).id;
            _this.onWidgetChange(type, ev);
            if (target.checked) {
              setChildrenEnabled.call(_this, item);
            }
            if ((type === 'checkbox') && !target.checked) {
              setChildrenDisabled.call(_this, target);
            }
            if (type === 'radio') {
              querySelectorAll.call(_this, '[name="' + target.name + '"]').forEach(function (radioItem) {
                if(radioItem.getAttribute('id') === parentId) {
                  return;
                }
                setChildrenDisabled.call(_this, radioItem);
              });
            }
          });
          // }
        });
        querySelectorAll.call(_this, '.rt-sr-s').forEach(function (item) {
          addListenerHandler(item, 'change', function (e) {
            var ev = e || window.event;
            _this.onWidgetChange('select', ev);
          });
          // item.onchange = function (ev) {
          //   _this.onWidgetChange('select', ev);
          // }
  
        });
      }
      
      setValByCode.call(_this);
      initChildDisabled.call(_this);
      if(isTrace) {
        // 添加痕迹标识
        var widget = querySelectorAll('.rt-sr-w', _this.ele);
        nodeListToArray(widget).forEach(function (node, i) {
          var parentNode = findWidgetRoot(node);
          var id = node.id || parentNode.id;
          var targetNode = node;
          addTraceFlagInDom(_this.idAndDomMap[id], targetNode);
        })
        addTraceHandler('.traceStatus', _this.idAndDomMap);
      }
    }, _this.enterOptions.oldDataText ? 0 : 100)
  }

  // 赋值公共节点的值,对应字段名取值
  function setValByCode(noValiVal) {
    var _this = this;
    if(!_this.enterOptions || !_this.enterOptions.publicInfo) {
      return;
    }
    querySelectorAll.call(_this, '[code]').forEach(function (item, i){
      var id = item.id || findWidgetRoot(item).id;
      var value = _this.idAndDomMap[id].value;
      if(!noValiVal && value) {
        return;
      }
      var type = getWidgetType(item);
      if('checkbox' === type) {
        return;
      }
      var code = item.getAttribute('code');
      if(publicCode[code]) {
        var widget = item;
        var codeName = publicCode[code].split(',');
        for(var i = 0; i < codeName.length; i++) {
          var resVal = _this.enterOptions.publicInfo[codeName[i]]
          if(typeof resVal === 'string') {
            var codeValMap = {
              '4': {'0': '未婚', '1': '已婚'},
              '26': {'0': '未生育', '1': '已生育', '2': '未知'},
              '31': {'0': '未月经', '1': '正常月经', '2': '已绝经', '3': '未知'}
            }
            if(codeValMap[code]) {
              resVal = codeValMap[code][resVal] || ''
            }
            if(resVal) {
              if('radio' === type && item.getAttribute('value') !== resVal) {
                continue;
              }
              var formatVal = widget.getAttribute('val-format');
              if(formatVal) {
                resVal = dayjs(resVal).format(formatVal);
              }
              setFormItemValue.call(_this, widget, resVal);
              break;
            }
          }
        }
      }
    })
  }

  // 将单选和复选下的项置为disabled
  function initChildDisabled(dom) {
    var _this = this;
    var domList = !dom ? querySelectorAll.call(_this, '.rt-sr-ck, .rt-sr-r') : 
      querySelectorAll.call(_this, '.rt-sr-ck, .rt-sr-r', dom);
    domList.forEach(function (item, i) {
      var type = getWidgetType(item);
      var parentId = item.id || findWidgetRoot(item).id;
      if (item.checked) {
        setChildrenEnabled.call(_this, item);
      }
      if (type === 'checkbox' && !item.checked) {
        setChildrenDisabled.call(_this, item);
      }
      if (type === 'radio') {
        var radioVal = getCheckedSelector(querySelectorAll.call(_this, '[name="' + item.name + '"]')) ? 
          getCheckedSelector(querySelectorAll.call(_this, '[name="' + item.name + '"]')).value : '';
        querySelectorAll.call(_this, '[name="' + item.name + '"]').forEach(function (radioItem) {
          if(radioItem.getAttribute('id') === parentId) {
            return;
          }
          if(radioVal !== radioItem.value) {
            setChildrenDisabled.call(_this, radioItem);
          }
        });
      }
    });
  }

  function setWidgetInvalidStyle(widget) {
    var _this = this;
    widget.className += ' rt-sr-inv';
  }

  function isWidgetValid(widget) {
    var _this = this;
    var widgetType = getWidgetType(widget);
    if (widgetType === 'text' && widget.value.trim() === '') {
      return false;
    }
    // if (widgetType === 'checkbox' && !widget.checked) {
    //   return false;
    // }
    if (widgetType === 'radio' || widgetType === 'checkbox') {
      var radioGroupName = widget.name;
      if(!radioGroupName && !widget.checked) {
        return false;
      }
      var radioGroup = getCheckedSelector(removeEleBySelector(querySelectorAll.call(_this, '[name="'+radioGroupName+'"]')));
      return radioGroup;
    }
    if (widgetType === 'select' && widget.value.trim() === '') {
      return false;
    }
    return true;
  }

  function setInvalidError(widget) {
    var _this = this;
    setWidgetInvalidStyle.call(_this, widget);
    var widgetId = widget.id || findWidgetRoot(widget).id;
    var errorMsg = '控件' + (widgetId) + '为必填项';
    _this.errorMsg = errorMsg;
    if($wfMessage) {
      var name = _this.idAndDomMap[widgetId] && _this.idAndDomMap[widgetId].name ? 
        _this.idAndDomMap[widgetId].name.replace('label', '') : '';
      $wfMessage({
        // content: '请填写必填项'
        content: !_this.enterOptions.oldDataText && name ? ('【' + name + '】不能为空') : '请填写必填项'
      });
    }
    if(window.errorCallBack) {
      window.errorCallBack(widgetId);
    }
    throw new Error(errorMsg);
  }

  // 当父元素为必填时校验子元素填写是否符合要求
  function checkChildrenValid(widget, root) {
    var _this = this;
    var children = removeEleBySelector(querySelectorAll.call(_this, '[parent-id="' + (widget.id || findWidgetRoot(widget).id) + '"]'));
    var requiredChildren = removeEleBySelector(querySelectorAll.call(_this, '[parent-id="' + (widget.id || findWidgetRoot(widget).id) + '"][rt-req]'));
    if (children.length > 0) {
      if (requiredChildren.length > 0) {
        requiredChildren.forEach(function (child) {
          if(child.className.indexOf('rt-sr-r') && !child.checked) {
            return;
          }
          checkChildrenValid.call(_this, child, child);
        });
      } else {
        var results = [];
        children.forEach(function (child) {
          if(isWidgetValid.call(_this, child)) {
            results.push(child);
          }
        })
        if (results.length > 0) {
          // results.forEach(function (res) {
          //   checkChildrenValid.call(_this, res, res);
          // });
        } else {
          setInvalidError.call(_this, root);
        }
      }
    } else {
      if (!isWidgetValid.call(_this, widget)) {
        setInvalidError.call(_this, root);
      }
    }
  }

  function checkValid(widget) {
    var _this = this;
    if(widget.className.indexOf('rt-hide') > -1) {
      return;
    }
    querySelectorAll.call(_this, '.rt-sr-inv').forEach(function (widget) {
      widget.className = widget.className.replace('rt-sr-inv', '');
    });
    checkChildrenValid.call(_this, widget, widget);
  }

  // 当填了父元素，且本身为必填项时校验
  function checkSelfValid(widget) {
    var _this = this;
    if(widget.className.indexOf('rt-hide') > -1) {
      return;
    }
    querySelectorAll.call(_this, '.rt-sr-inv').forEach(function (widget) {
      widget.className = widget.className.replace('rt-sr-inv', '');
    });
    var parentId = widget.getAttribute("parent-id");
    var parentWidgetRequired = querySelector.call(_this, '[id="' + parentId + '"][rt-req]');
    if(parentWidgetRequired && parentWidgetRequired.length > 0) {
      return;
    }
    var parentWidget = querySelector.call(_this, '[id="' + parentId + '"]');
    if(['radio', 'checkbox'].indexOf(parentWidget.type) > -1) {
      // if (isWidgetValid.call(_this, parentWidget)) {
      //   var parentValue = parentWidget.value;
      //   var widgetValue = getCheckedSelector(querySelectorAll.call(_this, '[name="' + parentWidget.name + '"]')) ? 
      //     getCheckedSelector(querySelectorAll.call(_this, '[name="' + parentWidget.name + '"]')).value : '';
      //   if(parentValue === widgetValue && !isWidgetValid.call(_this, widget)) {
      //     setInvalidError.call(_this, widget);
      //   }
      // }
      if(isWidgetValid.call(_this, parentWidget) && parentWidget.checked && !isWidgetValid.call(_this, widget)) {
        setInvalidError.call(_this, widget);
      }
    } else {
      if (isWidgetValid.call(_this, parentWidget)) {
        var widgetType = getWidgetType(widget);
        if (widgetType === 'title') {
          checkValid.call(_this, widget);
        } else {
          if(!isWidgetValid.call(_this, widget)) {
            setInvalidError.call(_this, widget);
          }
        }
      }
    }
  }

  function findWidgetRoot(node) {
    if(!node){
      return {};
    }
    var parentNode = node.parentNode;
    if (parentNode && parentNode.className && parentNode.className.indexOf("w-con") > -1) {
      return parentNode;
    } else {
      return findWidgetRoot(parentNode);
    }
  }

  // 导出结果集-保存用
  function exportStructureData(toRedis) {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    _this.errorMsg = '';
    if(!toRedis) {
      // querySelectorAll.call(_this, '[rt-req]:not([parent-id])').forEach(function (widget) {
      querySelectorAll.call(_this, '[rt-req]').forEach(function (widget) {
        if(widget.getAttribute('parent-id')) {
          return;
        }
        checkValid.call(_this, widget);
      });
      querySelectorAll.call(_this, '[rt-req][parent-id]').forEach(function (widget) {
        checkSelfValid.call(_this, widget);
      });
    }
    var parentList = querySelectorAll.call(_this, '.rt-sr-w');
    for(var i = 0; i < parentList.length; i++) {
      var wItem = parentList[i];
      if(wItem.getAttribute('parent-id') || wItem.classList.contains('rt-hide')) {
        parentList.splice(i, 1);
        i--;
      }
    }
    function collectChildren(parentList) {
      var pList = [];
      parentList.forEach(function (parent) {
        if(parent.classList.contains('rt-hide')) {
          return;
        }
        var parentItem = {
          id: parent.id || findWidgetRoot(parent).id,
          pid: parent.getAttribute('parent-id') || ''
        }
        var widgetType = getWidgetType(parent);
        if (['checkbox', 'radio'].indexOf(widgetType) > -1) {
          if (!parent.checked) {
            return;
          }
          parentItem.val = querySelector.call(_this, '[for="' + parentItem.id + '"] .rt-sr-w').value || 
            querySelector.call(_this, '[for="' + parentItem.id + '"] .rt-sr-lb').innerText;
            parentItem.desc = idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].desc : '';
            parentItem.name = idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].name : '';
            parentItem.code = idAndDomMap[parentItem.id] ? idAndDomMap[parentItem.id].code : '';
          pList.push(parentItem);
          // var children = querySelectorAll.call(_this, '[parent-id="' + parentItem.id + '"]:not(.rt-hide)');
          var children = removeEleBySelector(querySelectorAll.call(_this, '[parent-id="' + parentItem.id + '"]'));
          if (children.length > 0) {
            parentItem.child = collectChildren.call(_this, children);
          }
        } else {
          parentItem.val = widgetType === 'title' ? parent.innerHTML : parent.value;
          parentItem.desc = widgetType === 'title' ? 
            (idAndDomMap[parentItem.id].desc || parentItem.val) : (idAndDomMap[parentItem.id].desc || '');
          parentItem.name = idAndDomMap[parentItem.id].name || '';
          parentItem.code = idAndDomMap[parentItem.id].code || '';
          // var children = querySelectorAll.call(_this, '[parent-id="' + parentItem.id + '"]:not(.rt-hide)');
          var children = removeEleBySelector(querySelectorAll.call(_this, '[parent-id="' + parentItem.id + '"]'));
          if (children.length > 0) {
            parentItem.child = collectChildren.call(_this, children);
          }
          if(parentItem.val && (!parentItem.child || parentItem.child.length > 0 || (widgetType !== 0 && !parentItem.child.length))) {
            pList.push(parentItem);
          }
        }
      });
      return pList;
    }
    var attrData = collectChildren.call(_this, parentList);
    var resultData = sortDataByTopHandler.call(_this, attrData);
    // saveSrResult(resultData);
    return resultData;
  }

  // 结果集排序
  function sortDataByTopHandler(attrData) {
    var _this = this;
    getWidgetAttr.call(_this, _this.ele.innerHTML);
    var widgetProp = _this.widgetPropMap;
    var orderPropMap = [];
    widgetProp.forEach(function(d){
      orderPropMap.push(d.id);
    })
    var sortHandler = function(data) {
      data.sort(function(a,b){
        return orderPropMap.indexOf(a.id) - orderPropMap.indexOf(b.id);
      });
    }
    sortHandler(attrData);
    attrData.forEach(function(item){
      if(item.child && item.child.length) {
        sortHandler(item.child);
      }
    })
    return attrData;
  }

  /**      回显部分            */

  // 列举id值和控件的关系属性表
  function idAndDomMapHandler(htmlContent, resultData) {
    var _this = this;
    _this.idAndDomMap = {};
    getWidgetAttr.call(_this, htmlContent);
    var widgetPropData = JSON.parse(JSON.stringify(_this.widgetPropMap));
    var idAndDomMap = {};
    var lastValMap = {};
    var handler = function (data, lastChild, parentLastVal) {
      var attrValue = {};
      var delIndex = -1;
      var lastChildValList = null;
      data.forEach(function (item, i) {
        var getItemIdByCode = '';
        for(var j = 0; j < widgetPropData.length; j++) {
          var widget = widgetPropData[j];
          if(widget.id === item.id || (!item.id && item.code && widget.code === item.code)) {
            attrValue = widget;
            delIndex = j;
            getItemIdByCode = widget.id;
            break;
          }
        }
        if(delIndex > -1) {
          widgetPropData.splice(delIndex, 1);
          delIndex = -1;
        }
        if(item.id || getItemIdByCode) {
          idAndDomMap[item.id || getItemIdByCode] = objMerge(attrValue, {value: item.val, lastVal: item.lastVal});
          // var lastVal = item.lastVal === undefined ? parentLastVal : item.lastVal;
          var lastVal = item.lastVal;
          var lastValArr = null;
          if(attrValue.isItemList) {
            delete idAndDomMap[item.id || getItemIdByCode].style;
            if(lastChild && lastChild.length && !lastVal) {
              var tempItems = JSON.parse(JSON.stringify(attrValue.itemList));
              lastChild.forEach(function(lVal) {
                tempItems.forEach(function(itemCld, lIdx) {
                  if(lVal.id === itemCld.id && lVal.val) {
                    lastValArr ? lastValArr.push(lVal.val) : lastValArr = [lVal.val];
                    tempItems.splice(lIdx, 1);
                    lastValMap[item.id] ? lastValMap[item.id].push(lVal.id) : lastValMap[item.id] = [lVal.id];
                    return;
                  }
                })
              })
            }
          }
          if(attrValue.wt === 5 && lastValArr && lastValArr.length === 1 && lastValArr[0] === item.val) {
            lastValArr = null;
          }
          if(lastValArr || lastVal !== undefined) {
            if(lastValArr) {
              idAndDomMap[item.id || getItemIdByCode]['lastVal'] = lastValArr.join('、') || '-';
            } else {
              idAndDomMap[item.id || getItemIdByCode]['lastVal'] = lastVal || '-';
            }
          }
          
          if(item.child) {
            handler(item.child, item.lastChild, item.lastVal);
          }
        }
      });
    }
    handler(resultData);
    if(widgetPropData && widgetPropData.length) {
      widgetPropData.forEach(function(wProp) {
        if(idAndDomMap[wProp.id] || !wProp.id) {
          return;
        }
        idAndDomMap[wProp.id] = objMerge(wProp, {value:''});
        if(wProp.isItemList) {
          delete idAndDomMap[wProp.id].style;
        }
      })
    }

    for(var key in lastValMap) {
      if(!idAndDomMap[key].lastVal) {
        continue;
      }
      var arr = lastValMap[key];
      var itemList = idAndDomMap[key].itemList;
      var itemCheckArr = JSON.parse(JSON.stringify(arr));
      var itemCheckLen = 0;
      for(var i = 0; i < itemList.length; i++) {
        if(idAndDomMap[itemList[i].id] && idAndDomMap[itemList[i].id].value) {
          var index = itemCheckArr.indexOf(itemList[i].id);
          itemCheckLen++;
          if(index > -1) {
            itemCheckArr.splice(index, 1);
          }
        }
      }
      if(itemCheckLen===arr.length && !itemCheckArr.length) {
        delete idAndDomMap[key].lastVal;
      }
    }
    _this.idAndDomMap = idAndDomMap;
  }

  // 创建原报告内容
  function createContainer(htmlContent, options) {
    var container = document.createElement('div');
    htmlContent = htmlContent.replace(/<script.*?<\/script>/ig, '');
    htmlContent = htmlContent.replace(/<link.*?>/ig, '');
    container.innerHTML = htmlContent;
    container.style.display = "none";
    if(container.querySelector('.t-pg')) {
      container.querySelector('.t-pg').style.display = "none";
    }
    if(options.type === 'view') {
      nodeListToArray(container.querySelectorAll('.w-isk')).forEach(function (ele) {
        if(ele.remove) {
          ele.remove();
        } else {
          ele.removeNode(true);
        }
      })
    }
    return container;
  }
  
  // 回显报告内容进行编辑
  // htmlContent, resultData, srcUrl, examInfo, type, loadViewScript
  function rebuildStructureReport(options) {
    var _this = this;
    if(scriptCount < innerScriptArr.length) {
      // 判断额外js是否加载完成
      setTimeout(function() {
        rebuildStructureReport.call(_this, options)
      }, 100)
      return;
    }
    var options = options || {};
    var htmlContent = options.htmlContent || '';
    var resultData = options.resultData || [];
    var srcUrl = options.srcUrl || preURL || '';
    var examInfo = options.examInfo || {};
    var type = options.type || '';
    var loadViewScript = options.loadViewScript || false;
    _this.enterOptions = options;
    _this.examInfo = examInfo;
    _this.srcUrl = srcUrl;
    var container = createContainer(htmlContent, options);
    _this.ele && (_this.ele.innerHTML = container.innerHTML);
    if(container.remove) {
      container.remove();
    } else {
      container.removeNode(true);
    }
    idAndDomMapHandler.call(_this, htmlContent, resultData);
    var idAndDomMap = _this.idAndDomMap;
    var widget = querySelectorAll('.rt-sr-w', _this.ele);
    querySelector('.t-pg', _this.ele).setAttribute('isView', type === 'view');
    var setValInParent = [];   //子节点有值的父节点
    nodeListToArray(widget).forEach(function (node, i) {
      var parentNode = findWidgetRoot(node);
      var id = node.id || parentNode.id;
      var targetNode = node;
      if (idAndDomMap[id] && idAndDomMap[id].value) {
        // 赋值
        setFormItemValue.call(_this, targetNode, idAndDomMap[id].value);
        targetNode.removeAttribute('disabled');
        if(idAndDomMap[id].code) {
          targetNode.setAttribute('code', idAndDomMap[id].code);
        }
        var childWidget = querySelectorAll('[parent-id="'+id+'"]', _this.ele);
        nodeListToArray(childWidget).forEach(function (child) {
          child.removeAttribute('disabled');
        })
        if(idAndDomMap[id].pid && !idAndDomMap[idAndDomMap[id].pid].pid) {
          setValInParent.push(idAndDomMap[id].pid)
        }
      } else {
        if(idAndDomMap[id] && idAndDomMap[id].code) {
          targetNode.setAttribute('code', idAndDomMap[id].code);
        }

        /**
         * 当options.setNull=true时，未填写的内容显示‘无’
         * 是否有父节点，有的才做处理,或者没有父节点，但是以文本格式预览的节点
         * 单选项先判断其他项是否有值
         */
        if(idAndDomMap[id] && options.type === 'view' && options.setNull && setValInParent.indexOf(idAndDomMap[id].pid) === -1
          && ((idAndDomMap[id].pid && !idAndDomMap[idAndDomMap[id].pid].pid ) || (!idAndDomMap[id].pid && !idAndDomMap[id].pvf))
          && (idAndDomMap[id].wt !== 5 || !findSiblingVal(idAndDomMap, id))) {
          delete idAndDomMap[id].pvt;
          idAndDomMap[id].value = '无'
          idAndDomMap[id].tempValue = true;
          setValInParent.push(idAndDomMap[id].pid || idAndDomMap[id].id)
        }
      }
    })
    handlerScript.call(_this, _this.enterOptions.htmlContent, type, loadViewScript)
  }

  // 找出单选项中是否有选中的
  function findSiblingVal(idAndDomMap, id) {
    var bool = false;
    var itemList = idAndDomMap[id].itemList;
    if(itemList && itemList.length) {
      for(var i = 0; i < itemList.length; i++) {
        if(itemList[i] && idAndDomMap[itemList[i].id] && idAndDomMap[itemList[i].id].value) {
          bool = true;
          break;
        }
      }
    }
    return bool;
  }
  // 动态加载script，确保加载完成
  function handlerScript(content, type, loadViewScript) {
    var _this = this;
    querySelectorAll('[data-flag="curPattern"]', document).forEach(function(lastNode) {
      if(lastNode.remove) {
        lastNode.remove();
      } else {
        lastNode.removeNode(true);
      }
    })
    handlerCss.call(_this, _this.enterOptions.htmlContent)
    var scriptReg = /<script.*?<\/script>/ig;
    var scriptList = content.match(scriptReg);
    var resHtml = _this.ele.innerHTML;
    var len = 0;
    var headContent = document.querySelector('head').innerHTML;
    headContent = headContent.replace(/\/sreport/ig, '');
    for(var i = 0; i < scriptList.length; i++) {
      var srcReg1 = /(src\=\").*(?=\")/ig;  //IE不兼容?<=
      var url = scriptList[i].match(srcReg1);
      if(url && url.length && headContent.indexOf(url[0]) > -1) {
        scriptList.splice(i, 1);
        i--;
      }
    }
    if(scriptList && scriptList.length) {
      var handler = function(sItem, html) {
        // var srcReg = /(?<=src\=\").*(?=\")/ig;
        var srcReg = /(src\=\").*(?=\")/ig;  //IE不兼容?<=
        var urls = sItem.match(srcReg);
        if(urls && urls.length) {
          var url = urls[0].replace('src="', '');
          var script = document.createElement('script');
          if(url.indexOf('layui.js') === -1) {
            script.setAttribute('data-flag', 'curPattern');
          }
          url = url.replace(/\S*template-lib/ig, '/template-lib')
          // url = url.replace(/[.]+\//g, '/');
          script.src = _this.srcUrl + url + '?v=' + new Date().getTime();
          resHtml = html.replace(sItem, '');
          _this.ele.innerHTML = resHtml;
          document.getElementsByTagName('head')[0].appendChild(script);
          script.onload = script.onreadystatechange = function() {
            if(!this.readyState || this.readyState=='loaded' || this.readyState=='loading' || this.readyState=='complete') {  
              len++;
              if(len < scriptList.length) {
                handler(scriptList[len], resHtml);
              } else {
                if(type === 'view') {
                  if(loadViewScript) {
                    init.call(_this);
                  }
                  // 确保加载顺序加延时
                  setTimeout(function(){
                    loadScriptToShowText.call(_this);
                  }, 50)
                } else {
                  init.call(_this);
                }
              }
            }
            script.onload = script.onreadystatechange = null;
          }
        } else {
          len++;
          if(len < scriptList.length) {
            handler(scriptList[len], resHtml);
          } else {
            if(type === 'view') {
              if(loadViewScript) {
                init.call(_this);
              }
              setTimeout(function(){
                loadScriptToShowText.call(_this);
              }, 50)
            } else {
              init.call(_this);
            }
          }
        }
      }
      handler(scriptList[len], resHtml);
    } else {
      if(type === 'view') {
        if(loadViewScript) {
          init.call(_this);
        }
        loadScriptToShowText.call(_this);
      } else {
        init.call(_this);
      }
    }
  }

  // 动态加载css,兼容IE8
  function handlerCss(content) {
    var _this = this;
    var cssReg = /rel="stylesheet".*?\/?>/ig;
    var cssList = content.match(cssReg);
    var resHtml = content;
    var headContent = document.querySelector('head').innerHTML;
    headContent = headContent.replace(/\/sreport/ig, '');
    for(var i = 0; i < cssList.length; i++) {
      var hrefReg1 = /(href\=\").*(?=\")/ig;  //IE不兼容?<=
      var url = cssList[i].match(hrefReg1);
      if(url && url.length && headContent.indexOf(url[0]) > -1) {
        cssList.splice(i, 1);
        i--;
      }
    }
    if(cssList && cssList.length) {
      for(var i = 0; i < cssList.length; i++) {
        var hrefReg = /(href\=\").*(?=\")/ig;
        var urls = cssList[i].match(hrefReg);
        if(urls && urls.length) {
          var url = urls[0].replace('href="', '');
          var htmlLink = querySelector('[href="'+url+'"]', document);
          if(htmlLink) {
            if(htmlLink.remove) {
              htmlLink.remove();
            } else {
              htmlLink.removeNode(true);
            }
          }
          var link = document.createElement('link');
          link.setAttribute('data-flag', 'curPattern');
          link.rel = "stylesheet";
          url = url.replace(/\S*template-lib/ig, '/template-lib')
          // url = url.replace(/[.]+\//g, '/');
          link.href = _this.srcUrl + url + '?v=' + new Date().getTime();
          document.getElementsByTagName('head')[0].appendChild(link);
        }
      }
    }
  }

  /**
   * 预览报告
   * 预览规则：
   * 1.只展示已填写值得控件节点
   * 2.pvf预览格式，1为控件，2为不显示，其他值为文本
   * 3.当pvf!=1，即以文本显示时，如果存在pvt(预览文本内容)，则按pvt格式显示，$rt{AA}
   * 4.不存在pvt(预览文本内容)，则按格式：前缀+val/desc+后缀
   * @param {string} htmlContent html内容
   * @param {Array} resultData 结果集
   * @param {boolean} loadViewScript 是否需要加载相关js
   * @param {string} srcUrl 静态资源的存在位置
   * @param {object} examInfo 检查信息
  */
  //  htmlContent, resultData, srcUrl, examInfo, loadViewScript
  function previewStructureReport(options) {
    var _this = this;
    options = options || {};
    options.type = 'view';
    // 先回显所有内容，再结合显示方式及值处理
    // rebuildStructureReport.call(_this, htmlContent, resultData, srcUrl, examInfo, 'view', loadViewScript);
    rebuildStructureReport.call(_this, options);
  }
  
  function loadScriptToShowText() {
    var _this = this;
    var disabledEle = nodeListToArray(querySelectorAll.call(_this, '[disabled]'));
    disabledEle.forEach(function(ele) {
      ele.removeAttribute('disabled');
    })
    var srWon = nodeListToArray(querySelectorAll.call(_this, '.rt-sr-w'));
    var labelCon = nodeListToArray(querySelectorAll.call(_this, 'label[for]'));
    var eleList = srWon.concat(labelCon);
    eleList.forEach(function(ele) {
      ele.style.pointerEvents = 'none';  //防止编辑
      // 兼容IE8
      ele.onclick = function() {
        return false;
      }
    })
    widgetTransToText.call(_this); //提取文本

    nodeListToArray(querySelectorAll('.view-none', _this.ele)).forEach(function(vItem) {
      vItem.style.display = "none";
    })

    nodeListToArray(querySelectorAll('.hight-block', _this.ele)).forEach(function(hItem) {
      hItem.style.display = "block";
    })

    nodeListToArray(querySelectorAll('.p-item', _this.ele)).forEach(function(pItem) {
      if(pItem.innerText.trim() === '') {
        if(pItem.remove) {
          pItem.remove();
        } else {
          pItem.removeNode(true);
        }
      }
    })

    // 针对特殊的交互操作，sreport会用到，不通用所有项目
    nodeListToArray(querySelectorAll('[contect-id]', _this.ele)).forEach(function(cItem) {
      var cId = cItem.getAttribute('contect-id');
      if(_this.idAndDomMap[cId] && _this.idAndDomMap[cId].value) {
        cItem.style.display = "block";
      }
    })
    querySelector.call(_this, '.t-pg').style.display = 'block';
  }

  // 将不以控件类型预览的转成文本
  function widgetTransToText() {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    //pvf预览格式，1为控件，其他值为文本
    var textFlag = false;  //是否有文本格式
    for(var id in idAndDomMap) {
      var curItem = idAndDomMap[id];
      // if(curItem.pvf === '2' ||
      //   (curItem.pvf === '1' && curItem.pid && (!idAndDomMap[curItem.pid].value || idAndDomMap[curItem.pid].pvf === '2'))) {
      if(curItem.pvf === '2' ||
        (curItem.pvf === '1' && curItem.pid && idAndDomMap[curItem.pid].pvf === '2')) {
      // pvf='2'则移除节点
        var wConNode = querySelector('.w-con[id="'+id+'"]', _this.ele)
        if(wConNode) {
          if(wConNode.remove) {
            wConNode.remove();
          } else {
            wConNode.removeNode(true);
          }
        }
      }
      if(curItem.pvf === '1' || curItem.isItemList) {
        continue;
      }
      // 找到id的节点，处理已赋值的
      var node = querySelector('.w-con[id="'+id+'"]', _this.ele);
      if(node) {
        var str = '';
        if(curItem.wt !== 2 && curItem.itemList && curItem.itemList.length) {
          var strArr = [];
          var willDelIndex = -1;
          curItem.itemList.forEach(function(item, sI) {
            if(item && idAndDomMap[item.id] && idAndDomMap[item.id].value) {
              var tempStr = '';
              if(!idAndDomMap[item.id].pvt) {
                var text = idAndDomMap[item.id].text || '';
                var value = idAndDomMap[item.id].value || '';
                var pT = idAndDomMap[item.id].pT || '';
                var sT = idAndDomMap[item.id].sT || '';
                tempStr = pT + (text || value) + sT;
                if(value === '无' && idAndDomMap[item.id].tempValue) {
                  willDelIndex = sI;
                }
                // 是否包含子节点
                var curItemNode = querySelector('[id="'+item.id+'"]', _this.ele);
                if(curItemNode) {
                  var innerChild = querySelectorAll('.w-con[pid="'+item.id+'"]', curItemNode.parentNode);
                  var innerContent = [];
                  innerChild.forEach(function(innerItem) {
                    var innerId = innerItem.id;
                    if(idAndDomMap[innerId] && idAndDomMap[innerId].value) {
                      innerContent.push(idAndDomMap[innerId].value);
                    }
                  })
                  if(innerContent.length) {
                    tempStr += '：' + innerContent.join(',');
                  }
                }
              } else {
                var formatPvt = formatPvtHandler(idAndDomMap[item.id].pvt, idAndDomMap, item.id);
                tempStr = formatPvt;
              }
              if(tempStr) {
                strArr.push(tempStr);
              }
            }
          })
          if(strArr.length > 1 && willDelIndex > -1) {
            strArr.splice(willDelIndex, 1);
          }
          str += strArr.join('；');
        } else {
          if(curItem.value) {
            if(['6', '7'].indexOf(curItem.pvt) === -1) {
              if(!curItem.pvt) {
                var text = curItem.text || '';
                var value = curItem.value || '';
                var pT = curItem.pT || '';
                var sT = curItem.sT || '';
                
                str += pT + (text || value) + sT;
              } else {
                var formatPvt = formatPvtHandler(curItem.pvt, idAndDomMap, curItem.id);
                str += formatPvt;
              }
              if(curItem.wt === 4) {
                str += '；';
              }
            }
          }
        }
        str = str.replace(/；；/g, '；');
        if(str) {
          textFlag = true;
          node.classList.add('text-con');
          if(curItem.wt) {
            if(node.clientWidth && node.clientWidth > 0) {
              if(curItem.wt === 1) {
                node.style.minWidth = node.clientWidth + 'px';
              }
              node.style.maxWidth = node.clientWidth + 'px';
            }
            node.style.width = 'auto';
            node.style.height = 'auto';
            node.style.wordBreak = 'break-all';
            node.style.whiteSpace = 'pre-wrap';
            node.style.verticalAlign = 'bottom';
          }
          node.innerHTML = str;
          // if(curItem.wt === 1) {
          //   node.style.alignItems = 'flex-start';
          //   node.style.whiteSpace = 'pre-wrap';
          //   node.style.wordBreak = 'break-all';
          //   node.style.display = 'inline-block';  //兼容IE10，不换行问题，原样式为flex
          // }
        } else {
          if(node.remove) {
            node.remove();
          } else {
            node.removeNode(true);
          }
        }
      }
    }
    if(textFlag) {
      alignLeftHandler.call(_this);
      // 调整top
      resizeTopHandler.call(_this, idAndDomMap);
    }

  }

  // 处理预览文本格式的内容
  function formatPvtHandler(pvt, idAndDomMap, curId) {
    var keyReg = /\$rt\{.*?\}/ig;  //匹配$rt{AA}
    // var valReg = /(?<=\$rt\{).*?(?=\})/ig;  //匹配$rt{AA}中间的id->AA IE不兼容?<=
    var valReg = /\$rt\{.*?(?=\})/ig;  //匹配$rt{AA}中间的id->$rt{AA，兼容IE再做处理
    var keyList = pvt.match(keyReg);
    var valList = pvt.match(valReg);
    if(!keyList || keyList.length === 0) {
      return pvt;
    }
    keyList.forEach(function(keyStr, index) {
      var id = valList[index].replace('$rt{', '');
      if(id === 'this') {
        if((!idAndDomMap[curId] || !idAndDomMap[curId].value) && keyList.length === 1) {
          pvt = '';
        } else {
          pvt = pvt.replace(keyStr, idAndDomMap[curId] && idAndDomMap[curId].value ? idAndDomMap[curId].value : '');
        }
      } else {
        if(idAndDomMap[id] && idAndDomMap[curId] && !idAndDomMap[curId].value) {
          if(idAndDomMap[id].groupId !== idAndDomMap[curId].groupId) {
            pvt = pvt.replace(keyStr, idAndDomMap[id] && idAndDomMap[id].value ? idAndDomMap[id].value : '');
          } else {
            pvt = pvt.replace(keyStr, '');
          }
        } else {
          pvt = pvt.replace(keyStr, idAndDomMap[id] && idAndDomMap[id].value ? idAndDomMap[id].value : '');
        } 
       
      }
    })
    return pvt;
  }

  // 向父元素靠齐
  function alignLeftHandler() {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    var textConList = nodeListToArray(querySelectorAll('.text-con', _this.ele));
    var handler = function(node) {
      if(!node.classList.contains('text-con')) {
        return;
      }
      var pid = node.getAttribute('pid');
      if(pid) {
        // 找到同一级最近的那一个
        var siblingProp = findSiblingIdFun.call(_this, pid, node.id);
        var left = 0;
        var top = 0;
        var siblingNode = null;
        var nodeTop = idAndDomMap[node.id].top;
        var nodeLeft = idAndDomMap[node.id].left;
        var lastLevelNodeTop = 0;
        if(siblingProp) {
          var sibId = siblingProp.id;
          // 单选框 复选框
          if(idAndDomMap[sibId].wt === 5 || idAndDomMap[sibId].wt === 4) {
            sibId = idAndDomMap[sibId].groupId;
          }
          siblingNode = querySelector('.w-con[id="'+siblingProp.id+'"]', _this.ele);
          
          if(siblingNode) {
            if(siblingNode.classList.contains('text-con')) {
              handler(siblingNode);
            }
            var siblingNodeLeft = idAndDomMap[siblingNode.id].left;
            left = Number(siblingNodeLeft) + Number(siblingNode.offsetWidth) + 5;

            var siblingNodeTop = idAndDomMap[siblingNode.id].top;
            lastLevelNodeTop = siblingNodeTop;
            top = Number(siblingNodeTop) + Number(siblingNode.offsetHeight) + 10;
          }
        }
        if(!siblingNode) {
          // 单选框 复选框
          if(idAndDomMap[pid].wt === 5 || idAndDomMap[pid].wt === 4) {
            pid = idAndDomMap[pid].groupId;
          }
          
          var parentNode = querySelector('.w-con[id="'+pid+'"]', _this.ele);
          if(parentNode) {
            if(parentNode.classList.contains('text-con')) {
              handler(parentNode);
            }
            var parentNodeLeft = idAndDomMap[parentNode.id].left;
            if(nodeLeft - parentNodeLeft >= Number(parentNode.offsetWidth)) {
              left = Number(parentNodeLeft) + Number(parentNode.offsetWidth) + 5;
              var parentNodeTop = idAndDomMap[parentNode.id].top;
              lastLevelNodeTop = parentNodeTop;
              top = Number(parentNodeTop) + 5;
            }
          }
        }
        
        if(top > 0) {
          if(nodeTop - lastLevelNodeTop > 15) {
            left = 0;  //不在同样的Y上则不处理x
            // 5-10之间的差值不进行调整
            if(top < Number(nodeTop) - 5 || top > Number(nodeTop) + 10 ) {
              node.style.top = top + 'px';
              idAndDomMap[node.id].top = top;
            }
          }
        }
        if(left > 0) {
          node.style.left = left + 'px';
          idAndDomMap[node.id].left = left;
          node.classList.remove('text-con');
        }
      }
    }
    textConList.forEach(function(node) {
      handler(node);
    })
  }

  // 找到同一级兄弟最近的那一个，用于调整xy坐标
  function findSiblingIdFun(pid, selfId) {
    var _this = this;
    var idAndDomMap = _this.idAndDomMap;
    var selfLeft = idAndDomMap[selfId].left;
    var selfTop = idAndDomMap[selfId].top;
    var siblingIds = [];
    for(var key in idAndDomMap) {
      var curItem = idAndDomMap[key];
      var bool = false;
      if(curItem.pvf !== '1' && curItem && curItem.itemList) {
        curItem.itemList.forEach(function(item) {
          // if(idAndDomMap[item.id].value) {
          if(item && item.id && idAndDomMap[item.id]) {
            bool = true;
            return;
          }
        })
      }
      if(key === selfId || curItem.pid !== pid || curItem.isItemList || 
        (curItem.pvf !== '1' && !bool)) {
        // (curItem.pvf !== '1' && !curItem.value && !bool)) {
        continue;
      }
      // 位于左边且上方的才进行对比
      if(selfLeft - curItem.left >= -10 && selfTop - curItem.top >= -10) {
        siblingIds.push({
          id: curItem.id,
          left: curItem.left,
          top: curItem.top,
          leftDiff: selfLeft - curItem.left,
        })
      }
    }
    if(siblingIds && siblingIds.length) {
      // 排序取出最近的兄弟元素
      siblingIds = siblingIds.sort(function(a, b) {
        if(a.leftDiff - b.leftDiff >= -10) {
          return b.top - a.top;
        } else {
          return a.leftDiff - b.leftDiff;
        }
      })
      return siblingIds[0];
    }
    return null;
  }

  // 调整元素的top以完整显示超出模板设置的高度的内容
  function resizeTopHandler(idAndDomMap) {
    var _this = this;
    var handler = function(allId, curId) {
      for(var i = 0; i < allId.length; i++) {
        var curNode = querySelector.call(_this, '[id="'+curId+'"]');
        if(!curNode) {
          break;
        }
        var lastNode = querySelector.call(_this, '[id="'+allId[i]+'"]');
        // 找不到上一节点或者是子节点就继续找
        if(!lastNode || !idAndDomMap[curId] || idAndDomMap[curId].pid === allId[i] || !idAndDomMap[allId[i]]) {
          continue;
        }
        if((idAndDomMap[curId].top - idAndDomMap[allId[i]].top <= 15)) {
          continue;
        }
        var lastTopAndH = Number((lastNode.style.top).replace('px', '')) + Number(lastNode.offsetHeight);
        var curTop = Number((curNode.style.top).replace('px', ''));
        if(curTop < lastTopAndH + 10) {
          // 为了尽可能对齐，wt=2或3的情况加15,因为其他类型控件存在5px上内边距
          curNode.style.top = (lastTopAndH + (idAndDomMap[curId].wt === 5 || idAndDomMap[curId].wt === 4 ? 15 : 10)) + 'px';
          break;
        }
      }
    }
    var pageLi = querySelectorAll.call(_this, 'li.page');
    var widgetProp = _this.widgetPropMap;
    nodeListToArray(pageLi).forEach(function (page, index) {
      var temp = [];
      for(var j = 0; j < widgetProp.length; j++) {
        var item = widgetProp[j];
        var id = item.id;
        // 按页处理
        if(item.rtScPageNo === index + 1 && temp.indexOf(id) === -1) {
          if(item.isItemList) {
            if(temp.indexOf(item.groupId) === -1) {
              temp.push(item.groupId);
            }
          } else {
            temp.push(id);
          }
        }
      }
      for(var i = 0; i < temp.length; i++) {
        if(i === 0) {
          continue;
        }
        var siblingIds = temp.slice(0, i).reverse();
        handler(siblingIds, temp[i])
      }
      // 超出当前页面高度，则赋内容高度+15px作为留白
      if(page.offsetHeight < page.scrollHeight) {
        page.style.height = (page.scrollHeight + 15) + 'px';
      }
    })
  }

  // 判断表单类型及赋值
  function setFormItemValue(widget, value) {
    var _this = this;
    var type = getWidgetType(widget);
    if (type === 'text' || (!this.enterOptions.oldDataText && type === 'title')) {
      if(widget.nodeName === 'INPUT') {
        widget.setAttribute('value', value);
      } else {
        widget.innerHTML = value;
        widget.value = value;
      }
    } else if (['radio', 'checkbox'].indexOf(type) > -1) {
      if(type === 'radio' && getCheckedSelector(querySelectorAll.call(_this, '[name="'+widget.name+'"]'))) {
        getCheckedSelector(querySelectorAll.call(_this, '[name="'+widget.name+'"]')).removeAttribute('checked');
      }
      widget.setAttribute('checked', true);
    } else if (type === 'select') {
      if(querySelector('option[value="' + value + '"]', widget)) {
        querySelector('option[value="' + value + '"]', widget).setAttribute('selected', true);
      }
    } else if(['barcode', 'qrcode'].indexOf(type) > -1) {
      if(type === 'barcode' && createBarcode) {
        createBarcode(widget, value, 'A')
      }
      if(type === 'qrcode' && QRCode) {
        var qrcode = new QRCode(widget, {
          text: '',
          width: 100,
          height: 100,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        });
        qrcode.makeCode(value);
      }
    }
  }

  function findCurStructure(ele, instanceList) {
    var curInstance = null;
    for (var i = 0; i < instanceList.length; i++) {
      var instance = instanceList[i];
      if (instance.ele === ele) {
        curInstance = instance;
        break;
      }
    }
    return curInstance;
  }

  function RtStructure(id) {
    var isInstanceExist;
    var ele = document.querySelector(id);
    for (var i = 0; i < instanceList.length; i++) {
      var instance = instanceList[i];
      if (instance.ele === ele) {
        return instance;
      }
    }
    this.ele = ele;
    this.widgetPropMap = null;  //模板中的控件属性集合
    this.idAndDomMap = null;  //id和dom的Json对应关系表
    this.errorMsg = null;  //错误提示文本
    this.description = null;  //报告描述
    this.impression = null;  //报告印象
    this.examInfo = null;  //检查信息
    this.srcUrl = null;  //资源文件存放地址
    this.enterOptions = null;  //调取这个文件的所有入参
    instanceList.push(this);
    window.instanceList = instanceList;
    window.findCurStructure = findCurStructure;
  }

  RtStructure.prototype.onWidgetChange = function () { };
  RtStructure.prototype.setChildrenDisabled = setChildrenDisabled;
  RtStructure.prototype.exportStructureData = exportStructureData;
  RtStructure.prototype.rebuildStructureReport = rebuildStructureReport;
  RtStructure.prototype.previewStructureReport = previewStructureReport;
  RtStructure.prototype.initChildDisabled = initChildDisabled;
  RtStructure.prototype.init = init;
  RtStructure.prototype.setFormItemValue = setFormItemValue;
  RtStructure.prototype.setValByCode = setValByCode;

  window.RtStructure = RtStructure;

  // 增加痕迹标识data-trace
  function addTraceFlagInDom(item, widget) {
    // 纯文本不进行添加，可编辑的内容才存在痕迹
    if(item.wt !== 0 && item.lastVal) {
      if([4,5].indexOf(item.wt) > -1) {
        widget.parentNode.setAttribute('data-trace', item.id)
      } else {
        widget.setAttribute('data-trace', item.id)
      }
    }
  }

  // 添加报告痕迹的交互
  function addTraceHandler(selector, data) {
    if(scriptCount < innerScriptArr.length) {
      // 判断额外js是否加载完成
      setTimeout(function() {
        addTraceHandler(selector, data)
      }, 100)
      return;
    }
    if(!$(selector).length) {
      return;
    }
    // 延时是为了确保模板的内部交互加载完成
    setTimeout(function() {
      // 处理单复选框的禁用切换，不禁用事件
      $(selector + ' input:not([type="text"])').attr("onclick", "return false");
      $(selector).find('label[for]').attr("onclick", "return false");
      $(selector + ' select option').attr('disabled', true);
      $(selector + ' button').attr('disabled', true);
      $(selector + ' textarea').attr('readonly', 'readonly');
      $(selector + ' input[type="text"]').attr('readonly', 'readonly');
      $(selector + ' input').off('change').off('click');
      // 针对element-ui的处理is-disabled
      // $(selector).find('.is-checked').addClass('is-disabled');
      $(selector + ' .el-date-editor input').attr('disabled', true);
  
      var proverDom = null;
      $(selector).find('[data-trace]:not([data-trace="no"])').off('mouseenter').off('mouseleave');
      $(selector).find('[data-trace]:not([data-trace="no"])').on('mouseenter', function(e) {
        var traceKey = $(this).attr('data-trace')
        if(data[traceKey] && data[traceKey].lastVal) {
          proverDom = createProver($(this), data[traceKey].lastVal)
        }
      }).on('mouseleave', function(){
        if(proverDom) {
          proverDom.remove();
        }
      })
    }, 500);
  }

  function createProver(vm, content) {
    var width = vm.outerWidth();
    var height = vm.outerHeight();
    var top = vm.offset().top;
    var left = vm.offset().left;
    var html = '<div id="trace-prover" style="z-index:10;line-height:20px;white-wrap:pre-wrap;border-radius:4px;padding:4px 8px;position:absolute;background:#303133;top:'+top+'px;left:'+left+'px">';
    html += '<div style="font-size:12px;color:#FFF;word-break:break-all;">前一版本：'+content+'</div>';
    html += '<div style="width:0;height:0;position:absolute;top:100%;left:20px;border-width:8px 8px;border-style:solid;border-color:#303133 transparent transparent"></div>';
    html += '</div>';
    var proverDom = $(html);
    $('body').append(proverDom);
    if(width < 220) {
      proverDom.css('max-width', '220px');
    } else {
      proverDom.css('max-width', width+'px');
    }
    var proverHeight = proverDom.outerHeight();
    proverDom.css('top', (top - proverHeight - 8)+'px');
    return proverDom;
  }
  window.addTraceHandler = addTraceHandler;
})();
