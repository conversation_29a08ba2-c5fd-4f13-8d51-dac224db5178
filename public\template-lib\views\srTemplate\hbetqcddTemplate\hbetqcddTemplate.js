$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var examNo = '';
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#hbetqcdd1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      examNo = publicInfo.examNo;
      initPage();
    } else {

    }
    setTimeout(function() {
      // checkOverflow(curElem.find('.ryjc-con')[0]);
      // checkOverflow(curElem.find('.qcjl-con')[0]);
      checkOverflow(curElem.find('#hbetqcdd1 .rpt-body')[0]);
    }, 0)
  }
}

// 页面初始化
function initPage() {
  showCutDetail();
}

// 回显取材明细
function showCutDetail() {
  let list = window.getExamCandleList(examNo, '', 'candle_no') || [];
  if(list.length) {
    let qcjlHtmlStr = '';
    for(let i=0; i<list.length;i++) {
      let index = i + 1;
      let content = `${list[i] ? (index + '.' + (list[i].candleId || '') + ':' +  (list[i].candlePart || '') + (list[i].blockCount ? ' ' + list[i].blockCount : '')): ''}`;
      qcjlHtmlStr += `<div class="lk-item">${content}</div>`;
    }
    $('#hbetqcdd1 .qcjl-con').html(qcjlHtmlStr);
    setTimeout(function() {
      var itemWidth = $('#hbetqcdd1 .qcjl-con').outerWidth() * 0.5;
      $('#hbetqcdd1 .qcjl-con .lk-item:nth-child(odd)').css('width', 'calc(50% - 12px)');
      $('#hbetqcdd1 .qcjl-con .lk-item:nth-child(even)').css('max-width', '50%');
      // 计算偶数列靠右
      var maxWidth = 0;
      $('#hbetqcdd1 .qcjl-con .lk-item:nth-child(even)').each(function() {
        if($(this).outerWidth() > maxWidth) {
          maxWidth = $(this).outerWidth() + 5;
        }
      })
      var marginLeft = itemWidth - maxWidth <= 0 ? 0 : itemWidth - maxWidth;
      $('#hbetqcdd1 .qcjl-con .lk-item:nth-child(even)').css({
        'width': maxWidth + 'px',
        'margin-left': marginLeft + 'px'
      });
    }, 0)
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}