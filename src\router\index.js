import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  // npm cli split
  {
    path: '/ssbbEdit',
    name: 'ssbbEditReport',
    component: () => import(/* webpackChunkName: "ssbbEditReport" */ '../views/ssbbReport/ssbbEditReport'),
    meta: {
      title: '手术标本编辑页',
    }
  },
  {
    path: '/ssbbView',
    name: 'ssbbViewReport',
    component: () => import(/* webpackChunkName: "ssbbViewReport" */ '../views/ssbbReport/ssbbViewReport'),
    meta: {
      title: '手术标本预览页',
    }
  },
  {
    path: '/cjhjEdit',
    name: 'cjhjEditReport',
    component: () => import(/* webpackChunkName: "cjhjEditReport" */ '../views/cjhjReport/cjhjEditReport'),
    meta: {
      title: 'IBD肠镜活检编辑页',
    }
  },
  {
    path: '/cjhjView',
    name: 'cjhjViewReport',
    component: () => import(/* webpackChunkName: "cjhjViewReport" */ '../views/cjhjReport/cjhjViewReport'),
    meta: {
      title: 'IBD肠镜活检预览页',
    }
  },
  {
    path: '/wjhjEdit',
    name: 'wjhjEditReport',
    component: () => import(/* webpackChunkName: "wjhjEditReport" */ '../views/wjhjReport/wjhjEditReport'),
    meta: {
      title: '胃镜活检编辑页',
    }
  },
  {
    path: '/wjhjView',
    name: 'wjhjViewReport',
    component: () => import(/* webpackChunkName: "wjhjViewReport" */ '../views/wjhjReport/wjhjViewReport'),
    meta: {
      title: '胃镜活检预览页',
    }
  },
  {
    path: '/flxsgEdit',
    name: 'flxsgEditReport',
    component: () => import(/* webpackChunkName: "flxsgEditReport" */ '../views/flxsgReport/flxsgEditReport'),
    meta: {
      title: '反流性食管炎编辑页',
    }
  },
  {
    path: '/flxsgView',
    name: 'flxsgViewReport',
    component: () => import(/* webpackChunkName: "flxsgViewReport" */ '../views/flxsgReport/flxsgViewReport'),
    meta: {
      title: '反流性食管炎预览页',
    }
  },
  {
    path: '/jzcxrEdit',
    name: 'jzcxrEditReport',
    component: () => import(/* webpackChunkName: "jzcxrEditReport" */ '../views/jzcxrReport/jzcxrEditReport'),
    meta: {
      title: '结直肠息肉',
    }
  },
  {
    path: '/jzcxrView',
    name: 'jzcxrViewReport',
    component: () => import(/* webpackChunkName: "jzcxrViewReport" */ '../views/jzcxrReport/jzcxrViewReport'),
    meta: {
      title: '结直肠息肉',
    }
  },
  {
    path: '/',
    name: 'index',
    component: () => import(/* webpackChunkName: "index" */ '../views/index'),
    meta: {
      title: '编写鼻咽癌报告',
    }
  },
  {
    path: '/report',
    name: 'report',
    component: () => import(/* webpackChunkName: "report" */ '../views/report'),
    meta: {
      title: '预览鼻咽癌报告',
    }
  },
  {
    path: '/byaEdit',
    name: 'byaEdit',
    component: () => import(/* webpackChunkName: "byaEdit" */ '../views/byaReport/byaEditReport.vue'),
    meta: {
      title: '编写鼻咽癌报告',
    }
  },
  {
    path: '/byaView',
    name: 'byaView',
    component: () => import(/* webpackChunkName: "byaView" */ '../views/byaReport/byaViewReport.vue'),
    meta: {
      title: '预览鼻咽癌报告',
    }
  },
  {
    path: '/srPage',
    name: 'srLayout',
    component: () => import(/* webpackChunkName: "srLayout" */ '../views/srLayout'),
    meta: {
      title: '报告',
    }
  },
  {
    path: '/zcaEdit',
    name: 'zcaEditReport',
    component: () => import(/* webpackChunkName: "zcaEditReport" */ '../views/zcaReport/zcaEditReport'),
    meta: {
      title: '编写直肠癌报告',
    }
  },
  {
    path: '/zcaView',
    name: 'zcaViewReport',
    component: () => import(/* webpackChunkName: "zcaViewReport" */ '../views/zcaReport/zcaViewReport'),
    meta: {
      title: '预览直肠癌报告',
    }
  },
  {
    path: '/rxaEdit',
    name: 'rxaEditReport',
    component: () => import(/* webpackChunkName: "rxaEditReport" */ '../views/rxaReport/rxaEditReport'),
    meta: {
      title: '编写乳腺癌报告',
    }
  },
  {
    path: '/rxaView',
    name: 'rxaViewReport',
    component: () => import(/* webpackChunkName: "rxaViewReport" */ '../views/rxaReport/rxaViewReport'),
    meta: {
      title: '预览乳腺癌报告',
    }
  },
  {
    path: '/gjaEdit',
    name: 'gjaEditReport',
    component: () => import(/* webpackChunkName: "gjaEditReport" */ '../views/gjaReport/gjaEditReport'),
    meta: {
      title: '编写宫颈癌报告',
    }
  },
  {
    path: '/gjaView',
    name: 'gjaViewReport',
    component: () => import(/* webpackChunkName: "gjaViewReport" */ '../views/gjaReport/gjaViewReport'),
    meta: {
      title: '预览宫颈癌报告',
    }
  },
  {
    path: '/kgjEdit',
    name: 'kgjEditReport',
    component: () => import(/* webpackChunkName: "kgjEditReport" */ '../views/kgjReport/kgjEditReport'),
    meta: {
      title: '编写髋关节报告',
    }
  },
  {
    path: '/kgjView',
    name: 'kgjViewReport',
    component: () => import(/* webpackChunkName: "kgjViewReport" */ '../views/kgjReport/kgjViewReport'),
    meta: {
      title: '预览髋关节报告',
    }
  },
  {
    path: '/rxscEdit',
    name: 'rxscEditReport',
    component: () => import(/* webpackChunkName: "rxscEditReport" */ '../views/rxscReport/rxscEditReport'),
    meta: {
      title: '编写乳腺筛查报告',
    }
  },
  {
    path: '/rxscPrint',
    name: 'rxscPrintReport',
    component: () => import(/* webpackChunkName: "rxscPrintReport" */ '../views/rxscReport/rxscPrintReport'),
    meta: {
      title: '预览乳腺筛查报告',
    }
  },
  {
    path: '/rxsc2Edit',
    name: 'rxsc2EditReport',
    component: () => import(/* webpackChunkName: "rxsc2EditReport" */ '../views/rxsc2Report/rxscEditReport'),
    meta: {
      title: '编写乳腺筛查报告',
      mark: '迭代版本2'
    }
  },
  {
    path: '/rxsc2Print',
    name: 'rxsc2PrintReport',
    component: () => import(/* webpackChunkName: "rxsc2PrintReport" */ '../views/rxsc2Report/rxscPrintReport'),
    meta: {
      title: '预览乳腺筛查报告',
      mark: '迭代版本2'
    }
  },
  {
    path: '/rxscSyEdit',
    name: 'rxscSyEditReport',
    component: () => import(/* webpackChunkName: "rxscSyEditReport" */ '../views/rxscSyReport/rxscSyEditReport'),
    meta: {
      title: '编写乳腺筛查报告',
      mark: '中山三院'
    }
  },
  {
    path: '/rxscSyPrint',
    name: 'rxscSyPrintReport',
    component: () => import(/* webpackChunkName: "rxscSyPrintReport" */ '../views/rxscSyReport/rxscSyPrintReport'),
    meta: {
      title: '预览乳腺筛查报告',
      mark: '中山三院'
    }
  },
  {
    path: '/rxscFjrmEdit',
    name: 'rxscFjrmEditReport',
    component: () => import(/* webpackChunkName: "rxscFjrmEditReport" */ '../views/rxscFjrmReport/rxscFjrmEditReport'),
    meta: {
      title: '编写乳腺筛查报告',
      mark: '福建人民医院'
    }
  },
  {
    path: '/rxscFjrmPrint',
    name: 'rxscFjrmPrintReport',
    component: () => import(/* webpackChunkName: "rxscFjrmPrintReport" */ '../views/rxscFjrmReport/rxscFjrmPrintReport'),
    meta: {
      title: '预览乳腺筛查报告',
      mark: '福建人民医院'
    }
  },
  {
    path: '/rxscSmzxEdit',
    name: 'rxscSmzxEditReport',
    component: () => import(/* webpackChunkName: "rxscSmzxEditReport" */ '../views/rxscSmzxReport/rxscSmzxEditReport'),
    meta: {
      title: '编写乳腺筛查报告',
      mark: '三明市中西结合医院'
    }
  },
  {
    path: '/rxscSmzxPrint',
    name: 'rxscSmzxPrintReport',
    component: () => import(/* webpackChunkName: "rxscSmzxPrintReport" */ '../views/rxscSmzxReport/rxscSmzxPrintReport'),
    meta: {
      title: '预览乳腺筛查报告',
      mark: '三明市中西结合医院'
    }
  },
  {
    path: '/rxSPEdit',
    name: 'rxSPEditReport',
    component: () => import(/* webpackChunkName: "rxSPEditReport" */ '../views/rxSPReport/rxSPEditReport'),
    meta: {
      title: '超声科-乳腺筛查报告',
    }
  },
  {
    path: '/ctSPEdit',
    name: 'ctSPEditReport',
    component: () => import(/* webpackChunkName: "ctSPEditReport" */ '../views/ctSPReport/ctSPEditReport'),
    meta: {
      title: '放射科-乳腺筛查报告',
    }
  },
  {
    path: '/jzxEdit',
    name: 'jzxEditReport',
    component: () => import(/* webpackChunkName: "jzxEditReport" */ '../views/jzxReport/jzxEditReport'),
    meta: {
      title: '编写甲状腺报告',
    }
  },
  {
    path: '/jzxView',
    name: 'jzxViewReport',
    component: () => import(/* webpackChunkName: "jzxViewReport" */ '../views/jzxReport/jzxViewReport'),
    meta: {
      title: '预览甲状腺报告',
    }
  },
  {
    path: '/xmJzxEdit',
    name: 'xmJzxEditReport',
    component: () => import(/* webpackChunkName: "xmJzxEditReport" */ '../views/xmJzxReport/xmJzxEditReport'),
    meta: {
      title: '编写甲状腺报告',
    }
  },
  {
    path: '/xmJzxView',
    name: 'xmJzxViewReport',
    component: () => import(/* webpackChunkName: "xmJzxViewReport" */ '../views/xmJzxReport/xmJzxViewReport'),
    meta: {
      title: '预览甲状腺报告',
    }
  },
  {
    path: '/jzxSyEdit',
    name: 'jzxSyEditReport',
    component: () => import(/* webpackChunkName: "jzxSyEditReport" */ '../views/jzxSyReport/jzxSyEditReport'),
    meta: {
      title: '编写甲状腺报告',
      mark: '中山三院'
    }
  },
  {
    path: '/jzxSyView',
    name: 'jzxSyViewReport',
    component: () => import(/* webpackChunkName: "jzxSyViewReport" */ '../views/jzxSyReport/jzxSyViewReport'),
    meta: {
      title: '预览甲状腺报告',
      mark: '中山三院'
    }
  },
  {
    path: '/fjrmJzxEdit',
    name: 'fjrmJzxEditReport',
    component: () => import(/* webpackChunkName: "fjrmJzxEditReport" */ '../views/fjrmJzxReport/fjrmJzxEditReport'),
    meta: {
      title: '编写甲状腺报告',
      mark: '福建人民医院'
    }
  },
  {
    path: '/fjrmJzxView',
    name: 'fjrmJzxViewReport',
    component: () => import(/* webpackChunkName: "fjrmJzxViewReport" */ '../views/fjrmJzxReport/fjrmJzxViewReport'),
    meta: {
      title: '预览甲状腺报告',
      mark: '福建人民医院'
    }
  },
  {
    path: '/smzxJzxEdit',
    name: 'smzxJzxEditReport',
    component: () => import(/* webpackChunkName: "smzxJzxEditReport" */ '../views/smzxJzxReport/smzxJzxEditReport'),
    meta: {
      title: '编写甲状腺报告',
      mark: '三明市中西结合医院'
    }
  },
  {
    path: '/smzxJzxView',
    name: 'smzxJzxViewReport',
    component: () => import(/* webpackChunkName: "smzxJzxViewReport" */ '../views/smzxJzxReport/smzxJzxViewReport'),
    meta: {
      title: '预览甲状腺报告',
      mark: '三明市中西结合医院'
    }
  },
  {
    path: '/byaEditSd',
    name: 'byaEditReportSd',
    component: () => import(/* webpackChunkName: "byaEditSd" */ '../views/shunde/byaReport/byaEditReport'),
    meta: {
      title: '编写鼻咽癌报告',
    }
  },
  {
    path: '/byaViewSd',
    name: 'byaViewReportSd',
    component: () => import(/* webpackChunkName: "byaViewSd" */ '../views/shunde/byaReport/byaViewReport'),
    meta: {
      title: '预览鼻咽癌报告',
    }
  },
  {
    path: '/zcaEditSd',
    name: 'zcaEditReportSd',
    component: () => import(/* webpackChunkName: "zcaEditSd" */ '../views/shunde/zcaReport/zcaEditReport'),
    meta: {
      title: '编写直肠癌报告',
    }
  },
  {
    path: '/zcaViewSd',
    name: 'zcaViewReportSd',
    component: () => import(/* webpackChunkName: "zcaViewSd" */ '../views/shunde/zcaReport/zcaViewReport'),
    meta: {
      title: '预览直肠癌报告',
    }
  },
  {
    path: '/rxaEditSd',
    name: 'rxaEditReportSd',
    component: () => import(/* webpackChunkName: "rxaEditSd" */ '../views/shunde/rxaReport/rxaEditReport'),
    meta: {
      title: '编写乳腺癌报告',
    }
  },
  {
    path: '/rxaViewSd',
    name: 'rxaViewReportSd',
    component: () => import(/* webpackChunkName: "rxaViewSd" */ '../views/shunde/rxaReport/rxaViewReport'),
    meta: {
      title: '预览乳腺癌报告',
    }
  },
  {
    path: '/gjaEditSd',
    name: 'gjaEditReportSd',
    component: () => import(/* webpackChunkName: "gjaEditSd" */ '../views/shunde/gjaReport/gjaEditReport'),
    meta: {
      title: '编写宫颈癌报告',
    }
  },
  {
    path: '/gjaViewSd',
    name: 'gjaViewReportSd',
    component: () => import(/* webpackChunkName: "gjaViewSd" */ '../views/shunde/gjaReport/gjaViewReport'),
    meta: {
      title: '预览宫颈癌报告',
    }
  },
  {
    path: '/chqReport',
    name: 'cHqReport',
    component: () => import(/* webpackChunkName: "cHqReport" */ '../views/cHqReport/cHqReport'),
    meta: {
      title: 'C13呼气试验Hp检验报告',
    }
  },
  {
    path: '/yjbcEdit',
    name: 'yjbcEditReport',
    component: () => import(/* webpackChunkName: "yjbcEditReport" */ '../views/yjbcReport/yjbcEditReport'),
    meta: {
      title: '液基薄层细胞学报告模版--编写',
    }
  },
  {
    path: '/yjbcView',
    name: 'yjbcViewReport',
    component: () => import(/* webpackChunkName: "yjbcViewReport" */ '../views/yjbcReport/yjbcViewReport'),
    meta: {
      title: '液基薄层细胞学报告模版--预览',
    }
  },
  {
    path: '/rxjrxEdit',
    name: 'rxjrxEditReport',
    component: () => import(/* webpackChunkName: "rxjrxEditReport" */ '../views/rxjrxReport/rxjrxEditReport'),
    meta: {
      title: '编写乳腺浸润性癌报告',
    }
  },
  {
    path: '/rxjrxView',
    name: 'rxjrxViewReport',
    component: () => import(/* webpackChunkName: "rxjrxViewReport" */ '../views/rxjrxReport/rxjrxViewReport'),
    meta: {
      title: '预览乳腺浸润性癌报告',
    }
  },
  {
    path: '/waEdit',
    name: 'waEditReport',
    component: () => import(/* webpackChunkName: "waEditReport" */ '../views/waReport/waEditReport'),
    meta: {
    title: '胃癌结构化报告--编辑',
    }
  },
  {
    path: '/waView',
    name: 'waViewReport',
    component: () => import(/* webpackChunkName: "waViewReport" */ '../views/waReport/waViewReport'),
    meta: {
    title: '胃癌结构化报告--预览',
    }
  },
  {
    path: '/zcaFjfyEdit',
    name: 'zcaFjfyEditReport',
    component: () => import(/* webpackChunkName: "zcaFjfyEditReport" */ '../views/zcaFjfyReport/zcaEditReport'),
    meta: {
      title: '编写直肠癌报告',
    }
  },
  {
    path: '/zcaFjfyView',
    name: 'zcaFjfyViewReport',
    component: () => import(/* webpackChunkName: "zcaFjfyViewReport" */ '../views/zcaFjfyReport/zcaViewReport'),
    meta: {
      title: '预览直肠癌报告',
    }
  },
  {
    path: '/zcaFjfy2Edit',
    name: 'zcaFjfyEditReport',
    component: () => import(/* webpackChunkName: "zcaFjfy2EditReport" */ '../views/zcaFjfy2Report/zcaEditReport'),
    meta: {
      title: '编写直肠癌报告',
    }
  },
  {
    path: '/zcaFjfy2View',
    name: 'zcaFjfyViewReport',
    component: () => import(/* webpackChunkName: "zcaFjfy2ViewReport" */ '../views/zcaFjfy2Report/zcaViewReport'),
    meta: {
      title: '预览直肠癌报告',
    }
  },
  {
    path: '/gdseRxscEdit',
    name: 'gdseRxscEditReport',
    component: () => import(/* webpackChunkName: "gdseRxscEditReport" */ '../views/gdseRxscReport/gdseRxscEditReport'),
    meta: {
      title: '编写乳腺筛查报告',
      mark: '广东省第二人民医院'
    }
  },
  {
    path: '/gdseRxscView',
    name: 'gdseRxscPrintReport',
    component: () => import(/* webpackChunkName: "gdseRxscPrintReport" */ '../views/gdseRxscReport/gdseRxscPrintReport'),
    meta: {
      title: '预览乳腺筛查报告',
      mark: '广东省第二人民医院'
    }
  },
  {
    path: '/gdseJzxEdit',
    name: 'gdseJzxEditReport',
    component: () => import(/* webpackChunkName: "gdseJzxEditReport" */ '../views/gdseJzxReport/gdseJzxEditReport'),
    meta: {
      title: '编写甲状腺报告',
      mark: '广东省第二人民医院'
    }
  },
  {
    path: '/gdseJzxView',
    name: 'gdseJzxViewReport',
    component: () => import(/* webpackChunkName: "gdseJzxViewReport" */ '../views/gdseJzxReport/gdseJzxViewReport'),
    meta: {
      title: '预览甲状腺报告',
      mark: '广东省第二人民医院'
    }
  },
  {
    path: '/singleDisease',
    name: 'singleDisease',
    component: () => import(/* webpackChunkName: "singleDisease" */ '../views/singleDisease'),
    meta: {
      title: '单病种结构化报告',
    },
    children: []
  },
  {
    path: '/ear_rpt',
    name: 'ear_report',
    component: () => import(/* webpackChunkName: "earReport" */ '../views/generateReport/earReport'),
    meta: {
      title: '专科听力报告',
    }
  },
  {
    path: '/statistics',
    name: 'statistics',
    component: () => import(/* webpackChunkName: "statistics" */ '../views/statistics'),
    meta: {
      title: '病种统计',
    }
  },
  {
    path: '/trace',
    name: 'reportTrace',
    component: () => import(/* webpackChunkName: "reportTrace" */ '../views/Trace/reportTrace'),
    meta: {
      title: '报告痕迹',
    }
  },
  {
    path: '/srPdfView',
    name: 'srPdfView',
    component: () => import(/* webpackChunkName: "srPdfView" */ '../views/srPdfView'),
    meta: {
      title: '报告预览',
    }
  },
  {
    path: '/audioLayout',
    name: 'audioLayout',
    component: () => import(/* webpackChunkName: "audioLayout" */ '../views/audioLayout'),
    meta: {
      title: '语音结构化',
    },
    children: []
  },
  {
    path: '/signLayout',
    name: 'signLayout',
    component: () => import(/* webpackChunkName: "signLayout" */ '../views/signLayout'),
    meta: {
      title: '签名',
    },
    children: []
  },
  {
    path: '*',
    name: 'notFound',
    component: () => import(/* webpackChunkName: "notFound" */ '../views/notFound'),
    meta: {
      title: '404',
    }
  }
]
const router = new VueRouter({
  routes
});

router.beforeEach((to, from, next) => {
  window.document.title = to.meta.title == undefined?'structure-report':to.meta.title;
  if (to.meta.requireAuth) {
    let token = Cookies.get('access_token');
    let anonymous = Cookies.get('user_name');
    if (token) { 
      next({
        path: '/login',
        query: {
          redirect: to.fullPath
        } 
      })
    }
  }
  next();
})

export default router