/* 宽度 */
.full-w {
  width: 100%;
}
.wd-240 {
  width: 240px;
}
/* 颜色 */
.blue-bg {
  background: #F5F7FA;
}
/* 间距 */
.mt-8 {
  margin-top: 8px;
}
/* gdfyvng页面 */
#gdfyvng1 {
  position: relative;
  padding-bottom: 45px;
  width: 780px;
  min-height: 1100px;
  margin: 0 auto;
  border: 1px solid #F2A7C5;
  font-size: 14px;
}
#gdfyvng1 .gdfyvng-h {
  text-align: center;
}
#gdfyvng1 .gdfyvng-h img {
  margin-left: -1px;
  margin-top: -1px;
}
#gdfyvng1 .gdfyvng-h h1 {
  font-size: 24px;
  line-height: 24px;
  font-weight: bold;
  color: #000;
  margin-top: 26px;
}
#gdfyvng1 .gdfyvng-h h2 {
  font-size: 18px;
  line-height: 18px;
  color: #000;
  margin-top: 12px;
}
#gdfyvng1 .gdfyvng-h .top-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 32px;
  margin: 8px 50px 0;
  border-top: 1px solid #F2F2F2;
  border-bottom: 1px solid #F2F2F2;
}
#gdfyvng1 .info-lb {
  color: #606266;
}
#gdfyvng1 .info-con {
  color: #303133;
}
#gdfyvng1 .dib {
  display: inline-block;
  position: relative;
}
#gdfyvng1 .dib::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 10px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#gdfyvng1 .dib .layui-input {
  padding-right: 20px;
}
#gdfyvng1 .inp-group {
  position: relative;
  width: 100%;
  display: inline-table;
  line-height: normal;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}
#gdfyvng1 .inp-group input {
  border-top-right-radius: 0!important;
  border-bottom-right-radius: 0!important;
}
#gdfyvng1 .tail-span {
  display: table-cell;
  position: relative;
  padding: 0 8px;
  width: 1px;
  white-space: nowrap;
  vertical-align: middle;
  color: #909399;
  background: #F2F6FC;
  border: 1px solid #C8D7E6;
  border-left: none;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
#gdfyvng1 .gdfyvng-b {
  margin-top: 12px;
  padding: 0 50px;
}
#gdfyvng1 .gdfyvng-b input {
  min-height: 22px;
  height: 22px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
}
#gdfyvng1 .gdfyvng-b table {
  width: 100%;
  margin-top: 12px;
  border-collapse: collapse;
  border-color: #C8D7E6;
  table-layout: fixed;
  font-size: 14px;
}
#gdfyvng1 .gdfyvng-b tbody td {
  height: 30px;
  padding: 2px 8px;
}
#gdfyvng1 .gdfyvng-b .td-h {
  height: 26px;
  line-height: 22px;
  font-weight: bold;
  color: #000;
  text-align: left;
  padding: 2px 8px;
  background: #F2F6FC;
}
#gdfyvng1 .flex-sty {
  display: flex;
}
#gdfyvng1 .row-lb {
  font-weight: bold;
  color: #000;
}
#gdfyvng1 .imp-block {
  white-space: break-spaces;
}
#gdfyvng1 .imp-inp {
  height: 54px;
  flex: 1;
  line-height: 20px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 5px 15px;
}
#gdfyvng1 .gdfyvng-f {
  position: absolute;
  left: 50px;
  right: 50px;
  bottom: 40px;
  border-top: 1px solid #F2F2F2;
  /* padding-top: 8px; */
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#gdfyvng1 .gdfyvng-f input[type="text"] {
  height: 36px;
  border-radius: 3px;
  padding: 0 4px;
  font-size: 14px;
  border: 1px solid #DCDFE6;
}
[isView="true"] #gdfyvng1 .dib::after {
  border: none;
}
[isView="true"] #gdfyvng1 .tail-span {
  display: none;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}