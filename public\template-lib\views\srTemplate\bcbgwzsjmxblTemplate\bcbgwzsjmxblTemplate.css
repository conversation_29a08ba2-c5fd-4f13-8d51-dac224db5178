.t-pg {
  background-color: #F5F7FA;
}
#bcbgwzsjmxbl1 {
  background-color: #F5F7FA;
}
#bcbgwzsjmxbl1 * {
  font-family: '宋体';
}
#bcbgwzsjmxbl1 .view-wrap{display:none}
[isview='true'] #bcbgwzsjmxbl1 .edit-wrap{display:none}
[isview='true'] #bcbgwzsjmxbl1 .view-wrap{display:block}
#bcbgwzsjmxbl1 .edit-wrap {
  font-size: 14px;
  /* width: 904px; */
  margin: 0 auto;
  background-color: #fff;
  min-height: 100%;
  padding: 12px 24px;
}
.fw6 {
  font-weight: 600;
}
.bt {
  border-top: solid 1px #C0C4CC;
}
.xbt, .text_t_l {
  font-size: 16px;
  color: #303133;
}
.text_t_l {
  display: inline-block;
  min-width: 80px;
  text-align: right;
}
.bn {
  border: none;
}
.ta-c{
  text-align: center;
}
.item-flex {
  display: flex;
}
.item_bd {
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  height: 36px;
  line-height: 34px;
  box-sizing: border-box;
  padding-left: 12px;
}
.box {
  background-color: #fff;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  height: 86px;
  padding: 0 8px;
}

#bcbgwzsjmxbl1 .mb-12 {
  margin-bottom: 8px !important;
}

.dib{
  display: inline-block;
  position: relative;
}
#bcbgwzsjmxbl1 .dib::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 10px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#bcbgwzsjmxbl1 .dib .layui-input {
  padding-right: 20px;
}

#bcbgwzsjmxbl1 .flex-sty {
  overflow: hidden;
}
#bcbgwzsjmxbl1 .lbj-item {
  float: left;
  width: 32.4%;
  min-width: 240px;
  margin-bottom: 4px;
}
#bcbgwzsjmxbl1 .lbj-item input {
  min-width: 120px;
  width: calc(100% - 115px);
}
#bcbgwzsjmxbl1 .myzh-table {
  width: 100%;
  border: 1px solid #DCDFE6;
  border-collapse:collapse;
  vertical-align: top;
  background: #fff;
  margin: 5px 0;
}
#bcbgwzsjmxbl1 .tb-th th {
  height: 36px;
  padding: 7px 12px;
  text-align: left;
}
#bcbgwzsjmxbl1 tbody .td-lab {
  display: inline-block;
  width: 90px;
}
#bcbgwzsjmxbl1  tbody td{
  height: 40px;
  padding: 4px 12px;
}

.view-wrap {
  position: relative;
  width: 780px;
  min-height: 1100px;
  margin: 0 auto;
  background: #FFFFFF;
  /* border: 1px solid #C0C4CC; */
  padding: 30px 56px 100px 56px;
  box-sizing: border-box;
}
#bcbgwzsjmxbl1 .view-head {
  /* border-bottom: 1px solid #999; */
  padding-bottom: 9px;
  text-align: center;
}
#bcbgwzsjmxbl1 .view-head .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  position: relative;
}
#bcbgwzsjmxbl1 .view-head .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
 span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
 }
}
#bcbgwzsjmxbl1 .view-head .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 16px;
}
#bcbgwzsjmxbl1 .view-head .hos-tit{
  font-size: 21px;
  color: #000;
  font-family: "仿宋";
}
#bcbgwzsjmxbl1 .view-head .sub-tit{
  font-size: 26px;
  color: #000;
  margin-top: 20px;
  line-height: 26px;
}
.view-wrap .title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  text-align: center;
}
.v_hzxx {
  padding: 8px 0 0 0;
  border-top: solid 1px #999999;
  border-bottom: solid 1px #999999;
  font-size: 16px;
}
.item_t {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}
.tr{
  text-align: right;
}
.tc {
  text-align: center;
}

.rt_zt_l {
  color: #000;
  font-size: 16px;
  white-space: pre-line;
  word-break: break-all; 
}
.rt_zt_r {
  font-size: 14px; 
  color: #000000;
}
.bb{
  border-bottom: solid 1px #999999;
}
.btt{
  border-top: solid 1px #999999;
}
#bcbgwzsjmxbl1 .btt > div{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
}
#bcbgwzsjmxbl1 .btt > div + div {
  margin-left: 4px;
}
#bcbgwzsjmxbl1 .btt  > div .rt_zt_l {
  width: 70px;
}
#bcbgwzsjmxbl1 .btt  > div:nth-child(3) .rt_zt_l {
  width: 42px;
}
#bcbgwzsjmxbl1 .btt  > div .rt_zt_r {
  flex: 1;
}
.ib-p {
  display: inline-block;
}
.ib-p .rt_zt_r {
  margin-right: 10px;
}
.view-flex{
  display: flex;
} 
.flex_1 {
  flex: 1;
}
.flex_1-5 {
  flex: 1.5;
}
.flex_2 {
  flex: 2;
}
.flex_2-5 {
  flex: 2.5;
}
.flex_3 {
  flex: 3;
}
.flex_3-5 {
  flex: 3.5;
}
.flex_4 {
  flex: 4;
}
.flex_4-5 {
  flex: 4.5;
}
.flex_5 {
  flex: 5;
}
.flex_5-5 {
  flex: 5.5;
}
.flex_6 { 
  flex: 6;
}
.mb-8 {
  margin-bottom: 8px !important;
}
.mb-10 {
  margin-bottom: 6px !important;
}
.mb-20 {
  margin-bottom: 20px !important;
}
.view_img_box {
  display: none;
  overflow: hidden;
}
.view_img {
  float: left;
  width: 50%;
  margin-bottom: 8px;
}
.view_img img {
  display: block;
  margin: 0 auto;
  width: 265px;
  height: 200px;
}
.clac86 {
  width: calc(100% - 86px);
}
.item-fl {
  overflow: hidden;
}
.fl {
  float: left;
}
.item-flex-1 {
  float: left;
  min-width: 400px;
  margin-bottom: 4px;
}
.item-flex-2 {
  float: left;
  width: 50%;
  min-width: 400px;
  margin-bottom: 4px;
}
.item-fl .item-flex-3{
  float: left;
  width: 33.333%;
  min-width: 400px;
  margin-bottom: 4px;
}
.view-wrap .flex_2-5 img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
.title_text {
  display: none;
}
.textAlign {
  text-align: center;
}
.textAlign img {
  width: 426px;
  height: 300px;
}
.item {
  /* margin-bottom: 6px; */
  /* padding-top: 5px; */
}
.item-title {
  /* display: inline-block; */
  padding-bottom: 5px;
  font-weight: 600;
  line-height: 20px;
}
.itme_l {
  margin-bottom: 6px;
}
.itme_l p {
  white-space: pre-line;
  word-break: break-all;
}
/* 显示模板entry-type="5"样式处理 */
/* [entry-type="5"] #bcbgwzsjmxbl1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #bcbgwzsjmxbl1 .view-wrap {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #bcbgwzsjmxbl1 .view-wrap .view-head,
[entry-type="5"] #bcbgwzsjmxbl1 .view-wrap .view-patient,
[entry-type="5"] #bcbgwzsjmxbl1 .view-wrap .tip-wrap {
  display: none;
} */
/* 去掉边框，除表格外 */
/* [entry-type="5"] #bcbgwzsjmxbl1 .view-wrap div {
  border-bottom: none;
  border-top: none;
}
.blue-lb {
  margin-top: 5px;
  font-size: 16px;
  font-weight: 500;
  color: #1885F2;
  text-decoration:underline;
  cursor: pointer;
} */

/* 弹框字体 */
.diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
.diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
.diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
.diag-text-size .text-size .on {
  display: none;
}
#wzsjmxbl-rt-28, .text-con {
  opacity: 0;
}
.rt-dialog-con .edit-wrap.default textarea{
  font-size: 16px;
}
.rt-dialog-con .edit-wrap.large textarea{
  font-size: 18px;
}
.rt-dialog-con .edit-wrap.larger textarea{
  font-size: 20px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}

/* radio */
.bui-radios-label {
  margin-right: 4px;
  line-height: 20px;
}
.itme_l_mr .bui-radios-label{
  margin-right: 15px;
}
.bui-radios-label input {
  position: absolute;
  opacity: 0;
  visibility: hidden;
}
.bui-radios-label .bui-radios {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 16px;
  background: #FFFFFF;
  border: 1px solid #979797;
  /* border-radius: 50%; */
  vertical-align: -2px;
}
.bui-radios-label input:checked + .bui-radios:after {
  content: '✔';
  color: #000;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
}
.bui-radios-label input:checked + .bui-radios {
  /* background: #1885F2; */
  /* border: 1px solid #1885F2; */
}
.bui-radios-label input:disabled + .bui-radios {
  background-color: #e8e8e8;
  border: solid 1px #979797;
}
.bui-radios-label input:disabled:checked + .bui-radios:after {
  background-color: #c1c1c1;
}
.checkTheBox .bui-radios:after {
  content: '✔';
  color: #000;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
}
.bui-radios-label .bui-radios {
  -webkit-transition: background-color ease-out .3s;
  transition: background-color ease-out .3s;
}
.bui-radios-label>span {
  cursor: pointer;
}

.rt-sr-footer {
  position: absolute;
  bottom: 24px;
  left: 30px;
  right: 30px;
}
.report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#bcbgwzsjmxbl1 .view-wrap .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#bcbgwzsjmxbl1 .view-wrap.reporter-i.w120 {
  width: 120px;
}
#bcbgwzsjmxbl1 .view-wrap .reporter-i.w150 {
  width: 150px;
}
#bcbgwzsjmxbl1 .view-wrap .reporter-i span {
  width: 70px;
}
#bcbgwzsjmxbl1 .view-wrap .reporter-i span:last-child {
  flex: 1;
}
#bcbgwzsjmxbl1 .view-wrap .reporter-i img {
  min-width: 64px;
  height: 32px;
  object-fit: contain;
}
#bcbgwzsjmxbl1 .view-wrap .reporter-i + .reporter-i {
  margin-left: 8px;
}
#bcbgwzsjmxbl1 .view-wrap .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}
#bcbgwzsjmxbl1 .view-wrap .tip-wrap .tip-text {
  flex: 1;
}
.rt-sr-body {
  padding-top: 6px;
}