ul.td-page {
  background: #DCDFE6;
}
#hfbgtzs1 {
  font-size: 14px;
  width: 780px;
  height: 1100px;
  background: #FFF;
  margin: 0 auto;
  position: relative;
}
#hfbgtzs1 *{
  font-family: '宋体';
  font-size: 16px;
}
#hfbgtzs1 .hfbgtzs-view {
  width: 100%;
  margin: 0 auto;
  min-height: 100%;
  padding: 30px 56px 20px;
}
#hfbgtzs1 .hfbgtzs-view .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
}
#hfbgtzs1 .hfbgtzs-view .black-txt {
  color: #000;
  font-size: 14px;
}
#hfbgtzs1 .hfbgtzs-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#hfbgtzs1 .hfbgtzs-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#hfbgtzs1 .hfbgtzs-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}
#hfbgtzs1 .hfbgtzs-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 24px;
  text-align: center;
  position: relative;
}
#hfbgtzs1 .hfbgtzs-view .page-tit{
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 8px;
  color: #000;
}
#hfbgtzs1 .hfbgtzs-view .gray-txt .bold {
  font-weight: 600;
}
#hfbgtzs1 .hfbgtzs-view .bold {
  font-weight: bold;
}
#hfbgtzs1 .hfbgtzs-view .report-wrap {
  padding: 14px 0;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}
#hfbgtzs1 .hfbgtzs-view .reporter-i {
  flex: 1;
  display: flex;
}
#hfbgtzs1 .hfbgtzs-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#hfbgtzs1 .hfbgtzs-view .tip-wrap {
  font-size: 14px;
  margin-top: 8px;
}
.view-r {
  position: absolute;
  right: 0px;
}
.blh {
  display: inline-block;
  width: 60px;
}
.wb {
  word-break: break-all;
  min-height: 100px;
}