$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var bzConClone = null;  //作为给药模板具体内容
var bzTitleClone = null;  //作为给药模板标题
var trClone = null;  //作为给药表格模板内容
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否已填写
function initHtmlScript(ele) {
  curElem = $(ele);
  // console.log('curElem',curElem)
  // pageInit()
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList);
    rtStructure.createDescAndImpText = createDescAndImpTextHandler();
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    console.log('rtStructure.enterOptions',rtStructure.enterOptions);
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    console.log('idAndDomMap',idAndDomMap)
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false;
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      // toggleBlockItemStatus()
      togglePageType('preview')
      previewGYJL()
      
    } else {
      pageInit()
      toNurseList()
      togglePageType('edit')
    }
  }
}

function pageInit() {
  initBzCloneEle();
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    curElem.find('.def-ck').click();
    addBzHandler();
    useServerTime('zjjl-rt-8')
    $('#zjjl-rt-14').attr('checked',true)
  } else {
    displayBzContent();
  }
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    //执行一个laydate实例
    laydate.render({
      elem: '#zjjl-rt-1',//指定元素
      type: 'datetime',
      value: !isSavedReport ? new Date() : ''
    });
    laydate.render({
      elem: '#zjjl-rt-5',//指定元素
      type: 'datetime',
      value: !isSavedReport ? '' : ''
    });
    laydate.render({
      elem: '#zjjl-rt-6',//指定元素
      type: 'datetime',
      value: !isSavedReport ? '' : ''
    });
    laydate.render({
      elem: '#zjjl-rt-8',//指定元素
      type: 'datetime',
      value: !isSavedReport ? new Date() : ''
    });
  })
   // 手工和结构化填写的切换
  //  setWriteTypeBlock();
   $('#zjjl1').on('change', '.write-h .rt-sr-w', function() {
     var writeId = $(this).attr('id');
     $(this).closest('.write-type').find('.write-item').removeClass('act');
     $(this).closest('.write-type').find('.write-item .rt-sr-w').addClass('rt-hide')
     $(this).closest('.write-type').find('.write-item[write-id="'+writeId+'"]').addClass('act');
     $(this).closest('.write-type').find('.write-item[write-id="'+writeId+'"] .rt-sr-w').removeClass('rt-hide');
     $(this).closest('.write-type').find('.write-item:not([write-id="'+writeId+'"]) .hight-block').hide();
   })

     // 病灶/结构扭曲的item切换
    toggleBlockItemStatus();
}

function displayBzContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'zjjl-bzlist-1')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('zjjl-rt-', '');
        addBzHandler(paneId);
        $('.zjjl-rt-'+paneId+'-12').html('');
        var allTrData =  item.child;
        var trData = allTrData.filter(bzItem => bzItem.id === 'zjjl-rtlist-'+paneId+'-1');
        if(trData && trData.length) {
          var trList = trData[0].child || [];
          trList.forEach((item) => {
            var trPaneId = item.id.replace('zjjl-rt-', '').replace('-6', '');
            addClHandler(paneId,trPaneId)
          })
        }
      })
    }
  } else {
    curElem.find('.bz-list .bz-wrap').hide();
  }
}

// 添加镇静给药,oldPaneId已保存过的
function addBzHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.bz-list .bz-con .bz-item').length;
  var activeTab = 'zjjl-rt-' + paneId;
  var newPaneBlock = appendBzHtml(paneId, bzLen,oldPaneId);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '镇静给药',
      name: '镇静给药',
      pid: 'zjjl-bzlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('给药记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.bz-list .bz-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
    if(!oldPaneId) {
      // getAllDescAndImpContent();
    }
    // document.querySelector(".bz-list .bz-wrap .bz-tit-ls").scrollLeft = document.querySelector(".bz-list .bz-wrap .bz-tit-ls").scrollWidth;
  }
}

// 添加镇静给药测量记录,oldPaneId已保存过的
function addClHandler(paentPaneId,oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var trLen = $('.zjjl-rt-'+paentPaneId+'-12 tr').length;
  var activeTab = 'zjjl-rt-' + paentPaneId + '-6';
  var newPaneBlock = addTableHtml(paentPaneId,paneId,trLen,oldPaneId);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '测量记录',
      name: '测量记录',
      pid: 'zjjl-rtlist-'+paentPaneId+'-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('测量记录' + (1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('.zjjl-rt-'+ paentPaneId +'-12[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

// 处理新增给药的html
function appendBzHtml(paneId, bzLen,oldPaneId) {
  $('.bz-list .bz-tit-ls .bz-tit-i').removeClass('act');
  $('.bz-list .bz-con .bz-item').removeClass('act');
  var reg = new RegExp('000', 'ig');
  // console.log('bzConClone',bzConClone)
  var content = bzConClone.replace(reg, paneId);  //作为病灶示例模板具体内容
  // $('.bz-list .bz-tit-ls').append(title);
  // console.log('content',content);
  $('.bz-list .bz-con').append(content);
  $('#zjjl-rt-'+paneId+'-1').html('给药记录 ' + (bzLen + 1))
  layui.use(['laydate', 'dropdown'], function(){
    var laydate = layui.laydate;
    var dropdown = layui.dropdown;
    laydate.render({
      elem: '#zjjl-rt-'+paneId+'-4',//指定元素
      type: 'datetime',
      value: oldPaneId ? '' : new Date()
    });
    laydate.render({
      elem: '#zjjl-rt-'+paneId+'-8',//指定元素
      type: 'datetime',
      value: oldPaneId ? '' : new Date()
    });
    dropdown.render({
      elem: '#zjjl-rt-'+paneId+'-2' //可绑定在任意元素中，此处以上述按钮为例
      ,data: [{
        title: '口服'
        ,id: 108
        ,child: [{
          title: '水和氯醛溶液',
          pTitle: '口服'
          ,id: 1081
        }]
      },{
        title: '滴鼻'
        ,id: 108
        ,child: [{
          title: '盐酸右美托咪定注射液',
          pTitle: '滴鼻'
          ,id: 1081
        }]
      },{
        title: '静脉'
        ,id: 109
        ,child: [{
          title: '碘克沙醇注射液',
          pTitle: '静脉'
          ,id: 1091
        },{
          title: '碘佛醇注射液',
          pTitle: '静脉'
          ,id: 1092
        },{
          title: '碘普罗胺注射液50ml',
          pTitle: '静脉'
          ,id: 1093
        },{
          title: '碘普罗胺注射液20ml',
          pTitle: '静脉'
          ,id: 1094
        }]
      }]
      ,id: 'zjjl-rt-5'
      //菜单被点击的事件
      ,click: function(obj){
        $('#zjjl-rt-'+paneId+'-2').val(obj.pTitle + '/' +obj.title);
      }
    });
  })
  // if(oldPaneId){
  //   var trContent = trClone.replace(reg, oldPaneId);
  //   $('.bz-list .bz-con .table-content #zjjl-rt-'+oldPaneId+'-12').append(trContent);
  // }
  var newPaneBlock = $('.bz-list .bz-item[tab-target="zjjl-rt-'+paneId+'"] .drug-content');
  // $('.bz-list .bz-tit-i[tab-id="zjjl-rt-'+paneId+'"]').find('.bz-name').text('病灶'+(bzLen + 1));
  return newPaneBlock;
}

function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      // console.log('88888',rtStructure.idAndDomMap[id])
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        // console.log('9999',rtStructure.idAndDomMap[id].value)
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    newPaneBlock.find('.def-ck').click();
  }
}

function formatDate(date) {
  var year = date.getFullYear();
  var month = addZero(date.getMonth() + 1);
  var day = addZero(date.getDate());
  var hours = addZero(date.getHours());
  var minutes = addZero(date.getMinutes());
  var seconds = addZero(date.getSeconds());
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
}
 
function addZero(num) {
  return num < 10 ? '0' + num : num;
}

function addTableHtml(paentPaneId,paneId,trLen,oldPaneId){
  var reg = new RegExp('000', 'ig');
  var reg2 = new RegExp('rtlist-000', 'ig');
  
  var trCon = trClone.replace(reg2, 'rtlist-' + paentPaneId);
  trCon = trCon.replace(reg, paneId);
  $('.zjjl-rt-'+paentPaneId+'-12').append(trCon);
  layui.use(['laydate', 'dropdown'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#zjjl-rt-'+paneId+'-8',//指定元素
      type: 'datetime',
      value: oldPaneId ? '' : new Date()
    });
  })
  $('#zjjl-rt-'+paneId+'-7').html((trLen + 1));
  $('#zjjl-rt-'+paneId+'-14').html(formatDate(new Date()));
  var newPaneBlock = $('.zjjl-rt-'+paentPaneId+'-12 .bz-tr');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}

// 病灶/结构扭曲的item切换
function toggleBlockItemStatus() {
  $('.bz-wrap').on('click', '.bz-tit-i', function(e) {
    var target = $(this).attr('tab-id');
    $(this).siblings('.bz-tit-i').removeClass('act');
    $(this).addClass('act');
    $('.bz-item[tab-target="'+target+'"]').siblings('.bz-item').removeClass('act');
    $('.bz-item[tab-target="'+target+'"]').addClass('act');
    // 勾选上默认项
    $('.bz-item[tab-target="'+target+'"] .def-ck').each(function() {
      var name = $(this).attr('name');
      if(!$('[name="'+name+'"]:checked').length) {
        $(this).click();
      }
    })
  })
  curElem.find(".stop-lb").on("click", function(e) {
    var checkbox = $(this).find('.rt-sr-w');
    if(checkbox.attr('type')==='checkbox') {
      e.stopPropagation();
      e.preventDefault();
      $(this).parents('.bz-tit-i').click();
      if(!checkbox.is(":checked")) {
        checkbox.click();
      }
    }
  })
  curElem.find(".stop-lb").on("click", ".rt-sr-w", function(e) {
    if($(this).attr('type')==='checkbox') {
      e.stopPropagation();
      $(this).parents('.bz-tit-i').click();
    }
    if(!$(this).is(':checked')) {
      var target = $(this).parents('.bz-tit-i').attr('tab-id');
      $('.bz-item[tab-target="'+target+'"]').find('.hight-block').hide();
    }
  })

  // 默认打开结构扭曲第一项
  showNqWrap();
}

function showNqWrap() {
  var checked = $('.nq-wrap .bz-tit-ls .bz-tit-i input:checked');
  if(checked.length) {
    $('.nq-wrap .bz-tit-ls .bz-tit-i').removeClass('act');
    $('.nq-wrap .bz-item').removeClass('act');
    var target = $(checked[0]).closest('.bz-tit-i').attr('tab-id');
    $(checked[0]).closest('.bz-tit-i').addClass('act');
    curElem.find('.bz-item[tab-target="'+target+'"]').addClass('act');
  } else {
    $('.nq-wrap .bz-tit-ls .bz-tit-i:eq(0)').click();
  }
}

function delTab(vm, paneId){
  if(paneId === '000'){
    return;
  }
  
  var allSiblings = $(vm).parent().parent().parent().parent().siblings();
  allSiblings.each(function(index) {
      $(this).children().children().children().children().each(function(cIndex) {
        if(cIndex === 0){
          $(this).html('给药记录 '+(index + 1))
        }
      })
  });
  $(vm).parent().parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}
function delTrFuc(vm, paneId){
  var allSiblings = $(vm).parent().parent().siblings();
  allSiblings.each(function(index) {
      // console.log($(this).children().first(),index); // 输出：first 和 third
      $(this).children().first().html(index + 1)
  });
  // console.log('parent',$(vm).parent().parent().siblings())

  $(vm).parent().parent().remove();
  
}

// 复制暂存镇静给药的具体模板示例内容，为后续添加给药做准备
function initBzCloneEle() {
  var bzCon = curElem.find('.bz-list .bz-wrap .bz-item').clone(true);
  bzConClone = '<div class="bz-item act" tab-target="zjjl-rt-000">'+bzCon.html()+'</div>';
  var tr = curElem.find('.bz-list .bz-wrap .bz-item .bz-tr').clone(true);
  trClone = '<tr class="bz-tr rt-sr-w" id="zjjl-rt-000-6" pid="zjjl-rtlist-000-1" rt-sc="pageId:zjjl1;name:测量记录tr;wt:;vt:;pvf:;">'+tr.html()+'</tr>';
  // console.log('trClone',trClone)
  curElem.find('.bz-list .bz-wrap .bz-item .bz-tr').html('');
  curElem.find('.bz-list .bz-wrap .bz-con').html('');
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function togglePageType(type) {
  $(`[data-type]`).hide()
  $(`[data-type=${type}]`).show()
  if (type === 'preview') {
    $('[data-key]').each(function () {
      let key = $(this).attr('data-key')
      if (idAndDomMap) {
        let result = []
        Object.keys(idAndDomMap).forEach(idKey => {
          if (idKey.startsWith(key)) {
            let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value : '';
            if (value) {
              result.push(value)
            }
          }
        })
        if (key === 'hlhdyy-rt-0006-') {
          $(this).html(result.join(':'))
        } else if (key === 'hlhdyy-rt-0008-' || key === 'hlhdyy-rt-0009-') {
          $(this).html(result.join('、'))
        } else {
          $(this).html(result.join(','))
        }
        this.style.color = '#000'
      }
    })
  }
}
function previewGYJL(){
  // console.log();
  if(rtStructure.enterOptions.data){
    let gyjl = rtStructure.enterOptions.data.find(item=>item.id==='zjjl-bzlist-1')
    let html = ''
    if(gyjl&&gyjl.child){
      gyjl.child.forEach(item=>{
        let gyName = {}
        let method = {}
        let weight = {}
        let time = {}
        let table = {}
        if(item.child){
          item.child.forEach(item2=>{
            let id = item2.id
            if(id.startsWith('zjjl-rt-')&&id.endsWith('1')){
              gyName = item2
            }else if(id.startsWith('zjjl-rt-')&&id.endsWith('2')){
              method = item2
            }else if(id.startsWith('zjjl-rt-')&&id.endsWith('3')){
              weight = item2
            }else if(id.startsWith('zjjl-rt-')&&id.endsWith('4')){
              time = item2
            }else if(id.startsWith('zjjl-rtlist')&& id.endsWith('1')){
              table = item2
            }
          })
        }
        html+=`
          <div class="drug-content " >
            <div class="drug-top">
              <div >${gyName.val || ''}</div>
            </div>
            <div class="sign-content" style="justify-content: space-between;">
              <div class="weight" style="width: 33%;">
                <div>给药方式：</div>
                <div class="temperature">${method.val||''}</div>
              </div>
              <div class="weight" style="width: 33%;">
                <div>剂量：</div>
                <div class="temperature">${weight.val||''}</div>
              </div>
              <div class="weight" style="width: 33%;">
                <div>用药时间：</div>
                <div class="temperature">${time.val||''}</div>
              </div>
            </div>
            <div class="form-title"  style="margin-top: 12px;margin-left: 16px;display: flex;align-items: center;">
              <span>测量记录：</span>
            </div>
             <div class="table-content">
              <table class="layui-table">
                <colgroup>
                  <col width="62">
                  <col width="183">
                  <col width="163">
                  <col width="163">
                  <col>
                </colgroup>
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>记录时间</th>
                    <th>脉搏 (次/分)</th>
                    <th>氧饱和度 (%)</th>
                    <th>血压 (mmHg)</th>
                  </tr> 
                </thead>
                <tbody>
                 ${
                  renderTr(table)
                }
                </tbody>
                <tbody></tbody>
              </table>
            </div>
          </div>
        `
      });
      $('#gyjl').html(html)
    }
  }
}
function renderTr(table){
  let html = ''
  if(table && table.child){
    table.child.forEach((item)=>{
      let str = `<tr>
                    <td>{{7}}</td>
                    <td>{{8}}</td>
                    <td>{{9}}</td>
                    <td>{{10}}</td>
                    <td>{{11}}</td>
              </tr>`
      item.child.forEach(item2=>{
        if(item2.id.endsWith('7')){
          str = str.replace('{{7}}',item2.val)          
        }else if(item2.id.endsWith('8')){
          str = str.replace('{{8}}',item2.val)
        }else if(item2.id.endsWith('9')){
          str = str.replace('{{9}}',item2.val)
        }else if(item2.id.endsWith('10')){
          str = str.replace('{{10}}',item2.val)
        }else if(item2.id.endsWith('11')){
          str = str.replace('{{11}}',item2.val)
        }
      })
      str = str.replace(/{{\d}}/,'')
      html+=str
    })
  }
  return html
}
function toNurseList(){
  let list = window.getNurseList({deptCode: publicInfo.userInfo&&publicInfo.userInfo.param&&publicInfo.userInfo.param.deptCode || ''});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
  initInpAndSel('zjjl-rt-7', userList,optHandler  );
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#zjjl-rt-7').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#zjjl-rt-7').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }
 function initInpAndSel(idList, optionList, cb,id) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList,dropdown)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
     typeof cb === 'function' && cb(obj)
      // console.log('title',obj.title,id)
      if(id){
        $(`#${id}`).val('');
        if(obj.title === '口服'){
          //先拿到dropdown.render对象
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentOralList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }else if(obj.title === '滴鼻'){
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentSnortList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }else if(obj.title === '静脉'){
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentMainlineList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }
      }
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
  // if(id){
    // 使用dropdown.setData方法重新赋值
    
  // }
}