/* 间距 */
.ml-12 {
  margin-left: 12px;
}
.ml-56 {
  margin-left: 56px;
}
/* 宽度 */
.full-wd {
  width: 100%;
}
/* 边框 */
.bor-b {
  border-bottom: 1px solid #C0C4CC;
}
#hbetbraf1 {
  width: 100%;
  min-height: 100%;
  font-family: '宋体';
  font-size: 16px;
  color: #000;
  background: #F5F7FA;
  overflow: auto;
}
#hbetbraf1 * {
  font-family: '宋体';
}
#hbetbraf1 .inp-sty {
  background: #FFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 11px;
  font-size: 16px;
}
#hbetbraf1 .row-item {
  padding: 8px 24px;
}
#hbetbraf1 .row-lb {
  display: inline-block;
  min-width: 80px;
}
#hbetbraf1 .con-flex {
  display: flex;
}
#hbetbraf1 .flex-item {
  flex: 1;
}
#hbetbraf1 .lb-sty {
  width: 100%;
  text-align: center;
  font-weight: bold;
  margin-bottom: 4px;
}
[isview="true"] #hbetbraf1 .hbetbraf-edit {
  display: none;
}
[isview="true"] #hbetbraf1 .hbetbraf-view {
  display: block;
}
/* 预览页面 */
#hbetbraf1 .hbetbraf-view {
  display: none;
  position: relative;
  margin: 0 auto;
  width: 780px;
  min-height: 1100px;
  background: #FFF;
  border: 1px solid #DCDFE6;
  padding: 37px 56px 114px 56px;
}
#hbetbraf1 .hbetbraf-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#hbetbraf1 .hbetbraf-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}
#hbetbraf1 .hbetbraf-view .hos-tit {
  font-weight: 900;
  font-size: 36px;
  line-height: 50px;
  text-align: center;
}
#hbetbraf1 .hbetbraf-view .sub-tit {
  font-weight: bold;
  font-size: 24px;
  line-height: 35px;
  text-align: center;
}
#hbetbraf1 .hbetbraf-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
}
#hbetbraf1 .hbetbraf-view .logo-tit .code img {
  width: 122px;
  height: 36px;
}
#hbetbraf1 .hbetbraf-view .logo-tit .code span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
}
#hbetbraf1 .hbetbraf-view .blh-tit {
  font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  text-align: right;
}
#hbetbraf1 .hbetbraf-view .patient-info {
  margin-top: 8px;
  padding: 8px 0;
  border-top:2px solid #000;
  border-bottom:2px solid #000;
}
#hbetbraf1 .hbetbraf-view .p-item + .p-item {
  margin-top: 4px!important;
}
#hbetbraf1 .hbetbraf-view .bgjg-h {
  font-weight: bold;
  text-align: center;
  padding: 8px;
  border-bottom: 2px solid #000;
}
#hbetbraf1 .hbetbraf-view .bgjg-b {
  text-align: center;
  padding: 16px 8px 0;
}
#hbetbraf1 .hbetbraf-view .bgjg-h > div {
  margin-left: 4px;
}
#hbetbraf1 .hbetbraf-view .bgjg-b > div {
  white-space: pre-line;
  word-break: break-all;
  margin-left: 4px;
}
#hbetbraf1 .hbetbraf-view .report-wrap {
  padding: 8px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hbetbraf1 .hbetbraf-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  line-height: 32px;
}
#hbetbraf1 .hbetbraf-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#hbetbraf1 .hbetbraf-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
}
#hbetbraf1 .hbetbraf-view .rpt-footer {
  width: 666px;
  position: absolute;
  bottom: 40px;
}