export const treeData = [
  {
    id: 1,
    label: "翼状结构",
    shortCut: "yzjg",
    disabled: true,
    children: [
      {
        id: 5,
        label: "翼突根部",
        shortCut: "ytgb",
        lCode: "01004.0101",
        rCode: "01007.0101",
      },
      {
        id: 6,
        label: "翼外板",
        shortCut: "ywb",
        lCode: "01004.0102",
        rCode: "01007.0102",
      },
      {
        id: 7,
        label: "翼内板",
        shortCut: "ynb",
        lCode: "01004.0103",
        rCode: "01007.0103",
      },
      {
        id: 8,
        label: "翼腭窝",
        shortCut: "yew",
        lCode: "01004.0104",
        rCode: "01007.0104",
      },
    ],
  },
  {
    id: 2,
    label: "颅底",
    shortCut: "ldg",
    disabled: true,
    children: [
      {
        id: 9,
        label: "蝶骨大翼",
        shortCut: "dgdy",
        lCode: "01004.0201",
        rCode: "01007.0201",
      },
      {
        id: 10,
        label: "岩尖",
        shortCut: "yj",
        lCode: "01004.0202",
        rCode: "01007.0202",
      },
      {
        id: 30,
        label: "圆孔",
        shortCut: "yk",
        lCode: "01004.0203",
        rCode: "01007.0203",
      },
      {
        id: 31,
        label: "卵圆孔",
        shortCut: "lyk",
        lCode: "01004.0204",
        rCode: "01007.0204",
      },
      {
        id: 32,
        label: "破裂孔",
        shortCut: "plk",
        lCode: "01004.0205",
        rCode: "01007.0205",
      },
    ],
  },
  {
    id: 3,
    label: "鼻窦",
    shortCut: "bd",
    disabled: true,
    children: [
      {
        id: 11,
        label: "上颌窦",
        shortCut: "shd",
        lCode: "01004.0301",
        rCode: "01007.0301",
      },
      {
        id: 12,
        label: "筛窦",
        shortCut: "sd",
        lCode: "01004.0302",
        rCode: "01007.0302",
      },
      {
        id: 13,
        label: "额窦",
        shortCut: "ed",
        lCode: "01004.0303",
        rCode: "01007.0303",
      },
    ],
  },
  {
    id: 4,
    label: "淋巴结转移",
    disabled: true,
    shortCut: "jblbjzy",
    children: [
      {
        id: 14,
        label: "咽后",
        shortCut: "jbyh",
        lCode: "01004.0401",
        rCode: "01007.0401",
      },
      {
        id: 15,
        label: "颈部",
        children: [
          {
            id: 16,
            label: "ⅠA",
            shortCut: "jbⅠA",
            lCode: "01004.040201",
            rCode: "01007.040201",
          },
          {
            id: 17,
            label: "ⅠB",
            shortCut: "jbⅠB",
            lCode: "01004.040202",
            rCode: "01007.040202",
          },
          {
            id: 18,
            label: "ⅡA",
            shortCut: "jbⅡA",
            lCode: "01004.040203",
            rCode: "01007.040203",
          },
          {
            id: 19,
            label: "ⅡB",
            shortCut: "jbⅡB",
            lCode: "01004.040204",
            rCode: "01007.040204",
          },
          {
            id: 20,
            label: "Ⅲ",
            shortCut: "jbⅢ",
            lCode: "01004.040205",
            rCode: "01007.040205",
          },
          // {
          //   id: 21,
          //   label: "Ⅳ",
          //   shortCut: "jbⅣ",
          //   lCode: "01004.040206",
          //   rCode: "01007.040206",
          // },
          {
            id: 34,
            label: "ⅣA",
            shortCut: "jbⅣA",
            lCode: "01004.040210",
            rCode: "01007.040210",
          },
          {
            id: 35,
            label: "ⅣB",
            shortCut: "jbⅣB",
            lCode: "01004.040211",
            rCode: "01007.040211",
          },
          {
            id: 22,
            label: "ⅤA",
            shortCut: "jbⅤA",
            lCode: "01004.040207",
            rCode: "01007.040207",
          },
          {
            id: 23,
            label: "ⅤB",
            shortCut: "jbⅤB",
            lCode: "01004.040208",
            rCode: "01007.040208",
          },
          {
            id: 36,
            label: "ⅤC",
            shortCut: "jbⅤC",
            lCode: "01004.040212",
            rCode: "01007.040212",
          },
          {
            id: 37,
            label: "ⅦA",
            shortCut: "jbⅦA",
            lCode: "01004.040213",
            rCode: "01007.040213",
          },
          {
            id: 38,
            label: "ⅦB",
            shortCut: "jbⅦB",
            lCode: "01004.040214",
            rCode: "01007.040214",
          },
          {
            id: 39,
            label: "Ⅷ",
            shortCut: "jbⅧ",
            lCode: "01004.040215",
            rCode: "01007.040215",
          },
          {
            id: 24,
            label: "其他分区：",
            disabled: true,
          },
        ],
      },
    ],
  },
];

export const middleGroup = [
  {
    id: 3,
    label: "鼻咽",
    mCode: "01005.01",
  },
  {
    id: 4,
    label: "口咽",
    qfDirection: "向下",
    mCode: "01005.02",
  },
  {
    id: 5,
    label: "下咽",
    qfDirection: "向下",
    mCode: "01005.03",
  },
  {
    id: 1,
    label: "斜坡",
    qfDirection: "向上",
    mCode: "01005.04",
  },
  {
    id: 2,
    label: "蝶窦",
    qfDirection: "向上",
    mCode: "01005.05",
  },
  {
    id: 6,
    label: "颈椎",
    qfDirection: "向后",
    mCode: "01005.06",
  },
  {
    id: 7,
    label: "蝶窦底壁",
    qfDirection: "向上",
    mCode: "01005.07",
  },
]; // 中线多选框

export const checkList = [
  {
    id: 1,
    label: "腭帆提肌",
    shortCut: "eftj",
    belongs: "T1",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.03",
    rCode: "01006.03",
  },
  {
    id: 2,
    label: "鼻腔",
    shortCut: "bq",
    belongs: "T1",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.02",
    rCode: "01006.02",
  },
  {
    id: 3,
    label: "腭帆张肌",
    shortCut: "efzj",
    belongs: "T2",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.06",
    rCode: "01006.06",
  },
  {
    id: 4,
    label: "翼内肌",
    shortCut: "ynj",
    belongs: "T2",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.05",
    rCode: "01006.05",
  },
  {
    id: 5,
    label: "头长肌",
    shortCut: "tcj",
    belongs: "T2",
    qfDirection: "向后",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.04",
    rCode: "01006.04",
  },
  {
    id: 6,
    label: "咽旁间隙",
    shortCut: "ypjx",
    belongs: "T2",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.12",
    rCode: "01006.12",
  },
  {
    id: 7,
    label: "翼外肌",
    shortCut: "ywj",
    belongs: "T2",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.08",
    rCode: "01006.08",
  },
  {
    id: 8,
    label: "颈长肌",
    shortCut: "jcj",
    belongs: "T2",
    qfDirection: "向后",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.07",
    rCode: "01006.07",
  },
  {
    id: 9,
    label: "海绵窦",
    shortCut: "hmd",
    belongs: "T4",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.09",
    rCode: "01006.09",
  },
  {
    id: 10,
    label: "眼眶",
    shortCut: "yk",
    belongs: "T4",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.10",
    rCode: "01006.10",
  },
  {
    id: 11,
    label: "腮腺",
    shortCut: "sx",
    belongs: "T4",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.11",
    rCode: "01006.11",
  },
  {
    id: 12,
    label: "颅脑",
    shortCut: "ln",
    belongs: "T4",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.13",
  },
  {
    id: 13,
    label: "超翼外肌外缘软组织",
    shortCut: "cywjwyrzz",
    belongs: "T4",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.01",
    rCode: "01006.01",
  },
  {
    id: 33,
    label: "Meckel腔",
    shortCut: "Meckelq",
    belongs: "T4",
    qfDirection: "向外",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01003.14",
    rCode: "01006.14",
  },
];

// 翼状结构
export const strList = [
  {
    id: 1,
    label: "翼突根部",
    shortCut: "ytgb",
    belongs: "T3",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0101",
    rCode: "01007.0101",
  },
  {
    id: 2,
    label: "翼外板",
    shortCut: "ywb",
    belongs: "T3",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0102",
    rCode: "01007.0102",
  },
  {
    id: 3,
    label: "翼内板",
    shortCut: "ynb",
    belongs: "T3",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0103",
    rCode: "01007.0103",
  },
  {
    id: 4,
    label: "翼腭窝",
    shortCut: "yew",
    belongs: "T3",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0104",
    rCode: "01007.0104",
  },
];
// 颅底
export const boneList = [
  {
    id: 5,
    label: "蝶骨大翼",
    shortCut: "dgdy",
    belongs: "T3",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0201",
    rCode: "01007.0201",
  },
  {
    id: 6,
    label: "岩尖",
    shortCut: "yj",
    belongs: "T3",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0202",
    rCode: "01007.0202",
  },
  {
    id: 30,
    label: "圆孔",
    shortCut: "yk",
    belongs: "T3",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0203",
    rCode: "01007.0203",
  },
  {
    id: 31,
    label: "卵圆孔",
    shortCut: "lyk",
    belongs: "T3",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0204",
    rCode: "01007.0204",
  },
  {
    id: 32,
    label: "破裂孔",
    shortCut: "plk",
    belongs: "T3",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0205",
    rCode: "01007.0205",
  },
];
// 鼻窦
export const noseList = [
  {
    id: 7,
    label: "上颌窦",
    shortCut: "shd",
    belongs: "T3",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0301",
    rCode: "01007.0301",
  },
  {
    id: 8,
    label: "筛窦",
    shortCut: "sd",
    belongs: "T3",
    qfDirection: "向前",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0302",
    rCode: "01007.0302",
  },
  {
    id: 9,
    label: "额窦",
    shortCut: "ed",
    belongs: "T3",
    qfDirection: "向上",
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0303",
    rCode: "01007.0303",
  },
];
// 淋巴结转移
export const limList = [
  {
    id: 10,
    label: "咽后",
    shortCut: "jbyh",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.0401",
    rCode: "01007.0401",
  },
  {
    id: 11,
    label: "颈部",
    isLb: true,
    isSelectAll: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    shortCut: "jbⅠABⅡⅢⅣⅤ",
  },
];
// 颈部
export const neckList = [
  {
    id: 16,
    label: "ⅠA",
    shortCut: "jbⅠA",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040201",
    rCode: "01007.040201",
  },
  {
    id: 17,
    label: "ⅠB",
    shortCut: "jbⅠB",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040202",
    rCode: "01007.040202",
  },
  {
    id: 18,
    label: "ⅡA",
    shortCut: "jbⅡA",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040203",
    rCode: "01007.040203",
  },
  {
    id: 19,
    label: "ⅡB",
    shortCut: "jbⅡB",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040204",
    rCode: "01007.040204",
  },
  {
    id: 20,
    label: "Ⅲ",
    shortCut: "jbⅢ",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040205",
    rCode: "01007.040205",
  },
  {
    id: 34,
    label: "ⅣA",
    shortCut: "jbⅣA",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040210",
    rCode: "01007.040210",
  },
  {
    id: 35,
    label: "ⅣB",
    shortCut: "jbⅣB",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040211",
    rCode: "01007.040211",
  },
  {
    id: 22,
    label: "ⅤA",
    shortCut: "jbⅤA",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040207",
    rCode: "01007.040207",
  },
  {
    id: 23,
    label: "ⅤB",
    shortCut: "jbⅤB",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040208",
    rCode: "01007.040208",
  },
  {
    id: 36,
    label: "ⅤC",
    shortCut: "jbⅤC",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040212",
    rCode: "01007.040212",
  },
  {
    id: 37,
    label: "ⅦA",
    shortCut: "jbⅦA",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040213",
    rCode: "01007.040213",
  },
  {
    id: 38,
    label: "ⅦB",
    shortCut: "jbⅦB",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040214",
    rCode: "01007.040214",
  },
  {
    id: 39,
    label: "Ⅷ",
    shortCut: "jbⅧ",
    isLb: true,
    checked: [
      {
        po: "左",
        disabled: false,
      },
      {
        po: "右",
        disabled: false,
      },
    ],
    lCode: "01004.040215",
    rCode: "01007.040215",
  },
];
let tSort = checkList.concat(strList).concat(boneList).concat(noseList);
let sortLz = ["T1", "T2", "T3", "T4"];
tSort.sort((a, b) => {
  return sortLz.indexOf(a.belongs) - sortLz.indexOf(b.belongs);
});
// export const allSelection = checkList.concat(strList).concat(boneList).concat(noseList).concat(limList).concat(neckList)
export const allSelection = tSort.concat(limList).concat(neckList);
