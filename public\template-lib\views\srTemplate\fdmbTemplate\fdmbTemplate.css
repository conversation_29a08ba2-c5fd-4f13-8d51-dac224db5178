#fdmb1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}

#fdmb1 .fdmb-content {
  min-height: 100%;
  padding: 8px 12px;
  padding-bottom: 0;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}

#fdmb1 .c-item {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

#fdmb1 .bro-w {
  display: flex;
  align-items: center;
  border: 1px solid #C8D7E6;
  padding: 4px 10px;
  background: #EBEEF5;
}

#fdmb1 input[type="text"] {
  border: 1px solid #DCDFE6;
  width: 60px;
  padding-left: 6px;
  margin: 0 6px;
}

#fdmb1 .cdfi-title {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  width: 92px;
  background: #EBEEF5;
  border-right: 1px solid #C8D7E6;
}

#fdmb1 .cdfi-title .w-con {
  width: 100%;
  padding-left: 12px;
}

#fdmb1 .cdfi-title .w-con:hover {
  background-color: #E4E7ED;
}

#fdmb1 .cdfi-content {
  padding: 8px 12px;
}

#fdmb1 .bro-n{
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 8px;
}

#fdmb1 input[type="radio"], #fdmb1 input[type="checkbox"] {
  vertical-align: middle;
}

#fdmb1 .to-cd {
  cursor: pointer;
}

#fdmb1 label+label {
  margin-left: 30px;
}

#fdmb1 .c-item+.c-item {
  margin-top: 12px;
}
#fdmb1 .ml-10{
  margin-left: 10px;
}
#fdmb1 .ml-30{
  margin-left: 30px;
}