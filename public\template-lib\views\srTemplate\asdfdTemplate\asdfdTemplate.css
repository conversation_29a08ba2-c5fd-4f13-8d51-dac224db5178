/*-------公共--------*/
.w-con {
  display: inline-block;
  vertical-align: middle;
}

/*-------边距--------*/
.mb-8 {
  margin-bottom: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-32 {
  margin-left: 32px;
}
.ml-34 {
  margin-left: 34px;
}
.ml-36 {
  margin-left: 36px;
}
.ml-44 {
  margin-left: 44px;
}
.ml-74 {
  margin-left: 74px;
}
.ml-105 {
  margin-left: 105px;
}
.ml-109 {
  margin-left: 109px;
}

/*-------宽度--------*/
.wd-80 {
  width: 80px;
}

/*-------高度--------*/
.lh-28 {
  line-height: 28px;
}

/*-------总体布局--------*/
ul {
  overflow: auto;
}
#asdfd1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#asdfd1 .asdfd-content {
  min-height: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#asdfd1 .con-box {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#asdfd1 .box-row {
  padding: 8px 12px;
  box-sizing: border-box;
}
#asdfd1 .bor-w {
  border: 1px solid #C8D7E6;
  padding: 0 8px;
  background: #EBEEF5;
}
#asdfd1 .inp-sty {
  height: 28px;
  border: 1px solid #DCDFE6;
}
#asdfd1 [type="checkbox"],#asdfd1 [type="radio"] {
  vertical-align: middle;
}
#asdfd1 [type="text"] {
  padding: 0 2px;
}
#asdfd1 .tc {
  text-align: center;
}