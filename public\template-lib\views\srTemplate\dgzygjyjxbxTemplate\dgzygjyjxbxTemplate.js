$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
// 添加双击事件节点
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#dgzygjyjxbx1 .dgzyCg-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      // 有住院号显示住院号
      if(publicInfo.inpatientNo) {
        curElem.find('.outpatientNo-wrap').hide();
        curElem.find('.inpatientNo-wrap').show();
      } else {
        curElem.find('.outpatientNo-wrap').show();
        curElem.find('.inpatientNo-wrap').hide();
      }
      initViewCon();
    } else {
      initSelectData()
    }
  }
}


var xbbwa_selectData = [
  { 
    id: 'dgzygjyj-rt-1',
    width: '160',
    optionList: [
      {id: '满意',title: '满意'},
      {id: '不满意',title: '不满意'},
    ]
  },
  {
    id: 'dgzygjyj-rt-11',
    width: '260',
    optionList: [
      {id: ' ',title: ' '},
      {id: '轻度炎症',title: '轻度炎症'},
      {id: '中度炎症',title: '中度炎症'},
      {id: '重度炎症',title: '重度炎症'},
    ]
  }
]
var idData= ['dgzygjyj-rt-2', 'dgzygjyj-rt-3', 'dgzygjyj-rt-4', 'dgzygjyj-rt-5', 'dgzygjyj-rt-6', 'dgzygjyj-rt-7', 'dgzygjyj-rt-8', 'dgzygjyj-rt-9', 'dgzygjyj-rt-10' ]
function initSelectData() {
  idData.forEach(item => {
    xbbwa_selectData.push({
      id: item,
      width: '120',
      optionList: [
        {id: '有',title: '有'},
        {id: '无',title: '无'},
      ]
    })
  })
  xbbwa_selectData.forEach(item => {
    initInpAndSel(item.id, item.optionList, item.width)
  });
}

function initInpAndSel(idList, optionList,lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click:function(obj){
    this.elem.val(obj.title);
    if(lenVal === 0){
      // changeSel(obj.title);
    }
    },
    style: lenVal !== 0 ? `width: ${lenVal}px;` : 'width: calc(100% - 146px)'
  })
}



// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.dgzyCg-view [data-key]').each(function () {
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      if(publicInfo[key] || idAnVal) {
        var value = publicInfo[key] || idAnVal;
        if(key === 'reqHospital') {
          value = value === '东莞市中医院' ? '总院' : '分院';
        }
        if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
          value = value + ' ' + publicInfo['affirmTime'];
        }
        if(key === 'mensesStatus') {
          value = value === '1' ? '无' : value === '2' ? '是': '';
        }
        if(value) {
          $(this).html(value);
        }
        break;
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  curElem.find('.dgzyCg-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    var len = '';
    // if(rptImageList.length > 2) {
    //   len = rptImageList.length >= 4 ? 4 : rptImageList.length;
    // }
    rptImageList = rptImageList.slice(0, 2);
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="item-img">
          <img src="${image.src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      // curElem.find('.dgzyCg-view .rpt-img-ls').attr('data-len', len);
      curElem.find('.dgzyCg-view .rpt-img-ls').html(imgHtml);
      curElem.find('.dgzyCg-view .rpt-img-ls').css('display', 'block');
    }
  }
}


/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = curElem.find('.dgzyCg-edit [data-key="impression"]').val() || '';
  rtStructure.recommendation = curElem.find('.dgzyCg-edit [data-key="recommendation"]').val() || '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼观察
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}