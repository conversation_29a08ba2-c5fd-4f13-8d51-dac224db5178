#gfemyrs1 {
  position: relative;
  font-size: 16px;
  min-height: 100%;
  margin: 0 auto;
  padding: 8px 12px;
  background-color: #F5F7FA;
  * {
    font-family: 宋体;
  }
  .rt-sr-header, .rt-sr-footer, .show-in-view {
    display: none;
  }
  .hpt-info{
    position: relative;
    text-align: center;
    img {
      position: absolute;
      left: 80px;
      top: 0;
    }
    h1 {
      font-weight: 800;
      font-size: 24px;
      color: #000000;
      line-height: 32px;
    }
    h5 {
      font-size: 14px;
      color: #000000;
      line-height: 20px;
    }
    h2 {
      font-weight: 800;
      font-size: 18px;
      color: #000000;
      line-height: 26px;
    }
  }
  .patient-info {
    position: relative;
    border-top: 1px solid #999999;
    border-bottom: 1px solid #999999;
    margin-top: 20px;
    padding: 4px 0;
    display: flex;
    flex-wrap: wrap;
    li {
      width: 30%;
      flex-grow: 0;
      flex-shrink: 0;
      line-height: 30px;
      font-size: 16px;
      &:nth-of-type(3n-2) {
        width: 40%;
      }
      &.out {
        position: absolute;
        right: 0;
        top: -30px;
        width: auto;
      }
    }
    .lb {
      color: #000000;
      line-height: 23px;
    }
    .val {
      color: #000000;
      line-height: 23px;
    }
  }
  .rt-sr-body {
    margin-top: 8px;
    .detail-item {
      margin-top: 8px;
    }
    .separator {
      margin-top: 8px;
      border-top: 1px solid #999999;
    }
    .imgs {
      margin-top: 8px;
      img {
        margin-right: 10px;
        margin-bottom: 10px;
        width: 320px;
        height: 200px;
        object-fit: contain;
      }
    }
    .lb {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 23px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }
  .rt-sr-footer {
    position: absolute;
    left: 50px;
    right: 50px;
    bottom: 20px;
    .btm-info {
      border-top: 1px solid #999999;
      border-bottom: 1px solid #999999;
      padding: 5px 0;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      img {
        height: 36px;
        vertical-align: top;
      }
    }
    .itm {
      font-size: 16px;
    }
    .lb {
      color: #000000;
      line-height: 36px;
    }
    .val {
      color: #000000;
      line-height: 36px;
    }
    .tip {
      font-size: 10px;
      color: #000000;
      line-height: 17px;
      &.flex {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .textarea {
    width: 100%;
    height: 200px;
    padding: 4px;
    margin-top: 4px;
    font-size: 16px;
    background: #FFFFFF;
    border-radius: 3px;
    border: 1px solid #C0C4CC;
  }
  table {
    width: 100%;
    margin-top: 4px;
    border-color: #C0C4CC;
    table-layout: fixed;
    background: #FFFFFF;
  }
  tr {
    height: 36px;
  }
  th {
    text-align: center;
  }
  td {
    padding: 4px 8px;
    text-align: center;
    &.with-select {
      position: relative;
      &:after {
        content: "";
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-left: 1px solid #999;
        border-bottom: 1px solid #999;
        transform: rotate(-45deg) translateY(-100%);
      }
    }
    input[type="text"] {
      border: 1px solid #eee;
      width: 100%;
      height: 26px;
      border-radius: 3px;
      color: #303133;
      padding-left: 8px;
    }
  }
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
  * {
    font-family: 宋体;
    font-size: 16px;
  }
}

[isview="true"] #gfemyrs1 {
  padding: 30px 50px 120px;
  width: 780px;
  min-height: 1100px;
  background: white;
  .rt-sr-header, .rt-sr-footer, .show-in-view {
    display: block;
  }
  .exam-result {
    display: flex;
    font-size: 16px;
    line-height: 23px;
    word-break: break-all;
  }
  tr {
    height: 30px;
  }
  td {
    &.with-select {
      &:after {
        display: none;
      }
    }
  }
}