var rtDialog = null;
var isDialodDragInit = false;

/**
 * 绘制弹框
 * @param {object} option 弹框的参数
 * {boolean} modal 是否需要遮罩，默认false
 * {object} style 弹框body样式
 * {string} wholeStyle 整体弹框样式,appendTop为true时样式必须写在这里
 * {string} title 弹框标题
 * {string} content 弹框主体内容
 * {boolean} appendTop 是否插入到顶部窗口，默认false，在当前窗口打开
 * {boolean} canUseOtherContent 当弹窗不带遮罩时，允许操作弹框外(主页面)的内容，默认false
 */
function drawDialog(option) {
  // modal, style, title, content
  var styleObj = {
    'width': '400px',
    'background': '#fff',
    'border-radius': '4px',
    'border': '1px solid #EEE',
    'position': 'relative',
    'z-index': '1002',
    // 'top': '50px',
    // 'left': '50%',
    // 'transform': 'translateX(-50%)'
    'margin': '50px auto 0 auto'
  }
  if(option.style) {
    for(var prop in option.style) {
      styleObj[prop] = option.style[prop];
    }
  }
  var style = JSON.stringify(styleObj).replace(/{|}|\'|\"/ig, '');
  style = style.replace(/,/ig, ';');
  var bodyStyle = '';
  if(option.bodyStyle) {
    bodyStyle = JSON.stringify(option.bodyStyle).replace(/{|}|\'|\"/ig, '');
    bodyStyle = bodyStyle.replace(/,/ig, ';');
  }
  var nowDate = Date.now();
  var dialog = '<div class="rt-dialog-wrap rt-dialog-wrap-'+nowDate+'" style="position:fixed;top:0;right:0;bottom:0;left:0;z-index: 9999;">';
  if(option.wholeStyle) {
    dialog += '<style>'+option.wholeStyle+'</style>';
  }
  dialog += '<div class="rt-dialog-con" style="'+style+'">';
  // 头部
  dialog += '<div class="rt-dialog-header" style="height: 40px;padding:12px;cursor: move;">';
  if(option.title) {
    dialog += '<span class="rt-dialog-title" style="float:left;color:#000;font-size:14px;">'+option.title+'</span>'
  }
  dialog += '<span class="rt-dialog-close" style="float:right;color:#909399;font-size:16px;cursor:pointer;" date-val="'+nowDate+'">X</span>'
  dialog += '</div>';
  // 主体内容
  dialog += '<div class="rt-dialog-content" style="padding:12px;'+bodyStyle+'">';
  if(option.content) {
    dialog += option.content;
  }
  dialog += '</div>';

  // 底部
  if(option.isShowFooter) {
    dialog += '<div class="rt-dialog-footer" style="text-align: center;padding: 10px;">';
    dialog += '<button class="rt-dialog-confirm" style="padding: 0 8px;height: 28px;line-height: 28px;display: inline-block;border-radius: 3px;background: #1E9493;color: #fff;font-size: 12px;margin-right: 12px;cursor: pointer;">'+option.confirmBtnName+'</button>';
    dialog += '<button class="rt-dialog-close" style="padding: 0 8px;height: 28px;line-height: 28px;display: inline-block;border-radius: 3px;background: #1E9493;color: #fff;font-size: 12px;margin-right: 12px;cursor: pointer;" date-val="'+nowDate+'">'+option.cancelBtnName+'</button>';
    dialog += '</div>';
  }
  dialog += '</div>';
  dialog += '</div>';
  var targetCon = option.appendTop ? $(window.top.document.body) : $(document.body);  //目标插入容器
  targetCon.append($(dialog));
  var curDialog = targetCon.find('.rt-dialog-wrap-' + nowDate);  //找到当前弹框
  if(option.modal) {
    $(curDialog).append(drawModal(option.clickModalToClose === undefined ? true : option.clickModalToClose, option.closeFun, option.appendTop));
  }
  $(curDialog).find(".rt-dialog-close").click(function() {
    if(option.closeFun && typeof option.closeFun === 'function') {
      option.closeFun();
    }
    removeRtDialog(option.appendTop);
    return false;
  })
  if(option.isShowFooter) {
    $(curDialog).find(".rt-dialog-confirm").click(function() {
      if(option.confirmFun && typeof option.confirmFun === 'function') {
        option.confirmFun();
      }
      removeRtDialog(option.appendTop);
      return true;
    })
  }
  // rtDialog = $('.rt-dialog-wrap' + '-' + nowDate);
  rtDialog = $(curDialog);
  // 操作弹窗外的内容，即调整弹窗的定位只填满弹窗
  if(!option.modal && option.canUseOtherContent) {
    resetDialogWrapStyle(curDialog, option.appendTop);
  }
  dialodDrag(option); // 弹框拖拽移动
}

/**
 * 绘制遮罩层
 * @param {boolean} clickModal 是否可以通过点击modal关闭Dialog
 * @param {element} appendTop 是否插入到顶部窗口，默认false，在当前窗口打开
 * @return modalEle
 */
function drawModal(clickModal, closeFun, appendTop) {
  var modal = '<div style="position:fixed;top:0;right:0;bottom:0;left:0;background:#000;opacity:0.7;filter:Alpha(opacity=70);z-index: 1001;"></div>';
  var modalEle = $(modal);
  if(clickModal) {
    modalEle.click(function() {
      if(closeFun && typeof closeFun === 'function') {
        closeFun();
      }
      removeRtDialog(appendTop);
    })
  }
  return modalEle;
}

// 关闭移除弹框,appendTop表示是否插入到顶部窗口
function removeRtDialog(appendTop) {
  var targetCon = appendTop ? $(window.top.document.body) : $(document.body);  //目标插入容器
  var dialogCloseWrap = targetCon.find(".rt-dialog-close:last");
  // 兼容旧模板
  if(appendTop && !dialogCloseWrap.length) {
    dialogCloseWrap = $(document.body).find(".rt-dialog-close:last");
    targetCon = $(document.body);
  }
  var dateVal = dialogCloseWrap.attr('date-val');
  targetCon.find('.rt-dialog-wrap' + '-' + dateVal).remove();
  // rtDialog.remove();
  // rtDialog = null;
}

// 弹框拖拽移动,appendTop弹窗插入到顶部窗口
function dialodDrag(option) {
  var appendTop = option.appendTop;
  var canUseOtherContent = option.canUseOtherContent;
  var modal = option.modal;
  if (isDialodDragInit) {
    return;
  }
  isDialodDragInit = true;
  var isActive = false;
  var mousedownX, mousedownY, positionLeft, positionTop;
  var rtDialogDoc = appendTop ? window.top.window.document : window.document;
  var dialogWrap;
  $(rtDialogDoc).on('mousedown', '.rt-dialog-header', function(e) {
    dialogWrap = $(this).closest('.rt-dialog-wrap');
    mousedownX = e.clientX;
    mousedownY = e.clientY;
    var dialogCon = $(rtDialogDoc).find('.rt-dialog-con');
    positionLeft = !modal && canUseOtherContent ? parseInt(dialogWrap.css('left')) : parseInt(dialogCon.css('left'));
    positionTop = !modal && canUseOtherContent ? parseInt(dialogWrap.css('top')) : parseInt(dialogCon.css('top'));
    isActive = true;
  });
  $(rtDialogDoc).on('mousemove', function(e) {
    if (!isActive) {
      return;
    }
    // 底下内容可操作则直接改变wrap的位置
    if(!modal && canUseOtherContent) {
      dialogWrap.css('left', positionLeft + (e.clientX - mousedownX) + 'px');
      dialogWrap.css('top', positionTop + (e.clientY - mousedownY) + 'px');
    } else {
      var dialogCon = $(rtDialogDoc).find('.rt-dialog-con');
      dialogCon.css('left', positionLeft + (e.clientX - mousedownX) + 'px');
      dialogCon.css('top', positionTop + (e.clientY - mousedownY) + 'px');
    }
  });
  $(rtDialogDoc).on('mouseup', function(e) {
    isActive = false;
  });
}

// 允许操作弹窗外的内容，即调整弹窗的定位只填满弹窗
function resetDialogWrapStyle(dialogWrap, appendTop) {
  var targetCon = appendTop ? $(window.top.document.body) : $(document.body);  //目标插入容器
  dialogWrap = targetCon.find(dialogWrap);
  var dialogCon = dialogWrap.find('.rt-dialog-con');
  dialogCon.css('margin', '0')
  var dialogWidth = dialogCon.width();
  var dialogHeight = dialogCon.height();
  // 处理百分比的宽度和高度
  if(!dialogCon.attr('new-width')) {
    dialogCon.attr('new-width', dialogWidth);
    dialogCon.css('width', dialogWidth + 'px');
  }
  if(!dialogCon.attr('new-height')) {
    dialogCon.attr('new-height', dialogHeight);
    dialogCon.css('height', dialogHeight + 'px');
  }
  // 设置dialogWrap的新尺寸
  dialogWrap.css({
    'width': dialogWidth + 'px',
    'height': dialogHeight + 'px'
  });
  var win = appendTop ? window.top : window;
  var offsetTop = $(win).scrollTop(); // 获取滚动条的位置
  dialogWrap.css({
    'position': 'absolute',
    'top': (($(win).height() - dialogWrap.outerHeight()) / 2) + offsetTop + 'px',
    'left': (($(win).width() - dialogWrap.outerWidth()) / 2) + 'px'
  });
}