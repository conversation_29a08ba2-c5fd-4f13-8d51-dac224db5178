@charset "UTF-8";
#hbetzrxwsx1 {
  font-size: 16px;
  min-height: 100%;
  margin: 0 auto;
  background-color: #F5F7FA;
}
#hbetzrxwsx1 * {
  font-family: 宋体;
}
#hbetzrxwsx1 input, #hbetzrxwsx1 textarea {
  font-size: 16px;
  border: 1px solid #C0C4CC;
  line-height: 22px;
  border-radius: 3px;
  padding: 2px 8px;
}
#hbetzrxwsx1 .input-top-wrap {
  padding: 8px 20px;
  border-bottom: 2px solid #000;
}
#hbetzrxwsx1 .input-top-wrap .row {
  margin-top: 4px;
}
#hbetzrxwsx1 .input-top-wrap .row:first-child {
  margin-top: 0;
}
#hbetzrxwsx1 .input-top-wrap .lb {
  max-width: 80px;
  display: inline-block;
  text-align: left;
}
#hbetzrxwsx1 .input-top-wrap input {
  width: 320px;
}
#hbetzrxwsx1 .input-body-wrap {
  padding: 8px 20px;
}
#hbetzrxwsx1 .input-body-wrap .row {
  display: flex;
  margin-top: 4px;
}
#hbetzrxwsx1 .input-body-wrap .row:first-child {
  margin-top: 0;
}
#hbetzrxwsx1 .input-body-wrap .row.hd {
  font-weight: bold;
}
#hbetzrxwsx1 .input-body-wrap .row.line4 {
  height: 94px;
}
#hbetzrxwsx1 .input-body-wrap .row.line2 {
  height: 50px;
}
#hbetzrxwsx1 .input-body-wrap .cell {
  height: 100%;
  margin-left: 8px;
}
#hbetzrxwsx1 .input-body-wrap .cell:first-child {
  margin-left: 0;
}
#hbetzrxwsx1 .input-body-wrap .cell:nth-of-type(1) {
  flex: 200;
}
#hbetzrxwsx1 .input-body-wrap .cell:nth-of-type(2) {
  flex: 150;
  display: flex;
}
#hbetzrxwsx1 .input-body-wrap .cell:nth-of-type(3) {
  flex: 200;
}
#hbetzrxwsx1 .input-body-wrap .cell:nth-of-type(4) {
  flex: 80;
}
#hbetzrxwsx1 .input-body-wrap .over-mark {
  width: 16px;
  flex-shrink: 0;
  flex-grow: 0;
  color: #000;
  display: flex;
  align-items: center;
}
#hbetzrxwsx1 .input-body-wrap input, #hbetzrxwsx1 .input-body-wrap textarea {
  width: 100%;
  height: 100%;
}
#hbetzrxwsx1 .remark-wrap {
  padding: 0 20px;
}
#hbetzrxwsx1 .remark-wrap p {
  margin-bottom: 8px;
}
#hbetzrxwsx1 .remark-wrap textarea {
  width: 100%;
  height: 204px;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont {
  position: relative;
  text-align: center;
  padding-bottom: 34px;
  border-bottom: 2px solid #000;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont h1, #hbetzrxwsx1 .info-top-wrap .tl-cont h3, #hbetzrxwsx1 .info-top-wrap .tl-cont h2 {
  color: #000;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont h1 {
  font-family: 仿宋;
  font-size: 21px;
  line-height: 1;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont h3 {
  margin-top: 3px;
  font-family: 仿宋;
  font-size: 21px;
  line-height: 1;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont h2 {
  margin-top: 20px;
  font-size: 26px;
  line-height: 1;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont .code {
  position: absolute;
  right: 0;
  top: 27px;
  height: 36px;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont .code img {
  height: 36px;
}
#hbetzrxwsx1 .info-top-wrap .tl-cont .code-text {
  position: absolute;
  right: 0;
  bottom: 8px;
  font-weight: bold;
  font-size: 18px;
}
#hbetzrxwsx1 .info-top-wrap .base-info {
  margin-top: 6px;
  overflow: hidden;
  line-height: 26px;
}
#hbetzrxwsx1 .info-top-wrap .base-info .item {
  width: 25%;
  float: left;
}
#hbetzrxwsx1 .info-top-wrap .base-info .item.per50 {
  width: 50%;
}
#hbetzrxwsx1 .info-top-wrap .base-info .item.per75 {
  width: 75%;
}
#hbetzrxwsx1 .info-top-wrap .base-info .item.per100 {
  width: 100%;
}
#hbetzrxwsx1 .info-top-wrap .base-info .lb {
  max-width: 80px;
  display: inline-block;
  text-align: left;
}
#hbetzrxwsx1 .bottom-info-cont {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}
#hbetzrxwsx1 .bottom-info-cont.name-cont {
  padding: 8px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
}
#hbetzrxwsx1 .bottom-info-cont.time-cont {
  margin-top: 8px;
}
#hbetzrxwsx1 .bottom-info-cont img {
  vertical-align: middle;
  width: 64px;
  height: 32px;
  object-fit: cover;
}
#hbetzrxwsx1 .rt-sr-footer {
  position: absolute;
  left: 50px;
  right: 50px;
  bottom: 30px;
}
#hbetzrxwsx1 .rt-sr-header {
  margin-bottom: 16px;
  display: none;
}
#hbetzrxwsx1 .rt-sr-footer {
  display: none;
}

[isview=true] #hbetzrxwsx1 {
  padding: 30px 50px 100px;
  width: 780px;
  min-height: 1100px;
  background: white;
}
[isview=true] #hbetzrxwsx1 .input-top-wrap, [isview=true] #hbetzrxwsx1 .input-body-wrap, [isview=true] #hbetzrxwsx1 .remark-wrap {
  padding-left: 0;
  padding-right: 0;
}
[isview=true] #hbetzrxwsx1 .rt-sr-header, [isview=true] #hbetzrxwsx1 .rt-sr-footer {
  display: block;
}
[isview=true] #hbetzrxwsx1 .result-wrap .text-con {
  width: 67px;
  text-align: right;
  display: inline-block;
}
[isview=true] #hbetzrxwsx1 .text-con {
  white-space: pre-line;
  line-height: 22px;
}
[isview=true] #hbetzrxwsx1 .input-top-wrap {
  display: flex;
}
[isview=true] #hbetzrxwsx1 .input-top-wrap .row {
  flex: 1;
  margin-top: 0;
}
[isview=true] #hbetzrxwsx1 .input-body-wrap {
  padding-top: 10px;
}
[isview=true] #hbetzrxwsx1 .input-body-wrap .row {
  margin-top: 8px;
  padding-left: 8px;
  padding-right: 8px;
}
[isview=true] #hbetzrxwsx1 .input-body-wrap .row.hd {
  padding-bottom: 10px;
  border-bottom: 2px solid #000;
}
[isview=true] #hbetzrxwsx1 .input-body-wrap .row:first-child {
  margin-top: 0;
}
[isview=true] #hbetzrxwsx1 .remark-wrap {
  margin-top: 8px;
}
[isview=true] #hbetzrxwsx1 .remark-wrap p {
  font-weight: bold;
}
[isview=true] #hbetzrxwsx1 .remark-wrap .text-con {
  line-height: 28px;
}
[isview=true] #hbetzrxwsx1 .over-mark {
  align-items: start;
  margin-left: 36px;
}

/*# sourceMappingURL=hbetzrxwsxTemplate.css.map */
