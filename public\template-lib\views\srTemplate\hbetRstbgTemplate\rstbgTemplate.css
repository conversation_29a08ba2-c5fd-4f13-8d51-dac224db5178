.singleDisEditReport.main-page{
  min-width: unset;
}
#rstbg1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#rstbg1 * {
  font-family: '宋体';
}
#rstbg1 .rstbg-edit{
  padding: 8px 12px;
}
#rstbg1 .rstbg-edit .editor-wrap{
  display: flex;
  align-items: center;
}
#rstbg1 .rstbg-edit .editor-wrap .showInt {
  position: relative;
  background: #fff;
  width: 320px;
}
#rstbg1 .rstbg-edit .editor-wrap .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#rstbg1 .rstbg-edit .edit-content{
  margin-top: 8px;
}
#rstbg1 .addImg {
  cursor: pointer;
  text-align: center;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 4px 0;
  width: 122px;
  height: 120px;
  position: relative;
  margin-top: 8px;
  transition: transform 0.2s;
}

#rstbg1 .addImg:hover {
  color: #1885F2;
  border: 1px dashed #1885F2;
  transform: translate3d(0, -1px, -2px);
}

#rstbg1 #imageInput {
  position: absolute;
  opacity: 0;
  height: 120px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  font-size: 0;
  cursor: pointer;
}
#rstbg1 #karyotypeInput {
  position: absolute;
  opacity: 0;
  height: 120px;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  font-size: 0;
  cursor: pointer;
}

#rstbg1 .addImg {
  line-height: 112px;
  font-size: 24px;
  color: #909399;
  height: 120px;
  border-radius: 6px;
}

#rstbg1 #preview, #rstbg1 #typePreview {
  display: flex;
  flex-wrap: wrap;
  width: 120px;
  display: none;
  margin-top: 8px;
  position: relative;
}

#rstbg1 #typePreview img, #rstbg1 #preview img {
  width: 120px;
  height: 120px;
  border-radius: 6px;
  border: 1px solid #C0C4CC;
  position: relative;
}

#rstbg1 #preview .delImg, #rstbg1 #typePreview .delImg {
  width: 18px;
  height: 18px;
  position: absolute;
  top: 4px;
  right: 4px;
  color: #fff;
  font-size: 10px;
  line-height: 14px;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  background: #E64545;
  border-radius: 50%;
  z-index: 8888;
}

#rstbg1 .del-icon {
  cursor: pointer;
}
#rstbg1 input[type="text"], .inp-sty {
  width: 100%;
  padding: 6px 12px;
  height: 28px;
  margin-top: 8px;
  vertical-align: middle;
  border-radius: 4px !important;
  border: 1px solid #dcdfe6 !important;
}
#rstbg1 .rstbg-view {
  display: none;
}
[isview="true"] #rstbg1 .rstbg-edit {
  display: none;
}
[isview="true"] #rstbg1 .rstbg-view {
  display: flex;
}
#rstbg1 .rstbg-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 116px 56px;
  flex-direction: column;
  position: relative;
}
#rstbg1 .rstbg-view #divisionPreviewImg, #rstbg1 .rstbg-view #karyotypeImg {
  display: none;
}
#rstbg1 .rstbg-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#rstbg1 .rstbg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}
#rstbg1 .rstbg-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
 span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
 }
}
#rstbg1 .rstbg-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
#rstbg1 .rstbg-view .hos-tit{
  font-family: '仿宋';
  font-size: 21px;
  line-height: 26px;
  font-style: normal;
  color: #000;
}
#rstbg1 .rstbg-view .sub-tit{
  font-size: 26px;
  color: #000;
  font-family: '宋体';
  margin-top: 20px;
  line-height: 30px;
}
#rstbg1 .rstbg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#rstbg1 .rstbg-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-line;
  word-break: break-all;
}
.flex-column {
  margin: 8px auto;
  width: 520px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: 10px;
}
.desc-content{
  width: 520px;
  white-space: pre-line;
  word-break: break-all;
  font-size: 16px;
  color: #000000;
  line-height: 23px;
  margin-top: 4px;
}
#rstbg1 .rstbg-view .report-img, #rstbg1 .rstbg-view .type-img{
  display: flex;
  align-items: center;
  justify-content: center;
}
#rstbg1 .rstbg-view .report-img img {
  height: 205px;
  width: 202px;
  display: block;
  object-fit: contain;
}
#rstbg1 .rstbg-view .type-img img {
  height: 280px;
  width: 510px;
  display: block;
  object-fit: contain;
}
#rstbg1 .rstbg-view .black-txt {
  color: #000;
  font-size: 16px;
}
#rstbg1 .rstbg-view .red-txt {
  color: #000000;
  font-size: 16px;
}
#rstbg1 .rstbg-view .bold {
  font-weight: bold;
}
#rstbg1 .rstbg-view .info-i {
  width: 160px;
  display: flex;
  flex-wrap: wrap;
}
#rstbg1 .rstbg-view .info-i + .info-i {
  margin-left: 8px;
}
#rstbg1 .rstbg-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#rstbg1 .rstbg-view .view-patient .p-item {
  margin-top: 10px;
}
#rstbg1 .rstbg-view .rt-sr-footer {
  position: absolute;
  bottom: 28px;
  left: 30px;
  right: 30px;
}
#rstbg1 .rstbg-view .rt-sr-body {
  padding-top: 8px;
}
#rstbg1 .rstbg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#rstbg1 .rstbg-view .desc-con {
  width: 100%;
}

#rstbg1 .rstbg-view .memo-wrap {
  padding: 4px 0;
  border-top: 1px solid #999;
  font-size: 12px;
  line-height: 17px;
}
.memo-content{
  white-space: pre-line;
  word-break: break-all;
}
#rstbg1 .rstbg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  display: flex;
  justify-content: space-between;
  margin-top: 0px;
}
#rstbg1 .rstbg-view .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#rstbg1 .rstbg-view .reporter-i.w120 {
  width: 120px;
}
#rstbg1 .rstbg-view .reporter-i.w150 {
  width: 150px;
}
#rstbg1 .rstbg-view .reporter-i span {
  width: 70px;
}
#rstbg1 .rstbg-view .reporter-i span:last-child {
  flex: 1;
}
#rstbg1 .rstbg-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#rstbg1 .rstbg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#rstbg1 .rstbg-view .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}
#rstbg1 .rstbg-view .tip-wrap .tip-text {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#rstbg1 .rstbg-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  margin-bottom: 12px;
  display: none;
  padding-left: 86px;
}
#rstbg1 .rstbg-view .item-img {
  width: 240px;
  height: 180px;
  border: 1px solid #eee;
  margin-bottom: 8px;
}
#rstbg1 .rstbg-view .item-img:nth-child(odd){
  margin-right: 12px;
}
#rstbg1 .rstbg-view .item-img:nth-child(even){
  margin-left: 12px;
}
#rstbg1 .rstbg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#rstbg1 .p-item:nth-child(2) .text-size,#rstbg1 .p-item:nth-child(3) .text-size,#rstbg1 .p-item:nth-child(4) .text-size {
  display: none;
}
#rstbg1 .p-item:nth-child(2) .editor-area,#rstbg1 .p-item:nth-child(3) .editor-area {
  min-height: 72px;
}
#rstbg1 .p-item:nth-child(2) textarea,#rstbg1 .p-item:nth-child(3) textarea {
  height: 72px;
}
#rstbg1 .rstbg-view .blh-tit{
  text-align: right;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #rstbg1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #rstbg1 .rstbg-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #rstbg1 .rstbg-view .view-head,
[entry-type="5"] #rstbg1 .rstbg-view .view-patient,
[entry-type="5"] #rstbg1 .rstbg-view .tip-wrap {
  display: none;
}
[entry-type="5"] #rstbg1 .rstbg-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #rstbg1 .rstbg-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
