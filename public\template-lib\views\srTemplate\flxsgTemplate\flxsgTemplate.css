#flxsg1 {
  font-size: 14px;
}
#flxsg1 input[type="radio"],
#flxsg1 input[type="checkbox"] {
  min-height: unset;
}
#flxsg1 .flx-txt {
  width: 70px;
  text-align: right;
  padding-top: 8px;
  display: inline-block;
  color: #303133;
}
#flxsg1 .inl-flx-txt {
  width: 58px;
  text-align: right;
  display: inline-block;
}
#flxsg1 .line26 {
  line-height: 26px;
}
#flxsg1 .orange {
  color: #E68A2E;
}
#flxsg1 .w-80 {
  display: inline-block;
  width: 80px;
}
#flxsg1 .inp-sty {
  width: 60px;
  border-radius: 3px;
  padding: 3px 10px;
  outline: none;
  border: 1px solid #DCDFE6;
}
[isview="true"] #flxsg1 .w-block-pd {
  padding: 0;
  border: none;
  background-color: unset;
  min-height: unset;
}
#flxsg1 .show-view {
  display: none;
}
[isview="true"] #flxsg1 .show-view {
  display: inline;
}
[isview="true"] #flxsg1 .w-block-pd:not(.jj-wrap):not(.sg-wrap) {
  display: flex !important;
}
[isview="true"] #flxsg1 .p-item + .p-item{
  margin-top: 8px;
}
[isview="true"] #flxsg1 .inl-flx-txt{
  width: unset;
}
[isview="true"] #flxsg1 .p-item{
  color: #000;
}
[isview="true"] #flxsg1 .jj-wrap .inl-flx-txt {
  color: #303133;
}
[isview="true"] #flxsg1 .flx-txt {
  padding-top: 3px;
}