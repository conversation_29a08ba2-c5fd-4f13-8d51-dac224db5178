var busTypeMap = {
  '1': 'examNo',
  '2': 'applyNo',
  '3': 'scheduleId',
  '4': 'studyNo',
}
var urlParams = {
  patternId: '',
  examClass: '',
  examSubClass: '',
  patientSource: '',
  organName: '',
  patDocId: '',
  processTrace: ''
}
var paramBusName = 'applyNo';
var patternInfo = {};
var patternList = [];
var rtStructure = null;
var sourcePrefixUrl = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
var isPreview = location.href.indexOf('preview=1') > -1;
$(function() {
  for(var key in urlParams) {
    urlParams[key] = getParamByName(key) || '';
  }
  if(!urlParams.patternId) {
    $wfMessage({
      content: 'patternId不能为空'
    })
    return;
  }
  // 技术流程触屏端样式
  if(urlParams.processTrace === '1') {
    $('#blKnowCase').css({
      background: 'transparent',
      zoom: '1.3'
    });
    $('#blKnowCase *').css('color', '#143666');
  }
  // 多种业务类型，通过busType确定参数名,默认applyNo
  if(urlParams.busType) {
    paramBusName = busTypeMap[urlParams.busType];
  }
  addLoadCoverLayer();
  if(urlParams.patDocId) {
    getParDoc(urlParams.patDocId);
  } else {
    // 获取模板列表
    getPatternList();
  }
  
  rtStructure = new RtStructure('#blKnowCase .main-content');  //实例化
  console.log('rtStructure', rtStructure);
})

// 获取模板列表
function getPatternList() {
  if(!urlParams.patternId) {
    $wfMessage({
      content: '参数patternId不能为空'
    })
    removeLoadCoverLayer();
    return;
  }
  var params = {
    patternId: urlParams.patternId,
    patternType: '8',  //病理知识库
    organName: urlParams.organName,
    examClass: urlParams.examClass,
    examSubclass: urlParams.examSubClass,
    patientSource: urlParams.patientSource,
    pageSize: 0,
    pageNo: 0,
    mainFlag: '1'
  }
  params[paramBusName] = urlParams.busId;
  getPatternListHandler(params, {
    successCb: function(res) {
      var data = res.result || [];
      patternList = data;
      if(data.length) {
        var patDocId = data[0].patDocId;
        if(urlParams.patternId && urlParams.patternId !== data[0].patternId) {
          for(var i = 0; i < data.length; i++) {
            if(data[i].patternId === urlParams.patternId) {
              patDocId = data[i].patDocId;
              break;
            }
          }
        }
        urlParams.patDocId = patDocId;
        getParDoc(patDocId);
      } else {
        $wfMessage({
          content: '未找到相关模板'
        })
        removeLoadCoverLayer();
      }
    },
    errorCb: function(res) {
      $wfMessage({
        content: '内容加载失败'
      })
      removeLoadCoverLayer();
    }
  })
}

// 获取文档内容, isChange是否是切换模板
function getParDoc(patDocId) {
  if(!patDocId) {
    $wfMessage({
      content: '模板不存在'
    })
    removeLoadCoverLayer();
    return;
  }
  $("#blKnowCase .main-content").html('');
  var params = {
    patDocId: patDocId,
  }
  patternInfo = {};
  getParDocHtmlHandler(params, {
    successCb: function(res) {
      var data = res.result || {};
      patternInfo = data;
      var html = data.patternDoc;
      if(window.loadTemplateScriptAndCss) {
        window.loadTemplateScriptAndCss(html);
      }
      if(html) {
        drawContentHtml(html);
      } else {
        $wfMessage({
          content: '模板出错'
        })
        removeLoadCoverLayer();
      }
    },
    errorCb: function() {
      $wfMessage({
        content: '模板出错'
      })
      removeLoadCoverLayer();
    }
  })
}

// 渲染页面
function drawContentHtml(html) {
  // 病理知识库只需看，无需编辑
  rtStructure.previewStructureReport({htmlContent:html, resultData:[], loadViewScript:true, publicInfo: {}, srcUrl: sourcePrefixUrl});
  removeLoadCoverLayer();
}