import Vue from 'vue'
import {
    Input,
    Button,
    ButtonGroup,
    Form,
    FormItem,
    Checkbox,
    CheckboxGroup,
    Tree,
    Divider,
    MessageBox,
    Message,
    Collapse,
    CollapseItem,
    Popover,
    Radio,
    Select,
    Option,
    RadioButton,
    RadioGroup,
    Table,
	TableColumn,
    Pagination,
    Loading,
    DatePicker,
    Tooltip,
    Tag,
    Dialog,
    Timeline,
	TimelineItem,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Tabs,
    TabPane,
} from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(Loading.directive);
Vue
    .use(Input)
    .use(Button)
    .use(ButtonGroup)
    .use(Form)
    .use(FormItem)
    .use(Checkbox)
    .use(CheckboxGroup)
    .use(Tree)
    .use(Divider)
    .use(Collapse)
    .use(CollapseItem)
    .use(Popover)
    .use(Radio)
    .use(Select)
    .use(Option)
    .use(RadioButton)
    .use(RadioGroup)
    .use(Table)
    .use(TableColumn)
    .use(Pagination)
    .use(DatePicker)
    .use(Tooltip)
    .use(Tag)
    .use(Dialog)
    .use(Timeline)
    .use(TimelineItem)
    .use(Dropdown)
    .use(DropdownMenu)
    .use(DropdownItem)
    .use(Tabs)
    .use(TabPane)

    Vue.prototype.$loading = Loading.service;
    Vue.prototype.$message = Message;
    Vue.prototype.$confirm = MessageBox.confirm;
    Vue.prototype.$alert = MessageBox.alert;


