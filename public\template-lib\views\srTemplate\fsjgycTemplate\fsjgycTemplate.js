$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var mmUnit = 'mm',sqcmUnit = 'cm^2';
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      
    } else {

    }
  }
  initPage();
}
// 页面初始化
function initPage() {
  // 切换单选按钮的选中状态
  toggleRadioCheckStatus();
  changeMainMenu();
  changeTpGbTab();
  initEnterEv();
  let pageIdList = ['lcns','jz','pfj','xxf'];
  for(let pageId of pageIdList) {
    changeMenuSubItem(pageId);
  }
}
function changeMainMenu() {
  // 主框架
  $('#fsjgyc1 .fsjgyc-h .menu-group').on('click', '.menu-item', function() {
    if($(this).hasClass('act')) {
      return;
    }
    $(this).addClass('act').siblings('.menu-item').removeClass('act');
    $('.fsjgyc-b .menu-con').removeClass('act');
    var tabName = $(this).attr('menu-id');
    $('.fsjgyc-b .menu-con[id="'+tabName+'"]').addClass('act');
  })
}
// 胎盘与宫壁间
function changeTpGbTab() {
  $('#tp .tp-box-h .switch-group').on('click', '.switch-item', function() {
    if($(this).hasClass('act')) {
      return;
    }
    $(this).addClass('act').siblings('.switch-item').removeClass('act');
    $('.tp-box-b .switch-con').removeClass('act');
    var tabName = $(this).attr('switch-id');
    $('.tp-box-b .switch-con[id="'+tabName+'"]').addClass('act');
  })
}
// 页面内切换超声描述和超声提示
function changeMenuSubItem(pageId) {
  $('#'+pageId+' .menu-sub-group').on('click', '.menu-sub-item', function() {
    if($(this).hasClass('act')) {
      return;
    }
    $(this).addClass('act').siblings('#'+pageId+' .menu-sub-item').removeClass('act');
    $('#'+pageId+' .menu-sub-con').removeClass('act');
    var tabName = $(this).attr('menu-sub-id');
    $('#'+pageId+' .menu-sub-con[id="'+tabName+'"]').addClass('act');
  })
}
// 初始化回车键跳转输入框事件
function initEnterEv() {
  let keyDownTabList = ['tp','lcns','jz','pfj','xxf','gs','qt','ts1'];
  for(let pageId in keyDownTabList) {
    keydown_to_tab(pageId);
  }
}

// 胎盘
function getTpDes() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let pIdNeedMmUnit = ['fsyctp-rt-10','fsyctp-rt-20'];
  let cItem1NeedMmUnit = ['fsyctp-rt-2','fsyctp-rt-8'];
  $('#tp .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let mmStrList = [];
      str = parData.name;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        let mmValList = [];
        if(cItem1NeedMmUnit.indexOf(cItem1.id)>-1) {
          str += cItem1.val + mmUnit;
        }else if(pIdNeedMmUnit.indexOf(pId)>-1) {
          // 胎盘与宫壁可见、胎盘切面可见
          childD2.map(function(cItem2) {
            mmValList.push(cItem2.val + mmUnit);
          })
          str += cItem1.val;
          let mmStr = mmValList.length ? str += mmValList.join('x') : '';
          mmStr ? mmStrList.push(mmStr) : '';
          str = '';
        }else {
          str += cItem1.val;
          // 胎盘与宫壁间
          if(pId === 'fsyctp-rt-35') {
            childD2.map(function(cItem2) {
              let childD3 = cItem2.child || [];
              mmValList = [];
              childD3.map(function(cItem3) {
                mmValList.push(cItem3.val + mmUnit);
              })
              str += cItem2.val;
              let mmStr = mmValList.length ? str += mmValList.join('x') : '';
              mmStr ? mmStrList.push(mmStr) : '';
              str = '';
            })
          }
        }
      })
      mmStrList.length ? str += mmStrList.join('，') : '';
      str ? strList.push(str) : '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}

// 颅、侧脑室——超声描述1
function getLcnsMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['大小径线','宽度径线','左径线','右径线'];
  $('#lcns-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let mmStrList = [];
      pId === 'fsyclcnsms1-rt-1' ? strList.push(parData.name) : str = parData.name;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        let mmValList = [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.name;
          childD2.map(function(cItem2) {
            if(cItem2.name==='左'||cItem2.name==='右') {
              let childD3 = cItem2.child || [];
              str += cItem2.name;
              childD3.map(function(cItem3) {
                mmValList.push(cItem3.val + mmUnit);
              })
              let mmStr = mmValList.length ? str += mmValList.join('x') : '';
              if(mmStr) {
                mmStrList.push(mmStr);
                str = '';
                mmValList = [];
              }
            }else {
              mmValList.push(cItem2.val + mmUnit);
            }
          })
          let mmStr = mmValList.length ? str += mmValList.join('x') : '';
          if(mmStr) {
            mmStrList.push(mmStr);
            str = '';
          }
        }else {
          str += cItem1.name;
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            if(mmUnitNameList.indexOf(cItem2.name)>-1) {
              str += cItem2.name;
              childD3.map(function(cItem3) {
                mmValList.push(cItem3.val + mmUnit);
              })
              let mmStr = mmValList.length ? str += mmValList.join('x') : '';
              if(mmStr) {
                mmStrList.push(mmStr);
                str = '';
              }
            }else {
              str += cItem2.name;
              str ? strList.push(str) : '';
              str = '';
            }
          })
          if(str) {
            strList.push(str);
            str = '';
          }
        }
      })
      mmStrList.length ? str += mmStrList.join('，') : '';
      if(str) {
        strList.push(str);
        str = '';
      }
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 颅、侧脑室——超声描述2
function getLcnsMs2Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let cItem1NeedUnit = ['fsyclcnsms2-rt-5','fsyclcnsms2-rt-11','fsyclcnsms2-rt-15'];
  $('#lcns-csms2 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let mmStrList = [];
      str = parData.name;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        let mmValList = [];
        if(cItem1NeedUnit.indexOf(cItem1.id)>-1) {
          str += cItem1.val;
          let unit = cItem1.id==='fsyclcnsms2-rt-5' ? sqcmUnit : mmUnit;
          mmValList = [];
          childD2.map(function(cItem2) {
            mmValList.push(cItem2.val + unit);
          })
          let mmStr = mmValList.length ? str += mmValList.join('x') : '';
          mmStr ? mmStrList.push(mmStr) : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
        }
      })
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 脊椎——超声描述1
function getJz1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#jz-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        let inpValList = [];
        if(pId==='fsycjzms1-rt-28' || cItem1.id==='fsycjzms1-rt-54') {
          let childD2 = cItem1.child || [];
          childD2.map(function(cItem2) {
            if(cItem1.id==='fsycjzms1-rt-54') {
              inpValList.push(cItem1.val + cItem2.val + mmUnit);
            }else {
              inpValList.push(cItem2.val);
            }
          })
        }else {
          str += cItem1.val;
        }
        inpValList.length ? str += inpValList.join('-') : '';
        str ? strList.push(str) : '';
        str = '';
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 脊椎——超声描述2
function getJzMs2Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#jz-csms2 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        if(parData.name==='大小') {
          inpValList.push(cItem1.val + mmUnit);
        }else if(cItem1.id==='fsycjzms2-rt-29'){
          let childD2 = cItem1.child || [];
          childD2.map(function(cItem2) {
            str += cItem1.val + cItem2.val + '水平';
          })
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      str ? strList.push(str) : '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 皮肤、颈——超声描述1
function getPfjMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['左眼眶内径','右眼眶内径','宽度径线约','见囊性肿块大小','厚度径线','范围径线'];
  $('#pfj-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.name + '：';
          inpValList = [];
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          })
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            str += cItem2.val;
            if(mmUnitNameList.indexOf(cItem2.name)>-1) {
              inpValList = [];
              childD3.map(function(cItem3) {
                inpValList.push(cItem3.val + mmUnit);
              })
              inpValList.length ? str += inpValList.join('x') : '';
            }
          })
          str ? strList.push(str) : '';
          str = '';
        }
      })
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 心、胸、腹——超声描述1
function getXxfMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['径线','最大径线','径线'];
  $('#xxf-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.val;
          inpValList = [];
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          })
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            if(mmUnitNameList.indexOf(cItem2.name)>-1) {
              str += cItem2.val;
              inpValList = [];
              childD3.map(function(cItem3) {
                inpValList.push(cItem3.val + mmUnit);
              })
              inpValList.length ? str += inpValList.join('x') : '';
            }
          })
          str ? strList.push(str) : '';
          str = '';
        }
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 肝、肾
function getGsDes() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['液性暗区','低回声团','光团','左侧','右侧','厚径','最大径线'];
  $('#gs .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.val;
          inpValList = [];
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          })
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
        }
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 其他
function getQtDes() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['胎儿上唇见分叉征，裂缝','胎儿膀胱暗区','肿块','光团','光斑','液性暗区','胎儿阴囊内见液性暗区'];
  $('#qt .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        if(mmUnitNameList.indexOf(parData.name)>-1){
          let tailStr = parData.val === '胎儿上唇见分叉征，裂缝' ? '，鼻孔不对称' : '';
          inpValList.push(cItem1.val + mmUnit + tailStr);
        }else {
          str += cItem1.val;
          let childD2 = cItem1.child || [];
          childD2.map(function(cItem2) {
            if(mmUnitNameList.indexOf(cItem1.name)>-1) {
              inpValList.push(cItem2.val + mmUnit);
            }else {
              str += cItem2.val;
              let childD3 = cItem2.child || [];
              if(mmUnitNameList.indexOf(cItem2.name)>-1) {
                childD3.map(function(cItem3) {
                  inpValList.push(cItem3.val + mmUnit);
                })
              }
              inpValList.length ? str += inpValList.join('x') : '';
              str ? strList.push(str) : '';
              str = '';
              inpValList = [];
            }
          })
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
          inpValList = [];
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      str ? strList.push(str) : '';
      str = '';
      inpValList = [];
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}

// 颅、侧脑室——超声提示
function getLcnsTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#lcns-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        if(pId === 'fsyclcnsts-rt-11') {
          str = '';
          str = cItem1.val + parData.val;
        }else if(pId === 'fsyclcnsts-rt-15') {
          str = '';
          str = cItem1.val;
        }else {
          str += cItem1.val;
        }
      })
      str ? strList.push(str) : '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 脊椎——超声提示
function getJzTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#jz-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
        childD2.map(function(cItem2) {
          str += cItem2.val;
          str ? strList.push(str) : '';
          str = '';
        })
      })
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 皮肤、颈——超声提示
function getPfjTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#pfj-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      str ? strList.push(str) : '';
      str = '';
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        str = cItem1.val;
        childD2.map(function(cItem2) {
          let childD3 = cItem2.child || [];
          str += cItem2.val;
          childD3.map(function(cItem3) {
            str += cItem3.val;
          })
        })
        str ? strList.push(str) : '';
        str = '';
      })
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 心、胸、腹——超声提示
function getXxfTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#xxf-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      if(pId==='fsycxxfts-rt-1') {
        str ? strList.push(str) : '';
        str = '';
      }
      childD1.map(function(cItem1) {
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
        let childD2 = cItem1.child || [];
        childD2.map(function(cItem2) {
          str += cItem2.val;
          str ? strList.push(str) : '';
          str = '';
        })
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 提示（1）
function getTs1Imp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let pIdNeedMmUnit = ['fsycts1-rt-1','fsycts1-rt-4'];
  $('#ts1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        let unit = pIdNeedMmUnit.indexOf(pId)>-1 ? mmUnit : '';
        str += cItem1.val;
        childD2.map(function(cItem2) {
          str += cItem2.val + unit;
          str ? strList.push(str) : '';
          str = '';
        })
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 提示（2）
function getTs2Imp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#ts2 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}

// 所有描述拼接的内容
function getAllDescContent() {
  var allDesc = '', descList = [];
  // 胎盘
  var tpDes = getTpDes();
  tpDes && descList.push(tpDes);

  // 颅、侧脑室——超声描述1
  var lcnsMs1Des = getLcnsMs1Des();
  lcnsMs1Des && descList.push(lcnsMs1Des);
  // 颅、侧脑室——超声描述2
  var lcnsMs2Des = getLcnsMs2Des();
  lcnsMs2Des && descList.push(lcnsMs2Des);

  // 脊椎——超声描述1
  var jzMs1Des = getJz1Des();
  jzMs1Des && descList.push(jzMs1Des);
  // 脊椎——超声描述2
  var jzMs2Des = getJzMs2Des();
  jzMs2Des && descList.push(jzMs2Des);
  
  // 皮肤、颈——超声描述1
  var pfjMs1Des = getPfjMs1Des();
  pfjMs1Des && descList.push(pfjMs1Des);
  
  // 心、胸、腹——超声描述1
  var xxfMs1Des = getXxfMs1Des();
  xxfMs1Des && descList.push(xxfMs1Des);
  
  // 肝、肾
  var gsDes = getGsDes();
  gsDes && descList.push(gsDes);

  // 其他
  var qtDes = getQtDes();
  qtDes && descList.push(qtDes);

  allDesc = descList.length ? descList.join('。\n') + '。' : '';
  // console.log(allDesc);
  return allDesc;
}

// 所有诊断拼接的内容
function getAllImpContent() {
  var allImp = '', impList = [];

  // 脊椎——超声提示
  var jzTsImp = getJzTsImp();
  jzTsImp && impList.push(jzTsImp);

  // 颅、侧脑室——超声提示
  var lcnsTsImp = getLcnsTsImp();
  lcnsTsImp && impList.push(lcnsTsImp);

  // 皮肤、颈——超声提示
  var pfjTsImp = getPfjTsImp();
  pfjTsImp && impList.push(pfjTsImp);

  // 心、胸、腹——超声提示
  var xxfTsImp = getXxfTsImp();
  xxfTsImp && impList.push(xxfTsImp);

  // 提示（1）
  var ts1Imp = getTs1Imp();
  ts1Imp && impList.push(ts1Imp);

  // 提示（2）
  var ts2Imp = getTs2Imp();
  ts2Imp && impList.push(ts2Imp);

  allImp = impList.length ? impList.join('。\n') + '。' : '';
  // console.log(allImp);
  return allImp;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getAllDescContent();
  rtStructure.impression = getAllImpContent();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}