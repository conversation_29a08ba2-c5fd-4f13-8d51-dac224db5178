/* 宽度 */
.mb-2 {
  margin-bottom: 2px;
}
.mb-4 {
  margin-bottom: 4px;
}
.ml-2 {
  margin-left: 2px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-18 {
  margin-left: 18px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-22 {
  margin-left: 22px;
}
.ml-36 {
  margin-left: 36px;
}
.ml-40 {
  margin-left: 40px;
}
.ml-50 {
  margin-left: 50px;
}
.ml-56 {
  margin-left: 56px;
}
.ml-68 {
  margin-left: 68px;
}
.ml-82 {
  margin-left: 82px;
}
.ml-92 {
  margin-left: 92px;
}
/* 宽度 */
.wd-68 {
  width: 68px!important;
}
.wd-80 {
  width: 80px!important;
}
.wd-96 {
  width: 96px!important;
}
.wd-112 {
  width: 112px;
}
.wd-120 {
  width: 120px!important;
}
.wd-180 {
  width: 180px!important;
}
.wd-188 {
  display: inline-block;
  width: 188px !important;
}
.wd-200 {
  width: 200px!important;
}
.wd-249 {
  width: 249px!important;
}

/* 字体 */
.f-bold {
  font-weight: bold;
  color: #000;
}
/* MR食管癌页面 */
#fjsgact1 {
  width: 100%;
  height: 100%;
  position: relative;
  font-size: 14px;
  margin: 0 auto;
  padding-top: 40px;
}
#fjsgact1 .fjsgact-h {
  height: 40px;
  line-height: 40px;
  background: #E8F3FF;
  font-size: 18px;
  font-weight: bold;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid #DCDFE6;
}
#fjsgact1 input[type="radio"], #fjsgact1 input[type="checkbox"] {
  vertical-align: middle;
}
/* 编辑页面 */
#fjsgact1 .fjsgact-edit {
  position: relative;
  height: 100%;
  background: #fff;
}
#fjsgact1 .fjsgact-edit .rpt-con {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
#fjsgact1 .fjsgact-b {
  flex: 1;
  overflow: auto;
}
#fjsgact1 .w-auto {
  width: 960px;
  margin: 0 auto;
}
#fjsgact1 .con-title {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  line-height: 28px;
  margin-bottom: 8px;
}
#fjsgact1 .con-b {
  border-right: 1px solid #DCDFE6;
  border-left: 1px solid #DCDFE6;
  padding: 12px;
}
#fjsgact1 .row-item {
  display: flex;
  margin-bottom: 8px;
}
#fjsgact1 .row-tit {
  display: inline-block;
  width: 112px;
  text-align: right;
  line-height: 22px;
}
#fjsgact1 .row-box {
  flex: 1;
  padding: 7px 12px;
  box-sizing: border-box;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#fjsgact1 .box-item {
  display: flex;
  line-height: 28px;
}
#fjsgact1 .sub-box {
  background: #EBEEF5;
  border: 1px solid #DCDFE6;
  padding: 11px 12px;
}
#fjsgact1 .inp-sty{
  width: 60px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 0 3px;
}
#fjsgact1 .add-Jbqylbj {
  height: 20px;
  width: 20px;
  vertical-align: bottom;
}
#fjsgact1 .add-Jbqylbj:hover {
  cursor: pointer;
}
#fjsgact1 .fjsgact-f {
  background: #E8F3FF;
  border-top: 1px solid #DCDFE6;
  padding: 8px 12px;
  color: #000000;
  line-height: 22px;
}
#fjsgact1 .fjsgact-f .imp-tx {
  width: 80px;
  text-align: right;
  font-size: 18px;
  font-weight: bold;
  padding-right: 12px;
}
#fjsgact1 .con-flex {
  display: flex;
  line-height: 28px;
}
#fjsgact1 .flex-item {
  flex: 1;
}
#fjsgact1 .block-lb {
  display: block;
}
#fjsgact1 .fjsm-sty {
  font-size: 13px;
  line-height: 28px;
}
#fjsgact1 .textarea-sty {
  width: 100%;
  height: 64px;
  resize: vertical;
}
#fjsgact1 .del-btn {
  cursor: pointer;
  font-weight: bold;
  margin-left: 3px;
  vertical-align: middle;
}
#fjsgact1 .footer-view {
  display: none;
}
/* 预览样式 */
[isView="true"] #fjsgact1 .row-tit {
  width: 140px!important;
}
[isView="true"] #fjsgact1 .nfq-con .row-tit {
  width: 127px!important;
}
[isView="true"] #fjsgact1 .box-view,[isView="true"] #fjsgact1 .sub-box,[isView="true"] #fjsgact1 .hight-block {
  padding: 0!important;
  background: none!important;
  border: none!important;
}
[isview="true"] #fjsgact1 .w-block {
    min-height: unset;
}
[isview="true"] #fjsgact1 .bbwz-con,[isview="true"] #fjsgact1 .xhtdqh-con,[isview="true"] #fjsgact1 .yczy-con,[isview="true"] #fjsgact1 .qtzx-con {
  white-space: pre-line;
  line-height: 22px;
}
[isview="true"] #fjsgact1 .footer-edit {
  display: none!important;
}
[isview="true"] #fjsgact1 .footer-view {
  display: flex;
  min-height: 40px;
  background: #E8F3FF;
  border-top: 1px solid #E6E6E6;
  padding: 12px 44px;
  font-size: 14px;
  color: #000;
}
[isview="true"] #fjsgact1 .row-box {
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}
#fjsgact1 .footer-view .bt-tit {
  font-weight: bold;
}
#fjsgact1 .footer-view .bt-imp {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
[isview="true"] #fjsgact1 .add-Jbqylbj {
  display: none;
}
[isview="true"] #fjsgact1 .nfq-item {
  white-space: pre-wrap;
  line-height: 28px;
}