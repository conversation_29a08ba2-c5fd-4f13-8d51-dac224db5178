#fzblmds1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}

#fzblmds1 .mds-view {
  display: none;
}

[isview="true"] #fzblmds1 .mds-edit {
  display: none;
}

[isview="true"] #fzblmds1 .mds-view {
  display: block;
}

#fzblmds1 input[type="text"], #fzblmds1 .inp-sty {
  padding: 0 12px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #C0C4CC;
}

#fzblmds1 .mds-edit {
  color: #000;
  padding: 10px 24px;
}

#fzblmds1 .row-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

#fzblmds1 .row-item+.row-item {
  margin-top: 4px;
}

#fzblmds1 .exam-tbl {
  width: 100%;
  border-collapse: collapse;
  border-color: #C0C4CC;
  background: #ffffff;
  box-sizing: border-box;
  font-family: "宋体", sans-serif !important;
}

#fzblmds1 .exam-tbl tr th {
  padding: 8px;
  font-family: "宋体", sans-serif !important;

}

#fzblmds1 .exam-tbl tr td {
  padding: 0 8px;
  line-height: 36px;
  font-family: "宋体", sans-serif !important;

}

#fzblmds1 .exam-tbl tr td span {
  font-family: inherit;
}

#fzblmds1 .showInt {
  position: relative;
  width: 100%;
  background: #fff;
}

#fzblmds1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}

#fzblmds1 .showInt.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}

#fzblmds1 .textarea-stl {
  width: 100%;
  height: 200px;
  margin-top: 4px;
  padding: 4px 12px;
  font-size: 16px;
}

#fzblmds1 .mds-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 16px;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 20px;
  color: #000;
}

#fzblmds1 .mds-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 20px;
  text-align: center;
}

#fzblmds1 .mds-view .head-img {
  position: absolute;
  top: 30px;
  left: 110px;
  width: 64px;
  height: 64px;
}

#fzblmds1 .mds-view .head-img img {
  width: 100%;
  height: 100%;
}

#fzblmds1 .mds-view .right-num {
  position: absolute;
  top: 76px;
  right: 56px;
}

#fzblmds1 .mds-view .right-num .black-txt {
  display: inline-block;
  max-width: 148px;
}

#fzblmds1 .mds-view .page-tit {
  font-size: 24px;
  font-weight: 800;
  font-family: "宋体", sans-serif !important;
}

#fzblmds1 .mds-view .page-tit.sub-tit {
  font-size: 18px;
}

#fzblmds1 .mds-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#fzblmds1 .mds-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: "宋体", sans-serif !important;
}

#fzblmds1 .mds-view .gray-txt .bold {
  font-weight: 600;
}

#fzblmds1 .mds-view .black-txt {
  color: #000;
  font-size: 16px;
  font-family: "宋体", sans-serif !important;
}

#fzblmds1 .mds-view .bold {
  font-weight: bold;
}

#fzblmds1 .mds-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}

#fzblmds1 .mds-view .info-i+.info-i {
  margin-left: 8px;
}

#fzblmds1 .mds-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

#fzblmds1 .mds-view .view-patient .p-item {
  align-items: center;
}

#fzblmds1 .mds-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}

#fzblmds1 .mds-view .rt-sr-footer {
  position: absolute;
  bottom: 56px;
  left: 56px;
  right: 56px;
}

#fzblmds1 .mds-view .report-wrap {
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#fzblmds1 .mds-view .tip-wrap {
  margin-top: 8px;
  font-size: 12px;
}

#fzblmds1 .mds-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}

#fzblmds1 .mds-view .reporter-i [data-key] {
  flex: 1;
}

#fzblmds1 .mds-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}

#fzblmds1 .mds-view .reporter-i+.reporter-i {
  margin-left: 8px;
}

#fzblmds1 .wid-88 {
  width: 100%;
  min-width: 88px;
}

#fzblmds1 .wid-130 {
  width: 100%;
  min-width: 130px;
}

#fzblmds1 .wid-304 {
  width: 304px;
}

#fzblmds1 .wid-320 {
  width: 320px;
}

#fzblmds1 .pdrl-4 {
  padding: 0 4px !important;
}

#fzblmds1 .mt-8 {
  margin-top: 8px;
}

#fzblmds1 .mt-12 {
  margin-top: 12px;
}