
export const RT_SESSION_TOKEN = 'rt_session_token';  
  
// export const RT_SESSION_TOKEN_WEBPACS = 'rt_session_token_webpacs';    

export const DEFAULT_ENTRY_PEOPLE = "default_entry_people";  //录入者

export const GENERAL_USERINFO = "general_userInfo";  //用户

export const SR_ALL_CONTENT = "sr_all_content";  //操作过的所有结构化节点json

export const SR_ALL_PATDOCID = "sr_all_patDocId";  //操作过的所有结构化模板patDocId数组

export const SR_QUERY_PARAMS = "sr_query_params";  //结构化路由参数

export const SR_REDIS_PARAMS = "sr_redis_params";  //结构化存储数据时的参数

export const SR_SAVE_PARAMS = "sr_save_params";  //结构化原始数据,从接口取的

export const SR_CURENT_REDIS = "sr_curent_redis";  //缓存当前报告的同类型的数据

export const SR_CLOSE_WIN_AFTER_PRINT = "sr_close_win_after_print";  //勾选打印完成后关闭预览框

export const SIGN_APP_CLIENT_ID = "sign_app_client_id";  // 签名框架在APP端的唯一识别码
