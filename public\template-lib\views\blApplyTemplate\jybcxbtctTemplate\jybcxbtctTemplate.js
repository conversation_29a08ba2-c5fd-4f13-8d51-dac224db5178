$(function () {
  window.initHtmlScript = initHtmlScript;
})
// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
  // 'cdjc-rt-21': { label: '送检医生', wt: '1', hisKey: 'optName' }
}
// 需要更新的申请单数据
// applyItemList根据blApplyDictionary.js的全局变量addSampleList确定，id直接为''//已添加的标本数据 
// gmSampleList根据blApplyDictionary.js的全局变量addExamItemList确定，id直接为''//已添加的项目数据 
var saveApplyParams = {
  'name': { label: '姓名', id: 'jybc-rt-2' },
  'ageAndMonth': { label: '年龄', id: '' },
  'sex': { label: '性别', id: 'jybc-rt-9' },
  'sickId': { label: '病人ID', id: 'jybc-rt-15' },
  'outpatientNo': { label: '门诊号', id: 'jybc-rt-17' },
  'inpatientNo': { label: '住院号', id: 'jybc-rt-19' },
  'bedNo': { label: '床号', id: 'jybc-rt-21' },
  'identityCard': { label: '身份证号', id: 'jybc-rt-25' },
  'birthPlace': { label: '籍贯', id: 'jybc-rt-27' },
  'phoneNumber': { label: '手机号', id: 'jybc-rt-23' },
  'patientSource': { label: '病人来源', id: 'jybc-rt-11' },
  'lastMensesDate': { label: '末次月经', id: 'jybc-rt-33' },
  'noLastMensesDate': { label: '绝经', id: 'jybc-rt-34' },
  'birthDate': { label: '出生日期', id: 'jybc-rt-13' },
  'infectiousFlag': {
    label: '是否传染病', ids: [
      { id: 'jybc-rt-29', index: 13, value: '1' },
      { id: 'jybc-rt-30', index: 13, value: '0' },
    ]
  },
  'mailingAddress': { label: '通信地址', id: 'jybc-rt-38' },
  'bljcExamClasses': { label: '检查类别', id: '' },
  'examSubClass': { label: '检查子类', id: 'jybc-rt-40' },
  'scheduledDate': { label: '预约时间', id: 'jybc-rt-45' },
  'reqHospital': { label: '申请医院', id: 'jybc-rt-47' },
  'bljcReqDept': { label: '申请科室', id: '' },
  'bljcReqPhysician': { label: '申请医生', id: '' },
  'freezeFlag': {
    label: '是否冰冻', ids: [
      { id: 'jybc-rt-42', index: 13, value: '1' },
      { id: 'jybc-rt-43', index: 13, value: '0' },
    ]
  },
  'chargeFlag': {
    label: '是否缴费', ids: [
      { id: 'jybc-rt-57', index: 13, value: '1' },
      { id: 'jybc-rt-58', index: 13, value: '0' },
    ]
  },
  'bljcApplyItemList': { label: '检查项目', id: '' },
  'bljcGmSampleList': { label: '标本信息', id: '' },
  'charges': { label: '项目费用', id: 'jybc-rt-55' },
  'infection': { label: '传染病', id: 'jybc-rt-31' },
  'surgerySeen': { label: '临床所见', id: 'jybc-rt-75' },
  'examMotive': { label: '检查目的', id: 'jybc-rt-60' },
  'marriedFlag': { label: '婚姻状况', id: 'jybc-rt-36' },
  'clinDiag': { label: '临床诊断', id: 'jybc-rt-77' },
  'medRecord': {
    label: '病人病历', ids: [
      { id: 'jybc-rt-64', value: '口服避孕药或避孕针' },
      { id: 'jybc-rt-65', value: '子宫环' },
      { id: 'jybc-rt-66', value: '子宫切除' },
      { id: 'jybc-rt-67', value: '手术后' },
      { id: 'jybc-rt-68', value: '人乳头瘤病毒' },
      { id: 'jybc-rt-69', value: '不正常流血' },
      { id: 'jybc-rt-70', value: '怀孕妇' },
      { id: 'jybc-rt-71', value: '哺乳期 ' },
      { id: 'jybc-rt-72', value: '产后四个月 ' },
      { id: 'jybc-rt-73', value: '其他' },
    ]
  },
}
var marriedObj = {
  '0': '未知',
  '1': '已婚',
  '2': '未婚',
}
var specialExamSubClassList = ['组织学（冰冻切片）']
var specialExamClassList = ['冰冻切片检查与诊断']
var rtStructure = null;
var curElem = null;
var genderList = []; // 性别列表
// var nationList = []; // 民族列表
var patientSourceList = []; // 病人来源列表
var examItemList = []; // 检查项目列表
var examSubClassList = []; // 检查子类列表
var hospitalList = []; // 申请医院列表
var deptList = []; // 申请科室列表
var dictUsersList = []; // 申请医生列表
var gmSamplePartList = []; // 标本部位列表
var gmTypeNameList = []; // 标本类别列表
var gmSampleNameList = []; // 标本名称列表
var gmFixativeList = []; // 固定液列表
var defaultGmFixative = [];  //默认固定液
var deptCode = ''; // 科室参数
var examClass = ''; // 检查类别
var selList = [];
var fixativeGap = ''; // 固定时间与离体时间差
var firstLoad = true;  //新增
var setValFromApply = false;  //是否直接读取申请单数据
var disabledDate = [];
var sampleType = null; // 标本类别默认值,是否冰冻
var disableCode = []; // 用code禁用对应文本框
var unEditSampleSta = []; // 标本信息状态
var unEditAttrExcept = []; // 不受限制的字段
var hideAttrCode = []; // 申请单隐藏字段属性集配置
var dropdown = null;
var bbmcDropdown = null;
var pageParams = {
  pageNo: 1,
  pageSize: 20
};
var valueLike = '';
var curInpEle = null;
var bbmcId = '';
var isSavedReport = false; //模板是否填写过
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = {};
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#jybcxbtct1 .jybc-view"),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    setValFromApply = rtStructure.enterOptions.setValFromApply || false;
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    resultData = rtStructure.enterOptions ? rtStructure.enterOptions.resultData : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView();
    } else {
      // initDatePicker();  //初始化日期插件
      initPage();
    }
    showItemAndGm();
  }
  // setValByHis(hisAndFormMap);
}

// 预览页加载
function initView() {
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ?
    rtStructure.enterOptions.publicInfo : {};
  var radios = document.getElementsByName('jj');
  var checkboxs = document.getElementsByName('view-brbl');
  curElem.find('.jybc-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'jybc-rt-32') {
      $('#jybc-rt-34').prop('checked') ? radios[0].setAttribute('checked', true) : '';
    } else if (key === 'jybc-rt-63') {
      let medRecordList = enterData && enterData.medRecord ? enterData.medRecord.split(',') : [];
      for (let i = 0; i < checkboxs.length; i++) {
        if (medRecordList && medRecordList.length > 0) {
          medRecordList.indexOf(checkboxs[i].value) !== -1 ? checkboxs[i].setAttribute('checked', true) : ''
        }
      }
    } else if (value) {
      key === 'marriedFlag' ? $(this).text(marriedObj[value] || '') : $(this).text(value);
    } else {
      $(this).text('')
    }
  })
  // 标本信息回显
  var gmList = enterData.gmSampleList || [];
  var applyItem = enterData.applyItemList ? enterData.applyItemList[0] : {};
  if (gmList.length > 0) {
    // let { itemName = '' } = applyItem
    // let { examSubClass = '' } = enterData;
    // if ((specialExamSubClassList.findIndex(item => item === examSubClass) !== -1) || (specialExamClassList.findIndex(itemStr => itemStr === itemName) !== -1)) {
    gmList = gmList.sort((a, b) => {
      return Number(a.sampleNo) - Number(b.sampleNo)
    })  
    $('#sampleTable').hide();
      for(let i = 0; i < gmList.length; i++){
        let { sampleName = '', barcodeNo = '',inVitroDateTime = '', fixedDateTime = '', applyNo = '',sampleNo = ''} = gmList[i];
        if(!barcodeNo){
          barcodeNo = applyNo + sampleNo;
        }
        let sampleStr = `<div style="display: block;line-height:18px">${i+1}、{${barcodeNo || '&nbsp;&nbsp;'}，${sampleName || ''}`
        inVitroDateTime ?  sampleStr += `，离体：${inVitroDateTime || ''}` : ''
        fixedDateTime ?  sampleStr += `，固定：${fixedDateTime || ''}` : ''
        sampleStr += `}</div>`
        $('#sampleInfo').append(sampleStr);
      }
      $('#jyyb').css('height','inherit');
      
    // } else {
    //   // let length = gmList.length + 1;
    //   $('#jyyb').css('border', 'none');
    //   for (let i = 0; i < gmList.length; i++) {
    //     let { sampleName = '', samplePart = '', inVitroDateTime = '', fixedDateTime = '' } = gmList[i];
    //     let tr = "<tr>" +
    //       "<td class='item-sty mwd-68'>" + (i + 1) + "</td>" +
    //       "<td class = 'item-sty mwd-208'>" + sampleName + "</td>" +
    //       "<td class='item-sty mwd-80'>" + samplePart + "</td>" +
    //       "<td class='item-sty mwd-72'>" + 1 + "</td>" +
    //       "<td class='item-sty mwd-144'>" + inVitroDateTime + "</td>" +
    //       "<td class='item-sty mwd-144'>" + fixedDateTime + "</td>" +
    //       "</tr>"
    //     $('#sampeleTable').append(tr);
    //   }
    // }
  } else {
    $('#sampeleTable').parent().hide()
  }
}

// 编辑页初始化
function initPage() {
  var form = layui.form;
  form.render();
  $('#blhtzzsyd1 .date-wrap02').prop('readonly', true);
  // 年龄-岁输入框存在值时，只取数字部分
  // let ageYear = $('#jybc-rt-4').val() || '';
  // let regAge = ageYear.replace(/[^\d.]/g, '');
  // $('#jybc-rt-4').val(regAge);
  splitAgeAndUnit($('#jybc-rt-4').val());
  // 末次月经是否必填
  let sexVal = $('#jybc-rt-9').val() || '';
  lastMensesDateIsReq(sexVal);

  examClass = getParamByName('examClass');
  let deptCode = getParamByName('reqDeptCode');
  let examSubClass = getParamByName('examSubClass') || $('#jybc-rt-40').val() || '';
  getGmSettings();
  initDatePicker();
  initDatePicker(true);
  getGenderList();
  // getNationList();
  getPatientSourceList();
  getHospitalList();
  getDeptList();
  if (!$('#jybc-rt-49').val() && deptCode) {
    let filterDeptList = deptCode ? deptList.filter(item => item.deptCode === deptCode) : [];
    let deptName = filterDeptList.length ? filterDeptList[0].deptName : '';
    $('#jybc-rt-49').val(deptName);
    $('#jybc-rt-49').attr('deptCode', deptCode);
  }
  getDictUsersList(deptCode);
  getGmSamplePartList();
  getGmTypeNameList();
  // getGmSampleNameListV2();
  getGmFixativeList();
  getExamItemList(examSubClass);
  getExamSubClassList2();
  gmFixativeGap();
  initSelFun();
  // showTableVal('input[type="text"]');
  validateIdCard();
  validatePhoneNum();
  // 根据年龄推算出生日期事件
  var ageList = [
    { id: 'jybc-rt-4', evType: 'blur' },
    { id: 'jybc-rt-5', evType: 'change' },
    { id: 'jybc-rt-6', evType: 'blur' }
  ];
  for (var i = 0; i < ageList.length; i++) {
    let id = ageList[i].id;
    let evType = ageList[i].evType;
    getDateByAge(id, evType)
  }
  // 添加回车事件
  var evIdList = ['jybc-rt-15', 'jybc-rt-17', 'jybc-rt-19'];
  for (var i = 0; i < evIdList.length; i++) {
    enterEvFun(evIdList[i]);
  }
  // 模糊查询--检查项目、申请科室、申请医生、标本名称、标本部位、标本类别
  var inpSelIdList = ['jybc-rt-49', 'jybc-rt-51', 'jybc-rt-53'];
  for (var i = 0; i < inpSelIdList.length; i++) {
    bindEleFun(inpSelIdList[i]);
  }
  // 是否冰冻
  $('#blhtzzsyd1 .bd-item input[type="radio"]').on('click', function () {
    setBbTypeDefault('.bblb-sel');
  })
  // 末次月经“无”复选框
  noLastMensesDateCk();
  firstLoad = false;
  // 初始化标本信息日期时间控件事件
  $('.time-wrap01').on('blur', function (e) {
    curInpEle = e.target;
    compareDateTime('lt', curInpEle.value);
  });
  $('.time-wrap02').on('blur', function (e) {
    curInpEle = e.target;
    compareDateTime('gd', curInpEle.value);
  });
  initBbmcSelEv();
  // layui.use(['form'], function () {
  //   var form = layui.form;
  //   form.on('select(selChange)', function (data) {
  //     var val = data.value;
  //     val ? changeElm(val, 'change') : '';
  //   });
  // })
}

// 直接从申请单读取数据时，特殊回显标本和检查信息
function showItemAndGm() {
  // 回显检查项目，标本信息
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ?
    rtStructure.enterOptions.publicInfo : {};
  var applyItem = enterData.applyItemList ? enterData.applyItemList[0] : {};
  var gmList = enterData.gmSampleList || [];
  var gmData = [];
  gmDataMap = {};
  if (gmList.length) {
    var keyAndId = {
      'sampleName': '01-标本名称',
      'samplePart': '02-标本部位',
      'sampleType': '03-标本类别',
      'inVitroDateTime': '04-离体时间',
      'fixedDateTime': '05-固定时间',
      'fixLiquid': '06-固定液',
    }
    var pre = 'jybc-rt-';
    for (var i = 0; i < gmList.length; i++) {
      var gmItem = gmList[i];
      var flagId = createUUidFun();
      var oneGm = {
        desc: "列表行",
        id: `${pre}00.${flagId}`,
        name: "列表行",
        pid: "jybc-rt-78",
        val: `${i + 1}`,
        child: [],
      }
      for (var key in keyAndId) {
        var [id, name] = keyAndId[key].split('-');
        oneGm.child.push({
          desc: name,
          id: `${pre}${id}.${flagId}`,
          name: name,
          pid: `${pre}00.${flagId}`,
          val: gmItem[key] || '',
        })
      }
      gmData.push(oneGm);
      gmDataMap[`${pre}00.${flagId}`] = {
        sampleNo: gmItem.sampleNo,
        sampleStatus: gmItem.sampleStatus,
      }
    }
  }
  if (rtStructure.enterOptions.type === 'view') {
    $('.jcxm-item').text(applyItem.itemName);
    $('.xmfy-item').text(applyItem.charge);
    // 标本
    showTableVal('span:last', gmData);
  } else {
    $('[id="jybc-rt-53"]').val(applyItem.itemName);
    $('[id="jybc-rt-53"]').attr('itemCode', applyItem.itemCode);
    $('[id="jybc-rt-55"]').val(applyItem.cost);
    showTableVal('input[type="text"]', gmData);
  }
  // 申请医院
  let { reqHospitalName = '' } = srQueryParams[localKey];
  if (enterData.reqHospital || reqHospitalName) {
    let hospitalVal = enterData.reqHospital || reqHospitalName || '';
    $('#jybc-rt-47').val(hospitalVal)
  }
  // 申请科室
  if (enterData.reqDept || enterData.deptCode) {
    $('#jybc-rt-49').attr('deptCode', enterData.reqDept || enterData.deptCode);
  }
  // 申请医生
  if (enterData.reqPhysicianCode) {
    $('#jybc-rt-51').attr('staffNo', enterData.reqPhysicianCode);
  }
  // 年龄
  if (enterData.age) {
    splitAgeAndUnit(enterData.age);
  }
  // 性别
  if (enterData.sex) {
    lastMensesDateIsReq(enterData.sex);
  }
  // 预约时间
  if (enterData.scheduledDate && enterData.scheduledTime) {
    let date = enterData.scheduledDate + ' ' + enterData.scheduledTime;
    if (rtStructure.enterOptions.type === 'view') {
      $('.schedule-date').val(date);
    } else {
      $('#jybc-rt-45').val(date);
    }
  }
  // 是否冰冻
  if (enterData.freezeFlag) {
    resetEleVal('jybcxbtct1', 'bd-item', 'freezeFlag', enterData);
  }
  // 是否缴费
  if (enterData.chargeFlag) {
    resetEleVal('jybcxbtct1', 'jf-item', 'chargeFlag', enterData);
  }
  // 检查目的
  enterData.examMotive ? $('#jybc-rt-60').val(enterData.examMotive) : ''
  // 临床所见
  enterData.surgerySeen ? $('#jybc-rt-75').val(enterData.surgerySeen) : ''
  // 临床诊断
  enterData.clinDiag ? $('#jybc-rt-77').val(enterData.clinDiag) : ''
}

// 拆分年龄和单位
function splitAgeAndUnit(str) {
  if(!str) {
    return;
  }
  let regAge = '';
  if(isSavedReport && $('#jybc-rt-4').val()) {
    regAge = $('#jybc-rt-4').val();
  }else {
    let {age='',ageType='',subAge=''} = getAgeAndUnit(str);
    regAge = age;
    if(ageType === '日' || ageType === '天') {
      ageType = '天';
    }
    $('#jybc-rt-5').val(ageType);
    $('#jybc-rt-6').val(subAge);
    changeAgeUnit(ageType);
  }
  $('#jybc-rt-4').val(regAge);
}
// 获取年龄和单位
function getAgeAndUnit(str) {
  var index = escape(str).indexOf("%u");
  if (index > 0) {
    var age = str.substring(0, index);
    var ageType = str.substring(index, index + 1);
    if (str.length > index + 1) {
      var subAge = str.substring(index + 1, str.length - 1);
    }
  }
  return { age, subAge, ageType }
}
// 回车键事件
function enterEvFun(id) {
  $('#' + id).on('keydown', function (e) {
    if (e.which == 13) {
      getApplyPatient(id);
    }
  })
}
// 切换子类时，标本信息相关接口内容获取
function getGmInfoListHandler(dropdown) {
  // 标本部位
  getGmSamplePartList();
  $('.bbbw-sel:visible').each(function (i, dom) {
    var id = $(dom).attr('id');
    $(dom).val('');
    dropdown.reload(id, {
      data: gmSamplePartList,
      show: false // 重载即显示组件面板
    });
  })
  // 标本类别
  getGmTypeNameList();
  $('.bblb-sel:visible').each(function (i, dom) {
    var id = $(dom).attr('id');
    $(dom).val('');
    dropdown.reload(id, {
      data: gmTypeNameList,
      show: false // 重载即显示组件面板
    });
  })
}

// 末次月经复选框
function noLastMensesDateCk() {
  $('#jybc-rt-34').on('click', function () {
    if ($('#jybc-rt-34').is(':checked')) {
      $('#jybc-rt-33').val('');
    }
  });
}
// 下拉框事件
function initSelFun() {
  selList = [
    { id: 'jybc-rt-9', className: 'sex-sel', data: genderList },
    // { id: 'jybc-rt-16', className: 'mz-sel', data: nationList },
    { id: 'jybc-rt-11', className: 'patient-sel', data: patientSourceList },
    { id: 'jybc-rt-40', className: 'examsub-sel', data: examSubClassList },
    { id: 'jybc-rt-53', className: 'examitem-sel', data: examItemList },
    { id: 'jybc-rt-47', className: 'hosp-sel', data: hospitalList },
    { id: 'jybc-rt-49', className: 'dept-sel', data: deptList },
    { id: 'jybc-rt-51', className: 'doct-sel', data: dictUsersList },
    { className: 'bbmc-sel', data: gmSampleNameList, width: 120 },
    { className: 'bbbw-sel', data: gmSamplePartList, width: 120 },
    { className: 'bblb-sel', data: gmTypeNameList, width: 80 },
    { className: 'gdy-sel', data: gmFixativeList, width: 160 },
  ];
  let classNameList = ['hosp-sel', 'dept-sel', 'doct-sel', 'examsub-sel', 'examitem-sel', 'sex-sel', 'patient-sel'];
  selList.map(item => {
    let { id = '', className, data, width = '' } = item;
    if (classNameList.indexOf(className) > -1) {
      initDeptInpAndSel(id, className, data, width)
    }
    // else if(className==='bbmc-sel') {
    //   initBbmcInpAndSel(className,data,width);
    // }
    else {
      initInpAndSel(className, data, width);
    }
  });
}
// 输入框事件 支持模糊查询
function bindEleFun(eleId, isClass) {
  let eleAttr = isClass ? '.' + eleId : '#' + eleId;
  $(eleAttr).on('input', function () {
    var _this = $(this);
    let val = _this.val();
    let _thisId = _this.attr('id');
    let selName = _this.attr('sel-name');
    selList.map(item => {
      let { id = '', className = '' } = item;
      let allList = []; // 所有的下拉数据
      let filterOptList = []; // 过滤的下拉数据
      let optionList = []; // 最终的下拉数据
      // 检查项目、申请科室、申请医生
      if (_thisId === id) {
        let inpSelOptList = {
          'jybc-rt-53': examItemList,
          'jybc-rt-49': deptList,
          'jybc-rt-51': dictUsersList,
        };
        allList = inpSelOptList[id];
        if (_thisId === 'jybc-rt-53') {
          filterOptList = allList.filter(dataItem => dataItem.itemName.includes(val));
          _this.removeAttr('itemcode');
        } else if (_thisId === 'jybc-rt-49') {
          filterOptList = allList.filter(dataItem => dataItem.deptName.includes(val));
          _this.removeAttr('deptcode');
          $('#jybc-rt-49').val('');
        } else if (_thisId === 'jybc-rt-51') {
          filterOptList = allList.filter(dataItem => dataItem.title.includes(val));
        }
        optionList = val ? filterOptList : allList;
        // 重载方法
        dropdown.reload(id, {
          data: optionList,
          show: true // 重载即显示组件面板
        });
      } else if (selName !== 'gdy-sel' && selName !== 'bbmc-sel' && selName === className) {
        // 标本名称、标本部位、标本类别
        let inpSelOptList = {
          // 'bbmc-sel': gmSampleNameList,
          'bbbw-sel': gmSamplePartList,
          'bblb-sel': gmTypeNameList,
        };
        allList = inpSelOptList[selName];
        // if(selName === 'bbmc-sel') {
        //   filterOptList = allList.filter(dataItem => dataItem.value && dataItem.value.includes(val));
        // }else 
        if (selName === 'bbbw-sel') {
          filterOptList = allList.filter(dataItem => dataItem.partName.includes(val));
        } else if (selName === 'bblb-sel') {
          filterOptList = allList.filter(dataItem => dataItem.title.includes(val));
        }
        optionList = val ? filterOptList : allList;
        // 重载方法
        dropdown.reload(_thisId, {
          data: optionList,
          show: true // 重载即显示组件面板
        });

      }
    });
  });
}
// 初始化申请科室和申请医生下拉菜单，含id
function initDeptInpAndSel(id, selClass, optionList, lenVal) {
  let isExpect = isInitInpAndSel(selClass);
  if (!isExpect) return;
  let selLen = 230;
  lenVal ? selLen = lenVal : '';
  // let selLen = ''
  dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${id}`,
    data: optionList,
    className: 'laySelLab',
    id: id,
    click: function (obj) {
      this.elem.val(obj.title);
      if (selClass === 'hosp-sel') {
        // 申请医院联动申请科室，科室联动医生
        getDeptList();
        dictUsersList = [];
        $('#jybc-rt-49').removeAttr('deptCode');
        $('#jybc-rt-49').val('');
        $('#jybc-rt-51').removeAttr('staffNo');
        $('#jybc-rt-51').val('');
        // 重载方法
        let initSelList = {
          'jybc-rt-49': deptList,
          'jybc-rt-51': dictUsersList,
        }
        for (let key in initSelList) {
          dropdown.reload(key, {
            data: initSelList[key],
            show: false // 重载即显示组件面板
          });
        }
      }
      if (selClass === 'dept-sel') {
        let deptCode = obj.deptCode;
        $('#jybc-rt-49').attr('deptCode', obj.deptCode);
        // 申请科室联动申请医生
        if (deptCode) {
          $('#jybc-rt-51').removeAttr('staffNo');
          $('#jybc-rt-51').val('');
          getDictUsersList(deptCode);
          // 重载方法
          dropdown.reload('jybc-rt-51', {
            data: dictUsersList,
            show: false // 重载即显示组件面板
          });
        }
      } else if (selClass === 'doct-sel') {
        $('#jybc-rt-51').attr('staffNo', '');
      } else if (selClass === 'examsub-sel') {
        $('#jybc-rt-53').val('');
        $('#jybc-rt-55').val('');
        $('#jybc-rt-53').removeAttr('itemCode');
        getExamItemList(obj.title);
        getGmInfoListHandler(dropdown);
        // 重载方法
        dropdown.reload('jybc-rt-53', {
          data: examItemList,
          show: false // 重载即显示组件面板
        });
      } else if (selClass === 'examitem-sel') {
        $('#jybc-rt-53').attr('itemCode', obj.itemCode);
        $('#jybc-rt-55').val(obj.price);
      }
      if (selClass === 'sex-sel') {
        lastMensesDateIsReq(obj.title);
      }
    },
    style: `width: ${selLen}px;`
  });
}
// 初始化输入选择下拉框
function initInpAndSel(selClass, optionList, lenVal, id) {
  let isExpect = isInitInpAndSel(selClass);
  if (!isExpect) return;
  let selLen = 230;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: id ? `[id="${id}"]` : `.${selClass}`,
    data: optionList,
    className: 'laySelLab',
    id: id || '',
    click: function (obj) {
      this.elem.val(obj.title);
      // if(selClass === 'sex-sel') {
      //   lastMensesDateIsReq(obj.title);
      // }
    },
    style: `width: ${selLen}px;`
  });
}
// 初始化标本名称输入选择下拉框
function initBbmcInpAndSel(selClass, optionList, lenVal, id) {
  let isExpect = isInitInpAndSel(selClass);
  if (!isExpect) return;
  let selLen = 230;
  lenVal ? selLen = lenVal : '';
  bbmcDropdown = layui.dropdown;
  bbmcDropdown.render({
    elem: id ? `[id="${id}"]` : `.${selClass}`,
    data: optionList,
    className: 'bbmcInpSel',
    id: id || '',
    ready: function () {
      valueLike = '';
      initBbmcParams();
      initFlowFun(id);
    },
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: `width: ${selLen}px;`
  });
}
// 是否初始化输入选择下拉框
function isInitInpAndSel(selClass) {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ? rtStructure.enterOptions.publicInfo : {};
  var gmList = enterData.gmSampleList || []; // 标本信息列表
  let selCode = $('.' + selClass).attr('code');
  for (let i = 0; i < gmList.length; i++) {
    let sampleStatus = gmList[i].sampleStatus;
    if (unEditSampleSta.includes(sampleStatus)) {
      let isExpect = unEditAttrExcept.includes(selCode);
      return isExpect;
    }
  }
  if (disableCode.includes(selCode) && isSaveApply) {
    return false;
  }
  return true;
}
// 初始化日期时间插件
function initDatePicker(timeType) {
  // 时间控件的code
  var timeOrDateArr = ['date-wrap01', 'date-wrap02', 'date-wrap03'];
  for (let dateClass of timeOrDateArr) {
    let isExpect = isInitInpAndSel(dateClass);
    if (!isExpect) continue;
  }
  var path = location.href.split('template-lib/')[0]
  layui.config({ dir: path + 'template-lib/plugins/layui/' })
  layui.use('laydate', function () {
    var laydate = layui.laydate;
    //执行一个laydate实例
    if (timeType) {
      // 离体时间
      laydate.render({
        elem: '.time-wrap01', //指定元素
        type: 'datetime',
        trigger: 'click',
        format: 'yyyy-MM-dd HH:mm:ss',
        done: function (value) {
          compareDateTime('lt', value);
        },
      });
      // 固定时间
      laydate.render({
        elem: '.time-wrap02', //指定元素
        type: 'datetime',
        trigger: 'click',
        format: 'yyyy-MM-dd HH:mm:ss',
        done: function (value) {
          compareDateTime('gd', value);
        },
      });
    } else {
      laydate.render({
        elem: '.date-wrap01', //指定元素
        type: 'date',
        trigger: 'click',
        format: 'yyyy-MM-dd',
        done: function (value) {
          if (value && $('#jybc-rt-34').is(':checked')) {
            $('#jybc-rt-34').click();
          }
        }
      });
      laydate.render({
        elem: '.date-wrap02', //指定元素
        type: 'datetime',
        trigger: 'click',
        format: 'yyyy-MM-dd HH:mm:ss', // 需求10538改为时分秒
        className: 'yyDate',
        min: Date.now(),
        ready: function (value) {
          $('.laydate-btns-confirm').removeClass('laydate-disabled');
          $(".laydate-btns-time").off('click').on('click', function () {
            disabled_time();
            $(".laydate-time-list li").on('click', function () {
              disabled_time(value);
            });
          });
          $('.laydate-btns-now').off('click').on('click', function () {
            compareCurAndDisTime();
          })
          disabled_date();
        },
        change: function (value, date, endDate) {
          disabled_date();
        },
      });
      laydate.render({
        elem: '.date-wrap03', //指定元素
        type: 'date',
        trigger: 'click',
        format: 'yyyy-MM-dd',
        done: function (value) {
          getAgeByDate(value);
        }
      });
    }
  });
}
// 末次月经是否必填
function lastMensesDateIsReq(title) {
  if (title === '男') {
    $('#jybc-rt-32').siblings().css('display', 'none');
    $('#jybc-rt-32').removeAttr('rt-req');
  } else {
    $('#jybc-rt-32').siblings().css('display', 'inline-block');
    $('#jybc-rt-32').attr('rt-req', '1');
  }
}
/**
  *设置不可选择的星期
  *
*/
function disabled_date() {
  var trElems = $(".layui-laydate-content tbody").find('tr');

  trElems.each(function () {
    $(this).find('td').each(function (tdIndex, tdElem) {
      //遍历td，index===0表示周日，index===6表示周六
      var targetIndex = tdIndex === 0 ? '7' : (tdIndex).toString();
      disabledDate.map(item => {
        let { day = [], time = [] } = item;
        if (day.indexOf(targetIndex) > -1 && !time) {
          // laydate-disabled是layui的样式类，添加后会禁用元素
          $(this).addClass('laydate-disabled');
        }
      })
      // if (disabledDate.indexOf(targetIndex) > -1) {
      //   // laydate-disabled是layui的样式类，添加后会禁用元素
      //   $(this).addClass('laydate-disabled');
      // }
    });
  });
}
// 禁用对应日期的时间
function disabled_time(value) {
  var hourListElems = $('.layui-laydate-content .laydate-time-list li:nth-child(1)').find('ol');
  var minListElems = $('.layui-laydate-content .laydate-time-list li:nth-child(2)').find('ol');
  var thisDay = $('.layui-this').attr('lay-ymd');
  var week = getweekday(thisDay);
  var curHour = $('li:nth-child(1)').find('ol li.layui-this').text();
  // 禁用时间段——时
  hourListElems.each(function () {
    $(this).find('li').each(function (liIndex, liElem) {
      //遍历li
      disabledDate.map(item => {
        let { day = [], time = [] } = item;
        if (day.indexOf(week) > -1 && time.length) {
          for (let i = 0; i < time.length - 1; i++) {
            let startTimeItem = time[i];
            let endTimeItem = time[i + 1];
            let [hour1 = '', min1 = ''] = startTimeItem.split(':');
            let [hour2 = '', min2 = ''] = endTimeItem.split(':');
            for (let h = hour1; h <= hour2; h++) {
              // 当分钟的禁用不是00-59整段，时不可禁用
              if (Number(h) === Number(liIndex) && ((Number(h) === Number(hour1) && min1 === '00') || (Number(h) === Number(hour2) && min2 === '59'))) {
                $(this).addClass('laydate-disabled');
              } else if (Number(h) === Number(liIndex) && h != hour1 && h != hour2) {
                // laydate-disabled是layui的样式类，添加后会禁用元素
                $(this).addClass('laydate-disabled');
              }
            }
          }
        }
      })
    })
  });
  // 禁用时间段——分
  minListElems.each(function () {
    $(this).find('li').each(function (liIndex, liElem) {
      //遍历li
      disabledDate.map(item => {
        let { day = [], time = [] } = item;
        if (day.indexOf(week) > -1 && time.length) {
          for (let i = 0; i < time.length - 1; i++) {
            let startTimeItem = time[i];
            let endTimeItem = time[i + 1];
            let [hour1 = '', min1 = ''] = startTimeItem.split(':');
            let [hour2 = '', min2 = ''] = endTimeItem.split(':');
            if (Number(curHour) === Number(hour1)) {
              // 起始时间
              for (let min = min1; min <= 59; min++) {
                if (Number(min) === Number(liIndex)) {
                  // laydate-disabled是layui的样式类，添加后会禁用元素
                  $(this).addClass('laydate-disabled');
                }
              }
            } else if (Number(curHour) === Number(hour2)) {
              // 结束时间
              for (let min = 0; min <= min2; min++) {
                if (Number(min) === Number(liIndex)) {
                  // laydate-disabled是layui的样式类，添加后会禁用元素
                  $(this).addClass('laydate-disabled');
                }
              }
            } else {
              for (let min = min1; min <= min2; min++) {
                if (Number(min) === Number(liIndex) && curHour === Number(hour1)) {
                  // laydate-disabled是layui的样式类，添加后会禁用元素
                  $(this).addClass('laydate-disabled');
                }
              }
            }
          }
        }
      })
    })
  });
}
// 推算当前日期时间是否在配置时间的禁用时间段
function compareCurAndDisTime() {
  let curDateAndTime = new Date();
  let year = curDateAndTime.getFullYear();
  let month = curDateAndTime.getMonth() + 1;
  let date = curDateAndTime.getDate();
  let hours = curDateAndTime.getHours();
  let minutes = curDateAndTime.getMinutes();
  let curDate = year + '-' + month + '-' + date;
  let week = getweekday(curDate);
  disabledDate.map(item => {
    // 2024-3-18
    let { day = [], time = [] } = item;
    if (day.indexOf(week) > -1) {
      if (time.length) {
        // 有时间段
        for (let i = 0; i < time.length - 1; i++) {
          let startTimeItem = time[i];
          let endTimeItem = time[i + 1];
          let [hour1 = '', min1 = ''] = startTimeItem.split(':');
          let [hour2 = '', min2 = ''] = endTimeItem.split(':');
          if ((Number(hours) > Number(hour1) && Number(hours) < Number(hour2)) || (Number(hours) === Number(hour1) && Number(minutes) > Number(min1) && Number(minutes) < 59) || (Number(hours) === Number(hour2) && Number(minutes) > 0 && Number(minutes) < Number(min2))) {
            confirm('当前时间不在可选日期范围');
            $('#jybc-rt-45').val('');
          }
        }
      } else {
        confirm('当前时间不在可选日期范围');
        $('#jybc-rt-45').val('');
      }
    }
  })
}
// 根据日期推算星期
function getweekday(date) {
  var weekArray = new Array("7", "1", "2", "3", "4", "5", "6");
  var week = weekArray[new Date(date).getDay()];//注意此处必须先new一个Date
  return week;
}
// 固定时间要大于离体时间
function compareDateTime(type, value) {
  var curInpDom = $(curInpEle);
  // 离体时间
  if (type === 'lt') {
    var id = curInpDom.attr('id');
    var gdDateVal = '';
    if (id) {
      var tailId = id.split('.')[1];
      var gdDateId = 'jybc-rt-05.' + tailId;
      gdDateVal = $('[id="' + gdDateId + '"]').val();
    } else {
      gdDateVal = $('.new-gdsj').val();
    }
    var newLtDate = new Date(value).getTime();
    var newGdDate = new Date(gdDateVal).getTime();
    if (newLtDate > newGdDate) {
      $wfMessage({
        content: ('离体时间不能超过固定时间')
      });
      curInpDom.val('');
    }
  } else if (type === 'gd') {
    var id = curInpDom.attr('id');
    var ltDateVal = '';
    if (id) {
      var tailId = id.split('.')[1];
      var ltDateId = 'jybc-rt-04.' + tailId;
      ltDateVal = $('[id="' + ltDateId + '"]').val();
    } else {
      ltDateVal = $('.new-ltsj').val();
    }
    var newGdDate = new Date(value).getTime();
    var newLtDate = new Date(ltDateVal).getTime();
    if (newLtDate > newGdDate) {
      confirm('离体时间不能超过固定时间');
      curInpDom.val('');
    }
  }
}
// 标本类别默认值
// 为true时，当是否冰冻选“是”，标本类别默认“冰冻”；“否”时，标本类别默认“常规”
// 为false时，不处理
function setBbTypeDefault(idOrClass) {
  if (sampleType) {
    let bdRadioVal = $('#blhtzzsyd1 .bd-item input[type="radio"]:checked').val();
    $(`#blhtzzsyd1 ${idOrClass}:visible`).each(function (i, dom) {
      if (bdRadioVal === '是') {
        $(dom).val('冰冻');
      } else if (bdRadioVal === '否') {
        $(dom).val('常规');
      }
    });
  }
}
// 回显表格
function showTableVal(findDom, data) {
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  let resultDataLen = resultData.length;
  let childData = resultData[resultDataLen - 1] && resultData[resultDataLen - 1].child ? JSON.parse(JSON.stringify(resultData[resultDataLen - 1].child)) : [];
  let flagId = '';
  if (data) {
    childData = data;
  }
  if (data || resultDataLen > 1) {
    childData.map(function (item) {
      let childArr = item.child || [];
      childArr.map(function (cItem) {
        flagId = cItem.id ? cItem.id.split('.')[1] : '';
      });
      flagId ? addRow(flagId, findDom, childArr) : '';
    })
  }
}
// 新增行
function addRow(flagId, findDom, childArr) {
  let cloneTr = $('.cloneTr:first').clone(true);
  let cloneTd = cloneTr.find('td');
  let notHiddenInp = cloneTr.find('input[type="text"]');
  let hiddenInp = cloneTr.find('input[type="hidden"]');
  let randomNum = createUUidFun();
  let inpSortArr = ['01', '02', '03', '04', '05', '06'];
  let pid = '', selNameList = [];
  let bblbId = '', ltTimeId = '', gdTimeId = '', gdyId = '';
  if (flagId && typeof flagId === 'string') {
    pid = 'jybc-rt-00' + '.' + flagId;
  } else {
    pid = 'jybc-rt-00' + '.' + randomNum;
  }

  rtStructure.idAndDomMap[pid] = {
    id: pid,
    desc: '列表行',
    name: '列表行',
    pid: 'jybc-rt-78',
    pvf: '',
    value: '1',
    wt: '',
    vt: '',
  }
  $(hiddenInp).attr('id', pid);
  $(hiddenInp).attr('pid', 'jybc-rt-78');
  $(hiddenInp).addClass('rt-sr-w');

  // 预览
  if (flagId && findDom === 'span:last') {
    inpSortArr = ['', '01', '02', '03', '04', '05', '06'];
    childArr.map(function (cItem) {
      if (findDom === 'span:last') {
        cloneTd.each(function (i, dom) {
          id = 'jybc-rt-' + inpSortArr[i] + '.' + flagId;
          let tdEle = $(dom);
          i !== 0 && cItem.id.includes(id) ? tdEle.text(cItem.val) : '';
        })
      }
    })
    // $('thead tr th:last').css('display','none');
    // $('tbody tr td:last').css('display','none');
  }

  // 新增与编辑
  notHiddenInp.each(function (i, dom) {
    let ele = $(dom);
    let inpName = ele.attr('inp-name');
    let selName = ele.attr('sel-name');
    let id = '';
    if (flagId && findDom === 'input[type="text"]') {
      id = 'jybc-rt-' + inpSortArr[i] + '.' + flagId;
      childArr.map(function (cItem) {
        cItem.id.includes(id) ? ele.val(cItem.val) : '';
      })
    } else {
      id = 'jybc-rt-' + inpSortArr[i] + '.' + randomNum;
      if (i === 2) {
        bblbId = id;
      } if (i === 3) {
        ltTimeId = id
      } else if (i === 4) {
        gdTimeId = id
      } else if (i === 5) {
        gdyId = id;
      }
    }
    rtStructure.idAndDomMap[id] = {
      id: id,
      desc: inpName,
      name: inpName,
      pid: pid,
      pvf: '',
      value: '',
      wt: '',
      vt: '',
      aa: ''
    }
    ele.addClass('rt-sr-w');
    ele.attr('id', id);
    ele.attr('pid', pid);
    selNameList.push({ selName, id: `${id}` });
  })
  cloneTr.css('display', 'table-row');
  $('.htzzTableBody tr:last').after(cloneTr);
  selList.map(item => {
    selNameList.map(classItem => {
      if (item.className === classItem.selName) {
        let { className, data, width = '' } = item;
        // 部位和类型根据检查子类联动
        if (className === 'bbbw-sel') {
          data = gmSamplePartList;
        }
        if (className === 'bblb-sel') {
          data = gmTypeNameList;
        }
        if (className === 'bbmc-sel') {
          initBbmcInpAndSel(className, data, width, classItem.id);
        } else {
          initInpAndSel(className, data, width, classItem.id);
        }
        bindEleFun(className, true);
      }
    });
  });
  initDatePicker(true);
  // 新增默认值
  if (findDom === 'add') {
    var dateAndTime = getCurDateAndTime(false);
    var moment = dateAndTime.curDate;
    var momentTime = dateAndTime.curTime;
    var defaultDate = moment + ' ' + momentTime;
    var ltsjToHm = new Date(defaultDate).getTime();
    var sjcTohm = fixativeGap * 60 * 1000;
    var fixativeGapDate = ltsjToHm + sjcTohm;
    var fixativeGapDateFormat = dayjs(fixativeGapDate).format('YYYY-MM-DD HH:mm:ss');
    var gdyList = $('.gdy-sel:not(:first)');
    var lastGdyVal = '';
    gdyList.each(function (i, dom) {
      if (i === getRowLength() - 2) {
        lastGdyVal = $(dom).val();
      }
    })
    var newGdyVal = $('.new-gdy').val();
    var newLtTimeVal = $('.new-ltsj').val();
    var newGdTimeVal = $('.new-gdsj').val();
    // 输入离体时间、固定时间、固定液后，按当前输入的显示
    $('[id="' + gdyId + '"]').val(newGdyVal || lastGdyVal || defaultGmFixative);
    $('[id="' + ltTimeId + '"]').val(newLtTimeVal || defaultDate);
    $('[id="' + gdTimeId + '"]').val(newGdTimeVal || fixativeGapDateFormat);
    setBbTypeDefault('[id="' + bblbId + '"]');
    initBbmcParams();
  }
  $(".layout-body")[0].scrollTop = $(".layout-body")[0].scrollHeight;
  hideOrShowTable();
}
// 获取当前日期
function getCurDT(noSecond, minNum) {
  var date = new Date(minNum);
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var minute = date.getMinutes();
  var second = date.getSeconds();
  var curDate = year + '-' + addZero(month) + '-' + addZero(day)
  var curTime = addZero(hour) + ':' + addZero(minute) + (!noSecond ? ':' + addZero(second) : '');
  return { curDate: curDate, curTime: curTime };
}
// 删除行
function removeRow(item, type) {
  var curELeTr = $(".htzzTableBody").find("tr:not(:first)");
  var checkList = curELeTr.find('input[type="checkbox"]:checked');
  if (type === 'all' && !checkList.length) return;
  let flag = confirm('确认删除吗？');
  if (flag) {
    if (type === 'all') {
      $('.all-check').prop('checked', false);
      checkList.each(function (i, dom) {
        $(dom).closest("tr").remove();
      })
    } else {
      $(item).closest("tr").remove();
    }
  }
  hideOrShowTable();
}
function hideOrShowTable() {
  if (getRowLength() < 1) {
    $('.bbxx-table').hide();
  } else {
    $('.bbxx-table').show();
  }
}
function getRowLength() {
  // 统计当前表格行数，防止删空
  return $(".htzzTableBody").find("tr:not(:first)").length;
}
// 转换下拉数据格式
function transformOptList(list, titleName) {
  let arr = [];
  list.forEach((item, index) => {
    if (titleName) {
      arr.push({ ...item, title: item[titleName], id: index, templet: `<span title='${item[titleName]}'>${item[titleName]}</span>` });
    } else {
      arr.push({ title: item, id: index, templet: `<span title='${item}'>${item}</span>` });
    }
  })
  return arr;
}
// 获取系统配置项
function getGmSettings() {
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ? rtStructure.enterOptions.publicInfo : {};
  var gmList = enterData.gmSampleList || []; // 标本信息列表
  var paramNameArr = ['apply.cannotScheduleWeekTime', 'print.sampleTypeDefault', 'unableEDitApplyAttribute', 'apply.chargedFlag', 'apply.autoGenerationSickId', 'apply.unableEditSampleStatus', 'apply.unableEditAttributeExcepted', 'apply.hideApplyAttribute'];
  var params = { paramName: paramNameArr.join(',') };
  disabledDate = [];
  fetchAjax({
    url: api.getGmSettings,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0' && res.result) {
        res.result.map(item => {
          let { paramName = '', paramValue = '' } = item;
          if (paramName === 'apply.cannotScheduleWeekTime') {
            let parseParamValue = JSON.parse(paramValue);
            disabledDate = parseParamValue;
            // disabledDate = [
            //   { day: ['3','4'],
            //     time: ['14:10','19:10']
            //   },
            //   {
            //     day: ['6','7'],
            //     time: null
            //   }
            // ]
            // disabledDate = [
            //   {
            //     "day": ["1","3","5"],    //日期周一~周日，可多选
            //     "time": ["12:00", "18:30"]    //时间，第一个元素为开始时间， 第二个元素为结束时间
            //   },
            //   {
            //     "day": ["2","7"],  //周二，周日
            //     "time": ["10:10","18:30"]  //10:10 到 18:30
            //   }
            // ]
            // console.log('disabledDate-->',disabledDate);
          } else if (paramName === 'print.sampleTypeDefault') {
            sampleType = paramValue;
          } else if (paramName === 'unableEDitApplyAttribute') {
            disableCode = paramValue.split(',');
            codeToDisEdit();
          } else if (paramName === 'apply.chargedFlag') {
            // 南沙区域病理需求,是否默认已收费;值为true时,默认选中已缴费
            if (paramValue === 'true') {
              let chargeConfig = {
                chargeFlag: paramValue === 'true' ? '1' : '0'
              }
              resetEleVal('blhtzzsyd1', 'jf-item', 'chargeFlag', chargeConfig);
            }
          } else if (paramName === 'apply.autoGenerationSickId') {
            // 为true时，病人ID非必填；其余情况必填
            if (paramValue === 'true') {
              $('#jybc-rt-14').siblings().css('display', 'none');
              $('#jybc-rt-14').removeAttr('rt-req');
            }
          } else if (paramName === 'apply.unableEditSampleStatus') {
            // 限制不能修改申请单信息的标本状态,已打包流程就不能修改
            unEditSampleSta = paramValue.split(',');
            for (let i = 0; i < gmList.length; i++) {
              let sampleStatus = gmList[i].sampleStatus;
              if (unEditSampleSta.includes(sampleStatus)) {
                setUnEditSampleInfo();
                return;
              }
            }

          } else if (paramName === 'apply.unableEditAttributeExcepted') {
            // 不受限制的字段
            // 1、“不受限制的字段”配置 需要排除 “不可编辑的申请单信息配置” 中配置的字段，“不可编辑的申请单信息配置”优先级最高；
            // 2、当状态到达已打包50，“不受限制的字段”存在标本相关信息，可修改，不受“状态”限制
            unEditAttrExcept = paramValue.split(',');
            if (unEditAttrExcept.length) {
              unEditAttrExceptFun();
            }
          } else if (paramName === 'apply.hideApplyAttribute') {
            hideAttrCode = paramValue.split(',');
            setHideCodeItem();
          }
        });
      }
    },
  })
}
// 通过code禁用对应控件
function codeToDisEdit() {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  if (!isSaveApply) {
    return;
  }
  // 已知code的关联项
  var concatItemMap = {
    '3': ['#jybc-rt-5', '#jybc-rt-6'],  //年龄
    '310': ['#jybc-rt-31'],  //是否传染病
    '32': ['#jybc-rt-34'],  //末次月经
  }
  for (let code of disableCode) {
    var eleList = $('[code="' + code + '"]');
    if (eleList.length) {
      if (concatItemMap[code]) {
        var concatList = concatItemMap[code];
        concatList.forEach(function (cItem) {
          $(`${cItem}`).attr('disabled', true);
        })
      }
      eleList.each(function (i, dom) {
        var type = $(dom).attr('type');
        if (type === 'radio' || type === 'checkbox') {
          eleList.prop('disabled', true);
          eleList.addClass('rt-disabled');
          eleList.css('cursor', 'not-allowed');
        } else {
          $(dom).attr('disabled', true);
          $(dom).attr('readonly', true);
          $(dom).addClass('rt-disabled');
          $(dom).css('background', '#fafafa');
        }
      })
    } else if (code === '29') {
      var medRecordCheckList = document.getElementsByName('brbl');
      medRecordCheckList.forEach((item) => {
        $(item).prop('disabled', true);
        $(item).addClass('rt-disabled');
        // $(item).css('cursor', 'not-allowed');
        $(item).parent().css('cursor', 'not-allowed');
      })
    }
  }
}
// 设置不可编辑的标本信息
function setUnEditSampleInfo() {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  if (!isSaveApply) {
    return;
  }
  // 配置中的标本状态包含当前标本信息状态时，禁用所有控件
  var allEle = $('#blhtzzsyd1').find('[rt-sc]');
  // 禁用按钮
  $('#blhtzzsyd1 .tit-right .btn-sty').css('cursor', 'not-allowed');
  $('#blhtzzsyd1 .tit-right .btn-sty').removeAttr('onclick');
  $('#blhtzzsyd1 .del-tbtn').css('cursor', 'not-allowed');
  $('#blhtzzsyd1 .del-tbtn').removeAttr('onclick');
  allEle.each(function (i, dom) {
    var type = $(dom).attr('type');
    var tagName = dom.tagName;
    if (type === 'radio' || type === 'checkbox') {
      $(dom).prop('disabled', true);
      $(dom).addClass('rt-disabled');
      $(dom).css('cursor', 'not-allowed');
    } else if (type === 'text' || tagName === 'SELECT' || tagName === 'TEXTAREA') {
      $(dom).attr('readonly', true);
      $(dom).css('cursor', 'not-allowed');
      $(dom).css('background', '#fafafa');
    }
  })
}
// 不受配置的标本状态限制的控件,“不可编辑的申请单信息配置”优先级最高
function unEditAttrExceptFun() {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  if (!isSaveApply) {
    return;
  }
  for (let code of unEditAttrExcept) {
    if (!disableCode.includes(code)) {
      var exceptCode = $('[code="' + code + '"]');
      exceptCode.prop('disabled', false);
      exceptCode.attr('readonly', false);
      exceptCode.removeClass('rt-disabled');
      exceptCode.css('cursor', 'unset');
      exceptCode.css('background', '#fff');
    }
  }
}
// 同个code隐藏属性
function setHideCodeItem() {
  for (let code of hideAttrCode) {
    var eleList = $('[code="' + code + '"]');
    if (eleList.length) {
      eleList.each(function (i, dom) {
        $(dom).closest('.row-item').remove();
      })
    }
  }
}
// 获取性别列表
function getGenderList() {
  var params = {};
  fetchAjax({
    url: api.getGenderList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        genderList = transformOptList(data, 'sexName');
      }
    },
  })
}
// 获取民族列表
function getNationList() {
  var params = {};
  fetchAjax({
    url: api.getNationList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        nationList = transformOptList(data, 'nationName');
      }
    },
  })
}
// 获取病人来源列表
function getPatientSourceList() {
  var params = {};
  fetchAjax({
    url: api.getPatientSourceList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        patientSourceList = transformOptList(data, 'patientSourceName');
      }
    },
  })
}
// 获取申请医院列表
function getHospitalList() {
  var params = {}
  fetchAjax({
    url: api.getHospitalList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        hospitalList = transformOptList(data, 'hospitalName');
      }
    },
  })
}
// 获取申请科室列表
function getDeptList() {
  var params = {
    hospitalName: $('#jybc-rt-47').val() || ''
  };
  fetchAjax({
    url: api.getDeptList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        deptList = transformOptList(data, 'deptName');
        deptList.map(function (item) {
          let curDeptName = $('#jybc-rt-49').val();
          if (curDeptName === item.deptName) {
            $('#jybc-rt-49').attr('deptCode', item.deptCode);
          }
        })
      }
    },
  })
}
// 获取申请医生列表
function getDictUsersList(deptCode) {
  var params = { deptCode };
  if (!params.deptCode) return;
  fetchAjax({
    url: api.getDictUsersList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        dictUsersList = transformOptList(data);
        if (firstLoad && getParamByName('optName') && data.length) {
          $('#jybc-rt-51').val(getParamByName('optName'));
        }
        let curDoctName = $('#jybc-rt-51').val();
        dictUsersList.map(item => {
          if (curDoctName === item.name) {
            $('#jybc-rt-51').attr('staffNo', item.staffNo);
          }
        })
      }
    },
  })
}
// 获取标本部位列表
function getGmSamplePartList() {
  var params = {
    examSubClass: $('.examsub-sel').val() || ''
  };
  fetchAjax({
    url: api.getGmSamplePartList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        gmSamplePartList = transformOptList(data, 'partName');
      }
    },
  })
}
// 获取标本类别列表
function getGmTypeNameList() {
  var params = {
    examSubClass: $('.examsub-sel').val() || ''
  };
  fetchAjax({
    url: api.getGmTypeNameList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        gmTypeNameList = transformOptList(data);
      }
    },
  })
}
// 初始化流加载
function initFlowFun(id) {
  if (!id) return;
  bbmcId = id;
  layui.use('flow', function () {
    var flow = layui.flow;
    flow.load({
      elem: '.layui-dropdown-menu' //指定列表容器
      , scrollElem: '.bbmcInpSel'
      , done: function (flowPageNo, next) { //到达临界点（默认滚动触发），触发下一页
        var lis = [];
        //请求下一页数据（注意：pageNo是从2开始返回）
        let { pageNo = 1, pageSize = 20 } = pageParams;
        let params = {
          pageNo: flowPageNo,
          pageSize,
          valueLike,
        }
        fetchAjax({
          url: api.getGmSampleNameListV2,
          data: JSON.stringify(params),
          async: false,
          successFn: function (res) {
            layui.each(res.result, function (index, item) {
              lis.push('<li title="' + item.value + '" onclick="selBbmcLi(this)">' + item.value + '</li>');
            });
            // 执行下一页渲染，第二参数为：满足“加载更多”的条件，即后面仍有分页
            // pages为接口返回的总页数，只有当前页小于总页数的情况下，才会继续出现加载更多
            next(lis.join(''), flowPageNo < res.page.pages);
          },
        })
      }
    });
  });
}
function selBbmcLi(vm) {
  let value = $(vm).text();
  $('.bbmcInpSel').hide();
  $('[id="' + bbmcId + '"]').val(value);
}
// 初始化标本名称下拉事件
function initBbmcSelEv() {
  var _thisId = null;
  var bbmcInp = $('.bbmc-sel');
  bbmcInp.on("input", function (e) {
    _thisId = $(this).attr('id');
    valueLike = e.target.value;
    // 重载方法
    bbmcDropdown.reload(_thisId, {
      data: [],
      show: true // 重载即显示组件面板
    });
    initBbmcParams();
    initFlowFun(_thisId);
  });
}
// 初始化标本名称相关参数
function initBbmcParams() {
  pageParams.pageNo = 1;
  // gmSampleNameList = [];
}
// 获取标本名称列表
function getGmSampleNameListV2() {
  let { pageNo = 1, pageSize = 20 } = pageParams;
  var params = {
    pageNo,
    pageSize,
    valueLike,
  };
  fetchAjax({
    url: api.getGmSampleNameListV2,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        gmSampleNameList = gmSampleNameList.concat(transformOptList(data, 'value'));
      }
    },
  })
}// 获取固定液列表
function getGmFixativeList() {
  var params = {};
  fetchAjax({
    url: api.getGmFixativeList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        var defaultGm = data.filter(item => item.defaultFlag);
        if (defaultGm && defaultGm.length) {
          defaultGmFixative = defaultGm[0].value || '';
        }
        gmFixativeList = transformOptList(data, 'value');
      }
    },
  })
}
// 获取检查项目列表
function getExamItemList(examSubClass) {
  var params = {
    examClass,
    examSubClass
  }
  if (!params.examClass) return;
  fetchAjax({
    url: api.getExamItemList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        examItemList = transformOptList(data, 'itemName');
        examItemList.map(function (item) {
          let curExamItemName = $('#jybc-rt-53').val();
          if (curExamItemName === item.itemName) {
            $('#jybc-rt-53').attr('itemCode', item.itemCode);
          }
        })
      }
    },
  })
}
// 获取检查子类列表
function getExamSubClassList2() {
  var params = { examClass }
  if (!params.examClass) return;
  fetchAjax({
    url: api.getExamSubClassList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var data = res.result || [];
        examSubClassList = transformOptList(data, 'examSubclassName');
      }
    },
  })
}
// 获取病理固定时间与离体时间的间隔
function gmFixativeGap() {
  var params = {}
  fetchAjax({
    url: api.gmFixativeGap,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        fixativeGap = res.result || '';
      }
    },
  })
}
// 获取病人信息
function getApplyPatient(id) {
  // 测试数据，3个不同病人信息
  // sickId = 00026248 00026263
  // impatinetNo = **********
  // outpatientNo = 4548784
  let params = {
    sickId: $('#jybc-rt-15').val() || '',
    outpatientNo: $('#jybc-rt-17').val() || '',
    inpatientNo: $('#jybc-rt-19').val() || '',
  };
  fetchAjax({
    url: api.getApplyPatient,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        let applyPatientInfo = res.result || {};
        if (JSON.stringify(applyPatientInfo) !== '{}') {
          let flag = confirm('是否确认覆盖当前病人信息？');
          if (flag) {
            showPatientInfo(id, applyPatientInfo);
          }
          getAgeByDate(applyPatientInfo.birthDate);
        }
      }
    },
  })
}
// 回显病人信息
function showPatientInfo(id, info) {
  $("#blhtzzsyd1 [pat-info]").each(function (t, tEle) {
    var tKey = $(tEle).attr('pat-info');
    var eleId = $(tEle).attr('id');
    eleId !== id ? $(tEle).val(info[tKey]) : '';
  });
  // 末次月经是否必填
  let sexVal = $('#jybc-rt-9').val() || '';
  lastMensesDateIsReq(sexVal);
}
// 校验身份证号
function validateIdCard() {
  $('#jybc-rt-25').on('blur', function (e) {
    var _this = $(this);
    var idCardVal = _this.val();
    var idCardMainLand = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/;//大陆
    if (idCardVal && !idCardMainLand.test(idCardVal.trim())) {
      $('#jybc-rt-25').val('');
      confirm('请输入正确格式的身份证号');
    }
  })
}
// 校验手机号
function validatePhoneNum() {
  $('#jybc-rt-23').on('blur', function (e) {
    var _this = $(this);
    var phoneNum = _this.val();
    var phoneReg = /^1[3456789]\d{9}$/;
    if (phoneNum && !phoneReg.test(phoneNum.trim())) {
      $('#jybc-rt-23').val('');
      confirm('请输入正确格式的手机号');
    }
  })
}
// 年龄单位联动
function changeAgeUnit(ageUnit) {
  var ageUnitMap = {
    "岁": "月",
    "月": "天",
    "周": "天",
    "天": "时",
    "时": "分",
    "分": "秒",
  };
  var ageSubUnit = ageUnitMap[ageUnit];
  $('#jybc-rt-7').html(ageSubUnit);
}
// 拆分年龄年月日
function splitAgeNum(ageUnit, firstInpVal, secondInpVal) {
  var ageYear = '', ageMonth = '', ageDay = '';
  if (ageUnit !== '岁') {
    ageYear = 0;
    ageMonth = 0;
    if (ageUnit === '月') {
      ageMonth = firstInpVal;
      ageDay = secondInpVal;
    }
    if (ageUnit === '周') {
      ageDay = Number(firstInpVal) * 7 + Number(secondInpVal);
    }
    if (ageUnit === '天') {
      ageDay = firstInpVal;
    }
    if (ageUnit === '时') {
      ageDay = firstInpVal >= Number(24) ? parseInt(firstInpVal / 24) : 0;
    }
    if (ageUnit === '秒') {
      ageDay = 0;
    }
  } else {
    ageYear = firstInpVal;
    ageMonth = secondInpVal;
  }
  var birthDate = inferBirthdayByAge(ageYear, ageMonth, ageDay);
  $('#jybc-rt-13').val(birthDate);
}
// 根据年龄推算出生日期
function getDateByAge(id, evType) {
  $('#' + id).on(evType, function (e) {
    var firstInpVal = $('#jybc-rt-4').val();
    var ageUnit = $('#jybc-rt-5').val();
    changeAgeUnit(ageUnit);
    var secondInpVal = $('#jybc-rt-6').val();
    if (firstInpVal === '' && secondInpVal === '') {
      $('#jybc-rt-13').val('');
      return;
    }
    splitAgeNum(ageUnit, firstInpVal, secondInpVal);
  })
}
// 根据出生年月日生成年龄
function getAgeByDate(birthDate) {
  if (!birthDate) {
    return;
  }
  let age = inferAgeByBirthday(birthDate, ',');
  if (age === -1 || !birthDate) {
    $('#jybc-rt-4').val('');
    $('#jybc-rt-5').val('岁');
    $('#jybc-rt-6').val('');
  } else {
    let obj = splitStrFun(age);
    if (obj.ageUnit === '日') {
      obj.ageUnit = '天';
    }
    $('#jybc-rt-4').val(obj.age);
    $('#jybc-rt-5').val(obj.ageUnit);
    $('#jybc-rt-6').val(obj.monthNum);
  }
}
function splitStrFun(str) {
  let res = str.split(',');
  let obj = {
    age: res[0],
    ageUnit: '岁',
    monthNum: '',
  }
  if (res.length === 3) {
    res[3] = '月';
  }
  if (res.length === 2) {
    if (res[1] === '月') {
      obj.age = '';
      obj.monthNum = res[0];
    } else {
      obj.age = res[0];
      obj.ageUnit = res[1];
    }
  } else if (res.length === 4) {
    obj.age = res[0];
    obj.ageUnit = res[1];
    obj.monthNum = res[2];
  }
  console.log('res-->', res);
  return obj;
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
}