$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;

var optAllData = {
  lEmType: ['左耳耳鸣', '左侧颅鸣'],  //左耳鸣耳
  rEmType: ['右耳耳鸣', '右侧颅鸣'],  //右耳鸣耳
  emydCountList: ['单种', '多种'],  //耳鸣音调数量
  emzdTypeList: ['纯音', '脉冲纯音', '啭音', '窄带噪声', '白噪声', '言语噪声'],  //耳鸣主调声类型
  emxdList: ['(1)', '(2)', '(3)', '(4)', '(5)', '(6)', '(7)', '(8)', '(9)', '(10)'],  //耳鸣响度
  ybNorseList: ['纯音', '窄带噪声', '白噪声'],  //掩蔽声音
  seEmResList: ['匹配', '部分匹配', '无匹配'],  //双耳耳鸣检查结果
  cyyzTestList: ['阳性', '部分阳性', '阴性', '左侧阳性', '右侧阳性', '双侧阳性', '左侧部分阳性', 
    '右侧部分阳性', '双侧部分阳性', '左侧阴性', '右侧阴性', '双侧阴性', '检查过程无耳鸣', '左耳检查中无耳鸣', '右耳检查中无耳鸣'],  //残余抑制试验
  cyyzTest2List: ['左侧阳性', '右侧阳性', '左侧部分阳性', '右侧部分阳性', '左侧阴性', '右侧阴性'],  //残余抑制试验
  emMatchList: ['同侧匹配', '对侧匹配'],  //耳鸣匹配方式
  emTypeList: ['左侧持续性', '右侧持续性', '双侧持续性', '左侧间断性', '右侧间断性', '双侧间断性', '左侧间歇性', '右侧间歇性', '双侧间歇性', 
    '左侧搏动性', '右侧搏动性', '双侧搏动性'],  //耳鸣类型
  emTypeList2: ['左侧持续性', '右侧持续性', '双侧持续性', '左侧间断性', '右侧间断性', '双侧间断性', '左侧间歇性', '右侧间歇性', '双侧间歇性', 
    '左侧搏动性', '右侧搏动性', '双侧搏动性'],  //耳鸣类型
  adviseList: ['可行耳鸣声治疗。', '结合病史进一步检查，可试行耳鸣声治疗。', '结合病史进一步检查。', '安静环境下尽量转移注意力。', 
    '结合病史进一步检查，安静环境下尽量转移注意力。', '安静环境下尽量转移注意力，可行耳鸣声治疗。'],  //建议
}
var rptImageList = [];
var deviceAllData = {};
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('jxhem1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];
    deviceAllData = rtStructure.enterOptions ? (rtStructure.enterOptions.deviceAllData || {}) : {};
    console.log(rtStructure);
    loadFile();  //加载报告图像
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      // 兼容历史数据，值不存在默认双侧耳鸣检查结果
      if(rtStructure.idAndDomMap && (!rtStructure.idAndDomMap['jxhem-rt-36'] || !rtStructure.idAndDomMap['jxhem-rt-36'].value)) {
        $('#rt36td').html('双侧耳鸣检查结果');
      }
    } else {
      fillSelectOption();   //可编辑、多选的下拉选项
      fillConfigReporter();   //补充检查技师、报告医生等
      initDatePicker();
      getResScore();  //推导分数
      // 截图区域事件
      pastWrapHandler();
      if(rtStructure.enterOptions && !rtStructure.enterOptions.resultData.length) {
        var reportInfo = deviceAllData.reportInfo || {};
        // var reportStaff = reportInfo.reportStaff || rtStructure.enterOptions.publicInfo.reporter || rtStructure.enterOptions.publicInfo.optName || '';
        var reportStaff = '万建';
        var examStaff = reportInfo.examStaff || rtStructure.enterOptions.publicInfo.reporter || rtStructure.enterOptions.publicInfo.optName || '';
        $(".bgys").val(reportStaff);  //报告医生
        $(".jcjs").val(examStaff);  //检查技师
        if(!$('.rptDate').val()) {
          var date = getCurDateAndTime().curDate;
          $('.rptDate').val(reportInfo.reportDate || date);
        }
        $('[id="jxhem-rt-50"]').val('结合病史进一步检查。');
      } else {
        // 多选赋值回显
        xmSelect.get().forEach(function(opt) {
          var vm = $(opt.options.dom);
          var val = vm.next('.hide-val').val();
          if(val) {
            var valArr = val.split(',').map(function(name){
              return {title: name, id: name};
            })
            opt.setValue(valArr)
          }
        })
      }
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = $('[id="jxhem-rt-50"]').val();
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: $('[id="jxhem-rt-52"]').val() || '', //报告医生
    reportDate: $('[id="jxhem-rt-53"]').val() || '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
    rptImageList: rptImageList
  }
  console.log(rtStructure);
}

function fillConfigReporter() {
  var dataMap = {
    'jcjs': window.configData.examDoctorList,
    'bgys': window.configData.reportDoctorList,
  }
  for(var key in dataMap) {
    var curSelect = curElem.find('.' + key);
    var html = '';
    for(var item of dataMap[key]) {
      html += `<option value="${item.name}">${item.name}</option>`;
    }
    curSelect.html(html);
  }
}

// 将下拉数据补全和初始化多选，下拉编辑
function fillSelectOption() {
  let dropdown = layui.dropdown;
  for(var key in optAllData) {
    var curSelect = curElem.find('[opt-name="'+key+'"]');
    var multi = $(curSelect).attr('multi');
    multi !== '1' && optAllData[key].unshift('');
    var data = optAllData[key].map(function(item) {
      return {
        title: item,
        id: item || Date.now()
      }
    })
    if(multi === '1') {  //多选
      xmSelect.render({
        el: '[opt-name="'+key+'"]', 
        tips: '',
        prop: {
          name: 'title',
          value: 'id',
        },
        model: {
          label: {
            type: 'text',
            //使用字符串拼接的方式
            text: {
              //左边拼接的字符
              left: '',
              //右边拼接的字符
              right: '',
              //中间的分隔符
              separator: ',',
            },
          }
        },
        data: data,
        hide: function() {
          xmSelect.get().forEach(function(opt) {
            var vm = $(opt.options.dom);
            vm.next('.hide-val').val(vm.find('.label-content').text());
          })
        }
      })
    } else {
      dropdown.render({
        elem: '[opt-name="'+key+'"]', 
        data: data, 
        className: 'laySelLab', 
        click: function (obj) {
          this.elem.val(obj.title);
        },
      });
    }
  }
}

// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('laydate', function () {
      var laydate = layui.laydate;
      //执行一个laydate实例
      laydate.render({
          elem: '#jxhem-rt-53', //指定元素
          type: 'date',
          trigger: 'click',
          format: 'yyyy-MM-dd'
      });
  });
}

// 加载截图
function loadFile() {
  let dom = document.getElementById("jxhRptImg");
  const removeImgWrap = function(ele) {
    if(rtStructure.enterOptions.type==='view') {
      ele && $(ele).remove();
    }
  }
  if(dom && rptImageList && rptImageList.length) {
    let earImage = rptImageList.filter(image => image.fileName.includes('_jxh'));
    if(earImage && earImage.length) {
      let {src} = earImage[0];
      if(src) {
        let img = `<img src="${src}">`;
        dom.innerHTML = img;
        if(rtStructure.enterOptions.type==='view') {
          dom.setAttribute('contenteditable', false);
        }
        dom.style.display = "block";
      } else {
        removeImgWrap(dom);
      }
    } else {
      removeImgWrap(dom);
    }
  } else {
    removeImgWrap(dom);
  }
}

// 截图区域事件
function pastWrapHandler() {
  // 点击
  curElem.find("#jxhRptImg").on('click', function(e) {
    clickToFocusHandler()
  })
  // 粘贴
  curElem.find("#jxhRptImg").on('paste', function(e) {
    pasteImgHandler(e)
  })
  // 输入拼音结束
  curElem.find("#jxhRptImg").on('compositionend', function(e) {
    stopInputHandler(e, true)
  })
  // 按下字母数字键
  curElem.find("#jxhRptImg").on('keypress', function(e) {
    stopInputHandler(e)
  })
  // 输入时
  curElem.find("#jxhRptImg").on('keydown', function(e) {
    inputToDeleteImgHandler(e)
  })
}
// 阻止输入文本内容
function stopInputHandler(e, isReset) {
  e.preventDefault();
  if(isReset) {
    let img = e.target.querySelector('img');
    e.target.innerHTML = '';
    if(img) {
      e.target.appendChild(img);
    }
  }
}
// 删除图片
function inputToDeleteImgHandler(e) {
  if(e.key.toLowerCase() === 'backspace') {
    let list = rptImageList.filter(image => !image.fileName.includes('_jxh'));
    rptImageList = list;
  }
}
// 获取焦点
function clickToFocusHandler() {
  if(rtStructure.enterOptions.type==='view') {
    return;
  }
  var dom = document.getElementById('jxhRptImg');
  var range = document.createRange();
  range.selectNodeContents(dom);
  range.collapse(false);
  var sel = window.getSelection();
  sel.removeAllRanges();
  sel.addRange(range);
}
// 监听截图粘贴图片
function pasteImgHandler(event) {
  if(rtStructure.enterOptions.type==='view') {
    return;
  }
  document.getElementById('jxhRptImg').innerHTML = '';
  if (event.clipboardData || event.originalEvent) {
    let clipboardData = (event.clipboardData || event.originalEvent.clipboardData);
    if(clipboardData.items){
      let items = clipboardData.items,
          len = items.length,
          blob = null;
      for (let i = 0; i < len; i++) {
        if (items[i].type.indexOf("image") !== -1) {
          blob = items[i].getAsFile();
          uploadFile(blob);
        } else {
          setTimeout(() => {
            document.getElementById('jxhRptImg').innerHTML = '';
            let list = rptImageList.filter(image => !image.fileName.includes('_jxh'));
            rptImageList = list;
          }, 20)
          break;
        }
      }
    }
  }
}

function uploadFile(blob) {
  let curImage = {
    blob: blob, 
    specialTxt: '_jxh', 
    imageUid: Date.now(),
    src: window.URL.createObjectURL(blob),
  }
  curImage.fileName = 'RptImg_1_1' + curImage.specialTxt + curImage.imageUid;
  let list = rptImageList.filter(image => !image.fileName.includes(curImage.specialTxt));
  rptImageList = [...list, curImage];
}

// VAS评分->VAS   THI评分->THI
function getResScore() {
  // VAS评分->VAS
  $("#jxhem-rt-35").blur(function() {
    var val = $(this).val();
    $("#jxhem-rt-49").val(val);
  })

  // THI评分->THI
  // 轻微（耳鸣残疾量表THI:） 1~16分（THI评分）
  // 轻度 17~36分
  // 中度 37~56分
  // 重度 57~76分
  // 极重度 77~100分
  $("#jxhem-rt-33").blur(function() {
    var val = $(this).val();
    var res = '';
    if(!isNaN(Number(val))) {
      if(Number(val) >= 77) {
        res = '极重度';
      } else if(Number(val) >= 57 && Number(val) <= 76) {
        res = '重度';
      } else if(Number(val) >= 37 && Number(val) <= 56) {
        res = '中度';
      } else if(Number(val) >= 17 && Number(val) <= 36) {
        res = '轻度';
      } else if(Number(val) <= 16) {
        res = '轻微';
      }
    } 
    $("#jxhem-rt-47").val(res);
  })
}