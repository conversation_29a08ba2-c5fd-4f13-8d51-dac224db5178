<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/agreeTemplate/caTemplate/caTemplate.js"></script>
<style>
#ca1{font-size:14px;height:100%;overflow: auto;padding:12px 0;}
#ca1 .main-wrap{width:780px;margin: 0 auto;height: 100%;background: #FFF;overflow:hidden;}
#ca1 #caResPage{width:100%;height:100%;display: none;}
.ca1.operaBtn{text-align:center;height:52px;padding-top:8px;width:100%;background:#DCDFE6;border-top: 1px solid rgba(0,0,0,.1);position: absolute;left:0;right:0;bottom:0;}
.ca1.operaBtn .opt-btn {padding: 0 8px;height: 36px;line-height: 36px;display: inline-block;border-radius: 3px;background: #1885F2;color: #fff;font-size: 14px;cursor: pointer;border: none;}
.ca1.operaBtn .opt-btn#refreshBtn{background: #FFF;color:#606266;}
.ca1.operaBtn .opt-btn img{vertical-align: middle;}
.ca1.operaBtn .opt-btn+.opt-btn{margin-left: 8px;}
.ca1.operaBtn .opt-btn:not([disabled]):hover{opacity: 0.7;filter: Alpha(opacity=70);}
.ca1.operaBtn .opt-btn[disabled] {opacity: 0.7;filter: Alpha(opacity=70);background:#999;cursor: not-allow;}
#agreement .layout-header,#agreement .layout-footer{display: none;}
.layout-page{padding: 0 !important;}
#agreement .layout-body{padding: 0 !important;}
#ca1 .no-data {text-align: center;margin: 0 auto;margin-top: 80px;}
#ca1 .no-data .tip-msg{margin-top: 20px;font-size: 20px;color: #000}
</style>
<!-- 保存的区域 -->
<ul class="t-pg ca-ly" style="height: 100%;padding-bottom:52px;position:relative;background: #C0C4CC;">
  <li class="page" id="ca1">
    <div class="main-wrap">
      <iframe src="" frameborder="0" id="caResPage"></iframe>
      <div class="no-data">
        <div><img src="/template-lib/assets/images/no-data.png" alt="" class="dev-img"></div>
        <div class="tip-msg"></div>
      </div>
    </div>
  </li>
  <div class="ca1 operaBtn">
    <button class="opt-btn" id="signBtn" onclick="sendCaOpt('0')">
      <img src="/template-lib/layouts/assets/images/sign-icon.png" alt="" class="dev-img">
      签署
    </button>
    <button class="opt-btn" id="refreshBtn" onclick="refreshSignRes()">
      <img src="/template-lib/layouts/assets/images/refresh-icon.png" alt="" class="dev-img">
      刷新
    </button>
  </div>
</ul>