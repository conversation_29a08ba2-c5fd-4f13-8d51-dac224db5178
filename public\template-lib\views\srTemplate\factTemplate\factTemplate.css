@charset "UTF-8";
#fact1 {
  font-size: 14px;
  background-color: #fff;
  height: 100%;
}

.preview .preview-item {
  display: flex;
  margin-bottom: 4px;
}
.preview .preview-item .preview-item-label {
  width: 100px;
  text-align: right;
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}
.preview .preview-item .preview-item-content {
  flex: 1;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.container .container-header {
  height: 40px;
  line-height: 40px;
  background: #E8F3FF;
  box-shadow: inset 1px 0px 0px 0px #C8D7E6, inset -1px -1px 0px 0px #C8D7E6;
}
.container .container-header .pat-info {
  width: 960px;
  margin: 0 auto;
  font-size: 18px;
  font-weight: bold;
}
.container .container-content {
  flex: 1;
  overflow: auto;
  width: 960px;
  margin: 0 auto;
  background: #fff;
  border-right: 1px solid #DCDFE6;
  border-left: 1px solid #DCDFE6;
}
.container .container-content .lb-container {
  display: flex;
  flex-direction: column;
  row-gap: 2px;
}
.container .container-content .lb-container .lb-item {
  display: flex;
  align-items: center;
}
.container .container-content .lb-container .lb-item .lb-col-1 {
  width: 84px;
  text-align: right;
  color: #303133;
  font-weight: 600;
}
.container .container-content .lb-container .lb-item .lb-col-2 {
  width: 154px;
  color: #000;
}
.container .container-content .lb-container .lb-item .lb-col-3 {
  width: 160px;
  margin: 0 8px;
}
.container .container-content .lb-container .lb-item .lb-col-4 {
  width: 160px;
  margin-right: 8px;
}
.container .container-content .bz-item {
  padding: 8px;
}
.container .container-content .bz-form-item {
  display: flex;
  margin-top: 8px;
}
.container .container-content .bz-form-item .bz-form-item-label {
  width: 110px;
  text-align: right;
  padding-top: 2px;
}
.container .container-content .bz-form-item .bz-form-item-content {
  flex: 1;
}
.container .container-content .base-card {
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}
.container .container-content .sub-card {
  background: #EBEEF5;
  border: 1px solid #C8D7E6;
}
.container .container-content .bz-wrap {
  margin-top: 8px;
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}
.container .container-content .bz-tit-ls::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
.container .container-content .close-icon {
  color: #303133;
  width: 16px;
  height: 16px;
  line-height: 16px;
  font-weight: normal;
  font-size: 18px;
  font-family: "Microsoft YaHei";
  text-align: center;
  cursor: pointer;
}
.container .container-content .close-icon:hover {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #e1e1e1;
  color: #fff;
}
.container .container-content .bz-tit-i.act {
  position: relative;
  background: #EBEEF5;
  color: #1885F2;
  font-weight: bold;
}
.container .container-content .bz-tit-i {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 20px;
  color: #303133;
  border-right: 1px solid #DCDFE6;
}
.container .container-content .bz-tit-ls {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  height: 32px;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  display: -webkit-box;
  display: -moz-box;
}
.container .container-footer {
  height: 104px;
  background: #E8F3FF;
  border: 1px solid #DCDFE6;
  padding: 8px 0;
}
.container .container-footer .footer-impression {
  height: 100%;
  display: flex;
  width: 960px;
  margin: 0 auto;
}
.container .container-footer .footer-impression .impression-label {
  display: inline-block;
  font-size: 18px;
  font-weight: bold;
  padding-right: 16px;
  width: 114px;
  text-align: right;
}
.container .container-footer .footer-impression .impression-label-preview {
  text-align: center;
}
.container .container-footer .footer-impression-preview {
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
  padding: 8px;
}
.container textarea {
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 6px 12px;
  width: 100%;
  height: 120px;
}
.container .rt-sr-r {
  margin-right: 4px;
}
.container .p-8 {
  padding: 8px;
}
.container .pl-24 {
  padding-left: 24px;
}
.container .pr-4 {
  padding-right: 4px;
}
.container .pr-24 {
  padding-right: 24px;
}
.container .pt-8 {
  padding-top: 8px;
}
.container .pb-8 {
  padding-bottom: 8px;
}
.container .mt-12 {
  margin-top: 12px;
}
.container .mt-8 {
  margin-top: 8px;
}
.container .mt-6 {
  margin-top: 6px;
}
.container .mt-4 {
  margin-top: 4px;
}
.container .m-0-6 {
  margin: 0 6px;
}
.container .w-10 {
  width: 10px;
}
.container .w-15 {
  width: 15px;
}
.container .w-20 {
  width: 20px;
}
.container .w-25 {
  width: 25px;
}
.container .w-30 {
  width: 30px;
}
.container .w-35 {
  width: 35px;
}
.container .w-40 {
  width: 40px;
}
.container .w-45 {
  width: 45px;
}
.container .w-50 {
  width: 50px;
}
.container .w-55 {
  width: 55px;
}
.container .w-60 {
  width: 60px;
}
.container .w-65 {
  width: 65px;
}
.container .w-70 {
  width: 70px;
}
.container .w-75 {
  width: 75px;
}
.container .w-80 {
  width: 80px;
}
.container .w-85 {
  width: 85px;
}
.container .w-90 {
  width: 90px;
}
.container .w-95 {
  width: 95px;
}
.container .w-100 {
  width: 100px;
}
.container .w-105 {
  width: 106px;
}
.container .w-110 {
  width: 110px;
}
.container .w-115 {
  width: 115px;
}
.container .w-120 {
  width: 120px;
}
.container .w-125 {
  width: 125px;
}
.container .w-130 {
  width: 130px;
}
.container .w-135 {
  width: 135px;
}
.container .w-140 {
  width: 140px;
}
.container .w-145 {
  width: 145px;
}
.container .w-150 {
  width: 150px;
}
.container .w-155 {
  width: 155px;
}
.container .w-160 {
  width: 160px;
}
.container .w-165 {
  width: 165px;
}
.container .f-1 {
  flex: 1;
}
.container .f-1-5 {
  flex: 1.5;
}
.container .f-1-6 {
  flex: 1.6;
}
.container .f-2 {
  flex: 2;
}
.container .f-3 {
  flex: 3;
}
.container .fw-600 {
  font-weight: 600;
}
.container .a-center {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.container .a-start {
  display: flex;
  align-items: flex-start;
}
.container .flex {
  display: flex;
}
.container .flex span:first-child {
  white-space: nowrap;
}
.container .flex-column {
  display: flex;
  flex-direction: column;
}
.container .text-r {
  text-align: right;
}
.container .fs-36 {
  font-size: 36px;
}
.container .fs-24 {
  font-size: 24px;
}
.container .fs-20 {
  font-size: 20px;
}
.container .mr-20 {
  margin-right: 20px;
}

.form-item {
  display: flex;
  margin-top: 8px;
  padding-right: 8px;
}

.form-item-label {
  width: 82px;
  text-align: right;
}

.form-item-content {
  flex: 1;
  overflow: hidden;
}

.add-btn {
  display: inline-block;
  line-height: 30px;
  height: 30px;
  padding: 0px 8px;
  background: #1885F2;
  border-radius: 3px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
}

img {
  vertical-align: middle;
}

.border {
  border: 1px solid #DCDFE6;
}

.layui-input {
  height: 28px !important;
}

[isView=true] #fact1 .edit {
  display: none;
}

[isView=true] #fact1 .preview {
  display: block;
  padding: 8px;
}
