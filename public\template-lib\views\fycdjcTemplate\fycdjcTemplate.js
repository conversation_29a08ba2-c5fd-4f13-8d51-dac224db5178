// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
  'cdjc-rt-21': { label: '送检医生', wt: '1', hisKey: 'optName' }
}
var saveApplyParams = {
  'sampleName': { label: '送检标本', id: 'cdjc-rt-14' },
  'medRecord': { label: '主诉及病史', id: 'cdjc-rt-17'},
  'clinDiag': { label: '临床诊断', id: 'cdjc-rt-19'},
  'optName': { label: '送检医生', id: 'cdjc-rt-21'},
  'applyItemList': { label: '检查项目', id: ''},
  'gmSampleList': { label: '标本信息', id: ''},
}

function initHtmlScript(ele) {
  // 编辑
  if($(ele).attr('isview') !== 'true') {
      initDatePicker();
      if($(".sampleViewPart").length) {
          specialBlockViewHandler('sample', false)
      }
      if($(".eItemViewPart").length) {
          specialBlockViewHandler('examItem', false)
      }
      setDisabledByConfig && setDisabledByConfig(applyStatusRes);
  } else { // 预览
      if($(".sampleViewPart").length) {
          specialBlockViewHandler('sample', true)
      }
      if($(".eItemViewPart").length) {
          specialBlockViewHandler('examItem', true)
      }
  }
  getExamSubClassList(applyInfo);  //检查子类
  getGmSamplePartByLevelList();   //检查部位
  getSampleList(applyInfo);  //标本
  setValByHis(hisAndFormMap)
}

// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('laydate', function () {
      var laydate = layui.laydate;
      //执行一个laydate实例
      laydate.render({
          elem: '.date-wrap', //指定元素
          type: 'date',
          trigger: 'click',
          format: 'yyyy-MM-dd'
      });
  });
}

$(function () {
  window.initHtmlScript = initHtmlScript;
})