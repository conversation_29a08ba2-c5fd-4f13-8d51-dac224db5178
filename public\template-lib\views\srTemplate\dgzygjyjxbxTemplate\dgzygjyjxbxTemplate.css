.singleDisEditReport.main-page{
  min-width: unset;
}
#dgzygjyjxbx1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#dgzygjyjxbx1 * {
  font-family: STSongti-SC, STSongti-SC;
}
#dgzygjyjxbx1 .dgzyCg-edit{
  padding: 8px 12px;
}
#dgzygjyjxbx1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#dgzygjyjxbx1 .black-lb {
  color: #303133;
}
#dgzygjyjxbx1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#dgzygjyjxbx1 .blue-lb:hover {
  opacity: 0.8;
}
#dgzygjyjxbx1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#dgzygjyjxbx1 .text-size, .diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#dgzygjyjxbx1 .text-size img, .diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#dgzygjyjxbx1 .text-size img:hover, .diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#dgzygjyjxbx1 .text-size .on, .diag-text-size .text-size .on {
  display: none;
}
#dgzygjyjxbx1 .editor-area {
  /* padding: 4px 0; */
  min-height: 100px;
}
.editor-area textarea {
  width: 100%;
  min-height: 100px;
  border: none;
  font-size: 16px;
  resize: vertical;
}
#dgzygjyjxbx1 .p-item + .p-item {
  margin-top: 12px;
}
.editor-area.default textarea{
  font-size: 16px;
}
.editor-area.large textarea{
  font-size: 18px;
}
.editor-area.larger textarea{
  font-size: 20px;
}
#dgzygjyjxbx1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#dgzygjyjxbx1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#dgzygjyjxbx1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#dgzygjyjxbx1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#dgzygjyjxbx1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#dgzygjyjxbx1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

*:disabled {
  background: #fff;
  color: unset;
}

.item_line {
  display: inline-block;
  height: 28px;
  
}
.item_block {
  display: block;
  height: 28px;
  line-height: 28px;
}
.item_line .layui-input , .item_block .layui-input {
  height: 28px;
  line-height: 28px;
}
.mb4{
  margin-bottom: 4px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb8 {
  margin-bottom: 8px;
}
.textTitle {
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  line-height: 28px;
}
.item_box {
  display: flex;
}
.item_box_l {
  width: 96px;
  text-align: right;
}
.item_box_c {
  width: 210px;
}
.item_box_r {
  width: calc(100% - 306px);
}
.text_p {
  font-weight: 400;
  font-size: 12px;
  color: #000000;
  line-height: 22px;
  text-align: left;
}

.d-item {
  margin-top: 8px;
  display: flex;
}
.d-item .info-i-l {
  width: 357px;
}
.d-item .info-i-r {
  width: calc(100% - 357px);
}

.body_center {
  display: flex;
}
.body_center-l {
  width: 375px;
}
.body_center-r {
  width: calc(100% - 375px);
}

.view_item {
  margin-top: 8px;
  display: flex;
}
.view_item_l {
  width: 109px;
  font-size: 16px;
  color: #000000;
  line-height: 22px;
  text-align: left;
}
.view_item_c {
  width: 220px;
  font-size: 16px;
  color: #000000;
  line-height: 22px;
  text-align: left;
}
.view_item_r {
  width: calc(100% - 329px);
  font-size: 16px;
  color: #000000;
  line-height: 22px;
  text-align: left;
}
.mb16 {
  margin-bottom: 16px;
}
.view_text_p {
  width: 497px;
  height: 40px;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  line-height: 20px;
}
.fw900 {
  font-weight: 900;
}

textarea[readonly] {
  cursor: not-allowed;
}

.dib{
  position: relative;
}
#dgzygjyjxbx1 .dib::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 10px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}

.desc-con_box {
  display: block;
}
.desc-con_box .gray-txt{
  padding-left: 32px;
  min-height: 50px;
  padding-top: 4px;
  margin-bottom: 4px;
}
.borderBox {
  border-bottom: 2px solid #000;
  margin: 8px 0;
}
#dgzygjyjxbx1 .dgzyCg-view {
  display: none;
}
[isview="true"] #dgzygjyjxbx1 .dgzyCg-edit {
  display: none;
}
[isview="true"] #dgzygjyjxbx1 .dgzyCg-view {
  display: flex;
}
#dgzygjyjxbx1 .dgzyCg-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 115px 56px;
  flex-direction: column;
  position: relative;
}
#dgzygjyjxbx1 .dgzyCg-view .view-head {
  border-bottom: 2px solid #000;
  padding-bottom: 9px;
  text-align: center;
}
#dgzygjyjxbx1 .dgzyCg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
}
#dgzygjyjxbx1 .dgzyCg-view .logo-tit img {
  width: 80px;
  height: 80px;
  margin-right: 16px;
}
#dgzygjyjxbx1 .dgzyCg-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
}
#dgzygjyjxbx1 .dgzyCg-view .blh-tit [data-key="patLocalId"]{
  color: #E64545;
}
#dgzygjyjxbx1 .dgzyCg-view .hos-tit{
  font-weight: 900;
  font-size: 40px;
  color: #000;
  font-family: '楷体';
  letter-spacing: 14px;
}
#dgzygjyjxbx1 .dgzyCg-view .sub-tit{
  font-size: 28px;
  color: #E64545;
  font-weight: 900;
  margin-top: 8px;
  font-family: '楷体';
}
#dgzygjyjxbx1 .dgzyCg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 2px solid #000;
}
#dgzygjyjxbx1 .dgzyCg-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
}
#dgzygjyjxbx1 .dgzyCg-view .black-txt {
  color: #000;
  font-size: 16px;
}
#dgzygjyjxbx1 .dgzyCg-view .red-txt {
  color: #E64545;
  font-size: 16px;
}
#dgzygjyjxbx1 .dgzyCg-view .bold {
  font-weight: bold;
}
#dgzygjyjxbx1 .dgzyCg-view .info-i {
  width: 155px;
  display: flex;
  flex-wrap: wrap;
}
#dgzygjyjxbx1 .dgzyCg-view .info-i.w170 {
  width: 170px;
}
#dgzygjyjxbx1 .dgzyCg-view .info-i + .info-i {
  margin-left: 8px;
}
#dgzygjyjxbx1 .dgzyCg-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#dgzygjyjxbx1 .dgzyCg-view .view-patient .p-item {
  margin-top: 8px;
}
#dgzygjyjxbx1 .dgzyCg-view .body-exam-info {
  border-bottom: 2px solid #000;
  padding: 8px 0;
  font-size: 16px;
  color: #000;
}
#dgzygjyjxbx1 .dgzyCg-view .body-exam-info .b-exam-lb {
  font-weight: bold;
}
#dgzygjyjxbx1 .dgzyCg-view .body-exam-info .b-exam-info {
  flex: 1;
}
#dgzygjyjxbx1 .dgzyCg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#dgzygjyjxbx1 .dgzyCg-view .desc-con {
  display: none;
  margin-top: 10px;
  line-height: 22px;
}
#dgzygjyjxbx1 .dgzyCg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#dgzygjyjxbx1 .dgzyCg-view .desc-con {
  display: flex;
  align-items: baseline;
}
#dgzygjyjxbx1 .dgzyCg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#dgzygjyjxbx1 .dgzyCg-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#dgzygjyjxbx1 .dgzyCg-view .reporter-i span:last-child {
  flex: 1;
}
#dgzygjyjxbx1 .dgzyCg-view .reporter-i img {
  width: 90px;
  height: 40px;
  object-fit: contain;
  margin-left: 5px;
}
#dgzygjyjxbx1 .dgzyCg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#dgzygjyjxbx1 .dgzyCg-view .tip-wrap {
  margin-top: 8px;
  font-weight: 900;
  font-size: 14px;
  color: #E64545;
}
#dgzygjyjxbx1 .dgzyCg-view .rpt-img-ls {
  /* display: none; */
  flex-wrap: wrap;
  margin-bottom: 8px;
  padding-left: 0;
  padding-bottom: 8px;
  /* border-bottom: 2px solid #000; */
}
#dgzygjyjxbx1 .dgzyCg-view .item-img {
  display: inline-block;
  width: 292px;
  height: 200px;
  border: 1px solid #eee;
  margin-top: 8px;
}
/* #dgzygjyjxbx1 .dgzyCg-view .item-img:nth-child(odd) {
  margin-right: 20px;
}
#dgzygjyjxbx1 .dgzyCg-view .item-img:nth-child(even) {
  margin-left: 20px;
} */
/* #dgzygjyjxbx1 .dgzyCg-view .rpt-img-ls[data-len="3"] .item-img{
  width: 210px;
  height: 160px;
}
#dgzygjyjxbx1 .dgzyCg-view .rpt-img-ls[data-len="3"],
#dgzygjyjxbx1 .dgzyCg-view .rpt-img-ls[data-len="4"] {
  padding-left: 0;
}
#dgzygjyjxbx1 .dgzyCg-view .rpt-img-ls[data-len="4"] .item-img {
  width: 158px;
  height: 120px;
  margin: 0 4px 8px 4px;
} */
#dgzygjyjxbx1 .dgzyCg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#dgzygjyjxbx1 .p-item:nth-child(2) .text-size,#dgzygjyjxbx1 .p-item:nth-child(3) .text-size,#dgzygjyjxbx1 .p-item:nth-child(4) .text-size {
  display: none;
}
#dgzygjyjxbx1 .p-item:nth-child(2) .editor-area,#dgzygjyjxbx1 .p-item:nth-child(3) .editor-area {
  min-height: 72px;
}
#dgzygjyjxbx1 .p-item:nth-child(2) textarea,#dgzygjyjxbx1 .p-item:nth-child(3) textarea {
  min-height: 72px;
}
#dgzygjyjxbx1 .p-item:nth-child(4) .editor-area {
  min-height: 336px;
}
#dgzygjyjxbx1 .p-item:nth-child(4) textarea {
  min-height: 336px;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #dgzygjyjxbx1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #dgzygjyjxbx1 .dgzyCg-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #dgzygjyjxbx1 .dgzyCg-view .view-head,
[entry-type="5"] #dgzygjyjxbx1 .dgzyCg-view .view-patient,
[entry-type="5"] #dgzygjyjxbx1 .dgzyCg-view .tip-wrap {
  display: none;
}
[entry-type="5"] #dgzygjyjxbx1 .dgzyCg-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #dgzygjyjxbx1 .dgzyCg-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
