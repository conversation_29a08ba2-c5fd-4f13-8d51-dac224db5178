#hlhz1 {
  font-size: 14px;
  color: #303133;
}
#hlhz1 .hlhz-content{
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#hlhz1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#hlhz1 .form-content{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 12px 16px;
}
#hlhz1 .ml-6{
  margin-left: 6px;
}
#hlhz1 .form-content .form-item{
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 12px;
}
#hlhz1 .form-item-title{
  color: #606266;
}
#hlhz1 .input-width{
  width: 204px;
}
#hlhz1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#hlhz1 .rt-sr-lb {
  margin-left: 4px;
}
#hlhz1 .form-content .gray-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlhz1 .item-content{
  padding: 12px 16px;
  display: flex;
  align-items: start;
}
#hlhz1 .item-title{
  font-weight: 600;
  margin-right: 12px;
  line-height: 14px;
}
#hlhz1 .item-content .gray-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlhz1 .item-content .showInt {
  position: relative;
  background: #fff;
  width: 200px;
}
#hlhz1 .item-content .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}