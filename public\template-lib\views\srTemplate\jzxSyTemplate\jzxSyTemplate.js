$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;
var rtStructure = null;
var allResData = [];
var vueComp = null;
var curBzImageStatus = '';  //病灶图状态，正常zc,增大zd,缩小sx
var curBzImageStatusMap = {'zc':0,'sx':1,'zd':2};   //病灶图标索引
var hasZzyBz = false;   //判断是否存在锥状叶，有则使用正常的锥状叶图
var jzxInfo = {}  //甲状腺基本情况
var jzxShInfo = {}  //甲状腺术后
var lbjInfo = {}  //颈部淋巴结
var canvasLbEle = null;
var lbjImage = null;
var lbjCtx = null;
// 错误反馈回调处理
function errorCallBack(id) {
  var detailDom = $('[id="'+id+'"]').parents('.mb-detail');
  if(detailDom && detailDom.length) {
    if(!detailDom.is(":visible")) {
      var pid = detailDom.attr('data-pid');
      detailDom.show().siblings('.mb-detail').hide();
      $('[id="'+pid+'"]').addClass('act').siblings().removeClass('act');
    }
  }
  var curDom = $('[id="'+id+'"]').parents('[role="tabpanel"]');
  if(curDom && curDom.length) {
    var key = curDom.attr('id').replace('pane-', '');
    vueComp.activeTab = key;
  }
}
function initVueComp() {
  vueComp = new Vue({
		el: '#xmJzxVue',
		data() {
			return {
        activeTab: 'jzx-0000.001',
        bzPaneList: [],  //模板内病灶tabPane
        jzxPointList: [],  //病灶点
        bzDetailList: [],  //病灶详情
			}
		},
    computed: {
    },
		methods: {
      // 删除病灶
      delTab(idx, paneId) {
        this.$confirm('确认删除该病灶?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let delId = 'jzx-0000.' + paneId;
          this.bzPaneList.splice(idx, 1);
          if(this.bzPaneList.length > 0 && this.activeTab === delId) {
            let nextId = idx >= 1 ? idx - 1 : idx;
            this.activeTab = 'jzx-0000.' + this.bzPaneList[nextId].paneId;
          }
          this.$nextTick(() => {
            if(rtStructure && rtStructure.idAndDomMap) {
              for(var key in rtStructure.idAndDomMap) {
                if(key.indexOf(paneId) > -1) {
                  delete rtStructure.idAndDomMap[key];
                }
              }
            }
            getBzDetailInfo();
          })
        })
      },

      // 添加病灶,oldPaneId已保存过的
      addBzHandler(oldPaneId) {
        var paneId = oldPaneId || createUUidFun();
        this.bzPaneList.push({
          paneId: paneId
        })
        let activeTab = 'jzx-0000.' + paneId;
        this.activeTab = activeTab;
        this.$nextTick(() => {
          // 兼容导出数据用到的判断
          if(rtStructure) {
            if(rtStructure.idAndDomMap['']) {
              delete rtStructure.idAndDomMap[''];
            }
            var newPaneBlock = curElem.find('.rt-pane:eq('+(this.bzPaneList.length-1)+')');
            var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
            rtStructure.idAndDomMap[activeTab] = {
              id: activeTab,
              desc: '病灶',
              name: '病灶title',
              pid: 'jzx-0000',
              pvf: '',
              req: '1',
              rtScPageNo: 1,
              value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('病灶' + (this.bzPaneList.length)),
              wt: '',
              vt: '',
              itemList: oldIdAndDom.itemList,
              lastVal: oldIdAndDom.lastVal
            }
            var wCon = curElem.find("[rt-sc]");
            wCon.each(function(wIndex, wItem) {
              var node = $(wItem);
              var id = node.attr('id') || '';
              var pid = node.attr('pid') || '';
              var groupId = node.attr('name') || '';
              var sc = node.attr('rt-sc');
              var scArr = sc.split(';');
              var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
              var scObj = {
                id: id,
                pid: pid,
                rtScPageNo: 1,
                value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
                itemList: childOldIdAndDom.itemList,
                lastVal: childOldIdAndDom.lastVal
              };
              if(groupId) {
                scObj['groupId'] = groupId;
              }
              scArr.forEach(function(scItem) {
                var key = scItem.split(':')[0];
                var value = scItem.split(':')[1];
                if(key) {
                  if(key === 'code') {
                    scObj[key] = value;
                    var findCodeNode = $('[id="'+id+'"].rt-sr-w');
                    findCodeNode.attr('code', value);
                  } else {
                    var numberList = ['left', 'top', 'wt'];
                    scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
                  }
                }
              })
              rtStructure.idAndDomMap[id] = {...scObj};
            })
            rtStructure.init(true);
            rtStructure.initChildDisabled(newPaneBlock[0]);
            if(!oldPaneId) {
              // 默认值
              defValInBzTemp(newPaneBlock);
              document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollLeft = document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollWidth;
              // 默认分数
              // computeScoreHandler(newPaneBlock)
            }
          }
          setTimeout(function() {
            displayBzImage();
          }, 50)
          getBzDetailInfo();
          $(".bz-con .rt-sr-w").off('change')
          // 切换病灶点位置
          // $(".bz-con .rt-sr-w[pointKey]").change(function() {
          //   setBzPosition();
          // })
          // 切换分数
          // $(".bz-con .rt-sr-w[score]").change(function() {
          //   computeScoreHandler($(this).closest('.t-content'));
          // })
          
          $(".bz-con .rt-sr-w:not(.sel-temp)").change(function(){
            if($(this).attr('bzkey') != undefined) {
              displayBzImage();
            }
            getBzDetailInfo();
          })
        })
      }
    }
  })
}

function initHtmlScript(ele) {
  curElem = $(ele);

  initVueComp();
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList);
    if(rtStructure) {
      // 回显病灶
      allResData = rtStructure.enterOptions 
        && rtStructure.enterOptions.resultData ? 
        rtStructure.enterOptions.resultData : [];
      if(allResData && allResData.length) {
        var bzData = allResData.filter(bzItem => bzItem.id === 'jzx-0000')
        if(bzData && bzData.length) {
          var bzList = bzData[0].child || [];
          bzList.forEach((item) => {
            var paneId = item.id.replace('jzx-0000.', '');
            vueComp.addBzHandler(paneId)
          })
        }
      }
    }
  }
  vueComp.$nextTick(function() {
    // 淋巴的canvas初始化
    if(!canvasLbEle) {
      initCanvas();
    }

    // 模版默认的内容赋值
    initTempPage();

    // 病灶数据回显
    if(vueComp.bzPaneList && vueComp.bzPaneList.length) {
      $('[data-pid="jzx-0000"] .rt-sr-w').each(function(i, widget) {
        var id = $(widget).attr('id');
        if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
          rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
        }
      })
    }
    if(window.initPageHandler && typeof window.initPageHandler === 'function') {
      setTimeout(() => {
        window.initPageHandler();
      }, 100)
    }
  })
}

// 描述的内容初始化整体加载一遍
function setDescription(firstIn) {
  getJzxInfo();  //甲状腺基本信息
  getJzxShInfo();   //甲状腺术后
  // 甲状腺病灶动态加载的，不在此处渲染
  getLbjInfo(firstIn);  //颈部淋巴结信息
  getImpression();  //诊断内容
}

// 初始化模板默认内容
function initTempPage() {
  // 未编辑过
  if(!curElem.attr('data-edited')) {
    curElem.find('.def-ck').prop('checked', true);
    curElem.find('.hide-in').hide();
  }

  // 模版类型切换
  toggleMbHandler();
  curElem.find('[data-mtab]').change(function() {
    var mtab = $(this).attr('data-mtab');
    if($(this).is(':checked')) {
      // 甲状腺基本情况 和 甲状腺术后互斥
      if(mtab === 'jz-rt-6') {
        $('[data-mtab="jz-rt-7"]').prop('checked', false);
      }
      if(mtab === 'jz-rt-7') {
        $('[data-mtab="jz-rt-6"]').prop('checked', false);
      }
      if(mtab === 'jzx-0000') {
        if(!vueComp.bzPaneList.length) {
          vueComp.addBzHandler();
        } else {
          getBzDetailInfo();
        }
      }
    } else {
      if(mtab === 'jzx-0000') {
        getBzDetailInfo();
      }
    }
    displayBzImage();
    toggleMbHandler(mtab);
    setDescription();
  });

  // 标签切换
  $(".mb-hd-i").click(function() {
    var id = $(this).attr('id');
    $(this).addClass('act').siblings().removeClass('act');
    $('[data-pid="'+id+'"]').show().siblings('[data-pid]').hide();
  })
  
  // 相关联模块显示隐藏
  toggleContectModule();
  curElem.find('[data-show]').change(function() {
    var mName = $(this).attr('data-show');
    toggleContectModule(mName);
  })

  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem);
  initLigthItemChange(curElem);

  // // 甲状腺术后，根据值的情况展示内容
  // jzxSHDisplay();
  
  // 确定用哪张病灶图
  displayBzImage();
  curElem.find('[bzkey]').change(function() {
    displayBzImage();
  })

  setDescription(true);
  curElem.find('.mb-detail[data-pid="jz-rt-6"] .rt-sr-w').change(function() {
    getJzxInfo();
    getImpression();
  })
  curElem.find('.mb-detail[data-pid="jz-rt-7"] .rt-sr-w').change(function() {
    getJzxShInfo();
    getImpression();
  })
  curElem.find('.mb-detail[data-pid="jz-rt-9"] .rt-sr-w').change(function() {
    getLbjInfo();
    getImpression();
  })
}
function changeLbj(vm) {
  if($(vm).is(":checked")) {
    // 加延时是为了错开原有默认的公共高亮方法
    setTimeout(function() {
      $('[contect-id="jz-rt-131"] .def-ck').prop('checked', true);
    }, 50)
  }
}
// 模版类型显示/隐藏
function toggleMbHandler(tab) {
  var actTab = '';
  var curActIsHide = false;
  curElem.find('[data-mtab]').each(function(i, dom) {
    var mtab = $(dom).attr('data-mtab');
    if($(dom).is(':checked')) {
      if(!actTab) {
        actTab = mtab;
      }
      $('.mb-hd-i[id="'+mtab+'"]').removeClass('rt-hide').show();
      if(tab === mtab) {
        $('.mb-hd-i[id="'+mtab+'"]').addClass('act').siblings('.mb-hd-i').removeClass('act');
        $('.mb-detail[data-pid="'+mtab+'"]').show().siblings('.mb-detail').hide();
      }
      if(tab) {
        actTab = mtab;
        // $('.mb-detail[data-pid="'+mtab+'"]').find('.def-ck').click();
      }
      $('.mb-detail[data-pid="'+mtab+'"]').find('.rt-sr-w').each(function(i, rtW){
        if($(rtW).parents('[data-name]').length === 0) {
          $(rtW).removeClass('rt-hide'); 
        }
      })
    } else {
      if(tab === mtab && $('.mb-hd-i.act').attr('id') === tab) {
        curActIsHide = true;
      }
      $('.mb-hd-i[id="'+mtab+'"]').addClass('rt-hide').hide();
      $('.mb-hd-i[id="'+mtab+'"]').removeClass('act')
      $('.mb-detail[data-pid="'+mtab+'"]').hide();
      $('.mb-detail[data-pid="'+mtab+'"]').find('.rt-sr-w').addClass('rt-hide');  //带rt-hide的节点不回保存数据
    }
  })
  if((!tab && actTab) || (tab && curActIsHide)) {
    $('.mb-hd-i[id="'+actTab+'"]').removeClass('rt-hide').show();
    $('.mb-hd-i[id="'+actTab+'"]').addClass('act').siblings('.mb-hd-i').removeClass('act');
    $('.mb-detail[data-pid="'+actTab+'"]').show().siblings('.mb-detail').hide();
  }
  if(curElem.find('[data-mtab]:checked').length === 1) {
    curElem.find('[data-mtab]').attr('disabled', false)
    curElem.find('[data-mtab]:checked').attr('disabled', true)
  } else {
    curElem.find('[data-mtab]').attr('disabled', false)
  }
  if(!tab || tab === 'jz-rt-7') {
    jzxSHDisplay();
  }
}

// 关联模块显示隐藏
function toggleContectModule(mName) {
  $('[data-name]').hide();
  $('[data-name]').find('.rt-sr-w').addClass('rt-hide'); 
  curElem.find('[data-show]').each(function(i, dom) {
    var showName = $(dom).attr('data-show');
    if($(dom).is(':checked')) {
      $('[data-name="'+showName+'"]').show();
      $('[data-name="'+showName+'"]').find('.rt-sr-w').each(function(i, rtW){
        if($(rtW).parents('[data-name]').attr('data-name')===showName) {
          $(rtW).removeClass('rt-hide'); 
        }
      })
    }
  })
}

// 切换病灶模版
function changeBzTempHandler(vm) {
  var val = $(vm).val();
  var temp = $(vm).find('option[value="'+val+'"]').attr('data-val');
  var pane = $(vm).closest('.t-content');
  // computeScoreHandler(pane);
  defValInBzTemp(pane, temp);
  getBzDetailInfo();
}

// 病灶模板默认值切换
function defValInBzTemp(pane, temp) {
  temp = temp || 'def';
  pane.find('.rt-sr-w:not(.sel-temp)').each(function(i, rtW){
    if($(rtW).attr('data-temp') && $(rtW).attr('data-temp').indexOf(temp) > -1) {
      $(rtW).click();
    } else {
      $(rtW).prop('checked', false);
    }
  })
}

// 分数计算，推出等级
function computeScoreHandler(pane) {
  var totalScore = 0;
  pane.find('[score]:not(.rt-hide):checked').each(function(i,ele){
    var score = $(ele).attr('score');
    totalScore += score ? Number(score) : 0;
  })
  pane.find('.lvTxt').text(scoreAndLevelMap[totalScore]?scoreAndLevelMap[totalScore].level:'0');
  pane.find('.scTxt').text(totalScore);
}

// 确定用哪张病灶图
function displayBzImage() {
  var preSrc = './template-lib/assets/images/xmJzx/';
  var imgName = 'jzx-normal.png';
  curBzImageStatus = 'zc';
  // 是否存在锥状叶的
  var zzyFlag = $("#jz-rt-4:checked").length && $('[bzkey="zzy"]:checked').length;
  if(zzyFlag) {
    imgName = jzxBzImageObj['zzy'];
  } else {
    var mtab = $("#jz-rt-2").is(":checked") ? 'jz-rt-6' : ($("#jz-rt-3").is(":checked") ? 'jz-rt-7' : '');
    if(mtab) {
      var tempArr = [];
      var part = $('[data-pid="'+mtab+'"]');
      for(var i = 0; i < jzxBzJoinKey.length; i++) {
        var key = jzxBzJoinKey[i]
        if(part.find('[bzkey="'+key+'"]:checked').length === 0){
          continue;
        }
        tempArr.push(key);
      }
      if(tempArr.length){
        var joinKy = tempArr.join('-');
        curBzImageStatus = joinKy;
        if(jzxBzImageObj[joinKy]){
          imgName = jzxBzImageObj[joinKy];
        }
      }
    }
  }
  $("#jzx-iName").text(imgName);
  $("#jzx-img").attr('src', preSrc + imgName);
  // 换图的同时，图上的病灶点同步更换，并更新病灶信息
  // setBzPosition()
  if(rtStructure) {
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['bzImageName'] = $("#jzx-iName").text();
  }
  if($('[data-mtab="jz-rt-6"]').is(':checked')) {
    getBzDetailInfo();
  }
}

// 确定病灶位置
function setBzPosition(dom) {
  var pointStyle = {};
  var pointKey = $(dom).find('[pointKey]:checked');
  var tempArr = [];
  hasZzyBz = false;
  pointKey.each(function(i,point){
    if($(point).attr('pointKey')) {
      var pkey = $(point).attr('pointKey')
      tempArr.push(pkey);
      if(!hasZzyBz && pkey === 'zzy') {
        hasZzyBz = true;
      }
    }
  })
  if(tempArr.length) {
    var pointName = tempArr.join('-');
    var bzImgStatus = 'zc';
    if(jzxPosObj[pointName]) {
      for(var key in curBzImageStatusMap) {
        if(curBzImageStatus.indexOf(key) > -1) {
          bzImgStatus = key;
          break;
        }
      }
      var potIndex = curBzImageStatusMap[bzImgStatus];
      var point = jzxPosObj[pointName][potIndex] ? jzxPosObj[pointName][potIndex] : jzxPosObj[pointName][0];
      if(point && point.length) {
        pointStyle = {
          left: point[0],
          top: point[1],
          background: 'red',
        }
      }
    }
  }
  return pointStyle;
}

// 获取所有病灶的字段
function getBzFormVal(dom, paneId) {
  var bmLen = dom.find(`[id="jzx-0000.${paneId}.02.04"]:not(.rt-hide)`).val();
  var obj = {
    level: dom.find(`.lvTxt:not(.rt-hide)`).text() || '',  //等级
    score: dom.find(`.scTxt:not(.rt-hide)`).text() || '',  //评分
    posSide: dom.find(`[name="RG-0000.${paneId}.02.01"]:checked:not(.rt-hide)`).val() || '',  //位置-左\右\峡部\锥状叶
    posTorB: dom.find(`[name="RG-0000.${paneId}.02.02"]:checked:not(.rt-hide)`).val() || '',  //位置-上极\下...
    posOther: dom.find(`[name="RG-0000.${paneId}.02.03"]:checked:not(.rt-hide)`).val() || '',  //位置-具体位置
    bmLen: bmLen ? bmLen + 'cm' : '',  //距包膜最小距离
    hs: dom.find(`[name="RG-0000.${paneId}.04.01"]:checked:not(.rt-hide)`).val() || '',  //回声
    fw: dom.find(`[name="RG-0000.${paneId}.05.01"]:checked:not(.rt-hide)`).val() || '',  //方位
    by: dom.find(`[name="RG-0000.${paneId}.06.01"]:checked:not(.rt-hide)`).val() || '',  //边缘
    jzxQhs: dom.find(`[name="RG-0000.${paneId}.07.01"]:checked:not(.rt-hide)`).val() || '',  //局灶性强回声
    jg: dom.find(`[name="RG-0000.${paneId}.08.01"]:checked:not(.rt-hide)`).val() || '',  //结构性质
    bzCount: dom.find(`[name="RG-0000.${paneId}.09.01"]:checked:not(.rt-hide)`).val() || '',  //病灶数目
    xl: dom.find(`[name="RG-0000.${paneId}.10.01"]:checked:not(.rt-hide)`).val() || '',  //血流
    otherDesc: dom.find(`[id="jzx-0000.${paneId}.11.01"]:not(.rt-hide)`).val() || '',  //其他征象
  }
  var sizeTxt = [];
  var decimLen = 0;
  for(var bIdx = 1; bIdx < 4; bIdx++) {
    var bI = bIdx < 9 ? `0${bIdx}` : bIdx;
    var sizeVal = dom.find(`[id="jzx-0000.${paneId}.03.${bI}"]:not(.rt-hide)`).val();
    if(sizeVal) {
      var [num, decim = ''] = sizeVal.split('.');
      if(decim && decimLen < decim.length) {
        decimLen = decim.length;
      }
      sizeTxt.push(sizeVal);
    }
  }
  var sizeRes = setDecimCount(decimLen, sizeTxt, 'cm')
  if(sizeRes.length) {
    obj.bzSize = sizeRes.join('x');   //大小
  }
  return obj;
}

// 将大小的小数位拼成同样的个数
function setDecimCount(decimLen, sizeArr, unit) {
  var res = []
  for(var i = 0; i < sizeArr.length; i++) {
    var value = sizeArr[i];
    if(decimLen > 0) {
      var [num, decim = ''] = value.split('.');
      var len = decimLen - decim.length;
      if(len > 0) {
        var fillText = new Array(len).fill('0').join('');
        value = num + '.' + decim + fillText;
      }
    }
    res.push(value + unit);
  }
  return res;
}

// 甲状腺术后另一侧未手术的情况
function jzxSHDisplay() {
  if(!$("#jz-rt-3").is(":checked")) {
    $(".unqc-wrap .rt-sr-w").addClass('rt-hide');
    return;
  }
  var shSide = $('[name="r-qcbw"]:checked').val();  //手术侧
  var shRG = $('[name="r-qcbwp"]:checked').val();  //切除情况
  if(shSide && shRG && shSide !== '双侧') {
    var unqcLabel = (shSide === '左侧' ? '右侧' : '左侧') + '甲状腺' + (shRG === '部分切除' ? '及峡部' : '')
    $(".unqc-wrap #jz-rt-95").text(unqcLabel);
    $(".unqc-wrap").css('display', 'flex');
    $(".unqc-wrap .rt-sr-w").removeClass('rt-hide');
    if(shRG !== '部分切除') {
      $(".sh-xb").hide();
      $(".sh-xb .rt-sr-w").addClass('rt-hide');
    } else {
      $(".sh-xb").show();
      $(".sh-xb .rt-sr-w").removeClass('rt-hide');
    }
  } else {
    $(".unqc-wrap").hide();
    $(".unqc-wrap .rt-sr-w").addClass('rt-hide');
  }
  if(shSide === '双侧' && shRG === '部分切除') {
    $('.cyText').text('残余甲状腺及峡部');
  } else {
    $('.cyText').text('残余甲状腺');
  }
}

// 甲状腺基本信息汇总
function getJzxInfo() {
  var jzxInfoArr = [];
  if($("#jz-rt-2").is(":checked")) {
    // 甲状腺大小 --start
    var lrSide = ['jz-rt-11', 'jz-rt-16', 'jz-rt-21'];
    var lrSizeArr = [];
    for(var i = 0; i < lrSide.length; i++) {
      var childEl = $('[pid="'+lrSide[i]+'"]');
      var childValArr = [];
      var childChArr = [];
      var decimLen = 0;
      childEl.each(function(){
        if($(this).val()) {
          sizeVal = $(this).val();
          var [num, decim = ''] = sizeVal.split('.');
          if(decim && decimLen < decim.length) {
            decimLen = decim.length;
          }
          childValArr.push(sizeVal);
          if($(this).attr('placeholder')) {
            childChArr.push($(this).attr('placeholder'));
          }
        }
      })
      if(childValArr.length) {
        var sizeRes = setDecimCount(decimLen, childValArr, 'cm')
        sizeRes.forEach(function(sizeItem, chIdx){
          sizeRes[chIdx] = (childChArr[chIdx] || '') + sizeItem;
        })
        var size = sizeRes.join('，');   //大小
        var sideName = $('[id="'+lrSide[i]+'"]').text();
        lrSizeArr.push(sideName + size);
      }
    }
    if(lrSizeArr.length) {
      jzxInfoArr.push('甲状腺大小：' + lrSizeArr.join('；'))
    }
    // 甲状腺大小 --end

    // 除大小外的描述拼成一行
    var resetArr = [];
    // 正常/缺如情况 --start
    if($('[name="r-size"]:checked').val()){
      var norVal = $('[name="r-size"]:checked').val();
      // 正常情况不用描述
      if(norVal.indexOf('正常') === -1){
        var norId = $('[name="r-size"]:checked').attr('id');
        var childNorVal = $('[pid="'+norId+'"]:checked').val();
        if(childNorVal || norVal.indexOf('缺如') > -1) {
          if(norVal.indexOf('缺如') > -1) {
            resetArr.push(norVal);
          }
          if(childNorVal && childNorVal.indexOf('正常') === -1) {
            resetArr.push(childNorVal);
          }
        }
      }
    }
    // 正常/缺如情况 --end

    // 其他剩余选项 --start
    var otherId = ['jz-rt-40', 'jz-rt-44', 'jz-rt-48', 'jz-rt-53', 'jz-rt-61', 'jz-rt-67'];
    otherId.forEach(function(oItem){
      var oChildVal = '';
      if(oItem === 'jz-rt-67') {
        oChildVal = $('[pid="'+oItem+'"]').val();
        if(oChildVal) {
          resetArr.push('其他征象：' + oChildVal);
        }
      } else {
        oChildVal = $('[pid="'+oItem+'"]:checked').val();
        if(oChildVal) {
          var radioText = $('[id="'+oItem+'"]').text() + oChildVal;
          // 腺体回声不均匀
          if(oItem === 'jz-rt-53' && oChildVal === '不均匀' && $('[name="r-bjy"]:checked').val()){
            radioText += '，' + $('[name="r-bjy"]:checked').val();
          }
          // 血流信号-血供丰富
          if(oItem === 'jz-rt-61' && oChildVal === '血供丰富' && $('[id="jz-rt-64"]').val()){
            radioText += '，甲状腺上动脉流速' + $('[id="jz-rt-64"]').val() + 'cm/s';
          }
          resetArr.push(radioText);
        }
      }
    })
    // 其他剩余选项 --end

    if(resetArr.length) {
      jzxInfoArr.push(resetArr.join('，'));
    }
  }
  if(rtStructure) {
    var jzxInfoStr = jzxInfoArr.length ? ((jzxInfoArr[0].indexOf('甲状腺')===-1?'甲状腺':'') + jzxInfoArr.join('。\n') + '。') : '';
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['jzxInfoStr'] = jzxInfoStr;
  }
  // console.log('getJzxInfo', rtStructure.description);
}

// 甲状腺术后信息汇总
function getJzxShInfo() {
  var shInfoObj = {};
  var shInfoArr = [];
  var shqkTxt = '';  //甲状腺切除情况
  if($("#jz-rt-3").is(":checked")) {
    var qcbw = $('[name="r-qcbw"]:checked').val();   //切除部位
    var qcqk = $('[name="r-qcbwp"]:checked').val();   //切除情况
    if(qcbw && qcqk) {
      shqkTxt = qcbw + '甲状腺' + qcqk + '术后';
      var xths = $('[name="r-rcy-xths"]:checked:not(.rt-hide)').val() || '';  //腺体回声
      var xlxh = $('[name="r-rcy-xlxh"]:checked:not(.rt-hide)').val() || '';  //血流信号
      var ljzxc = $('[name="r-ljzxc"]:checked:not(.rt-hide)').val() || '';  //甲状腺床
      shInfoObj = { shqkTxt, xths, xlxh, ljzxc, qcbw, qcqk };
      
      if(qcqk !== '全切') {
        if(xths === '均匀' && xlxh === '未见异常') {
          joinTxt = qcbw + '残余甲状腺未见异常';
        } else {
          var hsDetail = $('[name="r-rcy-xthsIo"]:checked:not(.rt-hide)').val() || '';  //腺体回声不均匀情况
          joinTxt = qcbw + '残余甲状腺腺体回声' +xths + (hsDetail?'，'+hsDetail:'');
          var jzxcDetail = $("#r-rt-90").val() || '';
          joinTxt += `${xths?'，':''}血流信号${xlxh}` + (jzxcDetail?'，甲状腺上动脉流速'+jzxcDetail+'cm/s':'');
          shInfoObj.hsDetail = hsDetail;
          shInfoObj.jzxcDetail = jzxcDetail;
        }
        shInfoArr.push(joinTxt)
      } else {
        if(ljzxc === '未见异常') {
          joinTxt = qcbw + '甲状腺床未见异常';
        } else {
          joinTxt = qcbw + ljzxc;
        }
        shInfoArr.push(joinTxt)
      }
      if(qcbw !== '双侧') {
        // 另一未手术侧情况
        var resetArr  = [];
        var otherSide = $('#jz-rt-95').text();
        var otherId = ['jz-rt-96', 'jz-rt-100', 'jz-rt-104', 'jz-rt-108', 'jz-rt-113', 'jz-rt-121'];
        otherId.forEach(function(oItem){
          var oChildVal = '';
          oChildVal = $('[pid="'+oItem+'"]:checked:not(.rt-hide)').val();
          if(oChildVal) {
            var radioText = $('[id="'+oItem+'"]:not(.rt-hide)').text() + oChildVal;
            // 腺体回声不均匀
            if(oItem === 'jz-rt-113' && oChildVal === '不均匀' && $('[name="r-sh-bjy"]:checked:not(.rt-hide)').val()){
              radioText += '，' + $('[name="r-sh-bjy"]:checked').val();
            }
            // 血流信号-血供丰富
            if(oItem === 'jz-rt-121' && oChildVal === '血供丰富' && $('[id="jz-rt-125"]:not(.rt-hide)').val()){
              radioText += '，甲状腺上动脉流速' + $('[id="jz-rt-125"]').val() + 'cm/s';
            }
            resetArr.push(radioText);
          }
        })
        if(resetArr.length) {
          shInfoArr.push(otherSide + resetArr.join('，'));
        }
      }
    }
    // 其他征象
    if($('[id="jz-rt-128"]').val()) {
      var shOther = '其他征象：'+$('[id="jz-rt-128"]').val();
      shInfoArr.push(shOther);
      shInfoObj['shOther'] = shOther;
    }
  }
  
  if(rtStructure) {
    var shInfoStr = shInfoArr.length ? ((shqkTxt?shqkTxt+'，':'') + shInfoArr.join('；') + '。') : '';
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['shInfoStr'] = shInfoStr;
    rtStructure.description['shInfoObj'] = shInfoObj;
  }
  // console.log('getJzxShInfo', rtStructure.description);
}

// 甲状腺病灶信息汇总
function getBzDetailInfo() {
  vueComp.$nextTick(() => {
    vueComp.bzDetailList = [];
    vueComp.jzxPointList = [];
    var pointStr = [];
    var bzDescription = [];
    if($("#jz-rt-4").is(":checked")) {
      $('.rt-pane').each(function(i, dom) {
        computeScoreHandler($(dom));  //推算分数、等级
        var paneId = $(dom).attr('paneIdKey');
        var detail = getBzFormVal($(dom), paneId);
        vueComp.bzDetailList.push(detail);
        var pointStyle = setBzPosition(dom);
        var bzTitle = `病灶${i+1}：`;
        var bzDesc = bzTitle;
        bzDesc += `${detail.posSide?detail.posSide:''}${detail.posTorB?detail.posTorB:''}${detail.posOther?detail.posOther:''}${detail.hs?detail.hs==='无回声'?detail.hs+'病灶':'可见'+detail.hs+'病灶':''}\n`;
        bzDesc += `距包膜最小距离约${detail.bmLen || '-'}\n`;
        bzDesc += `大小约${detail.bzSize || '-'}\n`;
        bzDesc += `边缘${detail.by || '-'}\n`;
        bzDesc += `${detail.fw ? detail.fw : ''}\n`;
        bzDesc += `${detail.jzxQhs ? '可见'+detail.jzxQhs : ''}\n`;
        bzDesc += `${detail.xl ? detail.xl==='未见血流'?'未见血流信号' : '可见'+detail.xl : ''}`;
        if(detail.level && pointStyle.left && pointStyle.top && scoreAndLevelMap[detail.score]) {
          bzTitle += 'C-TIRADS ' + detail.level + '类；' + detail.jg;
          vueComp.jzxPointList.push({
            style: {
              left: pointStyle.left+'px',
              top: pointStyle.top+'px',
              background: scoreAndLevelMap[detail.score].color,
            },
            level: detail.level,
            showPointDetail: false,
            bzDesc: bzDesc,
            bzTitle: bzTitle,
            showDetail: false,
            bzIndex: i+1
          })
          // [x,y,颜色,等级]
          pointStr.push([pointStyle.left,pointStyle.top,scoreAndLevelMap[detail.score].color,detail.level]);
        }
        var oneBzDesc = bzDesc.split('\n');
        oneBzDesc.forEach((one, oI) => {
          if(one[one.length-1] === '，') {
            oneBzDesc[oI] = one.substring(0, one.length-1);
          }
        })
        // 用于描述的内容，除了表格外的数据
        if(detail.otherDesc) {
          oneBzDesc.push(`其他征像：${detail.otherDesc}`)
        }
        bzDescription.push(oneBzDesc.join('，') + (oneBzDesc.length?'。':''))
      })
    }
    if(pointStr.length) {
      $("#jzx-pot").text(JSON.stringify(pointStr));
    } else {
      $("#jzx-pot").text('');
    }
    if(rtStructure) {
      !rtStructure.description && (rtStructure.description = {});
      rtStructure.description['bzDetailList'] = vueComp.bzDetailList;
      rtStructure.description['jzxPointList'] = vueComp.jzxPointList;
      rtStructure.description.bzDescription = bzDescription.join('\n');
    }
    // console.log('getBzDetailInfo-->', rtStructure.description.bzDescription);
    getImpression();
  })
}

// 颈部淋巴结信息汇总
function getLbjInfo(firstIn) {
  var lbjInfoArr = [];
  var lbjInfoObj = {};
  var lbjHighData = [];
  if($("#jz-rt-5").is(":checked")) {
    var lbStatus = $('[name="lb-val"]:checked:not(.rt-hide)').val();
    cleanCanvas();
    if(lbStatus === '可见异常') {
      var lbPosId = $('[name="lbj-wz"]:checked:not(.rt-hide)').attr('id');
      // if(lbPosId) {
      lbjInfoObj = {
        lbStatus: lbStatus,
        lbPos: $('[name="lbj-wz"]:checked:not(.rt-hide)').val() || '',  //淋巴位置
        lbCount: $('[contect-id="'+lbPosId+'"] input[type="radio"]:checked:not(.rt-hide)').val() || '',  //淋巴数目
      }
      // 异常区域
      var lbArea = [];
      var dbSideArea = '';
      var dbSideAreaArr = [];
      var rightArea = [], leftArea = [];
      $('[contect-id="'+lbPosId+'"] input[type="checkbox"]:checked:not(.rt-hide)').each(function(iA, area){
        if(lbjInfoObj.lbPos === '双侧颈部') {
          if($(area).val().indexOf('左侧') > -1) {
            dbSideArea = '左侧';
            dbSideAreaArr.push(dbSideArea);
            leftArea.push($(area).val().replace(/左侧/ig, ''));
          } else {
            dbSideArea = '右侧';
            dbSideAreaArr.push(dbSideArea);
            rightArea.push($(area).val().replace(/右侧/ig, ''));
          }
        } 
        lbArea.push($(area).val())
        lbjInfoObj.dbSideAreaArr = dbSideAreaArr;
      })
      if(lbjInfoObj.lbPos === '双侧颈部') {
        if(lbArea.length === 1 && dbSideArea) {
          lbjInfoObj.dbArea = lbArea[0].replace(/左侧|右侧/ig, '') + '('+dbSideArea+'较大)';
        } else {
          var dbArr = [];
          if(leftArea.length) {
            dbArr.push('左侧('+leftArea.join('、')+')');
          }
          if(rightArea.length) {
            dbArr.push('右侧('+rightArea.join('、')+')');
          }
          lbjInfoObj.dbArea = dbArr.join(',');
        }
      }
      lbjInfoObj.lbArea = lbArea.join('、');
      lbjInfoObj.lbAreaArr = lbArea;

      // 淋巴大小
      var sizeTxt = [];
      var decimLen = 0;
      var sizeIds = ['jz-rt-149', 'jz-rt-150', 'jz-rt-151'];
      for(var bIdx = 0; bIdx < sizeIds.length; bIdx++) {
        var sizeVal = $(`[id="${sizeIds[bIdx]}"]:not(.rt-hide)`).val();
        if(sizeVal) {
          var [num, decim = ''] = sizeVal.split('.');
          if(decim && decimLen < decim.length) {
            decimLen = decim.length;
          }
          sizeTxt.push(sizeVal);
        }
      }
      var sizeRes = setDecimCount(decimLen, sizeTxt, 'cm')
      if(sizeRes.length) {
        lbjInfoObj.lbSize = sizeRes.join('x');   //大小
      }

      // 其他单选信息
      var otherRadioNames = [];
      $('.lbjI input[name]').each(function(r, rWidget) {
        if($(rWidget).is(':checked')) {
          var name = $(rWidget).attr('name');
          lbjInfoObj[name] = $(rWidget).val();
          otherRadioNames.push(name);
        }
      })
      lbjInfoArr.push(`${lbjInfoObj.lbPos}${lbjInfoObj.lbCount==='多发'?'多发':'可见'}异常淋巴结`)
      // $('[data-lb]:not(.rt-hide):checked').each(function(i, lbHight){
      //   var lbKey = $(lbHight).attr('data-lb');
      //   if(lbjHighCoord[lbKey]) {
      //     lbjHighData.push(lbKey);
      //     highLightOrgan(lbjHighCoord[lbKey]);
      //   }
      // })
      if(!firstIn) {
        lbjHighData = drawLbAreaHandler();
        if(lbjHighData.length) {
          $("#lbj-pot").text(JSON.stringify(lbjHighData));
        } else {
          $("#lbj-pot").text('');
        }
      }
      // }
      //  else {
      //   lbjInfoObj = {lbStatus: lbStatus};
      //   lbjInfoArr = [];
      //   $("#lbj-pot").text('');
      // }
    } else {
      lbjInfoObj = {lbStatus: lbStatus};
      lbjInfoArr.push('双侧颈部未见异常肿大淋巴结');
      $("#lbj-pot").text('');
    }
  }
  if(rtStructure) {
    let { dbArea='',dbSideAreaArr=[],lbArea='',lbAreaArr=[],lbCount='',lbPos='',lbSize='',lbStatus='',lbjBy='',lbjDzgh='',lbjHighData=[],lbjLbm='',lbjPzhd='',lbjPzhs='',lbjXllx='',lbjXt='' } = lbjInfoObj;
    var lbjInfoStr = lbjInfoArr.length ? (lbjInfoArr.join('；')) : '';
    var lbjDesStr = '';
    var lbjDesArr = [];
    if(lbjInfoStr && lbjInfoStr !== '双侧颈部未见异常肿大淋巴结') {
      if(lbCount === '多发') {
        lbPos ? lbjDesArr.push(lbPos + '可见' + lbCount + '淋巴结') : '';
        if(dbArea) {
          if(dbSideAreaArr.length>1) {
            lbjDesArr.push('分别位于' + dbArea);
            lbSize ? lbjDesArr.push('较大者约' + lbSize) : '';
          }else {
            lbjDesArr.push('较大者位于' + (lbAreaArr.lenght>1 ? dbArea : lbArea));
            lbSize ? lbjDesArr.push('大小约' + lbSize) : '';
          }
        }else {
          lbArea ? lbAreaArr.length>1 ? lbjDesArr.push('分别位于' + lbArea) : lbjDesArr.push('位于' + lbArea) : '';
          lbSize ? lbjDesArr.push('较大者约' + lbSize) : '';
        }
      }else {
        lbPos ? lbjDesArr.push(lbPos + '可见淋巴结') : '';
        if(dbArea) {
          dbSideAreaArr.length>1 ? lbjDesArr.push('分别位于' + dbArea) : lbjDesArr.push('位于' + (lbAreaArr.lenght>1 ? dbArea : lbArea));
        }else {
          lbArea ? lbAreaArr.length>1 ? lbjDesArr.push('分别位于' + lbArea) : lbjDesArr.push('位于' + lbArea) : '';
        }
        lbSize ? lbjDesArr.push('大小约' + lbSize) : '';
      }
      lbjBy ? lbjDesArr.push('边缘' + lbjBy) : '';
      lbjLbm ? lbjDesArr.push('淋巴门' + lbjLbm) : '';
      lbjXt ? lbjDesArr.push(lbjXt) : '';
      lbjPzhd ? lbjDesArr.push('皮质厚度' + lbjPzhd) : '';
      lbjPzhs ? lbjDesArr.push('皮质回声呈' + lbjPzhs) : '';
      lbjDzgh ? lbjDesArr.push('可见' + lbjDzgh) : '';
      lbjXllx ? lbjDesArr.push('血流类型：' + lbjXllx + '。') : '';
    }
    lbjDesStr = lbjDesArr.length ? lbjDesArr.join('，') : lbjInfoStr;
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['lbjInfoStr'] = lbjInfoStr;
    lbjInfoObj['lbjHighData'] = JSON.parse($("#lbj-pot").text() || '[]');
    rtStructure.description['lbjInfoObj'] = lbjInfoObj;
    rtStructure.description['lbjDesStr'] = lbjDesStr;
  }
  // console.log('description-->',rtStructure.description);
}

function drawLbAreaHandler() {
  var lbjHighData = [];  //淋巴结高亮区域
  $('[data-lb]:not(.rt-hide):checked').each(function(i, lbHight){
    var lbKey = $(lbHight).attr('data-lb');
    if(lbjHighCoord[lbKey]) {
      lbjHighData.push(lbKey);
      highLightOrgan(lbjHighCoord[lbKey]);
    }
  })
  return lbjHighData;
}

// 诊断内容
function getImpression() {
  if(rtStructure.description) {
    var impressList = [];
    var checkJzx = $('#jz-rt-2').is(':checked') || $('#jz-rt-3').is(':checked') || $('#jz-rt-4').is(':checked');
    var checkLbj = $('#jz-rt-5').is(':checked');
    let {jzxInfoStr, shInfoStr, shInfoObj, bzDetailList, bzDescription, lbjInfoStr, lbjInfoObj} = rtStructure.description;
    // 甲状腺做了检查
    if(checkJzx) {
      // 选择基本情况且没有选择病灶
      if(jzxInfoStr && !bzDescription) {
        impressList.push('<span class="bold-txt">甲状腺未见明显异常声像图</span>，C-TIRADS <input class="rt-sr-t" readonly style="width:50px" value="1"> 类，请结合临床');
      } else {
        // 术后
        if(shInfoObj) {
          let shImpress = '';
          let {shqkTxt = '', xths = '', xlxh = '', ljzxc, qcbw, qcqk} = shInfoObj;
          // shqkTxt && impressList.push(shqkTxt);
          if(shqkTxt) {
            shImpress = shqkTxt;
            if(qcqk !== '全切') {
              if(xths === "均匀" && xlxh === "未见异常") {
                shImpress += '，' + qcbw + $('.cyText').text() + '未见异常';
              }
            } else {
              if(ljzxc === '未见异常') {
                shImpress += '，' + qcbw + '甲状腺床未见异常';
              }
            }
            impressList.push('<span class="bold-txt">' + shImpress + '</span>');
            if(!bzDescription && qcbw !== '双侧') {
              impressList.push('<span class="bold-txt">' + $("#jz-rt-95").text() + '未见明显异常声像图' + '</span>');
            }
          }
        }

        // 病灶
        if(bzDetailList && bzDetailList.length) {
          bzDetailList = JSON.parse(JSON.stringify(bzDetailList));
          let levelOrder = ['6', '5', '4C', '4B', '4A', '3', '2'];
          if(bzDetailList.length > 1) {
            bzDetailList.sort(function(a, b){
              return levelOrder.indexOf(a.level) - levelOrder.indexOf(b.level);
            })
          }
          let has4Level = false;
          let yTxt = '';
          bzDetailList.forEach(function(bzItem) {
            var bzHtml = '';
            if(bzItem.posSide && bzItem.posTorB) {
              if(!has4Level && ['6', '5', '4C', '4B', '4A'].includes(bzItem.level)) {
                has4Level = true;
              }
              bzHtml += '<span class="bold-txt">';
              if(has4Level && !['6', '5', '4C', '4B', '4A'].includes(bzItem.level) && !yTxt) {
                yTxt = '余';
                bzHtml += yTxt;
              }
              bzHtml += '甲状腺' + bzItem.posSide + (bzItem.bzCount=='多发'?bzItem.bzCount:'') + bzItem.jg + '</span>' + '病灶，C-TIRADS ' + '<input class="rt-sr-t" readonly style="width:50px" value="'+ bzItem.level +'"> 类';
              if(scoreAndLevelMap[bzItem.score]&&scoreAndLevelMap[bzItem.score].advice) {
                bzHtml += '，' + scoreAndLevelMap[bzItem.score].advice;
              }
              impressList.push(bzHtml);
            }
          })
        }
      }
      $("#jzx-pot,#jzx-iName").removeClass('rt-hide');
      $(".jzx-img,.bz-desc").show();
    } else {
      $("#jzx-pot,#jzx-iName").addClass('rt-hide');
      $(".jzx-img,.bz-desc").hide();
    }

    // 淋巴结做了检查
    if(checkLbj) {
      if(lbjInfoStr) {
        impressList.push('<span class="bold-txt">' + lbjInfoStr + '</span>');
      }
      $("#lbj-pot").removeClass('rt-hide');
      $(".lbj-img").show();
    } else {
      $("#lbj-pot").addClass('rt-hide');
      $(".lbj-img").hide();
    }

    // 特殊情况
    if($('#jz-rt-70').val()) {
      impressList.push('<span class="bold-txt">' + $('#jz-rt-70').val() + '</span>');
    }
    rtStructure.impression = impressList.join('。\n') + (impressList.length?'。':'');
    // console.log('impression-->',rtStructure.impression);
  }
}
// 初始化canvas
function initCanvas() {
  canvasLbEle = document.querySelector("#lbjCanvas");
  lbjCtx = canvasLbEle.getContext("2d");
  lbjImage = document.querySelector("#lbjImage");
  lbjImage.onload = () => {
    lbjCtx.drawImage(lbjImage, 0, 0, 180, 180); // 绘制画布
    if($('#jz-rt-131').is(":checked")) {
      var lbjHighData = drawLbAreaHandler();
      $("#lbj-pot").text(JSON.stringify(lbjHighData));
    }
  }
}

// 高亮淋巴结部位
function highLightOrgan(curOrgan) {
  var ctx = lbjCtx;
  ctx.save();
  // 挡住默认的黑色字体
  if(curOrgan.textCord && curOrgan.textCord.length) {
    ctx.beginPath();
    ctx.fillStyle = '#FFF';
    ctx.rect(curOrgan.textCord[0],curOrgan.textCord[1],10,10);
    ctx.fill();
  }
  
  var coords = curOrgan.coords;
  if(coords && coords.length) {
    // 高亮背景和边框
    ctx.strokeStyle = curOrgan.strokeStyle; // 填充线颜色
    ctx.fillStyle = curOrgan.fillStyle; // 填充区域颜色
    ctx.lineWidth = 2;
    ctx.beginPath();
    for (var i = 0; i < coords.length; i++) {
      ctx.lineTo(coords[i][0], coords[i][1]);
    }
    ctx.closePath();
    ctx.stroke();
    ctx.fill();
  }
  
  // 高亮部位文字
  if(curOrgan.textCord && curOrgan.textCord.length) {
    ctx.beginPath();
    ctx.fillStyle = curOrgan.strokeStyle;
    ctx.font="bold 9px Arial";
    ctx.textBaseline = "top";
    ctx.fillText(curOrgan.text, curOrgan.textCord[0], curOrgan.textCord[1]);
    ctx.restore();
  }
}

// 清除画布之后重绘
function cleanCanvas() {
  lbjCtx.clearRect(0, 0, 180, 180);
  lbjCtx.drawImage(lbjImage, 0, 0, 180, 180); // 绘制画布
}
