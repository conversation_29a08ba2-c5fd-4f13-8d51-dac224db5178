#hlgm1 {
  font-size: 14px;
  color: #303133;
}
#hlgm1 .hlgm-content{
  /* margin: 16px; */
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#hlgm1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}
#hlgm1 .allergy-content .allergy-item {
  display: flex;
  align-items: center;
  margin-left: 16px;
}
#hlgm1 .allergy-content .second-content .layui-input {
  width: calc(100% - 11px);
}
#hlgm1 .second-content{
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
  margin: 6px 16px 12px 16px;
}
#hlgm1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#hlgm1 .rt-sr-lb {
  margin-left: 4px;
}
#hlgm1 .item-content{
  padding: 12px 16px;
}
#hlgm1 .form-content{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlgm1 .gray-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlgm1 .item-content .form-item{
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 12px;
}
#hlgm1 .form-item-title{
  color: #606266;
}
#hlgm1 .input-width{
  width: 200px;
}
#hlgm1 .add-btn{
  color: #1885F2;
}
#hlgm1 .advice-content{
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  margin-top: 8px;
}
#hlgm1 .advice-content .advice-title{
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: rgba(255,255,255,0);
  box-shadow: inset 0px -1px 0px 0px #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#hlgm1 .item-content .showInt {
  position: relative;
  background: #fff;
  width: 200px;
}
#hlgm1 .item-content .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}