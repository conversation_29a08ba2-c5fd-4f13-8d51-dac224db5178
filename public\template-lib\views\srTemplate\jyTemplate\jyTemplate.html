<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/srTemplate/jyTemplate/jyTemplate.css">
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/srTemplate/jyTemplate/jyTemplate.js"></script>
<!-- 保存的区域 -->
<ul class="t-pg" style="height: 100%;">
  <li class="page" id="jy1">
    <div class="jy-content">
      <div class="w-con">
        <div class="mb-8">
          <input class="rt-sr-w inp-sty wd-80" type="text" autocomplete="off" id="jy-rt-1" rt-sc="pageId:jy1;name:复查输入框;wt:1;desc:复查输入框;vt:;pvf:1;">
          <label class="ml-12" for="jy-rt-2">
            <input class="rt-sr-w" type="radio" name="fc-time" value="天" id="jy-rt-2" pid="jy-rt-1" rt-sc="pageId:jy1;name:天;wt:5;desc:天;vt:;pvf:1;">
            <span>天</span>
          </label>
          <label class="ml-12" for="jy-rt-3">
            <input class="rt-sr-w" type="radio" name="fc-time" value="周" id="jy-rt-3" pid="jy-rt-1" rt-sc="pageId:jy1;name:周;wt:5;desc:周;vt:;pvf:1;">
            <span>周</span>
          </label>
          <label class="ml-12" for="jy-rt-4">
            <input class="rt-sr-w" type="radio" name="fc-time" value="月" id="jy-rt-4" pid="jy-rt-1" rt-sc="pageId:jy1;name:月;wt:5;desc:月;vt:;pvf:1;">
            <span>月</span>
          </label>
          <label class="ml-12" for="jy-rt-5">
            <input class="rt-sr-w" type="radio" name="fc-time" value="年" id="jy-rt-5" pid="jy-rt-1" rt-sc="pageId:jy1;name:年;wt:5;desc:年;vt:;pvf:1;">
            <span>年</span>
          </label>
          <span>后复查</span>
        </div>
        <div class="mb-8">
          <label for="jy-rt-6">
            <input class="rt-sr-w" type="checkbox" name="dpljc" value="行彩色多普勒检查" id="jy-rt-6" rt-sc="pageId:jy1;name:行彩色多普勒检查;wt:4;desc:行彩色多普勒检查;vt:;pvf:1;">
            <span>行彩色多普勒检查</span>
          </label>
        </div>
        <div class="mb-8">
          <label for="jy-rt-7">
            <input class="rt-sr-w" type="checkbox" name="mrijc" value="行MRI检查" id="jy-rt-7" rt-sc="pageId:jy1;name:行MRI检查;wt:4;desc:行MRI检查;vt:;pvf:1;">
            <span>行MRI检查</span>
          </label>
        </div>
        <div class="mb-8">
          <label for="jy-rt-8">
            <input class="rt-sr-w" type="checkbox" name="xgzyjc" value="行心导管术心血管造影检查" id="jy-rt-8" rt-sc="pageId:jy1;name:行心导管术心血管造影检查;wt:4;desc:行心导管术心血管造影检查;vt:;pvf:1;">
            <span>行心导管术心血管造影检查</span>
          </label>
        </div>
      </div>
      <div class="w-con ml-40">
        <div class="mb-8">
          <label for="jy-rt-9">
            <input class="rt-sr-w" type="checkbox" name="xljd" value="心率减低后复查" id="jy-rt-9" rt-sc="pageId:jy1;name:心率减低后复查;wt:4;desc:心率减低后复查;vt:;pvf:1;">
            <span>心率减低后复查</span>
          </label>
        </div>
        <div class="mb-8">
          <label for="jy-rt-10">
            <input class="rt-sr-w" type="checkbox" name="teejc" value="行TEE检查" id="jy-rt-10" rt-sc="pageId:jy1;name:行TEE检查;wt:4;desc:行TEE检查;vt:;pvf:1;">
            <span>行TEE检查</span>
          </label>
        </div>
        <div class="mb-8">
          <label for="jy-rt-11">
            <input class="rt-sr-w" type="checkbox" name="ctjc" value="行快速CT检查" id="jy-rt-11" rt-sc="pageId:jy1;name:行快速CT检查;wt:4;desc:行快速CT检查;vt:;pvf:1;">
            <span>行快速CT检查</span>
          </label>
        </div>
        <div class="mb-8">
          <label for="jy-rt-12">
            <input class="rt-sr-w" type="checkbox" name="sxzyjc" value="行超声声学造影检查" id="jy-rt-12" rt-sc="pageId:jy1;name:行超声声学造影检查;wt:4;desc:行超声声学造影检查;vt:;pvf:1;">
            <span>行超声声学造影检查</span>
          </label>
        </div>
      </div>
    </div>
  </li>
</ul>