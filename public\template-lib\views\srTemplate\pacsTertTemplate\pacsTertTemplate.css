#pacsTert1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#pacsTert1 input[type="text"],#pacsTert1 .inp-sty{
  padding: 0 12px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #C0C4CC;
}
#pacsTert1 .tert-cont h4{
  margin-top: 12px;
  font-weight: 600;
}
#pacsTert1 .layui-inline input{
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
  border: 1px solid #C0C4CC;
}
#pacsTert1 #result-one,#pacsTert1 #result-two{
  width: 100%;
}
#pacsTert1 #result-two{
  margin-top: 4px;
}
#pacsTert1 #result-one input,#pacsTert1 #result-two input{
  vertical-align: middle;
}
.layui-menu.layui-dropdown-menu {
  max-height: 200px !important;
  overflow-y: auto;
  overflow-x: hidden;
}
#pacsTert1 .showInt {
  background: #fff;
}
#pacsTert1 .showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#pacsTert1 .inp-mlr8 input[type="text"]{
  margin: 0 8px;
}
#pacsTert1 .tert-item{
  border-bottom: 1px solid #C0C4CC;
  padding: 12px 24px;
}
#pacsTert1 .tert-title{
  margin-bottom: 12px;
  font-size: 20px;
  font-weight: 600;
}
#pacsTert1 .row-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#pacsTert1 .tert-cont .row-item+.row-item{
  margin-top: 8px;
}
#pacsTert1 .w-con{
  vertical-align: unset;
}

#pacsTert1 .mt-10{
  margin-top: 10px;
}
#pacsTert1 .mr-4{
  margin-right: 4px;
}
#pacsTert1 .mr-12{
  margin-right: 12px;
}
#pacsTert1 .mr-20{
  margin-right: 20px;
}
#pacsTert1 .pl-32{
  padding-left: 32px;
}
#pacsTert1 .ml-80{
  margin-left: 80px !important;
}
#pacsTert1 .wid100-96{
  width: calc(100% - 80px);
}
#pacsTert1 .wid100-80{
  width: calc(100% - 80px);
}
#pacsTert1 .wid100-86{
  width: calc(100% - 86px);
}
#pacsTert1 .wid-104{
  width: 104px;
}
#pacsTert1 .wid-120{
  width: 120px;
}
#pacsTert1 .wid-140 {
  width: 140px;
}
#pacsTert1 .wid-190 {
  width: 190px;
}
#pacsTert1 .wid-240{
  width: 240px;
}
#pacsTert1 .wid-458{
  width: 458px;
}
#pacsTert1 .pacsTert-view {
  display: none;
}
[isview="true"] #pacsTert1 .pacsTert-edit {
  display: none;
}
[isview="true"] #pacsTert1 .pacsTert-view {
  display: block;
}
#pacsTert1 .pacsTert-view {
  width: 780px;
  margin: 0 auto;
  font-size: 14px;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 20px;
}
#pacsTert1 .pacsTert-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
  position: relative;
}
#pacsTert1 .pacsTert-view .hos-logo{
  width: 100%;
  text-align: center;
  margin-bottom: 4px;
}
#pacsTert1 .pacsTert-view .hos-logo img{
  width: 284px;
  height: 40px;
}
#pacsTert1 .pacsTert-view .pat-wrap {
  text-align: right;
}
#pacsTert1 .pacsTert-view .page-tit{
  font-size: 24px;
  font-weight: 600;
}
#pacsTert1 .pacsTert-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#pacsTert1 .pacsTert-view .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
}
#pacsTert1 .pacsTert-view .gray-txt .bold{
  font-weight: 600;
}
#pacsTert1 .pacsTert-view .black-txt {
  color: #000;
  font-size: 14px;
}
#pacsTert1 .pacsTert-view .bold {
  font-weight: bold;
}
#pacsTert1 .pacsTert-view .info-i {
  width: 100px;
  display: flex;
  flex-wrap: wrap;
}
#pacsTert1 .pacsTert-view .info-i + .info-i {
  padding-left: 4px;
}
#pacsTert1 .pacsTert-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#pacsTert1 .pacsTert-view .view-patient .p-item {
  white-space: pre-wrap;
}
#pacsTert1 .pacsTert-view .view-patient [data-key="tert-rt-24"] {
  line-height: 20px;
}
#pacsTert1 .pacsTert-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}
#pacsTert1 .pacsTert-view .report-wrap {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}
#pacsTert1 .pacsTert-view .tip-wrap{
  margin-top: 8px;
}
#pacsTert1 .pacsTert-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}
#pacsTert1 .pacsTert-view .reporter-i [data-key] {
  flex: 1;
}
#pacsTert1 .pacsTert-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
#pacsTert1 .pacsTert-view .reporter-i + .reporter-i {
  margin-left: 8px;
}

[entry-type="5"] #pacsTert1{
  background: #fff;
  padding: 0;
}
[entry-type="5"] #pacsTert1 .pacsTert-view {
  width: 100%;
  min-height: auto;
  margin: unset;
  padding: 12px;
}
[entry-type="5"] #pacsTert1 .pacsTert-view .view-head,
[entry-type="5"] #pacsTert1 .pacsTert-view .rt-sr-header .view-patient,
[entry-type="5"] #pacsTert1 .pacsTert-view .tip-wrap {
  display: none;
}
[entry-type="5"] #pacsTert1 .pacsTert-view div {
  border-bottom: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}