

#hlhdyy1 {
  .agent-select {
    color: #1885F2;
    font-size: 14px;
    font-weight: normal;
    cursor: pointer;
  }

  font-size: 14px;
  .required{
    color: #E64545;
  }
  .card {
    border: 1px solid #DCDFE6;
  
    .card-header {
      height: 36px;
      font-size: 16px;
      color: #303133;
      font-weight: 600;
      line-height: 36px;
      padding-left: 16px;
      border-bottom: 1px solid #DCDFE6;
      background: #ECF5FF;
    }
    .card-content {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      padding: 12px 16px;
      .agent-line {
        column-gap: 8px;
      }
      .agent-info {
       flex: 1;
      }
    }
   
  }
  .add-row {
    color: #1885F2;
    cursor: pointer;
    
  }
  .suffix-input {
    display: flex;
    align-items: center;
    height: 36px;
    // border-left: 1px solid #DCDFE6;
    
    // border-top: 1px solid #DCDFE6;
    // border-bottom: 1px solid #DCDFE6;
    // border-right: none;
    border: 1px solid #DCDFE6;
    input {
      padding-left: 6px;
      border: none;
      height: 100%;
      width: 100%;
    }
    .suffix {
      line-height: 36px;
      text-align: center;
      width: 48px;
      background: #F5F7FA;
      height: 100%;
      border-left: 1px solid #DCDFE6;
    }
  }
}
.radio-buttons {
  display: flex;
}

  /* 隐藏原生单选框 */
  .radio-buttons input[type="radio"] {
    display: none;
  }
  
  .radio-buttons label {
    flex: 1;
    
    
  }
  /* 定义按钮样式 */
  .radio-buttons label span {
    display: flex;
    width: 100%;
    background-color: #FFFFFF;
    border-radius: 0;
    padding: 8px 0;
    margin-right: -1px; /* 这里使用负值使按钮相连 */
    cursor: pointer;
    transition: all 0.2s ease;
    justify-content: center;
    border: 1px solid #C0C4CC;
  }
  
  /* 当对应的单选框被选中时改变按钮样式 */
  .radio-buttons input[type="radio"]:checked + span{
    background-color: #ECF5FF ;
    color: #1885F2;
    border-color: #1885F2 ;
  }
  
  /* 鼠标悬停效果 */
  .radio-buttons label:hover {
    background-color: #ECF5FF;
  }
  
  /* 添加左右边框圆角 */
  .radio-buttons label:first-child  span{
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  
  // .radio-buttons label:last-child  span{
  //   border-top-right-radius: 4px;
  //   border-bottom-right-radius: 4px;
  //   margin-right: 0; /* 最后一个按钮不需要负margin */
  //   border-left-color: #1885F2;
  // }
  
  /* 避免中间按钮的右边框显示 */
  .radio-buttons label:not(:last-child) {
    border-right: none;
  }
.custom-select {
  position: relative;
  
  &::after{
    display: inline-block;
    position: absolute;
    top: 50%;
    right: 4px;
    content: '';
    width: 8px;
    height: 8px;
    border-left: 1px solid #999;
    border-bottom:1px solid #999;
    transform: rotate(-45deg) translateY(-100%);
  }

}
.label-color {
  color: #606266;
  text-align: right;
}
.bt {
  border-top: 1px solid #DCDFE6;
}
.rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  width: 100%;
  padding: 4px;

}
.rt-sr-label {
  column-gap: 8px;
}

.rt-sr-r {
  margin-right: 4px;
}

.mt-12 {
  margin-top: 12px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-6 {
  margin-top: 6px;
}
.m-0-6 {
  margin: 0 6px;
}

.w-10 {
  width: 10px;
}
.w-15 {
  width: 15px;
}

.w-20 {
  width: 20px;
}
.w-25 {
  width: 25px;
}

.w-30 {
  width: 30px;
}
.w-35 {
  width: 35px;
}

.w-40 {
  width: 40px;
}
.w-45 {
  width: 45px;
}
.w-50 {
  width: 50px;
}
.w-55 {
  width: 55px;
}
.w-60 {
  width: 60px;
}
.w-65 {
  width: 65px;
}
.w-70 {
  width: 70px;
}
.w-75 {
  width: 75px;
}
.w-80 {
  width: 80px;
}
.w-85 {
  width: 85px;
}
.w-90 {
  width: 90px;
}
.w-95 {
  width: 95px;
}
.w-100 {
  width: 100px;
}
.w-105 {
  width: 106px;
}
.w-110 {
  width: 110px;
}
.w-115 {
  width: 115px;
}
.w-120 {
  width: 120px;
}
.w-125 {
  width: 125px;
}
.w-130 {
  width: 130px;
}
.w-135 {
  width: 135px;
}
.w-140 {
  width: 140px;
}
.f-1 {
  flex: 1;
}

.fw-600 {
  font-weight: 600;
}

.a-center {
  display: flex;
  align-items: center;
}

.a-start {
  display: flex;
  align-items: flex-start;
}

.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.layui-input:read-only {
  background: #F2F6FC;
  cursor: not-allowed;
}
