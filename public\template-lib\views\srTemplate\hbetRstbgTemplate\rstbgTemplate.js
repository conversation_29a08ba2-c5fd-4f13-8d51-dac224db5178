$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var cursorInfo = {};  //光标位置信息
var isSavedReport = false; //模版是否保存
var specimenList = [
  {title: '外周血', id: 1},
  {title: '羊水', id: 2}
]
var codeMap = {
  '312': '肉眼所见',
  '201': '镜下所见',
  '211': '特殊检查',
  '202': '病理诊断',
}; // 添加双击事件节点
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#rstbg1 .rstbg-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initViewCon();
    } else {
      pageInit();
    }
  }
}
function pageInit() {
  initInpAndSel('rstbg-rt-1', specimenList, 320);
  if (!publicInfo.examNo && !publicInfo.busId) {
    return;
  }
  if(!isSavedReport){
    $('#rstbg-rt-1').val('外周血');
    $('#rstbg-rt-3').val('常规计数20个分裂相，核型分析5个。核型分析属于形态学检测，仅能检出该条带水平下的染色体结构或者数目异常，超出该条带水平可见的微小异常和低密度嵌合体可能无法检出。');
  }
  var rtEl=$('.fileids');
  var rtTypeEl=$('.typeFileids');
  // console.log('idAndDomMap',idAndDomMap)
  var idAnVal = idAndDomMap['rstbg-rt-4'] ? idAndDomMap['rstbg-rt-4'].value : '';
  var typeAnVal = idAndDomMap['rstbg-rt-5'] ? idAndDomMap['rstbg-rt-5'].value : '';
  if(idAnVal){
    rtEl.val(idAnVal);
    getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
      console.log(uris);
      if(uris&&uris.length){
        $('#preview').show();
        $('#addHistoryImg').hide();
        var url = uris.pop();
        $('#divisionImg').attr('src',url);
        $('#divisionPreview').attr('src',url);
      }
    })
  }
  if(typeAnVal){
    rtTypeEl.val(typeAnVal);
    getUploadedImageList(publicInfo.examNo,rtTypeEl,function(uris){
      console.log(uris);
      if(uris&&uris.length){
        $('#typePreview').show();
        $('#addKaryotypeImg').hide();
        var url = uris.pop();
        $('#typeImg').attr('src',url);
        $('#typePreviewImg').attr('src',url);
      }
    })
  }
  $('#imageInput').on('input',function(){
    var file = this.files[0];
    if(!file){
      return;
    }
    imageUploader(publicInfo.examNo,file, rtEl);
    getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
      console.log(uris);
      if(uris&&uris.length){
        $('#preview').show();
        $('#addHistoryImg').hide();
        var url = uris.pop();
        $('#divisionImg').attr('src',url);
        $('#divisionPreview').attr('src',url);
      }
    })
  })
  $('#divisionDel').on('click',function(){
    var pid = $('.fileids').val();
    console.log('pid',pid)
    deleteUploadedImage(publicInfo.examNo,pid, rtEl);
    $('#preview').hide();
    $('#addHistoryImg').show();
  })
  
  $('#karyotypeInput').on('input',function(){
    var file = this.files[0];
    if(!file){
      return;
    }
    imageUploader(publicInfo.examNo,file, rtTypeEl);
    getUploadedImageList(publicInfo.examNo,rtTypeEl,function(uris){
      console.log(uris);
      if(uris&&uris.length){
        $('#typePreview').show();
        $('#addKaryotypeImg').hide();
        var url = uris.pop();
        $('#typeImg').attr('src',url);
        $('#typePreviewImg').attr('src',url);
      }
    })
  })
  $('#typeDel').on('click',function(){
    var pid = $('.typeFileids').val();
    console.log('pid',pid)
    deleteUploadedImage(publicInfo.examNo,pid, rtTypeEl);
    $('#typePreview').hide();
    $('#addKaryotypeImg').show();
  })
  $('#divisionImg').on('click',function(){
    layer.open({
      title: '预览',
      type: 1,
      area: ['600px','500px'],
      content: $('#divisionPreview') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
    });
  })
  $('#typeImg').on('click',function(){
    layer.open({
      title: '预览',
      type: 1,
      area: ['600px','500px'],
      content: $('#typePreviewImg') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
    });
  })
  
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: lenVal !== 0 ? `width: ${lenVal}px;` : 'width:30%'
  })
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  var rtEl=$('.fileids');
  var rtTypeEl=$('.typeFileids');
  curElem.find('.rstbg-view [data-key]').each(function () {
    var key = $(this).attr('data-key')
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    $(this).closest('.desc-con').hide();
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      if(publicInfo[key] || idAnVal) {
        var value = publicInfo[key] || idAnVal;
        if(key ==='rstbg-rt-4') {
          rtEl.val(value);
          getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
            console.log(uris);
            if(uris&&uris.length){
              $('#divisionPreviewImg').show();
              $('#divisionPreviewImg').attr('src',uris.pop());
            }
          })
        }
        if(key ==='rstbg-rt-5') {
          rtTypeEl.val(value);
          getUploadedImageList(publicInfo.examNo,rtTypeEl,function(uris){
            console.log(uris);
            if(uris&&uris.length){
              $('#karyotypeImg').show();
              $('#karyotypeImg').attr('src',uris.pop());
            }
          })
        }
        if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
          value = value + ' ' + publicInfo['affirmTime'];
        }
        if(value) {
          $(this).html(value);
          $(this).closest('.desc-con').show();
        }
        break;
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  curElem.find('.rstbg-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = curElem.find('.rstbg-edit [data-key="description"]').val() || '';
  rtStructure.impression = curElem.find('.rstbg-edit [data-key="impression"]').val() || '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmDate: '',  //审核日期
    examParam: curElem.find('.rstbg-edit [data-key="examParam"]').val() || '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: curElem.find('.rstbg-edit [data-key="sampleSeen"]').val() || '',  //大体所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}