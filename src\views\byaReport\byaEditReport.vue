<template>
  <div :class="{'traceStatus':trace, 'bya':true}" v-loading="loadFlag">
    <div id="main">
      <div id="left-container">
        <div class="header">
          <span class="tit">影像所见</span>
          <div class="pat">
            <span class="info">{{ patientInfo.name }} {{ patientInfo.sex }} {{ patientInfo.age }} {{ patientInfo.patLocalId }}</span>
          </div>
        </div>
        <div class="form">
          <el-form ref="form" :model="form" label-width="114px">
            <el-form-item label="肿块描述：">
              <el-input
                v-model="zkms"
                :data-trace="traceObj['01012.01']&&traceObj['01012.01'].lastVal?'01012.01':'no'"
                placeholder="请输入"
                size="small"
              ></el-input>
            </el-form-item>
            <el-form-item label="信号特点：">
              <el-input
                v-model="xhtd"
                :data-trace="traceObj['01013.01']&&traceObj['01013.01'].lastVal?'01013.01':'no'"
                placeholder="请输入"
                size="small"
              ></el-input>
            </el-form-item>
            <div class="inline" style="display: flex; align-items: center">
              <el-form-item label="病灶最大横断面：">
                <!-- <el-input v-model="form.p" placeholder="请输入" size="mini" style="width: 160px; margin-left: 4px;"></el-input>，
                最大横断面 -->
                <el-input
                  v-model="form.x"
                  :data-trace="traceObj['01001.01']&&traceObj['01001.01'].lastVal?'01001.01':'no'"
                  size="mini"
                  class="mini40"
                  style="margin-right: 4px"
                ></el-input>
                <span>mm x</span>
                <el-input
                  v-model="form.y"
                  :data-trace="traceObj['01001.02']&&traceObj['01001.02'].lastVal?'01001.02':'no'"
                  size="mini"
                  class="mini40"
                  style="margin: 0 4px;"
                ></el-input>
                <span>mm</span>
                （
                  SE
                  <el-input 
                    size="mini" 
                    class="mini40"
                    v-model="form.se"
                    :data-trace="traceObj['01001.01.01']&&traceObj['01001.01.01'].lastVal?'01001.01.01':'no'"
                  ></el-input>，
                  IM
                  <el-input 
                    size="mini" 
                    class="mini40" 
                    v-model="form.im"
                    :data-trace="traceObj['01001.01.02']&&traceObj['01001.01.02'].lastVal?'01001.01.02':'no'"
                  ></el-input>
                    ），
                <span>高约：</span>
                <el-input v-model="form.z" :data-trace="traceObj['01001.04']&&traceObj['01001.04'].lastVal?'01001.04':'no'" size="mini" class="mini40" style="margin: 0 4px;"></el-input>
                <span>mm</span>（SE
                  <el-input 
                    size="mini" 
                    class="mini40"
                    v-model="form.seZ"
                    :data-trace="traceObj['01001.04.01']&&traceObj['01001.04.01'].lastVal?'01001.04.01':'no'"
                  ></el-input>，
                  IM
                  <el-input 
                    size="mini" 
                    class="mini40" 
                    v-model="form.imZ"
                    :data-trace="traceObj['01001.04.02']&&traceObj['01001.04.02'].lastVal?'01001.04.02':'no'"
                  ></el-input>）
              </el-form-item>
            </div>
            <el-form-item label="受侵部位：">
              <div class="table">
                <div class="table-head" v-if="!trace">
                  <el-button-group>
                    <el-button
                      :type="position === '1' ? 'primary' : ''"
                      @click="changePosition('1')"
                      >右</el-button
                    >
                    <el-button
                      :type="position === '0' ? 'primary' : ''"
                      @click="changePosition('0')"
                      >左</el-button
                    >
                  </el-button-group>
                  <el-input
                    v-model="filterText"
                    placeholder="请输入部位"
                    size="mini"
                    class="mini160"
                    clearable
                  ></el-input>
                  <!-- <el-button type="primary">搜索</el-button> -->
                </div>
                <div class="table-body">
                  <div class="body-title">
                    <div class="t-item">
                      <span>右侧</span>
                    </div>
                    <div class="t-middle">
                      <span>中线</span>
                    </div>
                    <div class="t-item">
                      <span>左侧</span>
                    </div>
                  </div>
                  <div class="body-content">
                    <div class="right-group">
                      <div class="t-wrap">
                        <div class="check-item" v-for="(tName, t) in tList" :key="t">
                          <span class="check-label">{{tName}}:</span>
                          <div class="checkbox-group-wrap" v-if="tName !== 'T3'">
                            <el-checkbox-group
                              v-model="form.rightGroup"
                              @change="mulCheckHandle(form.rightGroup, '右')"
                            >
                              <el-checkbox
                                v-for="(item, index) in rCheckList"
                                :key="item.id"
                                :label="item.label"
                                v-if="!item.checked[1].disabled && item.belongs === tName"
                                :data-trace="traceObj[item.rCode]&&traceObj[item.rCode].lastVal?item.rCode:'no'"
                                @change="mulCurrHandle(item.label, '右')"
                              ></el-checkbox>
                            </el-checkbox-group>
                          </div>
                          <div class="checkbox-group-wrap" v-else>
                            <collapse :title="`翼状结构`">
                              <div class="sel" slot="content">
                                <el-checkbox-group
                                  v-model="form.rightGroup"
                                  @change="mulCheckHandle(form.rightGroup, '右')"
                                >
                                  <el-checkbox
                                    v-for="(item, index) in rStrList"
                                    :label="item.label"
                                    :key="item.id"
                                    v-if="!item.checked[1].disabled"
                                    :data-trace="traceObj[item.rCode]&&traceObj[item.rCode].lastVal?item.rCode:'no'"
                                    @change="mulCurrHandle(item.label, '右')"
                                    >{{ item.label }}</el-checkbox
                                  >
                                </el-checkbox-group>
                              </div>
                            </collapse>
                            <collapse :title="`颅底`">
                              <div class="sel" slot="content">
                                <el-checkbox-group
                                  v-model="form.rightGroup"
                                  @change="mulCheckHandle(form.rightGroup, '右')"
                                >
                                  <el-checkbox
                                    v-for="(item, index) in rBoneList"
                                    :label="item.label"
                                    :key="item.id"
                                    v-if="!item.checked[1].disabled"
                                    :data-trace="traceObj[item.rCode]&&traceObj[item.rCode].lastVal?item.rCode:'no'"
                                    @change="mulCurrHandle(item.label, '右')"
                                    >{{ item.label }}</el-checkbox
                                  >
                                </el-checkbox-group>
                              </div>
                            </collapse>
                            <collapse :title="`鼻窦`" :className="`nasal`">
                              <div class="sel" slot="content">
                                <el-checkbox-group
                                  v-model="form.rightGroup"
                                  @change="mulCheckHandle(form.rightGroup, '右')"
                                >
                                  <el-checkbox
                                    v-for="(item, index) in rNoseList"
                                    :label="item.label"
                                    :key="item.id"
                                    v-if="!item.checked[1].disabled"
                                    :data-trace="traceObj[item.rCode]&&traceObj[item.rCode].lastVal?item.rCode:'no'"
                                    @change="mulCurrHandle(item.label, '右')"
                                    >{{ item.label }}</el-checkbox
                                  >
                                </el-checkbox-group>
                              </div>
                            </collapse>
                          </div>
                        </div>
                      </div>
                      <collapse :title="`淋巴结转移`" :className="`lymph`">
                        <div class="sel" slot="content">
                          <el-checkbox-group
                            v-model="form.rightGroup"
                          >
                            <el-checkbox 
                              :label="rLimList[0].label" 
                              @change="lightedPo('[右]颈部咽后', 'rightGroup')" 
                              :data-trace="traceObj['01007.0401']&&traceObj['01007.0401'].lastVal?'01007.0401':'no'"
                              v-if="!rLimList[0].checked[1].disabled"
                            >咽后</el-checkbox>
                            <div style="display: flex; align-items: center">
                              <img
                                v-if="rFold === 'unfold'"
                                src="@/assets/images/ic_right.png"
                                alt="收起"
                                class="arrow"
                                @click="foldHandle('fold', '右')"
                              />
                              <img
                                v-else
                                src="@/assets/images/ic_down.png"
                                alt="展开"
                                class="arrow"
                                @click="foldHandle('unfold', '右')"
                              />
                              <el-checkbox
                                :label="rLimList[1].label" 
                                v-if="!rLimList[1].checked[1].disabled"
                                v-model="form.rightGroup"
                                :indeterminate="isRIndeterminate"
                                @change="(val)=>handleAllChange(val, '右')"
                              >
                                颈部
                              </el-checkbox>
                            </div>
                          </el-checkbox-group>
                          <div class="neck" v-if="rFold === 'fold'">
                            <el-checkbox-group
                              v-model="form.rightGroup"
                              @change="mulCheckHandle(form.rightGroup, '右')"
                            >
                              <el-checkbox
                                v-for="(item, index) in rNeckList"
                                :label="item.label"
                                :key="item.id"
                                v-if="!item.checked[1].disabled || filterText.includes('颈')"
                                @change="mulCurrHandle(item.label, '右')"
                                :data-trace="traceObj[item.rCode]&&traceObj[item.rCode].lastVal?item.rCode:'no'"
                                >{{ item.label }}</el-checkbox
                              >
                            </el-checkbox-group>
                            <!-- <div>
                              <span>其他分区：</span>
                              <el-input
                                v-model="form.rightPo"
                                :data-trace="traceObj['01007.040209']&&traceObj['01007.040209'].lastVal?'01007.040209':'no'"
                                size="mini"
                                style="width: 50px"
                              ></el-input>
                            </div> -->
                          </div>
                        </div>
                      </collapse>
                    </div>
                    <div class="middle-group">
                      <el-checkbox-group
                        v-model="form.middle"
                        @change="mulCheckHandle(form.middle, '中')"
                      >
                        <el-checkbox
                          v-for="item in form.middleGroup"
                          :label="item.label"
                          :key="item.id"
                          @change="mulCurrHandle(item.label, '中')"
                          :data-trace="traceObj[item.mCode]&&traceObj[item.mCode].lastVal?item.mCode:'no'"
                        ></el-checkbox>
                      </el-checkbox-group>
                    </div>
                    <div class="left-group">
                      <div class="t-wrap">
                        <div class="check-item" v-for="(tName, t) in tList" :key="t">
                          <span class="check-label">{{tName}}:</span>
                          <div class="checkbox-group-wrap" v-if="tName !== 'T3'">
                            <el-checkbox-group
                              v-model="form.leftGroup"
                              @change="mulCheckHandle(form.leftGroup, '左')"
                            >
                              <el-checkbox
                                v-for="(item, index) in lCheckList"
                                :key="item.id"
                                :label="item.label"
                                v-if="!item.checked[0].disabled && item.belongs === tName"
                                @change="mulCurrHandle(item.label, '左')"
                                :data-trace="traceObj[item.lCode]&&traceObj[item.lCode].lastVal?item.lCode:'no'"
                              ></el-checkbox>
                            </el-checkbox-group>
                          </div>
                          <div class="checkbox-group-wrap" v-else>
                            <collapse :title="`翼状结构`">
                              <div class="sel" slot="content">
                                <el-checkbox-group
                                  v-model="form.leftGroup"
                                  @change="mulCheckHandle(form.leftGroup, '左')"
                                >
                                  <el-checkbox
                                    v-for="(item, index) in lStrList"
                                    :label="item.label"
                                    :key="item.id"
                                    v-if="!item.checked[0].disabled"
                                    @change="mulCurrHandle(item.label, '左')"
                                    :data-trace="traceObj[item.lCode]&&traceObj[item.lCode].lastVal?item.lCode:'no'"
                                  >{{ item.label }}</el-checkbox>
                                </el-checkbox-group>
                              </div>
                            </collapse>
                            <collapse :title="`颅底`">
                              <div class="sel" slot="content">
                                <el-checkbox-group
                                  v-model="form.leftGroup"
                                  @change="mulCheckHandle(form.leftGroup, '左')"
                                >
                                  <el-checkbox
                                    v-for="(item, index) in lBoneList"
                                    :label="item.label"
                                    :key="item.id"
                                    v-if="!item.checked[0].disabled"
                                    @change="mulCurrHandle(item.label, '左')"
                                    :data-trace="traceObj[item.lCode]&&traceObj[item.lCode].lastVal?item.lCode:'no'"
                                    >{{ item.label }}</el-checkbox
                                  >
                                </el-checkbox-group>
                              </div>
                            </collapse>
                            <collapse :title="`鼻窦`" :className="`nasal`">
                              <div class="sel" slot="content">
                                <el-checkbox-group
                                  v-model="form.leftGroup"
                                  @change="mulCheckHandle(form.leftGroup, '左')"
                                >
                                  <el-checkbox
                                    v-for="(item, index) in lNoseList"
                                    :label="item.label"
                                    :key="item.id"
                                    v-if="!item.checked[0].disabled"
                                    @change="mulCurrHandle(item.label, '左')"
                                    :data-trace="traceObj[item.lCode]&&traceObj[item.lCode].lastVal?item.lCode:'no'"
                                    >{{ item.label }}</el-checkbox
                                  >
                                </el-checkbox-group>
                              </div>
                            </collapse>
                          </div>
                        </div>
                      </div>
                      <collapse :title="`淋巴结转移`" :className="`lymph`">
                        <div class="sel" slot="content">
                          <el-checkbox-group
                            v-model="form.leftGroup"
                          >
                            <el-checkbox 
                              :label="lLimList[0].label" 
                              @change="lightedPo('[左]颈部咽后', 'leftGroup')" 
                              v-if="!lLimList[0].checked[0].disabled"
                              :data-trace="traceObj['01004.0401']&&traceObj['01004.0401'].lastVal?'01004.0401':'no'"
                            >咽后</el-checkbox>
                            <div style="display: flex; align-items: center">
                              <img
                                v-if="lFold === 'unfold'"
                                src="@/assets/images/ic_right.png"
                                alt="收起"
                                class="arrow"
                                @click="foldHandle('fold', '左')"
                              />
                              <img
                                v-else
                                src="@/assets/images/ic_down.png"
                                alt="展开"
                                class="arrow"
                                @click="foldHandle('unfold', '左')"
                              />
                              <el-checkbox
                                v-model="form.leftGroup"
                                :label="lLimList[1].label" 
                                v-if="!lLimList[1].checked[0].disabled"
                                :indeterminate="isLIndeterminate"
                                @change="(val)=>handleAllChange(val, '左')"
                              >
                                颈部
                              </el-checkbox>
                            </div>
                          </el-checkbox-group>
                          <div class="neck" v-if="lFold === 'fold'">
                            <el-checkbox-group
                              v-model="form.leftGroup"
                              @change="mulCheckHandle(form.leftGroup, '左')"
                            >
                              <el-checkbox
                                v-for="(item, index) in lNeckList"
                                :label="item.label"
                                :key="item.id"
                                v-if="!item.checked[0].disabled || filterText.includes('颈')"
                                @change="mulCurrHandle(item.label, '左')"
                                :data-trace="traceObj[item.lCode]&&traceObj[item.lCode].lastVal?item.lCode:'no'"
                                >{{ item.label }}</el-checkbox
                              >
                            </el-checkbox-group>
                            <!-- <div>
                              <span>其他分区：</span>
                              <el-input
                                v-model="form.leftPo"
                                :data-trace="traceObj['01004.040209']&&traceObj['01004.040209'].lastVal?'01004.040209':'no'"
                                size="mini"
                                style="width: 50px"
                              ></el-input>
                            </div> -->
                          </div>
                        </div>
                      </collapse>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
            <div class="inline" v-show="isShow">
              <el-form-item
                label="淋巴结"
                label-width="70px"
                style="margin-top: -18px"         
                required
              >
              <br />
              <span style="color: #606266;margin-left: -50px">最大横断面：</span>
                <el-input
                  v-model="form.maxLenX"
                  :data-trace="traceObj['01008.02']&&traceObj['01008.02'].lastVal?'01008.02':'no'"
                  size="mini"
                  class="mini40"
                  style="margin-right: 4px"
                ></el-input>
                <span>mm x</span>
                <el-input
                  v-model="form.maxLenY"
                  :data-trace="traceObj['01008.03']&&traceObj['01008.03'].lastVal?'01008.03':'no'"
                  size="mini"
                  class="mini40"
                  style="margin: 0 4px;"
                ></el-input>
                <span>mm</span>
                （
                  SE
                  <el-input 
                    size="mini" 
                    class="mini40"
                    v-model="form.maxLense"
                    :data-trace="traceObj['01008.02.01']&&traceObj['01008.02.01'].lastVal?'01008.02.01':'no'"
                  ></el-input>，
                  IM
                  <el-input 
                    size="mini" 
                    class="mini40" 
                    v-model="form.maxLenim"
                    :data-trace="traceObj['01008.02.02']&&traceObj['01008.02.02'].lastVal?'01008.02.02':'no'"
                  ></el-input>
                    ），
                <span>高约：</span>
                <el-input 
                  v-model="form.maxLenZ" 
                  :data-trace="traceObj['01008.04']&&traceObj['01008.04'].lastVal?'01008.04':'no'"
                  size="mini" 
                  class="mini40" 
                  style="margin: 0 4px;"
                ></el-input>
                <span>mm</span>
                （
                  SE
                  <el-input 
                    size="mini" 
                    class="mini40"
                    v-model="form.maxLenZse"
                    :data-trace="traceObj['01008.04.01']&&traceObj['01008.04.01'].lastVal?'01008.04.01':'no'"
                  ></el-input>，
                  IM
                  <el-input 
                    size="mini" 
                    class="mini40" 
                    v-model="form.maxLenZim"
                    :data-trace="traceObj['01008.04.02']&&traceObj['01008.04.02'].lastVal?'01008.04.02':'no'"
                  ></el-input>
                    ）
              </el-form-item>
              <el-form-item
                label="淋巴结最大者位于："
                label-width="150px"
                required
                v-if="maxLb.showMaxPos"
              >
                <el-radio-group style="margin-right:10px" v-model="maxLb.maxLbSide" size="mini">
                  <el-radio label="左侧" :data-trace="traceObj['01015.01']&&traceObj['01015.01'].lastVal&&traceObj['01015.01'].value=='左侧'?'01015.01':'no'"></el-radio>
                  <el-radio label="右侧" :data-trace="traceObj['01015.01']&&traceObj['01015.01'].lastVal&&traceObj['01015.01'].value=='右侧'?'01015.01':'no'"></el-radio>
                </el-radio-group>
                <el-select 
                  size="small" 
                  v-model="maxLb.maxLbArea"
                  :data-trace="traceObj['01015.02']&&traceObj['01015.02'].lastVal?'01015.02':'no'"
                  style="width:100px"
                >
                  <el-option
                    v-for="item in maxLb.maxLbOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                  ></el-option>
                </el-select> 组
              </el-form-item>
              <el-form-item
                required
                label-width="150px"
                label="淋巴结包膜外侵："
              > 
                <div class="hight-block">
                  <div class="bl-left">
                    <el-radio-group v-model="form.invasion" size="mini">
                      <el-radio label="无" :data-trace="traceObj['01002.01']&&traceObj['01002.01'].lastVal&&traceObj['01002.01'].value=='无'?'01002.01':'no'"></el-radio>
                      <el-radio :class="form.invasion==='有'?'active':''" label="有" :data-trace="traceObj['01002.01']&&traceObj['01002.01'].lastVal&&traceObj['01002.01'].value=='有'?'01002.01':'no'"></el-radio>
                    </el-radio-group>
                  </div>
                  <div class="bl-right" v-show="form.invasion==='有'">
                    <el-radio v-model="form.qfLevel" label="1级侵犯周围脂肪间隙" :data-trace="traceObj['01002.02']&&traceObj['01002.02'].lastVal&&traceObj['01002.02'].value==''?'01002.02':'no'">1级：侵犯周围脂肪间隙</el-radio>
                    <el-radio v-model="form.qfLevel" label="2级融合" :data-trace="traceObj['01002.02']&&traceObj['01002.02'].lastVal&&traceObj['01002.02'].value=='2级融合'?'01002.02':'no'">2级：淋巴结融合</el-radio>
                    <div style="display:flex">
                      <el-radio v-model="form.qfLevel" label="3级侵犯周围" :data-trace="traceObj['01002.02']&&traceObj['01002.02'].lastVal&&traceObj['01002.02'].value=='3级侵犯周围'?'01002.02':'no'">3级：侵犯周围</el-radio>
                      <el-checkbox-group 
                        class="flx-gp" 
                        v-model="form.qfLv3Org" 
                        :disabled="form.qfLevel !== '3级侵犯周围'"
                        :data-trace="traceObj['01002.03']&&traceObj['01002.03'].lastVal?'01002.03':'no'"
                      >
                        <el-checkbox label="肌肉"></el-checkbox>
                        <el-checkbox label="皮肤"></el-checkbox>
                        <el-checkbox label="神经血管"></el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                label-width="150px"
                label="坏死："
              > 
                <el-radio-group v-model="form.badStatus" size="mini">
                  <el-radio label="无" :data-trace="traceObj['01011.01']&&traceObj['01011.01'].lastVal&&traceObj['01011.01'].value=='无'?'01011.01':'no'"></el-radio>
                  <el-radio label="有" :data-trace="traceObj['01011.01']&&traceObj['01011.01'].lastVal&&traceObj['01011.01'].value=='有'?'01011.01':'no'"></el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <el-form-item
              label-width="150px"
              label="鼻窦炎/中耳乳突炎："
            > 
              <div class="hight-block">
                <div class="bl-left">
                  <el-radio-group v-model="form.isBdy" size="mini">
                    <el-radio label="无" :data-trace="traceObj['01016.01']&&traceObj['01016.01'].lastVal&&traceObj['01016.01'].value=='无'?'01016.01':'no'"></el-radio>
                    <el-radio :class="form.isBdy==='有'?'active':''" label="有" :data-trace="traceObj['01016.01']&&traceObj['01016.01'].lastVal&&traceObj['01016.01'].value=='有'?'01016.01':'no'"></el-radio>
                  </el-radio-group>
                </div>
                <div class="bl-right" v-show="form.isBdy==='有'">
                  <el-input
                    type="textarea"
                    :rows="4"
                    v-model="form.bdyDetail"
                    resize="none"
                    :data-trace="traceObj['01016.02']&&traceObj['01016.02'].lastVal?'01016.02':'no'"
                  ></el-input>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="其他征象：" label-width="150px">
              <!-- <div v-if="!trace">
                <el-button style="margin-bottom:8px;" size="mini" type="primary" @click="openQuoteWordLayer">引用词条</el-button>
              </div> -->
              <el-input
                type="textarea"
                v-model="form.others"
                :data-trace="traceObj['01009.01']&&traceObj['01009.01'].lastVal?'01009.01':'no'"
                resize="none"
                placeholder="请输入内容"
                :rows="4"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="footer">
          <div class="tit" style="width: 55px">印象</div>
          <div style="flex:1">
            <el-input
              type="textarea"
              v-model="impStr"
              :data-trace="traceObj['01010.03']&&traceObj['01010.03'].lastVal?'01010.03':'no'"
              resize="none"
              placeholder=""
              :rows="4"
            ></el-input>
            <!-- <div class="desc-line">
              <span class="type1">{{saveQuoteArr['qw_0001.002'] ?'1.':''}}考虑鼻咽癌</span>
              <span v-html="otherImpression"></span>
              <span class="type1">（MRI分期 T</span>
              <div class="box">                
                <span :style="{ color: tChange ? '#000' : '#C0C4CC' }">{{tLevel}}</span>
              </div>
              <span class="type1">N</span>
              <div class="box">
                <span :style="{ color: nChange ? '#000' : '#C0C4CC' }">{{nLevel}}</span>
              </div><span>）</span>   
              <span v-html="secondEntryData"></span>           
            </div>
            <div style="font-size: 14px; color:#666">(分期标准：鼻咽癌 UICC/AJCC分期第8版 / 中国分期2017版)</div> -->
          </div>
          <div class="btn" style="text-align:right" v-if="!isFromYunPacs && !sourceType">
            <el-button
              type="primary"
              style="background-color: #1885f2;border-color: #1885f2;right: 94px;"
              icon="el-icon-position"
              :disabled="submitFlag"
              v-if="!isFromYunPacs || ypacsRptInfo.examStatus < 60 || ypacsRptInfo.examStatus == 65"
              @click="submitForm({isSubmit:'1'})"
            >提交</el-button>
            <el-button
              type="primary"
              style="background-color: #21a685;border-color: #21a685;right: 12px;"
              icon="el-icon-tickets"
              :disabled="submitFlag"
              @click="submitForm({isSubmit:'0'})"
            >保存</el-button>
            <el-button
              type="primary"
              style="background-color: #1885f2;border-color: #1885f2;right: 94px;"
              icon="el-icon-position"
              :disabled="submitFlag"
              v-if="!isFromYunPacs || ypacsRptInfo.examStatus <= 70"
              @click="submitForm({isSubmit:'2'})"
            >确认</el-button>
          </div>
        </div>
      </div>
      <div id="right-container">
        <div class="header">
          <span class="tit">影像</span>
        </div>
        <div class="imgView" ref="imgView" @mousewheel="scrollEvent">
          <div class="img" ref="img">
            <span class="position">{{ currPosition }}</span>
            <canvas
              width="450"
              height="540"
              id="editCanvas"
              ref="canvas"
              @mousedown="moveHandler"
              @mousemove="showPositionName"
            ></canvas>

            <!-- 筛窦弹出框 -->
            <el-popover
              trigger="click"
              ref="sdPop"
              width="186"
              placement="rightlocal"
              v-model="sdVisible"
              class="pop1"
            >
              <el-checkbox
                v-model="sdCheckList"
                label="1"
                style="display: inline-block;margin-right: 14px;margin-left: 8px;"
                >左侧</el-checkbox
              >
              <el-checkbox
                v-model="sdCheckList"
                label="2"
                style="display: inline-block"
                >右侧</el-checkbox
              >
              <div class="popBtn" style="text-align: right">
                <el-button type="primary" size="mini" @click="selectPo('筛窦')"
                  >确定</el-button
                >
                <el-button size="mini" type="text" @click="closePo('sd')"
                  >取消</el-button
                >
              </div>
            </el-popover>
            <!-- 鼻腔弹出框 -->
            <el-popover
              trigger="click"
              ref="bqPop"
              width="186"
              placement="left-end"
              v-model="bqVisible"
              class="pop2"
            >
              <el-checkbox
                v-model="bqCheckList"
                label="1"
                style="display: inline-block;margin-right: 14px;margin-left: 8px;"
                >左侧</el-checkbox
              >
              <el-checkbox
                v-model="bqCheckList"
                label="2"
                style="display: inline-block"
                >右侧</el-checkbox
              >
              <div class="popBtn" style="text-align: right">
                <el-button type="primary" size="mini" @click="selectPo('鼻腔')"
                  >确定</el-button
                >
                <el-button size="mini" type="text" @click="closePo('bq')"
                  >取消</el-button
                >
              </div>
            </el-popover>
            <!-- 额窦弹出框 -->
            <el-popover
              trigger="click"
              ref="edPop"
              width="186"
              placement="left-end"
              v-model="edVisible"
              class="pop3"
            >
              <el-checkbox
                v-model="edCheckList"
                label="1"
                style="
                  display: inline-block;
                  margin-right: 14px;
                  margin-left: 8px;
                "
                >左侧</el-checkbox
              >
              <el-checkbox
                v-model="edCheckList"
                label="2"
                style="display: inline-block"
                >右侧</el-checkbox
              >
              <div class="popBtn" style="text-align: right">
                <el-button type="primary" size="mini" @click="selectPo('额窦')"
                  >确定</el-button
                >
                <el-button size="mini" type="text" @click="closePo('ed')"
                  >取消</el-button
                >
              </div>
            </el-popover>
          </div>
          <div class="imgList">
            <div class="divider">
              <div class="item">
                <el-divider content-position="center">冠状位</el-divider>
                <div class="imgs">
                  <div
                    :class="currImg === '2A' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('2A')"
                  >
                    <img src="@/assets/images/2A.png" alt="2A" class="small-img" />
                    <span>2A</span>
                  </div>
                  <div
                    :class="currImg === '2B' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('2B')"
                  >
                    <img src="@/assets/images/2B.png" alt="2B" class="small-img" />
                    <span>2B</span>
                  </div>
                  <div
                    :class="currImg === '2C' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('2C')"
                  >
                    <img src="@/assets/images/2C.png" alt="2C" class="small-img" />
                    <span>2C</span>
                  </div>
                  <div
                    :class="currImg === '2D' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('2D')"
                  >
                    <img src="@/assets/images/2D.png" alt="2D" class="small-img" />
                    <span>2D</span>
                  </div>
                </div>
              </div>
              <div class="item">
                <el-divider content-position="center">矢状位</el-divider>
                <div class="imgs">
                  <div
                    :class="currImg === '2E' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('2E')"
                  >
                    <img src="@/assets/images/2E.png" alt="2E" class="small-img" />
                    <span>2E</span>
                  </div>
                  <div
                    :class="currImg === '2F' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('2F')"
                  >
                    <img src="@/assets/images/2F.png" alt="2F" class="small-img" />
                    <span>2F</span>
                  </div>
                </div>
              </div>
              <div class="item">
                <el-divider content-position="center">颈部淋巴结</el-divider>
                <div class="imgs">
                  <div
                    :class="currImg === '左' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('左')"
                  >
                    <img src="@/assets/images/左.png" alt="左" class="small-img" />
                    <span>左</span>
                  </div>
                  <div
                    :class="currImg === '右' ? 'selected img-item' : 'img-item'"
                    @click="changeImg('右')"
                  >
                    <img
                      src="@/assets/images/右.png"
                      alt="右"
                      class="small-img"
                      ref="rightImg"
                    />
                    <span>右</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="list" style="display: none">
            <img src="@/assets/images/2A-.png" alt ref="aImg" />
            <img src="@/assets/images/2B-.png" alt ref="bImg" />
            <img src="@/assets/images/2C-.png" alt ref="cImg" />
            <img src="@/assets/images/2D-.png" alt ref="dImg" />
            <img src="@/assets/images/2E-.png" alt ref="eImg" />
            <img src="@/assets/images/2F-.png" alt ref="fImg" />
            <img src="@/assets/images/左-.png" alt ref="lImg" />
            <img src="@/assets/images/右-.png" alt ref="rImg" />
          </div>
        </div>
      </div>
    </div>
    <quote-word-layer
      :visible.sync="quoteWordVisible"
      :quoteWordList="quoteWordList"
      :saveQuoteArr="saveQuoteArr"
      @saveQuoteWord="saveQuoteWord"
    ></quote-word-layer>
  </div>
</template>
<script>
import collapse from "@/components/collapse.vue";
import { poArray } from "@/common/byaV2Json/positionObj.js";
import {
  aP,
  bP,
  cP,
  dP,
  eP,
  fP,
  lP,
  rP,
  T1,
  T2,
  T3,
  T4,
} from "@/common/byaV2Json/positionData.js";
import {
  aArr,
  bArr,
  cArr,
  dArr,
  eArr,
  fArr,
  lArr,
  rArr,
} from "@/common/byaV2Json/locationData.js";
import {
  middleGroup,
  checkList,
  strList,
  boneList,
  noseList,
  limList,
  neckList,
  allSelection
} from "@/common/byaV2Json/checkBoxData.js";
import {
  leftCheckJSON,
  leftTreeJSON,
  middleCheckJSON,
  rightCheckJSON,
  rightTreeJSON,
} from "@/common/byaV2Json/browserJSON.js";
import quoteWordLayer from '@/components/quoteWordLayer.vue';
import diseaseReportMixin from '@/mixins/diseaseReport.js'
export default {
  mixins: [diseaseReportMixin],
  components: { collapse, quoteWordLayer },
  data() {
    return {
      submitFlag: false,
      neckArr: ['ⅠA', 'ⅠB', 'ⅡA', 'ⅡB', 'Ⅲ', 'ⅣA', 'ⅣB', 'ⅤA', 'ⅤB', 'ⅤC', 'ⅦA', 'ⅦB', 'Ⅷ'],  // 颈部分区
      lFold: "fold", // unfold--收起，fold--展开
      rFold: "fold", // unfold--收起，fold--展开
      isShow: false, // 是否显示淋巴结最大截面和淋巴结包膜外侵
      tChange: false, // 控制T字体颜色
      nChange: false, // 控制N字体颜色
      // 转换浏览器数组
      leftCheckJSON: leftCheckJSON,   // 左侧多选提交的JSON格式
      leftTreeJSON: leftTreeJSON, // 左侧分类选项提交的JSON格式
      middleCheckJSON: middleCheckJSON,   // 中间多选提交的JSON格式
      rightCheckJSON: rightCheckJSON,   // 右侧分类选项提交的JSON格式
      rightTreeJSON: rightTreeJSON, // 右侧多选提交的JSON格式
      curCheck: [], // 暂时存
      clickSave: false, // 是否有保存
      leftCurrCheck: [], // 左侧树选中
      rightCurrCheck: [], // 右侧树选中
      currImg: "2A", // 当前选中图片:2A, 2B, 2C, 2D, 2E, 2F, 左, 右
      context: {}, // canvas对象
      img: new Image(), // 当前图片
      currentShapeIndex: null, // 当前形状
      lFilterList: [], // 左侧筛选
      rFilterList: [], // 右侧筛选
      currPosition: "", // 当前点击部位
      crrPoArr: aP, // 当前名字
      currArray: aArr, // 当前图片坐标数组
      aArray: aArr,
      bArray: bArr,
      cArray: cArr,
      dArray: dArr,
      eArray: eArr,
      fArray: fArr,
      lArray: lArr,
      rArray: rArr,
      poArray: poArray,
      T1: T1,
      T2: T2,
      T3: T3,
      T4: T4,
      aP: aP,
      bP: bP,
      cP: cP,
      dP: dP,
      eP: eP,
      fP: fP,
      lP: lP,
      rP: rP,
      allSelection:allSelection,  // 左右受侵部位-全部选项
      lCheckList: checkList,
      rCheckList: checkList,
      lStrList: strList, // 翼状结构
      lNoseList: noseList, // 鼻窦
      lBoneList: boneList, // 颅底
      lLimList: limList,  // 淋巴结转移
      lNeckList: neckList, // 颈部
      rLimList: limList, // 淋巴结转移
      rStrList: strList, // 翼状结构
      rBoneList: boneList, // 颅底
      rNoseList: noseList, // 鼻窦
      rNeckList: neckList, // 颈部

      filterText: "", // 部位搜索
      position: "0", // 受侵部位：0-左，1-右
      isRIndeterminate: false, // 右边颈部选中状态
      isLIndeterminate: false, // 左边颈部选中状态
      zkms: "鼻咽顶后侧壁不规则增厚形成软组织肿块",  //肿块描述
      xhtd: "T1WI呈稍低信号，T2WI呈稍高信号，增强后肿块明显强化",  //肿块描述
      form: {
        p: "", // 病灶位于
        x: "", // 病灶最大横断面1
        y: "", // 病灶最大横断面2
        se:"",//层面信息SE1
        im:"",//层面信息IM1
        z: "",  //高
        seZ:"",//层面信息SE2
        imZ:"",//层面信息IM2
        maxLen: "", // 淋巴结最大截面
        maxLenX:"",//淋巴结-长
        maxLenY:"",//淋巴结-宽
        maxLense:"",//淋巴结-SE
        maxLenim:"",//淋巴结-IM
        maxLenZ:"",//淋巴结-高
        maxLenZse:"",//淋巴结-高-SE
        maxLenZim:"",//淋巴结-高-IM
        others: "", // 其他征象
        type1: "X", // T
        type2: "0", // N
        invasion: "无", // 包膜外侵
        qfLevel: "", //侵犯登记
        qfLv3Org: [],  //侵犯周围
        badStatus: '无',  //淋巴坏死

        leftPo: "", // 左侧其他分区
        leftGroup: [], // 左边多选框
        lStrSel: [], // 翼状结构选中项
        lBoneSel: [], // 颅底选中项
        lNoseSel: [], // 鼻窦选中项

        middle: [], // 中线选中
        middleGroup: middleGroup, // 中线多选框

        rightPo: "", // 右侧其他分区
        rightGroup: [], // 右侧多选框
        rStrSel: [], // 翼状结构选中项
        rBoneSel: [], // 颅底选中项
        rNoseSel: [], // 鼻窦选中项
        rNeckSel: [], // 颈部选中项
        isBdy: '有',
        bdyDetail: '双侧上颌窦、筛窦、蝶窦、额窦粘膜增厚，窦腔及双侧乳突小房见T2WI高信号，窦壁骨质未见明显异常，增强后粘膜可见强化。'
      },
      impStr: '',   //印象
      defaultProps: {
        children: "children",
        label: "label",
        shortCut: "shortCut",
      },
      patientInfo: {
        name: "", // 名字
        examNo: "", //
        patLocalId: "",
        sex: "",
        age: "",
      }, // 患者信息
      nLevel: "0", // N
      tLevel: "X", // T
      bqVisible: false, // 鼠标点击鼻腔弹出框
      bqCheckList: [], // 鼻腔选择部位: 1--左侧，2--右侧
      edVisible: false, // 额窦弹出框
      edCheckList: [], // 额窦多选框
      sdVisible: false, // 筛窦弹出框
      sdCheckList: [], // 筛窦多选框
      result: [], // 提交结构化数组
      tList: ['T1', 'T2', 'T3', 'T4'],
      quoteWordVisible: false,
      quoteWordList: [
        {
          content: '鼻咽顶后侧壁粘膜不规则增厚形成软组织肿块，T1WI呈稍低信号，T2WI呈稍高信号，增强后肿块明显强化。',
          id: 'qw_0001.001',
          defaultPosition: '开头',
          checked: false,
          checkPosition: ''
        },
        {
          content: '双侧上颌窦、筛窦、蝶窦、额窦粘膜增厚，窦腔及双侧乳突小房见T2WI高信号，窦壁骨质未见明显异常，增强后粘膜可见强化。',
          id: 'qw_0001.002',
          defaultPosition: '结尾',
          checked: false,
          checkPosition: ''
        },
      ],
      saveQuoteArr: {},  //实际保存的值
      qfOrgan: {}, //侵犯部位  方向-部位-位置
      qfLb: {},  //侵犯的淋巴结
      organDirectMap: {},  //部位和方向的关系
      otherImpression: '',
      loadFlag: false,
      docId: '',
      secondEntryData:'',
      traceObj: {},
      firstIn: true,
      maxLb: {  //最大淋巴结信息
        xbCheckedArr: {},  //已勾选的淋巴结，区分左右
        showMaxPos: false,   //是否显示最大者位于
        maxLbSide: "",  //最大淋巴结位置
        maxLbOptions: [],  //最大淋巴结位置选项
        maxLbArea: "",  //最大淋巴结区域
      },
    };
  },
  watch: {
    // // 左侧其他分区
    // "form.leftPo"(val) {
    //   if (val !== "") {
    //     this.isShow = true;
    //   } else {
    //     let filterList = this.poArray.filter((item) => {
    //       return item.lighted === true && item.name.includes("颈部");
    //     });
    //     this.isShow = filterList.length > 0 || this.form.rightPo !== "";
    //   }
    // },
    // // 右侧其他分区
    // "form.rightPo"(val) {
    //   if (val !== "") {
    //     this.isShow = true;
    //   } else {
    //     let filterList = this.poArray.filter((item) => {
    //       return item.lighted === true && item.name.includes("颈部");
    //     });
    //     this.isShow = filterList.length > 0 || this.form.leftPo !== "";
    //   }
    // },
    // 筛选输入框
    filterText(val) {
      if (val !== "") {           // 筛选字段不为空
        this.allSelection.forEach((item, index)=>{
          item.checked[0].disabled=false;
          item.checked[1].disabled=false;
          if(!(item.label.includes(val) || item.shortCut.includes(val))) {
            this.position === "0"?item.checked[0].disabled=true:item.checked[1].disabled=true;
          }
        })
      } else {                    // 筛选字段为空
        this.allSelection.forEach((item, index)=>{
          item.checked[0].disabled=false;
          item.checked[1].disabled=false;
        })
      }
    },
    form: {
      handler(newVal) {
        if(!this.firstIn) {
          let {impress,entryData, desc} = this.getDescAndImpress(true);
          this.otherImpression = impress;
          this.secondEntryData = entryData;
          let allImp = `${impress || ''}`;
          this.impStr = allImp;
        }
      },
      deep: true,
      immediate: true
    },
  },
  computed: {},
  mounted() {
    this.loadFlag = true;
    this.lFilterList = this.lCheckList;
    this.rFilterList = this.rCheckList;
    this.setDirectDataArr();
    this.initCanvas();
    // 设置弹窗位置
    this.$nextTick(() => {
      // 筛窦弹框
      this.$refs.sdPop.$el.style.left = `${
        this.$refs.canvas.offsetLeft + 70
      }px `;
      this.$refs.sdPop.$el.style.top = `${this.$refs.canvas.offsetTop + 140}px`;
      // 鼻腔弹框
      this.$refs.bqPop.$el.style.left = `${this.$refs.canvas.offsetLeft}px `;
      this.$refs.bqPop.$el.style.top = `${this.$refs.canvas.offsetTop + 206}px`;
      // 额窦弹框
      this.$refs.edPop.$el.style.left = `${
        this.$refs.canvas.offsetLeft + 70
      }px `;
      this.$refs.edPop.$el.style.top = `${this.$refs.canvas.offsetTop + 66}px`;
    });
    // 筛窦 鼻腔 额窦弹框
    window.onresize = () => {
      this.$nextTick(() => {
        // 筛窦弹框
        this.$refs.sdPop.$el.style.left = `${
          this.$refs.canvas.offsetLeft + 70
        }px `;
        this.$refs.sdPop.$el.style.top = `${
          this.$refs.canvas.offsetTop + 140
        }px`;
        // 鼻腔弹框
        this.$refs.bqPop.$el.style.left = `${this.$refs.canvas.offsetLeft}px `;
        this.$refs.bqPop.$el.style.top = `${
          this.$refs.canvas.offsetTop + 206
        }px`;
        // 额窦弹框
        this.$refs.edPop.$el.style.left = `${
          this.$refs.canvas.offsetLeft + 70
        }px `;
        this.$refs.edPop.$el.style.top = `${
          this.$refs.canvas.offsetTop + 66
        }px`;
      });
    };

    this.pageInit();
  },
  methods: {
    async pageInit() {
      // 获取患者信息
      await this.getPatientInfo();
      if(!this.patientInfo.examNo && !this.isPreview) {
        this.loadFlag = false;
        this.$message.error("找不到检查记录");
        return
      }
      let dataList = await this.getData();
      let data = this.newDataToOldAttrCode(dataList);
      if(data && data.length) {
        this.fillAllData(data);
      }
      this.loadFlag = false;
      this.$nextTick(() => {
        this.firstIn = false;
        if(this.trace) {
          addTraceHandler('.traceStatus', this.traceObj);
        }
      })
    },
    // 处理归类侵犯方向的数据
    setDirectDataArr() {
      this.organDirectMap = {};
      let allSelection = [...this.allSelection, ...middleGroup];
      allSelection.forEach(item => {
        if(item.qfDirection) {
          this.organDirectMap[item.label] = item.qfDirection;
        }
      })
    },
    // 展开 / 收起
    foldHandle(i, po) {
      if(po==='左') {
        this.lFold = i;
      } else {
        this.rFold = i;
      }
    },
    /**
     * @description:  鼠标点击图片选择筛窦、鼻腔、额窦
     * @Date: 2020-09-08 18:10:24
     * @author: LLY
     */
    selectPo(po) {
      this.cleanCanvas();
      this.drawImg();
      // 取消高亮
      this.poArray.forEach((poItem, poIndex) => {
        let checkList = poItem.name.includes(po);
        if (poItem.name.startsWith("[左]") || poItem.name.startsWith("[右]")) {
          if (checkList) {
            poItem.lighted = false;
            this.form.leftGroup.forEach((itemCheck, indexCheck) => {
              if (poItem.name.split("[左]")[1] === itemCheck) {
                this.form.leftGroup.splice(indexCheck, 1);
              }
            });
            this.form.rightGroup.forEach((itemCheck, indexCheck) => {
              if (poItem.name.split("[右]")[1] === itemCheck) {
                this.form.rightGroup.splice(indexCheck, 1);
              }
            });
          }
        }
      });

      let arr = [];
      switch (po) {
        case "鼻腔":
          arr = this.bqCheckList;
          break;
        case "筛窦":
          arr = this.sdCheckList;
          break;
        case "额窦":
          arr = this.edCheckList;
          break;
      }
      if (arr.length !== 0) {
        // 点击高亮
        arr.forEach((item, index) => {
          for (let i = 0; i < this.poArray.length; i++) {
            if (item === "1") {
              if (this.poArray[i].name === `[左]${po}`) {
                this.poArray[i].lighted = true;
                this.form.leftGroup.push(po);
                continue;
              }
            } else if (item === "2") {
              if (this.poArray[i].name === `[右]${po}`) {
                this.poArray[i].lighted = true;
                this.form.rightGroup.push(po);
                continue;
              }
            }
          }
        });
      }
      this.sdVisible = false;
      this.edVisible = false;
      this.bqVisible = false;
      this.lightedPo();
    },
    /**
     * @description:  取消鼻腔、筛窦、额窦选择
     * @Date: 2020-09-08 18:11:28
     * @author: LLY
     */
    closePo(val) {
      this.sdVisible = false;
      this.edVisible = false;
      this.bqVisible = false;
      switch (val) {
        case "ed":
          this.edCheckList = this.curCheck;
          break;
        case "bq":
          this.bqCheckList = this.curCheck;
          break;
        case "sd":
          this.sdCheckList = this.curCheck;
          break;
      }
    },
    // 点击颈部选中
    handleAllChange(val, po) {
      const arr = this.neckArr;
      let sideArr = ['左', '右'];
      for(let side of sideArr) {
        if(side !== po) {
          continue;
        }
        let delArr = side==='左'?this.form.leftGroup:this.form.rightGroup;
        if(val) {
          arr.forEach((item, index)=> {
            let i = delArr.indexOf(item);
            if(i===-1) {
              delArr.push(item);
              this.poArray.forEach((aItem, aIndex)=>{
                if(aItem.name.includes('['+side+']') && aItem.name.includes(item)) {
                  aItem.lighted = true;
                  if(aItem.imgList) {             // 点亮右颈部淋巴结图片
                    aItem.imgList.forEach((imgItem, imgIndex)=>{
                      this.changeImg(imgItem.imgName);
                      this.addShape(aItem.name, this.context, imgItem.location, true);
                    })
                  }
                }
              })
            }
          })
        } else {
          // 清空颈部全选
          for(let i=0; i<delArr.length; i++) {
            for(let j=0; j<arr.length; j++) {
              if(delArr[i]===arr[j]) {
                this.poArray.forEach((item, index)=>{
                  if(item.name.includes('['+side+']') && item.name.includes(delArr[i])) {
                    item.lighted = false;
                  }
                })
                delArr.splice(i, 1);
                i--;
              }
            }
          }
          this.changeImg(side)
        }
        
        side==='左' ? (this.isLIndeterminate = false) : (this.isRIndeterminate = false);
      }
      this.lightedPo();
      this.tLevel = this.countTLevel();
      this.nLevel = this.countNLevel();
    },
    /**
     * @description:  左/中/右侧多选框
     * @Date: 2020-09-08 17:54:24
     * @author: LLY
     */
    mulCheckHandle(val, e) {
      let str = `[${e}]`;
      const arr = this.neckArr;
      let count = 0;
      this.cleanCanvas();
      this.drawImg();

      this.poArray.forEach((poItem, poIndex) => {
        if (poItem.name.startsWith(str)) {
          if (e === "左" || e === "右") {
            const sel = this.allSelection;
            let flag = false
            // 清空左、右侧多选
            for(let i=0; i<sel.length; i++) {
              flag = poItem.name.includes(sel[i].label);
              if(flag) {
                break;
              }
            }
            if (flag) {
              poItem.lighted = false;
              if (poItem.name.split(str)[1] === "鼻腔") {
                this.bqCheckList.forEach((item, index) => {
                  if (e === "左" && item === "1") {
                    this.bqCheckList.splice(index, 1);
                  } else if (e === "右" && item === "2") {
                    this.bqCheckList.splice(index, 1);
                  }
                });
              } else if (poItem.name.split(str)[1] === "额窦") {
                this.edCheckList.forEach((item, index) => {
                  if (e === "左" && item === "1") {
                    this.edCheckList.splice(index, 1);
                  } else if (e === "右" && item === "2") {
                    this.edCheckList.splice(index, 1);
                  }
                });
              } else if (poItem.name.split(str)[1] === "筛窦") {
                this.sdCheckList.forEach((item, index) => {
                  if (e === "左" && item === "1") {
                    this.sdCheckList.splice(index, 1);
                  } else if (e === "右" && item === "2") {
                    this.sdCheckList.splice(index, 1);
                  }
                });
              }
            }
          } else if (e === "中") {
            // 清空中线多选
            poItem.lighted = false;
          }
        }
      });
      if (val.length > 0) {
        val.forEach((item, index) => {
          arr.forEach((aItem, aIndex)=>{
            if(item===aItem) { count++; }
          })
          this.poArray.forEach((poItem, poIndex) => {
            let flag = poItem.name.split(str)[1];
            if (flag) {
              let name = flag.includes("颈部") ? flag.split("颈部")[1] : flag;
              if (poItem.name.startsWith(str) && name === item) {
                poItem.lighted = true;
                if (poItem.imgList) {
                  poItem.imgList.forEach((imgItem, imgIndex) => {
                    if (imgItem.imgName === this.currImg) {
                      this.addShape(
                        poItem.name,
                        this.context,
                        imgItem.location,
                        true
                      );
                    }
                  });
                }
                // 鼻腔
                if (name === "鼻腔") {
                  let i = e === "左" ? "1" : "2";
                  this.bqCheckList.push(i);
                }
                // 额窦
                if (name === "额窦") {
                  let i = e === "左" ? "1" : "2";
                  this.edCheckList.push(i);
                }
                // 筛窦
                if (name === "筛窦") {
                  let i = e === "左" ? "1" : "2";
                  this.sdCheckList.push(i);
                }
              }
            }
          });
        });
      }
      let groupArr = e==='左' ? this.form.leftGroup : this.form.rightGroup;
      let neckLen = this.neckArr.length;
      e==='左' ? (this.isLIndeterminate = count > 0 && count < neckLen) : 
        (this.isRIndeterminate = count > 0 && count < neckLen);
      if(count===neckLen) {
        groupArr.push('颈部')
      } else {
        const index = groupArr.indexOf('颈部');
        if(index>-1) {
          groupArr.splice(index, 1);
        }
      }
      this.lightedPo();
      this.tLevel = this.countTLevel();
      this.nLevel = this.countNLevel();
    },
    /**
     * @description:  多选框选中切换图片
     * @Date: 2020-09-08 16:06:54
     * @author: LLY
     */
    mulCurrHandle(name, po) {
      // 颈部部分是否全选
      const arr = this.neckArr;
      let selArr = po==='左'?this.form.leftGroup:this.form.rightGroup;
      let str = `[${po}]`,
        obj = {}, flagName;
      const flag = name.includes("A") || name.includes("B") || name.includes('Ⅲ') || name.includes('Ⅳ');
      obj = this.poArray.find((item) => {
        const filter = item.name.split(str)[1];
        if(filter) {
          flagName = flag?filter.split('颈部')[1]:filter
          return name === flagName;
        }
      });
      if (obj && obj.imgList) {
        this.changeImg(obj.imgList[0].imgName);
      }
    },
    /**
     * @description:  编辑
     * @Date: 2020-07-15 10:27:18
     * @author: LLY
     */
    fillAllData(resData) {
      this.traceObj = {};
      // let param = CallDllFunc("BrPrint.dll", "ReadJson", ""); // 调用YLZRT浏览器
      if(!resData || resData.length === 0) {
        return;
      }
      let result = resData;
      for (let i = 0; i < result.length; i++) {
        let allAttrValue = result[i].attrValue || [];
        allAttrValue.forEach((allItem) => {
          if(allItem.code && !this.traceObj[allItem.code]) {
            let { lastVal } = allItem;
            if(lastVal !== undefined) {
              allItem.lastVal = lastVal || '-';
            }
            this.traceObj[allItem.code] = allItem;
          }
        })
        // 病灶最大横断面
        if (result[i].attrCode === "01001") {
          let arr = result[i].attrValue;
          arr.forEach((item, index) => {
            if (item.code === "01001.01") {
              this.form.x = item.value;
            }else if(item.code === '01001.01.01'){
              this.form.se = item.value;
            }
            else if(item.code === '01001.01.02'){
              this.form.im = item.value;
            }
             else if (item.code === "01001.02"){
              this.form.y = item.value;
            } else if (item.code === "01001.04") {
              this.form.z = item.value;
            }else if(item.code === '01001.04.01'){
              this.form.seZ = item.value;
            }else if(item.code === '01001.04.02'){
              this.form.imZ = item.value;
            }
            // else if (item.code === "01001.03") {
            //   this.form.p = item.value;
            // } 
          });
        }
        // 包膜外侵
        if (result[i].attrCode === "01002") {
          let arr2 = result[i].attrValue
          arr2.forEach((item,index) => {
            if(item.code === "01002.01"){
              this.form.invasion = item.value;
            }else if(item.code === "01002.02"){
              this.form.qfLevel = item.value;
            }else if(item.code === "01002.03"){
              this.form.qfLv3Org = item.value.split('、');
            }
          })
        }
        if (result[i].attrCode === "01011") {
          this.form.badStatus = result[i].attrValue[0].value;
        }
        // 淋巴结最大径
        if (result[i].attrCode === "01008") {
          let arr2 = result[i].attrValue
          arr2.forEach((item,index) => {
            if(item.code === "01008.01"){
              this.form.maxLen = item.value;
            }else if(item.code === "01008.02"){
              this.form.maxLenX = item.value;
            }else if(item.code === "01008.03"){
              this.form.maxLenY = item.value;
            }else if(item.code === "01008.02.01"){
              this.form.maxLense = item.value;
            }else if(item.code === "01008.02.02"){
              this.form.maxLenim = item.value;
            }else if(item.code === "01008.04"){
              this.form.maxLenZ = item.value;
            }else if(item.code === "01008.04.01"){
              this.form.maxLenZse = item.value;
            }else if(item.code === "01008.04.02"){
              this.form.maxLenZim = item.value;
            }
          })
        }
        if (result[i].attrCode === "01015") {
          let arr2 = result[i].attrValue;
          arr2.forEach((item,index) => {
            if(item.code === "01015.01"){
              this.maxLb.maxLbSide = item.value;
            } else if(item.code === "01015.02"){
              this.maxLb.maxLbArea = item.value;
            }
          })
        }
        if (result[i].attrCode === "01016") {
          let arr2 = result[i].attrValue;
          arr2.forEach((item,index) => {
            if(item.code === "01016.01"){
              this.form.isBdy = item.value;
            } else if(item.code === "01016.02"){
              this.form.bdyDetail = item.value;
            }
          })
          if(arr2.length === 1) {
            this.form.bdyDetail = '';
          }
        }
        // 其他征象
        if (result[i].attrCode === "01009") {
          this.form.others = result[i].attrValue[0].value;
        }
        // 肿块描述
        if (result[i].attrCode === "01012") {
          this.zkms = result[i].attrValue[0].value;
        }
        // 信号特点
        if (result[i].attrCode === "01013") {
          this.xhtd = result[i].attrValue[0].value;
        }
        // 印象
        if (result[i].attrCode === "01010") {
          // this.nLevel = result[i].attrValue[1].value;
          // this.tLevel = result[i].attrValue[0].value;
          this.impStr = result[i].attrValue?.find(item => item.code === "01010.03")?.value;
        }
        // 中线
        if (result[i].attrCode === "01005") {
          let middle = result[i].attrValue;
          middle.forEach((item, index) => {
            this.form.middle.push(item.value);
            this.mulCheckHandle(this.form.middle, "中");
          });
        }
        // 左侧多选框
        if (result[i].attrCode === "01003") {
          let left = result[i].attrValue;
          left.forEach((item, index) => {
            this.form.leftGroup.push(item.value);
            this.mulCheckHandle(this.form.leftGroup, "左");
          });
        }
        // 左侧树形
        if (result[i].attrCode === "01004") {
          let tree = result[i].attrValue;
          tree.forEach((item, index) => {
            // if(item.code === '01004.040209') {
            //   this.form.leftPo = item.value;
            //   return;
            // }
            let po = "[左]";
            this.form.leftGroup.push(item.value);
            this.mulCheckHandle(this.form.leftGroup, "左");
          });
        }
        // 右侧多选框
        if (result[i].attrCode === "01006") {
          let right = result[i].attrValue;
          right.forEach((item, index) => {
            this.form.rightGroup.push(item.value);
            this.mulCheckHandle(this.form.rightGroup, "右");
          });
        }
        // 右侧树形
        if (result[i].attrCode === "01007") {
          let tree = result[i].attrValue;
          tree.forEach((item, index) => {
            // if(item.code === '01007.040209') {
            //   this.form.rightPo = item.value;
            //   return;
            // }
            let po = "[右]";
            this.form.rightGroup.push(item.value);
            this.mulCheckHandle(this.form.rightGroup, "右");
          });
        }
        
        // 引用词条
        // if(result[i].attrCode === "qw_0001") {
        //   this.saveQuoteArr = {};
        //   let quetoW = result[i].attrValue;
        //   quetoW.forEach(item => {
        //     let attrList = result.filter(attr => attr.attrCode === item.code);
        //     this.saveQuoteArr[item.code] = {
        //       content: item.value,
        //       position: this.findValByCode(item.code+'.01', attrList)
        //     }
        //   })
        // }
        setTimeout(() => {
          this.cleanCanvas();
          this.drawImg();
          this.lightedPo();
        }, 200);
      }
    },

    // 找到指定code的value
    findValByCode(code, data) {
      let value = '';
      for(let item of data) {
        if(item.attrValue && item.attrValue.length) {
          for(let attr of item.attrValue) {
            if(attr.code === code) {
              value = attr.value;
              break;
            }
          }
        }
        if(value) {
          break;
        }
      }
      return value;
    },
    /**
     * @description 鼠标滚动影像切换
     */
    scrollEvent(e) {
      let imgList = ["2A", "2B", "2C", "2D", "2E", "2F", "左", "右"];
      let cuIndex = null;
      imgList.forEach((item, index) => {
        if (item === this.currImg) {
          cuIndex = index;
        }
      });
      if (e.wheelDelta > 0) {
        if (cuIndex === 0) {
          cuIndex = 8;
        }
        this.currImg = imgList[cuIndex - 1];
        this.changeImg(this.currImg);
      } else {
        if (cuIndex === 7) {
          cuIndex = -1;
        }
        this.currImg = imgList[cuIndex + 1];
        this.changeImg(this.currImg);
      }
    },
    /**
     * @description 判断是否为 T1
     */
    isTLevel1() {
      let filterList = this.poArray.filter((item) => {
        return item.lighted === true;
      });
      let yCheck = false,
        zCheck = false,
        tag = false; // 是否勾选咽旁间隙
      for (let i = 0; i < filterList.length; i++) {
        let check1 = filterList[i].name.includes("咽旁间隙");
        if (check1) {
          yCheck = false;
          break;
        } else {
          yCheck = true;
        }
      }
      for (let i = 0; i < filterList.length; i++) {
        let check2 =
          filterList[i].name.includes("鼻咽") ||
          filterList[i].name.includes("口咽") ||
          filterList[i].name.includes("腭帆提肌") ||
          filterList[i].name.includes("鼻腔");
        if (check2) {
          zCheck = true;
          break;
        }
      }
      if (yCheck === true && zCheck) {
        tag = true;
      } else if (yCheck === false && zCheck) {
        // return false;
        tag = false;
      }
      return tag;
    },
    /**
     * @description 判断是否为 T2
     */
    isTLevel2() {
      let filterList = this.poArray.filter((item) => {
        return item.lighted === true;
      });
      let yCheck = false,
        bCheck = false,
        zCheck = false,
        tag = false; // 是否勾选咽旁间隙
      for (let i = 0; i < filterList.length; i++) {
        let check1 = filterList[i].name.includes("咽旁间隙"),
          check2 =
            filterList[i].name.includes("腭帆张肌") ||
            filterList[i].name.includes("头长肌") ||
            filterList[i].name.includes("颈长肌") ||
            filterList[i].name.includes("翼内肌") ||
            filterList[i].name.includes("翼外肌"),
          check3 =
            filterList[i].name.includes("鼻咽") ||
            filterList[i].name.includes("口咽") ||
            filterList[i].name.includes("腭帆提肌") ||
            filterList[i].name.includes("鼻腔");

        if (check1) {
          // 勾选咽旁间隙
          yCheck = true;
        }
        if (check2) {
          zCheck = true;
        }
        if (check3) {
          bCheck = true;
        }
        if (zCheck || yCheck || (yCheck && bCheck)) {
          tag = true;
          break;
        } else {
        }
      }
      return tag;
    },
    /**
     * @description 判断是否为T3
     */
    isTLevel3() {
      let filterList = this.poArray.filter((item) => {
        return item.lighted === true;
      });
      let tag = false;
      for (let i = 0; i < filterList.length; i++) {
        let check1 =
          filterList[i].name.includes("颈椎") ||
          filterList[i].name.includes("蝶窦底壁");
        let check2 =
          filterList[i].name.includes("翼突根部") ||
          filterList[i].name.includes("翼腭窝") ||
          filterList[i].name.includes("翼内板") ||
          filterList[i].name.includes("翼外板");
        let check3 =
          filterList[i].name.includes("岩尖") ||
          filterList[i].name.includes("斜坡") ||
          filterList[i].name.includes("蝶骨大翼")||
          filterList[i].name.includes("圆孔")||
          filterList[i].name.includes("卵圆孔")||
          filterList[i].name.includes("破裂孔");
        let check4 =
          filterList[i].name.includes("上颌窦") ||
          filterList[i].name.includes("蝶窦") ||
          filterList[i].name.includes("筛窦") ||
          filterList[i].name.includes("额窦");
        if (check1 || check2 || check3 || check4) {
          tag = true;
          break;
        }
      }
      return tag;
    },
    /**
     * @description 判断是否为 T4
     */
    isTLevel4() {
      let filterList = this.poArray.filter((item) => {
        return item.lighted === true;
      });
      let tag = false;
      for (let i = 0; i < filterList.length; i++) {
        let checked =
          filterList[i].name.includes("海绵窦") ||
          filterList[i].name.includes("下咽") ||
          filterList[i].name.includes("超翼外肌外缘软组织") ||
          filterList[i].name.includes("腮腺") ||
          filterList[i].name.includes("眼眶") ||
          filterList[i].name.includes("颅脑") ||
          filterList[i].name.includes("Meckel腔");
        if (checked) {
          tag = true;
          break;
        }
      }
      return tag;
    },
    /**
     * @description 判断是否 N1
     */
    isNLevel1() {
      let lCheck = this.form.leftGroup;
      let rCheck = this.form.rightGroup;
      if (lCheck.length > 0 && rCheck.length === 0) {
        for (let x = 0; x < lCheck.length; x++) {
          let lChecked =
            lCheck[x].includes("咽后") ||
            lCheck[x].includes("ⅠA") ||
            lCheck[x].includes("ⅠB") ||
            lCheck[x].includes("ⅡA") ||
            lCheck[x].includes("ⅡB") ||
            lCheck[x].includes("Ⅲ") ||
            lCheck[x].includes("ⅤA");
          if (lChecked && Number(this.form.maxLen) <= 6) {
            return true;
            break;
          }
        }
      } else if (rCheck.length > 0 && lCheck.length === 0) {
        for (let y = 0; y < rCheck.length; y++) {
          let rChecked =
            rCheck[y].includes("咽后") ||
            rCheck[y].includes("ⅠA") ||
            rCheck[y].includes("ⅠB") ||
            rCheck[y].includes("ⅡA") ||
            rCheck[y].includes("ⅡB") ||
            rCheck[y].includes("Ⅲ") ||
            rCheck[y].includes("ⅤA");
          if (rChecked && Number(this.form.maxLen) <= 6) {
            return true;
            break;
          }
        }
      } else if (rCheck.length > 0 && lCheck.length > 0) {
        for (let z = 0; z < lCheck.length; z++) {
          for (let j = 0; j < rCheck.length; j++) {
            let yanhou =
              lCheck[z].includes("咽后") || rCheck[j].includes("咽后");
            let leftSides =
              lCheck[z].includes("ⅠA") ||
              lCheck[z].includes("ⅠB") ||
              lCheck[z].includes("ⅡA") ||
              lCheck[z].includes("ⅡB") ||
              lCheck[z].includes("Ⅲ") ||
              lCheck[z].includes("ⅤA");
            let rightSides =
              rCheck[j].includes("ⅠA") ||
              rCheck[j].includes("ⅠB") ||
              rCheck[j].includes("ⅡA") ||
              rCheck[j].includes("ⅡB") ||
              rCheck[j].includes("Ⅲ") ||
              rCheck[j].includes("ⅤA");
            if (
              (yanhou ||
                (leftSides && !rightSides) ||
                (!leftSides && rightSides)) &&
              Number(this.form.maxLen) <= 6
            ) {
              return true;
              break;
            }
          }
        }
      }
    },
    /**
     * @description 判断是否 N2
     * <AUTHOR>
     * @date 2020/07/07
     */
    isNLevel2() {
      let lCheck = this.form.leftGroup;
      let rCheck = this.form.rightGroup;
      if (rCheck.length > 0 && lCheck.length > 0) {
        for (let i = 0; i < lCheck.length; i++) {
          for (let j = 0; j < rCheck.length; j++) {
            let leftChecked =
              lCheck[i].includes("ⅠA") ||
              lCheck[i].includes("ⅠB") ||
              lCheck[i].includes("ⅡA") ||
              lCheck[i].includes("ⅡB") ||
              lCheck[i].includes("Ⅲ") ||
              lCheck[i].includes("ⅤA");
            let rightChecked =
              rCheck[j].includes("ⅠA") ||
              rCheck[j].includes("ⅠB") ||
              rCheck[j].includes("ⅡA") ||
              rCheck[j].includes("ⅡB") ||
              rCheck[j].includes("Ⅲ") ||
              rCheck[j].includes("ⅤA");
            if (leftChecked && rightChecked && Number(this.form.maxLen) <= 6) {
              return true;
              break;
            }
          }
        }
        return false;
      } else {
        return false;
      }
    },
    /**
     * @description 判断是否 N3
     * <AUTHOR>
     * @date 2020/07/07
     */
    isNLevel3() {
      const newArr = this.poArray.filter((item, index) => {
        return item.lighted === true;
      });
      if (newArr.length > 0) {
        for (let i = 0; i < newArr.length; i++) {
          let maxFlag =
            newArr[i].name.includes("Ⅳ") ||
            newArr[i].name.includes("B") ||
            newArr[i].name.includes("A") ||
            newArr[i].name.includes("Ⅲ");
          let minFlag =
            newArr[i].name.includes("Ⅳ") || newArr[i].name.includes("ⅤB");
          if (Number(this.form.maxLen) > 6 && maxFlag) {
            return true;
            break;
          } else if (Number(this.form.maxLen) < 6 && minFlag) {
            return true;
            break;
          }
        }
      } else {
        return false;
      }
    },
    /**
     * @description 判断是否 NO
     * <AUTHOR>
     * @date 2020/07/07
     */
    isNLevel0() {
      let tag = false;
      const arr = this.poArray.filter((item, index) => {
        return item.lighted === true && item.name.includes("颈部");
      });
      tag = arr.length === 0;
      return tag;
    },
    // 判断是否 NX
    isNLevelx() {
      let newArr = this.poArray.filter((item, index) => {
        return item.lighted === true && item.name.includes("咽后");
      });
      let tag = false;
      if (newArr.length > 0 && Number(this.form.maxLen) > 6) {
        tag = true;
      }
      return tag;
    },
    /**
     * @description 包膜外侵转化为结构化字典
     */
    getInvasionValue() {
      let obj = {};
      if (this.isShow) {
        obj = {
          id: "01002",
          val: '淋巴结包膜外侵',
          name: '淋巴结包膜外侵',
          desc: '淋巴结包膜外侵',
          attrValue: [
            {
              id: "01002.01",
              val: this.form.invasion,
              pid: "01002",
              name: '淋巴结是否包膜外侵',
              desc: '淋巴结是否包膜外侵',
            },
          ],
        };
        let qfLvArr = [];
        if(this.form.invasion === '有' && this.form.qfLevel) {
          qfLvArr.push({
            id: "01002.02",
            val: this.form.qfLevel,
            pid: "01002",
            name: '淋巴结包膜外侵等级',
            desc: '淋巴结包膜外侵等级',
          })
          if(this.form.qfLevel === '3级侵犯周围' && this.form.qfLv3Org.length) {
            qfLvArr.push({
              id: "01002.03",
              val: this.form.qfLv3Org.join('、'),
              pid: "01002",
              name: '淋巴结包膜外侵周围',
              desc: '淋巴结包膜外侵周围',
            })
          }
        }
        if(qfLvArr.length) {
          obj.attrValue = [...obj.attrValue, ...qfLvArr];
        }
      } 
      return obj;
    },
    /**
     * @description 获取中线选中部位
     */
    getMiddle() {
      let str = "";
      this.form.middle.forEach((item, index) => {
        if (index === this.form.middle.length - 1) {
          str += item;
        } else {
          str += item + "，";
        }
      });
      if (str !== "") {
        str = str + "受侵。";
      } else {
        str = "";
      }
      return str;
    },
    /**
     * @description: 获取左\右侧淋巴转移--影像所见
     * @Date: 2020-09-10 10:00:09
     * @author: LLY
     */
    getLRPo(po) {
      let filterList = this.poArray.filter((item, index) => {
        return (
          item.name.includes(`[${po}]`) &&
          !item.name.includes("颈部") &&
          !item.name.includes("咽后") &&
          item.lighted === true
        );
      });
      let str = "",
        finalStr = "";
      filterList.forEach((item, index) => {
        if (index === filterList.length - 1) {
          str += item.name.split(`[${po}]`)[1];
        } else {
          str += item.name.split(`[${po}]`)[1] + "，";
        }
      });
      if (str !== "") {
        finalStr = po + "侧有" + str + "受侵。";
      }
      return finalStr;
    },

    /**
     * 获取左/右侧树JSON
     */
    getTreeJSON(po) {
      let obj = {},
        checkList = [];
      let treeJSON = po === "左" ? this.leftTreeJSON : this.rightTreeJSON;
      let treeCheck = po === "左" ? this.form.leftGroup : this.form.rightGroup;
      let otherPo = po === "左" ? this.form.leftPo : this.form.rightPo;
      let code = po === "左" ? "01004.040209" : "01007.040209";
      let attrCode = po === "左" ? "01004" : "01007";
      let attrCodeName = po === "左" ? "左侧" : "右侧";
      if (treeCheck.length === 0) {
        // if (otherPo !== "") {
        //   // 其他分区
        //   let other = {
        //     // code: code,
        //     // value: otherPo,
        //     id: code,
        //     val: otherPo,
        //     desc: otherPo,
        //     name: otherPo,
        //     pid: attrCode
        //   };
        //   checkList.push(other);
        // } else {
        //   obj = {};
        // }
      } else {
        treeCheck.forEach((item, index) => {
          treeJSON.forEach((tItem, tIndex) => {
            if (item === tItem.value) {
              checkList.push({
                id: tItem.code,
                val: tItem.value,
                desc: tItem.value,
                name: tItem.value,
                pid: attrCode
              });
            }
          });
        });
      }
      if (checkList.length === 0) {
        obj = {};
      } else {
        obj = {
          // attrCode: attrCode, // 树状结构
          // attrValue: checkList,
          id: attrCode, // 树状结构
          val: attrCodeName,
          desc: attrCodeName,
          name: attrCodeName,
          attrValue: checkList,
        };
      }
      return obj;
    },

    isEmpty(obj) {
      if (JSON.stringify(obj) !== "{}") {
        this.result.push(obj);
      }
    },

    /**
     * @description 获取左/右侧、中线多选框
     */
    getLRCheck(po) {
      let obj = {},
        checkList = [],
        arr = [],
        checkJSON = [],
        attrCode = "";
      let attrCodeName = '';
      if (po === "左") {
        arr = this.form.leftGroup;
        checkJSON = this.leftCheckJSON;
        attrCode = "01003";
        attrCodeName = "左侧"
      } else if (po === "右") {
        arr = this.form.rightGroup;
        checkJSON = this.rightCheckJSON;
        attrCode = "01006";
        attrCodeName = "右侧"
      } else {
        // 中
        attrCode = "01005";
        checkJSON = this.middleCheckJSON;
        arr = this.form.middle;
        attrCodeName = "中线"
      }
      if (arr.length === 0) {
        obj = {};
      } else {
        arr.forEach((item, index) => {
          checkJSON.forEach((cItem, cIndex) => {
            if (item === cItem.value) {
              checkList.push({
                id: cItem.code,
                val: cItem.value,
                name: cItem.value,
                desc: cItem.value,
                pid: attrCode
              });
            }
          });
        });
      }
      if (checkList.length === 0) {
        obj = {};
      } else {
        obj = {
          id: attrCode, // 多选框
          val: attrCodeName,
          name: attrCodeName,
          desc: attrCodeName,
          attrValue: checkList,
        };
      }
      return obj;
    },

    /**
     * @description 受侵部位JSON组合
     */
    getSQPo() {
      this.isEmpty(this.getLRCheck("左")); // 左侧多选框受侵
      this.isEmpty(this.getTreeJSON("左")); // 左侧树受侵
      this.isEmpty(this.getLRCheck("中")); // 中线受侵
      this.isEmpty(this.getLRCheck("右")); // 右侧多选框受侵
      this.isEmpty(this.getTreeJSON("右")); // 右侧树受侵
    },
    /**
     * @description 咽后
     */
    getYanhou() {
      let str = "",
        lStr = "",
        rStr = "";
      let filterList = this.poArray.filter((item, index) => {
        return item.name.includes("咽后") && item.lighted === true;
      });
      if (filterList.length === 0) {
        str = "";
      } else {
        filterList.forEach((item, index) => {
          if (item.name.includes("[左]")) {
            lStr += "左侧咽后受侵，";
          } else if (item.name.includes("[右]")) {
            rStr += "右侧咽后受侵，";
          }
        });
        if (lStr !== "" && rStr === "") {
          str = lStr;
        } else if (lStr === "" && rStr !== "") {
          str = rStr;
        } else if (lStr !== "" && rStr !== "") {
          str = "双侧咽后，";
        } else {
          str = "";
        }
      }
      return str;
    },
    /**
     * @description:  左/右侧淋巴
     * @Date: 2020-09-10 10:09:41
     * @author: LLY
     */
    getLRLin(po) {
      let str = "",
        fulStr = "";
      let filterList = this.poArray.filter((item, index) => {
        return (
          item.lighted === true &&
          item.name.includes(`[${po}]`) &&
          (item.name.includes("A") ||
            item.name.includes("B") ||
            item.name.includes("Ⅳ") ||
            item.name.includes("Ⅲ"))
        );
      });
      let strPo = po === "左" ? this.form.leftPo : this.form.rightPo;
      if (filterList.length === 0 && strPo === "") {
        str = "";
      } else {
        filterList.forEach((item, index) => {
          str += item.name.split(`[${po}]颈部`)[1] + "，";
        });
        if (strPo !== "") {
          str += "其他分区" + strPo + "，";
        }
      }
      fulStr = `颈部见肿大淋巴结，位于${str}`;
      return str;
    },
    /**
     * @description 返回json格式数据
     */
    getJSONObj(flag) {
      this.result = [];
      // 病灶
      let nidus = {
          id: "01001", // 病灶最大横断面
          val: '病灶最大横断面',
          name: '病灶最大横断面',
          desc: '病灶最大横断面',
          attrValue: [
            {
              id: "01001.01", // 长
              val: this.form.x,
              name: '病灶-长',
              desc: '病灶-长',
              pid: '01001'
            },
            {
              id: "01001.01.01", // 层面信息SE
              val: this.form.se,
              name: '病灶-SE',
              desc: '病灶-SE',
              pid: '01001.01'
            },
            {
              id: "01001.01.02", // 层面信息IM
              val: this.form.im,
              name: '病灶-IM',
              desc: '病灶-IM',
              pid: '01001.01'
            },
            {
              id: "01001.02", // 宽
              val: this.form.y,
              name: '病灶-宽',
              desc: '病灶-宽',
              pid: '01001'
            },
            {
              id: "01001.04", // 高
              val: this.form.z,
              name: '病灶-高',
              desc: '病灶-高',
              pid: '01001'
            },
            {
              id: "01001.04.01", // 高的层面信息SE
              val: this.form.seZ,
              name: '病灶-高-SE',
              desc: '病灶-高-SE',
              pid: '01001.04'
            },
            {
              id: "01001.04.02", // 高的层面信息IM
              val: this.form.imZ,
              name: '病灶-高-IM',
              desc: '病灶-高-IM',
              pid: '01001.04'
            },
          ],
        };
      this.result.push(nidus);
      // 包膜外侵
      this.isEmpty(this.getInvasionValue());

      if(this.isShow) {
        this.result.push({
          id: "01011", 
          val: '淋巴结坏死',
          desc: '淋巴结坏死',
          name: '淋巴结坏死',
          attrValue: [
            {
              id: "01011.01", // 淋巴坏死
              val: this.form.badStatus,
              desc: '淋巴结是否坏死',
              name: '淋巴结是否坏死',
              pid: "01011"
            },
          ]
        });
      }

      // 受侵部位
      this.getSQPo();
      
      // 淋巴结最大径
      let maxLen = {};
      if (!this.isShow) {
        this.form.maxLen = "";
      }
      if (this.form.maxLen !== "") {       
        maxLen = {
          id: "01008", // 淋巴结最大横断面
          val: '淋巴结最大横断面',
          name: '淋巴结最大横断面',
          desc: '淋巴结最大横断面',
          attrValue: [
            {
              id: "01008.01", // 淋巴结横断面最大数值
              val: this.form.maxLen,
              name: '淋巴结最大径',
              desc: '淋巴结最大径',
              pid: '01008'
            },
            {
              id: "01008.02", // 淋巴结-长
              val: this.form.maxLenX,
              name: '淋巴结-长',
              desc: '淋巴结-长',
              pid: '01008'
            },
            {
              id: "01008.03", // 淋巴结-宽
              val: this.form.maxLenY,
              name: '淋巴结-宽',
              desc: '淋巴结-宽',
              pid: '01008'
            },
            {
              id: "01008.02.01", // 淋巴结-SE
              val: this.form.maxLense,
              name: '淋巴结-SE',
              desc: '淋巴结-SE',
              pid: '01008.02'
            },
            {
              id: "01008.02.02", // 淋巴结-IM
              val: this.form.maxLenim,
              name: '淋巴结-IM',
              desc: '淋巴结-IM',
              pid: '01008.02'
            },
            {
              id: "01008.04", // 淋巴结-高
              val: this.form.maxLenZ,
              name: '淋巴结-高',
              desc: '淋巴结-高',
              pid: '01008'
            },
            {
              id: "01008.04.01", // 淋巴结-高-SE
              val: this.form.maxLenZse,
              name: '淋巴结-高-SE',
              desc: '淋巴结-高-SE',
              pid: '01008.04'
            },
            {
              id: "01008.04.02", // 淋巴结-高-IM
              val: this.form.maxLenZim,
              name: '淋巴结-高-IM',
              desc: '淋巴结-高-IM',
              pid: '01008.04'
            },
          ],
        };
      } else {
        maxLen = {};
      }
      this.isEmpty(maxLen);
      // 淋巴结最大者位于
      if(this.isShow && this.maxLb.showMaxPos) {
        this.result.push({
          id: "01015", 
          val: '淋巴结最大者位于',
          desc: '淋巴结最大者位于',
          name: '淋巴结最大者位于',
          attrValue: [
            {
              id: "01015.01", // 淋巴坏死
              val: this.maxLb.maxLbSide,
              desc: '淋巴结最大者所在侧',
              name: '淋巴结最大者所在侧',
              pid: "01015"
            },
            {
              id: "01015.02", // 淋巴坏死
              val: this.maxLb.maxLbArea,
              desc: '淋巴结最大者所在侧具体位置',
              name: '淋巴结最大者所在侧具体位置',
              pid: "01015"
            },
          ]
        });
      }

      let bdyObj = {
        id: "01016", // 鼻窦炎
        val: '鼻窦炎/中耳乳突炎',
        name: '鼻窦炎/中耳乳突炎',
        desc: '鼻窦炎/中耳乳突炎',
        attrValue: [
          {
            id: "01016.01", // 鼻窦炎
            val: this.form.isBdy,
            name: '是否有鼻窦炎/中耳乳突炎',
            desc: '是否有鼻窦炎/中耳乳突炎',
            pid: '01016'
          },
        ],
      }
      if(this.form.isBdy === '有') {
        bdyObj.attrValue.push({
          id: "01016.02", // 鼻窦炎
          val: this.form.bdyDetail,
          name: '鼻窦炎/中耳乳突炎详情',
          desc: '鼻窦炎/中耳乳突炎详情',
          pid: '01016'
        })
      }
      this.isEmpty(bdyObj);

      // 其他征象
      let others = {};
      if (this.form.others !== "") {
        others = {
          id: "01009", // 其他征象
          val: '其他征象',
          name: '其他征象',
          desc: '其他征象',
          attrValue: [
            {
              id: "01009.01", // 其他征象内容
              val: this.form.others,
              name: '其他征象',
              desc: '其他征象',
              pid: '01009'
            },
          ],
        };
      } else {
        others = {};
      }
      this.isEmpty(others);

      // 肿块描述
      let zkms = {
        id: "01012", // 其他征象
        val: '肿块描述',
        name: '肿块描述',
        desc: '肿块描述',
        attrValue: [
          {
            id: "01012.01", // 其他征象内容
            val: this.zkms,
            name: '肿块描述',
            desc: '肿块描述',
            pid: '01012'
          },
        ],
      };
      this.isEmpty(zkms);

      // 信号特点
      let xhtd = {
        id: "01013", // 其他征象
        val: '信号特点',
        name: '信号特点',
        desc: '信号特点',
        attrValue: [
          {
            id: "01013.01", // 其他征象内容
            val: this.xhtd,
            name: '信号特点',
            desc: '信号特点',
            pid: '01013'
          },
        ],
      };
      this.isEmpty(xhtd);

      // 中线选中
      let middle = this.getMiddle();
      let left = this.getLRPo("左"); // 左侧除淋巴转移
      let right = this.getLRPo("右"); // 右侧除淋巴转移
      let yanHou = this.getYanhou(); // 咽后
      let leftLin = this.getLRLin("左"); // 左边淋巴
      let rightLin = this.getLRLin("右"); // 右边淋巴
      let linBaStr = "";
      if (yanHou === "" && leftLin === "" && rightLin === "") {
        linBaStr = "未见淋巴结转移。";
      } else if (leftLin !== "" && rightLin === "") {
        linBaStr = `${yanHou}左侧颈部见肿大淋巴结，位于${leftLin}最大径 ${this.form.maxLen}mm。`;
      } else if (leftLin !== "" && rightLin !== "") {
        linBaStr = `${yanHou}左侧颈部见肿大淋巴结，位于${leftLin}右侧颈部见肿大淋巴结，位于${rightLin}最大径 ${this.form.maxLen}mm。`;
      } else if (leftLin === "" && rightLin !== "") {
        linBaStr = `${yanHou}右侧颈部见肿大淋巴结，位于${rightLin}最大径 ${this.form.maxLen}mm。`;
      } else if (leftLin === "" && rightLin == "" && yanHou !== "") {
        linBaStr = `${yanHou}最大径 ${this.form.maxLen}mm。`;
      }
      // 印象
      // let yx = {
      //   id: "01010", // 印象
      //   val: '印象',
      //   name: '印象',
      //   desc: '印象',
      //   attrValue: [
      //     {
      //       id: "01010.01", // T
      //       val: this.tLevel,
      //       name: 'T值',
      //       desc: 'T值',
      //       pid: "01010"
      //     },
      //     {
      //       id: "01010.02", // N
      //       val: this.nLevel,
      //       name: 'N值',
      //       desc: 'N值',
      //       pid: "01010"
      //     },
      //   ],
      // };
      let impObj = {}
      if(this.impStr) {
        impObj = {
          id: "01010", // 印象
          val: '印象',
          name: '印象',
          desc: '印象',
          attrValue: [
            {
              id: "01010.03",
              val: this.impStr,
              name: '印象',
              desc: '印象内容',
              pid: "01010"
            },
          ],
        };
      }
      this.isEmpty(impObj);

      // 获取引用的词条
      // let quetoW = this.getQuoteWHandler();
      // if(quetoW) {
      //   this.result = [...this.result, ...quetoW];
      // }
      let result = this.result;
      let {desc, impress,entryData} = this.getDescAndImpress();
      let des = {
        description: desc,
        impression: this.impStr,
        status: flag,
      };
      // `${this.saveQuoteArr['qw_0001.002']?'1.':''}考虑鼻咽癌${impress}${entryData}（MRI分期 T${this.tLevel}N${this.nLevel}）\n（分期标准：鼻咽癌UICC/AJCC分期第8版 / 中国分期2017版）`,
      let patientInfo = this.patientInfo;
      let pattern = {
        result,
        des,
        patientInfo,
      };
      return pattern;
    },
    // 获取描述内容
    getDescAndImpress(onlyImpress) {
      let desc = '';
      let impress = '考虑鼻咽癌';
      let entryData = '';
      let organDirectMap = this.organDirectMap;
      let middleArr = this.form.middle;
      let leftArr = this.form.leftGroup;
      let rightArr = this.form.rightGroup;
      this.qfOrgan = {
        '向前': {},
        '向后': {},
        '向外': {},
        '向上': {},
        '向下': {},
      }
      let existOrgan = [];   //已经存在的部位
      // 淋巴部分
      this.qfLb = {
        '双颈': [],
        '左侧': [],
        '右侧': []
      }
      let existLb = [];
      this.directDataHandler(middleArr, '中线', existOrgan, organDirectMap, this.qfOrgan);
      this.directDataHandler(leftArr, '左侧', existOrgan, organDirectMap, this.qfOrgan, this.qfLb, existLb);
      this.directDataHandler(rightArr, '右侧', existOrgan, organDirectMap, this.qfOrgan, this.qfLb, existLb);
      if(this.form.rightPo && this.form.leftPo && this.form.rightPo === this.form.leftPo) {
        this.qfLb['双颈'].push(this.form.rightPo)
      } else {
        if(this.form.rightPo) {
          this.qfLb['右侧'].push(this.form.rightPo) 
        }
        if(this.form.leftPo) {
          this.qfLb['左侧'].push(this.form.leftPo)
        }
      }
      if(!onlyImpress) {
        desc = this.getDescription(this.qfOrgan, this.qfLb);
      }
      // 印象--start
      let ljOrganText = this.ljOrganText(this.qfOrgan);
      impress += ljOrganText;
      let yhText = '';
      let ljLbText = '';
      let jbFlag = false;
      if(this.qfLb['双颈'].length || (this.qfLb['左侧'].length && this.qfLb['右侧'].length)) {
        ljLbText += '双颈';
        jbFlag = true;
      } else {
        if(this.qfLb['左侧'].length) {
          ljLbText += '左颈';
          jbFlag = true;
        }
        if(this.qfLb['右侧'].length) {
          ljLbText += '右颈';
          jbFlag = true;
        }
      }
      if(leftArr.includes('咽后') || rightArr.includes('咽后')) {
        let preTxt = jbFlag ? '及' : '';
        if(leftArr.includes('咽后') && rightArr.includes('咽后')) {
          yhText = '双咽后';
        } else {
          if(leftArr.includes('咽后')) {
            yhText = '左咽后';
          }
          if(rightArr.includes('咽后')) {
            yhText = '右咽后';
          }
        }
        ljLbText += preTxt + yhText;
      }
      if(jbFlag || yhText) {
        ljLbText += '淋巴结转移';
      }
      if(ljLbText) {
        if(ljOrganText) {
          impress += '并' + ljLbText;
        } else {
          impress += '，' + ljLbText;
        }
      }
      if(this.isShow && this.form.qfLevel) {
        let qfLevel = this.form.qfLevel.split('级')[0];
        impress += '，包膜外侵' + qfLevel + '级'; 
      }
      // 词条移除req17958
      // if(this.saveQuoteArr['qw_0001.002']){
      //   entryData = '\n2.鼻旁窦炎，双侧中耳乳突炎';
      // }
      if(this.form.isBdy === '有') {
        impress = '1.' + impress;
        impress += '\n2.鼻旁窦炎，双侧中耳乳突炎';
      }
      
      // 印象--end
      return {
        desc,
        impress,
        entryData
      }
    },
    // ----------------------------------------------------------------------------------------------------------------------------------------

    // 归类方向和受侵的位置
    directDataHandler(selectArr, sideName, existOrgan, organDirectMap, qfOrgan, qfLb, existLb) {
      selectArr.forEach(item => {
        if(item === '颈部' || item === '咽后') {
          return;
        }
        let dire = organDirectMap[item];
        if(dire) {
          let index = existOrgan.findIndex(organ => organ.endsWith('-'+item));  //中线的不分左右
          if(index > -1) { 
            let existItem = existOrgan[index];
            existOrgan.splice(index, 1);
            let [side = '', name] = existItem.split('-');
            qfOrgan[dire][side] = qfOrgan[dire][side].filter(sItem => sItem !== name);
            qfOrgan[dire]['双侧'] = qfOrgan[dire]['双侧'] || [];
            qfOrgan[dire]['双侧'].push(item);
            existOrgan.push('双侧-' + item)
          } else {
            qfOrgan[dire][sideName] = qfOrgan[dire][sideName] || [];
            qfOrgan[dire][sideName].push(item);
            existOrgan.push(sideName + '-' + item);
          }
        } else {
          if(qfLb && existLb) {
            let lbIndex = existLb.findIndex(lb => lb.endsWith('-'+item)); 
            if(lbIndex > -1) {
              let existLbItem = existLb[lbIndex];
              existLb.splice(lbIndex, 1);
              let [side = '', name] = existLbItem.split('-');
              qfLb[side] = qfLb[side].filter(sItem => sItem !== name);
              qfLb['双颈'] = qfLb['双颈'] || [];
              qfLb['双颈'].push(item);
              existLb.push('双颈-' + item)
            } else {
              qfLb[sideName] = qfLb[sideName] || [];
              qfLb[sideName].push(item);
              existLb.push(sideName + '-' + item);
            }
          }
        }
      })
    },

    // 受侵部位转移部位在印象中的展示文本
    ljOrganText(data) {
      let sortPathology = [];
      this.allSelection.forEach(item => {
        sortPathology.push(item.label);
      });
      let sortMiddleGroup = [];
      middleGroup.forEach(item => {
        sortMiddleGroup.push(item.label)
      });
      let concatTextArr = ['翼突根部', '翼外板', '翼内板', '蝶骨大翼','岩尖', '圆孔', '卵圆孔', '破裂孔', '斜坡', '蝶窦底壁'];   //这类部位统一合并成‘颅底受侵’
      let list = JSON.parse(JSON.stringify(data));
      let str = '';
      let allStr = [];
      let left = [], right = [], double = [], middle = [];
      let ldsq = [];  //颅底受侵
      for(let dire in list) {
        let curObj = list[dire];
        for(let key in curObj) {
          let resetData = curObj[key].filter(item => !concatTextArr.includes(item));
          if(key === '中线') {
            middle = [...middle, ...resetData];
          } else if(key === '左侧') {
            left = [...left, ...resetData];
          } else if(key === '右侧') {
            right = [...right, ...resetData];
          } else if(key === '双侧') {
            double = [...double, ...resetData];
          }
          if(resetData.length !== curObj[key].length && !ldsq.includes(key)) {
            ldsq.push(key)
          }
        }
      }
      if(this.form.middle.includes('鼻咽')) {
        middle.push('鼻咽');
      }
      middle.sort((a,b)=>{
        return sortMiddleGroup.indexOf(a) - sortMiddleGroup.indexOf(b)
      })
      left.sort((a,b)=>{
        return sortPathology.indexOf(a) - sortPathology.indexOf(b)
      })
      right.sort((a,b)=>{
        return sortPathology.indexOf(a) - sortPathology.indexOf(b)
      })
      double.sort((a,b)=>{
        return sortPathology.indexOf(a) - sortPathology.indexOf(b)
      })
    
      if(middle.length) {        
        allStr.push(middle.join('、'));
      }
      if(left.length) {
        allStr.push('左侧' + left.join('、'));
      }
      if(right.length) {
        allStr.push('右侧' + right.join('、'));
      }
      if(double.length) {
        allStr.push('双侧' + double.join('、'));
      }
      if(allStr.length) {
        str = '，累及' + allStr.join('，');
      }
      if(ldsq.length) {
        let hasInvolve =  str.includes('累及') ? '' : '累及';
        let isMiddle = ldsq.includes('中线');
        let isBilateral = ldsq.includes('双侧');
        if(ldsq.length === 1 && !isMiddle & !isBilateral){
          str += '，' + hasInvolve + ldsq[0] + '颅底';
        }else{
          str += '，' + hasInvolve +'颅底';
        }
        // str += '，' +  (ldsq.length > 1 ? '颅底' : ldsq[0] + '颅底');
      }      
      return str;
    },

    // 描述内容
    getDescription(qfOrgan, qfLb) {
      let str = '';
      let startStr = '', endStr = '', startArr = []; 
      let allStr = [];
      // 引用的词条
      // for(let idKey in this.saveQuoteArr) {
      //   let quote = this.saveQuoteArr[idKey];
      //   if(quote.position === '开头') {
      //     startStr += (startStr ? '\n' : '') + quote.content;
      //   } else {
      //     endStr += (endStr ? '\n' : '') + quote.content;
      //   }
      // }
      if(this.zkms) {
        startArr.push(this.zkms)
      }
      if(this.xhtd) {
        startArr.push(this.xhtd)
      }
      // 大小、高
      if(this.form.x || this.form.y || this.form.z) {
        startStr = '病灶';
        let sizeFlag = false;
        if(this.form.x || this.form.y) {
          startStr += `最大横断面积约${this.form.x}mm×${this.form.y}mm（SE${this.form.se}，IM${this.form.im}）`;
          sizeFlag = true;
        }
        if(this.form.z) {
          startStr += `${sizeFlag ? '，' : ''}高约${this.form.z}mm（SE${this.form.seZ}，IM${this.form.imZ}）`;
        }
        startStr && startArr.push(startStr);
      }
      if(startArr.length) {
        allStr.push('　　' + startArr.join('；') + '。');
      }
    
      // 累及部位
      let direArr = ['向前', '向后', '向外', '向上', '向下'];
      let sideArr = ['中线', '双侧', '右侧', '左侧'];
      let direDescArr = [];
      direArr.forEach(key => {
        if(qfOrgan[key] && JSON.stringify(qfOrgan[key]) !== '{}') {
          let oStr = key + '累及';
          let organs = [];
          sideArr.forEach(side => {
            if(qfOrgan[key][side] && qfOrgan[key][side].length) {            
              organs.push((side === '中线' ? '' : side) + qfOrgan[key][side].join('、'))
            }
          })
          if(organs.length) {
            direDescArr.push(oStr + organs.join('，'));
          }
        }
      })
      if(direDescArr.length || this.form.others) {
        let otherText = this.form.others ? `累及：${this.form.others}。` : ""    
        allStr.push('　　' + direDescArr.join('；') + (direDescArr.length ? '。' : '')+ otherText);
      }
      if(this.form.bdyDetail) {
        allStr.push('　　' + this.form.bdyDetail);
      }
     
      // 累计淋巴
      let ljLbArr = [];
      let leftArr = this.form.leftGroup;
      let rightArr = this.form.rightGroup;
      let yhText = '';
      if(leftArr.includes('咽后') || rightArr.includes('咽后')) {
        if(leftArr.includes('咽后') && rightArr.includes('咽后')) {
          yhText = '双咽后';
        } else {
          if(leftArr.includes('咽后')) {
            yhText = '左咽后';
          }
          if(rightArr.includes('咽后')) {
            yhText = '右咽后';
          }
        }
      }

      let lbText = [];
      let lbSide = ['双颈', '右侧', '左侧'];
      lbSide.forEach(side => {
        if(qfLb[side] && qfLb[side].length) {
          lbText.push(side + qfLb[side].join('、') + '组');
        }
      })
      let ljText = yhText + (yhText ? '及' : '') + lbText.join('，');
      if(ljText) { 
        ljLbArr.push(ljText + '见肿大淋巴结');
      }
      if(this.isShow) {
        if(this.form.invasion === '有') {
          let qfTxt = '包膜外侵' + (this.form.qfLevel || '');
          console.log(this.form.qfLevel);
          if(this.form.qfLevel.includes('3级') && this.form.qfLv3Org.length) {
            qfTxt += this.form.qfLv3Org.join('、');
          }
          ljLbArr.push(qfTxt);
        }
        if(this.form.badStatus === '有') {
          ljLbArr.push('内见坏死');
        }
        if(this.form.maxLenX && this.form.maxLenY && this.form.maxLenZ) {
          let se = this.form.maxLense || "";
          let im = this.form.maxLenim || "";
          let zse = this.form.maxLenZse || "";
          let zim = this.form.maxLenZim || "";
          ljLbArr.push('最大横断面积约：'+this.form.maxLenX+'mmx'+this.form.maxLenY+'mm（SE'+se+',IM'+im+'），高约'+this.form.maxLenZ+'mm（SE'+zse+',IM'+zim+'）');
        }
        if(this.maxLb.showMaxPos) {
          ljLbArr.push('最大者位于'+this.maxLb.maxLbSide+this.maxLb.maxLbArea+'组');
        }
      }
      let lbAndOther = '';
      if(ljLbArr.length) {
        lbAndOther = ljLbArr.join('，') + '。';
      }
     
      if(lbAndOther) {
        allStr.push('　　' + lbAndOther);
      }
      // if(this.form.others) {
      //   lbAndOther.push( '其他征象：' + this.form.others)
      // }
      if(endStr) {
        allStr.push('　　' + endStr);
      }
      str = allStr.join('\n');
      return str;
    },
    /**
     * @description 校验
     */
    validateForm() {
      if(!this.isShow) {
        return 'pass';
      }
      let maxLenisData = this.form.maxLen !=="" && this.form.maxLenX !=="" && this.form.maxLenY !=="" && this.form.maxLenZ !==""; //淋巴最大横断面长宽高是否都填了
      if(!maxLenisData) {
        return '请填写淋巴结最大横断面'
      }

      if(this.maxLb.showMaxPos) {
        let flag = this.maxLb.maxLbSide && this.maxLb.maxLbArea;  //已填
        if(!flag) {
          return '请选择淋巴结最大者位于'
        }
      }

      return 'pass';
    },
    /**
     * @description:  提交报告
     * @Date: 2020-07-06 16:24:26
     * @author: LLY
     */
    async submitForm(subParams) {
      let {isSubmit, newMode, toRedis} = subParams;
      let maxLenList = [Number(this.form.maxLenX),Number(this.form.maxLenY),Number(this.form.maxLenZ)];
      let maxSize = Math.max.apply(null,maxLenList)
      this.firstIn = true;
      this.form.maxLen = maxSize.toString();
      let tag = toRedis || this.validateForm() === 'pass';
      setTimeout(() => {
        this.firstIn = false;
      }, 100)
      if (tag) {       
        let pattern = this.getJSONObj(isSubmit);
        let {result = [], des = {}} = pattern || {};
        let docAttr = this.dataToTreeData(result);
        let data = {
          patternName: '福州-鼻咽癌',
          optType: isSubmit,
          description: des.description,
          impression: des.impression,
          docAttr: docAttr,
          busInfo: {},
          mainFlag: '1'
        };
        let postParams = this.saveParams(data);
        if(toRedis) {
          this.submitFlag = false;
          return [postParams];
        }
        console.log('postParams',JSON.stringify([postParams]));
        let apiRes = await this.saveDataByApi([postParams]);
        if(this.sourceType === '1') {
          this.submitFlag = false;
          // return apiRes;
        }

        let pacsRes = true;
        if(apiRes && (Number(this.sourceType) > 1 || this.isFromYunPacs)) {
          let yParams = {
            ...postParams,
            ypacsRptInfo: this.ypacsRptInfo,
          }
          pacsRes = await this.saveOrSubmitToYPacs(yParams, isSubmit, !this.docId);
          if(!pacsRes) {
            this.submitFlag = false;
            return false;
          }
        }
        this.submitFlag = false;
        if(apiRes && pacsRes) {
          setTimeout(() => {
            if(!newMode) {
              this.srCommonInnerOpt(isSubmit, true);  //2确认时才上传pdf2，其他传1
            }
          }, 1000);
          return apiRes;
        }

      } else {
        this.$confirm(this.validateForm(), {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "",
        })
          .then(() => {
            // 确认
            return;
          })
          .catch(() => {
            // 取消
            return;
          });
      }
    },
    /**
     * @description:  筛选节点
     * @Date: 2020-07-03 15:26:52
     * @author: LLY
     */
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      if (data.shortCut) {
        return (
          data.label.indexOf(value) !== -1 ||
          data.shortCut.indexOf(value) !== -1
        );
      } else {
        return data.label.indexOf(value) !== -1;
      }
    },
    /**
     * @description:  显示部位名称
     * @Date: 2020-07-03 14:37:43
     * @author: LLY
     */
    showPositionName(e) {
      // 转换鼠标坐标
      let rect = e.target.getBoundingClientRect();
      let x = e.clientX - rect.left * (e.target.width / rect.width);
      let y = e.clientY - rect.top * (e.target.height / rect.height);
      let currentPoint = { x: x, y: y };
      // 循环现有图形
      this.currentShapeIndex = 0;
      for (let i = 0; i < this.currArray.length; i++) {
        let currentShapePoint = this.currArray[i];
        // this.drawImg();
        if (this.rayCasting(currentPoint, currentShapePoint)) {
          this.currentShapeIndex = i;
          this.currPosition = this.crrPoArr[i];
          break;
        } else {
          this.currentShapeIndex = null;
          // this.drawAllShape(this.context);
        }
      }
    },
    /**
     * 鼠标监听
     */
    moveHandler(e) {
      if(this.trace) {
        return;
      }
      let arr = this.neckArr;
      // 转换鼠标坐标
      let rect = e.target.getBoundingClientRect();
      let x = e.clientX - rect.left * (e.target.width / rect.width);
      let y = e.clientY - rect.top * (e.target.height / rect.height);
      let currentPoint = { x: x, y: y };
      // 循环现有图形
      this.currentShapeIndex = 0;
      for (let i = 0; i < this.currArray.length; i++) {
        let currentShapePoint = this.currArray[i];
        this.cleanCanvas(this.context);
        this.drawImg();
        if (this.rayCasting(currentPoint, currentShapePoint)) {
          this.currentShapeIndex = i;
          this.currPosition = this.crrPoArr[i];
          // 标记点亮部位
          this.poArray.forEach((item, index) => {
            if (this.currPosition === "鼻腔") {
              this.bqVisible = true;
              this.edVisible = false;
              this.sdVisible = false;
              this.curCheck = this.bqCheckList;
            }
            if (this.currPosition === "额窦") {
              this.edVisible = true;
              this.bqVisible = false;
              this.sdVisible = false;
              this.curCheck = this.edCheckList;
            }
            if (this.currPosition === "筛窦") {
              this.sdVisible = true;
              this.bqVisible = false;
              this.edVisible = false;
              this.curCheck = this.sdCheckList;
            }
            if (item.name === this.currPosition) {
              if (item.lighted) {                                             // 取消高亮
                item.lighted = false;
                let po = item.name[1];
                let group =
                  po === "左" ? this.form.leftGroup : this.form.rightGroup;
                let tag = po === "左" ? "1" : "2";
                if (
                  item.name.startsWith(`[左]`) ||
                  item.name.startsWith(`[右]`)
                ) {
                  // 左右部分
                  if (item.name.startsWith(`[${po}]`)) {
                    let flag = item.name.split(`[${po}]`)[1];
                    let name = flag.includes("颈部")
                      ? flag.split("颈部")[1]
                      : flag;
                    // 监听多选框
                    group.forEach((itemCheck, indexCheck) => {
                      if (name === itemCheck) {
                        group.splice(indexCheck, 1);
                      }
                    });
                    // 鼻腔 / 筛窦 / 额窦
                    if (name === "鼻腔") {
                      this.bqCheckList.forEach((item, index) => {
                        if (item === tag) {
                          this.bqCheckList.pop(index, 1);
                        }
                      });
                    } else if (name === "筛窦") {
                      this.sdCheckList.forEach((item, index) => {
                        if (item === tag) {
                          this.sdCheckList.pop(index, 1);
                        }
                      });
                    } else if (name === "额窦") {
                      this.edCheckList.forEach((item, index) => {
                        if (item === tag) {
                          this.edCheckList.pop(index, 1);
                        }
                      });
                    }
                    // 颈部选中
                    let count = 0;
                    arr.forEach((nItem, nIndex)=>{
                      if(group.indexOf(nItem)>-1) {
                        count ++;
                      }
                    })
                    if(count < this.neckArr.length) {
                      const index = group.indexOf('颈部')
                      if(index>-1) {
                        group.splice(index, 1);
                      }
                    }
                    if (po === "左") {
                      this.isLIndeterminate = count > 0 && count < this.neckArr.length;
                      this.form.leftGroup = group;
                    } else {
                      this.isRIndeterminate = count > 0 && count < this.neckArr.length;
                      this.form.rightGroup = group;
                    }
                  }
                } else if (item.name.startsWith("[中]")) {
                  // 中间部分
                  // 监听多选框
                  this.form.middle.forEach((itemCheck, indexCheck) => {
                    if (item.name.split("[中]")[1].includes("颈椎")) {
                      this.form.middle.splice(indexCheck, 1);
                    } else if (item.name.split("[中]")[1] === itemCheck) {
                      this.form.middle.splice(indexCheck, 1);
                    }
                  });
                }
              } else {  // 点击高亮
                item.lighted = true;
                if (item.imgList) {
                  item.imgList.forEach((imgItem, imgIndex) => {
                    if (imgItem.imgName === this.currImg) {
                      this.addShape(
                        item.name,
                        this.context,
                        imgItem.location,
                        true
                      );
                    }
                  });
                }
                let po = item.name[1];
                let poTag = po === "左" ? "1" : "2";
                let group = po === "左" ? this.form.leftGroup : this.form.rightGroup;
                if (
                  item.name.startsWith("[左]") ||
                  item.name.startsWith("[右]")
                ) {
                  if (item.name.startsWith(`[${po}]`)) {
                    let flag = item.name.split(`[${po}]`)[1];
                    let name = flag.includes("颈部")
                      ? flag.split("颈部")[1]
                      : flag;
                    group.push(name); // 多选框组
                    // 鼻腔 额窦 筛窦
                    if (name === "鼻腔") {
                      this.bqCheckList.push(poTag);
                    } else if (name === "额窦") {
                      this.edCheckList.push(poTag);
                    } else if (name === "筛窦") {
                      this.sdCheckList.push(poTag);
                    }

                    // 颈部选中
                    let count = 0;
                    arr.forEach((nItem, nIndex)=>{
                      if(group.indexOf(nItem)>-1) {
                        count ++;
                      }
                    })
                    if(count=== this.neckArr.length) {
                      group.push('颈部');
                    }
                    if (po === "左") {
                      this.isLIndeterminate = count > 0 && count < this.neckArr.length;
                      this.form.leftGroup = group;
                    } else {
                      this.isRIndeterminate = count > 0 && count < this.neckArr.length;
                      this.form.rightGroup = group;
                    }
                  }
                } else if (item.name.startsWith("[中]")) {
                  if (item.name.split("[中]")[1].includes("颈椎")) {
                    this.form.middle.push("颈椎"); // 多选框组
                  } else {
                    this.form.middle.push(item.name.split("[中]")[1]); // 多选框组
                  }
                }
              }
            }
          });
          this.nLevel = this.countNLevel();
          this.tLevel = this.countTLevel();
          break;
        } else {
          this.currentShapeIndex = null;
        }
      }
      this.lightedPo();
    },
    /**
     * @description 淋巴最大径输入
     */
    maxLenInput(val) {
      if (val) {
        this.nLevel = this.countNLevel();
      }
    },
    /**
     * @description 计算 N0 N1 N2 N3级别
     */
    countNLevel() {
      this.nChange = true;
      if (this.isNLevel3()) {
        return "3";
      } else {
        if (this.isNLevel2()) {
          return "2";
        } else {
          if (this.isNLevel1()) {
            return "1";
          } else {
            if (this.isNLevel0()) {
              return "0";
            } else {
              return "X";
            }
          }
        }
      }
    },
    /**
     * @description 计算 T0 T1 T2 T3 T4 TX
     */
    countTLevel() {
      this.tChange = true;
      if (this.isTLevel4()) {
        return "4";
      } else {
        if (this.isTLevel3()) {
          return "3";
        } else {
          if (this.isTLevel2()) {
            return "2";
          } else {
            if (this.isTLevel1()) {
              return "1";
            } else {
              return "X";
            }
          }
        }
      }
    },
    rayCasting(p, poly) {
      var px = p.x,
        py = p.y,
        flag = false;
      for (var i = 0, l = poly.length, j = l - 1; i < l; j = i, i++) {
        var sx = poly[i].x,
          sy = poly[i].y,
          tx = poly[j].x,
          ty = poly[j].y;
        // 点与多边形顶点重合
        if ((sx === px && sy === py) || (tx === px && ty === py)) {
          return "on";
        }
        // 判断线段两端点是否在射线两侧
        if ((sy < py && ty >= py) || (sy >= py && ty < py)) {
          // 线段上与射线 Y 坐标相同的点的 X 坐标
          var x = sx + ((py - sy) * (tx - sx)) / (ty - sy);
          // 点在多边形的边上
          if (x === px) {
            return "on";
          }
          // 射线穿过多边形的边界
          if (x > px) {
            flag = !flag;
          }
        }
      }
      // 射线穿过多边形边界的次数为奇数时点在多边形内
      return flag;
    },
    /**
     * 图像描边
     * @param ctx
     * @param pointArry
     */
    addShape(name, ctx, pointArry, lightUpTag) {
      ctx.save();
      if (this.currImg === "左" || this.currImg === "右") {
        ctx.strokeStyle = "rgba(54, 205, 255, 1)"; // 填充线颜色
        ctx.fillStyle = "rgba(54, 205, 255, 0.2)"; // 填充绘画颜色
        ctx.lineWidth = 3;
      } else if (this.T2.includes(name)) {
        ctx.strokeStyle = "rgba(54, 205, 255, 1)"; // 填充线颜色
        ctx.fillStyle = "rgba(54, 205, 255, 0.2)"; // 填充绘画颜色
        ctx.lineWidth = 2;
      } else if (this.T1.includes(name)) {
        ctx.strokeStyle = "rgba(139, 206, 68, 1)"; // 填充线颜色
        ctx.fillStyle = "rgba(139, 206, 68, 0.2)"; // 填充绘画颜色
        ctx.lineWidth = 2;
      } else if (this.T3.includes(name)) {
        ctx.strokeStyle = "rgba(255, 179, 0, 1)"; // 填充线颜色
        ctx.fillStyle = "rgba(255, 179, 0, 0.2)"; // 填充绘画颜色
        ctx.lineWidth = 2;
      } else if (this.T4.includes(name)) {
        ctx.strokeStyle = "rgba(207, 39, 10, 1)"; // 填充线颜色
        ctx.fillStyle = "rgba(207, 39, 10, 0.2)"; // 填充绘画颜色
        ctx.lineWidth = 2;
      }
      ctx.font = "normal normal 600 14px serif";
      ctx.beginPath();

      let i;
      for (i = 0; i < pointArry.length; i++) {
        pointArry[i + 1] && ctx.lineTo(pointArry[i + 1].x, pointArry[i + 1].y);
      }
      // ctx.fillText("图形" + "mm", (pointArry[0].x + pointArry[i - 1].x) / 2, (pointArry[0].y + pointArry[i - 1].y) / 2 - 10);
      ctx.closePath();
      ctx.stroke();
      if (lightUpTag) {
        ctx.fill();
      }
      ctx.restore();
    },
    /**
     * 绘制所有图形
     */
    drawAllShape(ctx) {
      this.currArray.forEach((item, index) => {
        this.addShape(ctx, item);
      });
    },
    /**
     * @description:  初始化canvas
     * @Date: 2020-07-01 15:41:40
     * @author: LLY
     */
    initCanvas() {
      this.img = this.$refs.aImg;
      const canvas = this.$refs.canvas;
      this.context = canvas.getContext("2d");
      this.img.onload = () => {
        this.context.drawImage(this.img, 0, 0, 450, 540); // 绘制画布
      };
    },
    /**
     * @description:  重新绘制画布
     * @Date: 2020-07-01 15:41:40
     * @author: LLY
     */
    drawImg() {
      if (this.img === this.$refs.lImg || this.img === this.$refs.rImg) {
        this.$refs.canvas.height = 450;
        this.context.drawImage(this.img, 0, 0, 450, 450);
      } else {
        this.$refs.canvas.height = 540;
        this.context.drawImage(this.img, 0, 0, 450, 540);
      }
    },
    /**
     * @description:  清除画布
     * @Date: 2020-07-01 15:41:40
     * @author: LLY
     */
    cleanCanvas() {
      this.context.clearRect(0, 0, 450, 540);
    },
    /**
     * @description:  受侵部位
     * @Date: 2020-07-01 16:11:16
     * @author: LLY
     */
    changePosition(i) {
      this.position = i;
      // disabled--false 显示
      this.allSelection.forEach((item, index)=>{
        if(!(item.label.includes(this.filterText) || item.shortCut.includes(this.filterText))) {
          if(i==='0') {
            item.checked[0].disabled=true
          } else {
            item.checked[1].disabled=true
          }
        }
        // 另一边全部显示
        if(i==='0') {
            item.checked[1].disabled=false
          } else {
            item.checked[0].disabled=false
          }
      })
    },

    /**
     * @description:  切换图片
     * @Date: 2020-07-01 16:11:16
     * @author: LLY
     */
    changeImg(i) {
      this.currImg = i;
      this.cleanCanvas();
      switch (i) {
        case "2A":
          this.commonImgChange("aImg", this.aArray, this.aP);
          break;
        case "2B":
          this.commonImgChange("bImg", this.bArray, this.bP);
          break;
        case "2C":
          this.commonImgChange("cImg", this.cArray, this.cP);
          break;
        case "2D":
          this.commonImgChange("dImg", this.dArray, this.dP);
          break;
        case "2E":
          this.commonImgChange("eImg", this.eArray, this.eP);
          break;
        case "2F":
          this.commonImgChange("fImg", this.fArray, this.fP);
          break;
        case "左":
          this.commonImgChange("lImg", this.lArray, this.lP);
          break;
        case "右":
          this.commonImgChange("rImg", this.rArray, this.rP);
          break;
        default:
          this.commonImgChange("aImg", this.aArray, this.aP);
      }
      // 关闭筛窦 额窦 鼻腔弹窗
      this.sdVisible = false;
      this.edVisible = false;
      this.bqVisible = false;
      this.lightedPo();
    },
    commonImgChange(i, imgArray, imgP) {
      this.img = this.$refs[i];
      this.drawImg(); // 重新绘制画布
      this.currArray = imgArray;
      this.crrPoArr = imgP;
    },
    /*
     *@description: 点亮部位
     *@author: LLY
     *@date: 2020-07-06 11:09:09
     */
    lightedPo(isYh, keyName) {
      if(isYh && isYh.indexOf('咽后') > -1) {
        this.poArray.map(item => {
          if(item.name === isYh) {
            item.lighted = this.form[keyName].includes('咽后');
          }
        })
      }
      this.cleanCanvas();
      this.drawImg();
      let lightedArr = this.poArray.filter((item, index) => {
        return item.lighted === true;
      });
      // console.log('目前选中：', lightedArr);
      if (lightedArr.length !== 0) {
        lightedArr.forEach((item, index) => {
          if (item.lighted) {
            if (item.imgList) {
              item.imgList.forEach((imgItem, imgIndex) => {
                if (imgItem.imgName === this.currImg) {
                  this.addShape(
                    item.name,
                    this.context,
                    imgItem.location,
                    true
                  );
                }
              });
            }
          }
        });
      }
      // 控制淋巴结最大径的显示和隐藏
      let filterList = this.poArray.filter((item, index) => {
        return item.lighted === true && item.name.includes("颈部");
      });
      // this.isShow =
      //   filterList.length > 0 ||
      //   this.form.leftPo !== "" ||
      //   this.form.rightPo !== "";

      this.checkNeckArr();
    },

    // 存储颈部勾选的项
    checkNeckArr() {
      let sideArr = ['左', '右'];
      let count = 0;
      let checkSideArr = [];  //选中的侧
      let checkAreaArr = [];  //选中的具体位置
      for(let side of sideArr) {
        let allCheckData = side === '左' ? this.form.leftGroup : this.form.rightGroup;
        let lbChecked = allCheckData.filter(ck => this.neckArr.includes(ck));
        this.maxLb.xbCheckedArr[side] = lbChecked;
        count += lbChecked.length;
        if(lbChecked.length > 0) {
          checkSideArr.push(side);
          checkAreaArr = [...checkAreaArr, ...lbChecked];
        }
      }
      // 去重且排序
      checkAreaArr = Array.from(new Set(checkAreaArr)).sort((a, b) => {
        return this.neckArr.indexOf(a) - this.neckArr.indexOf(b);
      });
      this.maxLb.showMaxPos = count >= 2;
      this.maxLb.maxLbOptions = checkAreaArr;
      if(!this.firstIn) {
        if(!checkSideArr.includes(this.maxLb.maxLbSide.replace('侧', '')) || checkSideArr.length === 1) {
          this.maxLb.maxLbSide = checkSideArr.length === 1 ? checkSideArr[0] + '侧' : '';
        }
        if(!this.maxLb.maxLbOptions.includes(this.maxLb.maxLbArea)) {
          this.maxLb.maxLbArea = '';
        }
      }

      // 勾选了颈部具体位置，显示淋巴结详情
      this.isShow = count > 0;
    },

    // 词条的保存json
    getQuoteWHandler() {
      let arr = null;
      if(JSON.stringify(this.saveQuoteArr) !== '{}') {
        arr = [{
          "id":"qw_0001",
          "val": "词条",
          "name": "词条",
          "desc": "词条",
          "attrValue":[]
        }];
        for(let key in this.saveQuoteArr) {
          arr[0].attrValue.push({
            id: key,
            val: this.saveQuoteArr[key].content,
            pid: 'qw_0001',
          })
          arr.push({
            "id": key,
            "attrValue":[
              {
                "id": key + '.01',
                "val": this.saveQuoteArr[key].position,
                "desc": '词条存放位置',
                "pid": key
              }
            ]
          })
        }
      }
      return arr;
    },
    
    // 打开词条
    openQuoteWordLayer() {
      this.quoteWordVisible = true;
    },

    // 保存词条
    saveQuoteWord(saveQuoteArr) {
      this.saveQuoteArr = saveQuoteArr;
      let {impress,entryData, desc} = this.getDescAndImpress(true);
      this.otherImpression = impress;
      this.secondEntryData = entryData;
    },

  },
};
</script>
<style lang="scss">
.el-form-item {
  margin-bottom: 8px !important;
}
.el-form-item__label {
  padding: 0px;
  line-height: 28px;
}
.el-form-item__content {
  line-height: 22px;
}
.el-form-item__label {
  padding: 0px;
}
.el-input__inner {
  padding: 6px;
}
.el-textarea {
  /* margin-top: 6px !important; */
}
.el-textarea__inner {
  padding: 5px 8px;
}
.el-button-group .el-button {
  padding: 6px 15px;
}
.el-icon-my-submit {
  background: url(~@/assets/images/save.png) center center;
  background-size: contain;
  width: 20px;
  height: 20px;
  vertical-align: middle;
}
.el-icon-my-send {
  background: url(~@/assets/images/submit.png) center center;
  background-size: contain;
  width: 20px;
  height: 20px;
  vertical-align: middle;
}
.el-checkbox__label {
  line-height: 14px;
}
.el-form > div:nth-child(4) > .el-form-item__content {
  height: 100%;
}
/* 去掉树选择节点的背景 */
.el-tree-node:focus > .el-tree-node__content {
  background: none;
}
.el-tree-node__content:hover {
  background: none;
}
/* 去除el-tree一级多选 */
.el-tree > div > .el-tree-node__content > label {
  display: none;
}
/* 淋巴结转移 */
.el-tree > div:nth-child(4) > .el-tree-node__children > div:first-child {
  /* padding-left: 6px !important; */
  height: 22px;
}
.el-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > .el-tree-node
  > div:last-child
  .is-leaf {
  display: none;
}
/* 其他分区 */
.el-tree .el-input--mini .el-input__inner {
  height: 25px;
}
/* 去除树节点多余选框 */
.el-tree
  > div:not(:last-child)
  > .el-tree-node__children
  > div
  > .el-tree-node__content
  .is-leaf {
  display: none;
}
/* 设置树状标题 */
.el-tree > div > .el-tree-node__content .el-tree-node__label {
  font-weight: 600;
  color: #606266 !important;
}
/* 选中树节点字体颜色 */
.el-tree
  .is-checked
  .el-tree-node__content
  .custom-tree-node
  .el-tree-node__label {
  color: #409eff !important;
}
/* 树节点 */
.el-tree-node.is-expanded
  > .el-tree-node__children
  > div
  .el-tree-node__content
  > .el-tree-node__expand-icon {
  margin-left: -18px;
}
.el-tree-node__expand-icon.expanded {
  display: inline-block !important;
}
.el-tree-node__content > .el-tree-node__expand-icon {
  padding: 6px;
  /* display: none; */
}
/* 其他分区去除多选 */
.el-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:last-child
  > .el-tree-node__children
  > div:last-child
  > .el-tree-node__content
  > .el-checkbox {
  display: none;
}
.el-tree__empty-block {
  display: none;
}
/* 禁用上下箭头 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/* 底部按钮字体样式 */
.footer .el-button span {
  vertical-align: middle;
}
/* 印象输入框 */
/* .footer .el-input.is-disabled .el-input__inner {
  color: #000;
} */
/* 设置翼状结构 颅底 鼻窦 节点样式 */
.filter-tree > div:nth-child(1) .el-tree-node .el-tree-node__content,
.filter-tree > div:nth-child(2) .el-tree-node .el-tree-node__content,
.filter-tree > div:nth-child(3) .el-tree-node .el-tree-node__content {
  padding: 0 !important;
}
.filter-tree > div:nth-child(1) .el-tree-node__children,
.filter-tree > div:nth-child(2) .el-tree-node__children,
.filter-tree > div:nth-child(3) .el-tree-node__children {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-left: 24px;
}
/* 翼状结构 颅底 */
.filter-tree > div:nth-child(1) .el-tree-node__children > div:nth-child(1),
.filter-tree > div:nth-child(2) .el-tree-node__children > div:nth-child(1) {
  margin-right: 15px;
}
.filter-tree > div:nth-child(1) .el-tree-node__children > div:nth-child(2) {
  margin-right: 10px;
}
/* 鼻窦 */
.filter-tree > div:nth-child(3) .el-tree-node__children > div:nth-child(1) {
  margin-right: 28px;
}
.filter-tree > div:nth-child(3) .el-tree-node__children > div:nth-child(2) {
  margin-right: 23px;
}
/* 淋巴结转移 */
.filter-tree > div:nth-child(4) .el-tree-node__children {
  width: 200px;
  margin-left: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
/* 咽后 */
.filter-tree > div:nth-child(4) > .el-tree-node__children > div:nth-child(1) {
  width: 24px;
  margin-left: 24px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(1)
  .el-tree-node__content {
  padding: 0 !important;
}
/* 颈部 */
.filter-tree > div:nth-child(4) > .el-tree-node__children > div:nth-child(2) {
  width: 200px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__content {
  width: 46px;
  height: 22px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-left: 14px;
}
/* 颈部子节点 */
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div
  .el-tree-node__content {
  padding: 0px !important;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(1),
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(4),
.el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(7) {
  margin-right: 8px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(2),
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(5) {
  margin-right: 10px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(5)
  .el-tree-node__content {
  width: 40px !important;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(6)
  .el-tree-node__content {
  width: 40px !important;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(5) {
  width: 40px !important;
  margin-right: 20px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:not(:last-child) {
  width: 50px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(8) {
  margin-right: 40px;
}
.filter-tree
  > div:nth-child(4)
  > .el-tree-node__children
  > div:nth-child(2)
  .el-tree-node__children
  > div:nth-child(9) {
  height: 26px;
}

/* 改造树形部分 */
.collp {
  position: relative;
}
.collp .el-collapse-item__wrap {
  border: none;
}
.collp .el-collapse-item__header {
  border: none;
  height: 26px;
  margin-left: 24px;
  background: rgba(240, 242, 245, 1);
  /* border: 1px solid rgba(240, 242, 245, 1); */
}
.collp .el-collapse-item__content {
  background: rgba(240, 242, 245, 1);
  padding: 0;
  padding-left: 16px;
}
/* 箭头 */
.collp .el-icon-arrow-right:before {
  content: "\E791";
}
.collp .el-collapse-item__arrow {
  position: absolute;
  left: 8px;
  margin: 0;
}
.collp .el-collapse-item__wrap {
} /* 折叠展开 */
.collp .el-collapse-item__header {
  color: #606266;
  font-weight: 600;
}
.el-message-box__message p { font-family: Microsoft YaHei!important; }
.checkbox-group-wrap .arrow {
  margin-right: 10px;
  margin-left: -2px;
}
.bya.traceStatus {
  cursor: default !important;
  * {
    cursor: default !important;
  }
  .el-radio-button__orig-radio:disabled:checked+.el-radio-button__inner {
    background: #A0CFFF;
    color: #FFF;
  }
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background: #FFF;
  }
  .is-disabled {
    & + {
      span.el-checkbox__label, span.el-radio__label {
        color: #333 !important;
      }
    }
    .el-input__inner {
      color: #606266 !important;
    }
  }
  *:hover {
    border-color: #DCDFE6;
  }
  *:active,*:focus-visible,*:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: #DCDFE6!important;
  }
  .el-date-editor .el-icon-circle-close {
    display: none!important;
  }
  [data-trace]:not([data-trace="no"]) {
    color: #F56C6C !important;
    input, textarea, label[for] {
      color: #F56C6C !important;
    }
    &.active {
      .el-radio__label {
        color: #F56C6C !important;
      }
    }
    &.is-active {
      .el-radio-button__inner {
        background-color: #F56C6C !important;
      }
    }
    &.is-checked{
      .el-checkbox__label, .el-radio__label {
        color: #F56C6C !important;
      }
    }
    [disabled] {
      &.el-radio-button__orig-radio + * {
        color: #fff !important;
      }
    }
  }
}
</style>

<style scoped>
/* * {
  margin: 0;
  padding: 0;
} */
#main {
  min-width: 1320px;
  min-height: 690px;
  display: flex;
}
/* 设置滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  background-color: rgba(255, 255, 255, 0.1);
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 3px;
}
/* 滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.2);
}

#left-container {
  width: 792px;
  height: 100vh;
  min-height: 690px;
  display: flex;
  flex-direction: column;
  margin-right: 8px;
  background-color: white;
}
#left-container .header {
  width: 100%;
  height: 40px;
  padding: 6px 12px;
  background: rgba(232, 243, 255, 1);
  text-align: left;
  border-right: 1px solid rgba(200, 215, 230, 1);
  box-sizing: border-box;
}
.tit {
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 1);
  margin-right: 17px;
}
.pat {
  display: inline-block;
  margin-left: 24px;
}
/* 患者信息 */
/* .pat .info-title {
  color: #666;
  font-size: 14px;
} */
.pat .info {
  display: inline-block;
  font-size: 18px;
  color: #000;
  font-weight: 600;
}
/* 受侵部位表格 */
.form {
  width: 100%;
  flex: 1;
  border: solid 1px #c8d7e6;
  overflow: auto;
  padding: 12px;
  background: rgba(255, 255, 255, 1);
  box-shadow: -1px 0px 0px 0px rgba(200, 215, 230, 1);
  box-sizing: border-box;
}
.table {
  width: 649px;
  height: 100%;
}
.table-head {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 40px;
  padding: 6px 12px;
  background: rgba(245, 247, 250, 1);
  border: 1px solid rgba(200, 215, 230, 1);
  border-bottom: none;
  box-sizing: border-box;
}
.table-head .el-button {
  padding: 5px 15px;
}
.table-body {
  border-top: 1px solid rgba(200, 215, 230, 1);
  height: calc(100% - 40px);
}
.body-title {
  display: flex;
  font-size: 14px;
  color: rgba(48, 49, 51, 1);
}
.body-title > div {
  border-bottom: 1px solid rgba(200, 215, 230, 1);
  text-align: center;
  box-sizing: border-box;
}
.body-title > div > span {
  display: block;
  color: rgba(48, 49, 51, 1);
  font-size: 14px;
  line-height: 25px;
  vertical-align: middle;
}
.el-form {
  height: 100%;
}
.el-form > div:nth-child(4) {
  /* height: calc(100% - 216px); */
}

.t-item {
  display: inline-block;
  width: 266px;
  height: 24px;
  background: rgba(231, 236, 241, 1);
  border-left: 1px solid rgba(200, 215, 230, 1);
  border-right: 1px solid rgba(200, 215, 230, 1);
}
.t-middle {
  display: inline-block;
  width: 117px;
  height: 24px;
  background: rgba(231, 236, 241, 1);
  /* box-shadow: 1px -1px 0px 0px rgba(200, 215, 230, 1); */
}
.body-content {
  display: flex;
  height: calc(100% - 30px);
  /* overflow: hidden; */
}
.body-content > .left-group,
.right-group,
.middle-group {
  background: rgba(240, 242, 245, 1);
  border-left: 1px solid rgba(200, 215, 230, 1);
  border-bottom: 1px solid rgba(200, 215, 230, 1);
  box-sizing: border-box;
  overflow: auto;
}
.body-content .right-group .checkBox .el-checkbox-group,
.left-group .checkBox .el-checkbox-group {
  width: 100%;
  margin-top: 8px;
  padding-bottom: 0;
}
.body-content .el-checkbox-group .el-checkbox {
  display: inline-block;
}
/* T1 T2 T3 T4 标题 */
.checkBox {
  display: flex;
}
.t-wrap {
  padding-top: 6px;
  box-sizing: border-box;
}
.check-item {
  display: flex;
}
.check-label {
  margin: 0 4px 0px 8px;
  font-size: 14px;
  color: #606266;
  width: 22px;
}
.checkbox-group-wrap {
  flex: 1;
}
.subTitle {
  /* display: flex; */
}
.subTitle span {
  display: block;
  margin: 6px 4px 0px 8px;
  font-size: 14px;
  color: #606266;
}
.subTitle span:nth-child(3) {
  margin-top: 32px;
}
.body-content > .left-group {
  width: 266px;
  padding-bottom: 4px;
  box-sizing: border-box;
  border-right: solid 1px rgba(200, 215, 230, 1);
}
.body-content > .right-group {
  width: 265px !important;
  padding-bottom: 4px;
  box-sizing: border-box;
}
/* 中线 */
.body-content > .middle-group {
  width: 118px;
  padding: 8px;
  box-sizing: border-box;
}
.body-content > .middle-group > .el-checkbox-group {
  display: flex;
  flex-direction: column;
}
.body-content > .middle-group > .el-checkbox-group .el-checkbox {
  margin-bottom: 3px;
  height: 22px;
}
.body-content > .right-group {
  width: 266px;
}
.right-group > .el-checkbox-group,
.left-group > .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  /* min-height: 110px; */
  /* padding: 8px 7px; */
  padding-bottom: 0;
  box-sizing: border-box;
}
/* 设置多选框布局 */
.checkBox .el-checkbox-group > label, 
.checkbox-group-wrap .el-checkbox-group > label {
  margin-right: 6px;
}
.checkBox .el-checkbox-group > label:nth-child(2) {
  margin-right: 40px;
}
.el-checkbox-group > label:nth-child(3) {
  margin-right: 6px;
}
.el-checkbox-group > label:nth-child(9) {
  margin-right: 120px;
}
.el-checkbox-group > label:nth-child(10) {
  margin-right: 34px;
}
.el-checkbox-group > label:nth-child(11) {
  margin-right: 20px;
}
.el-checkbox-group .el-checkbox {
  height: 18px;
}
.el-checkbox:last-of-type {
  margin-bottom: 1px;
}
.middle-group .el-checkbox-group > label:not(:last-child) {
  width: 55%;
}

.el-tree {
  display: block;
  width: 100%;
  background: rgba(240, 242, 245, 1);
  padding-bottom: 8px;
  box-sizing: border-box;
}

.el-checkbox {
  /* width: 50%; */
  height: 22px;
  display: flex;
  margin-bottom: 8px;
  color: #000;
  font-size: 14px;
}
.grey {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  color: rgba(144, 147, 153, 1);
}
.mini40 {
  width: 40px;
}
.mini50 {
  width: 50px;
}
.mini30 {
  width: 30px;
  margin-right: 8px;
  vertical-align: top;
}
.mini160 {
  width: 160px;
  margin: 0 0 0 8px;
}
.footer {
  width: 100%;
  padding: 6px 12px;
  background: rgba(232, 243, 255, 1);
  box-shadow: -1px 1px 0px 0px rgba(200, 215, 230, 1);
  border-right: solid 1px #c8d7e6;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
}
.footer .el-button {
  padding: 3px 12px 5px 6px;
  height: 28px;
  font-size: 12px;
  box-sizing: border-box;
}
.footer .type1 {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  color: rgba(48, 49, 51, 1);
}
.footer .desc-line {
  white-space:pre-wrap;
  font-size: 14px;
  color: #000;
}
.footer .desc-line .box {
  display: inline-block;
  width: 26px;
  height: 26px;
  background-color: #F5F7FA;
  text-align: center;
}
.box span {
  font-size: 12px;
  line-height: 26px;
  /* vertical-align: middle; */
}
.footer .standard-title {
  font-size: 14px;
  font-family: NotoSansHans-Regular, NotoSansHans;
  color: rgba(102, 102, 102, 1);
}
.footer .standard-content {
  font-size: 14px;
  font-family: NotoSansHans-Regular, NotoSansHans;
  color: rgba(0, 0, 0, 1);
}

#right-container {
  width: calc(100% - 800px);
  height: 100vh;
  min-width: 504px;
  vertical-align: top;
  display: inline-block;
}
#right-container .header {
  width: 100%;
  height: 40px;
  padding: 6px 12px;
  background: rgba(232, 243, 255, 1);
  text-align: left;
  border-left: 1px solid rgba(200, 215, 230, 1);
  box-sizing: border-box;
}
.imgView {
  height: calc(100% - 40px);
  min-height: 556px;
  background: rgba(0, 0, 0, 1);
  box-shadow: 1px 0px 0px 0px rgba(200, 215, 230, 1);
  box-sizing: border-box;
}
.img {
  position: relative;
  width: 100%;
  min-height: 554px;
  height: calc(100% - 96px);
  display: flex;
  align-items: center;
  justify-content: center;
}
.position {
  position: absolute;
  top: 12px;
  left: 16px;
  font-size: 16px;
  color: white;
}
.imgList {
  width: 100%;
  height: 96px;
  background: black;
  display: flex;
  box-shadow: 1px -1px 0px 0px rgba(200, 215, 230, 1);
}
.divider {
  width: 100%;
  background: black;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 14px;
}
.item {
  margin-right: 8px;
}
.divider > div:last-child > div:first-child .el-divider__text {
  width: 60px;
}
.divider .el-divider--horizontal {
  margin: 0;
}
.divider .el-divider__text {
  background: black;
  color: white;
  font-size: 12px;
  padding: 0 6px;
}
.imgs {
  display: flex;
}
.imgs div:not(:last-child) {
  margin-right: 8px;
}
.img-item {
  margin-top: 10px;
  cursor: pointer;
}
.small-img {
  width: 48px;
  height: 48px;
}
.img-item span {
  display: block;
  font-size: 12px;
  text-align: center;
  color: rgba(144, 147, 153, 1);
}
.selected span {
  color: #36cdff !important;
  font-weight: 600;
}
.selected img {
  border: 2px solid rgba(54, 205, 255, 1);
  box-sizing: border-box;
}
/* 鼻腔弹窗 */
.pop2 {
  position: absolute;
}
/* 筛窦 */
.pop1 {
  position: absolute;
}
/* 额窦 */
.pop3 {
  position: absolute;
}
.popBtn .el-button {
  padding: 5px 15px;
  box-sizing: border-box;
}

/* 改造树形部分 */
.sel {
  margin: 0 10px 0 24px;
}
.sel .el-checkbox-group label {
  margin-right: 6px;
}
/* 鼻窦 */
.nasal .el-checkbox-group label:first-child {
  margin-right: 21px;
}
.nasal .el-checkbox-group label:nth-child(2) {
  margin-right: 20px;
}
/* 淋巴结转移 */
.lymph .sel > div > label {
  display: block !important;
  height: 24px;
}
.lymph .sel > div > label:first-child {
  width: 56px;
  margin-left: 24px;
}
.arrow {
  width: 16px;
  height: 16px;
  margin: 0 4px;
  cursor: pointer;
}
/* 颈部 */
.neck {
  margin: 0 0 0 48px;
}
.neck .el-checkbox-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 4px;
}
.neck .el-checkbox-group label {
  display: inline-block;
}
.neck .el-checkbox-group label:nth-child(4) {
  margin-right: 7px;
}
.neck .el-checkbox-group label:nth-child(5) {
  margin-right: 16px;
}
.bya {
  .hight-block {
    border: 1px solid #DCDFE6;
    background: #F5F7FA;
    display: flex;
    .bl-left {
      width: 80px;
      padding: 6px 8px;
    }
    .bl-right {
      flex: 1;
      border-left: 1px solid #DCDFE6;
      background: #EBEEF5;
      padding: 6px 8px;
      .el-radio {
        width: fit-content;
      }
      .flx-gp {
        display: flex;
        margin-top: 7px;
        margin-left: 5px;
        .el-checkbox {
          margin-bottom: 0;
        }
      }
    }
    .el-radio-group {
      width: 100%;
    }
    .el-radio {
      display: block;
      margin-right: 0;
      color: #000;
      height: 28px;
      line-height: 28px;
      width: 100%;
      position: relative;
      z-index: 1;
      &.active {
        &:before {
          content: '';
          position: absolute;
          left: -8px;
          right: -8px;
          top: 0;
          bottom: 0;
          z-index: -1;
          background: #E4E7ED;
        }
      }
    }
  }
}
</style>