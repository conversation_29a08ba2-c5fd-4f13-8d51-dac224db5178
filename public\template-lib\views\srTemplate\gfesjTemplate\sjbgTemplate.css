@charset "UTF-8";
#sjbg1 {
  font-size: 16px;
  background-color: #F5F7FA;
  min-height: 100%;
  color: #000;
}
#sjbg1 * {
  font-family: "宋体";
}
#sjbg1 table {
  width: 100%;
  background: #fff;
  border-color: #C0C4CC;
}
#sjbg1 table .layui-input, #sjbg1 table .layui-select, #sjbg1 table .layui-textarea {
  height: unset;
}
#sjbg1 table td, #sjbg1 table th {
  height: 36px;
}
#sjbg1 table thead th {
  text-align: center;
}
#sjbg1 table thead th:nth-child(3n+1) {
  text-align: left;
  padding-left: 12px;
  font-weight: bold;
}
#sjbg1 table tbody tr td {
  text-align: center;
}
#sjbg1 table tbody tr td:nth-child(3n+2) {
  padding: 4px 8px;
  box-sizing: border-box;
}
#sjbg1 table tbody tr td:nth-child(3n+1) {
  text-align: left;
  padding-left: 12px;
  box-sizing: border-box;
}
#sjbg1 .edit {
  padding: 8px 12px;
}
#sjbg1 .preview {
  display: none;
  flex-direction: column;
  width: 780px;
  min-height: 1100px;
  background-color: #fff;
  margin: 0 auto;
  padding: 40px 56px;
  word-break: break-all;
}
#sjbg1 .preview .preview-header .logo {
  width: 162px;
  text-align: right;
}
#sjbg1 .preview .preview-header .title {
  text-align: center;
}
#sjbg1 .preview .preview-header .code {
  width: 194px;
  text-align: right;
}
#sjbg1 .preview .preview-header .code img {
  width: 122px;
  height: 36px;
}
#sjbg1 .preview .preview-info {
  padding-top: 8px;
  padding-bottom: 11px;
  margin-top: 20px;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
}
#sjbg1 .preview .preview-content .preview-subcontent {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#sjbg1 .preview .preview-content .content-title {
  text-align: center;
  font-size: 20px;
}
#sjbg1 .preview .preview-footer .preview-report {
  display: flex;
  align-items: center;
  height: 48px;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
}
#sjbg1 .preview .preview-footer .preview-report img {
  height: 32px;
  width: 64px;
  object-fit: contain;
}
#sjbg1 .preview .preview-footer .preview-remark {
  margin-top: 4px;
  font-size: 12px;
}
#sjbg1 .preview .preview-footer .preview-remark div span {
  display: inline-block;
  transform: scale(0.92);
  transform-origin: left;
}
#sjbg1 .custom-select {
  position: relative;
}
#sjbg1 .custom-select::after {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 2%;
  content: "";
  width: 8px;
  height: 8px;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg) translateY(-100%);
}

[isView=true] #sjbg1 .edit {
  display: none;
}

[isView=true] #sjbg1 .preview {
  display: flex;
  flex-direction: column;
}

.rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  width: 100%;
  padding: 4px;
  resize: both;
}
.rt-textarea::placeholder {
  color: #000;
}

.rt-sr-label {
  margin-right: 8px;
}

.rt-sr-r {
  margin-right: 4px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-6 {
  margin-top: 6px;
}

.m-0-6 {
  margin: 0 6px;
}

.w-10 {
  width: 10px;
}

.w-15 {
  width: 15px;
}

.w-20 {
  width: 20px;
}

.w-25 {
  width: 25px;
}

.w-30 {
  width: 30px;
}

.w-35 {
  width: 35px;
}

.w-40 {
  width: 40px;
}

.w-45 {
  width: 45px;
}

.w-50 {
  width: 50px;
}

.w-55 {
  width: 55px;
}

.w-60 {
  width: 60px;
}

.w-65 {
  width: 65px;
}

.w-70 {
  width: 70px;
}

.w-75 {
  width: 75px;
}

.w-80 {
  width: 80px;
}

.w-85 {
  width: 85px;
}

.w-90 {
  width: 90px;
}

.w-95 {
  width: 95px;
}

.w-100 {
  width: 100px;
}

.w-105 {
  width: 106px;
}

.w-110 {
  width: 110px;
}

.w-115 {
  width: 115px;
}

.w-120 {
  width: 120px;
}

.w-125 {
  width: 125px;
}

.w-130 {
  width: 130px;
}

.w-135 {
  width: 135px;
}

.w-140 {
  width: 140px;
}

.f-1 {
  flex: 1;
}

.fw-600 {
  font-weight: 600;
}

.a-center {
  display: flex;
  align-items: center;
}

.a-start {
  display: flex;
  align-items: flex-start;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-r {
  text-align: right;
}
