* {
  margin:0;
  padding:0;
  box-sizing:border-box;
  font-family:Microsoft YaHei;
  /* font-family: Aria<PERSON>, "Helvetica Neue", Helvetica, sans-serif; */
  word-break: break-word !important;
}
html, body {
  width: 100%;
  height: 100%;
}
ul { list-style:none; }
.t-pg input, .t-pg select, .t-pg textarea { 
  outline:none; 
  min-height: 28px;
}
.t-pg input[type="radio"], .t-pg input[type="checkbox"] { 
  vertical-align: middle;
}
i {
  font-style: unset;
}
textarea {
  resize: none;
}
.rt-sr-t {
  border: 1px solid #DCDFE6;
  resize: none;
  padding: 4px 10px;
  border-radius: 3px;
  font-family: Microsoft YaHei;
  overflow: auto;
}
.t-pg { display:inline-block; width: 100%;min-height: 100%;}
.page {
  position:relative;
  text-align:left;
  font-size:14px;
  color: #303133;
  width: 100%;
}
.page + .page { margin-top:25px; }
/* 水平分布的弹性布局 */
.flex-h {
  display: flex;
}
/* 垂直分布的弹性布局 */
.flex-v {
  display: flex;
  flex-direction: column;
}
.p-item {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  width: 100%;
}
.p-item + .p-item {
  margin-top: 16px;
}
.p-item .p-item + .p-item {
  margin-top: 8px;
}
.w-con {
  /* display: flex;
  align-items: center; */
  display: inline-block;
  vertical-align: top;
}
.w-con label {
  display: inline-block;
}
.w-con label input {
  vertical-align: middle;
}
.w-con.v label {
  /* flex-direction: column;
  align-items: flex-start; */
  display: block;
}
.w-txt {
  display: inline-block;
  width: 80px;
  text-align: right;
}
.rt-sr-tit {
  width: 80px;
  text-align: right;
  padding-top: 4px;
}
.rt-sr-w .isk {
  color: #F56C6C;
  margin-right: 4px;
}
.r-i {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.w-con:not(.v) .r-i + .r-i {
  margin-left: 24px;
}
.r-i .rt-sr-lb {
  margin-left: 5px;
}
.p-lb {
  margin-right: 5px;
} 
.s-lb {
  margin-left: 5px;
}
select.rt-sr-s {
  min-width: 140px;
  border-color: #DCDFE6;
  border-radius: 3px;
}
.inp-number {
  position: relative;
  width: 120px;
  height: 28px;
  line-height: 28px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  overflow: hidden;
}
.inp-number_decrease, .inp-number_increase {
  position: absolute;
  z-index: 1;
  top: 0;
  width: 27px;
  bottom: 0;
  text-align: center;
  background: #F5F7FA;
  color: #606266;
  cursor: pointer;
  font-size: 14px;
}
.inp-number_decrease {
  left: 0;
  border-right: 1px solid #DCDFE6;
}
.inp-number_increase {
  right: 0;
  border-left: 1px solid #DCDFE6;
}
.inp-number_decrease.is-disabled, 
.inp-number_increase.is-disabled {
  color: #C0C4CC;
  cursor: not-allowed;
  pointer-events: none;
}
.inp-number .inp_inner {
  padding-left: 30px;
  padding-right: 30px;
  width: 100%;
  height: 100%;
  border: none;
  text-align: center;
  padding-top: 5px;
  font-family: Microsoft YaHei;
}
[isview='false'] input:checked + .rt-sr-lb {
  color: #1885F2;
}
/* 重置checkbox 和radio的样式 */
label[for].rt-cus-ckr{
  position: relative;
  padding-left: 15px;
}
.rt-cus-ckr input[type="checkbox"],.rt-cus-ckr input[type="radio"] {
  position: absolute;
  top: 0px;
  left: 0;
  width: 13px;
  height: 13px;
  opacity: 0;
  filter: Alpha(opacity=0);
}
.rt-cus-ckr input[type="checkbox"] + .rt-cus {
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  background: #fff;
  width: 13px;
  height: 13px;
  border: 1px solid #666;
  border-radius: 2px;
  box-sizing: border-box;
}
.rt-cus-ckr input[type="radio"] + .rt-cus {
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  width: 14px;
  height: 14px;
  background: url('./radio-un-icon.png');
  background-size: contain;
}
.rt-cus-ckr input[type="checkbox"].rt-disabled  + .rt-cus {
  background: #eee;
  border-color: #999;
}
.rt-cus-ckr input[type="radio"].rt-disabled  + .rt-cus {
  background: url('./radio-dis-icon.png');
}

.rt-cus-ckr input[type="checkbox"].rt-checked + .rt-cus {
  background: url('./check-icon.png');
  background-color: #1885F2;
  background-size: contain;
  position: absolute;
  border: 1px solid #1885F2;
  border-radius: 2px;
  box-sizing: border-box;
}
.rt-cus-ckr input[type="radio"].rt-checked + .rt-cus {
  background: url('./radio-icon.png');
  position: absolute;
  background-size: contain;
}
[isview="false"] .rt-cus-ckr input[type="checkbox"].rt-checked ~ .rt-sr-lb,
[isview="false"] .rt-cus-ckr input[type="checkbox"].rt-checked ~ span,
[isview="false"] .rt-cus-ckr input[type="radio"].rt-checked ~ .rt-sr-lb,
[isview="false"] .rt-cus-ckr input[type="radio"].rt-checked ~ span {
  color:#1885F2;
}

/* 块状区域样式 start*/
.w-block, .w-block-pd {
  flex: 1;
  display: flex;
  border: 1px solid #C8D7E6;
  background: #F5F7FA;
  min-height: 40px;
}
.w-block + .w-block, .w-block-pd + .w-block-pd {
  border-top: 0;
}
.w-block-pd {
  padding: 8px;
}
/* 浅色块 */
.w-block .light-block {
  padding: 8px;
}
.w-block .lb-row, .w-block .lb-high {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 0;
}
.w-block .lb-row + .lb-row, .w-block .lb-high + .lb-high {
  margin-top: 3px;
}
.w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::after,
.w-block .lb-row.lb-active.on > input::after {
  content: '';
  position: absolute;
  z-index: -1;
  left: -8px;
  right: -8px;
  top: 0;
  bottom: 0;
  background: #EBEEF5;
}
.w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::before,
.w-block .lb-row.lb-active.on > input::before {
  content: '';
  position: absolute;
  z-index: 0;
  left: -8px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #EBEEF5;
}
.w-block .lb-row.lb-active > input + .rt-sr-lb {
  flex: 1;
}
.w-block .lb-row.lb-active > input:checked + .rt-sr-lb {
  color: #1885F2;
}
.w-block .lb-row .lb-tip {
  position: absolute;
  right: 0;
  z-index: 0;
  color: #606266;
}
/* 深色块 */
.w-block .hight-block {
  padding: 8px;
  flex: 1;
  border-left: 1px solid #C8D7E6;
  background: #EBEEF5;
  color: #606266;
  display: none;
}

/* 块状区域样式-end */

.rt-sr-inv+.rt-sr-lb,
.rt-sr-inv {
  color:red!important;
  border-color:red!important;
}

/* tabs标签页 */
.rt-tabs {
  box-shadow: none;
  border: none;
  width: 100%;
}
.rt-tabs .el-tabs__nav-wrap {
  background: #F5F7FA;
  position: relative;
}
.rt-tabs .el-tabs__nav-wrap::after {
  content: '' !important;
  position: absolute;
  left: 2px;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #DCDFE6;
}
.rt-tabs .el-tabs__nav-next, .rt-tabs .el-tabs__nav-prev {
  line-height: 32px;
}
.rt-tabs .el-tabs__item {
  height: 32px;
  line-height: 32px;
  background: #fff;
  border: none !important;
  border-right: 1px solid #DCDFE6 !important;
  margin-left: 1px !important;
  border-bottom: 1px solid #DCDFE6 !important;
}
.rt-tabs .el-tabs__item.is-active {
  background: #F5F7FA !important;
  position: relative;
  border-bottom: none !important;
}
.rt-tabs .el-tabs__item .rt-sr-w {
  width: auto !important;
  padding-top: 0 !important;
}
.rt-tabs .el-tabs__nav-scroll {
  overflow-x: auto !important;
}
.rt-tabs .el-tabs__nav-wrap.is-scrollable {
  padding: 0 !important;
}
.rt-tabs .el-tabs__nav {
  transform: unset !important;
}
.rt-tabs .el-tabs__nav-prev, .rt-tabs .el-tabs__nav-next {
  display: none;
}
.rt-tabs.el-tabs--card>.el-tabs__header .el-tabs__nav {
  border: 0 !important;
}
.rt-tabs.el-tabs--card .el-tabs__item:not(.is-active) {
  color: #909399;
}
.rt-tabs.el-tabs--card>.el-tabs__header .pane-title .el-icon-close {
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: unset;
}
/* .rt-tabs .el-tabs__item.is-active::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: red;
} */
.rt-tabs .el-tabs__content {
  background: #F5F7FA;
}

/* 日期时间插件 */
.laydate-w {
  display: inline-block;
  min-width: 100px;
  font-size: 14px;
  border: 1px solid #DCDFE6;
  border-radius: 2px;
  position: relative;
}
.laydate-w .rt-sr-w {
  border: none;
  outline: none;
  display: block;
  width: 100%;
  height: 100%;
  padding: 5px 8px 5px 30px;
  box-sizing: border-box;
  height: 30px;
}
.laydate-w .layui-icon {
  position: absolute;
  left: 5px;
  top: 5px;
  font-size: 18px;
}
[isview='true'] .laydate-w {
  position: unset !important;
  font-size: 14px !important;
  border: none !important;
  top: 0 !important;
}
[isview='true'] .laydate-w .layui-icon {
  display: none !important;
}
[isview='true'] .laydate-w .rt-sr-w {
  padding-left: 0 !important;
}
.writeByHand {
  display: none;
}
.writeByHand * {
  pointer-events: none;
}
[isview='true'] .writeByHand {
  display: block;
}
/*--------打印table样式------------*/

.print,
.print table {
  width: 100%
}

.print table,
.print table tr,
.print table td,
.print table th {
  border: 1px solid #DCDFE6;
  text-align: center;
  line-height: 28px;
}
.print table th {
  font-weight: normal;
  color: #666;
}
.print table tbody {
  color: #303133;
}
/*---------------------颜色类-----------------------*/

.f-lightgray {
  color: #606266;
}

.f-black {
  color: #000;
}

.w-isk {
  color: #F56C6C;
  margin-right: 0;
  line-height: 28px;
}
/* 去掉layui时间插件的秒 */
.laydate-time-list:not(.show-seconds) > li:last-child {
  display: none;
}
.laydate-time-list:not(.show-seconds) > li {
  width: 50% !important;
}
.laydate-time-list:not(.show-seconds) > li > ol >li {
  padding-left: 54px !important;
}
input.rt-sr-t {overflow: hidden;}
@media print {
  body{
     -webkit-print-color-adjust:exact;
     -moz-print-color-adjust:exact;
     -ms-print-color-adjust:exact;
     print-color-adjust:exact;
 } 
}
.rt-sr-bcode div {
  height: 100% !important;
}
.rt-sr-bcode label {
  font-size: 14px !important;
  line-height: 14px !important;
}
.layui-menu li {
  min-height: 28px;
}
.layui-menu li.li-active {
  background: #f6f6f6;
}
.t-pg label:not(.lb-row):not(.lb-new) {
  width: fit-content;
}
*:disabled {
  cursor: not-allowed;
}
*::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

*::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 6px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ccc;
}

*::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  position: absolute;
  z-index: 8000;
  box-shadow: inset 0 0 5px #eee;
  background: #f2f2f2;
}

/* 病理pacs套打底单下样式，强制全局修改 --start */
#tdbc1 *, #tdbd1 *, #tdbq11 *, #tdbq21 *, #tdbq31 *, #tdbq41 *,
#tddd11 *, #tddd31 *, #tdddq1 *, #tddd1 *, #tddj1 *, #tdry11 *,
#tdry21 *, #tdsc1 *, #tdsqdbq1 *{
  font-size: 16px !important;
}
/* 标签样式强制全部修改行距 */
#tdsqdbq1 .pat-wrap > div:first-child{
  line-height: 15px !important;
}
#tdbq11 .pat-wrap > div:first-child, #tdbq21 .pat-wrap > div:first-child, 
#tdbq31 .pat-wrap > div:first-child, #tdbq41 .pat-wrap > div:first-child {
  line-height: 21px !important;
  font-size: 22px !important;
}
#tdbq11 .rt-sr-bcode, #tdbq21 .rt-sr-bcode, 
#tdbq31 .rt-sr-bcode, #tdbq41 .rt-sr-bcode, #tdsqdbq1 .rt-sr-bcode{
  vertical-align: bottom !important;
}
/* 病理pacs套打底单下样式，强制全局修改 --end */

/* 公共js函数 renderDropdown 下拉菜单样式  */
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}
mjx-container,mjx-container * {
  font-family: inherit !important;
  vertical-align: baseline;
}