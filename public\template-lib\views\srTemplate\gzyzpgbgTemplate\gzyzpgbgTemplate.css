.t-pg {
  background-color: #F5F7FA;
}
#gzyzpg1 {
  background-color: #F5F7FA;
}
#gzyzpg1 .view-wrap{display:none}
[isview='true'] #gzyzpg1 .edit-wrap{display:none}
[isview='true'] #gzyzpg1 .view-wrap{display:block}
#gzyzpg1 .edit-wrap {
  font-size: 16px;
  /* width: 904px; */
  margin: 0 auto;
  background-color: #F5F7FA;
  min-height: 100%;
  padding: 12px 24px;
}
.wrap_item {
  width: 100%;
  background: #FAFAFA;
  border: 1px solid #C0C4CC;
  padding: 8px;
  margin-bottom: 12px;
  font-family: '宋体' !important;
}
.wrap_item_t {
  height: 16px;
  line-height: 16px;
  border-left: 4px solid #1885F2;
  font-size: 16px;
  padding-left: 8px;
  font-weight: 600;
}
.wrap_item_c {
  padding-top: 15px;
}
textarea {
  width: 100%;
  border: 1px solid #C0C4CC;
}
.wrap_item_c label{
  /* float: left; */
  margin-right: 20px;
}
.t-pg label input {
  min-height: auto;
}
#gzyzpg1 .lbj-item {
  /* padding-right: 10px; */
}
#gzyzpg1 .lbj-item span {
  line-height: 28px;
}
#gzyzpg1 .lbj-item input {
  height: 28px;
}
.wrap_table {
  border: solid 1px #C0C4CC;
  background-color: #fff;
  border-bottom: none;
}
.wrap_table_tr {
  height: 40px;
  line-height: 40px;
  display: flex;
  border-bottom: solid 1px #C0C4CC;
}
.wrap_table_tr:nth-child(2n) {
  background: #ECF5FF;
}
.tr_bg {
  background: #ECF5FF;
}
.td_l {
  width: 200px;
  padding-left: 12px;
  box-sizing: border-box;
  border-right: solid 1px #C0C4CC;
}
.td_r {
  width: calc(100% - 200px);
  padding-left: 12px;
  box-sizing: border-box;
}
.pl_12 {
  padding-left: 12px;
  /* box-sizing: border-box; */
}
.pr_12 {
  padding-right: 12px;
}
.fw6 {
  font-weight: 600;
}

.whiteSpace{
  white-space: pre-wrap;
  word-break: break-all;
}
.rt-sr-body {
  padding-top: 4px;
}
.rt-sr-body .mb-4 {
  display: inline-block;
  margin-bottom: 8px !important;
}
.rt-sr-footer {
  width: calc(100% - 112px);
  position: absolute;
  bottom: 20px;
  left: 56px;
}













.header-title {
  font-size: 20px;
  color: #303133;
}
.bt {
  border-top: solid 1px #C0C4CC;
}
.xbt, .text_t_l {
  font-size: 16px;
  color: #303133;
}
.text_t_l {
  display: inline-block;
  min-width: 80px;
  text-align: right;
}
.bn {
  border: none;
}
.ta-c{
  text-align: center;
}
.item-flex {
  display: flex;
}
.item_bd {
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  height: 36px;
  line-height: 34px;
  box-sizing: border-box;
  padding-left: 12px;
}
.item_bd_s {
  width: calc(100% - 117px);
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  box-sizing: border-box;
  padding-left: 12px;
  line-height: 1.2;
  height: 28px;
}
.box {
  background-color: #fff;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  height: 86px;
  padding: 0 8px;
}
.mb-4 {
  margin-bottom: 4px !important;
}
#gzyzpg1 .mb-8 {
  margin-bottom: 8px !important;
}
.mb-8 input , .item-fl input{
  height: 28px;
}
#gzyzpg1 .mb-12 {
  margin-bottom: 12px !important;
}
.mb-12 input {
  height: 28px;
}
.dib{
  display: inline-block;
  position: relative;
}
#gzyzpg1 .dib::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 10px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#gzyzpg1 .dib .layui-input {
  padding-right: 20px;
}
.w {
  width: 100%;
}
.w30 {
  width: 30px;
}
.w60 {
  width: 60px;
}
.w80 {
  width: 80px;
}
.w110 {
  width: 110px;
}
.w112 {
  width: 112px;
}
.w126 {
  width: 126px;
}
.w130{
  width: 130px;
}
.w180 {
  width: 180px;
}
.w200 {
  width: 200px;
}
.w300 {
  width: 300px;
}
.w320 {
  width: 320px;
}

#gzyzpg1 .flex-sty {
  overflow: hidden;
}


#gzyzpg1 .myzh-table {
  width: 100%;
  border: 1px solid #DCDFE6;
  border-collapse:collapse;
  vertical-align: top;
  background: #fff;
  margin: 5px 0;
}
#gzyzpg1 .tb-th th {
  height: 36px;
  padding: 7px 12px;
  text-align: left;
}
#gzyzpg1 tbody .td-lab {
  display: inline-block;
  width: 90px;
}
#gzyzpg1  tbody td{
  height: 40px;
  padding: 4px 12px;
}
#gzyzpg1 .view-wrap * {
  font-family: "宋体";
}
.view-wrap {
  width: 780px;
  min-height: 1100px;
  margin: 0 auto;
  background: #FFFFFF;
  padding: 20px 56px 115px 56px;
  box-sizing: border-box;
  position: relative;
}
.view-wrap .title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  text-align: center;
}
.v_hzxx {
  padding: 8px 0 4px 0;
  border-top: solid 1px #999999;
  border-bottom: solid 1px #999999;
}
.p-sx5{
  padding: 5px 0;
}
.p-sx8{
  padding: 8px 0;
}
.p-sx14{
  padding: 14px 0;
}
.item_t {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}
.tr{
  text-align: right;
}
.tc {
  text-align: center;
}

.rt_zt_l {
  font-size: 14px;
  color: #000; 
}
.rt_zt_r {
  font-size: 14px; 
  color: #000000;
}
.view-patient .rt_zt_l,.view-patient .rt_zt_r {
  line-height: 20px;
}
.bb{
  border-bottom: solid 1px #999999;
}
.btt{
  border-top: solid 1px #999999;
}
#gzyzpg1 .btt > div{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
}
#gzyzpg1 .btt > div + div {
  margin-left: 4px;
}
#gzyzpg1 .btt  > div .rt_zt_l {
  width: 70px;
}
#gzyzpg1 .btt  > div:nth-child(3) .rt_zt_l {
  width: 42px;
}
#gzyzpg1 .btt  > div .rt_zt_r {
  flex: 1;
}
.ib-p {
  display: inline-block;
}
.ib-p .rt_zt_r {
  margin-right: 10px;
}
.view-flex{
  display: flex;
}
.flex_-8{
  flex: 0.8;
} 
.flex_1 {
  flex: 1;
}
.flex_1-5 {
  flex: 1.5;
}
.flex_1-8{
  flex: 1.8;
} 
.flex_2 {
  flex: 2;
}
.flex_2-3 {
  flex: 2.3;
}
.flex_2-5 {
  flex: 2.5;
}
.flex_2-8 {
  flex: 2.8;
}
.flex_3 {
  flex: 3;
}
.flex_3-5 {
  flex: 3.5;
}
.flex_4 {
  flex: 4;
}
.flex_4-5 {
  flex: 4.5;
}
.flex_5 {
  flex: 5;
}
.flex_5-5 {
  flex: 5.5;
}
.flex_6 {
  flex: 6;
}
.view_img_box {
  display: none;
  overflow: hidden;
}
.view_img {
  float: left;
  width: 50%;
  margin-bottom: 8px;
}
.view_img img {
  display: block;
  margin: 0 auto;
  width: 265px;
  height: 200px;
}
.clac86 {
  width: calc(100% - 86px);
}
.item-fl {
  overflow: hidden;
}
.item-flex-1 {
  float: left;
  min-width: 400px;
  margin-bottom: 4px;
}
.item-flex-2 {
  float: left;
  width: 50%;
  min-width: 400px;
  margin-bottom: 4px;
}
.item-fl .item-flex-3{
  float: left;
  width: 33.333%;
  min-width: 400px;
  margin-bottom: 4px;
}
.view-wrap .flex_2-5 img {
  width: 100px;
  height: 32px;
  object-fit: contain;
}
.rt-sr-footer {
  line-height: 32px;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #gzyzpg1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #gzyzpg1 .view-wrap {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #gzyzpg1 .view-wrap .view-head,
[entry-type="5"] #gzyzpg1 .view-wrap .view-patient,
[entry-type="5"] #gzyzpg1 .view-wrap .tip-wrap {
  display: none;
}
/* 去掉边框，除表格外 */
[entry-type="5"] #gzyzpg1 .view-wrap div {
  border-bottom: none;
  border-top: none;
}
.blue-lb {
  margin-top: 5px;
  font-size: 16px;
  font-weight: 500;
  color: #1885F2;
  text-decoration:underline;
  cursor: pointer;
}

/* 弹框字体 */
.diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
.diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
.diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
.diag-text-size .text-size .on {
  display: none;
}

.rt-dialog-con .edit-wrap.default textarea{
  font-size: 16px;
}
.rt-dialog-con .edit-wrap.large textarea{
  font-size: 18px;
}
.rt-dialog-con .edit-wrap.larger textarea{
  font-size: 20px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}