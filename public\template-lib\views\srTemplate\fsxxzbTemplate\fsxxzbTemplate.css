#fsxxzb1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}

#fsxxzb1 .fsxzb-content {
  min-height: 100%;
  padding: 8px 12px;
  padding-bottom: 0;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}

#fsxxzb1 .c-item {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

#fsxxzb1 .f-left {
  padding: 8px 0;
  border-right: 1px solid #C8D7E6;
  background: #EBEEF5;
}
#fsxxzb1 .f-left .l-child{
  padding:2px 8px;
}
#fsxxzb1 .f-right {
  padding: 12px;
}
#fsxxzb1 .f-right  .r-child {
  display: flex;
  align-items: center;
}
#fsxxzb1 .f-right  .r-child+.r-child {
  margin-top: 8px;
}
#fsxxzb1 .bro-w {
  display: flex;
  align-items: center;
  border: 1px solid #C8D7E6;
  padding: 4px 10px;
  background: #EBEEF5;
}
#fsxxzb1 .to-cd{
  cursor: pointer;
}
#fsxxzb1 .block-ck3:hover{
  background-color: #E4E7ED;
}
#fsxxzb1 input[type="text"] {
  border: 1px solid #DCDFE6;
  width: 60px;
  padding-left: 6px;
  margin: 0 6px;
}

#fsxxzb1 input[type="radio"],#fsxxzb1 input[type="checkbox"] {
  vertical-align: middle;
}

#fsxxzb1 label+label {
  margin-left: 30px;
}

#fsxxzb1 .c-item+.c-item {
  margin-top: 12px;
}
#fsxxzb1 .ml-8 {
  margin-left: 8px;
}
#fsxxzb1 .ml-10 {
  margin-left: 20px;
}
#fsxxzb1 .ml-20 {
  margin-left: 20px;
}
#fsxxzb1 .ml-30 {
  margin-left: 30px;
}
#fsxxzb1 .ml-48 {
  margin-left: 48px;
}
#fsxxzb1 .ml-50 {
  margin-left: 50px;
}