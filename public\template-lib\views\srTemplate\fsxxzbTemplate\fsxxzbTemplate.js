$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);

  initShow();

  $('#zdmb-c').click(function () {
    mbClick('zdmb')
  })
  $('#ejb-c').click(function () {
    mbClick('ejb')
  })
  $('#sjb-c').click(function () {
    mbClick('sjb')
  })
  $('#fdmb-c').click(function () {
    mbClick('fdmb')
  })

  $('#fsxzb-rt-1').click(function () {
    mbClick('zdmb', '1');
  })
  $('#fsxzb-rt-2').click(function () {
    mbClick('ejb', '2')
  })
  $('#fsxzb-rt-3').click(function () {
    mbClick('sjb', '3')
  })
  $('#fsxzb-rt-4').click(function () {
    mbClick('fdmb', '4')
  })


  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }

  keydown_to_tab('fsxxzb1');

  // radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });

  // 主动脉xxxx
  $('.block-ck3').click(function (e) {
    e.preventDefault();
  })
  $('.ck-inp3').click(function (e) {
    e.stopPropagation();
  })
  $('#ejbkzsh').click(function () {
    var isChecked = $('#fsxzb-rt-227').prop("checked");
    $('#ejbqn').attr('style', 'display:' + (isChecked ? 'unset;' : 'none;'))
  })

  $('#mvrshrg').click(function () {
    shRadioClick('mvr');
  })
  $('#avrshrg').click(function () {
    shRadioClick('avr');
  })
  $('#dvrshrg-p').click(function () {
    shRadioClick('dvr');
  })
  $('#tvrshrg').click(function () {
    shRadioClick('tvr');
  })

  $('#dvr-a').click(function () {
    dvrRadioClick();
  })
  $('#dvr-b').click(function () {
    dvrRadioClick();
  })
  $('#dvr-c').click(function () {
    dvrRadioClick();
  })

  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}

function mbClick(name, cId) {
  if (cId) {
    let mbCheck = $('#fsxzb-rt-' + cId).prop("checked");
    if (mbCheck) {
      $('#zdmb-c').parent().attr('style', 'background:' + (name === 'zdmb' && cId === '1' ? '#E4E7ED;' : 'unset;'))
      $('#ejb-c').parent().attr('style', 'background:' + (name === 'ejb' && cId === '2' ? '#E4E7ED;' : 'unset;'))
      $('#sjb-c').parent().attr('style', 'background:' + (name === 'sjb' && cId === '3' ? '#E4E7ED;' : 'unset;'))
      $('#fdmb-c').parent().attr('style', 'background:' + (name === 'fdmb' && cId === '4' ? '#E4E7ED;' : 'unset;'))
      $('#zdmb').attr('style', 'display:' + (name === 'zdmb' && cId === '1' ? 'unset;' : 'none;'));
      $('#ejb').attr('style', 'display:' + (name === 'ejb' && cId === '2' ? 'unset;' : 'none;'));
      $('#sjb').attr('style', 'display:' + (name === 'sjb' && cId === '3' ? 'unset;' : 'none;'));
      $('#fdmb').attr('style', 'display:' + (name === 'fdmb' && cId === '4' ? 'unset;' : 'none;'));
    }
  } else {
    $('#zdmb-c').parent().attr('style', 'background:' + (name === 'zdmb' ? '#E4E7ED;' : 'unset;'))
    $('#ejb-c').parent().attr('style', 'background:' + (name === 'ejb' ? '#E4E7ED;' : 'unset;'))
    $('#sjb-c').parent().attr('style', 'background:' + (name === 'sjb' ? '#E4E7ED;' : 'unset;'))
    $('#fdmb-c').parent().attr('style', 'background:' + (name === 'fdmb' ? '#E4E7ED;' : 'unset;'))
    $('#zdmb').attr('style', 'display:' + (name === 'zdmb' ? 'unset;' : 'none;'));
    $('#ejb').attr('style', 'display:' + (name === 'ejb' ? 'unset;' : 'none;'));
    $('#sjb').attr('style', 'display:' + (name === 'sjb' ? 'unset;' : 'none;'));
    $('#fdmb').attr('style', 'display:' + (name === 'fdmb' ? 'unset;' : 'none;'));
  }

}

function shRadioClick(rname) {
  let mvrChecked = $('#fsxzb-rt-86').prop("checked");
  let avrChecked = $('#fsxzb-rt-87').prop("checked");
  let dvrChecked = $('#fsxzb-rt-88').prop("checked");
  let tvrChecked = $('#fsxzb-rt-89').prop("checked");

  $('#mvrsh-ejb').attr('style', 'display:' + (mvrChecked ? 'unset;' : 'none;'));
  $('#avrsh-zdmb').attr('style', 'display:' + (avrChecked ? 'unset;' : 'none;'));
  $('#dvrshrg').attr('style', 'display:' + (dvrChecked ? 'unset;' : 'none;'));
  $('#dvrsh-l').attr('style', 'display:' + (dvrChecked ? 'unset;' : 'none;'));
  $('#tvrsh-sjb').attr('style', 'display:' + (tvrChecked ? 'unset;' : 'none;'));
  if (!dvrChecked) {
    $('#zdm-ejb').attr('style', 'display:' + (dvrChecked ? 'unset;' : 'none;'));
    $('#dvrzdm').attr('style', 'display:' + (dvrChecked ? 'unset;' : 'none;'));
    $('#ejbChild').attr('style', 'display:' + (dvrChecked ? 'unset;' : 'none;'));
  }
}

function dvrRadioClick(params) {
  let aChecked = $('#fsxzb-rt-136').prop("checked");
  let bChecked = $('#fsxzb-rt-137').prop("checked");
  let cChecked = $('#fsxzb-rt-138').prop("checked");
  $('#zdm-ejb').attr('style', 'display:' + (aChecked ? 'unset;' : 'none;'));
  $('#dvrzdm').attr('style', 'display:' + (bChecked ? 'unset;' : 'none;'));
  $('#ejbChild').attr('style', 'display:' + (cChecked ? 'unset;' : 'none;'));
}

function initShow() {
  let zdmbCheck = $('#fsxzb-rt-1').prop("checked");
  let rjbCheck = $('#fsxzb-rt-2').prop("checked");
  let sjbCheck = $('#fsxzb-rt-3').prop("checked");
  let fdmbCheck = $('#fsxzb-rt-4').prop("checked");
  if (zdmbCheck) {
    $('#zdmb-c').parent().attr('style', 'background:#E4E7ED');
    $('#zdmb').attr('style', 'display:unset');
  } else if (rjbCheck) {
    $('#ejb-c').parent().attr('style', 'background:#E4E7ED');
    $('#ejb').attr('style', 'display:unset');
  } else if (sjbCheck) {
    $('#sjb-c').parent().attr('style', 'background:#E4E7ED');
    $('#sjb').attr('style', 'display:unset');
  } else if (fdmbCheck) {
    $('#fdmb-c').parent().attr('style', 'background:#E4E7ED');
    $('#fdmb').attr('style', 'display:unset');
  }

  shRadioClick()
  dvrRadioClick()
  var isChecked = $('#fsxzb-rt-227').prop("checked");
  $('#ejbqn').attr('style', 'display:' + (isChecked ? 'unset;' : 'none;'))
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let strList = [];
  let xzIdList = ['fsxzb-rt-5', 'fsxzb-rt-25', 'fsxzb-rt-45', 'fsxzb-rt-65', 'fsxzb-rt-94', 'fsxzb-rt-117', 'fsxzb-rt-141', 'fsxzb-rt-162', 'fsxzb-rt-185', 'fsxzb-rt-208', 'fsxzb-rt-232'];
  let gbIdList = ['fsxzb-rt-12', 'fsxzb-rt-32', 'fsxzb-rt-52', 'fsxzb-rt-72', 'fsxzb-rt-100', 'fsxzb-rt-123', 'fsxzb-rt-147', 'fsxzb-rt-168', 'fsxzb-rt-191', 'fsxzb-rt-214', 'fsxzb-rt-238'];
  let zfIdList = ['fsxzb-rt-21', 'fsxzb-rt-41', 'fsxzb-rt-61', 'fsxzb-rt-81', 'fsxzb-rt-109', 'fsxzb-rt-132', 'fsxzb-rt-156', 'fsxzb-rt-177', 'fsxzb-rt-200', 'fsxzb-rt-223', 'fsxzb-rt-247'];
  let funIdList = ['fsxzb-rt-90', 'fsxzb-rt-91', 'fsxzb-rt-113', 'fsxzb-rt-114', 'fsxzb-rt-139', 'fsxzb-rt-140', 'fsxzb-rt-160', 'fsxzb-rt-161', 'fsxzb-rt-181', 'fsxzb-rt-182', 'fsxzb-rt-204', 'fsxzb-rt-205', 'fsxzb-rt-228', 'fsxzb-rt-229'];
  let avaIdList = ['fsxzb-rt-92', 'fsxzb-rt-115', 'fsxzb-rt-183', 'fsxzb-rt-206'];
  let mvaIdList = ['fsxzb-rt-93', 'fsxzb-rt-116', 'fsxzb-rt-184', 'fsxzb-rt-207', 'fsxzb-rt-231'];

  $('#xb .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      strList.push(curPid.val)
      let child = curPid.child;
      if (child && child.length) {
        for (var i = 0; i < child.length; i++) {
          if (xzIdList.indexOf(child[i].id) !== -1) {
            let xchild = child[i].child;
            if (xchild && xchild.length) {
              strList.push(child[i].val + xchild[0].val)
            }
          }
          if (gbIdList.indexOf(child[i].id) !== -1) {
            let gchild = child[i].child;
            if (gchild && gchild.length) {
              if (gchild.length === 1) {
                strList.push(child[i].val + '，' + gchild[0].val)
              } else {
                strList.push(child[i].val + '，' + gchild[0].val + '，' + gchild[1].val)
              }
            }
          }
          if (zfIdList.indexOf(child[i].id) !== -1) {
            let zchild = child[i].child;
            if (zchild && zchild.length) {
              strList.push(child[i].val + zchild[0].val + '血栓形成')
            }
          }
        }
      }
    }
  })

  $('#sh .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      strList.push(child[0].val);
      if (child && child.length) {
        let schild = child[0].id !== 'fsxzb-rt-88' ? child[0].child : child[0].child[0].child;
        if (schild && schild.length) {
          if (child[0].id === 'fsxzb-rt-88') {
            strList.push(child[0].child[0].val);
          }
          for (var s = 0; s < schild.length; s++) {
            if (funIdList.indexOf(schild[s].id) !== -1) {
              strList.push(schild[s].val)
            }
            if (avaIdList.indexOf(schild[s].id) !== -1) {
              strList.push('AVA为' + schild[s].val + 'cm^2')
            }
            if (mvaIdList.indexOf(schild[s].id) !== -1) {
              strList.push('MVA为' + schild[s].val + 'cm^2')
            }
            if (xzIdList.indexOf(schild[s].id) !== -1) {
              let xchild = schild[s].child;
              if (xchild && xchild.length) {
                strList.push(schild[s].val + xchild[0].val)
              }
            }
            if (gbIdList.indexOf(schild[s].id) !== -1) {
              let gchild = schild[s].child;
              if (gchild && gchild.length) {
                if (gchild.length === 1) {
                  strList.push(schild[s].val + '，' + gchild[0].val)
                } else {
                  strList.push(schild[s].val + '，' + gchild[0].val + '，' + gchild[1].val)
                }
              }
            }
            if (zfIdList.indexOf(schild[s].id) !== -1) {
              let zchild = schild[s].child;
              if (zchild && zchild.length) {
                strList.push(schild[s].val + zchild[0].val + '血栓形成')
              }
            }
          }
        }
      }
    }
  })

  $('#qnkz .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length) {
        for (var i = 0; i < child.length; i++) {
          if (funIdList.indexOf(child[i].id) !== -1) {
            strList.push(child[i].val)
          }
          if (mvaIdList.indexOf(child[i].id) !== -1) {
            strList.push('MVA为' + child[i].val + 'cm^2')
          }
          if (xzIdList.indexOf(child[i].id) !== -1) {
            let xchild = child[i].child;
            if (xchild && xchild.length) {
              strList.push(child[i].val + xchild[0].val)
            }
          }
          if (gbIdList.indexOf(child[i].id) !== -1) {
            let gchild = child[i].child;
            if (gchild && gchild.length) {
              if (gchild.length === 1) {
                strList.push(child[i].val + '，' + gchild[0].val)
              } else {
                strList.push(child[i].val + '，' + gchild[0].val + '，' + gchild[1].val)
              }
            }
          }
          if (zfIdList.indexOf(child[i].id) !== -1) {
            let zchild = child[i].child;
            if (zchild && zchild.length) {
              strList.push(child[i].val + zchild[0].val + '血栓形成')
            }
          }
        }
      }
    }
  })
  rtStructure.impression = '风湿性心脏病，' + strList.join('，')
}