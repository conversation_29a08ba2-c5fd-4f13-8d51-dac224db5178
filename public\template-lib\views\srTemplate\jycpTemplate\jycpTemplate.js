/**
 * 备注
 */
var remarkList = [
  {
    id: '1',
    title: `
2、检验方法的局限性：
（1）本试剂盒采用了BIOMED-2标准化引物组，免疫球蛋白（IG） 基因重排检测试剂盒（毛细管电泳法）获得NMPA批准，T细胞受体（TCR）基因重排检测结果仅供临床参考；
（2）IG/TCR基因重排并不一定局限于B/T细胞谱系，在非霍奇金淋巴瘤中克隆性重排也存在阴性可能，检测结果应结合临床、组织学及免疫表型等数据综合判断；
（3）肿瘤/异型细胞占比至少达到20%，肿瘤/异型细胞过低会影响检测灵敏度，存在假阴性可能。
3、本报告结果只对该检测标本负责，如有异议请在三日内提出。`,
  },
  {
    id: '2',
    title: `
2、检验方法的局限性：
（1）本试剂盒采用了BIOMED-2标准化引物组，免疫球蛋白（IG） 基因重排检测试剂盒（毛细管电泳法）获得NMPA批准，T细胞受体（TCR）基因重排检测结果仅供临床参考；
（2）IG/TCR基因重排并不一定局限于B/T细胞谱系，在非霍奇金淋巴瘤中克隆性重排也存在阴性可能，检测结果应结合临床、组织学及免疫表型等数据综合判断；
（3）肿瘤/异型细胞占比至少达到20%，肿瘤/异型细胞过低会影响检测灵敏度，存在假阴性可能。
3、本例病例内参基因仅见100bp、200bp和300bp产物，未见395bp产物，表明该样本DNA存在片段化的可能，不除外大于300bp的目的基因片段的假阴性情况；
4、本报告结果只对该检测标本负责，如有异议请在三日内提出。`,
  },
];
var particularly = [
  {
    id: '1',
    title: '凑样上机',
  },
  {
    id: '2',
    title: '结果有疑问，重复实验',
  },
  {
    id: '3',
    title: '漏执行',
  },
  {
    id: '4',
    title: '全流程质控',
  },
  {
    id: '5',
    title: '不同方法学验证',
  },
  {
    id: '6',
    title: '复检',
  },
];
var tableRes = [
  {
    id: '1',
    title: '阴性',
  },
  {
    id: '2',
    title: '阳性',
  },
];
var checkFragment =`1、检测片段：
（1）免疫球蛋白重链基因（IGH）VH-JH基因区放大（5次PCR，扩增片段VHFR1-JH长约310-360bp；VHFR2-JH长约250-295bp；VHFR3-JH长约100-170bp）；DH-JH （VHDH-JH长约110-290bp、390-420 bp；DH7-JH长约100-130bp）。
（2）免疫球蛋白轻链基因（IGK）Vκ-Jκ、Vκ-Kde/IntronRSS-Kde基因区放大（2次PCR，扩增片段IGK-A长约120-160bp、190-210bp、260-300bp；IGK-B长约210-250bp、270-300bp、350-390bp）。
（3）免疫球蛋白轻链基因（IGL）Vλ-Jλ基因区放大（1次PCR，扩增片段长约140-165bp）。
（4）T细胞受体β（TCRB）Vβ-Jβ和Dβ-Jβ基因区放大（3次PCR，扩增片段TCRB-A，Vβ-Jβ长约240-285 bp；TCRB-B，Vβ-Jβ长约240-285 bp；TCRB-C，Dβ-Jβ长约170-210bp、285-325 bp、160-220bp）。
（5）T细胞受体γ（TCRG）Vγ-Jγ基因区放大（2次PCR，扩增片段TCRG-A长约145-255bp；TCRG-B长约80-140 bp、160-220bp）。
（6）T细胞受体δ（TCRD）Vδ-Jδ基因区放大（1次PCR，扩增片段TCRG长约120-280bp）。`
$(function() {
  window.initHtmlScript = initHtmlScript;
});
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {}; //公共报告属性内容
var idAndDomMap = {}; //节点id和值对应的关系
var enterOptions = {}; //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过
var defaultValueList = []; //通过配置的默认值
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList);
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    isSavedReport =
      rtStructure && rtStructure.enterOptions
        ? rtStructure.enterOptions.isSavedReport
        : false;
    rtStructure.diffConfig = {
      nextWidgetEnable: false, //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false, //true确认报告时不上传pdf
      checkboxSort: false, //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#jycp1 .pv-warp'), //转成pdf的区域，默认是整个页面
      confirmTipIds: ['#jycp-rt-55'],
    };
  }
  if (rtStructure) {
    enterOptions = rtStructure.enterOptions || {};
    rptImageList = rtStructure.enterOptions
      ? rtStructure.enterOptions.rptImageList || []
      : []; //报告图像
    publicInfo = rtStructure.enterOptions
      ? rtStructure.enterOptions.publicInfo || {}
      : {}; //检查信息
    idAndDomMap =
      rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      resetLogoStyle();
      getCloudFilmQrCodeUrl(publicInfo.examNo, $('.jycp-pv .view-head'));
      initViewCon();
      replaceViewPatternByValue(
        $('[data-key="inpatientNo"]').siblings('.h-label'),
        $('[data-key="inpatientNo"]'),
        publicInfo
      );
    } else {
      initPage(publicInfo);
    }
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
/**
 * 1、编辑页面增加3个字段，样式见需求备注6
（1）检测流水号【为输入框】：输入框加上属性  autocomplete="on"，存在字段deviceRelateNo
（2）项数【为输入框】：存在字段itemCounts，每个模板的默认值不同，具体见需求第2点说明
（3）特殊备注【为可编辑的下拉框，选项见需求备注6】：存在字段specialMemo
2、找到initHtmlScript下的rtStructure.diffConfig，加入confirmTipIds，类型为数组，传入检测流水号的id选择器，如下
rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#pacsPole1 .pacsPole-view'),  //转成pdf的区域，默认是整个页面
      confirmTipIds: [],  //用于二次确认的控件选择器，如['#demo-rt-1', '.test']
    }
 * */
// 获取分子病理原检查医嘱开单信息
function getSampleOrganList2() {
  var markInfo = {};
  var params = {
    examNo: publicInfo.examNo,
    examClass: publicInfo.examClass,
    markerLike: '基因重排'
  }
  fetchAjax({
    url: api.getOrgMarkerInfo,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0' && res.result && res.result.length) {
        markInfo = res.result[0];
      }
    },
  })
  return markInfo;
}

// 未填写过报告的，根据标记物匹配检查项目
function findExamItemByMark() {
  // 填写过报告的忽略
  if(isSavedReport) {
    return;
  }
  var markInfo = getSampleOrganList2();
  var candleId = markInfo.candleId || '';  //蜡块号
  // 蜡块号每次都更新（req16416，改成修改过按修改的为准）
  if(candleId && !isSavedReport && rtStructure.enterOptions.type !=='view') {
    curElem.find('#jycp-rt-58').val(candleId);
  }
}
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '', //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '', //报告日期
    examTechnician: '', //检查技师
    priReporter: '', //初步报告医生
    priReporterNo: '', //初步报告医生流水号
    affirmReporter: '', //审核医生
    affirmReporterNo: '', //审核医生流水号
    affirmDate: '', //审核日期
    reDiagReporter: '', //复诊报告医生
    reDiagReporterNo: '', //复诊报告医生流水号，多个逗号隔开
    rptImageList: rptImageList, //报告图像
    deviceRelateNo: document.querySelector('#jycp-rt-55')
      ? document.querySelector('#jycp-rt-55').value
      : '', //检测流水号
    itemCounts: document.getElementById('jycp-rt-56')
      ? document.getElementById('jycp-rt-56').value
      : '', //项数
    specialMemo: document.getElementById('jycp-rt-57')
      ? document.getElementById('jycp-rt-57').value
      : '', //特殊备注
  };
  let curKeyValData =
    rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  $('#jycp1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
    }
  });
  // console.log(rtStructure);
}

function getImpression() {
  let result = '';
  let arr = [];
  $('.page-edit .i-label').each((index, item) => {
    let obj = {
      label: $(item)
        .text()
        .replace(/(\\n|\s+)/, ''),
      value: $(item)
        .next()
        .val(),
    };
    if (obj.label == '') {
      arr[index - 1].value = $(item)
        .next()
        .val();
    }
    arr.push(obj);
  });
  let dna = ['DNA浓度=', 'ng/μl，OD260/280=', '，OD260/230='];
  let dnaTmp = '';
  arr = arr.filter((item) => item.label != '');
  arr.forEach((item) => {
    if (item.label.includes('标本类型')) {
      item.value = $('#jycp-rt-6').val();
    }
    if (item.label.includes('实验结果')) {
      let text = '';
      $('#result>tbody>tr>td').each((index, item) => {
        if (index % 2 === 0) {
          text += $(item).text();
        } else {
          text +=
            $(item)
              .find('input')
              .val() + '；';
        }
      });
      item.value = text;
    }
    if (dna.includes(item.label)) {
      dnaTmp += item.label + item.value;
      item.value = '';
    }
  });
  arr.forEach((item) => {
    if (item.label.includes('DNA质控')) {
      item.value = dnaTmp;
    }
  });
  arr = arr.filter((item) => item.value != '');
  arr.forEach((item) => {
    if (item.label.includes('检测流水号') | item.label.includes('项数')) {
    } else {
      result += item.label + item.value + '\n';
    }
  });
  return result;
}
/**
 * 预览
 * */
function initViewCon() {
  var displayView = publicInfo.entryType === '5'; //显示模板
  if (displayView) {
    publicInfo['affirmDate'] =
      publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] =
      publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.jycp-pv [data-key]').each(function() {
    var key = $(this).attr('data-key');
    if (key === 'inPatNo') {
      key = 'inpatientNo';
    }
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    if (key == 'jycp-rt-49') {
      idAnVal = idAnVal.replace(
        /(IGH基因可见克隆性重排|IGK基因可见克隆性重排|IGL基因可见克隆性重排|TCRB基因可见克隆性重排|TCRG基因可见克隆性重排|TCRD基因可见克隆性重排)/g,
        (searchValue) => {
          return searchValue.replace(
            /可见/,
            '<span style="color:red;">可见</span>'
          );
        }
      );
    }
    var idIndex = {
      startIndex: 11,
      endIndex: 24,
    };
    for (var id = idIndex.startIndex; id <= idIndex.endIndex; id++) {
      if(key===`jycp-rt-${id}`)
      if (idAnVal !== '阴性') {
        this.style.color = 'red';
      }
    }
    if (key == 'jycp-rt-51') {
      idAnVal == '' &&
        $('.pv-remark').each(function() {
          $(this).hide();
        });
    }
    if (key == 'jycp-rt-10') {
      idAnVal == '' &&
        $('#jcpd').each(function() {
          $(this).hide();
        });
    }
    var value = publicInfo[key] || idAnVal;
    if (displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
      value = value + ' ' + publicInfo['affirmTime'];
    }
    // 兼容旧模板
    if (key === 'reqHospital') {
      if (value == '' || value == '广东省人民医院') {
        value = '本院';
      } else {
        value = '外院';
      }
    }
    $(this).html(value);
    addIdToNodeByView(this, key, idAndDomMap);
  });

  curElem.find('.jycp-pv [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  });

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="item-img">
          <img src="${image.src}" alt="">
        </div>
        `;
      }
    }
    if (imgHtml) {
      curElem.find('.preview .footer-img').html(imgHtml);
      curElem.find('.preview .footer-img').show();
    }
  }
}
/**
 * 实验结果
 */
function experimentalRes() {
  let selectList = $('#result input');
  selectList.each(function() {
    if ($(this).val() != '阴性') {
      this.style.color = '#E64545';
    } else {
      this.style.color = '#303133';
    }
    this.addEventListener('input', (e) => {
      if (e.target.value != '阴性') {
        this.style.color = '#E64545';
      } else {
        this.style.color = '#303133';
      }
    });
  });
}
/**
 * 获取单选选中内容
 * */
// 监听可见性
function listenRadio() {
  $('.radioGrop').each(function() {
    let radioList = $(this).find('input[type="radio"]');
    radioList.each((index, item) => {
      // console.log($(item).next());
      $(item).next()[0].style.color = '#303133';
      if (index == 1 && item.checked) {
        $(item).next()[0].style.color = '#E64545';
      }
      item.onchange = function() {
        radioList.each((i, elem) => {
          $(elem).next()[0].style.color = '#303133';
        });
        if (index == 1) {
          $(this).next()[0].style.color = '#E64545';
        }
      };
    });
  });
}
function getRadioChecked() {
  let target = $('#jycp-rt-49')[0];
  let radioList = $('input[type="radio"]');
  if (target) {
    radioList.each(function(item, index) {
      this.addEventListener('change', (e) => {
        let text=[];
        radioList.each(function(e) {
          let checked = this.checked;
          if (checked) {
            // text += `${this.value}。`;
            text.push(this.value);
          }
        });
        text = text.join('；')+'。';
        $(target).val(text);
      });
    });
  }
}
/**
 * 初始化实验结果下拉框
 */
function initResultTable() {
  let idIndex = {
    start: 11,
    end: 24,
  };
  let dropdown = layui.dropdown;
  for (let id = idIndex.start; id <= idIndex.end; id++) {
    // initInpAndSel(`jycp-rt-${id}`, tableRes);

    dropdown.render({
      elem: `#jycp-rt-${id}`,
      data: tableRes,
      click: function(obj) {
        this.elem.val(obj.title);
        if (obj.title !== '阴性') {
          this.elem[0].style.color = '#E64545';
        } else {
          this.elem[0].style.color = '#303133';
        }
      },
      style: `width:180px`,
    });
  }
}
/**
 * 备注发生改变时
 * */
function remarkChange() {
  let dropdown = layui.dropdown;
  let eventHandler = $('#jycp-rt-54')[0];
  let target = $('#jycp-rt-51')[0];
  let newTemp = !document.getElementById('jycp-rt-10')
  if(!newTemp){
    remarkList = [
      {
        id: '1',
        title: `1、检验方法的局限性：
（1）本试剂盒采用了BIOMED-2标准化引物组，免疫球蛋白（IG） 基因重排检测试剂盒（毛细管电泳法）获得NMPA批准，T细胞受体（TCR）基因重排检测结果仅供临床参考；
（2）IG/TCR基因重排并不一定局限于B/T细胞谱系，在非霍奇金淋巴瘤中克隆性重排也存在阴性可能，检测结果应结合临床、组织学及免疫表型等数据综合判断；
（3）肿瘤/异型细胞占比至少达到20%，肿瘤/异型细胞过低会影响检测灵敏度，存在假阴性可能。
2、本报告结果只对该检测标本负责，如有异议请在三日内提出。`,
      },
      {
        id: '2',
        title: `1、检验方法的局限性：
（1）本试剂盒采用了BIOMED-2标准化引物组，免疫球蛋白（IG） 基因重排检测试剂盒（毛细管电泳法）获得NMPA批准，T细胞受体（TCR）基因重排检测结果仅供临床参考；
（2）IG/TCR基因重排并不一定局限于B/T细胞谱系，在非霍奇金淋巴瘤中克隆性重排也存在阴性可能，检测结果应结合临床、组织学及免疫表型等数据综合判断；
（3）肿瘤/异型细胞占比至少达到20%，肿瘤/异型细胞过低会影响检测灵敏度，存在假阴性可能。
2、本例病例内参基因仅见100bp、200bp和300bp产物，未见395bp产物，表明该样本DNA存在片段化的可能，不除外大于300bp的目的基因片段的假阴性情况；
3、本报告结果只对该检测标本负责，如有异议请在三日内提出。`,
      },
    ]
  }
  dropdown.render({
    elem: `#jycp-rt-54`,
    data: remarkList,
    click: function(obj) {
      eventHandler &&  $(eventHandler).val(obj.title)
      if(newTemp){
        $(target).val(checkFragment+obj.title);
      }else{
        $(target).val(obj.title)
      }
    },
    style: 'width:calc(100% - 148px);',
  });
  eventHandler &&
    eventHandler.addEventListener('input', function() {
      $(target).val($(this).val());
    });
}
//获取标本类型列表
function toSampleType(examClass, examSubClass) {
  let sampleTypeList = [];
  let typeList = window.getSampleTypeList(examClass, examSubClass);
  if (typeList && typeList.length > 0) {
    for (var t = 0; t < typeList.length; t++) {
      sampleTypeList.push({
        title: typeList[t].typeName,
        id: typeList[t].typeCode,
      });
    }
  }
  initInpAndSel('jycp-rt-6', sampleTypeList, 240);
  initInpAndSel('jycp-rt-57', particularly, 240);
  // if (!enterOptions.isSavedReport) {
  //   curElem.find('#jycp-rt-6').val('基因检测');
  // }
}

// 初始化
function initPage(publicInfo) {
  findExamItemByMark()
  getImpression();
  let { examClass = '', examSubClass = '' } = publicInfo;
  console.log(publicInfo);
  initResultTable();
  experimentalRes();
  getRadioChecked();
  remarkChange();
  toSampleType(examClass, examSubClass);
  listenRadio();
  // 未写过的备注默认第一条
  if (!isSavedReport) {
    curElem.find('#jycp-rt-54').val(remarkList[0].title);
    curElem.find('#jycp-rt-51').val(checkFragment+remarkList[0].title);

    // 获取试剂号的默认值
    defaultValueList = getDefaultByPatternId('2028');
    let defaultValueMap = filterKeyMap(defaultValueList,'reagentNo');
    curElem.find('#jycp-rt-4').val(defaultValueMap.reagentNo || '');
    $('#jycp-rt-6').val('石蜡包埋组织')
  }
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function(obj) {
      this.elem.val(obj.title);
    },
    style: 'height: 220px; width: 280px;overflow-y:auto;overflow-x:hidden;',
  });
}
