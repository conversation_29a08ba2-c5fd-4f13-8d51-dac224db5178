$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; //结果集，用于判断是否为新报告
var isSavedReport = false; //报告是否填写过
var xbdnabtSelList = [
  {
    idList: ['gfexbdna-rt-1'],
    lenVal: 80,
    optionList: [
      {title: '满意', id: 1},
      {title: '不满意', id: 2},
      {title: '', id: 3},
    ]
  },
  // {
  //   idList: ['gfexbdna-rt-2'],
  //   lenVal: 236,
  //   optionList: [
  //     {title: '炎症细胞覆盖', id: 1},
  //     {title: '鳞状细胞少', id: 2},
  //     {title: '出血过多', id: 3},
  //     {title: '抹片过厚或人为假象', id: 4},
  //     {title: '', id: 5},
  //   ]
  // },
  {
    idList: ['gfexbdna-rt-4'],
    lenVal: 320,
    optionList: [
      {title: '未见DNA倍体异常细胞', id: 1},
      {title: '细胞数量少，未见DNA倍体异常细胞', id: 2},
      {title: '可见少量DNA倍体异常细胞', id: 3},
      {title: '可见DNA倍体异常细胞', id: 3},
      {title: '可见大量DNA倍体异常细胞', id: 3},
    ]
  },
  {
    idList: ['gfexbdna-rt-5'],
    lenVal: 320,
    optionList: [
      {title: '定期复查', id: 1},
      {title: '6-12个月复查', id: 2},
      {title: '请结合临床处理，6-12个月复查', id: 3},
      {title: '请作阴道镜检查及活体组织检查', id: 4},
      {title: '请结合TBS诊断结果', id: 5},
      {title: '请结合TBS诊断结果，定期复查', id: 6},
      {title: '请结合TBS诊断结果，作阴道镜检查及活体组织检查', id: 7},
      {title: '请结合TBS诊断结果，3-6个月复诊', id: 8},
    ]
  },
  {
    idList: ['gfexbdna-rt-7','gfexbdna-rt-8','gfexbdna-rt-9'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '无', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['gfexbdna-rt-11','gfexbdna-rt-12','gfexbdna-rt-13','gfexbdna-rt-14','gfexbdna-rt-15','gfexbdna-rt-17'],
    lenVal: 80,
    optionList: [
      {title: '有', id: 1},
      {title: '未见', id: 2},
      {title: '', id: 3},
    ]
  },
  {
    idList: ['gfexbdna-rt-10'],
    lenVal: 160,
    optionList: [
      {title: '正常', id: 1},
      {title: '轻度发炎', id: 2},
      {title: '中度发炎', id: 3},
      {title: '重度发炎', id: 4},
    ]
  },
  {
    idList: ['gfexbdna-rt-24'],
    lenVal: 750,
    optionList: [
      {title: '无上皮内病变或恶性病变（NILM）。', id: 1},
      {title: '非典型鳞状上皮细胞-意义不明确（ASC-US）,建议定期复查。', id: 2},
      {title: '非典型鳞状上皮细胞-不除外高级别鳞状上皮内病变（ASC-H）,建议做阴道镜活检。', id: 3},
      {title: '非典型鳞状上皮细胞-不除外低级别鳞状上皮内病变,建议做阴道镜活检。', id: 4},
      {title: '低级别鳞状上皮内病变（LSIL）,建议做阴道镜检查。', id: 5},
      {title: '低级别鳞状上皮内病变（LSIL）,部分细胞不排除高级别鳞状上皮内病变（ASC-H），建议做阴道镜检查。', id: 6},
      {title: '高级别鳞状上皮内病变（HSIL）,建议做阴道镜活检。', id: 7},
      {title: '高级别鳞状上皮内病变（HSIL）累及子宫颈管腺体,建议做阴道镜活检。', id: 8},
      {title: '高级别鳞状上皮内病变（HSIL）,不除外浸润可能，建议做阴道镜活检。', id: 9},
      {title: '鳞状细胞癌（SCC）,建议做阴道镜活检。', id: 10},
      {title: '非典型腺细胞（非特异）（AGC-NOS）。', id: 11},
      {title: '非典型子宫颈管腺细胞（非特异）（AGC-NOS）。', id: 12},
      {title: '非典型子宫内膜腺细胞（非特异）（AGC-NOS）。', id: 13},
      {title: '非典型腺细胞，倾向于肿瘤性（AGC-FN）。', id: 14},
      {title: '非典型子宫颈管腺细胞，倾向于肿瘤性（AGC-FN）。', id: 15},
      {title: '非典型子宫内膜腺细胞，倾向于肿瘤性（AGC-FN）。', id: 16},
      {title: '子宫颈管原位腺癌（AIS）。', id: 17},
      {title: '腺癌。', id: 18},
      {title: '子宫颈管腺癌。', id: 19},
      {title: '子宫内膜腺癌。', id: 20},
      {title: '子宫外腺癌。', id: 21},
      {title: '恶性肿瘤。', id: 22},
    ]
  }
]; 
var textData = [
  {
    itemData: '未见DNA倍体异常细胞',
    methData: '定期复查',
  },
  {
    itemData: '细胞数量少，未见DNA倍体异常细胞',
    methData: '定期复查',
  },
  {
    itemData: '可见少量DNA倍体异常细胞',
    methData: '请结合TBS诊断结果，定期复查',
  },
  {
    itemData: '可见DNA倍体异常细胞',
    methData: '请结合TBS诊断结果，作阴道镜检查及活体组织检查',
  },
  {
    itemData: '可见大量DNA倍体异常细胞',
    methData: '请结合TBS诊断结果，作阴道镜检查及活体组织检查',
  },
]
var inputAll = []
var mydText = ''
// 细胞DNA倍体检测下拉列表
var xbxfxOptList = ['无上皮内病变或恶性病变（NILM）','无上皮内病变或恶性病变（NILM）-反应性改变','无上皮内病变或恶性病变（NILM）-萎缩反应性改变','微生物感染-念珠菌','微生物感染-滴虫','微生物感染-放线菌','微生物感染-球杆菌','微生物感染-疱疹病毒','非典型鳞状上皮细胞-意义不明确（ASC-US）','非典型鳞状上皮细胞-不除外高级别鳞状上皮内病变（ASC-H）','低级别鳞状上皮内病变（LSIL）','高级别鳞状上皮内病变（ASC-H）','高级别鳞状上皮内病变（ASC-H）-具有可疑的侵袭特点','鳞状细胞癌（SCC）','腺细胞（非特异）（AGC-NOS）','腺细胞，倾向于肿瘤性（AGC-FN）','子宫颈管腺细胞（非特异）（AGC-NOS）','子宫颈管腺细胞，倾向于肿瘤性（AGC-FN）','子宫内膜腺细胞（非特异）（AGC-NOS）','子宫内膜腺细胞，倾向于肿瘤性（AGC-FN）','子宫颈管原位癌（AIS）','腺癌','宫颈腺癌','子宫内膜腺癌','查见子宫内膜细胞']; // 细胞学分析下拉列表
var bbmyd = ['炎症细胞覆盖','鳞状细胞少','出血过多','抹片过厚或人为假象']
var bgyj1 = [
  '无上皮内病变或恶性病变（NILM）',
  '非典型鳞状上皮细胞-意义不明确（ASC-US）,建议定期复查',
  '非典型鳞状上皮细胞-不除外高级别鳞状上皮内病变（ASC-H）,建议做阴道镜活检',
  '非典型鳞状上皮细胞-不除外低级别鳞状上皮内病变,建议做阴道镜活检。',
  '低级别鳞状上皮内病变（LSIL）,建议做阴道镜检查',
  '低级别鳞状上皮内病变（LSIL）,部分细胞不排除高级别鳞状上皮内病变（ASC-H），建议做阴道镜检查',
  '高级别鳞状上皮内病变（HSIL）,建议做阴道镜活检',
  '高级别鳞状上皮内病变（HSIL）累及子宫颈管腺体,建议做阴道镜活检',
  '高级别鳞状上皮内病变（HSIL）,不除外浸润可能，建议做阴道镜活检',
  '鳞状细胞癌（SCC）,建议做阴道镜活检',
  '非典型腺细胞（非特异）（AGC-NOS）',
  '非典型子宫颈管腺细胞（非特异）（AGC-NOS）',
  '非典型子宫内膜腺细胞（非特异）（AGC-NOS）',
  '非典型腺细胞，倾向于肿瘤性（AGC-FN）',
  '非典型子宫颈管腺细胞，倾向于肿瘤性（AGC-FN）',
  '非典型子宫内膜腺细胞，倾向于肿瘤性（AGC-FN）',
  '子宫颈管原位腺癌（AIS）',
  '腺癌',
  '子宫颈管腺癌',
  '子宫内膜腺癌',
  '子宫外腺癌',
  '恶性肿瘤',
]
var bgyj2 = [
  '阴道滴虫',
  '真菌，形态学上符合念珠菌属',
  '菌群变化，提示细菌性阴道病',
  '细菌，形态学上符合放线菌属',
  '细胞变化，符合单纯疱疹病毒感染',
  '细胞变化，符合巨细胞病毒感染',
  '见子宫内膜细胞',
  '表皮细胞萎缩',
  '炎症反应性改变',
  '鳞状上皮细胞少，请结合临床，必要时复查',
  '炎症减退后，请复取抺片',
  '出血和炎症减退后，请复取抺片',
  '出血减退后，请复取抺片',
  '微生物感染背景，请治疗感染后复取抺片',
  '放疗相关改变',
  '宫内节育器（IUD）',
  '不满意标本，鳞状上皮细胞过少，请复取抺片',
  '不满意标本，过多血液遮盖，请复取抺片',
  '不满意标本，过多炎性细胞遮盖，请复取抺片',
  '不满意标本，抹片过厚或人为假象，请复取抺片',
]

function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#gfexbdna1 .gfexbdna-view'),  //转成pdf的区域，默认是整个页面
      asyncAjax: true,  //存在异步操作的方法，应等其完成再继续
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || []) : []; // 结果集
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon();
    } else {
      pageInit();
    }
  }
}
// 初始化
function pageInit() {
  initAllSel();
  // fillSelectOption('gfexbdna-edit');
  // fillSelectOption('bgyj1');
  fillSelectOption('bgyj2');
  fillSelectOption('bbmyd');
  setDefaultVal();
  uploadImg();
  // 多选赋值回显
  xmSelect.get().forEach(function(opt, index) {
    var vm = $(opt.options.dom);
    var val = vm.next('.hide-val').val();
    if(val) {
      var valArr = val.split('；').map(function(name){
        return {title: name, id: name};
      })
      var text = valArr.map(item => item.id).join('；')
      // 回显隐藏数据
      var attrOptName = $(opt.options.el).attr('opt-name');
      if(attrOptName === 'bgyj1-dropdown') {
        $('#gfexbdna-rt-22').val(text)
      }
      if(attrOptName === 'bgyj2-dropdown') {
        $('#gfexbdna-rt-23').val(text)
        inputAll = valArr
      }
      if(attrOptName === 'bbmyd-dropdown') {
        $('#gfexbdna-rt-21').val(text)
      }
      opt.setValue(valArr)
    }
  })
  window.postMessage({
    message: 'finishAsyncAjax',
    data: {}
  }, '*');
}

// 设置初始化默认值
function setDefaultVal() {
  if(!resultData.length) {
    $('#gfexbdna-rt-1').val('满意');
    $('#gfexbdna-rt-4').val('未见DNA倍体异常细胞');
    $('#gfexbdna-rt-5').val('定期复查');
    $('#gfexbdna-rt-24').val('无上皮内病变或恶性病变（NILM）。');
    // “有”默认值id列表
    let yIdList = ['gfexbdna-rt-7','gfexbdna-rt-8','gfexbdna-rt-9'];
    for(let id in yIdList) {
      $('#'+yIdList[id]).val('有');
    }
    // “未见”默认值id列表
    let wjIdList = ['gfexbdna-rt-11','gfexbdna-rt-12','gfexbdna-rt-13','gfexbdna-rt-14','gfexbdna-rt-15','gfexbdna-rt-17'];
    for(let id in wjIdList) {
      $('#'+wjIdList[id]).val('未见');
    }
    $('#gfexbdna-rt-10').val('轻度发炎');
    $('#gfexbdna-rt-16').val('未见上皮内病变及癌变')
  }
}

// 将下拉数据补全和初始化多选，下拉编辑
function fillSelectOption(key) {
let dropdown = layui.dropdown;
  var curSelect = curElem.find('[opt-name="'+key+'-dropdown"]');
  if(!curSelect.length) {
    return;
  }
  var multi = $(curSelect).attr('multi');
  var dataList = []
  if(key === 'bgyj1') {dataList = bgyj1} 
  if(key === 'bgyj2') {dataList = bgyj2}
  if(key === 'bbmyd') {dataList = bbmyd}
  if(key === 'gfexbdna-edit') {dataList = xbxfxOptList}
  var data = dataList.map(function(item) {
    return {
      title: item,
      id: item || Date.now()
    }
  })
  if(multi === '1') {  //多选
    var optXmSel = xmSelect.render({
      el: '[opt-name="'+key+'-dropdown"]', 
      tips: '',
      prop: {
        name: 'title',
        value: 'id',
      },
      model: {
        label: {
          type: 'text',
          //使用字符串拼接的方式
          text: {
            //左边拼接的字符
            left: '',
            //右边拼接的字符
            right: '',
            //中间的分隔符
            separator: '；',
          },
        }
      },
      data: data,
      hide: function() {
        xmSelect.get().forEach(function(opt,index) {
          var vm = $(opt.options.dom);
          var text = vm.find('.label-content').text() || "";
          vm.next('.hide-val').val(text);
          var attroptName =$(opt.options.el).attr('opt-name');
          if(key === 'bgyj1' && attroptName ==='bgyj1-dropdown'){
            $('#gfexbdna-rt-22').val(text)
          }
          if(key === 'bgyj2' && attroptName ==='bgyj2-dropdown'){
            $('#gfexbdna-rt-23').val(text)
          }
          if(key === 'bbmyd' && attroptName ==='bbmyd-dropdown'){
            mydText = text
            $('#gfexbdna-rt-21').val(text)
            selecMyd(text)
          }
        })
        bgfxChange()
      }
    })
    if(!resultData.length && key === 'bgyj1') {
      optXmSel.setValue([{ title: '无上皮内病变或恶性病变（NILM）', id: '无上皮内病变或恶性病变（NILM）' }]);
      $('#gfexbdna-rt-22').val('无上皮内病变或恶性病变（NILM）')
    }
  } else {
    dropdown.render({
      elem: '[opt-name="'+key+'-dropdown"]', 
      data: data, 
      className: 'laySelLab', 
      click: function (obj) {
        this.elem.val(obj.title);
      },
    });
  }
}
function bgfxChange() {
  let html = ''
  // if($('#gfexbdna-rt-22').val()) {
  //   let arst = $('#gfexbdna-rt-22').val().split("；")
  //   html += arst.join('；\n') + '。\n\n'
  //   // html += $('#gfexbdna-rt-22').val() + '\n\n'
  // }
  if($('#gfexbdna-rt-24').val()) {
    html += $('#gfexbdna-rt-24').val() + '\n\n'
  }
  if($('#gfexbdna-rt-23').val()) {
    console.log('78979789');
    
    let arst = $('#gfexbdna-rt-23').val().split("；")
    html += arst.join('；\n') + '。\n\n'
    // html += $('#gfexbdna-rt-23').val() + '\n\n'
  }
  $('#gfexbdna-rt-16').val(html)
}
function selecMyd(text) { 
  let myd = $('#gfexbdna-rt-1').val()
  let arrys = text.split("；")
  let allset = []
  inputAll = inputAll.filter(e => 
    e.title !== '不满意标本，过多炎性细胞遮盖，请复取抺片' && e.title !== '不满意标本，鳞状上皮细胞过少，请复取抺片' && e.title !== '不满意标本，过多血液遮盖，请复取抺片' && e.title !== '不满意标本，抹片过厚或人为假象，请复取抺片'
  )
  if(myd === '不满意'){
    arrys.forEach(item => {
      if(item === '炎症细胞覆盖') {
        allset.push({title: '不满意标本，过多炎性细胞遮盖，请复取抺片', id: '不满意标本，过多炎性细胞遮盖，请复取抺片'})
      }
      else if(item === '鳞状细胞少') {
        allset.push({title: '不满意标本，鳞状上皮细胞过少，请复取抺片', id: '不满意标本，鳞状上皮细胞过少，请复取抺片'})
      }
      else if(item === '出血过多') {
        allset.push({title: '不满意标本，过多血液遮盖，请复取抺片', id: '不满意标本，过多血液遮盖，请复取抺片'})
      }
      else if(item === '抹片过厚或人为假象') {
        allset.push({title: '不满意标本，抹片过厚或人为假象，请复取抺片', id: '不满意标本，抹片过厚或人为假象，请复取抺片'})
      }
    })
  }
  inputAll.push(...allset)
  
  xmSelect.get().forEach(function(opt,index) {
    var attroptName =$(opt.options.el).attr('opt-name');
    if(attroptName ==='bgyj2-dropdown') {
      opt.setValue(inputAll)
      var htls = inputAll.map(item => item.id).join('；')
      $('#gfexbdna-rt-23').val(htls)
    }
  })
  bgfxChange()
}
// 初始化所有下拉框
function initAllSel() {
  xbdnabtSelList.map(item => {
    let { idList = [], lenVal = 0, optionList = [] } = item;
    idList.map(id => {
      initInpAndSel(id,optionList,lenVal);
    })
  })
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      changeSel(obj.title,idList)
      if(idList === 'gfexbdna-rt-1') {
        selecMyd(mydText)
      }
    },
    style:`max-height:200px;overflow:auto;overflow-x:hidden;${lenVal !== 0 ? `width: ${lenVal}px;` : 'width:30%'}`,
  })
}
function changeSel(text,idList){
  console.log('idList',idList,text);
  
  inputAll = inputAll.filter(e => 
    e.title !== '阴道滴虫' && e.title !== '真菌，形态学上符合念珠菌属' && e.title !== '菌群变化，提示细菌性阴道病' 
    && e.title !== '细菌，形态学上符合放线菌属' && e.title !== '细胞变化，符合单纯疱疹病毒感染' && e.title !== '细胞变化，符合巨细胞病毒感染'
  )
  var valArr = ['gfexbdna-rt-11','gfexbdna-rt-12','gfexbdna-rt-13','gfexbdna-rt-14','gfexbdna-rt-15','gfexbdna-rt-17',]
  valArr.forEach(item => {
    let test = $(`#${item}`).val()
    if(test === '有') {
      if(item === 'gfexbdna-rt-11'){
        inputAll.push({title: '阴道滴虫', id: '阴道滴虫'})
      }
      if(item === 'gfexbdna-rt-12') {
        inputAll.push({title: '真菌，形态学上符合念珠菌属', id: '真菌，形态学上符合念珠菌属'})
      }
      if(item === 'gfexbdna-rt-13') {
        inputAll.push({title: '菌群变化，提示细菌性阴道病', id: '菌群变化，提示细菌性阴道病'})
      }
      if(item === 'gfexbdna-rt-14') {
        inputAll.push({title: '细菌，形态学上符合放线菌属', id: '细菌，形态学上符合放线菌属'})
      }
      if(item === 'gfexbdna-rt-15') {
        inputAll.push({title: '细胞变化，符合单纯疱疹病毒感染', id: '细胞变化，符合单纯疱疹病毒感染'})
      }
      if(item === 'gfexbdna-rt-17') {
        inputAll.push({title: '细胞变化，符合巨细胞病毒感染', id: '细胞变化，符合巨细胞病毒感染'})
      }
    }
    
  })
  if(text === '有' || text === '未见'){
    xmSelect.get().forEach(function(opt,index) {
      var attroptName =$(opt.options.el).attr('opt-name');
      if(attroptName ==='bgyj2-dropdown') {
        opt.setValue(inputAll)
        var htls = inputAll.map(item => item.id).join('；')
      $('#gfexbdna-rt-23').val(htls)
      }
    })
  }
  bgfxChange()
  for(var i = 0;i < textData.length; i++){
    if(textData[i].itemData === text){
      $('#gfexbdna-rt-5').val(textData[i].methData);
    }
  }
}

// 上传图像
function uploadImg() {
  var rtEl=$('.fileids');
  if (!publicInfo.examNo && !publicInfo.busId) {
    return;
  }
  var idAnVal = idAndDomMap['gfexbdna-rt-3'] ? idAndDomMap['gfexbdna-rt-3'].value : '';
  if(idAnVal){
    rtEl.val(idAnVal);
    showImgList(rtEl);
  }

  $('#imageInput').on('input',function(e){
    if(this.files.length) {
      for(let fileIndex in this.files) {
        var file = this.files[fileIndex];
        if(!file || !file.size){
          e.target.value = '';
          return;
        }
        imageUploader(publicInfo.examNo,file, rtEl);
        showImgList(rtEl);
      }
    }
  })
}

// 获取图片并回显
function showImgList(rtEl) {
  getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
    if(uris&&uris.length){
      let imgHtml = '';
      for (let i in uris) {
        let url = uris[i];
        // console.log('回显url--->',url);
        if (url) {
          imgHtml += `
          <div class="preview">
            <img classs="divisionImg" id="divisionImg-${i}" src="${url}" alt=""  onclick="viewImg(this,${i})">
            <img class="divisionPreview" id="divisionPreview-${i}" style="display: none;width: 590px;height: 440px;margin-left: 5px;margin-top: 5px;" src="${url}" alt="">
            <div id="divisionDel-${i}" class="delImg" onclick="deleteImg(this,${i})">x</div>
          </div>
          `;
        }
      }
      $('.preview-img').html(imgHtml);
    }else {
      $('.preview-img').html('');
    }
  })
}

// 删除图片
function deleteImg(ele,index) {
  var rtEl =$('.fileids');
  var pidList = $('.fileids').val().split(',');
  var pid = pidList[index];
  // console.log('pid',pid)
  deleteUploadedImage(publicInfo.examNo,pid, rtEl);
  showImgList(rtEl);
}

// 预览图片
function viewImg(ele,index) {
  layer.open({
    title: '预览',
    type: 1,
    area: ['600px','500px'],
    content: $('#divisionPreview-'+index) //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
  });
}

// 获取诊断
function getImpression() {
  var strArr = [], impression = '';
  let dnayjVal = $('#gfexbdna-rt-4').val() || '';
  dnayjVal && strArr.push('DNA意见：' + dnayjVal);
  let dnajyVal = $('#gfexbdna-rt-5').val() || '';
  dnajyVal && strArr.push('DNA建议：' + dnajyVal);
  let xbxfxVal = $('#gfexbdna-rt-6').val() || '';
  xbxfxVal && strArr.push('细胞学分析：' + xbxfxVal);
  let bgyjVal = $('#gfexbdna-rt-16').val() || '';
  bgyjVal && strArr.push('报告意见和建议：' + bgyjVal);
  impression = strArr.length ? strArr.join('，') : '';
  return impression;
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  var rtEl=$('.fileids');
  $('.preview-img-ls').html('');
  curElem.find('.gfexbdna-view [data-key]').each(function () {
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      var value = publicInfo[key] || idAnVal;
      // 细胞DNA倍体检测页面
      if(key ==='gfexbdna-rt-3') {
        rtEl.val(value);
        getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
          // console.log('预览--->',uris);
          if(uris&&uris.length){
            let imgHtml = '';
            for (let i in uris) {
              let url = uris[i];
              // console.log('预览url-->',url);
              if (url) {
                imgHtml += `
                <div class="preview-item-img">
                  <img id="divisionPreviewImg-${i}" src="${url}" alt="">
                </div>
                `;
              }
            }
            if(imgHtml) {
              var oriHtml = $('.preview-img-ls').html();
              $('.preview-img-ls').css('display','flex');
              $('.preview-img-ls').html(imgHtml + oriHtml);
            }
          }
          window.postMessage({
            message: 'finishAsyncAjax',
            data: {}
          }, '*');
        })
      }
      if(key === 'bbmyd') {
        let bbxbVal = idAndDomMap['gfexbdna-rt-2'].value || '';
        value = idAndDomMap['gfexbdna-rt-1'].value + (bbxbVal ? '，' + bbxbVal : '');
      }

      if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
        value = value + ' ' + publicInfo['affirmTime'];
      }
      if(value) {
        $(this).html(value);
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  curElem.find('.gfexbdna-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (var image of rptImageList) {
      if (image.src) {
        // imgHtml += `
        // <div class="item-img">
        //   <img src="${image.src}" alt="">
        // </div>
        // `
        // 直接插到DNA分析图像后
        imgHtml += `
        <div class="preview-item-img">
          <img src="${image.src}">
        </div>
        `
      }
    }
    if (imgHtml) {
      var oriHtml = $('.preview-img-ls').html();
      $('.preview-img-ls').css('display','flex');
      $('.preview-img-ls').html(oriHtml + imgHtml);
      // curElem.find('.gfexbdna-view .rpt-img-ls').html(imgHtml);
      // curElem.find('.gfexbdna-view .rpt-img-ls').css('display', 'block');
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}