$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }

  initC();

  $('#ssq').click(function () {
    cdfiClick('ssq');
  })
  $('#szq').click(function () {
    cdfiClick('szq');
  })
  $('#sq').click(function () {
    cdfiClick('sq');
  })

  $('#dpl-tls').click(function () {
    lsClick('tls');
  })
  $('#dpl-fls').click(function () {
    lsClick('fls');
  })

  keydown_to_tab('fjm1');
  // 收缩期、舒张期、双期
  $('.block-ck3').click(function (e) {
    e.preventDefault();
  })
  $('.ck-inp3').click(function (e) {
    e.stopPropagation();
  })
  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });


  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}


//初始化CDFI可在该处探及
function initC() {
  var ssqChecked = $('#fjm-rt-27').prop("checked");
  var szqChecked = $('#fjm-rt-28').prop("checked");
  var sqChecked = $('#fjm-rt-29').prop("checked");
  var tlsChecked = $('#fjm-rt-49').prop("checked");
  var flsChecked = $('#fjm-rt-50').prop("checked");

  if (ssqChecked) {
    $('#ssq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-szq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  } else if (szqChecked) {
    $('#szq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-ssq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  } else if (sqChecked) {
    $('#sq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-ssq').attr("style", "display:none;");
    $('#dpl-szq').attr("style", "display:none;");
  } else {
    $('#ssq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-szq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  }
  if (flsChecked) {
    $('#tls').attr("style", "display:none;");
    $('#fls').attr("style", "display:unset;");
  } else {
    $('#tls').attr("style", "display:unset;");
    $('#fls').attr("style", "display:none;");
  }
}
function cdfiClick(ele) {
  $('#ssq').parent().parent().attr('style', 'background:' + (ele === 'ssq' ? '#E4E7ED;' : 'unset;'))
  $('#szq').parent().parent().attr('style', 'background:' + (ele === 'szq' ? '#E4E7ED;' : 'unset;'))
  $('#sq').parent().parent().attr('style', 'background:' + (ele === 'sq' ? '#E4E7ED;' : 'unset;'))
  $('#dpl-ssq').attr('style', 'display:' + (ele === 'ssq' ? 'unset;' : 'none;'));
  $('#dpl-szq').attr('style', 'display:' + (ele === 'szq' ? 'unset;' : 'none;'));
  $('#dpl-sq').attr('style', 'display:' + (ele === 'sq' ? 'unset;' : 'none;'));
}

function lsClick(ele) {
  $('#tls').attr('style', 'display:' + (ele === 'tls' ? 'unset;' : 'none;'));
  $('#fls').attr('style', 'display:' + (ele === 'fls' ? 'unset;' : 'none;'));
}


/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '';
  let strList = [], wzhcList = [], jrList = [], dplCheckList = [], dplTj = [], flsList = [];
  curKeyValData['fjm-rt-1'] ? strList.push(curKeyValData['fjm-rt-1'].val) : '';
  let wzIdList = ['fjm-zs', 'fjm-zx', 'fjm-ys', 'fjm-yx'];
  let wzhcIdList = ['fjm-rt-18', 'fjm-rt-19', 'fjm-rt-20', 'fjm-rt-21'];
  let jrIdList = ['fjm-rt-22', 'fjm-rt-23', 'fjm-rt-24', 'fjm-rt-25'];
  let tjPid = ['fjm-rt-27', 'fjm-rt-28', 'fjm-rt-29'];
  let tjId = ['fjm-rt-34', 'fjm-rt-35', 'fjm-rt-40', 'fjm-rt-41', 'fjm-rt-46', 'fjm-rt-47'];
  let lskId = ['fjm-rt-51', 'fjm-rt-55'];
  let cwId = ['fjm-rt-52', 'fjm-rt-56'];
  let pgId = ['fjm-rt-53', 'fjm-rt-57'];
  let mgId = ['fjm-rt-54', 'fjm-rt-58'];
  for (var w = 0; w < wzIdList.length; w++) {
    $('#' + wzIdList[w] + ' .rt-sr-w:not([pid])').each(function (pIdx, par) {
      let parent = $(par);
      let pid = parent.attr('id');
      let curPid = curKeyValData[pid];
      if (curPid) {
        let child = curPid.child;
        strList.push(curPid.val)
        if (child && child.length) {
          for (var i = 0; i < child.length; i++) {
            strList.push(child[i].val)
          }
        }
      }
    })
  }
  $('#fjm-hc .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      if (wzhcIdList.indexOf(curPid.id) !== -1) {
        wzhcList.push(curPid.val)
      }
      if (jrIdList.indexOf(curPid.id) !== -1) {
        jrList.push(curPid.val)
      }
    }
  })
  wzhcList && wzhcList.length ? str = (wzhcList.join('、') + '肺静脉合成共同肺静脉腔') : '';
  if (str) {
    jrList && jrList.length ? str += ('进入' + jrList.join('、')) : '';
  } else if (!str) {
    jrList && jrList.length ? str = ('肺静脉合成共同肺静脉腔进入' + jrList.join('、')) : '';
  }

  str ? strList.push(str) : '';
  str = '';

  $('#dplCheck').find('.rt-sr-w[pid=fjm-rt-26]').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    var commonTj = function (list, num) {
      let child = list.child;
      let cdfiTjList = [];
      if (child && child.length) {
        if (tjId.indexOf(child[0].id) === -1) {
          let flChild = child[0].child;
          if (flChild && flChild.length) {
            cdfiTjList.push(list.val + flChild[0].val + '分流')
          }
        }
        if (tjId.indexOf(child[0].id) !== -1) {
          cdfiTjList.push(list.val + child[0].val)
        }
      } else {
        cdfiTjList.push(list.val)
      }
      let tjStr = cdfiTjList.join('，')
      return tjStr
    }
    if (curPid) {
      if (tjPid.indexOf(curPid.id) !== -1) {
        dplTj.push(commonTj(curPid))
      }
      if (curPid.id === "fjm-rt-48") {
        let lsChild = curPid.child;
        if (lsChild && lsChild.length) {
          let flsChild = lsChild[0].child;
          if (flsChild && flsChild.length) {
            for (var s = 0; s < flsChild.length; s++) {
              if (lskId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('宽' + flsChild[s].val + 'mm')
              }
              if (cwId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('CW测得Vmax为' + flsChild[s].val + 'm/s')
              }
              if (pgId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('PG为' + flsChild[s].val + 'mmHg')
              }
              if (mgId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('MG为' + flsChild[s].val + 'mmHg')
              }
            }
            dplCheckList.push(lsChild[0].val + flsList.join('，'))
          } else {
            dplCheckList.push(lsChild[0].val)
          }
        }

      }
    }
  })
  dplTj && dplTj.length > 0 ? dplCheckList.unshift('CDFI可在该处探及，' + dplTj.join('，')) : '';
  str = dplCheckList.join('，');
  str ? strList.push('多普勒检查，' + str) : '';
  str = '';
  rtStructure.description = '肺静脉，' + strList.join('，');
}