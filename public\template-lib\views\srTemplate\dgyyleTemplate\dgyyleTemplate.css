#dgyyle1 {
  width: 780px;
  position: relative;
  font-size: 14px;
  color: #000;
  background: #FFFFFF;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  margin: 0 auto;
}

/*--------字体相关---------*/
.fs-10 {
  font-size: 10px!important;
}
.fs-12 {
  font-size: 12px!important;
}
.fs-14 {
  font-size: 14px!important;
}
.fs-16 {
  font-size: 16px;
}
.fs-20 {
  font-size: 20px;
  line-height: 20px;
}

/*--------颜色类---------*/
.text-c1{
  color: #303133;
}
.echart-h {
  background: #191975;
}
.bg-gray {
  background: #EAEAEA;
}
.req-flag {
  color: #E64545;
}

/*--------宽度类---------*/
.wd-18 {
  width: 18px!important;
}
.wd-22 {
  width: 22px;
}
.wd-30 {
  width: 30px;
}
.wd-38 {
  width: 38px;
}
.wd-40 {
  width: 40px;
}
.wd-60 {
  width: 60px;
}
.wd-71 {
  width: 71px;
}
.wd-136 {
  width: 136px;
}
.wd-150 {
  width: 150px;
}

/*--------间距---------*/
.mt-8 {
  margin-top: 8px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-12 {
  margin-bottom: 12px!important;
}
.mb-16 {
  margin-bottom: 16px!important;
}
.mb-24 {
  margin-bottom: 24px!important;
}
.ml-4 {
  margin-left: 4px!important;
}
.ml-8 {
  margin-left: 8px;
}
.ml-12 {
  margin-left: 12px;
}
/*--------边框类---------*/
.nbor-r {
  border-right: none!important;
}

/*--------dgyyle报告页面---------*/
.dgyyle-header img{
  width: 100%;
}
.dgyyle-body {
  position: relative;
  padding: 0 50px;
  box-sizing: border-box;
}
.text-mid {
  text-align: center;
}
.header-title {
  font-size: 22px;
}
.con-flex {
  display: flex;
  justify-content: space-between;
}
.row-box {
  position: relative;
  padding: 8px 0;
  box-sizing: border-box;
}
.row-box::after {
  position: absolute;
  bottom: 0;
  content: "";
  width: 100%;
  height: 1px;
  background: #606266;
}
.row-item {
  min-width: 150px;
}
.data-box {
  position: relative;
}
.data-box::after {
  position: absolute;
  content: '';
  width: 100%;
  height: 1px;
  background: #606266;
  left: 0;
  bottom: -12px;
}
.remark-inp {
  width: 100%;
  resize: vertical;
  padding: 0 3px;
}
.w-con {
  display: inline-block;
}
.yyct-box {
  width: 680px;
  border: 1px solid #000000;
}
.box-header {
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-bottom: 1px solid #000000;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
}
.echart-data {
  width: 656px;
  height: 267px;
  margin: auto;
}
.echart-data img {
  width: 100%;
  height: 100%;
}
.table-data tr {
  color: #333333;
  font-weight: 400!important;
}
.table-data thead {
  height: 32px;
  font-size: 12px;
  font-weight: normal;
}
.table-data thead th {
  font-weight: normal;
}
.table-h {
  height: 36px!important;
  line-height: 36px!important;
  background: #D6DFF7;
  color: #333333!important;
  font-size: 14px;
}
.box-left {
  width: 520px;
  border: 1px solid #000000;
}
.table-data thead th:first-child,.table-data tbody tr td:not(:first-child) {
  border-right: 1px solid #000;
}
.table-data thead th:not(:first-child),.table-data tbody tr td:not(:first-child) {
  border-bottom: 1px solid #000;
}
.table-data tbody tr td:first-child {
  border-right: 1px solid #000;
}
.box-left .table-data tbody tr td:first-child {
  line-height: 24px;
}
.box-right .table-data tbody tr td:first-child {
  line-height: 72px;
}
.table-data tbody tr:last-child td {
  border-bottom: none!important;
}
.table-data tbody tr td:last-child {
  border-right: none!important;
}
.box-right {
  width: 152px;
  height: 100%;
  border: 1px solid #000000;
}
.box-right .table-h {
  padding: 4px 0;
  line-height: 14px!important;
}
.zs-td {
  position: relative;
}
.zs-sp {
  position: absolute;
  width: 200px;
  top: 0;
  left: 3px;
  text-align: left;
}
.zs-sp sup {
  font-family: fangsong;
  font-size: 10px;
  color: #000;
}
.dgyyle-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 50px 43px;
  align-items: center;
}
.dgyyle-footer .sel-block {
  display: flex;
  align-items: center;
}
[isView="true"] .dgyyle-footer .sel-block.mt-8 {
  margin-top: 0!important;
}
[isView="true"] .dgyyle-footer img{
  max-width: unset!important;
  max-height: unset!important;
  width: 100px;
  height: 40px;
  object-fit: contain;
}
[isView="true"] .exam-people, [isView="true"] .duty-nurse {
  display: none;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}