$(function() {
  window.initHtmlScript = initHtmlScript;
})
var rtStructure = null;
var curElem = null;
var optAllData = {
  examUser: ['11', '22'],  //检查者
  doctorName: ['22', '33'],  //报告医生
}
var reportData = [];
var rptImageList = [];
var deviceData = {};
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('dgfsy1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    deviceData = rtStructure.enterOptions ? (rtStructure.enterOptions.docOrign || {}) : {};
    reportData = deviceData.reportData || [];

    // 填充表格数据
    fillTableData('left');
    fillTableData('right');
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      setDevicDataVal('view');
    } else {
      // fillSelectOption();  //暂时不要
      setDevicDataVal('edit');
    }

    // 来源为住院则显示住院号，其他都是门诊号
    if(rtStructure.enterOptions && rtStructure.enterOptions.publicInfo && 
        rtStructure.enterOptions.publicInfo.patientSource === '住院') {
        $(".inPatNo").show();
        $(".outPatNo").hide();
    } else {
      $(".inPatNo").hide();
      $(".outPatNo").show();
    }
  }
}

// 赋设备的来源值
function setDevicDataVal(type) {
  if(rtStructure.enterOptions && !rtStructure.enterOptions.resultData.length) {
    var reportInfo = deviceData.reportInfo || {};
    var reportStaff = reportInfo.reportStaff || rtStructure.enterOptions.publicInfo.reporter || rtStructure.enterOptions.publicInfo.optName || '';
    var examStaff = reportInfo.examStaff || rtStructure.enterOptions.publicInfo.reporter || rtStructure.enterOptions.publicInfo.optName || '';
    if(type === 'edit') {
      $("#dgfsy-rt-16").val(examStaff);  //检查技师
      $("#dgfsy-rt-18").val(reportStaff);  //报告医生
      if(!$('#dgfsy-rt-4').text()) {   //报告日期
        var date = getCurDateAndTime().curDate;
        $('#dgfsy-rt-4').text(reportInfo.reportDate || date);
      }
      if(!$("#dgfsy-rt-19").val()) {
        $("#dgfsy-rt-19").val(deviceData.result && deviceData.result.length ? (deviceData.result[0].description || '') : "");
      }
    } else {
      $(".exam-u").html(examStaff);  //检查技师
      $(".doctor-u").html(reportStaff);  //报告医生
      var desc = deviceData.result && deviceData.result.length ? (deviceData.result[0].description || '——') : "——";
      $(".body-desc .desc-con").html(desc);
    }
  }
}

// 将下拉数据补全和初始化下拉编辑
function fillSelectOption() {
  let dropdown = layui.dropdown;
  for(var key in optAllData) {
    var curSelect = curElem.find('[opt-name="'+key+'"]');
    var data = optAllData[key].map(function(item) {
      return {
        title: item,
        id: item || Date.now()
      }
    })
    dropdown.render({
      elem: '[opt-name="'+key+'"]', 
      data: data, 
      className: 'laySelLab', 
      click: function (obj) {
        this.elem.val(obj.title);
      },
    });
  }
}

// 填充表格数据
function fillTableData(earSide) {
  for(var i = 0; i < reportData.length; i++) {
    var item = reportData[i];
    if(item.earSide.toLowerCase() === earSide) {
      var table = $('.'+earSide+'-tb');
      var tempTr = table.find('.tdata-a');
      var testLinesETM = item.testLinesETM || [];
      if(testLinesETM.length) {
        table.find('.tdata-a').remove();
        // 组装图标是数据并画图
        initEarChart(earSide, testLinesETM);
      }
      // 测量数据
      testLinesETM.forEach(function(line, l) {
        var newTr = tempTr.clone();
        newTr.find('[t-name]').each(function(t, dom) {
          var name = $(dom).attr('t-name');
          if(name === 'result' && line[name] === '已拒绝') {
            $(dom).html('<span class="pink-bg">已拒绝</span>');
          } else {
            $(dom).text(line[name] || '');
          }
        })
        newTr.addClass('f-tr');
        if(l === 0) {
          table.find('tbody').prepend(newTr);
        } else {
          table.find('.f-tr:last').after(newTr);
        }
      })
      // 接受标准
      table.find('[fsy-name]').each(function(t, dom) {
        var name = $(dom).attr('fsy-name');
        $(dom).text(item[name] || '');
      })
      break;
    }
  }
}

function getDataByEarSide(earSide, testLinesETM) {
  var data = [];
  var lSymbolIcon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAAXNSR0IArs4c6QAAALhJREFUKFOVkDsKwlAQRe+swHSWrkI7O7HS3l4FcQEmqRKrJNqL+OnttdLSTtzEK1P5XMGVCQYeQQgOTDEzZ35XSK4BTAGkIpLCMZILACGAnZC0B//aGM47aLa8WESWypKMc2Ojy/aJcdKzCga5sckpuWMUdgtYwdzY2Mn58u2O3ILm3EbdUoBVWONyennKfyDJ+tUqQW5sVvNMqF+/juHNG8zaP+U5bx6YZP23gitH8KwieABAff8BCHuN2u1eTz4AAAAASUVORK5CYII=';
  var purpleShadow = [];
  var grayLowShadow = [];
  var grayHighShadow = [];
  for(var i = 0; i < testLinesETM.length; i++) {
    var item = testLinesETM[i];
    // DP1声强折线图，横坐标是F2，纵坐标是DP1
    let [x, formatV] = computeX(item.f2);
    data.push({
      value: [Number(x), item.dp1],
      symbol: earSide === 'left' ? `image://${lSymbolIcon}` : `emptyCircle`,
      symbolSize: earSide === 'left' ? 12 : 10,
      formatV: `${formatV}Hz, ${item.dp1}dB SPL`,
    })
    
    // 紫色阴影折线图，横坐标是F2，纵坐标是NF
    purpleShadow.push({
      value: [Number(x), item.nf]
    })

    // 灰色阴影最低点和最高点
    grayLowShadow.push({
      value: [Number(x), item.normalLow]
    })
    grayHighShadow.unshift({
      value: [Number(x), item.normalHigh]
    })
  }
  var series = [
    {
      type: 'line',
      data,
      color: "#7A4AA3",
    },  //测量数据
    {
      type: 'line',
      symbol: 'none',
      data: [...grayLowShadow, ...grayHighShadow],
      stack: 'Total',
      emphasis: {
        disabled: true
      },
      areaStyle: {
        color: '#D7D7D7',
      },
      lineStyle: {
        color: '#D7D7D7',
        width: 1
      },
    },  //正常范围灰色阴影部分
    {
      type: 'line',
      data: purpleShadow,
      symbol: 'none',
      lineStyle: {
        color: 'transparent'
      },
      areaStyle: {
        origin: 'start',
        color: '#C7B7D8',
      }
    },  //紫色阴影部分
  ];
  return {series};
}

// 格式化x轴的坐标数值
function computeX(orignX) {
  let x = Number(orignX/1000).toFixed(3);
  // interval：间距， multiple：临界值相乘的倍数， axis: 坐标实际值，resX: 当前坐标对应的值
  // interval = 坐标对应的值(resX)-上一个的坐标对应的值(resX)/中间间隔的格数，如第二条数据(1-0.5)/5=0.1
  // multiple = 上一个的坐标实际值(axis)/上一个的坐标对应的值(resX)，如第二条数据1/0.5=2
  let map = [
    {axis: 1, resX: 0.5, min: 0, max: 0.5, interval: 0.5, multiple: 0},
    {axis: 6, resX: 1, min: 0.5, max: 1, interval: 0.1, multiple: 2},
    {axis: 11, resX: 2, min: 1, max: 2, interval: 0.2, multiple: 6},
    {axis: 16, resX: 4, min: 2, max: 4, interval: 0.4, multiple: 5.5},
    {axis: 21, resX: 8, min: 4, max: 8, interval: 0.8, multiple: 4},
    {axis: 26, resX: 16, min: 8, max: 16, interval: 1.6, multiple: 2.625},
  ]
  let result = [x, x]; //原数值大小
  for(let item of map) {
    let {min, max, interval, multiple} = item;
    if(x >= min && x < max) {
      if(x == min) {
        result = [Number(Number(x * multiple).toFixed(3)), orignX];
      } else {
        let muliNum = Number(Number(min * multiple).toFixed(3)) + Number(((x - min)/interval).toFixed(3));
        result = [Number(muliNum.toFixed(3)), orignX];
      }
      break;
    }
  }
  return result;
}

// x网络线的样式
function xSplitLineStyle() {
  let solidColors = [];  //实线的颜色
  let dashedColors = [];  //虚线的颜色
  let solid = [1, 6, 11, 16, 21];  //这些位置显示实线网格线
  let dashed = [4, 9, 14, 19];  //这些位置显示虚线网格线
  for(let i = 0; i <= 24; i++) {
    if(solid.includes(i)) {
      solidColors.push('#e3e3e3');
    } else {
      solidColors.push('transparent');
    }
    if(dashed.includes(i)) {
      dashedColors.push('#e3e3e3');
    } else {
      dashedColors.push('transparent');
    }
  }
  return {solidColors, dashedColors};
}

// 图表
function initEarChart(earSide, testLinesETM) {
  let {series, visualMap} = getDataByEarSide(earSide, testLinesETM);
  var chartId = earSide + '-chart';
  if(!series) {
    return;
  }
  var xAxisStyle = xSplitLineStyle();
  // 指定图表的配置项和数据
  let option = {
    animation: false,
    grid: {
      y: 20,
      y2: 20,
      x2: 20,
      borderColor: earSide === 'left' ? 'blue' : 'red',
      borderWidth: 1,
      show: true,
    },
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
      formatter(param){
        return param.data.formatV;  
      },
      borderColor: earSide === 'left' ? 'blue' : 'red',
    },
    xAxis: [
      {
        name: 'Hz',
        nameLocation: 'middle',
        nameTextStyle: {
          padding: [8, 0, 0, 280],
          fontSize: '14px'
        },
        nameGap: 0,
        min: 0,
        max: 24,
        splitNumber: 24,
        axisLabel: {
          formatter:function(v){
            let value = ''
            switch(v) {
              case 1:
                value = 500;
                break;
              case 6:
                value = 1000;
                break;
              case 11:
                value = 2000;
                break;
              case 16:
                value = 4000;
                break;
              case 21:
                value = 8000;
                break;
            }
            return value;
          },
          color: '#000'
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: xAxisStyle.solidColors,
            type: 'solid',
          }
        },
      },
      {
        min: 0,
        max: 24,
        splitNumber: 24,
        axisLabel: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: xAxisStyle.dashedColors,
            type: 'dashed',
          }
        },
      }
    ],
    yAxis: [{
        name: 'dB SPL',
        nameLocation: 'end',
        nameTextStyle: {
          fontSize: '14px',
          color: '#000'
        },
        nameGap: 5,
        inverse: false,  //是否是反向坐标轴
        min: -30,
        max: 50,
        splitNumber: 10,
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#000',
          showMinLabel: false,  //是否显示最小的label
          showMaxLabel: false,  //是否显示最大的label
        },
        splitLine: {
          lineStyle: {
            color: ['transparent', '#d9d9d9', '#d9d9d9', '#d9d9d9', '#d9d9d9', '#d9d9d9', '#d9d9d9', '#d9d9d9', 'transparent']
          }
        },
        axisTick: {
          show: false,
        }
    },{
      show: false,
    }],
    series: series,
    visualMap
  };
  echarts.dispose(document.getElementById(chartId));
  let myChart = echarts.init(document.getElementById(chartId));
  myChart.setOption(option);
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = $("#dgfsy-rt-19").val() || ($(".body-desc .desc-con").html()!=='——'?$(".body-desc .desc-con").html():'') || '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: $('#dgfsy-rt-18').val() || $(".doctor-u").html() || '', //报告医生
    reportDate: $('#dgfsy-rt-4').text() || '',  //报告日期
    examTechnician: $('#dgfsy-rt-16').val() || $(".exam-u").html() || '',  //检查技师
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}