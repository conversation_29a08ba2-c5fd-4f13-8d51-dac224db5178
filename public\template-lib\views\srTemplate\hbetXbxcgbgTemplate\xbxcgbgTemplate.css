.singleDisEditReport.main-page{
  min-width: unset;
}
#xbxcgbg1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#xbxcgbg1 * {
  font-family: '宋体';
}
#xbxcgbg1 .xbxcgbg-edit{
  padding: 8px 12px;
}
#xbxcgbg1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#xbxcgbg1 .black-lb {
  color: #303133;
}
#xbxcgbg1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#xbxcgbg1 .blue-lb:hover {
  opacity: 0.8;
}
#xbxcgbg1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#xbxcgbg1 .text-size, .diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#xbxcgbg1 .text-size img, .diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#xbxcgbg1 .text-size img:hover, .diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#xbxcgbg1 .text-size .on, .diag-text-size .text-size .on {
  display: none;
}
#xbxcgbg1 .editor-area {
  padding: 4px 0;
  min-height: 100px;
}
.editor-area textarea {
  width: 100%;
  height: 100px;
  border: none;
  font-size: 16px;
}
#xbxcgbg1 .p-item + .p-item {
  margin-top: 12px;
}
.editor-area.default textarea{
  font-size: 16px;
}
.editor-area.large textarea{
  font-size: 18px;
}
.editor-area.larger textarea{
  font-size: 20px;
}
#xbxcgbg1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#xbxcgbg1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#xbxcgbg1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#xbxcgbg1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#xbxcgbg1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#xbxcgbg1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

*:disabled {
  background: #fff;
  color: unset;
}
textarea[readonly] {
  cursor: not-allowed;
}
#xbxcgbg1 .xbxcgbg-view {
  display: none;
}
[isview="true"] #xbxcgbg1 .xbxcgbg-edit {
  display: none;
}
[isview="true"] #xbxcgbg1 .xbxcgbg-view {
  display: flex;
}
#xbxcgbg1 .xbxcgbg-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 116px 56px;
  flex-direction: column;
  position: relative;
}
#xbxcgbg1 .xbxcgbg-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#xbxcgbg1 .xbxcgbg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}
#xbxcgbg1 .xbxcgbg-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
 span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
 }
}
#xbxcgbg1 .xbxcgbg-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
#xbxcgbg1 .xbxcgbg-view .hos-tit{
  font-family: '仿宋';
  font-size: 21px;
  line-height: 26px;
  font-style: normal;
  color: #000;
}
#xbxcgbg1 .xbxcgbg-view .sub-tit{
  font-size: 26px;
  color: #000;
  font-family: '宋体';
  margin-top: 20px;
  line-height: 30px;
}
#xbxcgbg1 .xbxcgbg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#xbxcgbg1 .xbxcgbg-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-line;
  word-break: break-all;
}
#xbxcgbg1 .xbxcgbg-view .hide-item {
  margin-bottom: 8px !important;
}
[data-key="xbxcgbg-rt-2,sampleSeen"]{
  min-height: 100px;
}
#xbxcgbg1 .xbxcgbg-view .hide-item .gray-txt {
  text-indent: 0 !important;
  padding-left: 28px;
  line-height: 32px !important;
}
.flex-column {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
  margin-top: 24px;
}
#xbxcgbg1 .xbxcgbg-view .report-img img {
  height: 240px;
  width: 320px;
  display: block;
  object-fit: contain;
}
#xbxcgbg1 .xbxcgbg-view .black-txt {
  color: #000;
  font-size: 16px;
}
#xbxcgbg1 .xbxcgbg-view .red-txt {
  color: #000000;
  font-size: 16px;
}
#xbxcgbg1 .xbxcgbg-view .bold {
  font-weight: bold;
}
#xbxcgbg1 .xbxcgbg-view .info-i {
  width: 160px;
  display: flex;
  flex-wrap: wrap;
}
#xbxcgbg1 .xbxcgbg-view .info-i + .info-i {
  margin-left: 8px;
}
#xbxcgbg1 .xbxcgbg-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#xbxcgbg1 .xbxcgbg-view .view-patient .p-item {
  margin-top: 8px;
}
#xbxcgbg1 .xbxcgbg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#xbxcgbg1 .xbxcgbg-view .rt-sr-body {
  padding-top: 8px;
}
#xbxcgbg1 .xbxcgbg-view .desc-con {
  /* padding: 8px 0; */
  display: none;
  margin-bottom: 20px;
}
#xbxcgbg1 .xbxcgbg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#xbxcgbg1 .xbxcgbg-view .desc-con {
  display: flex;
  align-items: baseline;
}
#xbxcgbg1 .xbxcgbg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i.w120 {
  width: 120px;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i.w150 {
  width: 150px;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i span {
  width: 70px;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i span:last-child {
  flex: 1;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#xbxcgbg1 .xbxcgbg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#xbxcgbg1 .xbxcgbg-view .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}
#xbxcgbg1 .xbxcgbg-view .tip-wrap .tip-text {
  flex: 1;
}
#xbxcgbg1 .xbxcgbg-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  margin-bottom: 12px;
  display: none;
  padding-left: 86px;
}
#xbxcgbg1 .xbxcgbg-view .item-img {
  width: 240px;
  height: 180px;
  border: 1px solid #eee;
  margin-bottom: 8px;
}
#xbxcgbg1 .xbxcgbg-view .item-img:nth-child(odd){
  margin-right: 12px;
}
#xbxcgbg1 .xbxcgbg-view .item-img:nth-child(even){
  margin-left: 12px;
}
#xbxcgbg1 .xbxcgbg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#xbxcgbg1 .p-item:nth-child(2) .text-size,#xbxcgbg1 .p-item:nth-child(3) .text-size,#xbxcgbg1 .p-item:nth-child(4) .text-size {
  display: none;
}
#xbxcgbg1 .p-item:nth-child(2) .editor-area,#xbxcgbg1 .p-item:nth-child(3) .editor-area {
  min-height: 72px;
}
#xbxcgbg1 .p-item:nth-child(2) textarea,#xbxcgbg1 .p-item:nth-child(3) textarea {
  height: 72px;
}
#xbxcgbg1 .xbxcgbg-view .blh-tit{
  text-align: right;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #xbxcgbg1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #xbxcgbg1 .xbxcgbg-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #xbxcgbg1 .xbxcgbg-view .view-head,
[entry-type="5"] #xbxcgbg1 .xbxcgbg-view .view-patient,
[entry-type="5"] #xbxcgbg1 .xbxcgbg-view .tip-wrap {
  display: none;
}
[entry-type="5"] #xbxcgbg1 .xbxcgbg-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #xbxcgbg1 .xbxcgbg-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
