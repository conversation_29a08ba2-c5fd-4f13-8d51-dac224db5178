/* 间距 */
.mb-12 {
  margin-bottom: 12px;
}
/* 颜色 */
.echart-h {
  background: #191975;
}
.table-h {
  background: #D6DFF7;
  color: #333333!important;
}
.bg-gray {
  background: #EAEAEA;
}
.req-flag {
  color: #E64545;
}
/* 宽度 */
.wd-134 {
  width: 134px;
}
.wd-182 {
  width: 182px;
}
/* gdfyempg页面 */
#gdfyempg1 {
  width: 780px;
  min-height: 1100px;
  position: relative;
  margin: 0 auto;
  padding-bottom: 40px;
  font-size: 14px;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
}
#gdfyempg1 .gdfyempg-h {
  text-align: center;
}
#gdfyempg1 .gdfyempg-h img {
  margin-left: -1px;
  margin-top: -1px;
}
#gdfyempg1 .gdfyempg-h h1 {
  font-size: 22px;
  line-height: 22px;
  font-weight: bold;
  color: #000;
  margin-top: 12px;
}
#gdfyempg1 .gdfyempg-h h2 {
  font-size: 20px;
  line-height: 20px;
  color: #000;
  margin-top: 12px;
}
#gdfyempg1 .gdfyempg-h .top-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 32px;
  margin: 8px 50px 0;
  border-top: 1px solid #606266;
  border-bottom: 1px solid #606266;
  font-size: 16px;
}
#gdfyempg1 .info-lb {
  color: #303133;
}
#gdfyempg1 .info-con {
  color: #000;
}
#gdfyempg1 .rpt-info {
  display: flex;
  justify-content: space-between;
  margin: 0 50px;
}
#gdfyempg1 .gdfyempg-b {
  margin-top: 12px;
  padding: 0 50px;
}
#gdfyempg1 .empg-box {
  position: relative;
  width: 680px;
  border: 1px solid #000;
}
#gdfyempg1 .empg-box .box-header {
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-bottom: 1px solid #000;
  font-size: 16px;
  font-weight: bold;
  color: #FFF;
}
#gdfyempg1 .echart-data {
  width: 426px;
  height: 426px;
  margin: 4px auto 8px;
}
#gdfyempg1 .echart-data img {
  width: 100%;
  height: 100%;
}
#gdfyempg1 .serial-no {
  width: 150px;
  height: 16px;
  position: absolute;
  top: 44%;
  right: 50px;
  transform: rotate(-90deg);
  font-size: 12px;
}
/* 表格 */
#gdfyempg1 .table-data tr {
  color: #333333;
  font-weight: 400!important;
}
#gdfyempg1 .table-data thead {
  height: 24px;
}
#gdfyempg1 .table-data thead th {
  font-weight: normal;
}
#gdfyempg1 .table-data thead th:first-child,#gdfyempg1 .table-data tbody tr td:not(:first-child) {
  border-right: 1px solid #000;
}
#gdfyempg1 .table-data thead th:not(:first-child),#gdfyempg1 .table-data tbody tr td:not(:first-child) {
  border-bottom: 1px solid #000;
}
#gdfyempg1 .table-data tbody tr td:first-child {
  line-height: 20px;
  border-right: 1px solid #000;
  text-align: left;
  padding: 0 10px 0 8px;
  box-sizing: border-box;
}
#gdfyempg1 .table-data tbody tr:last-child td {
  border-bottom: none!important;
}
#gdfyempg1 .table-data tbody tr td:last-child {
  border-right: none!important;
}
#gdfyempg1 .table-data tbody tr td:not(:first-child) {
  position: relative;
  padding-right: 8px;
}
#gdfyempg1 .con-flex {
  display: flex;
}
#gdfyempg1 .gdfyempg-b .remark-lb {
  display: inline-block;
  min-width: 54px;
  font-size: 18px;
  color: #828282;
}
#gdfyempg1 .remark-inp {
  flex: 1;
  height: 80px;
  border: 1px solid #DCDFE6;
}
#gdfyempg1 .gdfyempg-f {
  position: absolute;
  left: 50px;
  right: 50px;
  bottom:5px;
  border-top: 1px solid #F2F2F2;
  padding-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#gdfyempg1 .gdfyempg-f input[type="text"] {
  height: 36px;
  border-radius: 3px;
  padding: 0 4px;
  font-size: 14px;
  border: 1px solid #DCDFE6;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}