#jnzldd1 {
  font-size: 14px;
  width: 780px;
  margin: 0 auto;
  height: 1100px;
  padding: 48px 56px;

  * {
    font-family: '宋体';
    color: #000;
  }

  table {
    margin-top: 28px;
    width: 100%;
    background: #fff;
    border-color: #000;
    border-collapse: collapse; /* 合并边框 */
    margin-bottom: 36px;
    td,
    th {
      text-align: center;
      height: 28px;
    }
    thead {
      border-bottom: 1px solid #000;
    }
    tbody {
      tr {
        td {
          padding: 4px;
        }
      }
    }
  }
  .doctor {
    width: 296px;
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    line-height: 20px;
  }
  .impression {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px ;
  }
  .patLocal {
    text-align: right;

  }

  .userInfo {
    display: flex;
    align-items: center;
    margin-top: 10px;
    line-height: 20px;

    .userInfo-item {
      flex: 1;
    }
  }

  .eye-content {
    line-height: 20px;
    margin-top: 4px;
  }

  .bold {
    font-weight: bold;
  }

  .jbt {
    justify-content: space-between;
  }

  .mr-8 {
    margin-right: 8px;
  }

  .text-wrap {
    white-space: pre-wrap;
  }

  .rt-sr-r {
    margin-right: 4px;
  }

  .pl-24 {
    padding-left: 24px;
  }

  .pr-24 {
    padding-right: 24px;
  }

  .pt-8 {
    padding-top: 8px;
  }

  .pb-8 {
    padding-bottom: 8px;
  }

  .mt-24 {
    margin-top: 24px;
  }

  .mt-12 {
    margin-top: 12px;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .mt-6 {
    margin-top: 6px;
  }

  .mt-4 {
    margin-top: 4px;
  }

  .m-0-6 {
    margin: 0 6px;
  }

  .w-10 {
    width: 10px;
  }

  .w-15 {
    width: 15px;
  }

  .w-20 {
    width: 20px;
  }

  .w-25 {
    width: 25px;
  }

  .w-30 {
    width: 30px;
  }

  .w-35 {
    width: 35px;
  }

  .w-40 {
    width: 40px;
  }

  .w-45 {
    width: 45px;
  }

  .w-50 {
    width: 50px;
  }

  .w-55 {
    width: 55px;
  }

  .w-60 {
    width: 60px;
  }

  .w-65 {
    width: 65px;
  }

  .w-70 {
    width: 70px;
  }

  .w-75 {
    width: 75px;
  }

  .w-80 {
    width: 80px;
  }

  .w-85 {
    width: 85px;
  }

  .w-90 {
    width: 90px;
  }

  .w-95 {
    width: 95px;
  }

  .w-100 {
    width: 100px;
  }

  .w-105 {
    width: 106px;
  }

  .w-110 {
    width: 110px;
  }

  .w-115 {
    width: 115px;
  }

  .w-120 {
    width: 120px;
  }

  .w-125 {
    width: 125px;
  }

  .w-130 {
    width: 130px;
  }

  .w-135 {
    width: 135px;
  }

  .w-140 {
    width: 140px;
  }

  .w-145 {
    width: 145px;
  }

  .w-150 {
    width: 150px;
  }

  .w-155 {
    width: 155px;
  }

  .w-160 {
    width: 160px;
  }

  .w-165 {
    width: 165px;
  }

  .f-1 {
    flex: 1;
  }

  .f-1-5 {
    flex: 1.5;
  }

  .f-1-6 {
    flex: 1.6;
  }

  .f-2 {
    flex: 2;
  }

  .f-3 {
    flex: 3;
  }

  .fw-600 {
    font-weight: 600;
  }

  .a-center {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .a-start {
    display: flex;
    align-items: flex-start;
  }

  .flex {
    display: flex;

    span {
      &:first-child {
        white-space: nowrap;
      }
    }
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  .text-r {
    text-align: right;
  }

  .fs-36 {
    font-size: 36px;
  }

  .fs-24 {
    font-size: 24px;
  }

  .fs-20 {
    font-size: 20px;
  }

  .fs-16 {
    font-size: 16px;
  }

  .lh-20 {
    line-height: 20px;
  }

  .lh-23 {
    line-height: 23px;
  }
}