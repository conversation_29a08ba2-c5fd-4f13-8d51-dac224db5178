$(function () {
  window.initHtmlScript = initHtmlScript;
})
var examItemList = [
  { id: "13/16/18/21/22/X/Y染色体异常", title: "13/16/18/21/22/X/Y染色体异常" },
  { id: "子宫颈细胞TERC基因", title: "子宫颈细胞TERC基因" },
  { id: "神经母细胞瘤N-MYC基因", title: "神经母细胞瘤N-MYC基因" },
  { id: "C-MYC基因", title: "C-MYC基因" },
  { id: "乳腺癌HER-2基因", title: "乳腺癌HER-2基因" },
  { id: "CIC断裂基因", title: "CIC断裂基因" },
  { id: "ETV6断裂基因", title: "ETV6断裂基因" },
  { id: "EWSR1断裂基因", title: "EWSR1断裂基因" },
  { id: "FKHR断裂基因", title: "FKHR断裂基因" },
  { id: "PLAG1基因", title: "PLAG1基因" },
  { id: "SYT基因", title: "SYT基因" },
  { id: "USP6断裂基因", title: "USP6断裂基因" },
]
var sampleTypeList = [
  { id: "TCT采集细胞", title: "TCT采集细胞" },
  { id: "LCT采集细胞", title: "LCT采集细胞" },
  { id: "石蜡包埋组织", title: "石蜡包埋组织" },
  { id: "生理盐水收集细胞", title: "生理盐水收集细胞" },
  { id: "新鲜胎盘绒毛组织", title: "新鲜胎盘绒毛组织" },
  { id: "其他", title: "其他" },
]
var tzList = [
  { id: "GLP 16/GLP 22、GLP 13/GLP 21探计及CSP 18/CSP X/CSP Y探针", title: "GLP 16/GLP 22、GLP 13/GLP 21探计及CSP 18/CSP X/CSP Y探针" },
  { id: "GLP HER-2位点特异性探针与CSP17染色体着丝粒特异性探针", title: "GLP HER-2位点特异性探针与CSP17染色体着丝粒特异性探针" },
  { id: "GLP TERC位点特异性探针与CSF3染色体着丝粒特异性探针", title: "GLP TERC位点特异性探针与CSF3染色体着丝粒特异性探针" },
  { id: "Vysis lSI N-MYC位点特异性探针与CEP2染色体着丝粒特异性探针", title: "Vysis lSI N-MYC位点特异性探针与CEP2染色体着丝粒特异性探针" },
  { id: "GLP C-MYC位点特异性探针", title: "GLP C-MYC位点特异性探针" },
  { id: "CIC(19q13)位点特异性探针", title: "CIC(19q13)位点特异性探针" },
  { id: "Vysis LSI ETV6位点特异性探针", title: "Vysis LSI ETV6位点特异性探针" },
  { id: "GLP EWSR1位点特异性探针", title: "GLPEWSR1位点特异性探针" },
  { id: "FKHR(13q14)基因断裂探针", title: "FKHR(13q14)基因断裂探针" },
  { id: "PLAG1(8q12)位点特异性探针", title: "PLAG1(8q12)位点特异性探针" },
  { id: "GLP SYT 位点特异性探针", title: "GLP SYT 位点特异性探针" },
  { id: "USP6(17P13)基因断裂位点特异性探针", title: "USP6(17P13)基因断裂位点特异性探针" },
]
var hiddenIdList = ['lc-rt-2', 'lc-rt-4']
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var enterOptions = {};
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#fzbllc1 .lc-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    enterOptions = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initViewContainer()
    } else {
      initEditContainer()
    }
  }
  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}


// 回显预览内容
function initViewContainer() {
  curElem.find('.lc-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
  let tzShow = false;
  curElem.find('.lc-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === "resultAnalysis") {
      let { data = [] } = enterOptions;
      let valObj = data.filter(item => item.id === "lc-rt-30")[0] || {};
      let { child = [] } = valObj;
      if (child && child.length > 0) {
        let grandson = child[0].child;
        $(this).css("line-height", "24px")
        child[0].id !== 'lc-rt-33' ? $(this).text(grandson && grandson.length > 0 ? grandson[0].val : child[0].val) : "";
        if(child[0].id === "lc-rt-33"){
          $(this).text(grandson && grandson.length > 0 ? `${child[0].val}（${grandson.map(item => item.val).join("，")}）` : child[0].val)
          $(this).append(`，低于判断非嵌合体状态的异常标准（60%），出现这种异常的原因可能有胎儿染色体异常嵌合体，胎盘特异性染色体异常嵌合体及母体细胞污染等。\n`)
        }
      } else {
        $(this).parent().hide();
      }
    } else if (value) {
      key === 'lc-rt-6' || key === 'lc-rt-7' ? tzShow = true : '';
      $(this).html(value);
    } else {
      hiddenIdList.findIndex((item) => item === key) !== -1 ? $(this).parent().hide() : $(this).text('');
    }
    addIdToNodeByView(this, key, idAndDomMap);
  })
  tzShow ? "" : $('#jcAndTz').hide()
  // curElem.find('#fishShow [data-key]').each(function () {
  //   var key = $(this).attr('data-key');
  //   var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
  //   var value = publicInfo[key] || idAnVal;

  // })
  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for(let i = 0; i  < rptImageList.length; i++){
      if(rptImageList[i].src){
        imgHtml += `
        <div class="item-img">
          <img src="${rptImageList[i].src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find('.lc-view .rpt-img-ls').html(imgHtml);
      curElem.find('.lc-view .rpt-img-ls').css('display', 'flex');
    }
  }
}
function initIdLsit(num) {
  return `lc-rt-${num}`
}
// 回显编辑页
function initEditContainer() {
  let examItem = initIdLsit(2);
  let sampleType = initIdLsit(4);
  let tzSel = initIdLsit(6);
  let fishSel = initIdLsit(29);

  // initInpAndSel(examItem, examItemList, 320);
  // initInpAndSel(sampleType, sampleTypeList, 320);
  // initInpAndSel(tzSel, tzList, 460);
  $(`#${examItem}`).parent().removeClass('showInt')
  $(`#${sampleType}`).parent().removeClass('showInt')
  $(`#${tzSel}`).parent().removeClass('showInt')
  initInpAndSel(fishSel, [{ id: 'X，Y未见异常', title: "X，Y未见异常" }], 320);
  $('#yczbDescribe').click(function () {
    $('#lc-rt-33').prop("checked", !$('#lc-rt-33').prop("checked"));
    if (!$('#lc-rt-33').prop("checked")) {
      $('[name="yczb"]').each(function () {
        $(this).prop("checked", false)
      })
    }
  })
}
// 初始化输入选择下拉框
function initInpAndSel(id, optionList, len) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${id}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.id);
    },
    style: `width: ${len}px;`
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '';
  let fishList = []
  let easyId = ['lc-rt-1', 'lc-rt-3']
  $('.lc-edit .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length > 0) {
        if (curPid.id !== "lc-rt-8") {
          if (easyId.findIndex((item) => item === curPid.id) !== -1) {
            let grandson = child[0].child;
            str += grandson && grandson.length > 0 ? `${curPid.val}${grandson[0].val}\n` : `${curPid.val}${child[0].val}\n`
          } else if(curPid.id === "lc-rt-30"){
            let grandson = child[0].child;
            child[0].id !== "lc-rt-33" ? str += `${curPid.val}${grandson && grandson.length > 0 ? grandson[0].val : child[0].val}\n` : ''
            if(child[0].id === "lc-rt-33"){
              str += grandson && grandson.length > 0 ? `${curPid.val}${child[0].val}（${grandson.map(item => item.val).join("，")}）` : `${curPid.val}${child[0].val}`;
              str += "，低于判断非嵌合体状态的异常标准（60%），出现这种异常的原因可能有胎儿染色体异常嵌合体，胎盘特异性染色体异常嵌合体及母体细胞污染等。\n"
            }
          }else {
            str += `${curPid.val}${child.map((item) => item.val).join('')}\n`
          }
        } else {
          for (let i = 0; i < child.length; i++) {
            let son = child[i].child;
            if (son && son.length > 0) {
              fishList.push(`${child[i].name}：${son.map((item) => item.name + item.val).join('，')}`)
            } else {
              fishList.push(child[i].val)
            }
          }
          fishList && fishList.length > 0 ? str += `FISH表现：\n${fishList.join('\n')}\n` : ''
        }
      }
    }
  })
  str.endsWith('\n') ? str = str.slice(0, -1) : ''
  str ? rtStructure.impression = str : ''
}