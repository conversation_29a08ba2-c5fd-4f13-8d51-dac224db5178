var busTypeMap = {
  '1': 'examNo',
  '2': 'applyNo',
  '3': 'scheduleId',
  '4': 'studyNo',
}
var urlParams = {
  optId: '',
  optName: '',
  patternId: '',
  patDocId: '',
  applyNo: '',
  busId: '',
  busType: '',
  examClass: '',
  patientSource: '',
  examSubClass: '',
  name: '',
  age: '',
  sex: '',
}
var paramBusName = 'applyNo';
var applyInfo = {};
var patternInfo = {};
var patternList = [];
var rtStructure = null;
var docId = '';  //用于新增还是更新的判断
var isUpdate = true;   //申请单是否可更新
var oldPatDocId = '';
var oldResult = [];
var applyStatusRes = {};  //申请单的相关状态判断
var sourcePrefixUrl = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
var isPreview = location.href.indexOf('preview=1') > -1;
var pv = null;   //只显示打印页面
$(function() {
  for(var key in urlParams) {
    urlParams[key] = getParamByName(key) || '';
  }
  if(!urlParams.busId && !isPreview) {
    $wfMessage({
      content: 'busId不能为空'
    })
    return;
  }
  // 多种业务类型，通过busType确定参数名,默认applyNo
  if(urlParams.busType) {
    paramBusName = busTypeMap[urlParams.busType];
  }
  applyInfo = {
    examClass: urlParams.examClass,
    name: urlParams.name,
    age: urlParams.age,
    sex: urlParams.sex,
  }
  pv = getParamByName('pv') || null;
  if(pv === '1') {
    $(".layout-page").addClass('pv');
    isUpdate = false;
    addLoadCoverLayer();
    getApplyResult();
  } else {
    $(".layout-page").removeClass('pv');
    isUpdate = true;
    addLoadCoverLayer();
    // 获取模板列表
    getPatternList();
  }
  if(isPreview) {
    $(".layout-footer").hide();
    $(".layout-header").hide();
    $(".layout-page").css({
      'padding-top':'0',
      'padding-bottom':'0'
    })
  }
  
  // getParDoc(patDocId);
  rtStructure = new RtStructure('#agreement .main-content');  //实例化
  console.log('rtStructure', rtStructure);
  window.errorCallBack = function(widgetId) {
    $(".layout-body")[0].scrollTop = $('[id="'+widgetId+'"]')[0].offsetTop;
    if($('.rt-sr-inv').parents('.laydate-w').length) {
      $('.rt-sr-inv').parents('.laydate-w').css('borderColor', 'red');
    }
  }
})

// 获取模板列表
function getPatternList() {
  if(!urlParams.busId && !isPreview) {
    $wfMessage({
      content: '参数busId不能为空'
    })
    removeLoadCoverLayer();
    return;
  }
  if(isPreview && urlParams.patDocId) {
    getParDoc(urlParams.patDocId);
    return;
  }
  var params = {
    patternId: urlParams.patternId,
    patternType: '3',
    examClass: urlParams.examClass,
    examSubclass: urlParams.examSubClass,
    patientSource: urlParams.patientSource,
    pageSize: 0,
    pageNo: 0,
    mainFlag: '1'
  }
  params[paramBusName] = urlParams.busId;
  getPatternListHandler(params, {
    successCb: function(res) {
      var data = res.result || [];
      patternList = data;
      if(!urlParams.patDocId) {
        if(data.length) {
          var patDocId = data[0].patDocId;
          if(urlParams.patternId && urlParams.patternId !== data[0].patternId) {
            for(var i = 0; i < data.length; i++) {
              if(data[i].patternId === urlParams.patternId) {
                patDocId = data[i].patDocId;
                break;
              }
            }
          }
          // urlParams.patDocId = patDocId;
          oldPatDocId = patDocId;
        }
      } else {
        oldPatDocId = urlParams.patDocId;
      }
      console.log('patternList--oldPatDocId', oldPatDocId);
      getApplyResult();
    },
    errorCb: function() {
      getApplyResult();
    }
  })
}

// 获取文档内容, isChange是否是切换模板
function getParDoc(patDocId, isChange) {
  $("#agreement .main-content").html('');
  var params = {
    patDocId: patDocId,
  }
  patternInfo = {};
  getParDocHtmlHandler(params, {
    successCb: function(res) {
      var data = res.result || {};
      $(".ly-c").text(data.patternName || data.fileName || '');
      patternInfo = data;
      patternInfo.docId = docId;
      var html = data.patternDoc;
      if(window.loadTemplateScriptAndCss) {
        window.loadTemplateScriptAndCss(html);
      }
      if(html) {
        if(!isChange) {
          // getApplyResult(html);
          drawContentHtml(html, oldResult);
        } else {
          var changeResult = rtStructure.enterOptions ? rtStructure.enterOptions.resultData || [] : [];
          drawContentHtml(html, changeResult, 'edit');
        }
      } else {
        $wfMessage({
          content: '模板出错'
        })
        toggleBtn(isUpdate, 'edit');
      }
    },
    errorCb: function() {
      $wfMessage({
        content: '模板出错'
      })
      toggleBtn(isUpdate, 'edit');
    }
  })
}

// 获取已保存的结果数据,isSave是否保存后重新渲染
function getApplyResult(isSave) {
  if(isPreview) {
    getParDoc(oldPatDocId);
    return;
  }
  docId = '';
  oldResult = [];
  var params = {
    busType: urlParams.busType,
    busId: urlParams.busId,
    newestFlag: true,
    mainFlag: '1',
    patDocId: urlParams.patDocId ? oldPatDocId : ''
  }
  params[paramBusName] = urlParams.busId;
  getResJsonHandler(params, {
    successCb: function(res) {
      var allContent = res.result && res.result.length ? res.result[0] : {};
      var docParse = allContent.docContent ? JSON.parse(allContent.docContent) : {}
      var result = docParse.docContent ? (docParse.docContent.docAttr || []) : [];
      oldResult = result;
      if(JSON.stringify(allContent) !== '{}') {
        docId = allContent.docId;
        patternInfo.docId = docId;
        oldPatDocId = allContent.patDocId ? allContent.patDocId : oldPatDocId;
      }
      if(isSave) {
        drawContentHtml(patternInfo.patternDoc, oldResult, 'view')
      } else {
        getParDoc(oldPatDocId);
      }
    },
    errorCb: function() {
      if(!isSave) {
        getParDoc(oldPatDocId);
      }
    }
  })
}

// 渲染页面
function drawContentHtml(html, result, editType) {
  console.log('editType',editType);
  if(!result.length && isUpdate) {
    editType = 'edit';
    rtStructure.rebuildStructureReport({htmlContent:html, resultData:result, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, patternInfo: patternInfo});
  } else {
    if(!editType) {
      // 预览
      editType = 'view';
      rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, patternInfo: patternInfo});
    } else {
      if(editType === 'edit') {
        rtStructure.rebuildStructureReport({htmlContent:html, resultData:result, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, patternInfo: patternInfo});
      } else {
        rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, patternInfo: patternInfo});
      }
    }
  }
  toggleBtn(isUpdate, editType)
}

// 点击编写按钮事件
function editApply() {
  toggleBtn(true, 'edit');
  var html = rtStructure.enterOptions.htmlContent;
  var result = rtStructure.enterOptions.resultData;
  var applyInfo = rtStructure.enterOptions.publicInfo;
  drawContentHtml(html, result, 'edit')
}

// 调整操作按钮的显示隐藏
function toggleBtn(isUpdate, type) {
  console.log('7999',isUpdate, type);
  // 不可编辑全部隐藏
  if(!isUpdate) {
    $("#editBtn, #saveBtn, #tempBtn").hide();
    if(type === 'view') {
      $("#printBtn").show().attr('disabled', false);
    }
  } else {
    if(type === 'edit') {
      $("#saveBtn, #tempBtn").show().attr('disabled', false);
      $("#editBtn, #printBtn").hide();
      editBtn(false)
    } else {
      $("#saveBtn, #tempBtn").hide();
      $("#editBtn, #printBtn").show().attr('disabled', false);
      editBtn(true)
    }
  }
}

/**
 * 提交
 * 模板内维护的相关方法
 * 1.保存前业务的调用方法：saveSrPreCb，返回true / false
 * 2.保存成功后业务的调用方法：saveSrSuffixCb，不接收返回值
 */
async function saveApplyResult(vm) {
  var preBool = true;
  try {
    if(saveSrPreCb && typeof saveSrPreCb === 'function') {
      preBool = await saveSrPreCb();
    }
  } catch(e) {
    console.error('出错-->', e)
  }
  if(!preBool) {
    return;
  }
  var ele = $(vm);
  var dataObj = createResultData();
  var resultData = dataObj.resultData
  var params = {
    optType: '0',  
    optId: urlParams.optId,
    optName: urlParams.optName,
    patientInfo: applyInfo,
    resultData: resultData,
    patternInfo: patternInfo,
    busType: urlParams.busType,
    busId: urlParams.busId,
    docId: docId,
    mainFlag: '1'
  };
  var postParams = saveParams(params);
  ele.attr('disabled', true);
  saveHandler(postParams, {
    successCb: function() {
      try {
        if(saveSrSuffixCb && typeof saveSrSuffixCb === 'function') {
          saveSrSuffixCb();
        } else {
          getApplyResult(true);
        }
      } catch(e) {
        getApplyResult(true);
        console.error('出错-->', e)
      }
      
      ele.attr('disabled', false);
    },
    errorCb: function() {
      ele.attr('disabled', false);
    }
  })
}

// 打印
function printApply() {
  $(".wfmsgbox").remove();
  $("#agreement").addClass('print-status');
  window.print();
  setTimeout(function() {
    $("#agreement").removeClass('print-status');
  }, 50)
}
// 打开模板
function openTemplateHandler() {
  var html = templateListHtml(patternList, selectPattern)
  drawDialog({
    title: '选择模板',
    modal: true,
    content: html,
    style: {
      'width': '413px',
    }
  });
}

// 模板列表
function templateListHtml(data, successFn) {
  var html = '<div class="item-table" style="border:1px solid #C8D7E6;width:388px;">';
  html += '<style>.selText{color:#1885F2}</style>';
  html += '<div class="t-body" style="background: #F5F7FA;overflow: auto;height:300px">';
  if(data && data.length) {
    for(var i = 0; i < data.length; i++) {
      html += '<div id="'+data[i].patDocId+'" onclick="changeTempSelf(this)" style="padding: 8px 10px;cursor: pointer;" class="'+(patternInfo.patDocId === data[i].patDocId?'selText':'')+'">'+data[i].patternName+'</div>';
    }
  } else {
    html += '<div style="color:#666;padding:5px">暂无数据</div>';
  }
  html += '</div>';
  html += '</div>';
  html += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  html += '<button onclick="selectPattern(this, '+successFn+')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  html += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">关闭</button>';
  html += '</div>';
 return html;
}

// 切换模板
function selectPattern() {
  var patDocId = rtDialog.find('.selText').attr('id');
  if(!patDocId) {
    $wfMessage({
      content: '请选择模板'
    })
    return;
  }
  if(patDocId === patternInfo.patDocId) {
    $wfMessage({
      type: 'warn',
      content: '该模板正在使用'
    })
    return;
  }
  getParDoc(patDocId, true);
  removeRtDialog();
}

function changeTempSelf(ele) {
  $(ele).addClass('selText').siblings().removeClass('selText');
}

// 计费后处理配置中不可编辑的项
function setDisabledByConfig(applyStatusRes) {
  if(!applyStatusRes || typeof applyStatusRes !== 'object') {
    return;
  }
  // 检查项目不可编辑
  if(applyStatusRes.cantEditItem === '1') {
    $('[onclick="addMarkInfoHandler(\'examItem\')"]').hide();
    $(".eItemEditPart [onclick]").hide();
  }
}