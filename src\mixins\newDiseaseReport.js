import api from '@/config/api.js';
import JsPD<PERSON> from "jspdf";
import html2canvas from "html2canvas";
import { request } from '@/utils/common.js';
import {allSreportData} from '@/config/allSreportData.js'
import * as constant from'@/config/constant.js'
import {inferBirthdayByAge, inferAgeByBirthday} from 'rt-common-functions'
import {getOrSetCurReportLocal, initLocalParams} from '@/common/encryptCommonFun.js'
import $ from 'jquery';
export default {
  data() {
    return {
      rtStructure: null,  //报告实例对象
      widgetHtml: '',  //静态html控件内容
      resultData: [],  //存放填写的数据
      idMapResult: {}, //填写的数据对应id对象
      allContent: {},
      // isSubmit 0保存，1提交，2审核，3复审，对应生成结果的optType
      // previewType 1预览，2预览并上传pdf，3预览并打印
      rptOuterTypeMap: {
        '1': {name: '保存回调', isSubmit: '0', funName: 'srSave', previewType: '1'},
        '10': {name: '保存成功/失败状态通知回调', isSubmit: '0', funName: '', previewType: '1'},
        '2': {name: '提交回调', isSubmit: '1', funName: 'srSubmit', previewType: '1'},
        '20': {name: '提交成功/失败状态通知回调', isSubmit: '1', funName: '', previewType: '1'},
        '3': {name: '确认回调', isSubmit: '2', funName: '', previewType: '2'},
        '30': {name: '确认成功/失败状态通知回调', isSubmit: '2', funName: '', previewType: '2'},
        '4': {name: '编辑回调', isSubmit: '', funName: 'srEdit'},
        '5': {name: '打印回调', isSubmit: '', funName: '', previewType: '3'},
        '50': {name: '打印成功/失败状态通知回调', isSubmit: '', funName: ''},
        '9': {name: '语音结构化控制指令'},
        '11': {name: '手写签名 签署端-提交'},
        '12': {name: '手写签名 签署端-重签'},
        '13': {name: '手写签名 发起端-发起'},
        '14': {name: '手写签名 发起端-重签'},
      },
      contentListTop: {},
      patternInfo: {},
      docOrign: null,  //该字段只在非普通单病种结构化下出现，用于存放设备推送的原始数据，如听力报告
      deviceAllData: {},  //设备原始所有数据
      localQueryParams: {},   //当前报告缓存的数据
      localKey: '',  //当地报告缓存的标识
      deviceInfo: {},  //听力设备传入的信息，用于显示公共数据部分
      outerParam: null,   //外部调公共方法的其他入参
      outerAllParam: {},   //外部调公共方法的所有入参
      saveDocResult: {},  //保存完节点的结果数据集合
      asyncAjaxCallBack: null,  //模板内异步操作的回调
    }
  },
  computed: {
    isRTBrowser() {
      return this.$store.state.isRTBrowser;
    },
    token() {
      return this.$store.state.token;
    },
    ypacsRptInfo() {
      return this.$store.state.report.ypacsRptInfo;
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
    isFromYunPacs() {
      return this.$store.state.isFromYunPacs || this.localQueryParams.fromYunPacs==='1' || this.$route.query.fromYunPacs==='1';
    },
    sourceType() {
      // source=，1从pacs打开 2从其他第三方进来
      return this.localQueryParams.source || this.$route.query.source || this.localQueryParams.trace || this.$route.query.trace || '';
    },
    trace() {
      // trace =1 查看痕迹
      return this.localQueryParams.trace === '1' || this.$route.query.trace === '1';
    },
    isPreview() {
      return this.$store.state.isPreview || this.localQueryParams.preview==='1' || this.$route.query.preview==='1';
    },
    fromWebpacs() {
      return this.localQueryParams.webpacs === '1';
    },
    newOne() {
      return this.localQueryParams.newOne === '1';  //是否新增
    },
  },
  created() {
    initLocalParams(this);
  },
  mounted() {
    window.addEventListener("message", this.parentDataListener);
    // 将vue中的方法赋值给window，供外部js调用
    if(this.submitForm) {
      window.saveOrSubmitHandler = this.submitForm;
    }
    if(this.editForm) {
      window.editSrHandler = this.editForm;
    }
    if(this.srCommonOuterOptHandler) {
      window.srCommonOuterOptHandler = this.srCommonOuterOptHandler;
    }
    if(this.localQueryParams.child !== '1') {
      window.localStorage.removeItem(constant.SR_ALL_PATDOCID);
      window.localStorage.removeItem(constant.SR_ALL_CONTENT);
    }
    if(this.fromWebpacs && this.localQueryParams.rptType === '0' && this.localQueryParams.entryType === '0') {
      window.localStorage.removeItem(constant.SR_SAVE_PARAMS);
    }
  },
  destroyed() {
    delete window.saveOrSubmitHandler;
    delete window.editSrHandler;
    delete window.srCommonOuterOptHandler;
    delete window.printPageBreakHandler;
    delete window.loadedHtmlByJsHandler;
    delete window.loadReportContentOuter;
    delete window.saveDocToUpdateReportId;
    window.removeEventListener("message", this.parentDataListener);
  },
  methods: {
    // 监听加载图片
    parentDataListener(res) {
      let {message, data} = res.data || {};
      if(message === 'sendRptImg') {
        this.rptImageList && this.$store.commit('report/setRptImage', [...this.rptImageList, data])
      } else if(message === 'sendRptInfo') {
        console.log('监听消息sendRptInfo')
        data.isRTBrowser && this.$store.commit('setIsRTBrowser', data.isRTBrowser)
        this.$store.commit('setIsFromYunPacs', data.fromYunPacs === undefined ? true : data.fromYunPacs);
        data.token && this.$store.commit('setToken', data.token);
        data.userInfo && this.$store.commit('setUserInfo', data.userInfo);
        let examInfo = { ...(data.examInfo || {}), ...(data.examDoc || {})}
        this.$store.commit('report/setYpacsRptInfo', examInfo);
      } else if(message === 'toGetPrintHtml') {
        this.printFlag = true;
      } else if(message === 'quoteAiData') {   //引用AI数据
        let {docContent = {}} = data.structReport;
        let {docAttr = []} = docContent;
        let {ele, idAndDomMap} = this.rtStructure || {}
        if(this.quoteAiDataHandler) {
          this.quoteAiDataHandler(docContent);
        } else {
          if(docAttr && docAttr.length && ele) {
            docAttr.forEach((item, i) => {
              for(let attr of item) {
                if(!attr.code || !attr.val) {
                  continue;
                }
                let domList = $(ele).find('[code="'+attr.code+'"]');
                if(domList && domList.length) {
                  for(let dom of domList) {
                    let domId = $(dom).attr('id') || $(dom).closest('.w-con').attr('id')
                    if($(dom).hasClass('rt-sr-r') || $(dom).hasClass('rt-sr-ck')) {
                      if($(dom).val() === attr.val) {
                        rtStructure.setFormItemValue(dom, attr.val)
                        idAndDomMap[domId].value = attr.val;
                      }
                      break;
                    } else {
                      rtStructure.setFormItemValue(dom, attr.val)
                      idAndDomMap[domId].value = attr.val;
                    }
                  }
                }
              }
            })
          }
        }
      } else if(message === 'rebuildSrReportByRedis') {
        if(data.length) {
          let cutentRedis = data.filter(item => item.patDocId === this.patientInfo.patDocId)[0];
          if(cutentRedis) {
            window.localStorage.setItem(constant.SR_CURENT_REDIS, JSON.stringify(cutentRedis));
            window.location.reload();
          }
        }
      } else if(message === 'finishAsyncAjax') {  //模板内异步操作完成
        if(this.asyncAjaxCallBack && typeof this.asyncAjaxCallBack === 'function') {
          // if(this.loadedHtml) {
          //   return;
          // }
          // this.loadedHtml = true;
          this.asyncAjaxCallBack();
        }
      } else if(message === 'srHtmlLoaded') {  //模板加载完成
        if(this.asyncAjaxCallBack && typeof this.asyncAjaxCallBack === 'function') {
          // if(this.loadedHtml) {
          //   return;
          // }
          // this.loadedHtml = true;
          this.execCallBack();
        }
      }
    },
    // 获取患者信息
    getPatientInfo() {
      let {
        name = '', sex = '', age = '', 
        exam_no = '', examNo = '',
        pat_local_id = '', patLocalId = '', 
        pat_doc_id = '', patDocId = '', 
        opt_id = '', optId = '',
        reqPhysician = '', req_physician = '', 
        examDate = '', exam_date = '',
        optName = '', busId = '', oldExamNo = '', patternId = '', docNo, reportNo, busType = '1', birthDate = '', birthDay = '', docId,
        oldDocId = '', changePattern = false, viewId = '1'} = {...this.localQueryParams};
      this.patientInfo = {
        oldExamNo: oldExamNo,
        oldDocId,
        examNo: exam_no || examNo || busId,
        busId: exam_no || examNo || busId,
        busType: decodeURIComponent(busType) || '',
        sex: sex,
        name: decodeURIComponent(name) || '',
        age: decodeURIComponent(age) || ((birthDate || birthDay) ? (inferAgeByBirthday(birthDate || birthDay)) : ''),
        patLocalId: decodeURIComponent(pat_local_id) || decodeURIComponent(patLocalId) || '',
        patDocId: decodeURIComponent(pat_doc_id) || decodeURIComponent(patDocId) || '',
        patternId: decodeURIComponent(patternId) || '',
        optId: decodeURIComponent(opt_id) || decodeURIComponent(optId) || '',
        optName: decodeURIComponent(optName) || '',
        reqPhysician: decodeURIComponent(reqPhysician) || decodeURIComponent(req_physician) || '',
        examDate: decodeURIComponent(examDate) || decodeURIComponent(exam_date) || '',
        birthDate: decodeURIComponent(birthDate || birthDay) || (age ? (inferBirthdayByAge(parseInt(age).toString())) : ''),
        birthDay: decodeURIComponent(birthDate || birthDay) || (age ? (inferBirthdayByAge(parseInt(age).toString())) : ''),
        viewId: decodeURIComponent(viewId) || '1',
        changePattern: changePattern,
        docId,
      }
      if(!this.patientInfo.patternId && this.patientInfo.patDocId && allSreportData[this.patientInfo.patDocId]) {
        this.patientInfo.patternId = this.patientInfo.patDocId;
      }
      if(docNo || reportNo) {
        this.patientInfo.docNo = decodeURIComponent(docNo || reportNo) || '';
      } else {
        // 判断是否为pacs调用
        if(this.sourceType === '1' && !this.fromWebpacs && !this.fromWebpacs) {
          this.patientInfo.docNo = docNo || reportNo || '1';
        }
      }
      window.patientInfo = this.patientInfo;
      console.log('patientInfo--->', this.patientInfo);
    },

    async getData(examNo, type) {
      if(this.isPreview) {
        return [];
      }
      if(this.newOne || (!examNo && !this.patientInfo.examNo)) {
        return [];
      }
      // 查看痕迹的数据从痕迹列表页带进来
      if(this.trace) {
        let traceContent = window.parent.traceContent || {};
        let {docAttr = [], allContent = {}} = traceContent;
        this.allContent = allContent;
        return docAttr || [];
      }
      if(this.fromWebpacs && !this.localQueryParams?.docId) {
        return [];
      }
      let params = {
        examNo: examNo || this.patientInfo.examNo,
        docId: this.localQueryParams?.child === '1' ? '' : this.patientInfo.docId,
        busType: this.patientInfo.busType || '1',
        busId: examNo || this.patientInfo.examNo,
        patternId: this.patientInfo.patternId,
        patDocId: this.patientInfo.patDocId,
        newestFlag: true,
        docNo: this.patientInfo.docNo,
      };
      // pacs下不传
      if(this.sourceType === '1' && !this.fromWebpacs && !this.fromWebpacs) {
        delete params.docNo;
      }
      if(this.localQueryParams.child !== '1') {
        params.mainFlag = '1';
      }
      // 编辑下引用历史数据
      if(this.patientInfo.oldExamNo && this.localQueryParams.entryType === '0') {
        params.examNo = this.patientInfo.oldExamNo;
        params.busId = this.patientInfo.oldExamNo;
        params.docId = this.patientInfo.oldDocId;
      }
      // 定时存储至redis
      let getFromRedisStorage = window.localStorage.getItem(constant.SR_CURENT_REDIS) ? JSON.parse(window.localStorage.getItem(constant.SR_CURENT_REDIS)) : null;
      // 从缓存中取数据，用于子模板的情况
      if(!getFromRedisStorage) {
        let srAllContent = JSON.parse(window.localStorage.getItem(constant.SR_ALL_CONTENT) || '{}');
        if(srAllContent[this.patientInfo.patDocId] && srAllContent[this.patientInfo.patDocId].isChild !== '1' && srAllContent[this.patientInfo.patDocId].docContent) {
          let {docAttr = [], docOrign} = srAllContent[this.patientInfo.patDocId].docContent;
          this.contentListTop = srAllContent[this.patientInfo.patDocId];
          this.postTopWinMessage(true);
          // VNG存在空数据查不出contentList,则直接打开模板
          if(docOrign || this.patientInfo.patDocId === 'V000') {
            this.docOrign = docOrign || {};
            await this.tlRptContentHandler();   //处理听力报告的交互
          }
          return docAttr;
        }
      }
      let res = null;
      if(!getFromRedisStorage) {
        if(this.patientInfo.changePattern) {
          // delete params.docId;
          return [];
        }
        res = await request(api.getContentList, 'post', params);
        // this.postTopWinMessage(true);
        if(!res || res.status !== '0') {
          this.$message({
            type: 'error',
            message: res && res.message ? res.message : "操作失败",
          })
          return [];
        }
  
        // 尚未填写报告
        if(!res.result || !res.result.length) {
          if(this.patientInfo.patDocId === 'V000') {
            this.docOrign = {};
            await this.tlRptContentHandler(true);
            return;
          }
          if(type === 'view' && this.editForm) {
            window.top.postMessage({
              message: 'dataLoadFail',
              data: {}
            }, '*');
            // this.editForm();
          }
          return [];
        }
      }
      if(getFromRedisStorage) {
        // 和接口数据格式保持一致
        getFromRedisStorage['docContent'] = JSON.stringify({'docContent':getFromRedisStorage['docContent']});
        window.localStorage.removeItem(constant.SR_CURENT_REDIS);
      }
      let list = !getFromRedisStorage ? (res.result || []) : [getFromRedisStorage];
      let data = [];
      if(!this.patientInfo.patDocId) {
        data = list;
      } else {
        data = list.filter(item => item.patDocId && (item.patDocId === this.patientInfo.patDocId || item.patDocId === '1' || !allSreportData[item.patDocId]));
        // 如果条件都不符合，但是又结果数据，则取结果数据的第一条，用于跳转至该结果集patDocId的报告
        if(data.length === 0) {
          data = list;
        }
      }
      let allContent = data && data.length ? data[0] : {};
      this.contentListTop = allContent;
      let {docContent = '', docId = '', patDocId = '', patternId = '', docNo = '' } = allContent;
      let docParse = docContent ? JSON.parse(docContent) : {};
      let {srChildPatternContent = [], docOrign} = docParse.docContent || {};
      this.allContent = docParse;
      let result = docParse.docContent ? (docParse.docContent.docAttr || []) : [];
      // 非引用历史检查才显示
      if(!this.localQueryParams.oldExamNo) {
        this.docId = docId;
        this.patternInfo.docId = docId;
        this.patternInfo.docNo = docNo ? docNo : (this.patternInfo.docNo || '');
        if(docNo && !this.patientInfo.docNo && !this.fromWebpacs) {
          this.patientInfo.docNo = docNo;
        }
      }
      // this.setOriginDataInStorage(result);
      // VNG存在空数据查不出contentList,则直接打开模板
      if(docOrign || this.patientInfo.patDocId === 'V000') {
        this.docOrign = docOrign;
        await this.tlRptContentHandler();
      }
      if(!this.localQueryParams.oldExamNo) {
        patDocId && this.$set(this.patientInfo, 'patDocId', patDocId);
        patternId && this.$set(this.patientInfo, 'patternId', patternId);
      }
      if(this.localQueryParams.child !== '1' && srChildPatternContent.length) {
        let srIds = [];
        let srObj = {};
        srChildPatternContent.forEach(srItem => {
          srIds.push(srItem.patDocId);
          srObj[srItem.patDocId] = srItem;
          srObj[srItem.patDocId]['isChild'] = '1';
          srObj[srItem.patDocId]['patternId'] = this.localQueryParams.oldExamNo ? this.localQueryParams.patternId : patternId;
          srObj[srItem.patDocId]['docContent']['busInfo'] = {busId: this.patientInfo.busId};
        })
        window.localStorage.setItem(constant.SR_ALL_CONTENT, JSON.stringify(srObj));
        window.localStorage.setItem(constant.SR_ALL_PATDOCID, JSON.stringify(srIds));
      }
      if(!data.length && list && list.length && allSreportData[this.patientInfo.patDocId]) {
        if(list[0].patDocId !== '1' && list[0].patDocId!==this.patientInfo.patDocId) {
          this.$message.error(`结果集模板Id(${list[0].patDocId})与当前模板Id(${this.patientInfo.patDocId})不符`);
        }
      }
      return result;
    },

    setOriginDataInStorage(data) {
      let {rptType, entryType = '0'} = this.localQueryParams || {};
      if(this.fromWebpacs && entryType === '0' && rptType === '0') {
        let srRedis = JSON.parse(window.localStorage.getItem(constant.SR_SAVE_PARAMS) || '{}');
        let redisKey = `${this.patientInfo.busId+'_'+(this.patientInfo.docNo || this.docNo)}`;
        srRedis[redisKey] = data;
        window.localStorage.setItem(constant.SR_SAVE_PARAMS, JSON.stringify(srRedis));
      }
    },

    // 听力报告数据不完整时结合地址栏入参进行补充
    fillTLReportData(list) {
      let reportType = this.patientInfo.patDocId || this.contentListTop.patDocId;
      if(!reportType) {
        return;
      }
      if(!this.patientInfo.patDocId) {
        this.patientInfo.patDocId = reportType;
      }
      let {patInfo, reportInfo = {}} = list || {};
      if(!patInfo || !reportInfo.reportType) {
        let patInfoObj = {};
        let reportInfoObj = {};
        patInfoObj = {
          name: this.patientInfo.name || '', 
          age: this.patientInfo.age || '', 
          gender: this.patientInfo.gender || this.patientInfo.sex || '',
          birthday: this.patientInfo.birthday || this.patientInfo.birthDate
        };
        reportInfoObj = {
          examDate: this.patientInfo.examDate || '',
          examStaff: this.patientInfo.optName || '',
          examTime: this.patientInfo.examTime || '',
          reportDate: this.patientInfo.reportDate || '',
          reportNo: this.patientInfo.docNo,
          reportStaff: this.patientInfo.optName || '',
          reportTime: this.patientInfo.reportTime || '',
          reportType: reportType || '',
        };
        list.patInfo =  {
          ...patInfoObj,
          ...(patInfo || {}),
          ...(this.patientInfo.patDocId === 'V000' ? (list || {}) : {})
        };
        list.reportInfo = {
          ...reportInfoObj,
          ...(reportInfo || {}),
          ...(this.patientInfo.patDocId === 'V000' ? (list || {}) : {})
        }
        this.docOrign = list;
      }

      // 组装设备的信息
      this.deviceInfo = {
        name: this.docOrign.patInfo.name,
        age: this.docOrign.patInfo.age, 
        sex: this.docOrign.patInfo.sex,
        birthDate: this.docOrign.patInfo.birthday,
        reporter: this.docOrign.reportInfo.reportStaff,
        reportDate: this.docOrign.reportInfo.reportDate,
        examDate: this.docOrign.reportInfo.examDate,
        examTechnician: this.docOrign.reportInfo.examStaff,
      }
    },

    // 获取听力报告设备原始数据
    async getDeviceOriginData() {
      if(!this.contentListTop.busId || this.isPreview) {
        return;
      }
      this.deviceAllData = {};
      let params = {
        busId: this.contentListTop.busId,
        busType: this.contentListTop.busType,
        docNo: this.contentListTop.docNo,
      }
      let res = await request(api.deviceOriginData, 'post', params);
      if(!res || res.status !== '0') {
        this.fillTLReportData(this.docOrign);
        this.$message.error(res && res.message ? res.message : '操作失败');
        return;
      }
      this.deviceAllData = JSON.parse(res.result || '{}');
      let {docContent, patternId, patDocId} = this.deviceAllData;
      if(docContent && patternId) {
        let {docOrign = {}} = docContent;
        if(patDocId !== this.contentListTop.patDocId || 
            patternId !== this.contentListTop.patternId || 
            this.docOrign.updateFlag === '1') 
        {
          // this.docOrign为更新后的数据，docOrign为实际的原始数据
          if(this.patientInfo.patDocId === 'V000') {
            this.docOrign = { ...docOrign, ...this.docOrign };
          } else {
            this.docOrign = docOrign;
          }

          // 如果存在原始设备数据，可能存在数据上传覆盖的问题，重置为使用设备中的模板
          patternId && (this.patientInfo.patternId = patternId);
          patDocId && (this.patientInfo.patDocId = patDocId);
          if(patDocId && !patternId && allSreportData[patDocId]) {
            this.patientInfo.patternId = patDocId;
          }
        }
      }
      this.fillTLReportData(this.docOrign);
    },

    async tlRptContentHandler(noRpt) {
      if(noRpt) {  //没有报告时就触发
        this.fillTLReportData(this.docOrign);
      }
      await this.getDeviceOriginData();
    },

    postTopWinMessage(status) {
      window.parent.postMessage({
        message: 'loadStatusNotice',
        data: status
      }, '*');
    },

    // 模板及数据加载完成后相关操作
    // loadViewScript预览下是否需要加载js
    async loadedHandler(param) {
      this.rtStructure = null;
      this.asyncAjaxCallBack = null;
      let {docContent = {}} = this.allContent || {};
      let jsonDocContent = JSON.parse(JSON.stringify(docContent));
      delete jsonDocContent.docAttr;
      let {type, data, mainId, loadViewScript, callBack, oldDataText = false, srcUrl, 
          widgetHtml = '', publicInfo, checkboxSort = false, examInfo = {}, newSr = true } = param;
      let rtStructure = new RtStructure(mainId + " #" + this.domId);  //实例化
      this.rtStructure = rtStructure;
      this.widgetHtml = widgetHtml || document.querySelector(mainId + " #" + this.domId)?.innerHTML;  //病种表单内容
      let widgetJson = data;  // 结果json
      // let arr = this.treeToArr(JSON.parse(JSON.stringify(widgetJson)));
      // // 数组转以id为key的对象，方便使用
      // let resultDataObj = {};
      // arr.forEach(item => {
      //   resultDataObj[item.id] = item;
      // });
      // 去除纯空格的内容
      ['description', 'impression', 'recommendation', 'examParam'].forEach(k => {
        if (publicInfo[k] && !/\S/.test(publicInfo[k])) {
          publicInfo[k] = '';
        }
      });
      // 回显报告内容值
      let rptParams = {
        htmlContent:this.widgetHtml, 
        resultData:widgetJson, 
        // resultDataObj:resultDataObj, 
        oldDataText, 
        srcUrl, 
        publicInfo, 
        checkboxSort, 
        examInfo, 
        ...param, 
        ...jsonDocContent, 
        patternInfo: this.patternInfo,
        deviceAllData: this.deviceAllData,   //设备所有原始数据
        docOrign: this.docOrign,
        rptImageList: this.rptImageList || [],
        isSavedReport: Boolean(data && data.length) && this.contentListTop?.source !== '1', //source=1为设备数据
        resDataSource: this.contentListTop?.source,
        allContentListData: this.contentListTop || {},
      };
      if(type === 'edit') {
        await rtStructure.rebuildStructureReport(rptParams);
      } else if(type === 'view') {
        // 预览
        await rtStructure.previewStructureReport({...rptParams, loadViewScript:loadViewScript});
      }
      if(callBack && typeof callBack === 'function') {
        this.asyncAjaxCallBack = callBack;
        // 不存在异步操作直接执行
        // if(!newSr) {
        //   setTimeout(() => {
        //     callBack();
        //   }, 1000)
        // }else {
        //   if(type !== 'view') {
        //     callBack();
        //   } else {
        //     console.log(rtStructure?.diffConfig);
        //     setTimeout(() => {
        //       if(!rtStructure?.diffConfig || !rtStructure?.diffConfig?.asyncAjax) {
        //         callBack();
        //       }
        //     }, 500);
        //   }
        // }
      }
    },
    // 执行回调
    execCallBack() {
      if(!this.rtStructure?.diffConfig || !this.rtStructure?.diffConfig?.asyncAjax) {
        this.asyncAjaxCallBack();
      }
    },

    // 将页面相关表单生成结果json-->docAttr
    createResultData(toRedis, params) {
      this.resultData = this.rtStructure.exportStructureData(toRedis, params);
      this.saveClientLog('createDescAndImpText是否存在：' + typeof this.rtStructure?.createDescAndImpText);
      if(this.rtStructure.createDescAndImpText && typeof this.rtStructure.createDescAndImpText === 'function') {
        this.saveClientLog('执行createDescAndImpText');
        this.rtStructure.createDescAndImpText();
      }
      let arr = this.treeToArr(JSON.parse(JSON.stringify(this.resultData)));
      // 数组转以id为key的对象，方便使用
      this.idMapResult = {};
      arr.forEach(item => {
        this.idMapResult[item.id] = item;
      });
    },

    // 树转成一维数组
    treeToArr(data) {
      let arr = [];
      const handler = (list) => {
        list.forEach((item) => {
          if(item.child && item.child.length) {
            let tempChild = JSON.parse(JSON.stringify(item.child));
            delete item.child;
            arr.push(item);
            handler(tempChild);
          } else {
            arr.push(item);
          }
        })
      }
      handler(data);
      return arr;
    },

    // 通过id获取value
    getValById(id) {
      let idAndDomMap = this.rtStructure.idAndDomMap;
      let idMapResult = this.idMapResult;
      let value = '';
      if(idMapResult[id]) {
        value = idMapResult[id].val || '';
      } else {
        if(idAndDomMap[id] && idAndDomMap[id].itemList) {
          let arr = [];
          idAndDomMap[id].itemList.forEach((item) => {
            if(idMapResult[item.id]) {
              arr.push(idMapResult[item.id].val || '');
            }
          })
          value = arr.join('、');
        }
      }
      return value;
    },

    // 实际保存的数据
    saveParams(data) {
      let {patDocId = '', patternName = '', patternId = '', optType = '', optId = '', description = '', optName = '',
      impression = '', recommendation = '', reportInfo = {}, busInfo = {}, docAttr = [], docOrign, docNo, rptActType,
      rptImgs} = data;
      // 去除纯空格的内容
      ['description', 'impression', 'recommendation', 'examParam'].forEach(k => {
        if (data[k] && !/\S/.test(data[k])) {
          data[k] = '';
        }
        if (reportInfo[k] && !/\S/.test(reportInfo[k])) {
          reportInfo[k] = '';
        }
      });
      initLocalParams(this);
      if(this.localQueryParams && this.localQueryParams.docNo !== undefined) {
        this.patientInfo.docNo = this.localQueryParams.docNo;
      }
      if(this.localQueryParams.docId !== undefined) {
        this.docId = this.localQueryParams.docId;
        data.docId = this.localQueryParams.docId;
      }

      delete reportInfo.rptImageList;
      if(docOrign) {
        !reportInfo.examTechnician && (reportInfo.examTechnician = docOrign.examStaff);  //检查医生
        !reportInfo.reportDate && (reportInfo.reportDate = docOrign.reportDate);  //报告日期
        !reportInfo.reportTime && (reportInfo.reportTime = docOrign.reportTime);  //报告时间
        !reportInfo.reporter && (reportInfo.reporter = docOrign.reportStaff);   //报告医生
      }
      let params = {
        patDocId: patDocId || this.patientInfo.patDocId,
        patternName: patternName || this.patternInfo.fileName || this.patternInfo.patternInfo?.patternName|| '',
        patternId: patternId || this.patientInfo.patternId,
        optType: optType,
        rptActType: rptActType,
        optId: optId || this.patientInfo.optId,
        optName: optName || this.patientInfo.optName,
        docId: data.docId || this.docId || '',
        docNo: this.patientInfo.docNo || '',
        mainFlag: data.mainFlag || '',
        isAbnormal: this.outerParam && this.outerParam.isAbnormal ? this.outerParam.isAbnormal: '',
        docContent: {
          description,
          impression,
          recommendation,
          isAbnormal: this.outerParam && this.outerParam.isAbnormal ? this.outerParam.isAbnormal: '',
          rptImgs,
          ...reportInfo,
          busInfo: {
            busId: this.patientInfo.examNo,
            busType: this.patientInfo.busType || '1',
            name: this.patientInfo.name,
            sex: this.patientInfo.sex,
            age: this.patientInfo.age,
            birthDate: this.patientInfo.birthDate,
            patLocalId: this.patientInfo.patLocalId,
            ...busInfo,
          },
          docAttr: docAttr || this.resultData,
          docOrign: docOrign
        },
        ...(this.outerParam || {}),
        reportDocType: '3',
        reportType: '3',
        fromWebpacs: this.fromWebpacs
      }
      params.showTitle = this.patternInfo.showTitle || this.patternInfo.patternInfo?.showTitle || params.patternName || '';
      if(this.outerParam) {
        params.param = this.outerParam;
      }
      return params;
    },

    // 调接口保存
    async saveDataByApi(params, newMode, onlySave, saveNewDoc) {
      let mainParam = params.filter(pI => pI.mainFlag === '1');
      // 预览页面，结构化诊断库直接返回数据及图片
      if((this.patientInfo.viewId === '9' || this.patternInfo.patternType === '9') && this.localQueryParams.entryType === '1') {
        params = {...(mainParam[0] || params[0]), status: '0', message: '操作成功'};
        this.$message({
          message: "保存成功！",
          type: "success",
        });
        return params;
      }
      let apiUrl = params.some(pI => pI.docId) ? api.updateSrResult : api.saveSrResult;
      if (saveNewDoc) {
        apiUrl = api.saveSrResult; // 保存为新的文档
      }
      console.log('保存的入参', params);
      this.saveClientLog(`开始保存节点(examNo:${this.localQueryParams.busId})` + JSON.stringify(params))
      // throw new Error('保存停止');
      let res = await request(apiUrl, 'post', params);
      if(!onlySave && (!res || res.status !== '0')) {
        this.$message({
          message: res && res.message ? res.message : "操作失败",
          type: "error",
        });
        this.saveClientLog(`保存节点(examNo:${this.localQueryParams.busId})失败`);
        return false;
      }
      if(this.localQueryParams.oldExamNo) {
        delete this.localQueryParams.oldExamNo;
        delete this.localQueryParams.oldDocId;
      }
      console.log('是否为睿图浏览器：', this.isRTBrowser || (!this.isFromYunPacs && !!window.top.CallBrowserFunc));
      console.log('是否嵌入云pacs：', this.isFromYunPacs);
      if(!onlySave) {

        // 通知父窗口取消定时刷新缓存数据
        window.top.postMessage({
          message: 'cancelRedisFlag',
          data: true
        }, '*');

        if(this.sourceType !== '1' && (this.isRTBrowser || (!this.isFromYunPacs && !!window.top.CallBrowserFunc))) {
          await this.submitOrSaveFormByDll(JSON.stringify(params));
        }
        this.saveClientLog(`保存节点(examNo:${this.localQueryParams.busId})成功`);
        if(this.localQueryParams.webpacs!=='1' && !this.isFromYunPacs && !newMode) {
          this.$message({
            message: "保存成功！",
            type: "success",
          });
        }
      }
      let docId = res.result || '';
      params = {...(mainParam[0] || params[0]), ...this.patientInfo, ...res};
      if(docId) {
        params = {...params, reportId: docId, docId};
        this.docId = docId;
      }
      let newQueryParams = {...this.localQueryParams, docId: this.docId};
      this.localQueryParams = newQueryParams;
      getOrSetCurReportLocal({vm: this, type: 'set', data: newQueryParams});
      return params;
    },

    // 调用YLZRT浏览器进行提交或保存
    submitOrSaveFormByDll(pattern) {
      console.log('调brSR.dll-->srCommit', pattern);
      let result = CallDllFunc("brSR.dll", "srCommit", pattern);
      console.log("submitOrSaveResponse--->", result);
      if (typeof result == 'string') {
        try {
          let resultJSON = JSON.parse(result);
          if(typeof resultJSON == 'object' && resultJSON){
            if (resultJSON.code === "0") {
              let desc = resultJSON.desc;
              if (desc.dllCode === "0") {
                console.log('dll保存成功');
                return true;
              } else {
                console.error(desc.dllDesc);
              }
            } else {
              console.error(resultJSON.desc);
            }
          }else{
            return false;
          }
        } catch(e) {
          console.error('数据返回格式错误：'+e);
          return false;
        }
      }
      return false;
      
    },

    // 上传pdf时处理分页问题，页眉页脚
    /**
     * 打印时加入页眉页脚，以及保证分页内容的完整性
     * @param {string} printHtml  打印html代码
     * @param {string} pageSelector  打印区域的选择器
     * @param {string} headerSelector  页眉的选择器
     * @param {string} footerSelector  页脚的选择器
     * @param {string} bodySelector  其余内容的选择器
     * @param {string} showPageCounter  是否显示页码，默认false
     * 类名follow-break整块一起分页计算，break-page另起一页
     * params = {printHtml: '', pageSelector:'.print-page', headerSelector:'.rx-print-header', footerSelector:'.rx-print-footer', bodySelector:'.rx-print-body'}
     * rebuildPdfPage(params)
     */
    rebuildPdfPage(params) {
      let {printHtml = '', pageSelector, headerSelector, footerSelector, bodySelector, showPageCounter = false, newSr} = params;
      if(!printHtml) {
        console.error('打印内容不能为空');
        return;
      }
      const isWriteByEditor = this.rtStructure?.isWriteByEditor;  //是否来自编辑器
      // window.devicePixelRatio  当前浏览器和系统缩放后的比例
      if(this.localQueryParams.print !== '3') {
        console.log('浏览器和系统缩放比例', window.devicePixelRatio);
        if(window.devicePixelRatio < 1) {
          console.log('进行缩放：', 1 / window.devicePixelRatio);
          document.body.style.zoom = 1 / window.devicePixelRatio;
        }
      }
      // const pageH = 1000;  //固定，转成pdf正好一页
      // const pageH = newSr ? 1050 : 1050;  //固定，转成pdf正好一页, newSr区分singleDisease报告和厦门两个超声报告
      const pageH = newSr ? 1100 : 1050;  //固定，转成pdf正好一页
      // 先将canvas转成图,使分页显示正常
      let header = headerSelector ? $(pageSelector).find(headerSelector) : null;
      let footer = footerSelector ? $(pageSelector).find(footerSelector) : null;
      let body = $(pageSelector).find(bodySelector);
      body && body.css('flex', 'inherit');
      $(pageSelector).parents().css({
        'padding-top': '0',
        'padding-bottom': '0',
      });
      let headerH = header ? (header.outerHeight(true) || 0) : 0;  //头部高度
      let footerH = footer ? (footer.outerHeight(true) || 0) : 0;  //底部高度
      let pagePdT = $(pageSelector).css('padding-top') ? $(pageSelector).css('padding-top').replace('px', '') : 0;
      let pagePdB = $(pageSelector).css('padding-bottom') ? $(pageSelector).css('padding-bottom').replace('px', '') : 0;
      // 判断头部/底部是否为绝对定位，是的话不重复减上下边距
      let headerPos = header && header.length && !isWriteByEditor ? window.getComputedStyle(header[0]).position === 'absolute' : false;
      let footerPos = footer && footer.length && !isWriteByEditor ? window.getComputedStyle(footer[0]).position === 'absolute' : false;

      // let pagePdH = Number((headerPos ? 0 : pagePdT)) + Number((footerPos ? 0 : pagePdB));  //页面上下内边距
      let pagePdH = Number(pagePdT) + Number(pagePdB);  //页面上下内边距
      let bodyH = (body.outerHeight() - (body.css('padding-top') ? parseInt(body.css('padding-top')) : 0)) || 0;  //主体总高度
      if(isWriteByEditor && window.adjustParentHeight) {
        bodyH = window.adjustParentHeight(body[0]);
        body.css('height', bodyH + 'px');
        $(pageSelector).css('height', 'unset');  //分页使用，按A4纸处理
        $(pageSelector).css('min-height', '1100px');  //分页使用，按A4纸处理
      }
      let pageRemainH = pageH - (headerH + footerH + pagePdH) + (headerPos ? headerH : 0) + (footerPos ? footerH : 0);   //每页剩余的空间高度
      if(isWriteByEditor) {
        pageRemainH = pageRemainH - (headerPos ? headerH : 0) - (footerPos ? footerH : 0) + pagePdH;
      }
      let pageCount = Math.ceil(bodyH/pageRemainH);   //总页数
      // 非编辑器写的模板，超过一页先减去最后一个元素的marginBottom,避免计算的高度小于原页面高度，最后一个边距可不参与分页
      if(pageCount > 1) {
        if(!isWriteByEditor) {
          // body.find('> *:visible:last-child').css('margin-bottom', '0')
          let lastMarginBottom = parseInt(body.find('> *:visible:last-child').css('margin-bottom'));
          if(!isNaN(lastMarginBottom) && lastMarginBottom > 0) {
            bodyH -= lastMarginBottom;
            pageCount = Math.ceil(bodyH/pageRemainH)
          }
        }
      }
      let printCount = pageCount;  //调整换页后所需的打印总页数
      let _this = this;
      // 模板内自带页码占位符
      let pageCounterInTemplate = $(pageSelector).find('.rt-page-counter:not(.common-counter)').length > 0;
      // 内容高度小于页面剩余高度，则不需要处理
      if(bodyH <= pageRemainH) {
        body.css('height', pageRemainH + 'px');
        bodyH = pageRemainH;
        if(pageCounterInTemplate) {
          $(pageSelector).find('.rt-page-counter').each(function(i, dom) {
            _this.resetPagePlaceholder($(dom), i + 1, '1');
          })
        }
        return;
      }
      const addPageHandler = (pageNo) => {
        let lastPage = null;  //上一页整部分
        let allLastPageBodyH = 0;  //小于i页已用的总页面高度
        let lastPageBody = null;
        let lastOneUseH = 0;   //上一页最后一个元素已使用的高度
        if(pageNo > 0) {
          lastPage = $('body').find(`${pageSelector}:eq(${pageNo-1})`);  //上一页整部分
          lastPageBody = $('body').find(`${bodySelector}:eq(${pageNo-1})`);  //上一页的主体部分
          if(!lastPageBody.length) {
            lastPageBody = lastPage.find(`${bodySelector}`);
          }
          allLastPageBodyH = lastPageBody.attr('total-height');
          lastOneUseH = Number(lastPageBody.attr('lastone-use-height'));
        }
        let curH = (bodyH - allLastPageBodyH > pageRemainH) ? pageRemainH : (bodyH - allLastPageBodyH);  //每页所需的高度
        if(curH <= 0) {
          printCount--;
          return;
        }
        
        if(curH > 0 && pageNo > 0) {
          lastPage.after(printHtml);
        }
        if(isWriteByEditor && window.adjustParentHeight) {
          var newPage = $('body').find(`${pageSelector}:eq(${pageNo})`);
          $(newPage).css('height', 'unset');  //分页使用，按A4纸处理
          $(newPage).css('min-height', '1100px');  //分页使用，按A4纸处理
        }
        
        let newPageBody = $('body').find(`${bodySelector}:eq(${pageNo})`);  //当前新增的页主体部分
        if(!newPageBody.length) {
          newPageBody = $('body').find(`${pageSelector}:eq(${pageNo})`).find(`${bodySelector}`);
        }
        if(newPageBody.length) {
          newPageBody.closest(pageSelector).addClass('rt-print-add-page');
          newPageBody.closest(pageSelector).css({
            position: 'relative',
          })
          if(showPageCounter && !pageCounterInTemplate && newPageBody.closest(pageSelector).find('.rt-page-counter').length === 0) {
            newPageBody.closest(pageSelector).append(
              `<div class="rt-page-counter common-counter" style="position: absolute; right: 10px; bottom: 10px; z-index: 10; font-size: 12px; color: #333;">第<span class="pageNo">${pageNo+1}</span>/<span class="pageSize">${pageCount}</span>页</div>`
            )
          }
          newPageBody.addClass('rt-page-body');
          newPageBody.css({ height: curH + 'px', overflow: 'hidden' });
          if(isWriteByEditor) {
            if(pageNo > 0) {
              // newPageBody[0].scrollTop = allLastPageBodyH;   //超出高度的滚动位置
              // 滚动打印时不生效，改成改吧rt-layout的定位
              newPageBody.find('.rt-layout').each(function(l, lyDom) {
                if(lyDom.style.top !== '' && lyDom.style.top !== undefined) {
                  // - (pagePdH > 0 ? ((pagePdH - 4) * pageNo) : 0)
                  $(lyDom).css('top', (parseInt(lyDom.style.top) - allLastPageBodyH) + 'px');
                }
              })
              newPageBody.closest(pageSelector).attr('scroll-top', allLastPageBodyH); //做标记
              newPageBody.closest(pageSelector).addClass('page-clone');
              newPageBody.closest(pageSelector).css('margin-top', '20px');
            }
          } else {
            if(pageNo > 0) {
              newPageBody[0].scrollTop = allLastPageBodyH;   //超出高度的滚动位置
              newPageBody.closest(pageSelector).attr('scroll-top', allLastPageBodyH); //做标记
              newPageBody.closest(pageSelector).addClass('page-clone');
              newPageBody.closest(pageSelector).css('margin-top', '20px');
            }
          }
          // 自动加上的页码
          if(!pageCounterInTemplate) {
            newPageBody.closest(pageSelector).find('.rt-page-counter.common-counter .pageNo').text(pageNo + 1);
          }
          this.getLastElem({
            parentElement: newPageBody[0], 
            lastPageBody: lastPageBody,
            allLastPageBodyH: allLastPageBodyH,
            bodyH: bodyH, 
            originPageH: curH, 
            pageNo: pageNo,
            lastEleUseH: lastOneUseH,
            pageRemainH: pageRemainH,
            printCount,
            newSr
          });
        } else {
          printCount--;
        }
      }
      for(let i = 0; i < pageCount; i++) {
        addPageHandler(i);
      }
      let allPageBodyH = $('body').find(`${bodySelector}:eq(${pageCount-1})`).attr('total-height');
      // 5像素内忽略不计，可能是精确值导致
      console.log({allPageBodyH, bodyH});
      if(Math.ceil(allPageBodyH) < Math.ceil(bodyH) - 5) {
        addPageHandler(pageCount);
        printCount++;
      }
      if(!pageCounterInTemplate && showPageCounter && printCount > 1) {
        $('body').find('.rt-page-counter.common-counter').addClass('show');
        $('body').find('.rt-page-counter.common-counter .pageSize').text(printCount);
      } else {
        // 处理占位符
        if(pageCounterInTemplate) {
          $(pageSelector).find('.rt-page-counter').each(function(i, dom) {
            _this.resetPagePlaceholder($(dom), i + 1, printCount);
          })
        }
      }
    },
    
    /**
     * 获取当前页面最底部显示的元素的可视高度：除文本外(文本按行放)，放不下就整块换到下一页
     * 使用overflow: hidden和scollTop来显示/隐藏可视的内容
     * @param {string} pageSelector  打印区域的选择器
     * @param {string} lastPageBody  上一页的主体dom
     * @param {string} allLastPageBodyH  当前页之前的所有的主体内容高度
     * @param {string} originPageH  当前页未处理前的高度
     * @param {string} lastEleUseH  上一页最后一个元素已使用的高度
     * @param {string} pageRemainH  可使用的高度
     */
    getLastElem(params) {
      const isWriteByEditor = this.rtStructure?.isWriteByEditor;  //是否来自编辑器
      let {parentElement, allLastPageBodyH, lastPageBody, originPageH, lastEleUseH, pageNo, printCount, pageRemainH, newSr, bodyH} = params;
      let childElementArr = parentElement.querySelectorAll('*:not(.follow-break)');
      let lastPageBodyH = allLastPageBodyH || 0;  //上一个页面存储的总高度
      let isFirstLineImage = false;  //判断是否为第一行报告图像
      let theadFlag = false;
      let trRowspan = false;  //是否存在合并行的，正好分页的
      // 存储最底部显示不全的元素的差值
      let gapMap = {
        'IMG': {hGap: 0, useH: 0, tr: false},
        'DIV': {hGap: 0, useH: 0, tr: false},
        'TR': {hGap: 0, useH: 0, tr: false}
      }
      let overChild = [];
      let mathNode = ['math', 'msub', 'mi', 'mrow', 'mo'];   //数学公式mathJax插件排除掉的节点
      let parentRect = parentElement.getBoundingClientRect();
      let isScale = window.devicePixelRatio >= 1.25; // 处理浏览器缩放到125%以上的分页问题
      for(let i = 0; i < childElementArr.length; i++) {
        let childElement = childElementArr[i];
        let nodeName = childElement.nodeName.toLowerCase();
        if(nodeName && (mathNode.includes(nodeName) || nodeName.includes('-'))) {
          continue;
        }
        if(!childElement.style) {
          continue;
        }
        if(childElement.style.display === 'none') {
          continue;
        }
        if(childElement.nodeName==='BR') {
          continue;
        }
        let childRect = childElement.getBoundingClientRect();
        let originChildH = (isWriteByEditor ? childRect.height : childElement.offsetHeight) - lastEleUseH;  //当前元素的实际高度
        //在父元素内的可视高度
        let visibleChildH = Math.min(childRect.bottom, parentRect.bottom) - Math.max(childRect.top, parentRect.top);
        if(this.localQueryParams.isMobile || isScale) {
          visibleChildH = Math.ceil(visibleChildH);
        }
        if(!newSr && visibleChildH > 145 && visibleChildH < 148) {
          visibleChildH = 148;
        }

        // follow-next判断是否要紧跟下一个兄弟元素的内容显示
        if($(childElement).hasClass('follow-next') && i < childElementArr.length - 1 && originChildH <= visibleChildH) {
          let nextBroEle = childElementArr[i + 1];
          let nextBroRect = nextBroEle.getBoundingClientRect();
          let nextBroH = (isWriteByEditor ? nextBroRect.height : nextBroEle.offsetHeight);  //当前元素的实际高度
          //在父元素内的可视高度
          let visibleNextBro = Math.min(nextBroRect.bottom, parentRect.bottom) - Math.max(nextBroRect.top, parentRect.top);
          if(this.localQueryParams.isMobile || isScale) {
            visibleNextBro = Math.ceil(visibleNextBro);
          }
          // 存在下一兄弟元素，且下一兄弟元素高度小于等于可视高度：兄弟元素在本页一点内容都放不下时处理
          if(visibleNextBro === 0 || visibleNextBro < nextBroH) {
            originChildH = originChildH + 10;
            originPageH -= originChildH;
            parentElement.style.height = originPageH + 'px';
            parentRect = parentElement.getBoundingClientRect();
          }
        }
        // 指定另起一页，珠江血液报告
        if($(childElement).hasClass('break-page') && childRect.top <= pageRemainH * (pageNo + 1) && !$('.break-page').attr('new-page')) {
          gapMap['DIV'].hGap = visibleChildH + (parseInt($(childElement).css('margin-top')));
          overChild.push('DIV');
          $('.break-page').attr('new-page', '1');  //开始分页
          break;
        }
        if(visibleChildH >= 0 && originChildH > visibleChildH) {
          if(overChild.includes('TR') && overChild.includes('IMG')) {
            break;
          }
          // 除了图像和单元行外,无子元素的进行忽略不计，load-img为完整的图像区域
          if(!['TR', 'IMG'].includes(childElement.nodeName) 
              && childElement.childElementCount !== 0
              && !childElement.classList.contains('load-img')
              && childElement.nodeName !== 'THEAD'
          ) {
            continue;
          }
          if(trRowspan) {
            continue;
          }
          let imgIndex = newSr ? 1 : 2;
          // 判断当前图像是否为第一行，是的话将标题带上一起换页
          if((childElement.classList.contains('load-img') && $(parentElement).find(childElement).index($(parentElement).find('.load-img')) <= imgIndex)) {
            isFirstLineImage = true;
          }
          if(childElement.nodeName === 'THEAD') {
            theadFlag = true;
          }
          overChild.push(childElement.nodeName);
          let gapKey = gapMap[childElement.nodeName] ? gapMap[childElement.nodeName] : gapMap['DIV'];
          gapKey.tr = false;
          if(isFirstLineImage) {
            if(!this.fromWebpacs) {
              gapKey.hGap = visibleChildH + 24 + 8;   //超声图像的标题+之间的边距
            } else {
              gapKey.hGap = visibleChildH + 8;
            }
          } else {
            if((childElement.nodeName === 'DIV' || childElement.nodeName === 'P' || childElement.nodeName === 'SPAN')) {
              // 自定义生成，防止页面重新渲染存在延时导致计算问题
              let cloneLine = $(childElement).clone(true);
              cloneLine.attr('id', 'rt-clone-line');
              cloneLine.css({
                'white-space': 'nowrap',
                'overflow': 'hidden',
                'position': 'absolute'
              });
              cloneLine.css('min-height', 'unset');
              $(childElement).after(cloneLine);
              var lineHeight = cloneLine.outerHeight();
              document.querySelector('#rt-clone-line').remove();
              if(lineHeight > 0) {
                childElement.setAttribute('is-handler', '1');
                // =多出的部分[可视行数-取整的函数]*行高
                gapKey.hGap = Math.ceil(Number(((visibleChildH/lineHeight) - Math.floor(visibleChildH/lineHeight)).toFixed(3)) * lineHeight);
                if(isWriteByEditor) {
                  if($(childElement).parent().hasClass('w-con') && $(childElement).parent()[0].style.paddingBottom || 
                    $(childElement).parent().hasClass('w-con') && $(childElement).parent()[0].style.paddingTop) {
                    gapKey.hGap += parseInt($(childElement).parent()[0].style.paddingBottom || '0') + parseInt($(childElement).parent()[0].style.paddingTop || '0');
                  }
                }
              }
            } else if(childElement.nodeName === 'TD' || childElement.nodeName === 'TR') {
              if($(childElement).closest('tr').index() === 0 && 
                $(childElement).closest('table').find('thead').hasClass('ignore-head')) {
                  let theadH = $(childElement).closest('table').find('thead').outerHeight();
                  gapKey.hGap = Math.ceil(visibleChildH) + theadH;  
              } else {
                let rowspan = $(childElement).attr('rowspan') || 0;
                gapKey.hGap = Math.ceil(visibleChildH) - 1;  //td的高度-1，为边框，少减
                gapKey.tr = true;
                if(rowspan > 0) {
                  let lastTrIndex = $(lastPageBody).attr('rowspan-tr');
                  let curTrIndex = $(childElement).closest('tr:visible').index('tr:visible');
                  if(!lastTrIndex || curTrIndex > (lastTrIndex * (pageNo + 1))) {
                    let trIndex = Number($(childElement).closest('tr').index('tr:visible')) + Number(rowspan);
                    trRowspan = true;
                    $(parentElement).attr('rowspan-tr', trIndex);
                  }
                }
              }
            } else {
              let ptH = 0;
              if(childElement.nodeName === 'IMG' && $(childElement).parents('.rpt-img-ls').length) {
                ptH = $(childElement).parents('.rpt-img-ls').css('padding-top') ? $(childElement).parents('.rpt-img-ls').css('padding-top').replace('px', '') : 0;
              }
              let mgH = 0;
              if(childElement.nodeName === 'IMG' && $(childElement).parent().length) {
                let mtH = $(childElement).parent().css('margin-top') ? $(childElement).parent().css('margin-top').replace('px', '') : 0;
                let mbH = $(childElement).parent().css('margin-bottom') ? $(childElement).parent().css('margin-bottom').replace('px', '') : 0;
                mgH = Number(mtH) + Number(mbH);
              }
              gapKey.hGap = visibleChildH + mgH;
            }
          }
        } 
      }
      let lastOneUseH = 0;
      let tableFlag = false;   //最后一项是否为表格
      if(overChild.length) {
        let gapName = overChild[overChild.length - 1];
        // 找出gap中最大的值
        let maxHGap = 0;
        for(let key in gapMap) {
          if(gapMap[key].hGap > maxHGap) {
            maxHGap = gapMap[key].hGap;
            gapName = key;
          }
        }
        let hGap = gapMap[gapName] ? gapMap[gapName].hGap : gapMap['DIV'].hGap;
        lastOneUseH = gapMap[gapName] ? gapMap[gapName].useH : gapMap['DIV'].useH;
        if(hGap !== 0) {
          tableFlag = gapMap[gapName] ? gapMap[gapName].tr : gapMap['DIV'].tr;
        }
        // 非最后一页，或者最后页的高度超过页面剩余高度时减掉多余的
        // 临时存起总页面高度
        let tempChangePageHeight = Number(lastPageBodyH) + Number(Math.ceil(originPageH - hGap)) - (tableFlag? 1 : 0);
        if((pageNo < printCount - 1) || (originPageH > pageRemainH) || (!isWriteByEditor && Math.ceil(tempChangePageHeight) < Math.ceil(bodyH) - 5)) {
          let curH = Math.ceil(originPageH - hGap);   //原定的高度-超出当前页那部分元素的高度
          parentElement.style.height = curH + 'px';
        }
      }
      let curPageBodyH = $(parentElement).outerHeight();
      $(parentElement).attr('total-height', Number(lastPageBodyH) + Number(curPageBodyH) - (tableFlag? 1 : 0));
      $(parentElement).attr('lastone-use-height', Math.ceil(Number(lastOneUseH)));
    },

    // 重置页码占位符
    resetPagePlaceholder(pageCounterDom, pageNo, total) {
      // 第{CURPAGE}页，共{TOTALPAGE}页
      const TOTALREG = new RegExp(`{TOTALPAGE}`, 'g');
      const PAGENOREG = new RegExp(`{CURPAGE}`, 'g');
      let html = pageCounterDom.html()
      html = html.replace(TOTALREG, total).replace(PAGENOREG, pageNo);
      pageCounterDom.html(html);
    },

    async toPDF(ele, hideTip) {
      // 兼容旧版本后端直接预览打印
      if(this.localQueryParams.print === '3') {
        if(this.createPdfFlag !== undefined) {
          this.createPdfFlag = false;
        }
        if(!this.printFlag) {
          this.printFlag = false;
        }
        let patternLoadedStatus = this.localQueryParams.entryType !== '0' ? '1' : '2';
        $('#app').attr('pattern-loaded', patternLoadedStatus);
        return {};
      }
      // 诊断库不通过后端生成，由前端直接返回图片回显
      if(this.patientInfo.viewId !== '9' && this.patternInfo.patternType !== '9') {
        // 通过后端生成PDF
        let createPdfByInterface = await this.$store.dispatch('report/creatRptPdfBySr', {
          params: {
            examNo: this.patientInfo.examNo,
            reportNo: this.patientInfo.docNo
          },
          vm: this
        });
        // 如果createPdfByInterface为true，则说明后端不存在浏览器，使用前端生成PDF
        if(createPdfByInterface) {
          this.resetDocStyle && this.resetDocStyle(ele);
          this.commResetDocStyle && this.commResetDocStyle(ele);
          return {"status":"0","message":"操作成功","result":{}};;
        }
      }
      let eleW = ele.offsetWidth;   // 获得该容器的宽
      let eleH = ele.scrollHeight;  // 获得该容器的高
      let eleOffsetTop = ele.offsetTop;  // 获得该容器到文档顶部的距离
      let eleOffsetLeft = ele.offsetLeft;  // 获得该容器到文档最左的距离
      let canvas = document.createElement("canvas");
      let abs = 0;
      let win_in = document.documentElement.clientWidth || document.body.clientWidth;   // 获得当前可视窗口的宽度（不包含滚动条）
      let win_out = window.innerWidth;    // 获得当前窗口的宽度（包含滚动条）
      if (win_out>win_in) {
        abs = (win_out - win_in) / 2;  // 获得滚动条宽度的一半
      }
      let scale = 3;
      canvas.width = eleW * scale;  // 将画布宽&&高放大两倍
      canvas.height = eleH * scale;
      let context = canvas.getContext("2d");
      context.scale(scale, scale);    // 增强图片清晰度
      let uploadPdfRes = false;
      // context.translate(-eleOffsetLeft - abs, -eleOffsetTop);
      await html2canvas(ele, {
        dpi: 300,
        scale,
        // canvas,
        async: false, // 同步执行
        useCORS:true,  //允许canvas画布内可以跨域请求外部链接图片, 允许跨域请求。
      }).then(async (canvas)=> {
        let contentWidth = canvas.width;
        let contentHeight = canvas.height;
        //一页pdf显示html页面生成的canvas高度;
        let pageHeight = (contentWidth / 592.28) * 841.89; // 这样写的目的在于保持宽高比例一致 pageHeight/canvas.width = a4纸高度/a4纸宽度// 宽度和canvas.width保持一致
        //未生成pdf的html页面高度
        let leftHeight = contentHeight;
        //页面偏移
        let position = 0;
        //a4纸的尺寸[595.28,841.89],单位像素，html页面生成的canvas在pdf中图片的宽高
        let imgWidth = 595.28;
        let imgHeight = (595.28 / contentWidth) * contentHeight;
        let pageData = canvas.toDataURL("image/jpeg", 1.0);
        // let imgFile = this.dataURLtoFile(pageData, 'font.jpg');
        // window.open(URL.createObjectURL(imgFile))
        // return
        if(this.patientInfo.viewId === '9' || this.patternInfo.patternType === '9') {
          uploadPdfRes = pageData;
          return;
        }
        //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
        //当内容未超过pdf一页显示的范围，无需分页
        let fileList = [];
        let pdf = new JsPDF("", "pt", "a4");
        let pageNo = 1;
        if (leftHeight<pageHeight) {
          pdf = new JsPDF("", "pt", "a4");
          //在pdf.addImage(pageData, 'JPEG', 左，上，宽度，高度)设置在pdf中显示；
          pdf.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
          const buffer = pdf.output("datauristring");
          const file = this.dataURLtoFile(buffer, `${pageNo}.pdf`);
          fileList.push(file);
          // window.open(URL.createObjectURL(file));
          // return
        } else {
          // 分页
          while (leftHeight>0) {
            pdf = new JsPDF("", "pt", "a4");
            pdf.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
            leftHeight -= pageHeight;
            position -= 841.89;
            const buffer = pdf.output("datauristring");
            const file = this.dataURLtoFile(buffer, `${pageNo}.pdf`);
            fileList.push(file);
            // window.open(URL.createObjectURL(file))
            pageNo++;
            //避免添加空白页
            // if (leftHeight>0) {
            //   pdf.addPage();
            // }
          }
        }
        // const buffer = pdf.output("datauristring");
        // const file = this.dataURLtoFile(buffer, this.patientInfo.examNo);
        let formdata = new FormData();
        for (let i = 0; i < fileList.length; i++) {
          formdata.append('file', fileList[i]);
        }
        formdata.append("imageType", "sr_rpt_pdf"); // 文件类型：sr_rpt_pdf 结构化的报告pdf，rpt 报告图像，apply 申请单图像
        formdata.append("examNo", this.patientInfo.examNo);
        formdata.append("reportNo", this.patientInfo.docNo);
        // console.log("pdf本地路径", URL.createObjectURL(file));
        // pdf.save(`${this.patientInfo.examNo}.pdf`); // 保存文件到本地
        // return true
        uploadPdfRes = await this.uploadWebImgHandler(formdata, hideTip, ele); // 文件上传服务器
      })
      return uploadPdfRes;
    },
    convertBase64ToBlob(base64Str) {

      // 将base64字符串转为二进制数据
      const byteCharacters = atob(base64Str);
    
      // 创建Blob对象
      const blob = new Blob([byteCharacters], { type: 'application/octet-stream' });
    
      return blob;
    },
    // 上传网页图片
    async uploadWebImgHandler(param, hideTip, ele) {
      let res = {"status":"0","message":"操作成功","result":{}};
      if(this.isFromYunPacs) {
        // 云pacs下等待返回才显示pdf
        res = await this.$store.dispatch("report/uploadWebImg", {param, vm: this});
      } else {
        this.$store.dispatch("report/uploadWebImg", {param, vm: this});
      }
      if(!res) {
        this.resetDocStyle && this.resetDocStyle(ele);
        this.commResetDocStyle && this.commResetDocStyle(ele);
        !hideTip && this.$message.error('提交失败');
        return false;
      }
      if (res.status !== "0") {
        this.resetDocStyle && this.resetDocStyle(ele);
        this.commResetDocStyle && this.commResetDocStyle(ele);
        !hideTip && this.$message.error(res.message);
        return res;
      }
      if (res.status === "0") {
        !hideTip &&  this.$message({
          message: "文件上传成功！",
          type: "success",
        });
        this.resetDocStyle && this.resetDocStyle(ele);
        this.commResetDocStyle && this.commResetDocStyle(ele);
        return res;
      }
    },
    async sendPDF(param, hideTip, ele) {
      const res = await this.$store.dispatch("report/uploadPDF", {param, vm: this});
      if(!res) {
        this.resetDocStyle && this.resetDocStyle(ele);
        this.commResetDocStyle && this.commResetDocStyle(ele);
        !hideTip && this.$message.error('提交失败');
        return false;
      }
      if (res.status !== "0") {
        this.resetDocStyle && this.resetDocStyle(ele);
        this.commResetDocStyle && this.commResetDocStyle(ele);
        !hideTip && this.$message.error(res.message);
        return res;
      }
      if (res.status === "0") {
        !hideTip &&  this.$message({
          message: "文件上传成功！",
          type: "success",
        });
        this.resetDocStyle && this.resetDocStyle(ele);
        this.commResetDocStyle && this.commResetDocStyle(ele);
        return res;
      }
    },
    // 将base64转换为文件对象
    dataURLtoFile(dataurl, filename) {
      // console.log("dataurl", dataurl);
      let arr = dataurl.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      //转换成file对象
      return new File([u8arr], filename, { type: mime });
      //转换成成blob对象
      //return new Blob([u8arr],{type:mime});
    },
    
    // blob转文件流
    blobToFile(blob, filename, type) {
      return new File([blob], filename, { type })
    },
   
    // 与云pacs的交互会用到
    // 保存提交报告  isNewRpt是否为新的报告 type：0保存1提交2审核
    async saveOrSubmitToYPacs(postParams, type, isNewRpt, onlyUploadRptImage) {
      let {examClass, examItems, examOrgans, examStatus, isAbnormal, patientSource, reqDept,
        reqDeptName, rptImgList, reportNo
      } = postParams.ypacsRptInfo;
      let params = {
        // examClass, examItems, examOrgans, isAbnormal, 
        // patientSource, reqDept, rptImgList,
        examStatus, 
        reqDeptName, 
        isAbnormal: postParams.isAbnormal || isAbnormal || '',
        examNo: postParams.docContent.busInfo.busId,
        name: postParams.docContent.busInfo.name,
        sex: postParams.docContent.busInfo.sex,
        age: postParams.docContent.busInfo.age,
        patLocalId: postParams.docContent.busInfo.patLocalId,
        description: postParams.description || postParams.docContent.description, 
        impression: postParams.impression || postParams.docContent.impression,
        recommendation: postParams.recommendation || postParams.docContent.recommendation,
        otherInfo: postParams.otherInfo,
        reportNo: this.patientInfo.docNo || reportNo,
        patDocId: postParams.patDocId,
        reportDocType: '3',
        reportType: '3',
        reporter: postParams.reporter || postParams.docContent.reporter || '',
        reportDate: postParams.reportDate || postParams.docContent.reportDate || '',
        reportTime: postParams.reportTime || postParams.docContent.reportTime || '',
        examTechnician: postParams.examTechnician || postParams.docContent.examTechnician || '',
        reportId: postParams.reportId || this.docId || ''
      }
      // 如果是新的报告  0：addOrUpdate  1：saveAndSubmit 2:saveAndAffirm 
      // 编辑页olnyAffirm
      params.operateType = 'addOrUpdate';
      // if(type === '1' && (examStatus < 60 || examStatus == 65)) {  //尚未提交
      if(type === '1') {  //尚未提交
        params.operateType = 'saveAndSubmit';
      }
      if(type === '2') {  //尚未确认报告
        params.operateType = 'saveAndAffirm';
      }
      let saveRes = true;
      if(this.localQueryParams.webpacs!=='1') {
        saveRes = await this.$store.dispatch('report/saveReport', { params, type, isNewRpt, vm: this, onlyUploadRptImage });
        if(!saveRes) {
          return;
        }
        this.$message({
          message: "保存成功！",
          type: "success",
        });
      }
      return saveRes;
    },

    // 判断元素是否可见
    checkVisibility(childElement) {
      const parentRect = $(childElement).parent()[0].getBoundingClientRect();
      const childRect = childElement.getBoundingClientRect();

      // 判断子元素是否在父元素的可视范围内
      if (
        childRect.top >= parentRect.top &&
        childRect.left >= parentRect.left &&
        childRect.bottom <= parentRect.bottom &&
        childRect.right <= parentRect.right
      ) {
          return true;
      } else {
        return false;
      }
    },

    // 打印当前页面判断图像是否全部加载完成
    // 加载图片
    loadImage(src) {
      return new Promise(resolve => {
        let image = new Image();
        image.onload = () => {
          console.log('图片加载成功');
          resolve();
        };
        image.onerror = () => {
          console.log('图片加载失败');
          resolve();
        };
        image.src = src;
      });
    },
    // 加载完所有的报告图片
    async loadAllImages(selector) {
      let images = $(selector);
      if(images.length === 0) {
        return;
      }
      for(let i = 0; i < images.length; i++) {
        if(!$(images[i]).is(':visible') || this.checkVisibility(images[i])) {
          continue;
        }
        await this.loadImage(images[i].src);
      }
    },

    // blob转base64
    blobToBase64(blob) {
      return new Promise((resolve, reject) => {
        const fileReader = new FileReader();
        fileReader.onload = (e) => {
          resolve(e.target.result);
        };
        // readAsDataURL
        fileReader.readAsDataURL(blob);
        fileReader.onerror = () => {
          reject(new Error('文件流异常'));
        };
      });
    },

    // 获取确认报告的模板
    async getReportDoc() {
      if(this.isPreview) {
        return;
      }
      if(!this.patientInfo.docNo) {
        return;
      }
      // 非报告/诊断库不调
      if(!['1', '9'].includes(this.patientInfo.busType) && 
        !['1', '9'].includes(this.localQueryParams.busType)) {
        return;
      }
      console.log('执行getReportDoc')
      let params = {
        reportNo: this.patientInfo.docNo,
        examNo: this.patientInfo.examNo
      };
      let res = await request(api.getReportDocNew, "post", params, {
        headers: { token: this.token },
      });
      // console.log('getReportDoc内容-->', JSON.stringify(res))
      if(!res || res.status !== '0') {
        this.$message.error(res && res.message ? res.message : '获取报告失败')
        return;
      }
      if(!res.result) {
        return;
      }
      let data = res.result || {};
      let {examRpt = {}} = data;
      this.$store.commit('report/setYpacsRptInfo', {...data, ...examRpt});
    },

    // 获取报告信息
    async getExamInfoForSR() {
      this.$store.commit('report/setBaseRptInfo', {});
      if(this.isPreview) {
        return;
      }
      let params = {
        examNo: this.patientInfo.examNo
      };
      let res = await request(api.getExamInfoForSR, "post", params, {
        headers: { token: this.token },
      });
      if(!res) {
        this.$message.error('获取报告信息接口出错');
        return;
      }
      if(!res.result) {
        return;
      }
      this.$store.commit('report/setBaseRptInfo', res.result || {});
      return res.result || {};
    },
    // 从地址栏获取额外的参数
    getOutParam() {
      let param = {};
      let outParam = decodeURIComponent(this.$route.query.outParam || '');
      if(outParam) {
        param = JSON.parse(window.atob(outParam));
      }
      return param;
    },
    // 外部调用公共方法，如pacs、云pacs
    async srCommonOuterOptHandler(params, callBack) {
      this.saveDocResult = {};
      // type 1:保存回调 10:保存成功/失败状态通知回调 2:提交回调 20:提交成功/失败状态通知回调
      // 3:确认回调 30:确认成功/失败状态通知回调 4:编辑回调 5:打印回调 50:打印成功/失败状态通知回调
      // onlySetStorage 仅缓存起来，不请求接口
      let {type, status, message, result, onlySetStorage = false, toRedis, param = {}, id} = params || {};
      this.outerAllParam = params;
      if(JSON.stringify(param) === '{}') {
        param = this.getOutParam();
      }
      if(!toRedis) {
        if(param && id) {
          param.id = id;
        }
        if(param) {
          let temp = {};
          if(param.reportNo) {
            temp.docNo = param.reportNo;
          }
          this.patientInfo = {...this.patientInfo, ...param, ...temp};
          this.localQueryParams = {...this.localQueryParams, ...param, ...temp};
          getOrSetCurReportLocal({vm: this, type: 'set', data: this.localQueryParams});
        }
        this.outerParam = param;
        if(this.$children && this.$children.length && this.$children[0].outerParam !== undefined) {
          this.$children[0].outerParam = param;
        }
      }
      if(type === undefined || !this.rptOuterTypeMap[type]) {
        console.error('srCommonOuterOpt类型不存在-->' + type);
        return;
      }
      // 做缓存操作，仅用于云pacs缓存节点数据到redis
      if(this.isFromYunPacs && toRedis) {
        this.saveDataToRedis(toRedis);
        return;
      }

      if(this.isFromYunPacs && type !== '3' && this.ypacsRptInfo && this.ypacsRptInfo.examStatus >= 70) {
        this.srCommonOuterOptHandler({...params, type: '3'});
        return;
      }
      // 1保存 2提交 3确认
      if(['1', '2', '3'].includes(type)) {
        // 处于预览页面
        if(this.localQueryParams.entryType === '1') {
          if(type === '3') {  //预览页面直接上传pdf
            this.srCommonInnerOpt(type);
          }
          console.log('预览页面不处理该类型->' + type);
          return;
        }
        let res = await this.rptOperationHandler(params);
        if(callBack && typeof callBack === 'function') {
          callBack(res);
        }
        this.saveDocResult = res;
        if(!onlySetStorage) {
          if(typeof res === 'object') {
            res.type = type;
          }
          if(!this.isFromYunPacs || type !== '3') {
            this.srCommonSendMessage(res || {}, this.rptOuterTypeMap[type].isSubmit);
          }
          if(res && res.status === '0' && this.sourceType !== '1') {  //不是pacs，操作成功直接进入预览页面
            // console.log(this.rptOuterTypeMap[type].previewType);
            this.srCommonInnerOpt(type);
          }
        }
      }
      // 10保存成功/失败状态通知回调 20提交成功/失败状态通知回调
      if(['10', '20', '30'].includes(type)) {
        if(status === '0') {  //成功
          // 诊断库必定返回诊断图像
          if(this.patientInfo.viewId === '9' || this.patternInfo.patternType === '9') {
            this.srCommonInnerOpt(type, false, '2');
          } else {
            this.srCommonInnerOpt(type);
          }
        } else {
          console.error(type + '-' + (message || '回调失败'));
        }
      } 
      // 4编辑
      if(['4'].includes(type)) {
        // 处于编辑页面
        if(this.localQueryParams.entryType === '0') {
          console.log('编辑页面不处理该类型->' + type);
          return;
        }
        let query = {...this.localQueryParams, entryType: '0'};
        delete query.print;
        getOrSetCurReportLocal({vm: this, type: 'set', data: query});
        this.$router.push({
          name: "srLayout",
          query: {...this.$route.query},
        });
      }
      // 5打印
      if(['5'].includes(type)) {
        if(this.localQueryParams.entryType === '0') {
          this.$message.error('请先保存/提交/确认报告，再进行打印');
          return;
        }
        // if(!allSreportData[this.patientInfo.patDocId].reportDocType) {
        //   this.$message.error('该模板不存在pdf打印文件，请打印普通报告');
        //   return;
        // }
        this.srCommonInnerOpt(type);
      }
      // // 50打印成功/失败状态通知回调
      // if(['50'].includes(type)) {
      //   // 暂无业务
      // }

      // 9语音结构化控制消息
      if(['9'].includes(type)) {
        this.$store.commit('setAudioCtrlMsg', params || {});
      }

      // 手写签名 签署端-提交 相当于保存
      if(['11'].includes(type)) {
        if (!this.rtStructure.isSignPannelDraw()) {
          this.$message.error('签名未完成');
          return;
        }
        this.rtStructure.setSignDateTime('2'); // 写入受检者签署时间
        setTimeout(() => {
          params.type = '1'; // 改为保存
          this.srCommonOuterOptHandler(params, callBack);
        }, 100);
      }
      // 手写签名 签署端-重签
      if(['12'].includes(type)) {
        window.postMessage({message: 'signPannelClear'}, '*'); // 通知手写签名组件清空
        this.rtStructure.setSignDateTime('2', true); // 清空受检者签署时间
      }
      // 手写签名 发起端-发起
      if(['13'].includes(type)) {
        this.rtStructure.setSignImageCurDoctor(params.signStaffNo); // 写入staffNo到签名图片组件
        this.rtStructure.setSignDateTime('1'); // 写入告知医生签署时间
        setTimeout(() => {
          params.type = '1'; // 改为保存
          this.srCommonOuterOptHandler(params, callBack);
        }, 100);
      }
      // 手写签名 发起端-重签
      if(['14'].includes(type)) {
        window.postMessage({message: 'signPannelClear'}, '*'); // 通知手写签名组件清空
        this.rtStructure.setSignDateTime('2', true); // 清空受检者签署时间
        this.rtStructure.setSignImageCurDoctor(params.signStaffNo); // 写入staffNo到签名图片组件
        this.rtStructure.setSignDateTime('1'); // 写入告知医生签署时间
        setTimeout(() => {
          params.type = '1'; // 改为保存
          this.srCommonOuterOptHandler(params, callBack);
        }, 100);
      }
    },

    // 将数据缓存直localstorage
    async saveDataToRedis(toRedis) {
      let params = [];
      if(window.saveOrSubmitHandler && typeof window.saveOrSubmitHandler === 'function') {
        params = await window.saveOrSubmitHandler({isSubmit:1, type:'1', newMode: true, toRedis});
      } else {
        // 兼容当前页面为组件形式的情况
        params = await this.rptOperationHandler({type: '1', toRedis: true})
      }
      let srRedis = JSON.parse(window.localStorage.getItem(constant.SR_REDIS_PARAMS) || '{}');
      let redisKey = `${this.patientInfo.busId+'_'+(this.patientInfo.docNo || this.docNo || '1')}`;
      if(params && params.length) {
        srRedis[redisKey] = params;
        window.localStorage.setItem(constant.SR_REDIS_PARAMS, JSON.stringify(srRedis));
      } else {
        delete srRedis[redisKey];
        window.localStorage.setItem(constant.SR_REDIS_PARAMS, JSON.stringify(srRedis));
      }
    },
    
    // 公共方法-保存/提交
    async rptOperationHandler(params) {
      let {type, status, message, result, onlySetStorage = false, toRedis} = params || {};
      let isSubmit = this.rptOuterTypeMap[type].isSubmit;
      if(!toRedis && window.saveOrSubmitHandler && typeof window.saveOrSubmitHandler === 'function') {
        let oldRes = await window.saveOrSubmitHandler({isSubmit, type, newMode: true});
        return oldRes;
      }
      console.log('执行新的统一保存节点逻辑');
      // 兼容当前页面为组件形式的情况
      if(this.$children && this.$children.length && this.$children[0]) {
        if(!this.rtStructure && this.$children[0].rtStructure) {
          console.log('来自子组件的rtStructure:', this.$children[0].rtStructure);
          this.rtStructure = this.$children[0].rtStructure;
        }
        if(!this.createPacsDescFun && this.$children[0].createPacsDescFun) {
          this.createPacsDescFun = this.$children[0].createPacsDescFun;
        }
        if(!this.createImpressionFun && this.$children[0].createImpressionFun) {
          this.createImpressionFun = this.$children[0].createImpressionFun;
        }
      }
      this.createResultData(toRedis, params);
      if(!toRedis && !onlySetStorage && !this.resultData.length) {
        if(this.fromWebpacs) {
          // 病理pacs存在值保存初步诊断和备注的情况
          if(this.outerParam) {
            this.outerParam.reportStatus = '56';
          }
        } 
        this.$message.error('报告内容不能为空');
        window.top.postMessage({
          message: 'valiResponse',
          data: {isVail: false}
        }, '*');
        return 'stop';
      } 
      if(this.$children && this.$children.length && (!this.$children[0].idMapResult || JSON.stringify(this.$children[0].idMapResult) === '{}') && this.idMapResult) {
        this.$children[0].idMapResult = this.idMapResult;
      }
      let description = '';
      if(this.rtStructure && this.rtStructure.description) {
        description = this.rtStructure.description;
      } else if(this.createPacsDescFun && typeof this.createPacsDescFun == 'function') {
        description = this.createPacsDescFun();
      }
      let impression = '';
      if(this.rtStructure && this.rtStructure.impression) {
        impression = this.rtStructure.impression;
      } else if(this.createImpressionFun && typeof this.createImpressionFun == 'function') {
        impression = this.createImpressionFun();
      }
      let recommendation = '';
      if(this.rtStructure && this.rtStructure.recommendation) {
        recommendation = this.rtStructure.recommendation;
      } else if(this.createRecommendationFun && typeof this.createRecommendationFun == 'function') {
        recommendation = this.createRecommendationFun();
      }
      let docOrign = {};
      if(this.rtStructure && this.rtStructure.docOrign) {
        docOrign = this.rtStructure.docOrign;
      }
      let reportInfo = {};
      if(this.rtStructure && this.rtStructure.reportInfo) {
        reportInfo = this.rtStructure.reportInfo;
      }
      if(!this.webpacs) {
        if(reportInfo && reportInfo.rptImageList) {
          this.$store.commit('report/setRptImage', reportInfo.rptImageList);
        }
      }
      // 结构化诊断库
      let rptImgs = [];
      // dignosisImgs模板内部自动生成图
      if((this.patientInfo.viewId === '9' || this.patternInfo.patternType === '9') && this.localQueryParams.entryType === '1') {
        let canvasFlag = await this.pageToCanvas();
        if(canvasFlag) {
          if(canvasFlag === 'lose diagnosisImgs') {
            this.$message.error('未选择加入报告的诊断图');
            window.top.postMessage({
              message: 'valiResponse',
              data: {isVail: false}
            }, '*');
            return 'stop';
          }
          rptImgs = canvasFlag;
        }
      }
      let data = {
        optType: isSubmit,
        description: description,
        impression: impression,
        recommendation: recommendation,
        docAttr: this.resultData,
        busInfo: {},
        reportInfo,
        rptImgs
        // mainFlag: '1',
      };
      // 用于区分是否要获取原始设备数据，所以每次都传
      if(this.docOrign) {
        data.docOrign = {
          // updateFlag: '1',
          // ...docOrign
        }
      }
      let postParams = this.saveParams(data);
      // 缓存当前报告数据
      this.setJsonToStorage(postParams);
      if(!onlySetStorage) {
        let allPatternDataList = this.combineResultText();   //所有当前模板数据，包含嵌套的子模版
        // console.log('allPatternDataList',allPatternDataList);
        // return;
        if(toRedis) {
          return allPatternDataList;
        }
        let apiRes = await this.saveDataByApi(allPatternDataList, true, false, params.saveNewDoc); // params.saveNewDoc
        let pacsRes = true;
        if(apiRes) {
          // 调用模板内部私有方法
          if(this.rtStructure && this.rtStructure.patternPrivateFunction 
            && typeof this.rtStructure.patternPrivateFunction === 'function') {
            this.rtStructure.patternPrivateFunction({...params, ...apiRes});
          }
        }
        if(this.patientInfo.viewId !== '9' && this.patternInfo.patternType !== '9') {
          if(apiRes) {
            let yParams = {
              ...postParams,
              ypacsRptInfo: this.ypacsRptInfo,
              description: apiRes.docContent?.description,
              impression: apiRes.docContent?.impression,
              recommendation: apiRes.docContent?.recommendation,
            }
            if(Number(this.sourceType) > 1 || this.isFromYunPacs) {
              pacsRes = await this.saveOrSubmitToYPacs(yParams, isSubmit, !this.docId);
            } else {
              // 非云pacs或sourceType<=1下仅上传报告图像
              pacsRes = await this.saveOrSubmitToYPacs(yParams, isSubmit, !this.docId, true);
            }
  
            if(!pacsRes) {
              return false;
            }
          }
          if(apiRes && pacsRes) {
            window.localStorage.removeItem(constant.SR_ALL_PATDOCID);
            window.localStorage.removeItem(constant.SR_ALL_CONTENT);
            return apiRes;
          }
        } else {
          if(apiRes) {
            return apiRes;
          }
        }
      }
      return true;
    },

    // 结构化诊断库 将整个页面转图片
    async pageToCanvas() {
      if(this.rtStructure && this.rtStructure.diffConfig && this.rtStructure.diffConfig.dignosisImgs) {
        if(this.rtStructure.diffConfig.dignosisImgs.length) {
          return this.rtStructure.diffConfig.dignosisImgs;
        } else {
          return 'lose diagnosisImgs'   
        }
      }
      // noUploadPdf 为true时不上传pdf
      if(this.rtStructure && this.rtStructure.diffConfig && this.rtStructure.diffConfig.noUploadPdf) {
        return false;
      }
      let container = document.getElementById(this.domId);
      if(this.rtStructure && this.rtStructure.diffConfig && this.rtStructure.diffConfig.pdfContainer) {
        container = this.rtStructure.diffConfig.pdfContainer;
      }
      let imgData = await this.toPDF(container);
      // 作为诊断图加入模板的名称
      let widgetName = $(this.rtStructure.ele).find('[widget-name]').attr('widget-name') || '';
      let imgName = `${this.patientInfo.busId}_${this.patientInfo.docNo || '1'}_${widgetName}.jpg`
      this.createPdfFlag = false;

      // let link = document.createElement('a');
      // link.href = imgData;
      // link.download = 'image.png';
      // link.click();

      return [{imgContent: imgData, imgName}];
    },

    // 缓存当前节点数据集合，以及模板id，模板id用于排序
    setJsonToStorage(postParams) {
      let srAllContent = JSON.parse(window.localStorage.getItem(constant.SR_ALL_CONTENT) || '{}');
      let srAllPtIds = JSON.parse(window.localStorage.getItem(constant.SR_ALL_PATDOCID) || '[]');
      let curPatDcoId = this.patientInfo.patDocId;
      srAllPtIds = srAllPtIds.filter(ptId => ptId !== curPatDcoId);
      if(!postParams.docContent || !postParams.docContent.docAttr || !postParams.docContent.docAttr.length) {
        delete srAllContent[curPatDcoId];
      } else {
        srAllPtIds.push(curPatDcoId);
        srAllContent[curPatDcoId] = postParams;
      }
      window.localStorage.setItem(constant.SR_ALL_PATDOCID, JSON.stringify(srAllPtIds));
      window.localStorage.setItem(constant.SR_ALL_CONTENT, JSON.stringify(srAllContent));
    },

    // 组装当使用多份模板时，拼接描述和诊断到所有模板上
    combineResultText() {
      let srAllContent = JSON.parse(window.localStorage.getItem(constant.SR_ALL_CONTENT) || '{}');
      let srAllPtIds = JSON.parse(window.localStorage.getItem(constant.SR_ALL_PATDOCID) || '[]');
      let desList = [];
      let impressList = [];
      let recommendList = [];
      let allDataList = [];
      let srChildPatternContent = [];
      srAllPtIds.forEach(ptId => {
        if(srAllContent[ptId]) {
          let {docContent = {}, patternId = '', patDocId = '', isChild} = srAllContent[ptId];
          let {impression = '', description = '', busInfo = {}, recommendation = '' } = docContent;
          if(busInfo.busId === this.patientInfo.busId && patternId === this.patientInfo.patternId) {
            if(ptId !== this.patientInfo.patDocId) {
              impression && impressList.push(impression);
              description && desList.push(description);
              recommendation && recommendList.push(recommendation);
              srChildPatternContent.push({
                patDocId: patDocId,
                docContent: {
                  impression,
                  description,
                  recommendation,
                }
              })
            } else {
              // 主模板的内容放在最前面
              srAllContent[ptId].mainFlag = '1';
              impression && impressList.unshift(impression);
              description && desList.unshift(description);
              recommendation && recommendList.push(recommendation);
            }
          }
          if(!isChild) {
            allDataList.push(srAllContent[ptId]);
          } 
        }
      })
      if(allDataList.length) {
        for(let item of allDataList) {
          if(item.mainFlag !== '1') {
            continue;
          }
          if(!item.docContent) {
            item.docContent = {};
          }
          item.docContent['description'] = desList.join('\n');
          item.docContent['impression'] = impressList.join('\n');
          item.docContent['recommendList'] = recommendList.join('\n');
          item.docContent['srChildPatternContent'] = srChildPatternContent;
        }
      }
      return allDataList;
    },

    // 内部方法
    async srCommonInnerOpt(type, oldMode, printType) {
      if(oldMode) {
        let typeKy = {  //旧的数据对应公共方法的type
          '0': '1',
          '1': '2',
          '2': '3',
        }
        type = typeKy['type'];
      }
      let previewType = printType || this.rptOuterTypeMap[type].previewType;
      // 1预览  2预览并提交pdf 3预览并打印
      if(['1', '2', '3'].includes(previewType)) {
        let query = {...this.localQueryParams, entryType: '1'};
        if(this.$children && this.$children.length && this.$children[0].localQueryParams && !this.$children[0].$el?.classList?.contains('diseaseLayout')) {
          query = {...this.$children[0].localQueryParams, entryType: '1'};
        }
        if(['2', '3'].includes(previewType)) {
          query.print = previewType === '2' ? '1' : '2';  //1为提交pdf 2为打印
        } else {
          delete query.print;
        }
        getOrSetCurReportLocal({vm: this, type:'set', data: query});
        let queryObj = {...this.$route.query, type: type};
        delete queryObj.outParam;
        if(this.outerParam) {
          queryObj.outParam = window.btoa(JSON.stringify(this.outerParam));
        }
        this.$router.push({
          name: "srLayout",
          query: queryObj,
        });
      }
    },

    // 回调消息通知
    srCommonSendMessage(res, isSubmit) {
      if(!window.srSendMessage || typeof window.srSendMessage !== 'function') {
        console.error('srSendMessage方法不存在');
        return false;
      }
      if(res !== 'stop') {
        if(this.outerParam && typeof res === 'object') {
          res.param = this.outerParam;
        }
        let winName = this.localQueryParams.winName || '';
        if(!res) {
          // console.error('保存失败');
          window.srSendMessage(res, isSubmit, winName);
          return false;
        }
        if(res !== 'pdf') {
          window.srSendMessage(res, isSubmit, winName);
        }
        return Boolean(res);
      }
    },

    // 监听print=1上传pdf
    async srCommonUploadPdf(container) {
      let resParam = {
        submitType: 'pdf', 
        type: this.$route.query.type || '30', 
        status: '0'
      };
      if(!this.outerParam) {
        this.outerParam = this.getOutParam();
      }
      if(this.outerParam) {
        resParam.param = this.outerParam;
      }
      // noUploadPdf 为true时不上传pdf
      if(this.rtStructure && this.rtStructure.diffConfig && this.rtStructure.diffConfig.noUploadPdf) {
        if(window.srSendMessage && typeof window.srSendMessage === 'function') {
          window.srSendMessage(resParam, false, this.localQueryParams.winName || '');
        }
        return 'pdf';
      }
      if(this.patientInfo.viewId === '9' || this.patternInfo.patternType === '9') {
        this.createPdfFlag = true;
        this.$nextTick(async () => {
          let canvasFlag = await this.pageToCanvas();
          let rptImgs = [];
          if(canvasFlag) {
            if(canvasFlag === 'lose diagnosisImgs') {
              resParam.status = '1';
              resParam.message = '未插入诊断图';
            } else {
              rptImgs = canvasFlag;
            }
          }
          if(window.srSendMessage && typeof window.srSendMessage === 'function') {
            window.srSendMessage({
              ...resParam, 
              docContent: {rptImgs}
            }, false, this.localQueryParams.winName || '');
          }
        })
        return 'pdf';
      }
      if(this.rtStructure && this.rtStructure.diffConfig && this.rtStructure.diffConfig.pdfContainer) {
        container = this.rtStructure.diffConfig.pdfContainer;
      }
      if(container) {
        container.classList.add('cusPdfSty');
      }
      this.createPdfFlag = true;
      this.$nextTick(async() => {
        if(!container) {
          container = document.querySelector(".diseaseLayout");
          container.classList.add('print-status');
        }
        // let diseaseLayout = document.querySelector('.diseaseLayout');
        let bodyArea = document.querySelector('.disease-body');
        // 如果出现滚动条则暂时去掉layout的弹性布局，打印完成再加上
        if(bodyArea.scrollHeight > bodyArea.clientHeight) {
          container.style.display = 'block';
          document.querySelector(".main-page") && (document.querySelector(".main-page").style.height = 'auto');
        }
        if(this.printPageBreakHandler && typeof this.printPageBreakHandler === 'function') {
          // 病理pacs在预览时已处理
          if(!this.fromWebpacs) {
            await this.printPageBreakHandler();
          }
          $('#'+this.domId).find('.rt-print-add-page').css('margin-top', '0');
          let width = $(container).outerWidth();
          this.originPrintContainer = $(container).parent();
          this.originPrintStyle = {
            width: this.originPrintContainer.css('width'),
            height: this.originPrintContainer.css('height'),
            padding: this.originPrintContainer.css('padding'),
          }
          $(container).parent().css({
            'width': width + 'px',
            'height': 'auto',
            'padding': '0'
          });
          container = $(container).parent()[0];
        }
        let pdfRes = await this.toPDF(container);
        if(window.srSendMessage && typeof window.srSendMessage === 'function') {
          if(typeof pdfRes === 'object') {
            pdfRes.submitType = 'pdf';
            pdfRes.type = '30';
          }
          window.srSendMessage(pdfRes, false, this.localQueryParams.winName || '');
        }
      })
      return 'pdf';
    },

    // 恢复页面的样式
    commResetDocStyle(ele) {
      if(ele.classList.contains('cusPdfSty')) {
        ele.classList.remove('cusPdfSty');
      } else {
        let container = document.querySelector(".diseaseLayout");
        if(container) {
          container.classList.remove('print-status');
          container.style.display = 'flex';
          document.querySelector(".main-page") && (document.querySelector(".main-page").style.height = '100%');
        }
      }
      this.createPdfFlag = false;
    },

    // 获取模板列表
    async getPatternList(patternId) {
      if(!patternId) {
        this.$message.error('该模板不存在,请联系管理员');
        return [];
      }
      let params = {
        patternId: patternId,
        pageSize: 0,
        pageNo: 0
      };
      if(!this.patientInfo.patDocId) {
        params.mainFlag = '1';
      }
      let res = await request(api.patternList, "post", params, {
        headers: { token: this.token },
      });
      if(!res || res.status !== '0') {
        if(res) {
          this.$message.error(res && res.message ? res.message : '该模板不存在,请联系管理员');
        }
        return [];
      }
      return res.result || [];
    },

    // 获取模板详情
    async getPatternContent(patDocId) {
      this.patternInfo = {};
      if(!patDocId) {
        this.$message.error('该模板不存在,请联系管理员');
        return '';
      }
      let params = {
        patDocId: patDocId,
        withPrint: this.localQueryParams.entryType !== '0',  //非编辑状态获取打印页面
        examNo: this.patientInfo.busId
      };
      let res = await request(api.patternInfo, "post", params, {
        headers: { token: this.token },
      });
      if(!res || res.status !== '0') {
        if(res) {
          this.$message.error(res && res.message ? res.message : '该模板不存在,请联系管理员');
        }
        return '';
      }
      let {patternDoc = '', patternId = ''} = res.result || {};
      this.$set(this.patientInfo, 'patDocId', patDocId);
      this.$set(this.patientInfo, 'patternId', patternId);
      this.patternInfo = res.result || {};
      this.patternInfo.docId = this.docId;
      let {patternInfo = {}} = this.patternInfo || {};
      this.patternInfo = {...this.patternInfo, ...patternInfo};
      let query = {...this.localQueryParams, patternName: this.patternInfo.fileName || this.patternInfo.patternInfo?.patternName, showTitle: this.patternInfo.showTitle || this.patternInfo.patternInfo?.showTitle};
      window.parent.selfParams = query;
      return patternDoc;
    },

    // 通过模板id获取模板内容
    async getPatternDocByPatternId(patternId) {
      let patternList = await this.getPatternList(patternId);
      if(!patternList || !patternList.length) {
        return '';
      }
      let patDocId = patternList[0].patDocId || patternId;
      let patternDoc = await this.getPatternContent(patDocId);
      this.$set(this.patientInfo, 'patDocId', patDocId);
      this.$set(this.patientInfo, 'patternId', patternId);
      return patternDoc;
    },

    // 找出指定id的值
    findValByIdCommon(data, id, joinChar) {
      let idData = data.filter(item => item.id.endsWith(id));
      let value = [];
      if(idData && idData.length) {
        value = idData.map(item => item.val);
      }
      return value.join(joinChar || '、');
    },

    // 保存doc节点并更新reportId，进而后端生成pdf
    async saveDocToUpdateReportId() {
      await this.srCommonOuterOptHandler({type: '1'});
      let {reportId, busId, busType, docNo} = this.saveDocResult || {};
      if(!reportId) {
        return false;
      }
      // 保存节点成功更新reportId
      let params = {
        examNo: busId,
        reportNo: docNo,
        reportId
      }
      await request(api.updateReportId, "post", params, {
        headers: { token: this.token },
      });
    },

    // 保存客户端日志
    saveClientLog(logMsg) {
      if(window.parent.saveClientOperateLogHandler && typeof window.parent.saveClientOperateLogHandler === 'function') {
        window.parent.saveClientOperateLogHandler(logMsg);
      }
    }
  }
}