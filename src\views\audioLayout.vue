<template>
  <div class="audio-layout-wrap" v-if="isShow">
    <div class="audio-panel">
      <div class="word-tip-wrap">
        <div class="tl-wrap">
          <span class="tl-text">说法提示</span>
        </div>
        <div class="inner-wrap">
          <el-collapse class="tips-list" v-model="templateAudioConfig.activeCollapseNames" v-if="configMode === 1">
            <el-collapse-item
              class="tip-item"
              v-for="(word, wIdx) in templateAudioConfig.sampleWordList"
              :name="wIdx + ''"
              :key="wIdx"
              :ref="word.id"
            >
              <div class="tip-hd" slot="title">
                <div class="tip-order">{{ wIdx + 1 }}</div>
                <div class="tip-main-text" v-html="getTemplate(word)"></div>
              </div>
              <div class="tip-cont" v-if="word.subWords && word.subWords.length">
                <el-tag
                  class="tip-sub-text"
                  :type="word.matchSubIndex === sIdx ? 'danger' : ''"
                  effect="plain"
                  v-for="(subWord, sIdx) in word.subWords"
                  :key="sIdx"
                >{{ subWord }}</el-tag>
              </div>
            </el-collapse-item>
          </el-collapse>
          <el-tabs v-model="templateAudioConfig.activeTab" v-if="configMode === 2" type="border-card">
            <el-tab-pane v-for="tab in templateAudioConfig.wordTabList" :label="tab.tabName" :name="tab.tabName" :key="tab.tabName">
              <el-collapse class="tips-list" v-model="tab.activeCollapseNames">
                <el-collapse-item
                  class="tip-item"
                  :class="group.type === 'group' && 'group-tl'"
                  v-for="(group, gIdx) in tab.groupList"
                  :name="group.groupName"
                  :key="group.groupName"
                  :ref="group.id"
                >
                  <template v-if="group.type === 'group'">
                    <div class="tip-hd" slot="title">
                      <div class="tip-main-text">{{ group.groupName }}</div>
                    </div>
                    <div class="tip-cont" v-if="group.subWords && group.subWords.length">
                      <el-tag
                        class="tip-sub-text"
                        :type="group.matchSubIndex === sIdx ? 'danger' : ''"
                        effect="plain"
                        v-for="(subWord, sIdx) in group.subWords"
                        :key="sIdx"
                      >{{ subWord }}</el-tag>
                    </div>
                    <el-collapse class="tips-list inside" v-model="group.activeCollapseNames">
                      <el-collapse-item
                        class="tip-item white-tl"
                        v-for="(word, wIdx) in group.sampleWordList"
                        :name="wIdx + ''"
                        :key="wIdx"
                        :ref="word.id"
                      >
                        <div class="tip-hd" slot="title">
                          <div class="tip-order">{{ wIdx + 1 }}</div>
                          <div class="tip-main-text" v-html="getTemplate(word)"></div>
                        </div>
                        <div class="tip-cont" v-if="word.subWords && word.subWords.length">
                          <el-tag
                            class="tip-sub-text"
                            :type="word.matchSubIndex === sIdx ? 'danger' : ''"
                            effect="plain"
                            v-for="(subWord, sIdx) in word.subWords"
                            :key="sIdx"
                          >{{ subWord }}</el-tag>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </template>
                  <template v-else>
                    <div class="tip-hd" slot="title">
                      <div class="tip-order">{{ gIdx + 1 }}</div>
                      <div class="tip-main-text" v-html="getTemplate(group)"></div>
                    </div>
                    <div class="tip-cont" v-if="group.subWords && group.subWords.length">
                      <el-tag
                        class="tip-sub-text"
                        :type="group.matchSubIndex === sIdx ? 'danger' : ''"
                        effect="plain"
                        v-for="(subWord, sIdx) in group.subWords"
                        :key="sIdx"
                      >{{ subWord }}</el-tag>
                    </div>
                  </template>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="record-status-wrap">
        <div class="tl-wrap">
          <span class="tl-text">识别结果</span>
          <div>
            <span class="time-count el-icon-time" v-if="isStartRecord">
              <span class="time-text">{{ recordTimeStr }}</span>
            </span>
            <el-button size="mini" :disabled="isStartRecord && !isPauseRecord || tabName === '3'" @click="handleStartRecord()">开始</el-button>
            <el-button size="mini" :disabled="!isStartRecord || isPauseRecord || tabName === '3'" @click="handlePauseRecord()">暂停</el-button>
            <el-button size="mini" :disabled="!isStartRecord || tabName === '3'" @click="handleStopRecord()">结束</el-button>
          </div>
        </div>
        <el-tabs type="border-card" @tab-click="handleTabClick" v-model="tabName">
          <el-tab-pane name="1">
            <span slot="label" class="tab-hd-text"><img src="@/assets/images/audio-icon.png"> 所见</span>
            <div class="match-list">
              <div class="match-item" v-for="(res, i) in matchResultList" :key="i">{{ res }}</div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="2">
            <span slot="label" class="tab-hd-text"><img src="@/assets/images/edit-icon.png"> 插入</span>
            <div class="record-text">{{ insertTextList.join('') }}</div>
          </el-tab-pane>
          <el-tab-pane name="3" :disabled="isStartRecord">
            <span slot="label" class="tab-hd-text"><img src="@/assets/images/list-icon.png"> 记录</span>
            <div class="audio-list">
              <div
                class="file-item"
                :class="[audio === selectedAudio && 'active']"
                v-for="(audio, i) in audioList"
              >
                <span class="file-name">{{ audio }}</span>
                <span class="play-file" @click="toggleSeceltAudio(audio)">{{selectedAudio === audio ? '停止' : '播放'}}</span>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-button size="mini" class="shot-text-btn" @click="showTextReceive()">文本</el-button>
      </div>
    </div>
    <el-dialog title="收到文本记录" :visible.sync="isShowTextRecord" :append-to-body="true" class="text-record-cont" top="16px">
      <ul>
        <li v-for="(t, i) in textRecordList" :key="i">{{ t }}</li>
      </ul>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/config/api.js';
import { pinyin } from 'pinyin-pro';
import similarity from 'string-similarity';
import { request } from '@/utils/common.js';
import dayjs from 'dayjs';

export default {
  name: "audioLayout",
  components: {},
  data() {
    return {
      isShow: false,
      examNo: null,
      reportNo: null,
      patternId: null,
      activeCollapseNames: [], // 说法提示折叠面板
      tabName: '1', // 识别结果tab 1所见 2插入 3记录
      lastTabName: '1',
      configMode: 1, // 模式 1旧版，字段templateAudioConfig.sampleWordList 2支持tab，字段templateAudioConfig.wordTabList

      // 语音结构化配置，各模板提供
      templateAudioConfig: {
        descriptionEle: '',
        impressionEle: '',
        sampleWordList: [],
        activeTab: '',
        wordTabList: [],
      },
      publicInfo: {}, // 当前检查信息 {busId, examNo, reportNo, patLocalId, patDocId: '2123v2', patternId: '2123'}
      startWords: ['kai shi lu yin', 'kai qi lu yin', 'da kai lu yin'],
      diagnosisWords: ['suo jian', 'qie huan suo jian', 'qie huan dao suo jian'],
      insertWords: ['cha ru', 'qie huan cha ru', 'qie huan dao cha ru'],
			finishWords: ['jie shu lu yin', 'ting zhi lu yin', 'guan bi lu yin'],
      matchThreshold: 0.5, // 相似度匹配的阈值
      matchResultList: [],
      lastWords: [], // 前面没匹配到的句子记录
      lastWordsResetTimeMs: 5000,
      lastWordsResetTT: null,

      inputEles: [], // 输入元素列表
      curInputEle: null, // 选择的输入元素
      curStartText: '', // 光标前面的内容
      curEndText: '', // 光标后面的内容
      insertTextList: [], // 插入的内容列表

      audioList: [], // 录音记录文件列表
      selectedAudio: null,
      audioPlayer: null,

      isStartRecord: false, // 是否开始录音
      isPauseRecord: false, // 是否暂停录音
      recordStartTimeMs: 0, // 开始录音的时间
      recordedTimeMs: 0, // 暂停前的录音时长
      recordTimeStr: '00:00',
      textRecordList: [], // 收到的每句文字记录
      isShowTextRecord: false,
    }
  },
  computed: {
    // PACS传来的语音结构化指令
    audioCtrlMsg() {
      return this.$store.state.audioCtrlMsg;
    },
    userInfo() {
      return this.$store.state.userInfo || {};
    },
  },
  watch: {
    audioCtrlMsg() {
      if (!this.isShow) {
        return;
      }
      // {
      //   type:"9", // 9语音结构化指令
      //   status:"0",// 状态，0成功 1失败
      //   result:
      //   {
      //     examNo:
      //     reportNo:
      //     actType:   // 0:传递语音识别内容；1:开始录音；2:暂停录音 3:结束录音
      //     content:   // 语音识别内容,actType=0时必传
      //   }
      // }
      let {type, status, message, result, onlySetStorage = false, toRedis, param = {}, id} = this.audioCtrlMsg;
      let { patternId, examNo, reportNo, actType, content, response } = result || {};
      if (response) { // 有response参数的表示C端响应的消息
        if (status === '1') { // 失败
          console.log('【语音结构化】PACS响应指令 失败');
          return;
        }
        switch (actType) {
          case '0':
            console.log('【语音结构化】PACS响应指令0-识别内容');
            break;
          case '1':
            console.log('【语音结构化】PACS响应指令1-开始录音');
            break;
          case '2':
            console.log('【语音结构化】PACS响应指令2-暂停录音');
            break;
          case '3':
            console.log('【语音结构化】PACS响应指令3-结束录音');
            this.getAudioList();
            break;
        }
        return;
      }
      switch (actType) {
        case '0':
          console.log('【语音结构化】PACS指令0-识别内容', {content});
          console.time('【语音结构化】PACS指令0-识别内容');
          this.saveContentToLog(content); // 记录文本到日志
          this.splitContent(content);
          console.timeEnd('【语音结构化】PACS指令0-识别内容');
          break;
        case '1':
          console.log('【语音结构化】PACS指令1-开始录音');
          if (!['1', '2'].includes(this.tabName)) {
            this.tabName = '1';
            this.lastTabName = this.tabName;
          }
          this.startRecord();
          break;
        case '2':
          console.log('【语音结构化】PACS指令2-暂停录音');
          this.pauseRecord();
          break;
        case '3':
          console.log('【语音结构化】PACS指令3-结束录音');
          this.stopRecord();
          this.getAudioList();
          break;
      }
      // 结构化响应C端的消息
      this.$emit('srCommonSendMessage', {
        type,
        status: '0',
        result: {
          patternId,
          examNo,
          reportNo,
          actType,
          content,
          response: '1', // 加上response表示响应
        }
      });
    },
  },
  created() {
    window.top.addEventListener('message', this.handleMessage);
  },
  mounted() {
    
  },
  activated() {

  },
  deactivated() {
    this.unloadPage();
  },
  destroyed() {
    this.unloadPage();
  },
  methods: {
    handleMessage(e) {
      if (e.data && e.data.message === 'srLoadSuccess') {
        this.initConfig();
      }
    },
    initConfig() {
      if (!window.templateAudioConfig) {
        return;
      }
      console.log('【语音结构化】模板加载完成，开始语音面板初始化');
      this.templateAudioConfig = window.templateAudioConfig;
      if (this.templateAudioConfig.wordTabList) {
        this.configMode = 2;
      } else {
        this.configMode = 1;
      }
      this.isShow = true;
      this.$emit('showAudio', true);
      if (window.publicInfo) {
        this.publicInfo = window.publicInfo;
        this.examNo = this.publicInfo.examNo;
        this.reportNo = this.publicInfo.reportNo;
        this.patternId = this.publicInfo.patternId;
      }
      this.transformKeyWord();
      document.addEventListener('click', this.handleInputEle);
      document.addEventListener('keyup', this.handleInputEle);
      this.getAudioList();
    },
    unloadPage() {
      document.removeEventListener('click', this.handleInputEle);
      document.removeEventListener('keyup', this.handleInputEle);
      window.top.removeEventListener('message', this.handleMessage);
    },
    handleTabClick() {
      if (this.tabName === this.lastTabName && ['1', '2'].includes(this.tabName)) {
        if (this.isStartRecord) {
          if (this.isPauseRecord) {
            this.handleStartRecord();
          } else {
            this.handleStopRecord();
          }
        } else {
          this.handleStartRecord();
        }
      }
      if(this.tabName === '3') {
        this.getAudioList();
      }
      this.lastTabName = this.tabName;
    },
    switchToTab(tabName) {
      this.tabName = tabName;
      this.lastTabName = this.tabName;
    },
    // 把关键词转成拼音
    transformKeyWord() {
      let hotWords = [];
      if (this.configMode === 1) {
        let activeCollapseNames = [];
        this.templateAudioConfig.sampleWordList.forEach((word, idx) => {
          activeCollapseNames.push(idx + '');
          this.initWordItem(word, hotWords);
        });
        this.$set(this.templateAudioConfig, 'activeCollapseNames', activeCollapseNames);
      }
      if (this.configMode === 2) {
        this.templateAudioConfig.wordTabList.forEach(item => {
          this.initWordItem(item, hotWords);
        });
      }
      hotWords = this.hotwordProcess(hotWords);
      this.templateAudioConfig.content = hotWords?.join(',');
    },
    // 设置配置项的初始数据
    initWordItem(item, hotWords) {
      this.$set(item, 'id', this.getRandomId());
      if (item.type === 'tab') { // tab
        this.$set(item, 'tabNamePy', pinyin(item.tabName, {pattern: 'pinyin', toneType: 'none'}).toLowerCase());
        this.$set(item, 'activeCollapseNames', item.activeCollapseNames || []); // 展开的分组 [groupName]
        this.splitHotWord(item.tabName, hotWords);
        item.groupList.forEach(group => {
          this.initWordItem(group, hotWords);
        });
      }
      if (item.type === 'group') { // 分组
        this.$set(item, 'groupNamePy', pinyin(item.groupName, {pattern: 'pinyin', toneType: 'none'}).toLowerCase());
        this.$set(item, 'subWordsPy', item.subWords.map(sub => pinyin(sub, {pattern: 'pinyin', toneType: 'none'}).toLowerCase()));
        this.$set(item, 'matchSubIndex', -1);
        this.$set(item, 'activeCollapseNames', item.activeCollapseNames || []); // 展开的提示词 [index]
        this.splitHotWord(item.groupName, hotWords);
        item.subWords.forEach(sw => this.splitHotWord(sw, hotWords));
        item.sampleWordList.forEach(child => {
          this.initWordItem(child, hotWords);
        });
      }
      if (!item.type) { // 提示词
        this.$set(item, 'mainWordPy', pinyin(item.mainWord, {pattern: 'pinyin', toneType: 'none'}).toLowerCase());
        this.$set(item, 'subWordsPy', item.subWords.map(sub => pinyin(sub, {pattern: 'pinyin', toneType: 'none'}).toLowerCase()));
        item.aliasWords && this.$set(item, 'aliasWordsPy', item.aliasWords.map(sub => pinyin(sub, {pattern: 'pinyin', toneType: 'none'}).toLowerCase()));
        this.$set(item, 'isMatchMain', false);
        this.$set(item, 'matchSubIndex', -1);
        this.$set(item, 'val', []);
        this.splitHotWord(item.mainWord, hotWords);
        item.subWords.forEach(sw => this.splitHotWord(sw, hotWords));
      }
    },
    getRandomId() {
      return ('d' + Math.random()).replace(/\./g, '');
    },
    // 分割热词，去掉英文数字
    splitHotWord(wordText, hotWords) {
      let wSplit = wordText.split(/[0-9a-z]+/gi);
      for (let w of wSplit) {
        if (w.length > 1) {
          hotWords.push(w);
        }
      }
    },
    // 热词去重
    hotwordProcess(hotWords) {
      return Array.from(new Set(hotWords));
    },
    handleStartRecord() {
      // 开始录音停止播放中的记录
      this.resetPlayer();
      let d = this.getSendData('1');
      console.log('【语音结构化】点击-开始录音', d);
      this.$emit('srCommonSendMessage', d); // 通知C端开始录音
      this.startRecord();
    },
    handlePauseRecord() {
      let d = this.getSendData('2');
      console.log('【语音结构化】点击-暂停录音', d);
      this.$emit('srCommonSendMessage', d); // 通知C端暂停录音
      this.pauseRecord();
    },
    handleStopRecord() {
      let d = this.getSendData('3');
      console.log('【语音结构化】点击-结束录音', d);
      this.$emit('srCommonSendMessage', d); // 通知C端结束录音
      this.stopRecord();
    },
    getSendData(actType) {
      let result = {
        patternId: this.patternId,
        examNo: this.examNo,
        reportNo: this.reportNo,
        actType,
      };
      // 开始语音时，发送语音识别内容
      if(actType === '1') {
        result.content = this.templateAudioConfig.content;
      } 
      return {
        type: '9',
        status: '0',  //触发pacs动作默认传0
        result,
      };
    },
    // 开始录音
    startRecord() {
      if (this.isStartRecord) {
        if (this.isPauseRecord) {
          // 恢复录音
          this.isPauseRecord = false;
          this.recordStartTimeMs = Date.now();
          this.getRecordTimeStr();
        }
        return;
      }
      this.isStartRecord = true;
      this.isPauseRecord = false;
      this.recordStartTimeMs = Date.now();
      this.recordedTimeMs = 0;
      this.insertTextList = [];
      this.getRecordTimeStr();
    },
    // 获取录音时间显示
    getRecordTimeStr() {
      if (!this.isStartRecord || this.isStartRecord && this.isPauseRecord) {
        return;
      }
      let deltaMs = Date.now() - this.recordStartTimeMs + this.recordedTimeMs;
      let secTotal = Math.floor(deltaMs / 1000);
      let minut = Math.floor(secTotal / 60) + '';
      let secLeft = secTotal % 60 + '';
      this.recordTimeStr = minut.padStart(2, '0') + ':' + secLeft.padStart(2, '0');
      setTimeout(() => {
        this.getRecordTimeStr();
      }, 1000);
    },
    // 暂停录音
    pauseRecord() {
      this.isPauseRecord = true;
      this.recordedTimeMs = Date.now() - this.recordStartTimeMs + this.recordedTimeMs; // 记录暂停前的录音时长
    },
    // 结束录音
    stopRecord() {
      this.isStartRecord = false;
    },
    // 用数字划分句子
    splitContent(sentence) {
      sentence = sentence.replace(/乘以?/g, 'x');
      if (this.lastWords.length > 0) {
        sentence = this.lastWords.join('') + sentence; // 拼接上次匹配到关键词没匹配到数值的记录
      }
      let arr = sentence.match(/[^\d.]+-?\d+(\.\d+)?([xX×]?(-?\d+(\.\d+)?)?)*/g); // 匹配数字 数字x数字
      if (arr) {
        arr.forEach(s => {
          this.handleContent(s);
          sentence = sentence.replace(s, ''); // 去除已处理的
        });
        if (sentence) {
          // 处理剩下的
          this.handleContent(sentence);
        }
      } else {
        this.handleContent(sentence);
      }
    },
    // 处理一句内容
    handleContent(sentence) {
      if (!this.isStartRecord || this.isPauseRecord || !sentence) {
        return;
      }
      let content = sentence;
      let {trimText, pinyinText, isCommand} = this.getPinyinText(content);
      if (isCommand) {
        return;
      }
      if (this.tabName === '1') { // 所见
        if (this.configMode === 1) {
          this.matchGroupText(this.templateAudioConfig, {}, {sentence, content, trimText, pinyinText})
        }
        if (this.configMode === 2) {
          this.handleTabMatch({sentence, content, trimText, pinyinText});
        }
        return;
      }
      if (this.tabName === '2') { // 插入
        this.insertText(sentence);
        return;
      }
    },
    getPinyinText(content) {
      let trimText = content.replace(/[。，]/g, '').trim();
      let pinyinText = pinyin(trimText, {pattern: 'pinyin', toneType: 'none'}).toLowerCase();
      let isCommand;
      if (this.startWords.includes(pinyinText)) { // 开始
        isCommand = true;
			} else if (this.diagnosisWords.includes(pinyinText)) { // 所见
        isCommand = true;
        this.switchToTab('1');
      } else if (this.insertWords.includes(pinyinText)) { // 插入
        isCommand = true;
        this.switchToTab('2');
        if (!this.isSelectInputEle()) {
          this.$message.error('请点击所见输入框要插入内容的位置');
        }
			} else if (this.finishWords.includes(pinyinText)) { // 结束
        isCommand = true;
			}
      return {trimText, pinyinText, isCommand};
    },
    // 处理tab匹配
    handleTabMatch({sentence, content, trimText, pinyinText}) {
      this.templateAudioConfig.wordTabList.forEach((tab, tIdx) => {
        this.handleItemText(
          tab,
          {config: this.templateAudioConfig, tab},
          {sentence, content, trimText, pinyinText});
      });
    },
    // 匹配处理单个项目
    handleItemText(item, {config, tab, group}, {sentence, content, trimText, pinyinText}) {
      if (item.type === 'tab') {
        // 处理tab切换
        if (pinyinText === item.tabNamePy) {
          config.activeTab = item.tabName;
          let targetEle = document.querySelector(item.ele);
          if (targetEle) {
            targetEle.checked = true;
            targetEle.dispatchEvent(new Event('click', {bubbles: true}));
            targetEle.dispatchEvent(new Event('input', {bubbles: true}));
            targetEle.dispatchEvent(new Event('change', {bubbles: true}));
          }
          this.matchResultList.unshift(content);
          return;
        }
        // 不是当前tab
        if (config.activeTab !== item.tabName) {
          return;
        }
        // 处理子级
        item.groupList.forEach((g, gIdx) => {
          this.handleItemText(g, {config, tab, group: g}, {sentence, content, trimText, pinyinText});
        });
      }
      if (item.type === 'group') {
        // 处理分组切换
        if (pinyinText === item.groupNamePy) {
          tab.activeCollapseNames = [item.groupName];
          this.showActiveItem(item);
          this.matchResultList.unshift(content);
          return;
        }
        let isMatchGroup, matchSubIndex;
        item.subWordsPy.forEach((subPy, sIdx) => {
          if (pinyinText === subPy) {
            isMatchGroup = true;
            matchSubIndex = sIdx;
          }
        });
        if (isMatchGroup) {
          tab.activeCollapseNames = [item.groupName];
          item.matchSubIndex = matchSubIndex;
          this.showActiveItem(item);
          this.matchResultList.unshift(content);
          return;
        }
        // 不是当前展开分组
        if (!tab.activeCollapseNames.includes(item.groupName)) {
          return;
        }
        // 组内匹配
        this.matchGroupText(item, {config, tab, group}, {sentence, content, trimText, pinyinText});
      }
      if (!item.type) {
        // 单个提示词
      }
    },
    // 组内匹配
    // sentence 原始一句文字
    // content 上一次匹配到关键词但未匹配到数值的句子+sentence
    // trimText content去掉逗号句号
    // pinyinText trimText的拼音
    matchGroupText(grp, {config, tab, group}, {sentence, content, trimText, pinyinText}) {
      let isFullTextMatch; // 是否精确匹配
      let isMatchWordText; // 是否匹配到关键词
      let isMatchVal; // 是否有数值
      let targetWord;  // 目标词
      let targetWordIdx; // 目标词序号

      // 精确匹配
      grp.sampleWordList.forEach((word, wIdx) => {
        if (isFullTextMatch) {
          return;
        }
        let isMatchMain = false; // 匹配到主词
        let matchSubIndex = -1; // 匹配到字词序号
        let matchAliasIndex = -1; // 匹配到别名序号
        if (pinyinText.includes(word.mainWordPy)) { // 主词
          isMatchMain = true;
        }
        word.subWordsPy.forEach((sub, idx) => { // 子词
          if (pinyinText.includes(sub)) {
            matchSubIndex = idx;
          }
        });
        if (word.aliasWordsPy) { // 别名
          word.aliasWordsPy.forEach((sub, idx) => {
            if (pinyinText.includes(sub)) {
              matchAliasIndex = idx;
            }
          });
        }
        if (isMatchMain || matchSubIndex !== -1 || matchAliasIndex !== -1) {
          let val = this.extractNumbers(trimText);
          isMatchVal = val.length > 0;
          isMatchVal && (word.val = val);
          word.isMatchMain = isMatchMain;
          word.matchSubIndex = matchSubIndex;
          // if (isMatchVal) {
            targetWord = word;
            targetWordIdx = wIdx;
          // }
          isFullTextMatch = true;
          isMatchWordText = true;
        }
      });

      // 精确匹配没匹配到时使用模糊匹配，从所有关键词找出匹配度最大的
      if (!isFullTextMatch) {
        let maxMatchScore = 0;
        let targetSubIdx = -1;
        grp.sampleWordList.forEach((word, wIdx) => {
          let matchScoreMain = similarity.compareTwoStrings(pinyinText, word.mainWordPy); // 主词匹配度

          // 子词匹配度
          let maxMatchScoreSub = 0;
          let matchScoreSubIdx = -1;
          word.subWordsPy.forEach((sub, idx) => {
            let matchScoreSub = similarity.compareTwoStrings(pinyinText, sub);
            if (matchScoreSub > maxMatchScoreSub) {
              maxMatchScoreSub = matchScoreSub;
              matchScoreSubIdx = idx;
            }
          });

          // 别名匹配
          let maxMatchScoreAlias = 0;
          let matchScoreAliasIdx = -1;
          if (word.aliasWordsPy) {
            word.aliasWordsPy.forEach((sub, idx) => {
              let matchScoreSub = similarity.compareTwoStrings(pinyinText, sub);
              if (matchScoreSub > maxMatchScoreAlias) {
                maxMatchScoreAlias = matchScoreSub;
                matchScoreAliasIdx = idx;
              }
            });
          }

          let targetMatchScore = Math.max(matchScoreMain, maxMatchScoreSub, maxMatchScoreAlias); // 获取当前关键词的最大匹配度
          if (targetMatchScore > maxMatchScore) {
            maxMatchScore = targetMatchScore;
            targetWord = word;
            targetWordIdx = wIdx;
            if (maxMatchScoreSub > matchScoreMain) {
              targetSubIdx = matchScoreSubIdx;
            } else {
              targetSubIdx = -1;
            }
          }
        });

        if (targetWord && maxMatchScore >= this.matchThreshold) {
          let val = this.extractNumbers(trimText);
          isMatchVal = val.length > 0;
          isMatchVal && (targetWord.val = val);
          if (targetSubIdx !== -1) {
            targetWord.matchSubIndex = targetSubIdx;
          } else {
            targetWord.isMatchMain = true;
          }
          isMatchWordText = true;
        }
      }

      // 匹配到关键词没匹配到数值时记录到下一次
      if (isMatchWordText && !isMatchVal) {
        this.lastWords.push(sentence);
        clearTimeout(this.lastWordsResetTT);
      }

      if (isMatchWordText) {
        if (isMatchVal) {
          // 赋值、触发输入
          targetWord.eles.forEach((ele, idx) => {
            let targetVal = targetWord.val[idx];
            let targetEle = document.querySelector(ele);
            if (targetVal && targetEle) {
              targetEle.value = targetVal;
              targetEle.dispatchEvent(new Event('input', {bubbles: true}));
              targetEle.dispatchEvent(new Event('change', {bubbles: true}));
            }
          });
          this.lastWords = [];
        }
        this.matchResultList.unshift(content); // 匹配记录
        // 展开
        if (!grp.activeCollapseNames.includes(targetWordIdx + '')) {
          grp.activeCollapseNames.push(targetWordIdx + '');
        }
        this.showActiveItem(targetWord);
      }

      // 延时重置句子记录
      this.lastWordsResetTT = setTimeout(() => {
        // this.lastWords = [];
      }, this.lastWordsResetTimeMs);
    },
    // 显示匹配的项目
    showActiveItem(item) {
      setTimeout(() => {
        this.$refs[item.id][0].$el.scrollIntoView({behavior: 'smooth', block: 'start'});
      }, 400);
    },
    // 匹配数值
    extractNumbers(text) {
      const numberPattern = /-?\d+(\.\d+)?/g;
      const matches = text.match(numberPattern);
      return matches ? matches : [];
    },
    // 获取说法提示模板
    getTemplate(word) {
      let valTemp = word.valTemp;
      let spaceCount = valTemp.replace(/[^_]/g, '').length;
      for (let i = 0; i < spaceCount; i++) {
        valTemp = valTemp.replace('_', `<span class="space">${word.val && word.val[i] || ''}</span>`);
      }
      return word.mainWord + valTemp;
    },

    // 获取输入元素
    getInputEles() {
      if (this.inputEles.length) {
        return;
      }
      let descriptionEle = this.templateAudioConfig.descriptionEle && document.querySelector(this.templateAudioConfig.descriptionEle);
      let impressionEle = this.templateAudioConfig.impressionEle && document.querySelector(this.templateAudioConfig.impressionEle);
      if (!descriptionEle && !impressionEle) {
        return;
      }
      this.inputEles = [descriptionEle, impressionEle];
    },
    // 输入元素处理
    handleInputEle(e) {
      this.getInputEles();
      let target = e.target;
      if (!this.inputEles.includes(target)) {
        return;
      }
      this.curInputEle = target;
      this.getInputEleText();
    },
    // 获取目标输入元素的内容
    getInputEleText() {
      let curSelectionEnd = this.curInputEle.selectionEnd;
      let txt = this.curInputEle.value;
      this.curStartText = txt.substring(0, curSelectionEnd);
      this.curEndText = txt.substring(curSelectionEnd);
    },
    // 是否选择了输入框
    isSelectInputEle() {
      return this.curInputEle && this.inputEles.includes(this.curInputEle);
    },
    // 插入内容
    insertText(sentence) {
      if (!this.isSelectInputEle()) {
        return;
      }
      // 若输入元素的内容变更则重新获取
      let lastText = this.curInputEle.value;
      if (lastText !== this.curStartText + this.curEndText) {
        this.getInputEleText();
      }
      let newStartText = this.curStartText + sentence;
      let newStart = newStartText.length;
      this.curInputEle.value = newStartText + this.curEndText;
      this.curInputEle.setSelectionRange(newStart, newStart);
      // 使光标始终可见
      this.curInputEle.blur();
      this.curInputEle.focus();
      this.curStartText = newStartText;
      this.insertTextList.push(sentence);
    },

    // 获取录音记录
    async getAudioList() {
      if (!this.examNo) {
				return;
			}
      let params = {
				examNo: this.examNo,
			};
			let res = await request(api.getAudioList, 'post', params);
			if (!res || res.status !== '0') {
        this.audioList = [];
				let msg = res ? '：' + res.message : '';
				this.$message.error('获取录音列表出错' + msg);
				return;
			}
			let list = res.result || [];
			this.audioList = list;
		},
    // 点击音频记录
    toggleSeceltAudio(audio) {
			if (audio === this.selectedAudio) {
        this.resetPlayer();
				return;
			}
			this.selectedAudio = audio;
      this.createPlayer();
    },
    // 播放录音
    async createPlayer() {
			let uri = await this.getAudioStream();
			if (!uri) {
				this.$message.error('播放失败');
				URL.revokeObjectURL(uri);
				return;
			}
			this.audioPlayer = new Audio(uri);
			this.audioPlayer.play();
			this.audioPlayer.onplay = () => {

			};
			this.audioPlayer.onerror = err => {
				this.$message.error('播放失败');
				this.resetPlayer();
			};
			this.audioPlayer.onended = () => {
				this.resetPlayer();
			};
		},
    // 获取录音文件流
    async getAudioStream() {
			if (!this.selectedAudio) {
				return;
			}
      let params = {
				examNo: this.examNo,
				fileName: this.selectedAudio,
			};
			let res = await request(api.getAudioStream, 'post', params, {
				responseType: 'blob',
			});
			if (!res) {
				this.$message.error('获取语音文件出错');
				return;
			}
			try {
				return URL.createObjectURL(res);
			} catch (err) {
				console.log(err);
			}
		},
    // 重置播放状态
    resetPlayer() {
      if (!this.audioPlayer) {
				return;
			}
			URL.revokeObjectURL(this.audioPlayer.src);
			this.audioPlayer.pause();
			this.audioPlayer.remove();
			this.audioPlayer = null;
      this.selectedAudio = null;
    },
    // 记录文本到日志
    async saveContentToLog(content) {
      this.textRecordList.push(content);
      if (!this.examNo) {
				return;
			}
      let patientInfo = window.patientInfo || {};
      let params = {
        description: '收到结构化语音文本--->' + JSON.stringify({content, examNo: this.examNo, patternId: patientInfo.patternId, patDocId: patientInfo.patDocId}),
        logDateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        userId: this.userInfo.userId,
        userName: this.userInfo.userName,
			};
      let res = await request(api.saveClientLog, 'post', [params]);
    },
    // 显示文字记录
    showTextReceive() {
      this.isShowTextRecord = true;
    },
  }
}
</script>

<style lang="scss">
.audio-layout-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 360px;
  .audio-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #C0C4CC;
  }
  .word-tip-wrap {
    height: 0;
    flex: auto;
    display: flex;
    flex-direction: column;
    .el-tabs {
      border: none;
      box-shadow: none;
      background: none;
      .el-tabs__header {
        position: absolute;
        right: 8px;
        top: 3px;
        margin: 0;
      }
      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }
      .el-tabs__active-bar {
        display: none;
      }
      .el-tabs__item {
        height: 26px;
        line-height: 26px;
        border: none;
        color: #000;
        background-color: #fff;
        &.is-active {
          color: #fff;
          background-color: #1885F2;
        }
      }
      .el-tabs__content {
        height: 100%;
        padding: 0;
      }
      .el-tab-pane {
        height: 100%;
      }
    }
  }
  .inner-wrap {
    height: 0;
    flex: auto;
    overflow: auto;
    padding: 8px;
  }
  .el-collapse {
    border: none;
  }
  .el-collapse-item {
    &.group-tl {
      .el-collapse-item__header {
        background-color: #E8F2FF;
      }
    }
  }
  .el-collapse-item {
    &.white-tl {
      .el-collapse-item__header {
        background-color: #fff;
      }
    }
  }
  .tips-list {
    &.inside {
      padding: 0 2px 2px;
    }
    .tip-item {
      margin-bottom: 8px;
      border: 1px solid #C0C4CC;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .tip-hd {
      flex: auto;
      display: flex;
    }
    .tip-order {
      width: 39px;
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      text-align: center;
      border-right: 1px solid #C0C4CC;
      background: #E8F2FF;
    }
    .tip-main-text {
      width: 0;
      flex: auto;
      padding-left: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 600;
      font-size: 16px;
      color: #000000;
      .space {
        height: 22px;
        min-width: 30px;
        padding: 0 4px;
        margin: 0 4px;
        display: inline-block;
        line-height: 20px;
        vertical-align: middle;
        text-align: center;
        color: #E64545;
        border-radius: 4px;
        border: 1px solid #C0C4CC;
        background-color: white;
      }
    }
    .tip-cont {
      display: flex;
      flex-wrap: wrap;
      padding-left: 8px;
      padding-top: 8px;
      margin-right: -8px;
    }
    .tip-sub-text {
      margin-right: 8px;
      margin-bottom: 8px;
      height: 28px;
      line-height: 26px;
    }
    .el-collapse-item__header {
      height: 39px;
      line-height: 39px;
    }
    .el-collapse-item__wrap {
      border-top: 1px solid #C0C4CC;
    }
    .el-collapse-item__content {
      padding: 0;
    }
  }
  .record-status-wrap {
    position: relative;
    height: 300px;
    flex-shrink: 0;
    flex-grow: 0;
    border-top: 1px solid #C0C4CC;
    display: flex;
    flex-direction: column;
    .el-tabs {
      height: 0;
      flex: auto;
      border: none;
      box-shadow: none;
      background: none;
      display: flex;
      flex-direction: column;
    }
    .el-tabs__header {
      background: none;
      border: none;
      .el-tabs__item {
        padding: 0 8px;
        background: none;
        border: none;
        height: 36px;
        line-height: 36px;
        font-size: 16px;
        color: #000;
        &.is-active {
          color: #409EFF;
          font-weight: bold;
        }
        &.is-disabled {
          opacity: 0.3;
          cursor: not-allowed;
        }
      }
    }
    .el-tabs__content {
      height: 0;
      flex: auto;
      padding: 0 12px 12px;
      .el-tab-pane {
        height: 100%;
      }
    }
    .tab-hd-text {
      user-select: none;
      img {
        vertical-align: middle;
      }
    }
    .match-list {
      height: 100%;
      overflow: auto;
      border: 1px solid #C0C4CC;
      background-color: white;
      .match-item {
        padding: 4px 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 21px;
        &:before {
          content: "所";
          display: inline-block;
          width: 21px;
          margin-right: 4px;
          overflow: hidden;
          vertical-align: middle;
          text-align: center;
          border-radius: 50%;
          font-size: 12px;
          color: white;
          background-color: #21A64D;
        }
      }
    }
    .record-text {
      height: 100%;
      padding: 4px 8px;
      overflow: auto;
      border: 1px solid #C0C4CC;
      background-color: white;
    }
    .audio-list {
      height: 100%;
      overflow: auto;
      border: 1px solid #C0C4CC;
      background-color: white;
      .file-item {
        padding: 4px 8px;
        line-height: 21px;
        font-size: 14px;
        color: #333;
        display: flex;
        align-items: center;
        &.active { 
          .file-name {
            color: #409EFF;
          }
        }
        .file-name {
          width: calc(100% - 32px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .play-file {
          margin-left: 4px;
          cursor: pointer;
          color: #1885F2;
          &:hover {
            color: #409EFF;
            text-decoration: underline;
          }
        }
      }
    }
    .shot-text-btn {
      position: absolute;
      right: 12px;
      top: 40px;
    }
  }
  .tl-wrap {
    padding: 4px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #E8F2FF;
    border-bottom: 1px solid #C0C4CC;
    .tl-text {
      font-weight: 600;
      font-size: 18px;
      color: #000000;
    }
    .time-count {
      margin-right: 4px;
    }
    .time-text {
      margin-left: 4px;
    }
    .el-button + .el-button {
      margin-left: 4px;
    }
  }
}
.text-record-cont {
  .el-dialog__body {
    padding-top: 0;
    font-size: 16px;
    li {
      margin-bottom: 8px;
    }
  }
}
</style>