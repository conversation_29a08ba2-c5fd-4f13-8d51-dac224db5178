$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var isSavedReport = false; //模板是否填写过
var idAndDomMap = {};  //节点id和值对应的关系
// var examArr = [
//   {
//     "floor": "500",
//     "label": "EB病毒PCR检测(血)",
//     "value": "EB病毒(EBV)"
//   },
//   {
//     "floor": "500",
//     "label": "巨细胞病毒PCR检测(血)",
//     "value": "人巨细胞病毒(HCMV)"
//   },
//   {
//     "floor": "1000",
//     "label": "肺炎支原体PCR检测(血)",
//     "value": "肺炎支原体(MP)"
//   },
//   {
//     "floor": "500",
//     "label": "结核杆菌PCR检测",
//     "value": "结核分枝杆菌复合群(TB)"
//   },
//   {
//     "floor": "1000",
//     "label": "单纯疱疹病毒I型 II型PCR检测",
//     "value": "单纯疱疹病毒I型(HSV I)"
//   },
//   {
//     "floor": "",
//     "label": "沙眼衣原体、肺炎支原体、肺炎衣原体PCR检测",
//     "value": ""
//   },
//   {
//     "floor": "1000",
//     "label": "肠道病毒三联PCR检测",
//     "value": "肠道病毒"
//   },
//   {
//     "floor": "20",
//     "label": "乙型肝炎病毒PCR检测(血液)",
//     "value": "乙型肝炎病毒(HBV)"
//   },
//   {
//     "floor": "5000",
//     "label": "肺炎支原体及耐药突变位点PCR检测",
//     "value": "耐药突变位点"
//   },
//   {
//     "floor": "1000",
//     "label": "腺病毒PCR检测",
//     "value": "腺病毒(ADV)"
//   },
//   {
//     "floor": "500",
//     "label": "巨细胞病毒PCR检测(尿)",
//     "value": "人巨细胞病毒(HCMV)"
//   },
//   {
//     "floor": "500",
//     "label": "巨细胞病毒PCR检测(脑脊液)",
//     "value": "人巨细胞病毒(HCMV)"
//   },
//   {
//     "floor": "500",
//     "label": "EB病毒PCR检测(脑脊液)",
//     "value": "EB病毒(EBV)"
//   },
//   {
//       'floor':'500',
//       'label':'EB病毒PCR检测(肺泡灌洗液)',
//       "value": "EB病毒(EBV)"
//   },
//   {
//     "floor": "1000",
//     "label": "肺炎支原体PCR检测(脑脊液)",
//     "value": "肺炎支原体(MP)"
//   },
//   {
//     "floor": "1000",
//     "label": "肺炎支原体PCR检测(肺泡灌洗液)",
//     "value": "肺炎支原体(MP)"
//   },
//   {
//     "floor": "1000",
//     "label": "肺炎支原体PCR检测(胸水)",
//     "value": "肺炎支原体(MP)"
//   },
//   {
//     floor:"500",
//     label:'结核分枝杆菌复合群',
//     value:'TB'
//   },
//   {
//     floor:"500",
//     label:'人巨细胞病毒',
//     value:'HCMV'
//   },  
//   {
//     floor:"500",
//     label:'EB病毒',
//     value:'EB'
//   }, 
//    {
//     floor:"1000",
//     label:'单纯疱疹病毒Ⅰ型',
//     value:'HSVⅠ'
//   }, 
//    {
//     floor:"1000",
//     label:'单纯疱疹病毒Ⅱ型',
//     value:'HSVⅡ'
//   }, 
//    {
//     label:'EV71型肠道病毒',
//     value:'EV71',
//     floor:"1000",
//   }, 
//   {
//     label:'CA16型肠道病毒',
//     value:'CA16',
//     floor:"1000",
//   },   {
//     label:'肠道病毒',
//     value:'EVU',
//     floor:"1000",
//   },  {
//     label:'腺病毒',
//     value:'ADV',
//     floor:"1000",
//   },  {
//     label:'乙型肝炎病毒',
//     value:'HBV',
//     floor:"20",
//   },  {
//     label:'肺炎支原体',
//     value:'MP',
//     floor:"1000",
//   },  {
//     label:'耐药突变位点',
//     value:'MR',
//     floor:"5000",
//   },  {
//     label:'肺炎支原体',
//     value:'MPP',
//     floor:"5000",
//   }, 
// ]
var selectMap  = {}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: $('#hbetjybl21 .preview'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // 仅为当前模板的特殊条件判断，其他常规模板仍取rtStructure.enterOptions.isSavedReport
    isSavedReport = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.resultData && rtStructure.enterOptions.resultData.length > 0;
    if(rtStructure.enterOptions.type==='view') {
      initPreview()
    } else {
      
      // let resultData = rtStructure.enterOptions.resultData.find(item=>item.name === '检测项目集合')
      // initItemTemplate( (resultData && [resultData]) || [])
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = ''; //getDescription()
  rtStructure.impression = getImpressionStr();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
    memo:getMemo(), // 项目
  }
  // console.log(rtStructure);
}
function initPage(){
  $('input,textarea').attr('autocomplete','off')  
}
function initPreview(){
  let registerDate = publicInfo['registerDate'] || '';
  let registerTime = publicInfo['registerTime'] || '';
  
  curElem.find('#receiveDate').text(registerDate + ' ' + registerTime);
  var sampleList = getSampleInfoForSR(publicInfo.examNo, '2') || [];
  var flowDateTime = getSampleInfoForSR(publicInfo.examNo, 'C') || [];
  if(sampleList.length) {
    curElem.find('#actBeginDateTime').text((sampleList[0].actBeginDate || '') + ' ' + (sampleList[0].actBeginTime || ''));
  }
  if(flowDateTime.length){
    curElem.find('#flowDateTime').text((flowDateTime[0].actBeginDate || '') + ' ' + (flowDateTime[0].actBeginTime || ''))
  }
  // let resultData = rtStructure.enterOptions.resultData.find(item=>item.name === '检测项目集合')
  // 签名
  curElem.find('.preview [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } 
    }else {
      $(this).hide();
    }
  });
  curElem.find('.preview [data-key]').each(function(){
    var key = $(this).attr('data-key')
    var idAnVal,value;
    var keyList = key.split(',');
    if(keyList.length>1){
      let result = []
      keyList.forEach(item=>{
        result.push(publicInfo[item])
      })
      $(this).html(result.join(' '))
      return
    }
    // 报告图片
    if(key==='report-img'){
      if(rptImageList && rptImageList.length){
        rptImageList = rptImageList.slice(0,2)
        let html = ''
        rptImageList.forEach(item=>
            html+=`<img src="${item.src}" alt=""">`
        )
        $(this).html(html)
      }else{
        $(this).hide()
      }
      return
    }
    // 兜底
    idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
    value = publicInfo[key] || idAnVal;
    
    this.style.whiteSpace = 'pre-line'
    $(this).html(value)
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  let hideList = [[28,29,30],[34,35,36],[40,41,42],[46,47,48],[52,53,54],[58,59,60],[64,65,66],[70,71,72],[76,77,78],[82,83,84],[88,89,90],[94,95,96],[100,101,102],[109,110,111]]
  let ndFlag = false // 是否有一项浓度
  hideList.forEach((item)=>{
    let text = ''
    let firstKey = ''
    item.forEach((key,index)=>{
      if(!firstKey){
        firstKey = key
      }
      let dom = $(`[data-key="hbetjybl2-rt-${key}"]`).text().trim()
      if(index===0 && dom){
        ndFlag = true
      }
      text+=dom
    })
    if(!text){
      $(`[data-key="hbetjybl2-rt-${firstKey}"]`).parent().hide()
    }
  })
  if(!ndFlag){
    $('.preview-content span:contains("浓度")').closest('div.flex-column').hide()
    hideList.forEach(item=>{
     let hideDom = item[0]   
     $(`[data-key="hbetjybl2-rt-${hideDom}"]`).hide()
    })
  }
  $('#hbetjybl2-rt-15').html('本院')
  console.log( $('.preview-content span:contains("浓度")').closest('div.flex-column'))
}
// IMPRESSION 存入诊断
function getImpressionStr(){
  let preFix = '#hbetjybl2-rt-'
  let hideList = [[27,28,29,30],[33,34,35,36],[39,40,41,42],[45,46,47,48],[51,52,53,54],[57,58,59,60],[63,64,65,66],[69,70,71,72],[75,76,77,78],[81,82,83,84],[87,88,89,90],[93,94,95,96],[99,100,101,102],[108,109,110,111]]
  let result = hideList.map(item=>{
    let [itemName,ndV,ctV,checkResult] = item
    let ndValue = $(preFix+ndV).val() || ''
    let ctValue = $(preFix+ctV).val() || ''
    let checkResultValue = $(preFix+checkResult).val() || ''
    
    if(ndValue || ctValue || checkResultValue){
      let template = `检测项目：${$(preFix+itemName).text()};`
      if(ndValue){
        template+=`浓度：${ndValue};`
      }
      if(ctValue){
        template+=`CT值：${ctValue};`
      }
      if(checkResultValue){
        template+=`检测结果：${checkResultValue};`
      }
      return template
    }
    return
  }).filter(Boolean)
  // let keyObj = {
  //   '#hbetjybl2-rt-5':'检测结果：',
  // }
  // let result = []
  // Object.entries(keyObj).forEach(([key,value],)=>{
  //   let str =  $(key).val()
  //   if(str){
  //     result.push(`${value}${str}`)
  //   }
  // })
  console.log('getImpressionStr\n',result.join('\n'))
  return result.join('\n')
}
function getMemo(){
  let keyObj = {
    '#hbetjybl2-rt-6':'参考范围：',
    '#hbetjybl2-rt-7':'检测下限：',
  }
  let result = []
  Object.entries(keyObj).forEach(([key,value],)=>{
    let str =  $(key).val()
    if(str){
      result.push(`${value}${str}`)
    }
  })
  return result.join(' ')
}
function initInpAndSel(idList, optionList,cb) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function(obj) {
      //当前节点
      this.elem.val(obj.title);
      cb && cb(obj)
      // if (lenVal === 0) {
      //   changeSel(obj.title)
      // }
    },
    // style:
    //   lenVal !== 0
    //     ? `width: 40%;height:200px;overflow:auto;overflow-x:hidden;`
    //     : 'width: calc(100% - 146px)',
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width = elem[0] && elem[0].clientWidth
      elePanel[0] && ( elePanel[0].style.width = width + 'px')
    }
  });
}
function renderChildren(children,pid) {
  let childHTML = '';
  let nd = children.find(item=>item.name==='浓度')
  let ctValue = children.find(item=>item.name==='CT值')
  let checkResult = children.find(item=>item.name==='检测结果')
  let memo = children.find(item=>item.name==='参考范围')
  let memoValue = children.find(item=>item.name==='检测下限')
  if(!nd){
    children.push({
      id:`${pid}-01`,
      pid,
      val:'',
      desc:'浓度',
      name:'浓度'
    })
  }
  if(!ctValue){
    children.push({
      id:`${pid}-02`,
      pid,
      val:'',
      desc:'CT值',
      name:'CT值'
    })
  }
  if(!memoValue){
    children.push({
      id:`${pid}-05`,
      pid,
      val:'',
      desc:'检测下限',
      name:'检测下限'
    })
  }
  if(!checkResult){
    children.push({
      id:`${pid}-03`,
      pid,
      val:'',
      desc:'检测结果',
      name:'检测结果'
    })
  }
  if(!memo){
    children.push({
      id:`${pid}-04`,
      pid,
      val:'',
      desc:'参考范围',
      name:'参考范围'
    })
  }
  children = children.sort((a,b)=>{
    let aId = a.id.split('-').pop()
    let bId = b.id.split('-').pop()
    return aId - bId
    
  })
  children.forEach(child => {
      if(child.name==='检测下限'){
        selectMap[child.pid] = child.id
      }
      childHTML += `
          <div class="flex-column f-1" style="align-items: center;justify-content: flex-end;row-gap: 2px;">
              <input class="rt-sr-w layui-input" value="${child.val}" id="${child.id}"
                  rt-sc="pageId:hbetjybl21;name:${child.name};wt:1;desc:${child.name};vt:;pvf:;" pid="${child.pid}">
          </div>
      `;
  });
  return childHTML;
}
function renderPreviewChildren(children){
  let arrMap = ['浓度','CT值','检测结果','参考范围','检测下限']
  children = arrMap.map(item=>{
    return children.find(child=>child.name===item) || {}
  })
  let childHTML = ''
  children.forEach(item=>{
    childHTML+= ` <span class="f-1" style="text-align: center;" data-key="hbetjybl2-rt-3">${item.val || ''}</span>`
  })
  return childHTML
}