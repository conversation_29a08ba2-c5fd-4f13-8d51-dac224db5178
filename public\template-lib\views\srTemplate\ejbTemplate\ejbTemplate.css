/*-------公共--------*/
.w-con {
  display: inline-block;
  vertical-align: middle;
}

/*-------边距--------*/
.mt-2 {
  margin-top: 2px;
}
.mb-8 {
  margin-bottom: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-14 {
  margin-left: 14px;
}
.ml-22 {
  margin-left: 22px;
}
.ml-110 {
  margin-left: 110px;
}
.ml-166 {
  margin-left: 166px;
}

/*-------宽度--------*/
.wd-70 {
  width: 70px;
}
.wd-80 {
  width: 80px;
}

/*-------总体布局--------*/
ul {
  overflow: auto;
}
#ejb1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#ejb1 .ejb-content {
  min-height: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#ejb1 .con-box {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#ejb1 .flex-sty {
  display: flex;
}
#ejb1 .box-row {
  padding: 8px 12px;
  box-sizing: border-box;
}
#ejb1 .split-line {
  position: relative;
}
#ejb1 .split-line::after {
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: '';
  background: #C8D7E6;
}
#ejb1 .bor-w {
  border: 1px solid #C8D7E6;
  padding: 0 8px;
  background: #EBEEF5;
}
#ejb1 .inp-sty {
  height: 28px;
  border: 1px solid #DCDFE6;
}
#ejb1 [type="checkbox"],#ejb1 [type="radio"] {
  vertical-align: middle;
}
#ejb1 [type="text"] {
  padding: 0 2px;
}
#ejb1 .add-btn {
  display: inline-block;
  padding: 3px 12px;
  color: #fff;
  background: #1885F2;
  border-radius: 4px;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
}