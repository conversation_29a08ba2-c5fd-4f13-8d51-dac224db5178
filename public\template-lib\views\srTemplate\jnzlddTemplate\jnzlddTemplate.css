@charset "UTF-8";
#jnzldd1 {
  font-size: 14px;
  width: 780px;
  margin: 0 auto;
  height: 1100px;
  padding: 48px 56px;
}
#jnzldd1 * {
  font-family: "宋体";
  color: #000;
}
#jnzldd1 table {
  margin-top: 28px;
  width: 100%;
  background: #fff;
  border-color: #000;
  border-collapse: collapse; /* 合并边框 */
  margin-bottom: 36px;
}
#jnzldd1 table td,
#jnzldd1 table th {
  text-align: center;
  height: 28px;
}
#jnzldd1 table thead {
  border-bottom: 1px solid #000;
}
#jnzldd1 table tbody tr td {
  padding: 4px;
}
#jnzldd1 .doctor {
  width: 296px;
  display: flex;
  flex-direction: column;
  row-gap: 4px;
  line-height: 20px;
}
#jnzldd1 .impression {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
#jnzldd1 .patLocal {
  text-align: right;
}
#jnzldd1 .userInfo {
  display: flex;
  align-items: center;
  margin-top: 10px;
  line-height: 20px;
}
#jnzldd1 .userInfo .userInfo-item {
  flex: 1;
}
#jnzldd1 .eye-content {
  line-height: 20px;
  margin-top: 4px;
}
#jnzldd1 .bold {
  font-weight: bold;
}
#jnzldd1 .jbt {
  justify-content: space-between;
}
#jnzldd1 .mr-8 {
  margin-right: 8px;
}
#jnzldd1 .text-wrap {
  white-space: pre-wrap;
}
#jnzldd1 .rt-sr-r {
  margin-right: 4px;
}
#jnzldd1 .pl-24 {
  padding-left: 24px;
}
#jnzldd1 .pr-24 {
  padding-right: 24px;
}
#jnzldd1 .pt-8 {
  padding-top: 8px;
}
#jnzldd1 .pb-8 {
  padding-bottom: 8px;
}
#jnzldd1 .mt-24 {
  margin-top: 24px;
}
#jnzldd1 .mt-12 {
  margin-top: 12px;
}
#jnzldd1 .mt-8 {
  margin-top: 8px;
}
#jnzldd1 .mt-6 {
  margin-top: 6px;
}
#jnzldd1 .mt-4 {
  margin-top: 4px;
}
#jnzldd1 .m-0-6 {
  margin: 0 6px;
}
#jnzldd1 .w-10 {
  width: 10px;
}
#jnzldd1 .w-15 {
  width: 15px;
}
#jnzldd1 .w-20 {
  width: 20px;
}
#jnzldd1 .w-25 {
  width: 25px;
}
#jnzldd1 .w-30 {
  width: 30px;
}
#jnzldd1 .w-35 {
  width: 35px;
}
#jnzldd1 .w-40 {
  width: 40px;
}
#jnzldd1 .w-45 {
  width: 45px;
}
#jnzldd1 .w-50 {
  width: 50px;
}
#jnzldd1 .w-55 {
  width: 55px;
}
#jnzldd1 .w-60 {
  width: 60px;
}
#jnzldd1 .w-65 {
  width: 65px;
}
#jnzldd1 .w-70 {
  width: 70px;
}
#jnzldd1 .w-75 {
  width: 75px;
}
#jnzldd1 .w-80 {
  width: 80px;
}
#jnzldd1 .w-85 {
  width: 85px;
}
#jnzldd1 .w-90 {
  width: 90px;
}
#jnzldd1 .w-95 {
  width: 95px;
}
#jnzldd1 .w-100 {
  width: 100px;
}
#jnzldd1 .w-105 {
  width: 106px;
}
#jnzldd1 .w-110 {
  width: 110px;
}
#jnzldd1 .w-115 {
  width: 115px;
}
#jnzldd1 .w-120 {
  width: 120px;
}
#jnzldd1 .w-125 {
  width: 125px;
}
#jnzldd1 .w-130 {
  width: 130px;
}
#jnzldd1 .w-135 {
  width: 135px;
}
#jnzldd1 .w-140 {
  width: 140px;
}
#jnzldd1 .w-145 {
  width: 145px;
}
#jnzldd1 .w-150 {
  width: 150px;
}
#jnzldd1 .w-155 {
  width: 155px;
}
#jnzldd1 .w-160 {
  width: 160px;
}
#jnzldd1 .w-165 {
  width: 165px;
}
#jnzldd1 .f-1 {
  flex: 1;
}
#jnzldd1 .f-1-5 {
  flex: 1.5;
}
#jnzldd1 .f-1-6 {
  flex: 1.6;
}
#jnzldd1 .f-2 {
  flex: 2;
}
#jnzldd1 .f-3 {
  flex: 3;
}
#jnzldd1 .fw-600 {
  font-weight: 600;
}
#jnzldd1 .a-center {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#jnzldd1 .a-start {
  display: flex;
  align-items: flex-start;
}
#jnzldd1 .flex {
  display: flex;
}
#jnzldd1 .flex span:first-child {
  white-space: nowrap;
}
#jnzldd1 .flex-column {
  display: flex;
  flex-direction: column;
}
#jnzldd1 .text-r {
  text-align: right;
}
#jnzldd1 .fs-36 {
  font-size: 36px;
}
#jnzldd1 .fs-24 {
  font-size: 24px;
}
#jnzldd1 .fs-20 {
  font-size: 20px;
}
#jnzldd1 .fs-16 {
  font-size: 16px;
}
#jnzldd1 .lh-20 {
  line-height: 20px;
}
#jnzldd1 .lh-23 {
  line-height: 23px;
}
