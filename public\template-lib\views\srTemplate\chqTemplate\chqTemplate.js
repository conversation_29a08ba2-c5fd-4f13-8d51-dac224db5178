$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;
var rtStructure = null;
var allResData = [];   //所有结果数据
var myChart = null;
var labPosition = 'bottom';
var yStartValue = 0;
var yEndValue = 40;
var yInterval = 5;
var DOBSeriesData = ['',''];
var repResultColor = 'none';
var options = {};
var optionList = [
  { name: '张丹' },
  { name: '穆远远' },
  { name: '杨红燕' },
  { name: '郭玉梅' },
  { name: '杜紫千' },
  { name: '李娜' },
  { name: '郭洪宇' },
  { name: '刘佩垚' },
  { name: '马美霞' },
  { name: '林春鹏' },
  { name: '刘士博' },
];

function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList);
    if(rtStructure) {
      allResData = rtStructure.enterOptions 
        && rtStructure.enterOptions.resultData ? 
        rtStructure.enterOptions.resultData : [];
    }
  }
  getCheckVal();
  initEchart();
  initSelect();
  let publicInfo = rtStructure.enterOptions.publicInfo;
  let { affirmReporter = '', affirmDate = '' } = publicInfo;
  rtStructure.idAndDomMap['chq-rt-32'].value = affirmReporter;
  $('#affirmReporter')[0].textContent = affirmReporter;
  rtStructure.idAndDomMap['chq-rt-16'].value = affirmDate;
  $('#affirmDate')[0].textContent = affirmDate;
}

// 初始化检查助手下拉框
function initSelect() {
  let option= '';
  let optionVal = rtStructure.idAndDomMap['chq-rt-30'].value;
  for (var i=0;i<optionList.length;i++){
    if(optionList[i].name === optionVal) {
      option += "<option value=\""+optionList[i].name +"\" selected=\""+true+"\">" +  optionList[i].name + "</option>";
    }else {
      option += "<option value=\""+optionList[i].name+"\">" +  optionList[i].name + "</option>";
    }
    $("#chq-rt-30").html("<option value=''>请选择</option>"+option);
  }
}

// 获取检验值
function getCheckVal() {
  let checkValDom = $('.check-td');
  if(checkValDom.children()[0]) {
    let checkText = checkValDom.children()[0].innerText;
    let checkVal = checkValDom.children()[0].value;
    if(checkText) {
      DOBSeriesData[0] = 30;
      DOBSeriesData[1] = parseFloat(checkText);
    }else if(checkVal){
      DOBSeriesData[0] = 30;
      DOBSeriesData[1] = parseFloat(checkVal);
    }
    let floatVal = DOBSeriesData[1];
    setlabPosition(floatVal);
    setCheckResSty(floatVal);
    setYStartAndEndVal(floatVal);
  }
}

// 设置label位置，避免与基准线重叠
function setlabPosition(val) {
  if(val <= 1) {
    labPosition = 'right';
  }else if(val > 100) {
    labPosition = 'bottom';
  }else {
    labPosition = 'top';
  }
}

// 给检验值输入框添加失去焦点事件
$('#chq-rt-24').on('blur',function(e) {
  let val = e.target.value;
  let floatVal = parseFloat(val);
  val ? DOBSeriesData[0] = 30 : '';
  DOBSeriesData[1] = floatVal;
  myChart.dispose(document.getElementById('dobValEchart'));
  val ? setYStartAndEndVal(floatVal) : setYStartAndEndVal(0);
  val ? setlabPosition(floatVal) : '';
  val ? setCheckResSty(floatVal) : setCheckResSty(0);
  initEchart();
});

// 设置y轴起始值和终点值
function setYStartAndEndVal(val) {
  let boundaryVal = 4;
  if(val < 40) {
    yInterval = 5;
    yStartValue = 0;
    yEndValue = 40 + boundaryVal;
  }else if(val >= 80) {
    yInterval = 20;
    yStartValue = 0;
    yEndValue = 120 + boundaryVal;
  }else {
    let intVal = parseInt(val / 10) * 10;
    yInterval = 10;
    yStartValue = 0;
    yEndValue = intVal + 20 + boundaryVal;
  }
}

// 设置阴阳性样式
function setCheckResSty(val) {
  let repResultDom = $('.result-text');
  let c13Suggest = $('.c13-suggest');
  let isNegative = '';
  let repResultText = '';
  if(val || val === 0) {
    isNegative = val <= 4.0 ? 'none' : 'block';
    repResultText = val <= 4.0 ? '阴性' : '阳性';
    repResultColor = val <= 4.0 ? '#21A685' : '#F56C6C';
    c13Suggest[0].style.display = isNegative;
  }else {
    repResultText = '';
    repResultColor = 'none';
    c13Suggest[0].style.display = 'none';
  }
  for(let i=0; i<repResultDom.length; i++) {
    repResultDom[i].textContent =  repResultText;
    repResultDom[i].style.color = repResultColor;
  }
}

function initEchart() {
  myChart = echarts.init(document.getElementById('dobValEchart'));
  options = {
    animation: false,
    grid: {
      left: '25%',
      bottom: '20%',
      width: 338,
      height: 236,
      borderColor: '#B3B3B3',
      borderWidth: 1,
      show: true,
    },
    xAxis: {
      show: true,
      type: 'value',
      boundaryGap: false,
      name: '呼气时刻【分钟】',
      nameLocation: 'center',
      minorTick: {
        show: true,
        splitNumber: 6,
      },
      splitLine: {
        show: false
      },
      nameTextStyle: {
        color: '#333333',
        fontSize: 14,
        padding: [15,0,0,0]
      },
      interval: 5,
      boundaryGap: false,
      axisLabel: {
        show: true,
        showMaxLabel: false,
      },
      min: 0,
      max: 32, // 横坐标长度固定30，不会变化
    },
    yAxis: {
      type: 'value',
      name: 'DOB值',
      nameLocation: 'center',
      axisLine: {
        show: true,
      },
      axisTick: {
        show: true
      },
      minorTick: {
        show: true,
        splitNumber: 5,
      },
      splitLine: {
        show: false
      },
      nameTextStyle: {
        color: '#333333',
        fontSize: 14,
        padding: [0,0,15,0]
      },
      boundaryGap: false,
      axisLabel: {
        show: true,
        showMaxLabel: false,
      },
      interval: yInterval,
      min: yStartValue,
      max: yEndValue,
    },
    series: [
      {
        type: 'line',
        smooth: true,//平滑曲线
        data: [
          DOBSeriesData
        ],
        markLine: {
          silent: true,
          symbol: false,
          lineStyle: {
            normal: {
              color: '#000000'
            }
          },
          data: [
            {
              yAxis: ['4']
            }
          ],
          label: {
            normal: {
              formatter: '基准线'
            }
          },
        },
        label: {
          show: true,
          position: labPosition,
          color: '#333333',
          fontSize: 14,
          fontWeight: 'bold',
          width: 90,
          lineHeight: 32,
          padding: [0, 16, 0, 16],
          borderWidth: 1,
          borderColor: '#EEEEEE',
          backgroundColor: '#FFFFFF',
          formatter: function() {
            return `检验值 (${DOBSeriesData[1]})`;
          }
        },
        symbol: 'circle',
        symbolSize: 8,
        color: repResultColor,
      },
      {
        // 竖线
        data: [
          [DOBSeriesData[0], yStartValue],
          [DOBSeriesData[0], DOBSeriesData[1]],
        ],
        type: 'line',
        color: repResultColor,
        lineStyle: {
          width: 2,
        },
        symbol: 'none',
      },
      {
        // 横线
        data: [
          [0, DOBSeriesData[1]],
          [DOBSeriesData[0], DOBSeriesData[1]],
        ],
        type: 'line',
        color: repResultColor,
        lineStyle: {
          width: 2,
        },
        symbol: 'none',
      },
    ],
  }
  myChart.setOption(options);
}
