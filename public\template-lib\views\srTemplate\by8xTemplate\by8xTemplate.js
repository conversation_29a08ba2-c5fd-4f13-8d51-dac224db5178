$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#by8x1 .by8x-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon()
    } else {
      initPage()
    }
  }
}
function initPage() {
  if(!isSavedReport){
    let arr = ['#by8x-rt-9','#by8x-rt-10','#by8x-rt-11','#by8x-rt-12','#by8x-rt-13','#by8x-rt-14','#by8x-rt-15','#by8x-rt-16']
    arr.forEach((item,index) => {
      if($(item).val() === '+'){
        $(item).val('阳性（+）')
      }
      if($(item).val() === '-'){
        $(item).val('阴性（-）')
      }
    })
  }
}
function initViewCon() {
  curElem.find('.by8x-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if(key === 'registerDate'){
      var sampleList = getSampleInfoForSR(publicInfo.examNo, '2') || [];
      if(sampleList.length) {
        $(this).html((sampleList[0].actBeginDate || ''));
      }
    }else {
      $(this).html(value)
    }
    addIdToNodeByView(this, key, idAndDomMap);
  })
  curElem.find('.by8x-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}
function imprser() {
  let datas = ''
  datas += `${$('#by8x-rt-1').val()}：${$('#by8x-rt-9').val()}\n`
  datas += `${$('#by8x-rt-2').val()}：${$('#by8x-rt-10').val()}\n`
  datas += `${$('#by8x-rt-3').val()}：${$('#by8x-rt-11').val()}\n`
  datas += `${$('#by8x-rt-4').val()}：${$('#by8x-rt-12').val()}\n`
  datas += `${$('#by8x-rt-5').val()}：${$('#by8x-rt-13').val()}\n`
  datas += `${$('#by8x-rt-6').val()}：${$('#by8x-rt-14').val()}\n`
  datas += `${$('#by8x-rt-7').val()}：${$('#by8x-rt-15').val()}\n`
  datas += `${$('#by8x-rt-8').val()}：${$('#by8x-rt-16').val()}\n`
  return datas
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = imprser();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}