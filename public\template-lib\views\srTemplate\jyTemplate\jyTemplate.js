$(function() {
  window.initHtmlScript = initHtmlScript;
})
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  // initTabContentHandler();
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 获取诊断/印象
function getImpression() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let impression = '';
  let blockStrList1 = [];
  $('#jy1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      pid !== 'jy-rt-1' ? blockStrList1.push(parData.name) : '';
      childD1.map(function(cItem1) {
        blockStrList1.push(parData.val + cItem1.name + '后复查');
      })
    }
  });
  impression = blockStrList1.length ? blockStrList1.join('，') + '。': '';
  return impression;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  // rtStructure.description = '';
  rtStructure.impression = getImpression();
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}