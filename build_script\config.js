/**
 * @description: 环境变量配置
 * @param {string} env 当前环境
 */
const fileMapGenerator = (env) => {
  const curTime = formatDateTime(Date.now());
  return {
    production: {
      NODE_ENV: env,
      VUE_APP_BUILD_TIME: curTime,
    },
    development: {
      NODE_ENV: env,
      VUE_APP_BUILD_TIME: curTime,
    },
  }[env];
};

/**
 * 格式化时间
 */
function formatDateTime(row) {
  let timeStamp = new Date(row);
  let date = timeStamp.getDate();
  let year = timeStamp.getFullYear();
  let month = timeStamp.getMonth() + 1;
  let hour =
    timeStamp.getHours() >= 10
      ? timeStamp.getHours()
      : `0${timeStamp.getHours()}`;
  let minute =
    timeStamp.getMinutes() >= 10
      ? timeStamp.getMinutes()
      : `0${timeStamp.getMinutes()}`;
  let second =
    timeStamp.getSeconds() >= 10
      ? timeStamp.getSeconds()
      : `0${timeStamp.getSeconds()}`;
  return `${year}年${month}月${date}日 ${hour}:${minute}:${second}`;
}
module.exports.fileMapGenerator = fileMapGenerator;
