export const leftCheckJSON = [
  {
    "code": "01003.01", 
    "value": "超翼外肌外缘软组织",
  }, {
    "code": "01003.02",
    "value": "鼻腔",
  }, {
    "code": "01003.03", 
    "value": "腭帆提肌",
  }, {
    "code": "01003.04", 
    "value": "头长肌",
  }, {
    "code": "01003.05", 
    "value": "翼内肌",
  }, {
    "code": "01003.06",
    "value": "腭帆张肌",
  }, {
    "code": "01003.07",
    "value": "颈长肌",
  }, {
    "code": "01003.08",
    "value": "翼外肌",
  }, {
    "code": "01003.09",
    "value": "海绵窦",
  }, {
    "code": "01003.10",
    "value": "眼眶",
  }, {
    "code": "01003.11",
    "value": "腮腺",
  }, {
    "code": "01003.12",
    "value": "咽旁间隙",
  }, {
    "code": "01003.13",
    "value": "颅脑",
  }, {
    "code": "01003.14",
    "value": "Meckel腔",
  }
]

export const leftTreeJSON = [
  {
    "code": "01004.0101", // 翼突根部
    "value": "翼突根部",
  }, {
    "code": "01004.0102", // 翼外板
    "value": "翼外板",
  }, {
    "code": "01004.0103", // 翼内板
    "value": "翼内板",
  }, {
    "code": "01004.0104", // 翼腭窝
    "value": "翼腭窝",
  },
  {
    "code": "01004.0201", // 蝶骨大翼
    "value": "蝶骨大翼",
  }, {
    "code": "01004.0202", // 岩尖
    "value": "岩尖",
  },{
    "code": "01004.0203", // 圆孔
    "value": "圆孔",
  },{
    "code": "01004.0204", // 卵圆孔
    "value": "卵圆孔",
  },{
    "code": "01004.0205", // 破裂孔
    "value": "破裂孔",
  },
  {
    "code": "01004.0301", // 上颌窦
    "value": "上颌窦",
  }, {
    "code": "01004.0302", // 筛窦
    "value": "筛窦",
  }, {
    "code": "01004.0303", // 额窦
    "value": "额窦",
  },
  {
    "code": "01004.0401", // 咽后
    "value": "咽后",
  },
  {
    "code": "01004.040201", // IA
    "value": "ⅠA",
  }, {
    "code": "01004.040202", // IB
    "value": "ⅠB",
  }, {
    "code": "01004.040203", // IIA
    "value": "ⅡA",
  }, {
    "code": "01004.040204", // IIB
    "value": "ⅡB",
  }, {
    "code": "01004.040205", // III
    "value": "Ⅲ",
  }, {
    "code": "01004.040210", // IV
    "value": "ⅣA",
  }, {
    "code": "01004.040211", // VA
    "value": "ⅣB",
  }, {
    "code": "01004.040207", // VB
    "value": "ⅤA",
  }, {
    "code": "01004.040208",
    "value": "ⅤB",
  }, {
    "code": "01004.040212",
    "value": "ⅤC",
  }, {
    "code": "01004.040213",
    "value": "ⅦA",
  }, {
    "code": "01004.040214",
    "value": "ⅦB",
  }, {
    "code": "01004.040215",
    "value": "Ⅷ",
  }
]


export const middleCheckJSON = [
  {
    "code": "01005.01", // 鼻咽
    "value": "鼻咽",
  }, {
    "code": "01005.02", // 口咽
    "value": "口咽",
  }, {
    "code": "01005.03", // 下咽
    "value": "下咽",
  }, {
    "code": "01005.04", // 斜坡
    "value": "斜坡",
  }, {
    "code": "01005.05", // 蝶窦
    "value": "蝶窦",
  }, {
    "code": "01005.06", // 颈椎
    "value": "颈椎",
  }, {
    "code": "01005.07", // 蝶窦底壁
    "value": "蝶窦底壁",
  }
]

export const rightCheckJSON = [
  {
    "code": "01006.01", 
    "value": "超翼外肌外缘软组织",
  }, {
    "code": "01006.02",
    "value": "鼻腔",
  }, {
    "code": "01006.03", 
    "value": "腭帆提肌",
  }, {
    "code": "01006.04", 
    "value": "头长肌",
  }, {
    "code": "01006.05", 
    "value": "翼内肌",
  }, {
    "code": "01006.06",
    "value": "腭帆张肌",
  }, {
    "code": "01006.07",
    "value": "颈长肌",
  }, {
    "code": "01006.08",
    "value": "翼外肌",
  }, {
    "code": "01006.09",
    "value": "海绵窦",
  }, {
    "code": "01006.10",
    "value": "眼眶",
  }, {
    "code": "01006.11",
    "value": "腮腺",
  }, {
    "code": "01006.12",
    "value": "咽旁间隙",
  }, {
    "code": "01006.13",
    "value": "颅脑",
  }, {
    "code": "01006.14",
    "value": "Meckel腔",
  }
]

export const rightTreeJSON = [
  {
    "code": "01007.0101", // 翼突根部
    "value": "翼突根部",
  }, {
    "code": "01007.0102", // 翼外板
    "value": "翼外板",
  }, {
    "code": "01007.0103", // 翼内板
    "value": "翼内板",
  }, {
    "code": "01007.0104", // 翼腭窝
    "value": "翼腭窝",
  },
  {
    "code": "01007.0201", // 蝶骨大翼
    "value": "蝶骨大翼",
  }, {
    "code": "01007.0202", // 岩尖
    "value": "岩尖",
  },
  ,{
    "code": "01007.0203", // 圆孔
    "value": "圆孔",
  },{
    "code": "01007.0204", // 卵圆孔
    "value": "卵圆孔",
  },{
    "code": "01007.0205", // 破裂孔
    "value": "破裂孔",
  },
  {
    "code": "01007.0301", // 上颌窦
    "value": "上颌窦",
  }, {
    "code": "01007.0302", // 筛窦
    "value": "筛窦",
  }, {
    "code": "01007.0303", // 额窦
    "value": "额窦",
  },
  {
    "code": "01007.0401", // 咽后
    "value": "咽后",
  },
  {
    "code": "01007.040201", // IA
    "value": "ⅠA",
  }, {
    "code": "01007.040202", // IB
    "value": "ⅠB",
  }, {
    "code": "01007.040203", // IIA
    "value": "ⅡA",
  }, {
    "code": "01007.040204", // IIB
    "value": "ⅡB",
  }, {
    "code": "01007.040205", // III
    "value": "Ⅲ",
  }, {
    "code": "01007.040210", // IV
    "value": "ⅣA",
  }, {
    "code": "01007.040211", // VA
    "value": "ⅣB",
  }, {
    "code": "01007.040207", // VB
    "value": "ⅤA",
  }, {
    "code": "01007.040208",
    "value": "ⅤB",
  }, {
    "code": "01007.040212",
    "value": "ⅤC",
  }, {
    "code": "01007.040213",
    "value": "ⅦA",
  }, {
    "code": "01007.040214",
    "value": "ⅦB",
  }, {
    "code": "01007.040215",
    "value": "Ⅷ",
  }
]