$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#jnzldd1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      
    } else {

    }
    init()
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function init(){
  let template = ''
  template+=eyeRender()
  // console.log(template)
  template+=candleListRender()
  template+=diagnosisRender('1')
  template+=diagnosisRender('0')
  template+=diagnosisRender('3')
  $('#mount').html(template)
  setTimeout(function() {
    checkOverflow($('#jnzldd1')[0]);
  }, 0)
}
function eyeRender(){
  let content = ''
  if(publicInfo.sampleSeen){
    content = `
    <div class="mt-24">
      <div class="bold fs-16 lh-23">肉眼所见：</div>
      <div class="eye-content text-wrap">${publicInfo.sampleSeen}</div>
    </div> 
    `
  }
  return content
}
function candleListRender(){
  let content = ''
  let list = window.getExamCandleList(publicInfo.examNo)
  if(list.length){
    content = `
    <table border>
      <colgroup>
          <col  >
          <col  >
          <col >
          <col >
          <col >
          <col  >
          <col  >
          <col width="170px" >
      </colgroup>
      <thead>
        <th>任务来源</th>
        <th>序号</th>
        <th>取材部位</th>
        <th>蜡块说明</th>
        <th>材块数</th>
        <th>取材医生</th>
        <th>记录员</th>
        <th>取材时间</th>
      </thead>
      ${
        renderTr(list)
      }
    </table>
    `
  }
  function renderTr(list){
    return list.map((item)=>{
      return `
      <tr>
        <td>${item.source|| ''}</td>
        <td>${item?.candleId.split('-').pop() || ''}</td>
        <td>${item.candlePart  || ''}</td>
        <td>${item.candleMemo  || ''}</td>
        <td>${item.blockCount  || ''}</td>
        <td>${item.cutUser  || ''}</td>
        <td>${item.register  || ''}</td>
        <td>${item.cutDate  || ''} ${item.cutTime || ''}</td>
      </tr>
      `
    }).join(' ')
  }
 return content
}
function diagnosisRender( rptType){
  let title = {
    0:'病理诊断',
    1:'冰冻诊断',
    3:'补充诊断'
  }[rptType]
  let content = ''
  let arr = window.getExamRptList(publicInfo.examNo,rptType) || []
  let renderList = (rptType!='3' ? [arr.shift()]:arr).filter(item=>item && item.impression)
  if(renderList.length){
    content+=renderTemplate(renderList)
  }
  function renderTemplate(renderList){
    let template = `
    <div class="mt-24">
      <div class="bold fs-16 lh-23">${title}：</div>
      ${
        renderList.map(item=>{
          return `
          <div class="mt-4 mb-16">
            <div class="text-wrap">${item.impression || ''}</div>
            <div class="impression">
             <div class="doctor">
              <div class="flex" >
                <div class="f-1 flex">
                  <span class="w-70">诊断医生：</span>
                  <span class="f-1">${item.reporter || ''}</span>
                </div>
                <div class="f-1 flex">
                <span class="w-70">审核医生：</span>
                <span class="f-1">${item.affirmReporter || ''}</span> </div>
              </div>
              <div class="flex">
                 <span class="w-70">诊断时间：</span>
                 <span class="f-1">${item.affirmDate || ''}</span>
              </div>
            </div>
            </div>
            </div>
          `
        }).join(' ')
      }
    </div> 
    `
    return template
  }
  return content
}