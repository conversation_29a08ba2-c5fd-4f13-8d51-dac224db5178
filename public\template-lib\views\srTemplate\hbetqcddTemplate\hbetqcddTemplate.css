/* 间距 */
#hbetqcdd1 .mb-8 {
  margin-bottom: 8px;
}
#hbetqcdd1 {
  position: relative;
  width: 780px;
  height: 1100px;
  margin: 0 auto;
  padding: 72px 56px;
  font-size: 16px;
  color: #000;
  background: #FFF;
}
#hbetqcdd1 .desc-tit {
  font-weight: bold;
  line-height: 23px;
  margin-bottom: 8px;
}
#hbetqcdd1 .desc-con {
  white-space: pre-line;
  word-break: break-all;
}
#hbetqcdd1 .ryjc-con {
  min-height: 210px;
}
#hbetqcdd1 .qcjl-con {
  min-height: 150px;
  display: flex;
  flex-wrap: wrap;
}
#hbetqcdd1 .blzd-con {
  min-height: 426px;
}
#hbetqcdd1 .rpt-body {
  height: 100%;
}
#hbetqcdd1 .rpt-footer {
  position: absolute;
  left: 0;
  right: 90px;
  text-align: right;
}
#hbetqcdd1 .wd-64 {
  display: inline-block;
  width: 64px;
}
#hbetqcdd1 .wd-32 {
  display: inline-block;
  width: 32px;
}