var urlParams = {
  optId: '',
  optName: '',
  patternId: '',
  applyNo: '',
  busId: '',
  busType: ''
}
var applyInfo = {};
var patternInfo = {};
var patternList = [];
var rtStructure = null;
var docId = '';  //用于新增还是更新的判断
var isUpdate = true;   //申请单是否可更新
var oldPatDocId = '';
var oldResult = [];
var applyStatusRes = {};  //申请单的相关状态判断
var sourcePrefixUrl = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
var pv = null;   //只显示打印页面
$(function() {
  for(var key in urlParams) {
    urlParams[key] = getParamByName(key) || '';
  }
  if(!urlParams.applyNo && urlParams.busId) {
    urlParams.applyNo = urlParams.busId;
  }
  if(!urlParams.busId && urlParams.applyNo) {
    urlParams.busId = urlParams.applyNo;
  }
  pv = getParamByName('pv') || null;

  if(pv === '1') {
    $(".layout-page").addClass('pv');
    isUpdate = false;
    addLoadCoverLayer();
    getApplyResult();
  } else {
    $(".layout-page").removeClass('pv');
    isUpdate = true;
    addLoadCoverLayer();
    getHisInfo(function(info){
      console.log('申请单详情-->', info);
      checkElectApplyIsUpdate(info);
      getPatternList(info);
    });  //申请单详情
  }
  
  // getParDoc(patDocId);
  rtStructure = new RtStructure('#blApply .main-content');  //实例化
  console.log('rtStructure', rtStructure);
  window.errorCallBack = function(widgetId) {
    $(".layout-body")[0].scrollTop = $('[id="'+widgetId+'"]')[0].offsetTop;
    if($('.rt-sr-inv').parents('.laydate-w').length) {
      $('.rt-sr-inv').parents('.laydate-w').css('borderColor', 'red');
    }
  }
})

// 获取模板列表
function getPatternList(applyInfo) {
  if(!urlParams.applyNo || !applyInfo.examClass) {
    $wfMessage({
      content: '找不到该申请单'
    })
    removeLoadCoverLayer();
    return;
  }
  var params = {
    applyNo: urlParams.applyNo,
    // patternId: urlParams.patternId,
    patternType: '2',
    examClass: applyInfo.examClass,
    examSubclass: applyInfo.examSubClass,
    // patientSource: applyInfo.patientSource,
    pageSize: 0,
    pageNo: 0,
    mainFlag: '1'
  }
  getPatternListHandler(params, {
    successCb: function(res) {
      var data = res.result || [];
      patternList = data;
      if(data.length) {
        var patDocId = data[0].patDocId;
        for(var i = 0; i < data.length; i++) {
          if(data[i].patternId === urlParams.patternId) {
            patDocId = data[i].patDocId;
            break;
          }
        }
        urlParams.patDocId = patDocId;
        oldPatDocId = patDocId;
        console.log('patternList--oldPatDocId', oldPatDocId);
      }
      getApplyResult();
    },
    errorCb: function() {
      getApplyResult();
    }
  })
}

// 获取申请单详情
function getHisInfo(callBack) {
  if(!urlParams.applyNo) {
    $wfMessage({
      content: '参数applyNo不能为空'
    })
    removeLoadCoverLayer();
    return;
  }
  var params = {
    applyNo: urlParams.applyNo,
  }
  fetchAjax({
    url: api.getHISApplyInfo,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        if(data && data.length > 0) {
          var obj = data[0];
          var sheetContent = obj.sheetContent ? JSON.parse(obj.sheetContent) : {};
          $.extend(obj, sheetContent);
          $.extend(obj, sheetContent.applyInfo || {}, sheetContent.otherInfo || {}, sheetContent.patientInfo || {}, sheetContent.patternFilter || {}, sheetContent.surgeryInfo || {}, sheetContent.pathologyInfo || {});
          applyInfo = obj;
          applyInfo.optName = urlParams.optName;
        }
        if(JSON.stringify(applyInfo) === '{}') {
          $wfMessage({
            content: '找不到该申请单'
          })
          removeLoadCoverLayer();
          return;
        }
        if(callBack) {
          callBack(applyInfo);
        }
      } else {
        removeLoadCoverLayer();
      }
    },
    errorFn: function() {
      removeLoadCoverLayer();
    }
  })
}


// 获取文档内容, isChange是否是切换模板
function getParDoc(patDocId, isChange) {
  $("#blApply .main-content").html('');
  var params = {
    patDocId: patDocId,
  }
  patternInfo = {};
  getParDocHtmlHandler(params, {
    successCb: function(res) {
      var data = res.result || {};
      $(".ly-c").text(data.patternName || data.fileName || '');
      patternInfo = data;
      var html = data.patternDoc;
      
      if(html) {
        if(!isChange) {
          // getApplyResult(html);
          drawContentHtml(html, oldResult);
        } else {
          var changeResult = rtStructure.enterOptions ? rtStructure.enterOptions.resultData || [] : [];
          drawContentHtml(html, changeResult, 'edit');
        }
      } else {
        $wfMessage({
          content: '模板出错'
        })
        toggleBtn(isUpdate, 'edit');
      }
    },
    errorCb: function() {
      $wfMessage({
        content: '模板出错'
      })
      toggleBtn(isUpdate, 'edit');
    }
  })
}

// 判断申请单是否可修改
function checkElectApplyIsUpdate(info) {
  var params = {
    applyNo: info.applyNo
  }
  applyStatusRes = {};
  fetchAjax({
    url: api.checkElectApplyIsUpdate,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status !== '0') {
        isUpdate = false;
      } else {
        applyStatusRes = res.result || {};
      }
    },
    errorFn: function() {
      isUpdate = false;
    }
  })
}

// 设置his中读取的值
function setValByHis(hisAndFormMap, reload) {
  applyInfo.moment = getCurDateAndTime().curDate;
  applyInfo.momentTime = getCurDateAndTime().curTime;
  for(var id in hisAndFormMap) {
    var value = hisAndFormMap[id].wt ? $('[id="'+id+'"].rt-sr-w:checked').val() : ($('[id="'+id+'"].rt-sr-w').val() || $('[id="'+id+'"].rt-sr-w').text());
    if(!value && hisAndFormMap[id].wt){
      value = $('[id="'+id+'"].rt-sr-w').val() || $('[id="'+id+'"].rt-sr-w:checked').text()
    }
    if(hisAndFormMap[id].wt === '5') {
      var name =  $('[id="'+id+'"].rt-sr-w').attr('name');
      value = $('[name="'+name+'"].rt-sr-w:checked').val();
    }
    if(value && !reload) {
      continue;
    }
    var keys = hisAndFormMap[id].hisKey.split(',');
    for(var j = 0; j < keys.length; j++) {
      var resVal = applyInfo[keys[j]]
      if(resVal) {
        if(keys[j] === 'identity' && resVal.length) {
          resVal = resVal[0].idNumber;
        } 
        if(keys[j] === 'flags' && hisAndFormMap[id].sex && hisAndFormMap[id].sex !== applyInfo.sex) {
          break;
        }
        if(keys[j] === 'flags' && resVal && typeof resVal === 'string') {
          resVal = resVal[hisAndFormMap[id].index];
        }
        if(keys[j] === 'momentTime' && resVal && typeof resVal === 'string') {
          var formatVal = $('[id="'+id+'"].rt-sr-w').attr('val-format');
          if(formatVal){
            resVal = dayjs(applyInfo.moment + ' ' +resVal).format(formatVal);
          }
        }
        if(hisAndFormMap[id].wt) {
          if(hisAndFormMap[id].wt === '4' || hisAndFormMap[id].wt === '5') {
            if(resVal === hisAndFormMap[id].value) {
              // $('.rt-sr-w[value="'+resVal+'"][id="'+id+'"]').prop('checked', true);
              $('.rt-sr-w[id="'+id+'"]').prop('checked', true);
            }
          } else if(hisAndFormMap[id].wt === '1') {
            $('[id="'+id+'"].rt-sr-w').val(resVal);
            $('[id="'+id+'"].rt-sr-w').text(resVal);
          } else {
            $('.rt-sr-w[id="'+id+'"]').find('option[value="'+resVal+'"]').attr('selected', true);
          }
        } else {
          $('[id="'+id+'"].rt-sr-w').val(resVal);
          $('[id="'+id+'"].rt-sr-w').text(resVal);
        }
        break;
      }
    }
  }
}

// 获取已保存的结果数据,isSave是否保存后重新渲染
function getApplyResult(isSave) {
  docId = '';
  oldResult = [];
  var params = {
    applyNo: urlParams.applyNo,
    busType: '2',
    busId: urlParams.applyNo,
    newestFlag: true,
    mainFlag: '1',
  }
  getResJsonHandler(params, {
    successCb: function(res) {
      var allContent = res.result && res.result.length ? res.result[0] : {};
      var docParse = allContent.docContent ? JSON.parse(allContent.docContent) : {}
      var result = docParse.docContent ? (docParse.docContent.docAttr || []) : [];
      oldResult = result;
      if(JSON.stringify(allContent) !== '{}') {
        docId = allContent.docId;
        oldPatDocId = allContent.patDocId ? allContent.patDocId : oldPatDocId;
      }
      if(isSave) {
        drawContentHtml(patternInfo.patternDoc, oldResult, 'view')
      } else {
        getParDoc(oldPatDocId);
      }
    },
    errorCb: function() {
      if(!isSave) {
        getParDoc(oldPatDocId);
      }
    }
  })
}

// 渲染页面
function drawContentHtml(html, result, editType) {
  // var setNull = html.indexOf('data-setnull="false"') > -1 ? false : true;
  var setNull = false;
  if(!result.length && isUpdate) {
    editType = 'edit';
    rtStructure.rebuildStructureReport({htmlContent:html, resultData:result, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, setNull: setNull});
  } else {
    if(!editType) {
      // 预览
      editType = 'view';
      rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, setNull: setNull});
    } else {
      if(editType === 'edit') {
        rtStructure.rebuildStructureReport({htmlContent:html, resultData:result, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, setNull: setNull});
      } else {
        rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: applyInfo, srcUrl: sourcePrefixUrl, setNull: setNull});
      }
    }
  }
  toggleBtn(isUpdate, editType)
}

// 点击编写按钮事件
function editApply() {
  toggleBtn(true, 'edit');
  var html = rtStructure.enterOptions.htmlContent;
  var result = rtStructure.enterOptions.resultData;
  var applyInfo = rtStructure.enterOptions.publicInfo;
  drawContentHtml(html, result, 'edit')
}

// 调整操作按钮的显示隐藏
function toggleBtn(isUpdate, type) {
  // 不可编辑全部隐藏
  if(!isUpdate) {
    $("#editBtn, #saveBtn, #tempBtn, #reloadBtn").hide();
    if(type === 'view') {
      $("#printBtn").show().attr('disabled', false);
    }
  } else {
    if(type === 'edit') {
      $("#saveBtn, #tempBtn, #reloadBtn").show().attr('disabled', false);
      $("#editBtn, #printBtn").hide();
    } else {
      $("#saveBtn, #tempBtn, #reloadBtn").hide();
      $("#editBtn, #printBtn").show().attr('disabled', false);
    }
  }
  $("#applyBtn").show().attr('disabled', false);
}

// 保存
function saveApplyResult(vm) {
  var ele = $(vm);
  $(".sampleTxt, .examTxt").css('color', '#606266');
  $('.rt-sr-inv').parents('.laydate-w').css('borderColor', '#DCDFE6');
  var sampleByTable = $('.t-pg').attr('data-sampleByTable') || '';
  var dataObj = createResultData();
  var resultData = dataObj.resultData
  var idMapResult = dataObj.idMapResult;
  var sampleEditPart = $('.sampleEditPart');
  var eItemEditPart = $('.eItemEditPart');
  if(!sampleByTable && sampleEditPart.length && sampleEditPart.html() === '') {
    $wfMessage({
      content: ('【标本信息】不能为空')
    });
    $(".sampleTxt").css('color', 'red');
    $(".layout-body")[0].scrollTop = $(".sampleTxt")[0].offsetTop;
    return;
  }
  if(!sampleByTable && eItemEditPart.length && eItemEditPart.html() === '') {
    $wfMessage({
      content: ('【检查项目】不能为空')
    });
    $(".examTxt").css('color', 'red');
    $(".layout-body")[0].scrollTop = $(".examTxt")[0].offsetTop;
    return;
  }
  var params = {
    optType: '0',  
    optId: urlParams.optId,
    optName: urlParams.optName,
    patientInfo: applyInfo,
    resultData: resultData,
    patternInfo: patternInfo,
    busType: urlParams.busType || '2',
    busId: urlParams.busId,
    docId: docId,
    mainFlag: '1'
  };
  var postParams = saveParams(params);
  ele.attr('disabled', true);
  saveApplyInfoApi(idMapResult, postParams, ele);
}

// 保存json接口
function saveResultApi(postParams, ele) {
  saveHandler(postParams, {
    successCb: function() {
      getApplyResult(true);
      ele.attr('disabled', false);
    },
    errorCb: function() {
      ele.attr('disabled', false);
    }
  })
}

// 更新申请单详情接口
function saveApplyInfoApi(idMapResult, postParams, ele) {
  var applyUpdateParams = {
    applyNo: urlParams.applyNo,
    optId: urlParams.optId,
    optName: urlParams.optName,
  };
  if(saveApplyParams && JSON.stringify(saveApplyParams) !== '{}') {
    for(var key in saveApplyParams) {
      var id = saveApplyParams[key].id;
      if(['applyItemList', 'gmSampleList'].indexOf(key) > -1) {
        applyUpdateParams[key] = []
        if(key === 'applyItemList') {
          if(addExamItemList && addExamItemList.length) {
            addExamItemList.forEach(function(examItem) {
              applyUpdateParams[key].push({
                itemName: examItem.itemName,
                itemCode: examItem.itemCode || (examItem.parentName ? examItem.parentName.split('__')[1] : ''),
                examSubClass: examItem.parentName ? examItem.parentName.split('__')[0] : ''
              })
            })
          }
        }
        if(key === 'gmSampleList') {
          addSampleList.forEach(function(sItem) {
            var sid = sItem.id;
            if(!idMapResult[sid] || !idMapResult[sid].val) {
              return;
            }
            applyUpdateParams[key].push({
              sampleName: idMapResult[sid] ? idMapResult[sid].val : '',  //标本名称
              samplePart: idMapResult[sid + '.01'] ? idMapResult[sid + '.01'].val : '',  //标本部位
              inVitroDateTime: idMapResult[sid + '.02'] ? idMapResult[sid + '.02'].val : '',  //离体时间
              fixedDateTime: idMapResult[sid + '.03'] ? idMapResult[sid + '.03'].val : '',  //固定时间
              barcodeNo: idMapResult[sid + '.04'] ? idMapResult[sid + '.04'].val : '',  //标本编号
            })
          })
        }
      } else if(key === 'flags') {
        var ids = saveApplyParams[key].ids;
        for(var k = 0; k < ids.length; k++) {
          var idItem = ids[k];
          var idVal = idMapResult[idItem.id] ? idMapResult[idItem.id].val : '';
          if(idVal) {
            var flags = applyInfo.flags || '0000000000000000000000';
            var flagArr = flags.split('');
            flagArr[idItem.index] = idItem.value;
            applyUpdateParams[key] = flagArr.join('');
            break;
          }
        }
      }else {
        applyUpdateParams[key] = idMapResult[id] ? idMapResult[id].val : '';
      }
    }
  } else {
    // 更新申请单成功后更新结构化json数据
    saveResultApi(postParams, ele);
    return;
  }
  // console.log(applyUpdateParams);
  // return;
  addLoadCoverLayer()
  fetchAjax({
    url: api.addOrUpdateApply,
    data: JSON.stringify(applyUpdateParams),
    successFn: function(res) {
      if(res.status == '0') {
        // 更新申请单成功后更新结构化json数据
        saveResultApi(postParams, ele);
      } else {
        ele.attr('disabled', false);
        removeLoadCoverLayer();
      }
    },
    errorFn: function() {
      ele.attr('disabled', false);
      removeLoadCoverLayer();
    }
  })
}

// 打印
function printApply() {
  $(".wfmsgbox").remove();
  // $("#blApply").addClass('print-status');
  printAfterImgLoaded();
}

function printAfterImgLoaded() {
  var imageList = $('body img:visible')
  var imgCount = imageList.length;
  var loadedImgCount = 0;
  var loadFlag = false;
  var waitImgLoad = function(src) {
    loadFlag = true;
    var image = new Image();
    image.src = src;
    image.onload = function() {
      loadedImgCount++;
      console.log('图片加载完成');
      if(loadedImgCount !== imgCount) {
        waitImgLoad(imageList.eq(loadedImgCount)[0].src)
      }
    }
  }
  var timer = setInterval(function() {
    if(imgCount > 0 && loadedImgCount !== imgCount) {
      if(!loadFlag) {
        waitImgLoad(imageList.eq(loadedImgCount)[0].src)
      }
      return;
    }
    clearInterval(timer);
    printByLodop(true);
    // window.print();
    // setTimeout(function() {
    //   $("#blApply").removeClass('print-status');
    // }, 50)
  }, 50);
}
// 使用lodop打印
function printByLodop(isPreview) {
  var LODOP = getLodopFn();  
		try{ 
      if (LODOP.VERSION) {
        if (LODOP.CVERSION) {
          console.log("当前有WEB打印服务C-Lodop可用!\n C-Lodop版本:"+LODOP.CVERSION+"(内含Lodop"+LODOP.VERSION+")"); 
        }  else {
          console.log("本机已成功安装了Lodop控件！\n 版本号:"+LODOP.VERSION); 
        }
        var patternWidth = '';//纸张宽度
        var patternHeight = '';//纸张高度
        // var printName = document.getElementById('pin').value || '';  
        var strHtml = $('.layout-body').html();
        var strStyleLink=$('head').html();  //外联样式
        var strStyleCss=$('style').html();  //内联样式
		    var strAllHtml="<head>"+strStyleLink+"<style>"+strStyleCss+"</style></head><body>"+strHtml+"</body>";

        LODOP.SET_PRINT_PAGESIZE(1, patternWidth, patternHeight, ''); //设置纸张大小 （patternWidth纸张宽度 patternHeight 纸张高度, 不传默认A4大小）
        LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', strAllHtml); //打印html模板（目前都是采用模板的形式打印） strHtml 打印内容
        // 控制打印机自带双面打印(所谓自动双面)功能和纸张来源
        LODOP.SET_PRINT_MODE("PRINT_DUPLEX", 2);  //0-不控制 1-不双面 2-双面(长边装订) 3-小册子双面(短边装订_长边水平)
        // LODOP.SET_PRINT_MODE("PRINT_DEFAULTSOURCE",1);  //1-纸盒 4-手动 7-自动 0-不控制
        
        // LODOP.SET_PRINTER_INDEX(printName); //选择该打印机打印  printName 打印机名称 （未选打印机默认第一个）
        // 脚本需加注册语句去掉水印等其它问题
        LODOP.SET_LICENSES("易联众信息技术股份有限公司","2BCE21A2F9D18B1D8DA44DB77AD304F2","","");
        
        //isPreview 是否预览
        if(isPreview) {
          LODOP.PREVIEW() // 打印预览
          return
        }
        
        LODOP.PRINT() //直接打印
      } else {
        alert('请先安装Lodop控件');
      }
    }catch(err){
      console.error(err);
    } 
}

// 打开模板
function openTemplateHandler() {
  var html = templateListHtml(patternList, selectPattern)
  drawDialog({
    title: '选择模板',
    modal: true,
    content: html,
    style: {
      'width': '413px',
    }
  });
}

// 重载
function reloadApply() {
  addLoadCoverLayer();
  getHisInfo(function(info) {
    if(rtStructure) {
      rtStructure.enterOptions.publicInfo = info;
      rtStructure.setValByCode(true);
      console.log('重载--->',info);
      try {
        if(setValByHis && hisAndFormMap) {
          setValByHis(hisAndFormMap, true)
        }
      } catch (err) {
        console.error(err);
      }
    }
    removeLoadCoverLayer();
  })
}

// 模板列表
function templateListHtml(data, successFn) {
  var html = '<div class="item-table" style="border:1px solid #C8D7E6;width:388px;">';
  html += '<style>.selText{color:#1885F2}</style>';
  html += '<div class="t-body" style="background: #F5F7FA;overflow: auto;height:300px">';
  if(data && data.length) {
    for(var i = 0; i < data.length; i++) {
      html += '<div id="'+data[i].patDocId+'" onclick="changeTempSelf(this)" style="padding: 8px 10px;cursor: pointer;" class="'+(patternInfo.patDocId === data[i].patDocId?'selText':'')+'">'+data[i].patternName+'</div>';
    }
  } else {
    html += '<div style="color:#666;padding:5px">暂无数据</div>';
  }
  html += '</div>';
  html += '</div>';
  html += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  html += '<button onclick="selectPattern(this, '+successFn+')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  html += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">关闭</button>';
  html += '</div>';
 return html;
}

// 切换模板
function selectPattern() {
  var patDocId = rtDialog.find('.selText').attr('id');
  if(!patDocId) {
    $wfMessage({
      content: '请选择模板'
    })
    return;
  }
  if(patDocId === patternInfo.patDocId) {
    // $wfMessage({
    //   type: 'warn',
    //   content: '该模板正在使用'
    // })
    removeRtDialog();
    return;
  }
  getParDoc(patDocId, true);
  removeRtDialog();
}

function changeTempSelf(ele) {
  if($(ele).hasClass('selText')){
    return;
  }
  $(ele).addClass('selText').siblings().removeClass('selText');
  selectPattern();
}
// 打开申请列表
function applyList() {
  var url = sourcePrefixUrl + '/template-lib/layouts/blApply/applies/applyList.html?reqDeptName=' + (applyInfo.reqDeptName || '') + '&reqDeptCode=' + (applyInfo.reqDeptCode || '') + '&reqPhysician=' + (applyInfo.reqPhysicianName || '') + '&optId=' + (applyInfo.optId || '') + '&optName=' + (applyInfo.optName || '');
  window.open(url, 'apply-win')
}

// 计费后处理配置中不可编辑的项
function setDisabledByConfig(applyStatusRes) {
  if(!applyStatusRes || typeof applyStatusRes !== 'object') {
    return;
  }
  // 检查项目不可编辑
  if(applyStatusRes.cantEditItem === '1') {
    $('[onclick="addMarkInfoHandler(\'examItem\')"]').hide();
    $(".eItemEditPart [onclick]").hide();
  }
}