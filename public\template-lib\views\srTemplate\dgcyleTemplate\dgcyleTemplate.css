#dgcyle1 {
  width: 780px;
  position: relative;
  font-size: 14px;
  color: #000;
  background: #FFFFFF;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  margin: 0 auto;
}

/*--------字体相关---------*/
.fs-12 {
  font-size: 12px;
}
.fs-16 {
  font-size: 16px;
}
.fs-20 {
  font-size: 20px;
  line-height: 20px;
}

/*--------颜色类---------*/
.text-c1{
  color: #303133;
}
.text-black {
  color: #000!important;
}
.bg-blue {
  background: #D6DFF7!important;
}
.bg-gray {
  background: #EAEAEA;
}

/*--------宽度类---------*/
.wd-17 {
  width: 20px!important;
}
.wd-23 {
  width: 23px!important;
}
.wd-27 {
  width: 27px!important;
  text-align: right;
}
.wd-32 {
  width: 32px!important;
}
.wd-38 {
  width: 36px!important;
  text-align: center;
}
.wd-41 {
  width: 41px!important;
}
.wd-60 {
  width: 60px;
}
.wd-70 {
  width: 70px;
}
.wd-150 {
  width: 150px;
}
.wd-213 {
  width: 213px!important;
  text-align: center;
}
.wd-216 {
  width: 216px!important;
}
.wd-260 {
  width: 260px;
}

/*--------间距---------*/
.mb-4 {
  margin-bottom: 4px;
}
.mb-10 {
  margin-bottom: 10px!important;
}
.mb-12 {
  margin-bottom: 12px!important;
}
.mb-16 {
  margin-bottom: 16px!important;
}
.ml-4 {
  margin-left: 4px;
}
.ml-12 {
  margin-left: 12px;
}
/*--------边框类---------*/
.bor-b {
  border-bottom: 1px solid #000;
}
/*--------位置类---------*/
.txt-r {
  text-align: right;
}
/*--------dgcyle报告页面---------*/
.dgcyle-header img {
  width: 100%;
}
.dgcyle-body {
  position: relative;
  padding: 0 50px;
  box-sizing: border-box;
}
.text-mid {
  text-align: center;
}
.header-title {
  font-size: 22px;
}
.con-flex {
  display: flex;
  justify-content: space-between;
}
.row-box {
  position: relative;
  padding: 8px 0;
  box-sizing: border-box;
}
.row-box::after {
  position: absolute;
  bottom: 0;
  content: "";
  width: 100%;
  height: 1px;
  background: #606266;
}
.row-item {
  min-width: 150px;
}
.data-box {
  position: relative;
}
.data-box::after {
  position: absolute;
  content: '';
  width: 100%;
  height: 1px;
  background: #606266;
  left: 0;
  bottom: -12px;
}
.remark-inp {
  width: 100%;
  resize: vertical;
  padding: 0 3px;
}
.box-left,.box-right {
  position: relative;
  width: 334px;
  border: 1px solid #000000;
}
.box-header {
  height: 32px;
  line-height: 32px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background: #191975;
}
.qd-block {
  font-size: 12px;
  margin: 8px 90px 5px;
}
.qd-sel {
  font-size: 12px;
  border: 1px solid #d7dae2;
}
.echart-data {
  width: 320px;
  height: 329px;
  margin: auto;
}
.echart-data img {
  width: 100%;
  height: 100%;
}
.tb-title {
  font-size: 18px;
  line-height: 22px;
  text-align: center;
  color: #333333;
  margin-bottom: 4px;
}
.no-bor {
  border: none;
}
.table-data,.pta-box {
  border: 1px solid #000000;
}
.table-data tr {
  color: #333333;
  font-weight: 400!important;
}
.table-data tbody tr td:first-child {
  border-right: 1px solid #000;
}
.table-data tbody tr td:first-child {
  line-height: 24px;
}
.pta-tb thead {
  height: 24px;
}
.pta-tb thead th {
  font-weight: normal;
}
.pta-tb thead th:first-child {
  border-right: 1px solid #000;
}
.pta-tb thead th:not(:first-child) {
  border-bottom: 1px solid #000;
}
.pta-tb tbody tr td:first-child {
  line-height: 20px;
  border-right: 1px solid #000;
  padding: 0 10px 0 8px;
  box-sizing: border-box;
}
.pta-tb tbody tr td:not(:first-child) {
  border-right: 1px solid #000;
  border-bottom: 1px solid #000;
}
.pta-tb tbody tr td:last-child {
  border-right: none!important;
}
.pta-tb tbody tr:last-child td {
  border-bottom: none!important;
}
#dgcyle1 .tb-inp {
  display: inline-block;
  width: 32px;
  /* padding: 0 2px; */
  box-sizing: border-box;
  min-height: 20px!important;
  border: none!important;
  outline: none;
  background: transparent!important;
}
#dgcyle1 input[type="checkbox"] {
  vertical-align: middle;
}
#dgcyle1 .inp-sty{
  width: 144px;
  min-height: 20px;
  border: none;
  border-bottom: 1px solid #606266;
  background: #fff;
}
.dgcyle-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 50px 43px;
  align-items: center;
}
.dgcyle-footer .sel-block {
  display: flex;
  align-items: center;
}
.dgcyle-footer .sel-block.mt-8 {
  margin-top: 0!important;
}
#dgcyle1 .serial-no {
  width: 150px;
  height: 16px;
  position: absolute;
  top: 56%;
  right: -67px;
  transform: rotate(-90deg);
  font-size: 12px;
}
#dgcyle1 .right-ear-tb, #dgcyle1 .left-ear-tb {
  font-size: 12px!important;
}
[isView="true"] .dgcyle-footer img{
  max-width: unset!important;
  max-height: unset!important;
  width: 100px;
  height: 40px;
  object-fit: contain;
}
[isView="true"] .pta-td .text-con {
  display: inline-block;
  width: 213px;
  line-break: anywhere;
}
[isView="true"] .exam-people, [isView="true"] .duty-nurse {
  display: none;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}