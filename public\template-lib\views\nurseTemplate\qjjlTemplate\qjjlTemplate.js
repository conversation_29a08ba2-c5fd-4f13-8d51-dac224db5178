$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var bzConClone = null;
var liveConClone = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //模版是否保存
var doctorList = [];
var nurseList = [];
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      inintPreview('preview')
    } else {
      inintPreview('edit');
      pageInit()
    }
  }
}

function inintPreview(type){
  $(`[data-type]`).hide();
  $(`[data-type=${type}]`).show();
  if(type==='preview'){    
    $('[data-key]').each(function(){
      let key = $(this).attr('data-key')
      if(idAndDomMap){
        let result = [],list = []
        Object.keys(idAndDomMap).forEach(idKey=>{
          if(idKey.startsWith(key)){
           let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value:'';
           if(value){
            result.push(value)
           }
          }
        })
        // console.log('result',result)
        $(this).html(result.join(','))
        
        this.style.color = '#000'  
      }
    })
    initBzCloneEle();
    // 回显镇静记录
    allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    console.log('allResData',allResData);
    if(allResData && allResData.length) {
      displayBzContent();
      displayLiveContent()
    }
  }else{
    $(`.live-con[data-type="preview"]`).remove()
  }
}

function pageInit() {
  initBzCloneEle();
  window.getNurseList ? toNurseList() : '';
  window.getNurseList ? toDoctorList() : '';
  console.log('rtStructure.enterOptions.resultData',rtStructure.enterOptions.resultData)
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    curElem.find('.def-ck').click();
    addBzHandler();
    addLiveHandler();
  } else {
    displayBzContent();
    displayLiveContent()
  }
  if(!isSavedReport){
    $('#qjjl-rt-13').attr('checked',true)
  }
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    //执行一个laydate实例
    laydate.render({
      elem: '#qjjl-rt-1',//指定元素
      type: 'datetime',
      value: !isSavedReport ? new Date() : '',
      done: function(value, date, endDate){
        console.log(value, date, endDate);
        // rescueResult()
      }
    });

    laydate.render({
      elem: '#qjjl-rt-2',//指定元素
      type: 'datetime',
      value: !isSavedReport ? new Date() : ''
    });
    laydate.render({
      elem: '#qjjl-rt-006',//指定元素
      type: 'datetime',
      value: !isSavedReport ? new Date() : ''
    });
    $('#qjjl-rt-21').on('change', function(value) {
      var is_addChoice = $("#qjjl-rt-21").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#qjjl-rt-22',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            // console.log(value, date, endDate);
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#qjjl-rt-23').on('change', function(value) {
      var is_addChoice = $("#qjjl-rt-23").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#qjjl-rt-24',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#qjjl-rt-25').on('change', function(value) {
      var is_addChoice = $("#qjjl-rt-25").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#qjjl-rt-26',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#qjjl-rt-27').on('change', function(value) {
      var is_addChoice = $("#qjjl-rt-27").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#qjjl-rt-28',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
  })
  $('#qjjl-rt-22').on('blur', function(value) {
    rescueResult()
  })
  $('#qjjl-rt-24').on('blur', function(value) {
    rescueResult()
  })
  $('#qjjl-rt-26').on('blur', function(value) {
    rescueResult()
  })
  $('#qjjl-rt-28').on('blur', function(value) {
    rescueResult()
  })
  initInpAndSel('qjjl-rt-3', doctorList);
  initInpAndSel('qjjl-rt-4', nurseList);
  initInpAndSel('qjjl-rt-005', nurseList,optHandler  );
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#qjjl-rt-005').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#qjjl-rt-005').val()
    if(val){
      let obj =  nurseList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
  
}

function rescueResult(){
  let str = '',obj = {
    '停止CPR': '停止CPR：' + $('#qjjl-rt-22').val(),
    '自主循环恢复': '自主循环恢复：' + $('#qjjl-rt-24').val(),
    '宣告临床死亡': '宣告临床死亡：' + $('#qjjl-rt-26').val(),
    '家属要求放弃抢救': '家属要求放弃抢救:' + $('#qjjl-rt-28').val(),
  };
  if($("#qjjl-rt-21").is(':checked') == false){
    delete obj['停止CPR']
  }
  if($("#qjjl-rt-23").is(':checked') == false){
    delete obj['自主循环恢复']
  }
  if($("#qjjl-rt-25").is(':checked') == false){
    delete obj['宣告临床死亡']
  }
  if($("#qjjl-rt-27").is(':checked') == false){
    delete obj['家属要求放弃抢救']
  }
  let list = [];
  Object.entries(obj).forEach(([key, value]) => {
    list.push(value)
  })
  str = list.join(',');
  console.log(str);
  $("#qiangjiuEffect").val(str);
}

function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }

function toNurseList(){
  let list = window.getNurseList({});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
}

function toDoctorList(){
  let list = window.getNurseList({},'','doctor');
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  doctorList = userList;
}

function displayBzContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'qjjl-bzlist-1')
    // console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('qjjl-bzlist-', '');
        addBzHandler(paneId)
      })
    }
  } else {
    curElem.find('.bz-list .bz-wrap').hide();
  }
}

function displayLiveContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'qjjl-rtlist-1')
    // console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('qjjl-rtlist-', '');
        addLiveHandler(paneId)
      })
    }
  } else {
    curElem.find('.bz-list .bz-wrap').hide();
  }
}

function delTab(vm, paneId){
  var allSiblings = $(vm).parent().parent().parent().siblings();
  console.log('allSiblings',allSiblings)
  allSiblings.each(function(index) {
      console.log($(this).children().children().children()[0],index); // 输出：first 和 third
      $(this).children().children().children().each(function(cIndex) {
        console.log('$(this)',$(this));
        if(cIndex === 0){
          $(this).html('医嘱记录'+(index + 1))
        }
      })
  });
  $(vm).parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}

function delTrFuc(vm, paneId){
  var allSiblings = $(vm).parent().parent().siblings();
  allSiblings.each(function(index) {
      // console.log($(this).children().first(),index); // 输出：first 和 third
      $(this).children().first().html(index + 1)
  });
  $(vm).parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}

// 添加处理医嘱,oldPaneId已保存过的
function addLiveHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.table-content .live-con .bz-tr').length;
  // console.log('bzLen',bzLen)
  var activeTab = 'qjjl-rtlist-' + paneId;
  var newPaneBlock = appendLiveHtml(paneId, bzLen,oldPaneId);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '生命体征记录',
      name: '生命体征记录',
      pid: 'qjjl-rtlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('体征记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.table-content .live-con .bz-tr').find("[rt-sc]");
    // console.log('wCon',wCon)
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

// 添加处理医嘱,oldPaneId已保存过的
function addBzHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.bz-list .bz-wrap .bz-con .bz-item').length;
  // console.log('bzLen',bzLen)
  var activeTab = 'qjjl-bzlist-' + paneId;
  var newPaneBlock = appendBzHtml(paneId, bzLen);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '处理医嘱',
      name: '处理医嘱',
      pid: 'qjjl-bzlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('医嘱记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.bz-list .bz-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    newPaneBlock.find('.def-ck').click();
  }
}

// 处理新增处理医嘱的html
function appendBzHtml(paneId, bzLen,oldPaneId) {
  // console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  var content = bzConClone.replace(reg, paneId);  //作为病灶示例模板具体内容
  $('.bz-list .bz-con').append(content);
  $('#qjjl-bzlist-'+paneId+'-1').html('医嘱记录' + (bzLen + 1));
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#qjjl-bzlist-'+paneId+'-2',//指定元素
      type: 'datetime',
      value: new Date()
    });
  })
  initInpAndSel('qjjl-bzlist-'+paneId+'-6', doctorList);
  initInpAndSel('qjjl-bzlist-'+paneId+'-7', nurseList);
  var newPaneBlock = $('.bz-list .bz-item[tab-target="qjjl-bzlist-'+paneId+'"] .drug-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}

// 处理新增生命体征记录的html
function appendLiveHtml(paneId, bzLen,oldPaneId) {
  // console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  var content = liveConClone.replace(reg, paneId);  //作为病灶示例模板具体内容
  $('.table-content .live-con').append(content);
  $('#qjjl-rtlist-'+paneId+'-1').html((bzLen + 1));
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    laydate.render({
      elem: '#qjjl-rtlist-'+paneId+'-2',//指定元素
      type: 'datetime',
      value:  oldPaneId ? '' : new Date()
    });
  })
  var newPaneBlock = $('.table-content .live-con .bz-tr[tab-target="qjjl-rtlist-'+paneId+'"]');
  return newPaneBlock;
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}

// 复制暂存处理医嘱的具体模板示例内容，为后续添加处理医嘱做准备
function initBzCloneEle() {
  var bzCon = curElem.find('.bz-list .bz-wrap .bz-item').clone(true);
  bzConClone = '<div class="bz-item act" tab-target="qjjl-bzlist-000">'+bzCon.html()+'</div>';
  var liveCon = curElem.find('.table-content .live-con .bz-tr').clone(true);
  liveConClone = '<tr class="bz-tr rt-sr-w" tab-target="qjjl-rtlist-000" id="qjjl-rtlist-000" pid="qjjl-rtlist-1" rt-sc="pageId:qjjl1;name:体征记录;wt:;desc:体征记录;vt:;pvf:;">'+liveCon.html()+'</tr>';
  curElem.find('.bz-list .bz-wrap .bz-con').html('');
  curElem.find('.table-content .live-con').html('');
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}