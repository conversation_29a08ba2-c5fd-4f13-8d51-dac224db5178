$(function () {
  window.initHtmlScript = initHtmlScript;
})
// 标本类型
var sampleTypeList = [
  { id: "石蜡组织切片", title: "石蜡组织切片"},
  {id: "外院会诊白片",title: "外院会诊白片"}, 
  {id: "尿沉渣",title: "尿沉渣"},
  {id: "细胞沉渣制片",title: "细胞沉渣制片"},
]
// 固定液
var fixLiquid = [{
  id: "10%中性缓冲福尔马林",
  title: "10%中性缓冲福尔马林"
}, {
  id: "不详",
  title: "不详"
}]
// 固定前时间
var beforeFixTime = [
  { id: "<30分钟", title: "<30分钟" },
  { id: ">30分钟", title: ">30分钟" },
  { id: "不详", title: "不详" }
]
// 固定时间
var fixDate = [{ id: "24-36小时", title: "24-36小时" }, {
  id: "11-16小时",
  title: "11-16小时"
}, {
  id: "不详",
  title: "不详"
}]
// 探针信息和生产商
var producer = [
  { id: "安必平", title: "安必平" }, 
  { id: "Vysis", title: "Vysis" }, 
  { id: "KREATECH", title: "KREATECH" }, 
  { id: "Zytonision", title: "Zytonision" }, 
  { id: "", title: " " }
]
// 样本量是否适合评估
var estimate = [{
  id: "足够", title: "足够"
}, {
  id: "",
  title: " "
}]
//FISH报告模板     检测项目
var examItem = [
  { id: "", title: " " },
  { id: "1p/1q；19p/19q", title: "1p/1q；19p/19q" },
  { id: "ALK BA", title: "ALK BA" },
  { id: "BCL2 BA", title: "BCL2 BA" },
  { id: "CMET（7q31）/CEP7", title: "CMET（7q31）/CEP7" },
  { id: "FOXO1 BA", title: "FOXO1 BA" },
  { id: "HER2/CEP17（乳腺癌）", title: "HER2/CEP17（乳腺癌）" },
  { id: "MALT淋巴瘤", title: "MALT淋巴瘤" },
  { id: "MAML2 BA", title: "MAML2 BA" },
  { id: "MDM2/CEP12", title: "MDM2/CEP12" },
  { id: "PDGFB BA", title: "PDGFB BA" },
  { id: "ROS1 BA", title: "ROS1 BA" },
  { id: "软组织分离", title: "软组织分离" },
  { id: "TFE3 BA", title: "TFE3 BA" },
  { id: "TOP2A/CEP17", title: "TOP2A/CEP17" },
  { id: "USP6 BA", title: "USP6 BA" },
  { id: "淋巴瘤多项", title: "淋巴瘤多项" },
  { id: "CCND1/IGH", title: "CCND1/IGH" },
  { id: "CEP3/CEP7CEP7/LSH", title: "CEP3/CEP7CEP7/LSH" },
  { id: "P16", title: "P16" },
  { id: "DDIT3", title: "DDIT3" },
  { id: "IRF4", title: "IRF4" },
  { id: "11q", title: "11q" },
  { id: "HER2/CEP17（肺癌）", title: "HER2/CEP17（肺癌）" },
  { id: "HER2/CEP17（胃癌）", title: "HER2/CEP17（胃癌）" },
  { id: "自定义", title: "自定义" },
]
var commonDef = {
  sampleType: "手术",
  fixLiquid: "10%中性缓冲福尔马林",
  beforeFixTime: "<30分钟",
  fixDate: "24-36小时",
  producer: "Vysis",
  estimate: "足够"
}
var fishData = [
  { id: "", references: "", changeId: ['a2', 'b2'] },
  { id: "1p/1q；19p/19q", references: "Anticancer Drugs.2005 Apr.16(4):461-6.", changeId: ['a1', 'b1'] },
  {
    id: "ALK BA",
    judgeResult: "用ALK双色分离探针检测ALK基因断裂情况，计数50个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "ALK基因有/无断裂（%，参考阈值>15%）。",
    changeId: ['a2', 'b2']
  },
  {
    id: "BCL2 BA",
    references: "",
    judgeResult: "用BCL2双色分离探针检测BCL2基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "BCL2基因有/无断裂（%，参考阈值>10%）。",
    changeId: ['a2', 'b2']
  },
  { id: "CMET（7q31）/CEP7", references: "Vi-Long Wu,et al.ASCOC  2014,Abstract 8001.", changeId: ['a4', 'b4'] },
  {
    id: "FOXO1 BA",
    references: "Genomic and Clinical Analysis of Fusion Gene Amplification in Rhabdomyosarcoma: a Report from the Children's Oncology Group，Genes Chromosomes Cancer. 2012 July ; 51(7): 662-674. doi:10.1002/gcc.21953.",
    judgeResult: "用FOXO1双色分离探针检测FOXO1基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "FOXO1基因有/无断裂（%，参考阈值>17%）。",
    changeId: ['a2', 'b2']
  },
  { id: "HER2/CEP17（乳腺癌）", references: "《乳腺癌HER2检测指南（2024版）》中华病理学杂志2024年12月第53卷第12期。", changeId: ['a6', 'b6'] },
  {
    id: "MALT淋巴瘤",
    references: "",
    judgeResult: "1）用MALT/IGH双色融合探针检测MALT基因与IGH基因融合情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见MALT与IGH单融合与双融合信号。\n2）用MALT/API2双色融合探针检测MALT基因与API2基因融合情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见MALT与API2单融合与双融合信号。",
    conclusion: "1）MALT/IGH基因有/无融合（0%，参考阈值>10%）；\n2）MALT/API2基因有/无融合（0%，参考阈值>10%）。",
    changeId: ['a2', 'b2']
  },
  {
    id: "MAML2 BA",
    references: "J Mol Diagn。2004 Aug；6（3）：205-210。",
    judgeResult: "用MAML2双色分离探针检测MAML2基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "MAML2基因有/无断裂（%，参考阈值>10%）。",
    changeId: ['a2', 'b2']
  },
  { id: "MDM2/CEP12", references: "Modern Pathology(2008)21.943-949", changeId: ['a9', 'b9'] },
  {
    id: "PDGFB BA",
    references: "Anticancer Drugs.2005 Apr；16(4):461-6.",
    judgeResult: "用PDGFB双色分离探针检测PDGFB基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号/多出橙色信号。",
    conclusion: "PDGFB基因有/无断裂（%，参考阈值>20%）。",
    changeId: ['a2', 'b2']
  },
  {
    id: "ROS1 BA",
    references: "Clin Cancer Res.2012 Sep 1;18(17):4570-4579。",
    judgeResult: "用ROS1双色分离探针检测ROS1基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "ROS1基因有/无断裂（%，参考阈值>15%）。",
    changeId: ['a2', 'b2']
  },
  { id: "软组织分离", references: "", judgeResult: "", conclusion: "", changeId: ['a2', 'b2'] },
  {
    id: "TFE3 BA",
    references: "Zhao et al.Diagnostic Pathology(2015)10:160",
    judgeResult: "用TFE3双色分离探针检测TFE3基因断裂情况，计数200个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "TFE3基因有/无断裂（%，参考阈值>15%）。",
    changeId: ['a2', 'b2']
  },
  { id: "TOP2A/CEP17", references: "Orlando L,et al,Breast 2008.17:506-511.", conclusion: "OP2A基因有缺失/TOP2A基因有扩增/TOP2A基因无扩增", changeId: ['a14', 'b2'] },
  {
    id: "USP6 BA",
    references: "Hum Pathol.2014 Jun,45(6):1147-1152",
    judgeResult: "用USP6 双色分离探针检测USP6基因断裂情况，计数200个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "USP6基因有/无断裂（%，参考阈值>20%）。",
    changeId: ['a2', 'b2']
  },
  {
    id: "淋巴瘤多项",
    references: "",
    judgeResult: "1）用CMYC 双色分离探针检测MYC基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。\n2）用BCL2/IGH 双色融合探针检测BCL2基因与IGH基因融合情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单融合及双融合信号。\n3）用BCL6  双色分离探针检测BCL6基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。\n4）用ALK双色分离探针检测ALK基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离及双分离信号。",
    conclusion: "1）CMYC基因有/无断裂（%，参考阈值>10%）；\n2）BCL2/IGH基因有/无融合（%，参考阈值>10%）；\n3）BCL6基因有/无断裂（%，参考阈值>10%）；\n4）ALK基因有/无断裂（%，参考阈值>15%）。",
    changeId: ['a2', 'b2']
  },
  {
    id: "CCND1/IGH",
    references: "",
    judgeResult: "用CCDN1/IGH双色融合探针检测CCDN1基因与IGH基因融合情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单融合和双融合信号。",
    conclusion: "CCDN1/IGH基因有/无融合（%，参考阈值>10%）。",
    changeId: ['a2', 'b2']
  },
  { id: "CEP3/CEP7CEP17/LSH", references: "", judgeResult: "", conclusion: "", changeId: ['a2', 'b2'] },
  { id: "P16", references: "", judgeResult: "", conclusion: "", changeId: ['a2', 'b2'] },
  {
    id: "DDIT3",
    references: "Am J Surg Pathol.(2008)32.8-13。",
    judgeResult: "用DDIT3双色分离探针检测DDIT3基因断裂情况，计数100个肿瘤细胞核：\n肿瘤细胞内未见单分离和双分离信号。",
    conclusion: "阴性（DDIT3基因无扩增）。",
    changeId: ['a2', 'b2']
  },
  { id: "IRF4", references: "", judgeResult: "", conclusion: "", changeId: ['a2', 'b2'] },
  { id: "11q", references: "", judgeResult: "", conclusion: "", changeId: ['a2', 'b2'] },
  { id: "HER2/CEP17（肺癌）", references: "J Clin Oncol.2005;23(22):5007-5018。", changeId: ['a6', 'b6'] },
  { id: "HER2/CEP17（胃癌）", references: "《胃癌HER2检测指南》中华病理学杂志2011年8月第40卷第8期。", changeId: ['a6', 'b6'] },
  { id: "自定义", judgeResult: "", conclusion: "", changeId: ['a2', 'b2'] },
]
//特殊备注
var specialRemark = [
  { id: "", title: " " },
  { id: "当日未送", title: "当日未送" },
  { id: "漏执行医嘱", title: "漏执行医嘱" },
  { id: "疑难病例", title: "疑难病例" },
  { id: "重复检测", title: "重复检测" },
  { id: "多种方法验证", title: "多种方法验证" },
  { id: "多人复阅", title: "多人复阅" },
  { id: "外院白片异常", title: "外院白片异常" },
]
var redId = ['fish-rt-88', 'fish-rt-90', 'fish-rt-92'];
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var enterOptions = null;
var resultData = [];
var isSavedReport = false; //报告是否填写过
var sampleSelType = '', fixYe = '', pg = '', product = '', fixTimes = '', fixbef = '';
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#pacsFish1 .pacsFish-view'),  //转成pdf的区域，默认是整个页面
    }
  }

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
    let { target } = e;
    let { id = '' } = target;
    redId.forEach((eId) => {
      $('#' + eId).parent().css('color', (_is_checked_ && eId === id ? 'red' : ''))
    })
  });

  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    enterOptions = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions : {};
    resultData = enterOptions && enterOptions.resultData ? enterOptions.resultData : [];
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      resetLogoStyle();
      getCloudFilmQrCodeUrl(publicInfo.examNo, $('.pacsFish-view .view-head'));
      initViewCon();
      replaceViewPatternByValue($('[data-key="inpatientNo"]').siblings('.gray-txt'), $('[data-key="inpatientNo"]'), publicInfo);
    } else {
      initPage();
      getProducerByExamItem(true);
    }
    findExamItemByMark();
  }
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if (displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.pacsFish-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    let str = '';
    if (key === 'result') {
      str = enterOptions.description;
      $(this).html(str);
    } else if (key === 'conclusion') {
      let bList = [], jdbList = {};
      jdbList = resultData.filter((item) => item.id === "fish-rt-79") && resultData.filter((item) => item.id === "fish-rt-79").length > 0 ? resultData.filter((item) => item.id === "fish-rt-79")[0] : []
      for (var i = 0; i < fishData.length; i++) {
        if (fishData[i].id === $("#fish-rt-2").val()) {
          let { changeId = [] } = fishData[i];
          if (changeId[0] === "a2") {
            str = $("#fish-rt-86").val();
          } else {
            let bId = "conclusion-" + changeId[1];
            $('#' + bId).find('.rt-sr-w[pid=fish-rt-79]').each(function (pIdx, par) {
              let parent = $(par);
              let pid = parent.attr('id');
              let jbChild = jdbList.child;
              let curPid = jbChild.filter((item) => item.id === pid) && jbChild.filter((item) => item.id === pid).length ? jbChild.filter((item) => item.id === pid)[0] : ''
              if (curPid) {
                let child = curPid.child;
                if (child && child.length > 0) {
                  bList.push(curPid.val + child[0].val)
                } else {
                  bList.push(curPid.val)
                }
              }
            })
            bList && bList.length > 0 ? str = bList.join('\n') : ''
          }
          i = fishData.length;
        }
      }
      str && str.indexOf('阳性') !== -1 ? str = `<span style="color:red;">${str}</span>` : ''
      $(this).html(str);
      $(this).css('padding-left', '28px');
    } else {
      var value = publicInfo[key] || idAnVal;
      if (displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
        value = value + ' ' + publicInfo['affirmTime'];
      }
      key === 'fish-rt-94' ? $(this).css('padding-left', '28px') : '';
      if(key === 'outterGmId'){
        // console.log('>>>idAndDomMap',idAndDomMap);
        // idAndDomMap['fish-rt-8'].value && value ? value += `-${idAndDomMap['fish-rt-8'].value}` : ''
        idAndDomMap['fish-rt-8'].value ? value = `${publicInfo['fish-rt-8'] || idAndDomMap['fish-rt-8'].value}` : ''
      }
      $(this).html(value);
    }
    addIdToNodeByView(this, key, idAndDomMap);
  });

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="item-box load-img">
          <div class="item-img follow-break">
            <img class="follow-break" src="${image.src}" alt="">
          </div>
          <div class="img-mark follow-break">${image.remark || ''}</div>
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find('.pacsFish-view .rpt-img-ls').html(imgHtml);
      curElem.find('.pacsFish-view .rpt-img-ls').css({ 'display': 'flex', 'padding-left': '54px' });
    } else {
      $('.rpt-img-ls').parent().parent().css('display', 'none')
    }
  } else {
    $('.rpt-img-ls').parent().parent().css('display', 'none')
  }
  curElem.find('.pacsFish-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}
function initIdLsit(num) {
  return `fish-rt-${num}`
}
// 初始化
function initPage() {
  $('#fish-rt-1').parent().addClass('mt-6');
  $('#fish-rt-95').parent().addClass('mt-6');
  var reportTem = initIdLsit(2);
  var item = initIdLsit(20)
  initInpAndSel(item, examItem, 412);
  initTemplateSel(reportTem);
  sampleSelType = initIdLsit(4);
  fixYe = initIdLsit(10);
  fixbef = initIdLsit(12);
  fixTimes = initIdLsit(14);
  product = initIdLsit(18);
  pg = initIdLsit(22);
  var specialId = initIdLsit(96);
  initInpAndSel(sampleSelType, sampleTypeList, 200);
  initInpAndSel(fixYe, fixLiquid, 200);
  initInpAndSel(fixbef, beforeFixTime, 180);
  initInpAndSel(fixTimes, fixDate, 180);
  initInpAndSel(product, producer, 200);
  initInpAndSel(pg, estimate, 200);
  initInpAndSel(specialId, specialRemark, 160);
  // !isSavedReport ? createDef() : '';
  redRadio();
  idAndDomMap['fish-rt-2'] && idAndDomMap['fish-rt-2'].value ? changeElm(idAndDomMap['fish-rt-2'].value, 'init') : '';
  layui.use(['form'], function () {
    var form = layui.form;
    form.on('select(selChange)', function (data) {
      var val = data.value;
      val ? changeElm(val, 'change') : '';
      if(data.elem.id === 'fish-rt-2') {
        getProducerByExamItem();
      }
    });
  })
}
// 未填写过报告的，根据标记物匹配检查项目
function findExamItemByMark() {
  // 填写过报告的忽略
  if(isSavedReport) {
    return;
  }
  var markInfo = getSampleOrganList();
  var candleId = markInfo.candleId || '';  //蜡块号
  var marker = markInfo.marker;  //标记物
  // 蜡块号每次都更新（req16416，改成修改过按修改的为准）
  if(candleId && !isSavedReport && rtStructure.enterOptions.type !=='view') {
    curElem.find('#fish-rt-8').val(candleId);
    curElem.find('[data-key="fish-rt-8"]').text(candleId);
  }
  // if(marker && !isSavedReport && rtStructure.enterOptions.type!=='view') {
  //   var examItem = ngsMarkMap[marker] || '';
  //   if(examItem) {
  //     curElem.find('#fish-rt-20').val(examItem);
  //     changeSel(examItem);
  //   }
  // }
}
//红色渲染
function redRadio() {
  redId.forEach((eId) => {
    var checked = $('#' + eId).prop("checked");
    $('#' + eId).parent().css('color', (checked ? 'red' : ''))
  })
}
//渲染默认值
function createDef() {
  $('#' + sampleSelType).val(commonDef.sampleType);
  $('#' + fixYe).val(commonDef.fixLiquid);
  $('#' + fixbef).val(commonDef.beforeFixTime);
  $('#' + fixTimes).val(commonDef.fixDate);
  $('#' + product).val(commonDef.producer);
  $('#' + pg).val(commonDef.estimate);
}
//模板下拉框
function initTemplateSel(id) {
  var form = layui.form;
  form.render();
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, len) {
  let dataList = [];
  dataList = JSON.parse(JSON.stringify(optionList));
  if (idList === "fish-rt-20") {
    dataList[6] = { id: "HER2/CEP17", title: "HER2/CEP17" };
    dataList.splice(23, 3);
  }
  let dropdown = layui.dropdown;
  dropdown.render({
    id: `${idList}`,
    elem: `#${idList}`,
    data: dataList,
    click: function (obj) {
      this.elem.val(obj.title);
      let id = this.elem.attr('id');
      id === "fish-rt-2" ? changeElm(obj.title, 'change') : ""
      if(id === 'fish-rt-20') {
        getProducerByExamItem();
      }
      if (id === 'fish-rt-4' && obj.title === '外院会诊白片') {
        $('#fish-rt-10').val('不详');
        $('#fish-rt-12').val('不详');
        $('#fish-rt-14').val('不详');
      }
    },
    style: `width: ${len}px;`
  })
}
//切换对应子页面
function changeElm(str, type) {
  // !isSavedReport ? createDef() : '';
  for (var i = 0; i < fishData.length; i++) {
    if (fishData[i].id === str) {
      let { changeId = [], references = '', judgeResult = '', conclusion = '' } = fishData[i];
      let changeAId = "result-" + changeId[0];
      let changeBId = "conclusion-" + changeId[1];
      let parentAElm = $("#" + changeAId).parent();
      let parentBElm = $("#" + changeBId).parent();
      if (type && type !== "init") {
        str === " " || str === "自定义" ? $('#fish-rt-20').val('') : $('#fish-rt-20').val(changeId[0] === 'a6' ? 'HER2/CEP17' : str);
        $('#fish-rt-24').val(references);
        $('#fish-rt-42').val(judgeResult ? judgeResult : '');
        $('#fish-rt-86').val(conclusion ? conclusion : '');
      }
      parentAElm.children().each(function (pIdx, par) {
        let parent = $(par);
        let pid = parent.attr('id');
        $("#" + pid).css('display', (changeAId === pid ? 'inline-block' : 'none'))
      })
      parentBElm.children().each(function (pIdx, par) {
        let parent = $(par);
        let pid = parent.attr('id');
        $("#" + pid).css('display', (changeBId === pid ? 'inline-block' : 'none'))
      })
      if (str === 'HER2/CEP17（胃癌）' || str === 'HER2/CEP17（肺癌）') {
        curElem.find('#fish-rt-62').val('');
        curElem.find('#fish-rt-62').closest('.row-item').hide();
      } else {
        curElem.find('#fish-rt-62').closest('.row-item').show();
      }
      return
    }
  }
}
// 获取分子病理原检查医嘱开单信息
function getSampleOrganList() {
  var markInfo = {};
  var params = {
    examNo: publicInfo.examNo,
    examClass: publicInfo.examClass,
    markerLike: 'FISH'
  }
  fetchAjax({
    url: api.getOrgMarkerInfo,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      if (res.status == '0' && res.result && res.result.length) {
        markInfo = res.result[0];
      }
    },
  })
  return markInfo;
}

// 根据模板/检查项目获取探针厂商
function getProducerByExamItem(firstIn) {
  const handler = function() {
    var muName = curElem.find('#fish-rt-2').val();  //模板名称
    var itemName = curElem.find('#fish-rt-20').val();  //项目名称
    // 获取探针厂商
    var producerList = getDefaultByPatternId('2032');
    var factoryList = [];
    if(producerList && producerList.length) {
      producerList.forEach((item) => {
        if(item.name === 'sr_dict_dlag_rpt_firm_config') {
          var reserve2 = item.reserve2 || '';
          var itemList = reserve2.split(';');  //配置关联的项目
          var value = item.value?.split(';');  //配置的探针厂商
          if(value.length) {
            if(!itemList.length || itemList.indexOf(itemName) > -1) {
              value.forEach(pItem => {
                factoryList.push({
                  id: pItem,
                  title: pItem  
                })
              })
            }
          }
        }
      })
    }
    if(!factoryList.length) {
      factoryList = JSON.parse(JSON.stringify(producer));
    }
    if(factoryList.length) {
      let dropdown = layui.dropdown;
      dropdown.reload('fish-rt-18', {
        data:factoryList,
      })
      if(!firstIn || !isSavedReport) {
        $('#fish-rt-18').val(factoryList[0].title);
      }
    }
    // curElem.find('#jycp-rt-4').val(defaultValueMap.reagentNo || '');
  }
  handler();
  curElem.on('change', '#fish-rt-20', function() {
    handler();
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    specialMemo: document.querySelector('#fish-rt-96')
    ? document.querySelector('#fish-rt-96').value
    : '', //特殊备注
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let aList = [], bList = [];
  for (var i = 0; i < fishData.length; i++) {
    if (fishData[i].id === $("#fish-rt-2").val()) {
      if (fishData[i].id === "HER2/CEP17（胃癌）") {
        curElem.find('#fish-rt-62').val('');
      }
      let { changeId = [] } = fishData[i];
      if (changeId[0] === "a2") {
        rtStructure.description = $("#fish-rt-42").val();
        rtStructure.impression = $("#fish-rt-86").val();
      } else {
        let aId = "result-" + changeId[0];
        let bId = "conclusion-" + changeId[1];
        $('#' + aId).find('.rt-sr-w[pid=fish-rt-25]').each(function (pIdx, par) {
          let parent = $(par);
          let pid = parent.attr('id');
          let curPid = curKeyValData[pid];
          if (curPid) {
            let child = curPid.child;
            if (child && child.length > 0) {
              aList.push(child[0].name + '：' + child[0].val);
            }
          }
        })
        $('#' + bId).find('.rt-sr-w[pid=fish-rt-79]').each(function (pIdx, par) {
          let parent = $(par);
          let pid = parent.attr('id');
          let curPid = curKeyValData[pid];
          if (curPid) {
            let child = curPid.child;
            if (child && child.length > 0) {
              bList.push(curPid.val + child[0].val)
            } else {
              bList.push(curPid.val)
            }
          }
        })
      }
      i = fishData.length;
    }
  }
  aList && aList.length > 0 ? rtStructure.description = aList.join('\n') : '';
  // bList && bList.length > 0 ? rtStructure.impression = bList.join('\n') : '';
  // rtStructure.recommendation = $("#fish-rt-94").val() || '';
  let impressionList = [];
  let jusgeResult = '', conclusion = '';
  aList && aList.length > 0 ? jusgeResult = `判读结果：${aList.join(',\n\t\t ')}。` : '';
  bList && bList.length > 0 ? conclusion = `结论：${bList.join('\n\t ')}` : '';
  $('.pacsFish-edit .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length > 0) {
        if (curPid.id === "fish-rt-25" || curPid.id === "fish-rt-79") {
          if (curPid.id === "fish-rt-25") {
            var textStylea = $('#result-a2').css('display');
            textStylea !== 'none' && curKeyValData['fish-rt-42'] ? jusgeResult = `判读结果：\n${curKeyValData['fish-rt-42'].val}` : ''
            jusgeResult ? impressionList.push(jusgeResult) : ''
          }
          if (curPid.id === "fish-rt-79") {
            var textStyleb = $('#conclusion-b2').css('display');
            textStyleb !== 'none' && curKeyValData['fish-rt-86'] ? conclusion = `结论：\n${curKeyValData['fish-rt-86'].val}` : ''
            conclusion ? impressionList.push(conclusion) : ''
          }
        } else {
          if (curPid.id === 'fish-rt-1' || curPid.id === 'fish-rt-93') {

          // } else if (curPid.id === 'fish-rt-93') {
          //   child[0].val && child[0].val !== '' ? impressionList.push(curPid.val + '\n' + child[0].val) : ''
          
          } else {
            child[0].val && child[0].val !== '' ? impressionList.push(curPid.val + child[0].val) : ''
          }
        }
      }
    }
  })
  impressionList && impressionList.length > 0 ? rtStructure.impression = impressionList.join('\n') : ''
}