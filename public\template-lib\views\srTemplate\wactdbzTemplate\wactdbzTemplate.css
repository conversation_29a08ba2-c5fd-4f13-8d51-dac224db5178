#wactdbz1 {
  height: 100%;
  color: #000;
  font-size: 14px;
}
#wactdbz1 .custom-table input[type=text] {
  width: 96%;
}
#wactdbz1 input {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 4px;
}
#wactdbz1 * {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
#wactdbz1 textarea {
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 6px 12px;
}
#wactdbz1 .distantTransfer {
  display: flex;
  flex-direction: column;
}
#wactdbz1 .container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
#wactdbz1 .container .container-header {
  height: 40px;
  line-height: 40px;
  background: #E8F3FF;
  box-shadow: inset 1px 0px 0px 0px #C8D7E6, inset -1px -1px 0px 0px #C8D7E6;
}
#wactdbz1 .container .container-header .pat-info {
  width: 960px;
  margin: 0 auto;
  font-size: 18px;
  font-weight: bold;
}
#wactdbz1 .container .container-content {
  flex: 1;
  overflow: auto;
  width: 960px;
  margin: 0 auto;
  background: #FFFFFF;
  border-right: 1px solid #DCDFE6;
  border-left: 1px solid #DCDFE6;
}
#wactdbz1 .container .container-content .form {
  padding: 12px;
}
#wactdbz1 .container .container-content .form .form-item {
  display: flex;
  margin-bottom: 20px;
}
#wactdbz1 .container .container-content .form .form-item .form-item-label {
  width: 114px;
  text-align: right;
  color: #303133;
  line-height: 40px;
}
#wactdbz1 .container .container-content .form .form-item .form-item-label-preview {
  line-height: unset;
}
#wactdbz1 .container .container-content .form .form-item .form-item-content {
  flex: 1;
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
  padding: 10px 8px;
}
#wactdbz1 .container .container-content .form .form-item .form-sub-item-label {
  width: 70px;
  text-align: right;
  color: #303133;
}
#wactdbz1 .container .emvi-container {
  display: flex;
  height: 134px;
}
#wactdbz1 .container .emvi-container .emvi-radio-group {
  display: flex;
  flex-direction: column;
  width: 128px;
  padding: 8px 0;
}
#wactdbz1 .container .emvi-container .emvi-radio-group .emvi-radio-group-item {
  padding: 0 8px;
  width: 100%;
}
#wactdbz1 .container .emvi-container .emvi-radio-child {
  display: flex;
  flex-direction: column;
  padding: 0 8px;
  flex: 1;
  background: #EBEEF5;
  border-left: 1px solid #C8D7E6;
  justify-content: space-around;
}
#wactdbz1 .container .emvi-container .emvi-radio-child label {
  display: flex;
  align-items: center;
  width: 100%;
}
#wactdbz1 .container .emvi-container .emvi-radio-child label input {
  margin-right: 8px;
}
#wactdbz1 .container .infringement {
  display: flex;
}
#wactdbz1 .container .infringement .infringement-item {
  flex: 1.5;
}
#wactdbz1 .container .infringement .infringement-item .infringement-item-radio {
  display: flex;
  align-items: center;
  padding: 2px;
}
#wactdbz1 .container .infringement .infringement-item .infringement-item-radio .infringement-item-radio-label {
  width: 60px;
  text-align: right;
  color: #303133;
}
#wactdbz1 .container .infringement .infringement-item-child {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-left: 1px solid #C8D7E6;
  padding: 8px;
}
#wactdbz1 .container .infringement .infringement-item-child :last-child {
  flex: 1;
}
#wactdbz1 .container .infringement .infringement-item-child textarea {
  height: 100%;
  width: 100%;
}
#wactdbz1 .container .size-content {
  display: flex;
  padding: 0 !important;
  height: 134px;
}
#wactdbz1 .container .size-content span {
  color: #303133;
}
#wactdbz1 .container .size-content .size-group {
  padding: 8px 0;
}
#wactdbz1 .container .size-content .size-group .size-group-item {
  padding-left: 8px;
}
#wactdbz1 .container .size-content .size-group .size-group-item:checked {
  background: #F5F7FA;
}
#wactdbz1 .container .size-content .size-value {
  background: #EBEEF5;
  border-left: 1px solid #DCDFE6;
  padding: 8px 12px;
}
#wactdbz1 .container .size-content .size-value .size-value-item input {
  width: 60px;
}
#wactdbz1 .container .density-group {
  display: flex;
  height: 104px;
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}
#wactdbz1 .container .density-group .density-group-radio {
  display: flex;
  flex-direction: column;
  width: 240px;
  padding-top: 8px;
}
#wactdbz1 .container .density-group .density-group-radio label {
  width: 100%;
  padding-left: 8px;
}
#wactdbz1 .container .density-group .density-group-toggleshow {
  padding: 8px;
  flex: 1;
  background-color: #EBEEF5;
  border-left: 1px solid #C8D7E6;
}
#wactdbz1 .container .container-footer {
  height: 104px;
  background: #E8F3FF;
  border: 1px solid #DCDFE6;
  padding: 8px 0;
}
#wactdbz1 .container .container-footer .footer-impression {
  height: 100%;
  display: flex;
  width: 960px;
  margin: 0 auto;
}
#wactdbz1 .container .container-footer .footer-impression .impression-label {
  display: inline-block;
  font-size: 18px;
  font-weight: bold;
  width: 114px;
  text-align: right;
}
#wactdbz1 .container .container-footer .footer-impression .impression-label-preview {
  text-align: center;
}
#wactdbz1 .container .container-footer .footer-impression-preview {
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
  padding: 8px;
}
#wactdbz1 .container .container-footer-preview {
  background: #F5F7FA;
  padding: 0;
}
#wactdbz1 .rt-sr-r {
  margin-right: 4px;
}
#wactdbz1 .pl-24 {
  padding-left: 24px;
}
#wactdbz1 .pr-24 {
  padding-right: 24px;
}
#wactdbz1 .pt-8 {
  padding-top: 8px;
}
#wactdbz1 .pb-8 {
  padding-bottom: 8px;
}
#wactdbz1 .mt-12 {
  margin-top: 12px;
}
#wactdbz1 .mt-8 {
  margin-top: 8px;
}
#wactdbz1 .mt-6 {
  margin-top: 6px;
}
#wactdbz1 .mt-4 {
  margin-top: 4px;
}
#wactdbz1 .m-0-6 {
  margin: 0 6px;
}
#wactdbz1 .w-10 {
  width: 10px;
}
#wactdbz1 .w-15 {
  width: 15px;
}
#wactdbz1 .w-20 {
  width: 20px;
}
#wactdbz1 .w-25 {
  width: 25px;
}
#wactdbz1 .w-30 {
  width: 30px;
}
#wactdbz1 .w-35 {
  width: 35px;
}
#wactdbz1 .w-40 {
  width: 40px;
}
#wactdbz1 .w-45 {
  width: 45px;
}
#wactdbz1 .w-50 {
  width: 50px;
}
#wactdbz1 .w-55 {
  width: 55px;
}
#wactdbz1 .w-60 {
  width: 60px;
}
#wactdbz1 .w-65 {
  width: 65px;
}
#wactdbz1 .w-70 {
  width: 70px;
}
#wactdbz1 .w-75 {
  width: 75px;
}
#wactdbz1 .w-80 {
  width: 80px;
}
#wactdbz1 .w-85 {
  width: 85px;
}
#wactdbz1 .w-90 {
  width: 90px;
}
#wactdbz1 .w-95 {
  width: 95px;
}
#wactdbz1 .w-100 {
  width: 100px;
}
#wactdbz1 .w-105 {
  width: 106px;
}
#wactdbz1 .w-110 {
  width: 110px;
}
#wactdbz1 .w-115 {
  width: 115px;
}
#wactdbz1 .w-120 {
  width: 120px;
}
#wactdbz1 .w-125 {
  width: 125px;
}
#wactdbz1 .w-130 {
  width: 130px;
}
#wactdbz1 .w-135 {
  width: 135px;
}
#wactdbz1 .w-140 {
  width: 140px;
}
#wactdbz1 .w-145 {
  width: 145px;
}
#wactdbz1 .w-150 {
  width: 150px;
}
#wactdbz1 .w-155 {
  width: 155px;
}
#wactdbz1 .w-160 {
  width: 160px;
}
#wactdbz1 .w-165 {
  width: 165px;
}
#wactdbz1 .f-1 {
  flex: 1;
}
#wactdbz1 .f-1-5 {
  flex: 1.5;
}
#wactdbz1 .f-1-6 {
  flex: 1.6;
}
#wactdbz1 .f-2 {
  flex: 2;
}
#wactdbz1 .f-3 {
  flex: 3;
}
#wactdbz1 .fw-600 {
  font-weight: 600;
}
#wactdbz1 .a-center {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#wactdbz1 .a-start {
  display: flex;
  align-items: flex-start;
}
#wactdbz1 .flex {
  display: flex;
}
#wactdbz1 .flex span:first-child {
  white-space: nowrap;
}
#wactdbz1 .flex-column {
  display: flex;
  flex-direction: column;
}
#wactdbz1 .text-r {
  text-align: right;
}
#wactdbz1 .fs-36 {
  font-size: 36px;
}
#wactdbz1 .fs-24 {
  font-size: 24px;
}
#wactdbz1 .fs-20 {
  font-size: 20px;
}
#wactdbz1 .mr-20 {
  margin-right: 20px;
}
#wactdbz1 .lbj-preview-label {
  width: 130px;
  text-align: right;
  color: #303133;
}
