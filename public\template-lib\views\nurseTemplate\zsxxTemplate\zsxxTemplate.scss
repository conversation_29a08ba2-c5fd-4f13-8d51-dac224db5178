#zsxx1 {
  font-size: 14px;
}
.injection {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.layui-item{
  width: 108px;
  text-align: center;
  cursor: pointer;
}
.item-select{
  color: #1885f2;
  height: 40px;
  line-height: 40px;
  border-bottom: 2px solid #1885f2;
}

.injection-information {
  display: flex;
  height: calc(100% - 64px);

  .left-content {
    width: 96px;
    background: #fff;
    box-shadow: -1px 0 0 0 #e4e7ec inset;

    .left-item {
      width: 96px;
      height: 64px;
      font-size: 14px;
      line-height: 64px;
      color: #303133;
      text-align: center;
      cursor: pointer;
    }

    .select-left {
      color: #1885f2;
      border-right: 2px solid #1885f2;
    }
  }

  .right-content {
    width: calc(100% - 96px);
    padding: 12px;
    overflow: auto;

    ::-webkit-scrollbar {
      width: 0;
    }
    .add-btn{
      width: 100px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      background: #ffffff;
      border: 1px solid #dcdfe6;
      border-radius: 3px;
      cursor: pointer;
    }
    .convention-btn{
      margin-left: 12px;
      margin-bottom: 12px;
      margin-top: 12px;
    }
    .delay-btn{
      margin-left: 12px;
      margin-bottom: 12px;
      margin-top: 12px;
      display: none;
    }
    .enhance-btn{
      margin-left: 12px;
      margin-bottom: 12px;
      margin-top: 12px;
      display: none;
    }
    .tags-content{
      width: 100%;
      background: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      .delay-list{
        display: none;
      }
      .enhance-list{
        display: none;
      }
      .tags-top{
        width: 100%;
        height: 40px;
        background: #ffffff;
        border-bottom: 1px solid #dcdfe6;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: start;
        .tags-item{
          width: 128px;
          text-align: center;
          cursor: pointer;
        }
        .select-item{
          color: #1885f2;
          height: 40px;
          line-height: 40px;
          border-bottom: 2px solid #1885f2;
        }
      }
      .tags-box{
        padding: 12px 12px 0 12px;
      }
    }
    .form-content{
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: space-between;
      .close-icon{
        margin-left: 5px;
      }
      .form-item{
        display: flex;
        align-items: center;
        width: 33.3%;
        margin-bottom: 10px;
        .form-label{
          min-width: 60px;
          text-align: right;
        }
        .form-title{
          min-width: 72px;
          width: 80px;
          text-align: right;
        }
        .form-second{
          min-width: 80px;
          width: 80px;
          text-align: right;
        }
        .radio-label{
          display: flex;
          align-items: center;
          margin-right: 18px;
          .rt-sr-lb{
            margin-left: 4px;
          }
        }
        .custom-select{
          width: 100%;
          position: relative;
          background: #fff;
        }
        .custom-select::after {
          content: "";
          position: absolute;
          width: 5px;
          height: 5px;
          right: 20px;
          top: 50%;
          z-index: 11;
          border-top: 1px solid #606266;
          border-right: 1px solid #606266;
          transform: rotate(135deg);
          margin-top: -5px;
        }
        .showInt{
          width: 100%;
          position: relative;
          background: #fff;
        }
        .showInt::after {
          content: "";
          position: absolute;
          width: 5px;
          height: 5px;
          right: 20px;
          top: 50%;
          z-index: 11;
          border-top: 1px solid #606266;
          border-right: 1px solid #606266;
          transform: rotate(135deg);
          margin-top: -5px;
        }
        .input-content{
          display: flex;
          align-items: center;
          width: calc(100% - 58px);
          .unit{
            min-width: 60px;
            height: 38px;
            line-height: 38px;
            text-align: center;
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 0 4px 4px 0;
            border-left: 0;
          }
        }
      }
    }
    .top-content {
      height: 40px;
      padding-left: 12px;
      font-size: 14px;
      font-weight: bold;
      line-height: 40px;
      color: #303133;
      background: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      border-radius: 2px;
    }

    .patient-content {
      background: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 2px;

      &:not(:first-child) {
        margin-top: 12px;
      }
    }

    .main-content {
      padding: 12px;
    }

    .form-item {
      display: flex;
      align-items: center;

      .label {
        width: 80px;
        color: #606266;
        text-align: right;
      }
    }
  }
}

.action {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 64px;
  line-height: 64px;
  text-align: center;
  border-top: 1px solid #dcdfe6;
}