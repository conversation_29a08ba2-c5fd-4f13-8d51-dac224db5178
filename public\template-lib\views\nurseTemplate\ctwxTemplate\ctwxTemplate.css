#ctwx1 {
  font-size: 16px;
}
#ctwx1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  margin-bottom: 4px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}
#ctwx1 .second-title {
  color: #E68A2E;
  margin-left: 16px;
}
#ctwx1 .allergy-content {
  margin-bottom: 16px;
}
#ctwx1 .second-content {
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  margin: 6px 16px;
  padding-bottom: 12px;
}
#ctwx1 .allergy-content .allergy-item {
  display: flex;
  align-items: center;
  margin-left: 16px;
}
#ctwx1 .allergy-content .allergy-item .layui-input {
  width: calc(100% - 24px);
}
#ctwx1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#ctwx1 .radio-label .rt-sr-lb {
  margin-left: 4px;
}
#ctwx1 .gray-item {
  margin-left: 12px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#ctwx1 .layui-inline {
  display: block;
  width: calc(100% - 24px);
}
#ctwx1 .allergy-content .hydration-item {
  margin-left: 10px;
  width: calc(100% - 32px);
  height: 60px;
  background: #FAFAFA;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 16px;
  margin-top: 8px;
}
#ctwx1 .allergy-content .hydration-item .item-left {
  display: flex;
  align-items: center;
}
#ctwx1 .sign-content {
  display: flex;
  align-items: center;
  margin-left: 10px;
  flex-wrap: wrap;
}
#ctwx1 .sign-content .layui-input {
  width: 100px;
}
#ctwx1 .sign-content .weight {
  margin-right: 20px;
  display: flex;
  align-items: center;
}
#ctwx1 .form-title{
  min-width: 48px;
}
#ctwx1 .sign-content .temperature {
  display: flex;
  align-items: center;
}
#ctwx1 .sign-content .unit {
  width: 58px;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #DCDFE6;
  border-left: 0;
  line-height: 38px;
  text-align: center;
  color: #606266;
}
#ctwx1 .showInt {
  position: relative;
  background: #fff;
  width: 190px;
}
#ctwx1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 11;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}


/*# sourceMappingURL=ctwxTemplate.css.map */
