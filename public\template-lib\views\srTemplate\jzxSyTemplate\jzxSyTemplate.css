.xmJzxPage{
  display: flex;
  flex-direction: column;
  height: 100%;
  flex-wrap: wrap;
  overflow: auto;
  font-size: 14px;
  color: #000;
}
.xmJzxPage .xm-con{
  width: 960px;
  margin: 0 auto;
  flex: 1;
  border-left: 1px solid #C8D7E6;
  border-right: 1px solid #C8D7E6;
  padding: 8px 0;
}
.xmJzxPage .hide-in{display: none;}
.xmJzxPage .gray-t{color: #303133 !important;}
.xmJzxPage .mb4{margin-bottom: 4px !important;}
.xmJzxPage .ml0{margin-left: 0 !important;}
.xmJzxPage .pt0{padding-top: 0 !important;}
.xmJzxPage .pd0{padding: 0 !important;}
.xmJzxPage .w60{width: 60px !important;}
.xmJzxPage .noBor{border: none !important;}
.xmJzxPage .noBorL{border-left: none !important;}
.xmJzxPage .noBorR{border-right: none !important;}
.xmJzxPage .noBorT{border-top: none !important;}
.xmJzxPage .noBorB{border-bottom: none !important;}
.xmJzxPage .xm-lb{margin-right:5px;color: #303133;width: 97px;text-align: right;padding-top:8px;word-break:break-all;}
.xmJzxPage .t-content .xm-lb{width: 110px;}
.xmJzxPage .xm-lb::after{content: '：';}
.xmJzxPage .gray-item{
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
  flex: 1;
  padding: 8px 12px;
}
.xmJzxPage .w-block-pd{padding:8px}
.xmJzxPage .w-block-pd.v{
  flex-direction: column;
}
.xmJzxPage .w-block-pd.v label{margin-left: 0!important;}
.xmJzxPage .bz-box{
  height: 180px;
  display: flex;
  position: relative;
  border: none;
  padding: 0;
}
.xmJzxPage .bz-box::after{
  position: absolute;
  z-index: 1;
  content: '';
  width: 100%;
  top: 0;
  bottom: 0;
  border-top: 1px solid #C8D7E6;
  border-bottom: 1px solid #C8D7E6;
}
.xmJzxPage .jzx-img img{
  height: 180px;
  position: relative;
  z-index: 2;
}
.xmJzxPage .jzx-box{
  position: relative;
  z-index: 2;
}
.xmJzxPage .jzx-box .jzx-point{
  position: absolute;
  z-index: 2;
  width: 12px;
  height: 12px;
  background: #000;
  border-radius: 50%;
  border: 1px solid #000;
  text-align: center;
  font-size: 12px;
  line-height: 10px;
  cursor: pointer;
}
.xmJzxPage .jzx-box .jzx-point.act {
  border-color: #FFF443;
}
.xmJzxPage .jzx-img{display: flex;}
.xmJzxPage .jzx-img, .xmJzxPage .lbj-img{
  height: 180px;
  margin-right: 12px;
  position: relative;
  z-index: 2;
}
.xmJzxPage .bz-desc{
  flex: 1;
  padding: 8px;
  padding-left: 0;
  position: relative;
  z-index: 2;
}
.xmJzxPage .bz-desc .bz-tip{
  font-size: 12px;
  color: #606266;
  line-height: 22px;
}
.tooltip-xmwrap {
  line-height: 20px;
  white-space: pre-wrap;
}
.xmJzxPage .bz-desc .bz-dItem{
  font-size: 14px;
  color: #303133;
  line-height: 22px;
  width: fit-content;
}
.xmJzxPage .bz-desc .bz-dItem.act{
  color: #1885F2;
}
.xmJzxPage .gray-item label+label, .xmJzxPage .w-block-pd label+label,.xmJzxPage .nor-lb label+label{
  margin-left: 24px;
}
.xmJzxPage .w-block-pd label {line-height: 22px;}
.xmJzxPage .w-block-pd label input,.xmJzxPage .gray-item label input,.xmJzxPage .nor-lb label input{
  min-height: unset;
}
.xmJzxPage .mb-hd{
  background: #F5F7FA;
  border-top: 1px solid #C8D7E6;
  border-bottom: 1px solid #C8D7E6;
  height: 40px;
  display: flex;
  align-items: center;
  color: #303133;
  margin-top: 12px;
}
.xmJzxPage .mb-hd .mb-hd-i{
  padding: 0 12px;
  height: 100%;
  line-height: 40px;
  border-right: 1px solid #C8D7E6;
}
.xmJzxPage .mb-hd .mb-hd-i{
  cursor: pointer;
}
.xmJzxPage .mb-hd .mb-hd-i:hover{
  opacity: 0.7;
}
.xmJzxPage .mb-hd .mb-hd-i.act{
  background: #fff;
  color: #1885F2;
  font-weight: bold;
  position: relative;
}
.xmJzxPage .mb-hd .mb-hd-i.act::after{
  position: absolute;
  content: '';
  bottom: -1px;
  height: 2px;
  width: 100%;
  left: 0;
  background: #fff;
}
.xmJzxPage .hight-block{color:#000}
.xmJzxPage .mb-detail{padding: 8px 12px 12px 0}
.xmJzxPage .w-block-pd.h-b{border: none;}
.xmJzxPage .w-block-pd.h-b+.h-b{border-top: 1px solid #C8D7E6;}

/* 特殊情况 */
.xmJzxPage .xm-btm{
  width: 100%;
  background: #F5F7FA;
  border-top: 1px solid #DCDFE6;
  padding: 8px 0;
}
.xmJzxPage .btm-lb {
  width: 110px;
  text-align: right;
  font-size: 18px;
  font-weight: bold;
  padding-right: 27px;
}
.xmJzxPage .xm-btm .btm-con{
  width: 960px;
  margin: 0 auto;
}
.xmJzxPage .xm-r-img{
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}
.xmJzxPage .xm-r-img .r-img-item {
  width: 200px;
  height: 140px;
  margin-right: 8px;
  position: relative;
  cursor: pointer;
  background: #000;
  margin-bottom: 8px;
}
.xmJzxPage .xm-r-img .r-img-item:hover .del-wrap{
  display: block;
}
.xmJzxPage .xm-r-img .r-img-item img {
  width: 100%;
  height: 100%;
}
.xmJzxPage .xm-r-img .r-img-item .del-wrap {
  display: none;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: rgba(0,0,0,.6);
  color: #fff;
}
.xmJzxPage .p-item+.p-item{
  margin-top: 8px;
}
.xmJzxPage .lbj-area label{
  margin-left: 0!important;
  width: 80px!important;
  line-height: 28px;
}
.xmJzxPage .lbj-w130 label{
  margin-left: 0!important;
  width: 130px!important;
  line-height: 28px;
}
.xmJzxPage .t-content label{
  line-height: 28px;
}