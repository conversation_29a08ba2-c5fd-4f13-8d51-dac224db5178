<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>知情同意书</title>
  
  <link rel="stylesheet" href="../../utils/layerMsg/layerMsg.css">
  <script src="../../../configData.js"></script>
  <script src="../../plugins/jquery.min.js"></script>
  <script src="../../utils/layerMsg/layerMsg.js"></script>
  <script src="../../plugins/crypto-js.min.js"></script>
  <script src="../../utils/common/common.js"></script>
  <script src="../../plugins/barcode.js"></script>
  <script src="../../plugins/qrcode.min.js"></script>
  <script src="../../utils/structFun2.0.js"></script>
  <script src="../../utils/globalDefinedFun.js"></script>
  <script src="../../controls/api.js"></script>
  <script src="../../utils/rtDialog.js"></script>
  <script src="../agreement/agreement.js"></script>
</head>
<style>
  * {
    border: 0;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  html,body {
    width: 100%;
    height: 100%;
  }
  .layout-page {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 40px 12px 50px 12px;
    background: #F5F7FA;
  }
  .layout-page.pv {
    padding: 0;
  }
  .layout-page.pv .layout-header,
  .layout-page.pv .layout-footer {
    display: none;
  }
  .layout-header {
    height: 40px;
    line-height: 40px;
    background: #E8F3FF;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    font-size: 18px;
    color: #000;
    font-weight: 600;
    padding: 0 12px;
  }
  .layout-body {
    height: 100%;
    overflow: auto;
    padding: 12px 0;
  }
  .layout-footer {
    height: 50px;
    line-height: 50px;
    background: #E8F3FF;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    padding: 0 12px;
  }
  .layout-page .main-content {
    margin: 0 auto;
    width: 100%;
    height: 100%;
  }
  .layout-page.print {
    padding: 0;
    background: #FFF;
  }
  .layout-page.print .layout-header, 
  .layout-page.print .layout-footer {
    display: none;
  }
  .layout-page .sr-btn {
    padding: 0 8px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    border-radius: 3px;
    background: #1885F2;
    color: #fff;
    font-size: 16px;
    margin-right: 16px;
    cursor: pointer;
    display: none;
    width: 100%;
  }
  .layout-page .sr-btn:not([disabled]):hover {
    opacity: 0.7;
    filter: Alpha(opacity=70);
  }
  .layout-page .sr-btn img {
    vertical-align: middle;
  }
  .layout-page .sr-btn[disabled] {
    color: #FFF;
  }
  .layout-page .sr-btn.green {
    background: #1E9493;
  }
  .layout-page .sr-btn.default {
    background: #fff;
    border: 1px solid #dcdfe6;
    color: #333;
  }
  .layout-page.print-status {
    padding: 0;
    background: #fff;
  }
  .layout-page.print-status .layout-header,
  .layout-page.print-status .layout-footer {
    display: none;
  }
  .layout-page.print-status .layout-body {
    padding: 0;
    overflow: visible;
  }
  .layout-page.print-status .t-pg {
    border: none;
    padding-bottom: 0;
    padding-top: 30px;
  }
  .layout-page.print-status .t-pg li {
    height: auto !important;
  }
  .writeByHand {
    display: none;
  }
  .r-wrap .ck-w {
    display: inline-block;
    width: 100%;
    position: relative;
    padding-left: 14px;
  }
  .r-wrap input[type="checkbox"] {
    width: 14px;
    height: 14px;
    display: inline-block;
    text-align: center;
  }
  .r-wrap input[type="checkbox"] {
    position: absolute;
    top: 3px;
    left: 0;
  }
  .r-wrap input[type="checkbox"] + .t-ck {
    content: "";
    position: absolute;
    top: 3px;
    left: 0;
    background: #fff;
    width: 14px;
    height: 14px;
    border: 1px solid #666;
    border-radius: 2px;
    box-sizing: border-box;
  }
  .r-wrap input[type="checkbox"]:disabled  + .t-ck {
    background: #eee;
    border-color: #999;
  }

  .r-wrap input[type="checkbox"]:checked + .t-ck {
    background: url('./check-icon.png');
    background-color: #1885F2;
    background-size: contain;
    position: absolute;
    border: 1px solid #1885F2;
    border-radius: 2px;
    box-sizing: border-box;
  }
  .r-wrap input[type="checkbox"]:checked ~ .ck-txt {
    color:#1885F2;
  }
  @page {
    margin: 0;
  }
  @media print {
  body{
    -webkit-print-color-adjust:exact;
    -moz-print-color-adjust:exact;
    -ms-print-color-adjust:exact;
    print-color-adjust:exact;
  } 
}
</style>
<body>
  <div class="layout-page" id="agreement">
    <div class="layout-header">
      <div class="ly-c"></div>
    </div>
    <div class="layout-body">
      <!-- 存放模板区域 -->
      <div class="main-content">
      </div>
    </div>
    <div class="layout-footer">
      <!-- <button class="sr-btn" id="tempBtn" onclick="openTemplateHandler()">
        <img src="../assets/images/temp-icon.png" alt="">
        模板
      </button> -->
      <!-- <button class="sr-btn" id="editBtn" onclick="editApply(this)">
        <img src="../assets/images/edit-icon.png" alt="">
        编写
      </button> -->
      <!-- <button class="sr-btn" id="printBtn" onclick="printApply()">
        <img src="../assets/images/print-icon.png" alt="">
        打印
      </button> -->
      <!-- <button class="sr-btn" id="saveBtn" onclick="saveApplyResult(this)">
        <img src="../assets/images/save-icon.png" alt="">
        提交
      </button> -->
    </div>
  </div>
</body>
</html>