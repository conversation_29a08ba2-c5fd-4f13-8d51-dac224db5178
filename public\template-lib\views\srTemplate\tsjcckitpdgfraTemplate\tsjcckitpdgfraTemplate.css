#tsjcckitpdgfra1 {
  font-size: 16px;
  background: #F5F7FA;
}
#tsjcckitpdgfra1 .ckitpdgfra-edit {
  padding: 16px 12px;
}
.w776-h62{
  width: calc(100% - 86px);
  height: 62px;
}
.w776{
  width: calc(100% - 86px);
}
.egfr-title{
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 28px;
  margin-bottom: 12px;
}
.egfr-msg{
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  line-height: 24px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.egfr-form{
  margin-bottom: 8px;
}
.line{
  width: 100%;
  height: 1px;
  background: #C0C4CC;
  margin-bottom: 8px;
}
.form-item{
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
#tsjcckitpdgfra1 .form-item span{
  display: inline-block;
  min-width: 96px;
  text-align: right;
  font-size: 16px;
}
#tsjcckitpdgfra1 .p-item + .p-item{
  margin-top: 8px;
}
.w776-h80{
  width: calc(100% - 78px);
  height: 80px;
}
.wid100-86{
  width: calc(100% - 78px);
}
#tsjcckitpdgfra1 .row-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#tsjcckitpdgfra1 .row-item .w-con{
  display: inline-block;
  vertical-align: unset;
}
.w776-h84{
  width: calc(100% - 84px);
  height: 84px;
  background-color: #fff;
  padding: 9px 12px;
  font-size: 16px;
  line-height: 22px;
}
.w776-h172{
  width: calc(100% - 84px);
  height: 172px;
  background-color: #fff;
  padding: 9px 12px;
  font-size: 16px;
  line-height: 22px;
}
.ml-3{
  margin-left: 3px;
}
.layui-inline input{
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
}
.showInt {
  position: relative;
  background: #fff;
}
.showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
.table-content{
  width: calc(100% - 86px);
  /* height: 469px; */
  border: 1px solid #C0C4CC;
  background: #fff;
  display: flex;
}
.table-left{
  width: 240px;
  border-right: 1px solid #C0C4CC;
}
.left-title{
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid #C0C4CC;
}
.right-item{
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid #C0C4CC;
  padding-left: 12px;
  padding-right: 10px;
}
.table-right{
  flex: 1;
}
.table-title{
  height: 36px;
  line-height: 36px;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  border-bottom: 1px solid #C0C4CC;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .blh-tit{
  text-align: right;
}
#tsjcckitpdgfra1 .ckitpdgfra-view {
  display: none;
}
[isview="true"] #tsjcckitpdgfra1 .ckitpdgfra-edit {
  display: none;
}
[isview="true"] #tsjcckitpdgfra1 .ckitpdgfra-view {
  display: block;
}

#tsjcckitpdgfra1 .ckitpdgfra-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 20px 56px 20px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .view-project{
  padding-top: 8px;
  line-height: 21px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 12px;
  text-align: center;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .page-tit{
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 8px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .view-result {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .gray-txt .bold{
  font-weight: 600;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .black-txt {
  color: #000;
  font-size: 14px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .bold {
  font-weight: bold;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .info-i + .info-i {
  padding-left: 4px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .report-wrap {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#tsjcckitpdgfra1 .ckitpdgfra-view .reporter-i [data-key]{
  flex: 1;
}
#tsjcckitpdgfra1 [data-img] {
  width: 100px;
  height: 48px;
  object-fit: contain;
}

/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #tsjcckitpdgfra1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #tsjcckitpdgfra1 .ckitpdgfra-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #tsjcckitpdgfra1 .ckitpdgfra-view .view-head,
[entry-type="5"] #tsjcckitpdgfra1 .ckitpdgfra-view .view-patient,
[entry-type="5"] #tsjcckitpdgfra1 .ckitpdgfra-view .tip-wrap {
  display: none;
}
[entry-type="5"] #tsjcckitpdgfra1 .ckitpdgfra-view div {
  border-bottom: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}