#fsg1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}

#fsg1 .fsg-content {
  min-height: 100%;
  padding: 8px 12px;
  padding-bottom: 0;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}

#fsg1 .c-item {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

#fsg1 .bro-w {
  display: flex;
  align-items: center;
  border: 1px solid #C8D7E6;
  padding: 4px 10px;
  background: #EBEEF5;
}

#fsg1 input[type="text"] {
  border: 1px solid #DCDFE6;
  width: 60px;
  padding-left: 6px;
  margin: 0 6px;
}

#fsg1 .cdfi-title {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  width: 92px;
  background: #EBEEF5;
  border-right: 1px solid #C8D7E6;
}

#fsg1 .cdfi-title .w-con {
  width: 100%;
  padding-left: 12px;
}

#fsg1 .cdfi-title .w-con:hover {
  background-color: #E4E7ED;
}

#fsg1 .cdfi-content {
  padding: 8px 12px;
}

#fsg1 .bro-n{
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 8px;
}

#fsg1 input[type="radio"], #fsg1 input[type="checkbox"] {
  vertical-align: middle;
}

#fsg1 .to-cd {
  cursor: pointer;
}

#fsg1 label+label {
  margin-left: 30px;
}

#fsg1 .c-item+.c-item {
  margin-top: 12px;
}
#fsg1 .ml-30{
  margin-left: 30px;
}