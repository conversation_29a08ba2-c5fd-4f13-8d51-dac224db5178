/* ----字体---- */
.fs-14 {
  font-size: 14px;
}
/* ----边距---- */
.mt-8 {
  margin-top: 8px;
}
.mb-2 {
  margin-bottom: 2px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-2 {
  margin-left: 2px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-6 {
  margin-left: 6px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-34 {
  margin-left: 34px;
}
/* ----宽度---- */
.wd-75 {
  width: 75px!important;
}
.wd-98 {
  width: 98px!important;
}
.wd-103 {
  width: 103px!important;
}
.wd-147 {
  width: 147px!important;
}
.wd-154 {
  width: 154px!important;
}
/* ----mrpdjpg1页面---- */
#mrpdjpg1 {
  width: 100%;
  height: 100%;
  position: relative;
  font-size: 14px;
  margin: 0 auto;
  padding-top: 40px;
}
#mrpdjpg1 .pdjpg-h {
  height: 40px;
  line-height: 40px;
  background: #E8F3FF;
  font-size: 18px;
  font-weight: bold;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: 1px solid #DCDFE6;
}
#mrpdjpg1 input[type="radio"], #mrpdjpg1 input[type="checkbox"] {
  vertical-align: middle;
}
/* 编辑页面 */
#mrpdjpg1 .pdjpg-edit {
  position: relative;
  height: 100%;
  background: #fff;
}
#mrpdjpg1 .pdjpg-edit .rpt-con {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
#mrpdjpg1 .pdjpg-b {
  flex: 1;
  overflow: auto;
}
#mrpdjpg1 .pdjpg-f {
  background: #E8F3FF;
  border-top: 1px solid #DCDFE6;
  padding: 6px 12px 8px 48px;
  color: #000000;
  line-height: 22px;
}
#mrpdjpg1 .w-auto {
  width: 960px;
  margin: 0 auto;
}
#mrpdjpg1 .row-tit {
  width: 98px;
  padding-right: 0;
}
#mrpdjpg1 .con-title {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  line-height: 28px;
  margin-bottom: 8px;
}
#mrpdjpg1 .con-b {
  border-right: 1px solid #DCDFE6;
  border-left: 1px solid #DCDFE6;
  padding: 12px;
}
#mrpdjpg1 .row-item {
  display: flex;
  margin-bottom: 8px;
}
#mrpdjpg1 .row-tit {
  width: 70px;
  text-align: right;
  line-height: 22px;
  padding-right: 14px;
}
#mrpdjpg1 .row-box {
  flex: 1;
  padding: 11px 12px;
  box-sizing: border-box;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#mrpdjpg1 .box-item {
  display: flex;
  line-height: 28px;
}
#mrpdjpg1 .box-tit {
  text-align: right;
}
#mrpdjpg1 .inp-sty{
  width: 60px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 0 3px;
  margin-left: 4px;
}
#mrpdjpg1 .sub-item {
  flex: 1;
  border: 1px solid #C8D7E6;
}
#mrpdjpg1 .sub-left {
  width: 64px;
  padding: 6px 12px;
  background: #EBEEF5;
  border-right: 1px solid #C8D7E6;
}
#mrpdjpg1 .sub-right {
  flex: 1;
  padding: 6px 12px;
}
#mrpdjpg1 .lar-tit {
  display: inline-block;
  width: 210px;
  text-align: right;
}
#mrpdjpg1 .pdjpg-f {
  background: #E8F3FF;
  border-top: 1px solid #DCDFE6;
  padding: 8px 12px;
  color: #000000;
  line-height: 22px;
}
#mrpdjpg1 .pdjpg-f .imp-tx {
  width: 80px;
  text-align: right;
  font-size: 18px;
  font-weight: bold;
  padding-right: 12px;
}
#mrpdjpg1 .con-flex {
  display: flex;
}
#mrpdjpg1 .flex-item {
  flex: 1;
}
[isview="true"] .pdjpg-edit {
  display: none!important;
}
[isview="true"] .pdjpg-view {
  display: block!important;
}
/* 预览页面 */
#mrpdjpg1 .pdjpg-view {
  display: none;
  height: 100%;
  overflow: auto;
  background: #F5F7FA;
  padding: 12px 0;
  color: #000;
}
#mrpdjpg1 .pdjpg-view .pdf-con {
  width: 780px;
  min-height: 1100px;
  background: #FFF;
  margin: 0 auto;
  border: 1px solid #E6E6E6;
  display: flex;
  flex-direction: column;
}
#mrpdjpg1 .pdjpg-view .pdf-top {
  flex: 1;
  padding: 36px 52px 16px 58px;
  display: flex;
  flex-direction: column;
}
#mrpdjpg1 .pdjpg-view .pdf-top .page-name {
  font-size: 22px;
  font-weight: bold;
  text-align: center;
}
#mrpdjpg1 .pdjpg-view .pdf-top .patient-info {
  margin-top: 16px;
  margin-bottom: 12px;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  padding: 8px 0;
}
#mrpdjpg1 .pdjpg-view .pdf-top .flex-h {
  flex-wrap: wrap;
}
#mrpdjpg1 .pdjpg-view .pdf-top .pat-item {
  /* flex: 1; */
  display: flex;
  flex-wrap: wrap;
}
#mrpdjpg1 .pdjpg-view .pdf-top .pat-item + .pat-item {
  margin-left: 20px;
}
#mrpdjpg1 .pdjpg-view .pdf-top .item-wrap {
  display: flex;
  flex-wrap: wrap;
  margin-left: 5px;
}
#mrpdjpg1 .pdjpg-view .pdf-top .gray {
  color: #303133;
}
#mrpdjpg1 .pdjpg-view .pdf-top .flex-w {
  flex: 1;
  word-break: break-all;
}
#mrpdjpg1 .pdjpg-view .block-title {
  font-weight: bold;
  font-size: 16px;
  color: #333333;
}

#mrpdjpg1 .pdjpg-view .rpt-block {
  display: flex;
  flex: 1;
  margin-top: 12px;
}
#mrpdjpg1 .pdjpg-view .rpt-block:first-child {
  margin-top: 0;
}
#mrpdjpg1 .pdjpg-view .block-box {
  width: 326px;
  padding: 12px;
  border: 1px solid #979797;
}
#mrpdjpg1 .pdjpg-view .rpt-tit {
  width: 98px;
  color: #333333;
  text-align: right;
}
#mrpdjpg1 .pdjpg-view .rpt-block .flex-h {
  flex-wrap: wrap;
}
#mrpdjpg1 .pdjpg-view .rpt-desc {
  flex: 1;
  color: #303133;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  word-break: break-all;
  white-space: pre-line;
  line-height: 22px;
  margin-left: 8px;
  padding: 9px 11px;
}
#mrpdjpg1 .pdjpg-view .pdf-con .pdf-bottom {
  min-height: 40px;
  background: #E8F3FF;
  border-top: 1px solid #E6E6E6;
  padding: 12px 44px;
  display: flex;
  font-size: 14px;
  color: #000;
}
#mrpdjpg1 .pdjpg-view .pdf-con .pdf-bottom .bt-tit {
  font-weight: bold;
}
#mrpdjpg1 .pdjpg-view .pdf-con .pdf-bottom .bt-imp {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#mrpdjpg1 .clear-sty {
  padding: 0!important;
  border-radius: 0!important;
  border: none!important;
}