$(function() {
  window.initHtmlScript = initHtmlScript;
})
var specimenAndPathogenOptionsMap = {
  '#hbettct-rt-1':[
    {
      id:'1',
      title:'满意'
    },
    {
      id:'2',
      title:'需要重新采样'
    },
  ],
  '#hbettct-rt-2':[
    {
      id:'1',
      title:'细胞量：>40%'
    },
    {
      id:'2',
      title:'细胞量：<40%'
    },
  ],
  '#hbettct-rt-3':[
    {
      id:'1',
      title:'颈管细胞：无'
    },
    {
      id:'2',
      title:'颈管细胞：有'
    },
  ],
  '#hbettct-rt-4':[
    {
      id:'1',
      title:'化生细胞：无'
    },
    {
      id:'2',
      title:'化生细胞：有'
    },
  ],
  '#hbettct-rt-5':[
    {
      id:'1',
      title:'菌群转变：无'
    },
    {
      id:'2',
      title:'菌群转变：有'
    },
  ],
  '#hbettct-rt-6':[
    {
      id:'1',
      title:'滴虫感染提示：无'
    },
    {
      id:'2',
      title:'滴虫感染提示：有'
    },
  ],
  '#hbettct-rt-7':[
    {
      id:'1',
      title:'念珠菌感染提示：无'
    },
    {
      id:'2',
      title:'念珠菌感染提示：有'
    },
  ],
  '#hbettct-rt-8':[
    {
      id:'1',
      title:'HPV感染提示：无'
    },
    {
      id:'2',
      title:'HPV感染提示：有'
    },
  ],
  '#hbettct-rt-9':[
    {
      id:'1',
      title:'疱疹感染提示：无'
    },
    {
      id:'2',
      title:'疱疹感染提示：有'
    },
  ],
}
var degreeOptions = [
  {
    id:'1',
    title:'轻度'
  },
  {
    id:'2',
    title:'中度'
  },
  {
    id:'3',
    title:'重度'
  },
]
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: $('#hbettctbg1 .preview'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPreview()
      getDescription()
    } else {
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescription();
  rtStructure.impression = getImpressionStr();
  rtStructure.recommendation = getRecommendation();
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function initPage(){
  initSpecimenAndPathogen()
  initInpAndSel('#hbettct-rt-10',degreeOptions)
}
// 标本满意度 病原微生物
function initSpecimenAndPathogen(){
  let specimenElem = $('#hbettctbg1 .edit [data-value="specimen"]')
  let pathogenElem = $('#hbettctbg1 .edit [data-value="pathogen"]')
  let specimen = []
  let pathogen = []
  function updateSpecimenAndPathogen(){
    $(specimenElem).html('')
    $(pathogenElem).html('')
    specimen.forEach(item=>{
      let value = $(item).val()
      $(specimenElem).append(`<span>${value}</span>`)
     })
     pathogen.forEach(item=>{
       let value = $(item).val()
       $(pathogenElem).append(`<span>${value}</span>`)
     })
  }
  Object.entries(specimenAndPathogenOptionsMap).forEach(([key,value],index)=>{
    if(index<5){
      specimen.push(key)
    }else{
      pathogen.push(key)
    }
    $(key).on('input',updateSpecimenAndPathogen)
    initInpAndSel(key,value,updateSpecimenAndPathogen)
  })
  updateSpecimenAndPathogen()
}
function initInpAndSel(idList, optionList, callBack) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `${idList}`,
    data: optionList,
    click: function(obj) {
      this.elem.val(obj.title);
      callBack && callBack(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.Maxheight = '200px'
      }
    }
  });
}
// 预览页
function initPreview(){
  // 渲染报告医生
  curElem.find('.preview [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    }else {
      $(this).hide();
    }
  });
  curElem.find('.preview [data-key]').each(function(){
    var notHideNullValueList = ['hbettct-rt-11','hbettct-rt-12','hbettct-rt-13']
    var key = $(this).attr('data-key')
    var idAnVal,value;
    if(key==='report-img'){
      if(rptImageList && rptImageList.length){
        rptImageList = rptImageList.slice(0,2)
        let html = ''
        rptImageList.forEach(item=>
            html+=`<img src="${item.src}" alt=""">`
        )
        $(this).html(html)
      }else{
        $(this).hide()
      }
      return
    }
    let keyList = key.split(',')
    if(keyList.length>1){
      let temp = ''
      keyList.forEach(key=>{
        idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
        value = publicInfo[key] || idAnVal;
        if(value){
          temp+=`<span>${value}</span>`
        }
      })
      $(this).html(temp)
      addIdToNodeByView(this, keyList, idAndDomMap);
      return
    }
    this.style.whiteSpace = 'pre-line'
    // 兜底
    idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
    value = publicInfo[key] || idAnVal;
    if(value || notHideNullValueList.includes(key)){
      $(this).html(value)
    }else {
      $(this).parent().hide()
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
}
function getImpressionStr(){
  let arr = [
    // {
    //   label:'补充意见1：',
    //   idList:['hbettct-rt-12']
    // },
    {
      label:'',
      idList:['hbettct-rt-13']
    },
  ]
  let result = arr.map(item=>{
    let str = ''
    str+=item.label
    let list =  item.idList.map(id=>{
      let value = $('#'+id).val()
      if(value){
        return value
      }
    }).filter(Boolean)
    if(list.length>0){
      str += list.join(' ') + '；'
      return str
    }
  }).filter(Boolean)
  return result.join('\n')
  // let keyObj = {
  //   '#hbettct-rt-11':'TBS诊断：',
  // }
  // let result = []
  // Object.entries(keyObj).forEach(([key,value],)=>{
  //   let str =  $(key).val()
  //   if(str){
  //     result.push(`${value}${str}`)
  //   }
  // })
  // return result.join('\n')
}
function getDescription(){
  let arr = [
    // {
    //   label:'标本满意度：',
    //   idList:['hbettct-rt-1','hbettct-rt-2','hbettct-rt-3','hbettct-rt-4','hbettct-rt-5']
    // },
    // {
    //   label:'病原微生物：',
    //   idList:['hbettct-rt-6','hbettct-rt-7','hbettct-rt-8','hbettct-rt-9']
    // },
    // {
    //   label:'炎症程度：',
    //   idList:['hbettct-rt-10']
    // }
    {
      label:'',
      idList:['hbettct-rt-11']
    }
  ]
  let result = arr.map(item=>{
    let str = ''
    str+=item.label
    let list =  item.idList.map(id=>{
      let value = $('#'+id).val()
      if(value){
        return value
      }
    }).filter(Boolean)
    if(list.length>0){
      str += list.join(' ') + '；'
      return str
    }
  }).filter(Boolean)
  return result.join('\n')
}
function getRecommendation(){
  let arr = [
    {
      label:'',
      idList:['hbettct-rt-12']
    },
    // {
    //   label:'补充意见2：',
    //   idList:['hbettct-rt-13']
    // },
   
  ]
  let result = arr.map(item=>{
    let str = ''
    str+=item.label
    let list =  item.idList.map(id=>{
      let value = $('#'+id).val()
      if(value){
        return value
      }
    }).filter(Boolean)
    if(list.length>0){
      str += list.join(' ') + '；'
      return str
    }
  }).filter(Boolean)
  return result.join('\n')
}