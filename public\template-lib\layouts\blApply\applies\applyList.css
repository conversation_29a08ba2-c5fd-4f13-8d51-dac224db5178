html, body{
  height: 100%;
  width: 100%;
  background: #F0F2F5;
}
body {
  min-width: 1200px;
}
* {
  box-sizing: border-box;
}
.sqlb{
  padding:0 15px 15px;
  height: 100%;
  background: #fff;
  padding-bottom: 60px;
  position: relative;
}
.sqlb-header{
  width: 100%;
  position: relative;
}
.sq-s{
  font-size: 20px;
  font-weight: bold;
  color: #000000;
  line-height: 30px;
  margin-top: 15px;
}
.row-t{
  display: inline-block;
  margin-left: 10px;
  margin-top: 15px;
}
.concat-inp {
  display: inline-block !important; 
  position: absolute !important; 
  border-left: 0px !important; 
  width: 120px !important; 
  height: 36px !important;
  top:0;
  left:101px !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.select-l{
  width: 96px;
  height: 36px;
  border-radius: 3px 0px 0px 3px;
  padding: 0 0 0 10px;
  margin-left: 5px;
  border: 1px solid #DCDFE6;
}
select{
  width: 120px;
  height: 36px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 0 0 0 10px;
}

#btn{
  display: inline-block;
  padding: 0 5px;
  height: 36px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  line-height: 36px;
  cursor: pointer;
}
#btn:hover {
  opacity: 0.5;
}
img{
  width: 20px;
  height: 20px;
}
.row-h{
  display: inline-block;
}
.center .layui-table-cell {
  height: auto ;
  white-space: normal;
  word-wrap:break-word;
}
.row-b {
  position: absolute;
  bottom: 10px;
}
.print-table {
  border-collapse: collapse;
  display: none;
  width: 1070px;
  font-size: 20px;
  background: #fff;
}
.print-table td, .print-table th {
  padding: 5px 8px;
}
.print-table td, .print-table tr{
  page-break-inside: avoid;
  text-align: center;
}
.ope-txt {
  color: blue;
  cursor: pointer;
}
.reason-box {
  border: 1px solid #B3D8FF;
  padding: 12px;
  background: #ECF5FF;
  border-radius: 4px;
  margin-bottom: 8px;
}
