/*-------公共--------*/
.w-con {
  display: inline-block;
  vertical-align: middle;
}

/*-------边距--------*/
.mb-8 {
  margin-bottom: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-22 {
  margin-left: 22px;
}
.ml-26 {
  margin-left: 26px;
}
.ml-28 {
  margin-left: 28px;
}
.ml-32 {
  margin-left: 32px;
}
.ml-30 {
  margin-left: 30px;
}
.ml-38 {
  margin-left: 38px;
}
.ml-36 {
  margin-left: 36px;
}
.ml-46 {
  margin-left: 46px;
}
.ml-58 {
  margin-left: 58px;
}
.ml-65 {
  margin-left: 65px;
}
.ml-78 {
  margin-left: 78px;
}
.ml-170 {
  margin-left: 170px;
}

/*-------宽度--------*/
.wd-60 {
  width: 60px;
}

/*-------高度--------*/
.lh-28 {
  line-height: 28px;
}

/*-------总体布局--------*/
ul {
  overflow: auto;
}
#jgdm1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#jgdm1 .jgdm-content {
  min-height: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#jgdm1 .con-box {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#jgdm1 .box-row {
  padding: 8px 12px;
  box-sizing: border-box;
}
#jgdm1 .bor-w {
  border: 1px solid #C8D7E6;
  padding: 0 8px;
  background: #EBEEF5;
}
#jgdm1 .inp-sty {
  height: 28px;
  border: 1px solid #DCDFE6;
}
#jgdm1 [type="checkbox"],#jgdm1 [type="radio"] {
  vertical-align: middle;
}
#jgdm1 [type="text"] {
  padding: 0 2px;
}
#jgdm1 .row-title {
  display: inline-block;
  width: 130px;
  text-align: right;
}
#jgdm1 sup {
  font-weight: bold;
  font-family: fangsong;
}