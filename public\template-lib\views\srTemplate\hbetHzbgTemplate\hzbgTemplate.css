.singleDisEditReport.main-page{
  min-width: unset;
}
#hzbg1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#hzbg1 * {
  font-family: '宋体';
}
#hzbg1 .hzbg-edit{
  padding: 8px 12px;
}
#hzbg1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#hzbg1 .black-lb {
  color: #303133;
}
#hzbg1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#hzbg1 .blue-lb:hover {
  opacity: 0.8;
}
#hzbg1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#hzbg1 .text-size, .diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#hzbg1 .text-size img, .diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#hzbg1 .text-size img:hover, .diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#hzbg1 .text-size .on, .diag-text-size .text-size .on {
  display: none;
}
#hzbg1 .editor-area {
  padding: 4px 0;
  min-height: 100px;
}
.editor-area textarea {
  width: 100%;
  height: 100px;
  border: none;
  font-size: 16px;
}
#hzbg1 .p-item + .p-item {
  margin-top: 12px;
}
.editor-area.default textarea{
  font-size: 16px;
}
.editor-area.large textarea{
  font-size: 18px;
}
.editor-area.larger textarea{
  font-size: 20px;
}
#hzbg1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#hzbg1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hzbg1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#hzbg1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#hzbg1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#hzbg1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

*:disabled {
  background: #fff;
  color: unset;
}
textarea[readonly] {
  cursor: not-allowed;
}
#hzbg1 .hzbg-view {
  display: none;
}
[isview="true"] #hzbg1 .hzbg-edit {
  display: none;
}
[isview="true"] #hzbg1 .hzbg-view {
  display: flex;
}
#hzbg1 .hzbg-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 116px 56px;
  flex-direction: column;
  position: relative;
}
#hzbg1 .hzbg-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#hzbg1 .hzbg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}
#hzbg1 .hzbg-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
 span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
 }
}
#hzbg1 .hzbg-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
#hzbg1 .hzbg-view .hos-tit{
  font-family: '仿宋';
  font-size: 21px;
  line-height: 26px;
  font-style: normal;
  color: #000;
}
#hzbg1 .hzbg-view .sub-tit{
  font-size: 26px;
  color: #000;
  font-family: '宋体';
  margin-top: 20px;
  line-height: 30px;
}
#hzbg1 .hzbg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#hzbg1 .hzbg-view .hide-item {
  margin-bottom: 8px !important;
}
#hzbg1 .hzbg-view .hide-item .gray-txt {
  text-indent: 0 !important;
  padding-left: 28px;
  line-height: 32px;
}
#hzbg1 .hzbg-view .gray-txt {
  color: #000;
  font-size: 16px;
  font-family: '宋体';
  white-space: pre-line;
  word-break: break-all;
}
.flex-column {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  margin-top: 10px;
}
#hzbg1 .hzbg-view .report-img img {
  height: 240px;
  width: 320px;
  display: block;
  object-fit: contain;
}
#hzbg1 .hzbg-view .black-txt {
  color: #000;
  font-size: 16px;
  font-family: '宋体';
}
#hzbg1 .hzbg-view .red-txt {
  color: #000000;
  font-size: 16px;
}
#hzbg1 .hzbg-view .bold {
  font-weight: bold;
}
#hzbg1 .hzbg-view .info-i {
  width: 160px;
  display: flex;
  flex-wrap: wrap;
}
#hzbg1 .hzbg-view .info-i + .info-i {
  margin-left: 8px;
}
#hzbg1 .hzbg-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#hzbg1 .hzbg-view .view-patient .p-item {
  margin-top: 10px;
}
#hzbg1 .hzbg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#hzbg1 .hzbg-view .rt-sr-body {
  padding-top: 8px;
}
#hzbg1 .hzbg-view .desc-con {
  /* padding: 8px 0; */
  display: none;
  margin-bottom: 20px;
}
#hzbg1 .hzbg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#hzbg1 .hzbg-view .desc-con {
  display: flex;
  align-items: baseline;
}
#hzbg1 .hzbg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hzbg1 .hzbg-view .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#hzbg1 .hzbg-view .reporter-i.w120 {
  width: 120px;
}
#hzbg1 .hzbg-view .reporter-i.w150 {
  width: 150px;
}
#hzbg1 .hzbg-view .reporter-i span {
  width: 70px;
}
#hzbg1 .hzbg-view .reporter-i span:last-child {
  flex: 1;
}
#hzbg1 .hzbg-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#hzbg1 .hzbg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#hzbg1 .hzbg-view .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}
#hzbg1 .hzbg-view .tip-wrap .tip-text {
  flex: 1;
}
#hzbg1 .hzbg-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  margin-bottom: 12px;
  display: none;
  padding-left: 86px;
}
#hzbg1 .hzbg-view .item-img {
  width: 240px;
  height: 180px;
  border: 1px solid #eee;
  margin-bottom: 8px;
}
#hzbg1 .hzbg-view .item-img:nth-child(odd){
  margin-right: 12px;
}
#hzbg1 .hzbg-view .item-img:nth-child(even){
  margin-left: 12px;
}
#hzbg1 .hzbg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#hzbg1 .p-item:nth-child(2) .text-size,#hzbg1 .p-item:nth-child(3) .text-size,#hzbg1 .p-item:nth-child(4) .text-size {
  display: none;
}
#hzbg1 .p-item:nth-child(2) .editor-area,#hzbg1 .p-item:nth-child(3) .editor-area {
  min-height: 72px;
}
#hzbg1 .p-item:nth-child(2) textarea,#hzbg1 .p-item:nth-child(3) textarea {
  height: 72px;
}
#hzbg1 .hzbg-view .blh-tit{
  text-align: right;
}
[data-key="hzbg-rt-2,sampleSeen"] {
  min-height: 100px;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #hzbg1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #hzbg1 .hzbg-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #hzbg1 .hzbg-view .view-head,
[entry-type="5"] #hzbg1 .hzbg-view .view-patient,
[entry-type="5"] #hzbg1 .hzbg-view .tip-wrap {
  display: none;
}
[entry-type="5"] #hzbg1 .hzbg-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #hzbg1 .hzbg-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
