#cjhj1 {
  font-size: 14px;
}
/*--------宽度类---------*/
.wd-70 {
  width: 70px;
}
.wd-80 {
  width: 80px;
}
.wd-86 {
  width: 86px;
}
.wd-148 {
  width: 148px;
}
.wd-174 {
  width: 174px;
}
.wd-200 {
  width: 200px;
}
/*--------内间距类---------*/
.con-pad {
  padding: 11px 12px;
}
/*--------边框类---------*/
.sel-bor {
  border-radius: 3px;
  border: 1px solid #DCDFE6;
}
.cjhj-content:not(:last-child)::after {
  width: 960px;
  height: 1px;
  content: '';
  background: #C8D7E6;
  position: absolute;
  left: 0;
}
.flex-con {
  display: flex;
}
.flex-sty {
  flex: 1;
}
.label-sty {
  display: inline-block;
  text-align: right;
  vertical-align: sub;
}
.row-box {
  position: relative;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
.ver-sub {
  vertical-align: sub;
}
.area-sty {
  height: 80px;
  resize: none;
}
.box-line::after {
  width: 1px;
  height: 107px;
  content: '';
  background: #C8D7E6;
  position: absolute;
  left: 82px;
  top: 0;
}
.rybox-line::after {
  width: 1px;
  height: 162px;
  content: '';
  background: #C8D7E6;
  position: absolute;
  left: 153px;
  top: 0;
}
.cjhjPage {
  position: relative;
  padding-bottom: 116px;
}
.cjhjBody {
  padding: 8px 20px;
  border: 1px solid #DCDFE6;
}
.cjhjFooter {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 116px;
  text-align: left;
  background: #E8F3FF;
  box-shadow: inset 1px 0px 0px 0px #C8D7E6, inset -1px 1px 0px 0px #C8D7E6;
  z-index: 11;
}
.footer-box {
  width: 960px;
  margin: auto;
  padding: 11px 12px;
}
.footer2 {
  display: flex;
  justify-content: space-between;
  line-height: 28px;
}
.organ-sel,.maxdiam-sel {
  height: 28px;
  padding: 0 16px;
}
.show-or-hide {
  position: relative;
  background: #fff;
}
.show-or-hide input {
  padding: 0 16px;
  position: relative;
  z-index: 10;
  background: transparent;
}
.show-or-hide:hover {
  opacity: .8;
}
.show-or-hide::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}
.show-or-hide.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}
.show-or-hide.hide-more:after {
  transform: rotate(-45deg);
  margin-top: -2px;
}
.laySelLab li {
  height: 26px;
}