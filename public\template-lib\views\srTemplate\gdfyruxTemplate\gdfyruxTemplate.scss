#gdfyrux1 {
  box-sizing: border-box;
  background-color: #F0F2F5;
  input[type=checkbox], input[type=radio] {
    min-height: revert;
  }
  input[type=text] {
    width: 60px;
    border: 1px solid #C0C4CC;
    padding: 0 4px;
    border-radius: 3px;
  }
  label {
    margin-left: 8px;
    span {
      vertical-align: top;
    }
  }
  .large-text {
    font-weight: 600;
    font-size: 16px;
  }
  .clr-blu {
    color: #1885F2;
  }
  .clr-red {
    color: #E64545;
  }
  .mt-8 {
    margin-top: 8px;
  }
  .ml-6 {
    margin-left: 6px;
  }
  .ml-0 {
    margin-left: 0;
  }
  .hide {
    display: none;
  }
  .top-bar {
    height: 40px;
    border-bottom: 1px solid #C8D7E6;
    background: #E8F3FF;
    .bar-inner {
      height: 100%;
      width: 980px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      span {
        margin-right: 12px;
        font-weight: bold;
        font-size: 18px;
        color: #000000;
      }
    }
  }
  .body-wrap {
    width: 980px;
    margin: 10px auto 0;
    border: 1px solid #C8D7E6;
    background: white;
    .body-section {
      & + .body-section {
        border-top: 1px solid #C8D7E6;
      }
    }
    .side-section {
      display: flex;
      .side-cont {
        flex: 1;
        & + .side-cont {
          border-left: 1px solid #C8D7E6;
        }
      }
    }
    .form-item {
      display: flex;
      line-height: 28px;
      .form-label {
        min-width: 80px;
        padding-left: 8px;
        padding-right: 2px;
        text-align: right;
        background-color: #E8F3FF;
      }
      .form-content {
        flex: 1;
        border-left: 1px solid #C8D7E6;
      }
      .divider {
        width: 100%;
        flex-shrink: 0;
        flex-grow: 0;
        border-bottom: 1px solid #C8D7E6;
      }
    }
  }
  .footer-wrap {
    margin-top: 10px;
    background: #E8F3FF;
    border-top: 1px solid #C8D7E6;
    border-bottom: 1px solid #C8D7E6;
    .footer-inner {
      width: 980px;
      margin: 0 auto;
      padding: 8px 0;
      display: flex;
    }
    .result-label {
      padding: 8px 8px 8px 0;
      display: flex;
      flex-direction: column;
    }
    .result-wrap {
      flex: 1;
      display: flex;
      border: 1px solid #C8D7E6;
      background-color: #fff;
    }
    .result-info {
      border-left: 1px solid #C8D7E6;
      padding-left: 12px;
      display: flex;
      align-items: center;
    }
    .large-label {
      margin-right: 8px;
      font-size: 18px;
      font-weight: bold;
    }
    .result-level {
      font-size: 16px;
    }
  }
}