/*-----------------字体大小、行高-----------------*/
.fs-14 {
  font-size: 14px;
  line-height: 14px;
}

.fs-18 {
  font-size: 18px;
  line-height: 18px;
}

.fs-22 {
  font-size: 22px;
  line-height: 22px;
}

/*-------------------字体加粗---------------------*/
.fw-600 {
  font-weight: 600;
}

/*---------------------颜色类-----------------------*/
.f-lightgray {
  color: #606266;
}

.f-black {
  color: #000;
}

/*---------------------外边距-----------------------*/
.mb-12 {
  margin-bottom: 12px;
}

.pt-4 {
  padding-top: 4px;
}

/*---------------------边框线-----------------------*/
.bottom-line {
  border-bottom: 1px solid #999;
  padding: 4px 0;
  box-sizing: content-box;
}

/*-------浮动--------*/
.fl {
  float: left;
  line-height: 20px;
  width: 25%;
  margin: 0!important;
}

.fl.w10 {
  width: 10%;
}

.fl.w14 {
  width: 14%;
}

.fl.w18 {
  width: 18%;
}

.fl.w35 {
  width: 35%;
}

.fl.w32 {
  width: 32%;
}

.fl.w50 {
  width: 50%;
}

.fl.w100 {
  width: 100%;
}

.fl .w120 {
  width: 120px;
}

.fl .w-con label input {
  vertical-align: top;
}

/*-----------------位置类-----------------*/
.t-l {
  text-align: left;
}

/*-------宽度根据内容撑开----------*/
.w-i {
  width: inherit;
  padding-top: 0;
}

/*------按钮块------*/
.button {
  display: inline-block;
  width: inherit;
  height: 28px;
  padding-right: 8px;
  padding-left: 8px;
  background-color: #1885F2;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;
}

.button:hover {
  background-color: #60a7ee;
}

/*清浮动系列*/
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/*--------cd检查申请单---------*/
.cd-page {
  width: 780px;
  margin: 0 auto;
  padding: 40px 60px 0;
  display: block;
  border: 1px solid #E6E6E6;
  padding-bottom: 27px;
  background: #fff;
}

/*--------cd检查申请单头部---------*/
.cd-header {
  position: relative;
  text-align: center;
  border-bottom: 1px solid #999;
  padding-bottom: 20px;
}

/*--------cd检查项目---------*/
.options {
  display: inline-block;
  width: auto;
  height: 28px;
  margin-left: 4px;
  padding: 0 8px;
  background-color: #ECF5FF;
  color: #1885F2;
  border-radius: 3px;
  vertical-align: top;
  border: 1px solid #B3D8FF;
  margin-bottom: 2px;
  padding-top: 2px;
}

.options span {
  cursor: pointer;
}