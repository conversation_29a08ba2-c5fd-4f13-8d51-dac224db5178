#jybgajs1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
  color: #000;
  padding: 12px 24px;
}

.wid-160 {
  width: 130px;
}

.wid-198 {
  width: 198px;
}
.wid-180 {
  width: 180px;
}

.wid-200 {
  width: calc(100% - 500px);
}

.wid-200 input {
  width: calc(100% - 40px);
}

.wid-272 {
  width: 272px;
}

.wid-281 {
  width: 281px;
}
.wid-98 {
  width: 98px;
}
.wid-150 {
  width: 150px;
}
.wid-100 {
  width: 80px;
}

.mr-12 {
  margin-right: 12px;
}

.mr-24 {
  margin-right: 24px;
}
.mr-40 {
  margin-right: 40px;
}

.pl-4 {
  padding-left: 4px;
}

.pb-16 {
  padding-bottom: 16px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-16 {
  margin-bottom: 16px;
}

#jybgajs1 input[type="text"] {
  vertical-align: middle;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding-left: 12px;
}
#jybgajs1  .over-mark {
  width: 16px;
  flex-shrink: 0;
  flex-grow: 0;
  color: #000;
  align-items: center;
  margin-left: 20px;
}

#jybgajs1 .row-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

#jybgajs1 .row-item+.row-item {
  margin-top: 8px;
}

#jybgajs1 .p-item+.p-item {
  margin-top: 6px;
}
#jybgajs1 .p-item{
  align-items: center;
}
/* #jybgajs1 .rt-sr-body .p-item+.p-item {
  margin-top: 10px;
} */

#jybgajs1 .tbl-title {
  font-weight: 600;
}

[isview="true"] #jybgajs1 .ajs-edit {
  display: none;
}
[isview="true"] #jybgajs1 {
  padding: 0;
}
[isview="true"] #jybgajs1 .ajs-view, [isview="true"] #jybgajs1 .view-container {
  display: block;
}

#jybgajs1 .ajs-view * {
  font-family: '宋体';
}

#jybgajs1 .ajs-view {
  display: none;
  position: relative;
  font-size: 16px;
  background: #fff;
  padding: 30px 24px 34px 22px;
  color: #000;
}

#jybgajs1 .ajs-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 8px;
  text-align: center;
}

#jybgajs1 .ajs-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}

#jybgajs1 .ajs-view .hos-tit {
  font-size: 21px;
  margin-bottom: 20px;
  text-align: center;
}

#jybgajs1 .ajs-view .hos-tit * {
  font-family: '仿宋' !important;
}

#jybgajs1 .ajs-view .sub-tit {
  font-size: 26px;
  line-height: 35px;
  text-align: center;
}

#jybgajs1 .ajs-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
}

#jybgajs1 .ajs-view .logo-tit .code img {
  width: 122px;
  height: 36px;
}

#jybgajs1 .ajs-view .logo-tit .code span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
}

#jybgajs1 .ajs-view .blh-tit {
  font-size: 16px;
  line-height: 23px;
  text-align: right;
}

#jybgajs1 .ajs-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#jybgajs1 .ajs-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}

#jybgajs1 .ajs-view .rt-sr-footer {
  position: absolute;
  bottom: 36px;
  left: 56px;
  right: 56px;
}

#jybgajs1 .ajs-view .report-wrap {
  margin-top: 12px;
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#jybgajs1 .ajs-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
}

#jybgajs1 .ajs-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
}

#jybgajs1 .ajs-view .reporter-i [data-key] {
  flex: 1;
}

#jybgajs1 .ajs-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}

#jybgajs1 .ajs-view .reporter-i+.reporter-i {
  margin-left: 8px;
}

#jybgajs1 .view-container {
  display: none;
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
}

#jybgajs1 .ajs-view .view-title {
  font-size: 20px;
}

#jybgajs1 .ajs-view .view-table th {
  font-size: 14px;
  text-align: left;
}

#jybgajs1 .ajs-view .view-table td {
  font-size: 12px;
}

#jybgajs1 .ajs-view .view-table th, #jybgajs1 .ajs-view .view-table td {
  padding: 0 8px;
  line-height: 18px;
}

#jybgajs1 .view-list {
  display: flex;
  width: 100%;
  border: 1px solid #303133;
  box-sizing: border-box;
  line-height: 25px;
}

#jybgajs1 .ajs-view .view-table.advice th {
  font-size: 16px;
}

#jybgajs1 .ajs-view .view-table.advice td {
  font-size: 14px;
}