/* ----边距---- */
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-24 {
  margin-left: 24px;
}
.ml-28 {
  margin-left: 28px;
}
.ml-40 {
  margin-left: 40px;
}
.ml-50 {
  margin-left: 50px;
}
.ml-66 {
  margin-left: 66px;
}
.ml-70 {
  margin-left: 70px;
}
.con-pad {
  padding: 11px 12px;
}
.box-pad {
  padding: 8px 12px;
}
/* 宽度类 */
.wd-50 {
  display: inline-block;
  width: 50px!important;
}
.wd-68 {
  display: inline-block;
  width: 68px!important;
  line-height: 28px;
}
.wd-78 {
  display: inline-block;
  width: 78px!important;
}
.wd-90 {
  display: inline-block;
  width: 90px!important;
}
.wd-136 {
  display: inline-block;
  width: 136px!important;
}
.wd-200 {
  display: inline-block;
  width: 200px!important;
}
/* 高度类 */
.lh-28 {
  line-height: 28px;
}
/* 颜色类 */
.bg-gray {
  background: #F5F7FA!important;
}
.gray-light {
  background: #EBEEF5!important;
}
#gxtcsgcyc1 {
  width: 960px;
  height: 100%;
  font-size: 14px;
  margin: 0 auto;
  border: 1px solid #C8D7E6;
}
#gxtcsgcyc1 .gxtcsgcyc-h {
  background: #EBEEF5;
  height: 44px;
  padding: 8px 12px;
  border-bottom: 1px solid #C8D7E6;
}
#gxtcsgcyc1 .menu-group {
  display: flex;
  height: 28px;
  line-height: 28px;
}
#gxtcsgcyc1 .menu-item {
  padding: 0 15px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  background: #fff;
  border: 1px solid #DCDFE6;
  line-height: 26px;
}
#gxtcsgcyc1 .menu-item + .menu-item {
  border-left: none;
}
#gxtcsgcyc1 .menu-item:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
#gxtcsgcyc1 .menu-item:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
#gxtcsgcyc1 .menu-item.act {
  border-color: #1885F2;
  background: #1885F2;
  color: #FFF;
}
#gxtcsgcyc1 .menu-item:hover {
  opacity: 0.7;
}
#gxtcsgcyc1 .gxtcsgcyc-b {
  height: calc(100% - 44px);
  overflow: auto;
}
#gxtcsgcyc1 .menu-con {
  display: none;
}
#gxtcsgcyc1 .menu-con {
  height: 100%;
  padding: 8px 12px;
}
#gxtcsgcyc1 .menu-con.act {
  display: block;
}
#gxtcsgcyc1 .con-box {
  box-sizing: border-box;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#gxtcsgcyc1 .box-item {
  border: 1px solid #C8D7E6;
  background: #EBEEF5;
}
#gxtcsgcyc1 .box-title {
  font-size: 16px;
  color: #000000;
  font-weight: bold;
}
#gxtcsgcyc1 .flex-sty {
  display: flex;
  justify-content: space-between;
}
#gxtcsgcyc1 .flex-item {
  flex: 1;
}
#gxtcsgcyc1 input[type="radio"], #gxtcsgcyc1 input[type="checkbox"]{
  vertical-align: middle;
}
#gxtcsgcyc1 .inp-sty {
  width: 60px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 0 3px;
}
#gxtcsgcyc1 .fill-lab label {
  display: inline-block;
  width: 100%;
}

/* 含有超声描述、超声提示 */
#gxtcsgcyc1 .menu-sub-group {
  display: flex;
  overflow: hidden;
  height: 44px;
}
#gxtcsgcyc1 .menu-sub-item {
  position: relative;
  height: 32px;
  line-height: 22px;
  padding: 5px 20px;
  box-sizing: border-box;
  cursor: pointer;
  color: #606266;
}
#gxtcsgcyc1 .menu-sub-item.act {
  font-weight: bold;
  color: #1885F2;
  border-bottom: 2px solid #1885F2;
}
#gxtcsgcyc1 .menu-sub-box {
  height: calc(100% - 44px);
  overflow: auto;
}
#gxtcsgcyc1 .menu-sub-con {
  display: none;
}
#gxtcsgcyc1 .menu-sub-con.act {
  display: block;
}
/* 胎儿颅脑室、侧脑室 */
#telcns-csms1 .box-left {
  width: 98px;
  border-right: 1px solid #C8D7E6;
  padding: 11px 12px;
  box-sizing: border-box;
}
#telcns-csts .box-item {
  display: inline-block;
  padding: 0 8px;
  border: 1px solid #C8D7E6;
  background: #EBEEF5;
  margin-left: 12px;
}
/* 胎儿腹部 */
#tefb .dxjx-item {
  display: inline-block;
  vertical-align: top;
  margin-top: 54px;
  margin-left: 40px;
}
