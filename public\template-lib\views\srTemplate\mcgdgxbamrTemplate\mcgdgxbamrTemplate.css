#mcgxbmr1 {
  font-size: 14px
}

#mcgxbmr1 * {
  box-sizing: border-box;
}

#mcgxbmr1 .sel-light {
  color: #1885F2;
  background: #ECF5FF;
}

#mcgxbmr1 .pl-10 {
  padding-left: 10px;
}

#mcgxbmr1 .pl-12 {
  padding-left: 12px;
}

#mcgxbmr1 .pl-42 {
  padding-left: 42px;
}

#mcgxbmr1 .mb-2 {
  margin-bottom: 2px;
}

#mcgxbmr1 .mr-12 {
  margin-right: 12px;
}

#mcgxbmr1 .ml-8 {
  margin-left: 8px;
}

#mcgxbmr1 .ml-12 {
  margin-left: 12px;
}

#mcgxbmr1 .ml-24 {
  margin-left: 24px;
}

#mcgxbmr1 .ml-26 {
  margin-left: 26px;
}

#mcgxbmr1 .ml-28 {
  margin-left: 28px;
}

#mcgxbmr1 .ml-74 {
  margin-left: 74px;
}

#mcgxbmr1 .ml-102 {
  margin-left: 102px;
}

#mcgxbmr1 .wid-70 {
  min-width: 70px;
}

#mcgxbmr1 .wid-84 {
  width: 84px;
}

#mcgxbmr1 .wid-96 {
  width: 96px;
}

#mcgxbmr1 .wid-98 {
  width: 98px;
}

#mcgxbmr1 .wid-186 {
  width: 186px;
}

#mcgxbmr1 .top-bar {
  border-bottom: 1px solid #c8d7e6;
  background: #e8f3ff
}

#mcgxbmr1 .top-bar .bar-inner {
  width: 960px;
  margin: 0 auto
}

#mcgxbmr1 .top-bar span {
  margin-left: 12px;
  font-weight: bold;
  font-size: 18px;
  color: #000;
  line-height: 39px
}

#mcgxbmr1 .body-wrap {
  width: 960px;
  margin: 0 auto;
  padding: 12px 12px 12px 0;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  background: #fff
}

#mcgxbmr1 .body-wrap .p-row {
  overflow: hidden;
  margin-bottom: 8px;
  color: #000
}

#mcgxbmr1 .body-wrap .p-row:last-child {
  margin-bottom: 0
}

#mcgxbmr1 .body-wrap .lb {
  width: 118px;
  text-align: right;
  margin-top: 3px;
  float: left;
  line-height: 28px;
  color: #303133
}

#mcgxbmr1 label {
  display: inline-block;
  cursor: pointer;
}

#mcgxbmr1 label+label {
  margin-left: 12px;
}

#mcgxbmr1 input[type="text"] {
  vertical-align: top;
  padding: 0 4px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

#mcgxbmr1 input[type="radio"], #mcgxbmr1 input[type="checkbox"] {
  vertical-align: middle;
  cursor: pointer;
}

#mcgxbmr1 textarea, #mcgxbmr1 select {
  padding: 4px 10px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

#mcgxbmr1 .body-wrap .g-item {
  padding: 3px 12px;
  margin-left: 118px;
  min-height: 36px;
  line-height: 28px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  overflow: hidden
}

#mcgxbmr1 .body-wrap .g-item.view-no {
  padding: unset;
  margin: unset;
  min-height: unset;
  line-height: unset;
  background: unset;
  border: unset;
  overflow: unset;
}

#mcgxbmr1 .body-wrap .g-item.with-textarea {
  padding: 0;
  border: none
}

#mcgxbmr1 .bz-item {
  display: inline-block;
  width: 140px;
  height: 32px;
  padding: 3px 8px 3px 12px;
  line-height: 22px;
  border-right: 1px solid #dcdfe6;
}

#mcgxbmr1 .visit-status {
  color: #1885F2;
}

#mcgxbmr1 .bz-item .edit-btn {
  display: inline-block;
  width: 44px;
  height: 24px;
  background: #ECF5FF;
  border-radius: 3px;
  border: 1px solid #B3D8FF;
  color: #1885F2;
  text-align: center;
}

#mcgxbmr1 .cont {
  display: inline-block;
  width: 727px;
  background: #EBEEF5;
  padding: 3px 8px 3px 12px;
  border: 1px solid #DCDFE6;
}

#mcgxbmr1 .lb-wid {
  display: inline-block;
  text-align: right;
  vertical-align: top;
}

#mcgxbmr1 .left-cont {
  display: inline-block;
  background: #F5F7FA;
  border-right: 1px solid #DCDFE6;
  vertical-align: top;
}

#mcgxbmr1 .right-cont {
  display: inline-block;
  background: #EBEEF5;
  vertical-align: top;
}

#mcgxbmr1 .lb-box {
  width: 100%;
  height: 28px;
  padding-left: 10px
}

#mcgxbmr1 .lb-box+.lb-box {
  margin: 0;
}

#mcgxbmr1 .l1-box {
  background-color: unset;
  width: 102px;
  padding-top: 8px;
}

#mcgxbmr1 .r1-box {
  width: 698px;
  height: 174px;
  padding: 8px 12px;
}

#mcgxbmr1 .r2-box {
  width: 128px;
  height: 100%;
  border-right: 1px solid #DCDFE6;
}

#mcgxbmr1 .r3-box {
  width: 724px;
  height: 104px;
  padding: 8px 12px;
}

#mcgxbmr1 .r2-right {
  width: 565px;
  height: 100%;
  display: inline-block;
}

#mcgxbmr1 .footer-wrap {
  background: #f5f7fa;
  border-top: 1px solid #dcdfe6;
  width: 100%;
  position: fixed;
  bottom: 0;
}

#mcgxbmr1 .footer-wrap .footer-inner {
  width: 960px;
  margin: 0 auto;
  padding: 8px 0
}

#mcgxbmr1 .footer-wrap .info-item {
  overflow: hidden
}

#mcgxbmr1 .footer-wrap .info-item .lb {
  width: 70px;
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
  font-size: 18px;
  color: #000;
  text-align: right;
  margin-right: 8px;
}

#mcgxbmr1 .footer-wrap .info-item textarea {
  width: 100%;
  height: 186px;
  padding: 0 4px;
  background: #fff;
  border-radius: 3px;
  line-height: 24px;
  border: 1px solid #c0c4cc
}

#mcgxbmr1 .view-show {
  display: none;
}
#mcgxbmr1 .kj-item {
  border-bottom: 1px solid #dcdfe6;
  display: inline-block;
  width: 548px;
  height: 32px;
  vertical-align: top;
}

#mcgxbmr1 .bracket {
  display: inline-block;
  vertical-align: middle;
}
#mcgxbmr1 .view-none1 {
  display: none;
}
#mcgxbmr1 .yfz-view {
  display: none;
}
#mcgxbmr1 .md-cont {
  border: 1px solid #DCDFE6;
  display: inline-block;
  width: 630px;
}
#mcgxbmr1 .layui-inline input{
  width: 100%;
  height: 28px;
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
  border: 1px solid #C0C4CC;
}
#mcgxbmr1 .showInt {
  background: #fff;
  width: 96px;
  display: inline-block;
  margin-right: 8px;
}
#mcgxbmr1 .showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}

[isview="true"] #mcgxbmr1 .view-show {
  display: block;
}
[isview="true"] #mcgxbmr1 .kj-item {
  display: none;
}
[isview="true"] #mcgxbmr1 .yfz-show,
[isview="true"] #mcgxbmr1 .zz-show,
[isview="true"] #mcgxbmr1 .zfg-view,
[isview="true"] #mcgxbmr1 .gzt-view {
  display: none;
}
[isview="true"] #mcgxbmr1 .yfz-view,
[isview="true"] #mcgxbmr1 .zz-view {
  display: inline-block;
}

[isview="true"] #mcgxbmr1 .view-show.inline {
  display: inline-block;
}

[isview="true"] #mcgxbmr1 .view-none {
  display: none;
}
[isview="true"] #mcgxbmr1 .showInt {
  background: none
}
[isview="true"] #mcgxbmr1 .showInt::after {  
  display: none;
}
[isview="true"] #mcgxbmr1 .sel-light {
  color: unset;
  background: unset;
}

[isview="true"] #mcgxbmr1 .edit-btn {
  display: none;
}

[isview="true"] #mcgxbmr1 .visit-status {
  color: unset;
}

[isview="true"] #mcgxbmr1 .bz-item {
  display: none;
}

[isview="true"] #mcgxbmr1 .zz-block {
  display: block !important;
}

[isview="true"] #mcgxbmr1 .cont,
[isview="true"] #mcgxbmr1 .md-cont,
[isview="true"] #mcgxbmr1 .left-cont,
[isview="true"] #mcgxbmr1 .right-cont {
  background: unset;
  border: unset;
  padding: unset !important;
  width: unset !important;
  height: unset !important;
}

[isview="true"] #mcgxbmr1 .mb-8 {
  margin: 0 !important;
}

[isview="true"] #mcgxbmr1 .body-wrap .g-item {
  padding: 3px 12px !important;
  border-bottom: 1px solid #dcdfe6 !important;
}

[isview="true"] #mcgxbmr1 .body-wrap .g-item.view-no {
  padding: 3px 12px;
  margin-left: 118px;
  min-height: 36px;
  line-height: 28px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  overflow: hidden
}

[isview="true"] #mcgxbmr1 .lb-wid {
  display: inline;
}

[isview="true"] #mcgxbmr1 .view-nomg {
  margin: 0 !important;
}

[isview="true"] #mcgxbmr1 .mjm-view,
[isview="true"] #mcgxbmr1 .gjm-view,
[isview="true"] #mcgxbmr1 .xqjm-view{
  display: inline-block !important;
  width: 827px;
}
[isview="true"] #mcgxbmr1 .gjm-view,
[isview="true"] #mcgxbmr1 .xqjm-view{
  margin-left:  0;
}
[isview="true"]  #mcgxbmr1 .pl-12{
  padding: 0 !important;
}