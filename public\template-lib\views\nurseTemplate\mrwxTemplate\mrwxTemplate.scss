#mrwx1 {
  font-size: 14px;
  .sign-content{
    display: flex;
    align-items: center;
    margin-left: 10px;
    .layui-input{
      width: 240px;
    }
    .weight{
      margin-right: 20px;
    }
    .temperature{
      display: flex;
      align-items: center;
    }
    .unit{
      width: 48px;
      height: 38px;
      background: #F5F7FA;
      border-radius: 0px 3px 3px 0px;
      border: 1px solid #DCDFE6;
      border-left: 0;
      line-height: 38px;
      text-align: center;
      color: #606266;
    }
    .layui-inline input{
      position: relative;
      background: transparent;
      z-index: 10;
      padding: 0 16px;
    }
    .showInt {
      position: relative;
      background: #fff;
      width: 240px;
    }
    .showInt::after{
      content: "";
      position: absolute;
      width: 5px;
      height: 5px;
      right: 30px;
      top: 50%;
      z-index: 9;
      border-top: 1px solid #606266;
      border-right: 1px solid #606266;
      transform: rotate(135deg);
      margin-top: -5px;
    }
  }
}

#mrwx1 .mr-content{
  height: 100%;
}
#mrwx1 .mr-title{
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 5px;
  border-left: 2px solid #1885F2;
  margin-bottom: 4px;
}
#mrwx1 .mt-20{
  margin-top: 20px;
}

#mrwx1 .allergy-content .allergy-item{
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  .layui-input{
    width: calc(100% - 62px);
  }
}

#mrwx1 .radio-label{
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#mrwx1 .rt-sr-lb{
  margin-left: 4px
}
#mrwx1 .gray-item{
  margin-left: 24px;
  display: flex;
  align-items: center;
}
#mrwx1 .layui-inline{
  margin-top: 8px;
  display: block;
  width: calc(100% - 62px);
}
#mrwx1 .layui-inline input{
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
  width: 100%;
}
