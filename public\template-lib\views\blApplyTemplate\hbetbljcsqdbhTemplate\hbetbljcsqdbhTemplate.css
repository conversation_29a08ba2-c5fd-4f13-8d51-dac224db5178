/* 颜色类 */
.bor-b {
  border-bottom: 1px solid #DCDFE6;
}

/* 边距类 */
.mb-4 {
  margin-bottom: 4px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-14 {
  margin-bottom: 14px;
}

.mb-54 {
  margin-bottom: 54px;
}

.ml-4 {
  margin-left: 4px;
}

.ml-8 {
  margin-left: 8px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-12 {
  margin-left: 12px;
}

.ml-28 {
  margin-left: 28px;
}

.ml-34 {
  margin-left: 34px;
}

.mr-8 {
  margin-right: 8px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-34 {
  margin-right: 34px;
}

.mr-48 {
  margin-right: 48px;
}

.mr-60 {
  margin-right: 60px;
}

.mr-102 {
  margin-right: 102px;
}

.mr-188 {
  margin-right: 188px;
}


/* 宽度类 */
.wd-72 {
  display: inline-block;
  width: 72px !important;
}

.wd-84 {
  display: inline-block;
  width: 84px !important;
}


#zzblbh1 {
  font-size: 14px;
  color: #606266;
  background: #fff;
  margin: 0 auto;
  border: 1px solid #DCDFE6;
}

[isview="true"] #zzblbh1 {
  border: none;
}

[isview="true"] #zzblbh1 .zzblbh-view {
  display: block;
}

[isview="true"] #zzblbh1 .zzblbh-edit {
  display: none;
}

.layout-page .main-content {
  width: 1140px;
}

#zzblbh1 input[type="checkbox"], #zzblbh1 input[type="radio"] {
  vertical-align: middle;
}

.bljcsqd-h {
  height: 48px;
  padding: 13px 20px;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: bold;
  border-radius: 2px 2px 0px 0px;
}

#zzblbh1 .laydate-w .rt-sr-w {
  height: 28px;
}

.box-title {
  height: 44px;
  padding: 12px 44px;
  background: #F5F7FA;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  justify-content: space-between;
}

.box-title .bb-title {
  color: rgba(0, 0, 0, 0.88);
  font-weight: bold;
}

.box-title .span-tit {
  margin-left: 4px;
}

.box-title .reservation-tip{
  color: #E64545;
}

.form-item {
  display: inline-block;
}

.box-con {
  padding: 12px 35px 12px 21px;
}

.row-item {
  display: inline-block;
  width: 360px;
}

.row-item:last-child {
  width: unset;
}

.row-title {
  text-align: right;
}

.row-lab {
  width: 109px;
  display: inline-block;
  text-align: right;
}

.inp-sty {
  width: 230px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
}

.ver-t {
  vertical-align: top;
}

.btn-sty {
  display: inline-block;
  height: 36px;
  line-height: 36px;
  color: #FFF;
  background: #1885F2;
  border-radius: 3px;
  padding: 0 12px;
  cursor: pointer;
}

.add-btn, .del-btn {
  margin-left: 8px;
}

.del-tbtn {
  color: #1885F2;
  cursor: pointer;
}

.bbxx-table {
  border: 1px solid #DCDFE6;
  border-bottom: none;
  border-right: none;
  width: 100%;
  table-layout: fixed;
}

.bbxx-table thead {
  width: 56px;
  height: 48px;
  line-height: 48px;
  font-weight: bold;
  color: #909399;
  background: #F5F7FA;
}

.bbxx-table tr {
  height: 48px;
  line-height: 28px;
  text-align: center;
}

.w-con {
  display: inline-block;
  vertical-align: middle;
}

.show-or-hide {
  position: relative;
}

.show-or-hide input {
  padding: 0 16px;
  position: relative;
  background: #fff;
  /* z-index: 2; */
  /* background: transparent; */
}

.show-or-hide:hover {
  opacity: .8;
}

.show-or-hide::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  /* z-index: 1; */
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}

.show-or-hide.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}

.show-or-hide.hide-more:after {
  transform: rotate(-45deg);
  margin-top: -2px;
}

.bbmcInpSel {
  height: 250px;
  overflow: auto;
  overflow-x: hidden;
}

.bbmcInpSel .layui-menu-item-none {
  display: none;
}

.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}

.laySelLab li, .bbmcInpSel li {
  height: 26;
}

#zzblbh1 .laydate-w textarea[val-format] {
  height: 29px;
}

.bljcsqdTableBody tr:last-child>td {
  border-bottom: none;
}

[isView="true"] #zzblbh1 .box-item .box-con .text-con {
  flex: 1;
  white-space: break-spaces;
  word-break: break-all;
}

[isView="true"] #zzblbh1 .btn-sty, [isView="true"] .bljcsqdTableBody tr>td:nth-child(2) span.text-con:first-child, [isView="true"] #zzblbh1 .show-or-hide::after {
  display: none;
}

[isView="true"] #zzblbh1 .del-tbtn {
  pointer-events: none;
}

[isView="true"] #zzblbh1 .row-item:not(:last-child) .show-or-hide {
  width: calc(100% - 114px);
}

#zzblbh1 .isk {
  color: #F56C6C;
}

[isView="true"] #zzblbh1 .isk {
  display: none;
}

[isView="true"] #zzblbh1 .mb-12 {
  display: flex;
}

[isView="true"] #zzblbh1 .row-lab {
  vertical-align: bottom;
}

.laydate-time-list:not(.show-seconds)>li:last-child {
  display: inline-block !important;
}

.laydate-time-list:not(.show-seconds)>li {
  width: 33.3% !important;
}

.laydate-time-list:not(.show-seconds)>li>ol>li {
  padding-left: 33px !important;
}

#zzblbh1 .form-item .laydate-w input {
  border: none;
  outline: none;
  display: block;
  width: 100%;
  height: 100%;
  padding: 5px 8px 5px 30px;
  box-sizing: border-box;
  height: 30px;
}

#zzblbh1 .wrap-box {
  display: flex;
  flex-wrap: wrap;
}

#zzblbh1 .wrap-box .row-item {
  margin-bottom: 12px;
}

#zzblbh1 .lb-btn {
  display: inline-block;
  vertical-align: top;
}

#zzblbh1 .btn-txt {
  color: #1885F2;
  cursor: pointer;
  text-decoration-line: underline;
  text-align: right;
}

.rt-dialog-title {
  font-weight: bold;
  font-size: 18px !important;
}

[isView="true"] #zzblbh1 .tqnjzd-btn {
  display: none;
}

/* 升降序按钮 */
.angle-btn-item {
  position: relative;
  text-align: center;
  color: #5e5e5e;
}

.angle-top {
  content: '';
  width: 0;
  height: 0;
  display: block;
  border-style: solid;
  border-width: 0 5px 5px;
  position: absolute;
  transform: rotate(180deg);
  bottom: 3px;
  right: 34px;
  cursor: pointer;
}

.angle-bottom {
  content: '';
  width: 0;
  height: 0;
  display: block;
  border-style: solid;
  border-width: 0 5px 5px;
  position: absolute;
  top: 3px;
  right: 34px;
  cursor: pointer;
}

.inactive-color {
  border-color: transparent transparent #C0C4CC;
}

.active-color {
  border-color: transparent transparent #1885F2;
}

.rx-box {
  width: calc(100% - 114px);
  height: 128px;
  padding: 8px 16px;
  background: #F5F7FA;
  border: 1px solid #C0C4CC;
  border-radius: 3px;
}

.rx-box .row-item {
  width: inherit;
}

.rx-box .inp-sty {
  width: 200px;
}

.rx-box label+label {
  margin-left: 16px;
}

#zzblbh1 .zzblbh-view {
  display: none;
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 14px;
  min-height: 1100px;
  background: #fff;
  padding: 24px 32px 32px;
  color: #000;
}

#zzblbh1 .zzblbh-view .view-head {
  text-align: center;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 16px;
}

#zzblbh1 .view-form {
  width: 100%;
  height: 940px;
  border: 1px solid #000000;
  display: flex;
}

#zzblbh1 .view-form * {
  font-size: 12px;
}

#zzblbh1 .left-letter {
  width: 244px;
  padding: 8px;
  border-right: 1px solid #000000;
  box-sizing: border-box;
}

#zzblbh1 .left-letter p {
  text-indent: 2em;
  line-height: 16px;
}

#zzblbh1 .left-letter p+p {
  margin-top: 16px;
}

#zzblbh1 .right-msg {
  width: calc(100% - 244px);
  box-sizing: border-box;
  position: relative;
}

#zzblbh1 .msg-line {
  width: 100%;
  padding: 0 8px;
  min-height: 28px;
  border-bottom: 1px solid #000000;
  box-sizing: border-box;
}

#zzblbh1 .flex-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

#zzblbh1 .wd100-36 {
  width: calc(100% - 36px);
}

#zzblbh1 .wd100-48 {
  width: calc(100% - 48px);
}

#zzblbh1 .wd100-60 {
  width: calc(100% - 60px);
}

#zzblbh1 .wd100-72 {
  width: calc(100% - 72px);
}

#zzblbh1 .wd100-84 {
  width: calc(100% - 84px);
}

.sample-tbl tr th, .sample-tbl tr td {
  max-width: 240px;
  width: 240px;
  padding: 0 4px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  border: 1px #000 solid;
}

.special-item {
  width: 100%;
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}