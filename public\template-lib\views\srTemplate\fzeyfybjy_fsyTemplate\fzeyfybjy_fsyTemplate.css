#dgfsy1 {
  font-size: 14px;
  width: 780px;
  min-height: 100%;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  position: relative;
  padding-bottom: 70px;
}
#dgfsy1 .fsy-logo img {
  width: 100%;
}
#dgfsy1 .fsy-head {
  padding: 0 50px;
  position: relative;
}
#dgfsy1 .fsy-head .tit-w {
  padding: 0 0 24px 0;
  position: relative;
}
#dgfsy1 .fsy-head .abs {
  position: absolute;
  bottom: 8px;
  font-size: 14px;
}
#dgfsy1 .fsy-head .abs.l {
  left: 0;
}
#dgfsy1 .fsy-head .abs.r {
  right: 0;
}
#dgfsy1 .fsy-head .rpt-tit {
  font-size: 22px;
  color: #000;
  text-align: center;
}
#dgfsy1 .fsy-head .rpt-subTit {
  font-size: 20px;
  color: #000;
  text-align: center;
  margin-top: 12px;
}
#dgfsy1 .fsy-head .fsy-pat {
  padding: 8px 0;
  border-top: 1px solid #606266;
  border-bottom: 1px solid #606266;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
}
#dgfsy1 .fsy-head .pat-i {
  margin-right: 18px;
  display: flex;
}
#dgfsy1 .fsy-head .pat-i:last-child {
  margin-right: 0;
}
#dgfsy1 .fsy-body {
  padding: 16px 50px;
}
#dgfsy1 .fsy-body .body-chart {
  display: flex;
  justify-content: space-between;
}
#dgfsy1 .fsy-body .lr-wrap {
  width: 335px;
}
#dgfsy1 .fsy-body .chart-w {
  width: 100%;
  height: 350px;
  border: 1px solid rgba(0, 0, 0, .5);
  display: flex;
  flex-direction: column;
}
#dgfsy1 .fsy-body .chart-tit {
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  color: #fff;
  background: #191975;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, .5);
}
#dgfsy1 .fsy-body #left-chart,
#dgfsy1 .fsy-body #right-chart {
  width: 310px;
  height: 290px;
  margin: auto;
}
#dgfsy1 .fsy-body .table-w {
  margin-top: 16px;
}
#dgfsy1 .fsy-body .table-w td {
  height: 18px;
}
#dgfsy1 .fsy-body .table-tit {
  font-size: 16px;
  color: #000;
  font-weight: bold;
}
#dgfsy1 .fsy-body .lr-table {
  width: 100%;
  border-collapse: collapse;
  color: #000;
  font-size: 12px;
  text-align: center;
  table-layout: fixed;
  border-color: rgba(0, 0, 0, .5);
}
#dgfsy1 .fsy-body .lr-table .caption {
  background: #D6DFF7;
}
#dgfsy1 .fsy-body .pl8 {
  padding-left: 8px;
}
#dgfsy1 .fsy-body .pr8 {
  padding-right: 8px;
}
#dgfsy1 .fsy-body .noborder {
  border: none;
}
#dgfsy1 .fsy-body .deep-bg {
  background: #7A4AA3;
  height: 16px;
  display: block;
}
#dgfsy1 .fsy-body .pink-bg {
  width: 100%;
  height: 100%;
  display: block;
  background: #FFC9CB;
}
#dgfsy1 .fsy-body .gray-bg {
  background: #EAEAEA;
  font-weight: bold;
}
#dgfsy1 .fsy-body .body-desc {
  display: flex;
  margin-top: 16px;
  color: #000;
}
#dgfsy1 .fsy-body .body-desc .desc-con{
  flex: 1;
}
#dgfsy1 .fsy-body .body-desc textarea{
  width: 100%;
}
#dgfsy1 .fsy-foot {
  position: absolute;
  border-top: 1px solid #606266;
  padding-top: 8px;
  bottom: 30px;
  left: 50px;
  right: 50px;
  display: flex;
}
#dgfsy1 .fsy-foot .foot-i {
  flex: 1;
  margin-right: 16px;
  display: flex;
  font-size: 16px;
}
#dgfsy1 .lb {
  color: #303133;
}
#dgfsy1 .vlt {
  color: #000;
}
/* 下拉选择项 */
#dgfsy1 .in-drop {
  position: relative;
}
#dgfsy1 .in-drop input {
  padding-right: 18px;
}
#dgfsy1 .in-drop.inb {
  display: inline-block;
  width: 48%;
}
[isview='true'] #dgfsy1 .in-drop.inb {
  display: block;
  width: 100%;
}
#dgfsy1 .in-drop .iarr {
  position: absolute;
  top: 7px;
  right: 8px;
  width: 8px;
  height: 8px;
  z-index: 9;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg);
}
[isview='true'] #dgfsy1 .in-drop .iarr{
  display: none;
}