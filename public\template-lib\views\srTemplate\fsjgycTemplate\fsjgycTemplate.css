/* ----边距---- */
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-24 {
  margin-left: 24px;
}
.ml-28 {
  margin-left: 28px;
}
.ml-40 {
  margin-left: 40px;
}
.ml-50 {
  margin-left: 50px;
}
.ml-66 {
  margin-left: 66px;
}
.ml-70 {
  margin-left: 70px;
}
.con-pad {
  padding: 11px 12px;
}
.box-pad {
  padding: 8px 12px;
}
/* 宽度类 */
.wd-50 {
  display: inline-block;
  width: 50px!important;
  line-height: 28px;
}
.wd-68 {
  display: inline-block;
  width: 68px!important;
}
.wd-78 {
  display: inline-block;
  width: 78px!important;
}
.wd-90 {
  display: inline-block;
  width: 90px!important;
}
.wd-136 {
  display: inline-block;
  width: 136px!important;
}
.wd-200 {
  display: inline-block;
  width: 200px!important;
}
/* 颜色类 */
.bg-gray {
  background: #F5F7FA!important;
}
.gray-light {
  background: #EBEEF5!important;
}
/* 主框架 */
#fsjgyc1 {
  width: 960px;
  height: 100%;
  font-size: 14px;
  margin: 0 auto;
  border: 1px solid #C8D7E6;
}
#fsjgyc1 .fsjgyc-h,#tp .tp-box-h {
  background: #EBEEF5;
  height: 44px;
  padding: 8px 12px;
  border-bottom: 1px solid #C8D7E6;
}
#fsjgyc1 .menu-group, #tp .switch-group {
  display: flex;
  height: 28px;
  line-height: 28px;
}
#fsjgyc1 .menu-item, #tp .switch-item {
  padding: 0 15px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  background: #fff;
  border: 1px solid #DCDFE6;
  line-height: 26px;
}
#fsjgyc1 .menu-item + .menu-item, #tp .switch-item + .switch-item {
  border-left: none;
}
#fsjgyc1 .menu-item:first-child,#tp .switch-item:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
#fsjgyc1 .menu-item:last-child, #tp .switch-item:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
#fsjgyc1 .menu-item.act, #tp .switch-item.act {
  border-color: #1885F2;
  background: #1885F2;
  color: #FFF;
}
#fsjgyc1 .menu-item:hover, #tp .switch-item:hover {
  opacity: 0.7;
}
#fsjgyc1 .fsjgyc-b {
  height: calc(100% - 44px);
  overflow: auto;
  /* padding: 8px 12px; */
}
#fsjgyc1 .menu-con, #tp .switch-con {
  display: none;
}
#fsjgyc1 .menu-con {
  height: 100%;
  padding: 8px 12px;
}
#fsjgyc1 .menu-con.act, #tp .switch-con.act {
  display: block;
}
#fsjgyc1 .con-box {
  box-sizing: border-box;
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#fsjgyc1 .box-item {
  border: 1px solid #C8D7E6;
  background: #EBEEF5;
}
#fsjgyc1 .box-title {
  font-size: 16px;
  color: #000000;
  font-weight: bold;
}
#fsjgyc1 .flex-sty {
  display: flex;
  justify-content: space-between;
}
#fsjgyc1 .flex-item {
  flex: 1;
}
#fsjgyc1 input[type="radio"], #fsjgyc1 input[type="checkbox"]{
  vertical-align: middle;
}
#fsjgyc1 .inp-sty {
  width: 60px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 0 3px;
}
#fsjgyc1 .fill-lab label {
  display: inline-block;
  width: 100%;
}

/* 含有超声描述、超声提示 */
.menu-sub-group {
  display: flex;
  overflow: hidden;
  height: 44px;
}
.menu-sub-item {
  position: relative;
  height: 32px;
  line-height: 22px;
  padding: 5px 20px;
  box-sizing: border-box;
  cursor: pointer;
  color: #606266;
}
.menu-sub-item.act {
  font-weight: bold;
  color: #1885F2;
  border-bottom: 2px solid #1885F2;
}
.menu-sub-box {
  height: calc(100% - 44px);
  overflow: auto;
}
.menu-sub-con {
  display: none;
}
.menu-sub-con.act {
  display: block;
}

/* 胎盘 */
#tp .box-left {
  width: 120px;
  padding: 8px;
  margin: auto;
  text-align: center;
}
#tp .box-mid {
  width: 130px;
  padding: 8px 11px;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#tp .box-mid label {
  display: inline-block;
  width: 100%;
}
#tp .box-right {
  flex: 1;
  padding: 8px 12px;
  background: #EBEEF5;
}
/* 颅、侧脑室 */
#lcns-csms1 .box-left {
  width: 98px;
  border-right: 1px solid #C8D7E6;
  padding: 11px 12px;
  box-sizing: border-box;
}
#lcns-csts .box-item {
  display: inline-block;
  padding: 0 8px;
  border: 1px solid #C8D7E6;
  background: #EBEEF5;
  margin-left: 12px;
}
/* 其他 */
#qt .box-left {
  width: 230px;
  padding: 11px 12px;
}
#qt .box-mid {
  width: 68px;
  padding: 11px 12px;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#qt .box-right {
  flex: 1;
  padding: 8px 12px;
  background: #EBEEF5;
}
