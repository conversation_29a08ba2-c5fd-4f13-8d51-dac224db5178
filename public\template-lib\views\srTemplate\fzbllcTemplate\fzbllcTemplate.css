#fzbllc1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}

#fzbllc1 .lc-view {
  display: none;
}

[isview="true"] #fzbllc1 .lc-edit {
  display: none;
}

[isview="true"] #fzbllc1 .lc-view {
  display: block;
}

#fzbllc1 input[type="radio"], #fzbllc1 input[type="checkbox"] {
  vertical-align: middle;
}

#fzbllc1 input[type="text"], #fzbllc1 .inp-sty {
  padding: 0 12px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #C0C4CC;
}

#fzbllc1 .reset-tt {
  width: calc(100% - 24px);
  height: 80px;
  margin-left: 24px;
  padding: 4px 12px;
}

#fzbllc1 .lc-edit {
  color: #000;
  padding: 10px 24px;
}

#fzbllc1 .row-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

#fzbllc1 .row-item+.row-item {
  margin-top: 4px;
}

#fzbllc1 .exam-tbl {
  width: 100%;
  border-collapse: collapse;
  border-color: #C0C4CC;
  background: #ffffff;
  box-sizing: border-box;

}

#fzbllc1 #fishShow .exam-tbl tr th, #fzbllc1 #fishShow .exam-tbl tr td {
  text-align: center;
}

#fzbllc1 .exam-tbl tr th {
  padding: 8px;


}

#fzbllc1 .exam-tbl tr td {
  padding: 0 8px;
  line-height: 36px;


}

#fzbllc1 .exam-tbl tr input {
  width: 100%;
  min-width: 120px;
}

#fzbllc1 .exam-tbl tr td span {
  font-family: inherit;
}

#fzbllc1 .showInt {
  position: relative;
  width: 100%;
  background: #fff;
}

#fzbllc1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}

#fzbllc1 .showInt.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}

#fzbllc1 .textarea-stl {
  width: 100%;
  height: 200px;
  margin-top: 4px;
  padding: 4px 12px;
  font-size: 16px;
}

#fzbllc1 .lc-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 16px;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 106px;
  color: #000;
}

#fzbllc1 .lc-view * {
  font-family: "宋体", sans-serif !important;
}

#fzbllc1 .lc-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 20px;
  text-align: center;
}

#fzbllc1 .lc-view .head-img {
  position: absolute;
  top: 30px;
  left: 110px;
  width: 64px;
  height: 64px;
}

#fzbllc1 .lc-view .head-img img {
  width: 100%;
  height: 100%;
}

#fzbllc1 .lc-view .right-num {
  position: absolute;
  top: 76px;
  right: 56px;
}

#fzbllc1 .lc-view .right-num .black-txt {
  display: inline-block;
  max-width: 148px;
}

#fzbllc1 .lc-view .page-tit {
  font-size: 24px;
  font-weight: 800;
}

#fzbllc1 .lc-view .page-tit.sub-tit {
  font-size: 18px;
}

#fzbllc1 .lc-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#fzbllc1 .lc-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 24px;

}

#fzbllc1 .lc-view .gray-txt .bold {
  font-weight: 600;
}

#fzbllc1 .lc-view .black-txt {
  color: #000;
  font-size: 16px;
  line-height: 24px;

}

#fzbllc1 .lc-view .bold {
  font-weight: bold;
}

#fzbllc1 .lc-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}

#fzbllc1 .lc-view .info-i+.info-i {
  margin-left: 8px;
}

#fzbllc1 .lc-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

#fzbllc1 .lc-view .view-patient .p-item {
  align-items: center;
}

#fzbllc1 .lc-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}

#fzbllc1 .fz-tip {
  line-height: 24px;
}


#fzbllc1 .lc-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}

#fzbllc1 .lc-view .report-wrap {
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#fzbllc1 .lc-view .tip-wrap {
  margin-top: 8px;
  font-size: 12px;
}

#fzbllc1 .lc-view .rpt-img-ls {
  display: none;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 0;
  /* justify-content: center; */
}

#fzbllc1 .lc-view .item-img {
  /* display: inline-block; */
  width: 159px;
  height: 120px;
  box-sizing: border-box;
  border: 1px solid #eee;
  margin-right: 8px;
  margin-top: 12px;
}

#fzbllc1 .lc-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

#fzbllc1 .lc-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}

#fzbllc1 .lc-view .reporter-i [data-key] {
  flex: 1;
}

#fzbllc1 .lc-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}

#fzbllc1 .lc-view .reporter-i+.reporter-i {
  margin-left: 8px;
}

#fzbllc1 .wid-88 {
  width: 100%;
  min-width: 88px;
}

#fzbllc1 .wid-130 {
  width: 100%;
  min-width: 130px;
}

#fzbllc1 .wid-304 {
  width: 304px;
}

#fzbllc1 .wid-320 {
  width: 320px;
}

#fzbllc1 .mt-4 {
  width: 100%;
  margin-top: 4px !important;
}

#fzbllc1 .mt-8 {
  margin-top: 8px;
}

#fzbllc1 .mt-12 {
  margin-top: 12px;
}

#fzbllc1 .mt-14 {
  margin-top: 14px;
}

#fzbllc1 .ml-16 {
  margin-left: 16px;
}