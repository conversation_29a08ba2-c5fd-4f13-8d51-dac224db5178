/*-----------------字体大小、行高-----------------*/
.fs-16 {
  font-size: 16px;
  line-height: 16px;
}
.fs-24 {
  font-size: 24px;
  line-height: 24px;
}
.fs-32 {
  font-size: 32px;
  line-height: 64px;
}

/*-------------------字体加粗---------------------*/
.fw-600 {
  font-weight: 600;
}

/*---------------------颜色类-----------------------*/
.f-white {
  color: #fff;
}
.f-black {
  color: #000;
}
.f-green {
  color: #21A685;
}
.f-red {
  color: #F56C6C;
}
.b-fc{
  background: #F2F6FC;
}
.row-item {
  color: #606266;
}
.item-val {
  color: #303133;
}

/*-------------------高度---------------------*/
.h-36 {
  height: 36px;
  line-height: 36px;
}

/*清浮动系列*/
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/*-------浮动--------*/
.fl {
  float: left;
}
.fl.w23 {
  width: 23%;
}
.fl.w25 {
  width: 25%;
}
.fl.w27 {
  width: 27%;
}
.fl.w33 {
  width: 33%;
}
.fl.w34 {
  width: 34%;
}

.fr {
  float: right;
}
.fr.w25 {
  width: 25%;
}

/*---------------------边框线-----------------------*/
.bottom-line {
  border-bottom: 1px solid #F2F2F2;
}
.blue-b-line {
  border-bottom: 1px solid #C8D7E6;
}
.blue-r-line {
  border-right: 1px solid #C8D7E6;
}

/*-------宽度根据内容撑开----------*/
.w-i {
  width: inherit;
  padding-top: 0;
}

/*-------文本位置----------*/
.t-r {
  width: 70px;
  text-align: right;
}
.t-l {
  text-align: left;
}
.t-c {
  text-align: center;
}

/*-------内边距----------*/
.ptb-8 {
  padding: 8px 0;
}
.ptb-10 {
  padding: 10px 0;
}
.ptb-18 {
  padding: 18px 0;
}

/*--------chq检查报告页面---------*/
.chq-page {
  width: 780px;
  background: #fff;
}
/*--------chq检查报告头部---------*/
.chq-header {
  position: relative;
  padding: 11px 88px;
  text-align: center;
  background-color: #056E1E;
}

/*--------chq检查报告内容---------*/
.chq-body {
  padding: 0 50px;
}
.contain-header {
  padding: 20px 90px;
  text-align: center;
}
.contain-footer {
  padding: 0 26px;
}
.table-header {
  padding: 5px 8px;
}
.td-h {
  height: 52px;
  box-sizing: border-box;
}
.check-td {
  line-height: 52px;
}
.rep-table {
  margin: 12px 0;
  border: 1px solid #C8D7E6;
}
.inp-sty {
  width: 92%;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
}
.sel-sty {
  width: 184px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
}
.assess-result {
  padding: 8px 13px;
}
.footer-item {
  display: inline-block;
}

.c13-suggest {
  display: none;
}