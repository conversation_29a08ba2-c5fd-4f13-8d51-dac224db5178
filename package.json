{"name": "cloud-pacs-struct-report-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "node build_script vue-cli-service build --mode production"}, "dependencies": {"axios": "^0.21.1", "babel-plugin-transform-imports": "^2.0.0", "canvas": "^2.11.2", "chinese-to-pinyin": "^1.3.1", "cloud-structure-layout-ui": "^1.1.7", "compressorjs": "^1.2.1", "core-js": "^3.6.5", "crypto-js": "^4.0.0", "dayjs": "^1.10.6", "dommatrix": "^1.0.3", "drag-tree-table": "^2.2.0", "echarts": "^5.1.2", "echarts-gl": "^2.0.8", "element-ui": "^2.13.2", "file-saver": "^2.0.5", "fontkit": "^2.0.4", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.6.0", "jspdf": "^2.5.1", "pdf-lib": "^1.17.1", "pdfh5": "^1.4.3", "pinyin-pro": "^3.26.0", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "print-kit": "^2.1.7", "rt-common-functions": "^1.3.4", "sass": "^1.77.8", "sass-loader": "^9.0.2", "stats-webpack-plugin": "^0.7.0", "string-similarity": "^4.0.4", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0", "web-streams-polyfill": "^3.2.1", "xlsx": "^0.17.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^6.1.1", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "Chrome >= 49", "ie >= 9"]}