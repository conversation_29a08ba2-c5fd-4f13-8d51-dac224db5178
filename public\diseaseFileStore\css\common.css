* {
  margin:0;
  padding:0;
  box-sizing:border-box;
}
ul { list-style:none; }
.t-pg input, .t-pg select, .t-pg textarea { 
  outline:none; 
  min-height: 28px;
}
i {
  font-style: unset;
}
.rt-sr-t {
  border: 1px solid #DCDFE6;
  resize: none;
  padding: 4px 10px;
  border-radius: 3px;
  font-family: Microsoft YaHei;
}
.t-pg { display:inline-block; width: 100%;}
.page {
  position:relative;
  text-align:left;
  font-size:14px;
  color: #303133;
  width: 100%;
}
.page + .page { margin-top:25px; }
/* 水平分布的弹性布局 */
.flex-h {
  display: flex;
}
/* 垂直分布的弹性布局 */
.flex-v {
  display: flex;
  flex-direction: column;
}
.p-item {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  width: 100%;
}
.p-item + .p-item {
  margin-top: 16px;
}
.p-item .p-item + .p-item {
  margin-top: 8px;
}
.w-con {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.w-con.v {
  flex-direction: column;
  align-items: flex-start;
}
.w-txt {
  display: inline-block;
  width: 80px;
  text-align: right;
}
.rt-sr-tit {
  width: 80px;
  text-align: right;
  padding-top: 4px;
}
.rt-sr-w .isk {
  color: #F56C6C;
  margin-right: 4px;
}
.r-i {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.w-con:not(.v) .r-i + .r-i {
  margin-left: 24px;
}
.r-i .rt-sr-lb {
  margin-left: 5px;
}
.p-lb {
  margin-right: 5px;
} 
.s-lb {
  margin-left: 5px;
}
select.rt-sr-s {
  min-width: 140px;
  border-color: #DCDFE6;
  border-radius: 3px;
}
.inp-number {
  position: relative;
  width: 120px;
  height: 28px;
  line-height: 28px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  overflow: hidden;
}
.inp-number_decrease, .inp-number_increase {
  position: absolute;
  z-index: 1;
  top: 0;
  width: 27px;
  bottom: 0;
  text-align: center;
  background: #F5F7FA;
  color: #606266;
  cursor: pointer;
  font-size: 14px;
}
.inp-number_decrease {
  left: 0;
  border-right: 1px solid #DCDFE6;
}
.inp-number_increase {
  right: 0;
  border-left: 1px solid #DCDFE6;
}
.inp-number_decrease.is-disabled, 
.inp-number_increase.is-disabled {
  color: #C0C4CC;
  cursor: not-allowed;
  pointer-events: none;
}
.inp-number .inp_inner {
  padding-left: 30px;
  padding-right: 30px;
  width: 100%;
  height: 100%;
  border: none;
  text-align: center;
  padding-top: 5px;
  font-family: Microsoft YaHei;
}
input:checked + .rt-sr-lb {
  color: #1885F2;
}

/* 块状区域样式 start*/
.w-block, .w-block-pd {
  flex: 1;
  display: flex;
  border: 1px solid #C8D7E6;
  background: #F5F7FA;
  min-height: 40px;
}
.w-block + .w-block, .w-block-pd + .w-block-pd {
  border-top: 0;
}
.w-block-pd {
  padding: 8px;
}
/* 浅色块 */
.w-block .light-block {
  padding: 8px;
}
.w-block .lb-row, .w-block .lb-new {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 0;
}
.w-block .lb-row + .lb-row, .w-block .lb-new + .lb-new {
  margin-top: 3px;
}
.w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::after,
.w-block .lb-new.lb-new-act > input::after,
.w-block .lb-row.lb-active.on > input::after {
  content: '';
  position: absolute;
  z-index: -1;
  left: -8px;
  right: -8px;
  top: 0;
  bottom: 0;
  background: #EBEEF5;
}
.w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::before,
.w-block .lb-new.lb-new-act > input::before,
.w-block .lb-row.lb-active.on > input::before {
  content: '';
  position: absolute;
  z-index: 0;
  left: -8px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #1885F2;
}
.w-block .lb-row.lb-active > input:checked + .rt-sr-lb,
.w-block .lb-new.lb-new-act > input + .rt-sr-lb {
  color: #1885F2;
}
.w-block .lb-row .lb-tip,
.w-block .lb-new .lb-tip {
  position: absolute;
  right: 0;
  z-index: 0;
  color: #606266;
}
/* 深色块 */
.w-block .hight-block {
  padding: 8px;
  flex: 1;
  border-left: 1px solid #C8D7E6;
  background: #EBEEF5;
  color: #606266;
  display: none;
}

/* 块状区域样式-end */

.w-con .rt-sr-r.rt-sr-inv+.rt-sr-lb,
.w-con .rt-sr-ck.rt-sr-inv+.rt-sr-lb,
.w-con .rt-sr-tit.rt-sr-inv {
  color:red!important;
}
.w-con .rt-sr-t.rt-sr-inv {border-color:red!important;}

/* tabs标签页 */
.rt-tabs {
  box-shadow: none;
  border: none;
  width: 100%;
}
.rt-tabs .el-tabs__nav-wrap {
  background: #F5F7FA;
  position: relative;
}
.rt-tabs .el-tabs__nav-wrap::after {
  content: '' !important;
  position: absolute;
  left: 2px;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #DCDFE6;
}
.rt-tabs .el-tabs__nav-next, .rt-tabs .el-tabs__nav-prev {
  line-height: 32px;
}
.rt-tabs .el-tabs__item {
  height: 32px;
  line-height: 32px;
  background: #fff;
  border: none !important;
  border-right: 1px solid #DCDFE6 !important;
  margin-left: 1px !important;
  border-bottom: 1px solid #DCDFE6 !important;
}
.rt-tabs .el-tabs__item.is-active {
  background: #F5F7FA !important;
  position: relative;
  border-bottom: none !important;
}
.rt-tabs .el-tabs__item .rt-sr-w {
  width: auto !important;
  padding-top: 0 !important;
}
.rt-tabs .el-tabs__nav-scroll {
  overflow-x: auto !important;
}
.rt-tabs .el-tabs__nav-wrap.is-scrollable {
  padding: 0 !important;
}
.rt-tabs .el-tabs__nav {
  transform: unset !important;
}
.rt-tabs .el-tabs__nav-prev, .rt-tabs .el-tabs__nav-next {
  display: none;
}
.rt-tabs.el-tabs--card>.el-tabs__header .el-tabs__nav {
  border: 0 !important;
}
.rt-tabs.el-tabs--card .el-tabs__item:not(.is-active) {
  color: #909399;
}
.rt-tabs.el-tabs--card>.el-tabs__header .pane-title .el-icon-close {
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: unset;
}
/* .rt-tabs .el-tabs__item.is-active::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: red;
} */
.rt-tabs .el-tabs__content {
  background: #F5F7FA;
}
.t-pg label:not(.lb-row):not(.lb-new) {
  width: fit-content;
}
