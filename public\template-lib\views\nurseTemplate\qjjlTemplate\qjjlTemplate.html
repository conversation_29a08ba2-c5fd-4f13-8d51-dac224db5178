<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/nurseTemplate/qjjlTemplate/qjjlTemplate.css">
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/nurseTemplate/qjjlTemplate/qjjlTemplate.js"></script>

<ul class="t-pg" style="height: 100%;overflow: auto;">
  <li class="page" id="qjjl1">
    <div class="qjjl-content">
      <div class="qjjl-title">抢救时间</div>
      <div class="sign-content">
        <div class="weight">
          <div>开始抢救时间：</div>
          <div class="temperature"><input type="text" class="layui-input rt-sr-w" id="qjjl-rt-1" placeholder="开始抢救时间" style="display: inline-block; width: 240px;" rt-sc="pageId:qjjl1;name:开始抢救时间;wt:1;desc:开始抢救时间;vt:;pvf:;code:450;"></div>
        </div>
        <div class="weight">
          <div>医生到达时间：</div>
          <div class="temperature"><input type="text" class="rt-sr-w layui-input" placeholder="医生到达时间" id="qjjl-rt-2" rt-sc="pageId:qjjl1;name:医生到达时间;wt:1;desc:医生到达时间;vt:;pvf:;code:451"></div>
        </div>
      </div>
      <div class="qjjl-title rt-sr-w" id="qjjl-rt-001" rt-sc="pageId:qjjl1;name:抢救人员;wt:;desc:抢救人员;vt:;pvf:1;">抢救人员</div>
      <div class="sign-content">
        <div class="weight">
          <div>抢救医生：</div>
          <div data-type="edit" class="temperature showInt"><input type="text" class="layui-input rt-sr-w" id="qjjl-rt-3" pid="qjjl-rt-001" placeholder="请输入" style="display: inline-block; width: 240px;" rt-sc="pageId:qjjl1;name:抢救医生;wt:1;desc:抢救医生;vt:;pvf:;code:452;"></div>
          <span data-type="preview" data-key="qjjl-rt-3"></span>
        </div>
        <div class="weight">
          <div>抢救护士：</div>
          <div data-type="edit" class="temperature showInt"><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-4" pid="qjjl-rt-001" rt-sc="pageId:qjjl1;name:抢救护士;wt:1;desc:抢救护士;vt:;pvf:;code:452;"></div>
          <span data-type="preview" data-key="qjjl-rt-4"></span>
        </div>
      </div>
      <div class="qjjl-title rt-sr-w" id="qjjl-rt-004" rt-sc="pageId:qjjl1;name:抢救前生命支持;wt:;desc:抢救前生命支持;vt:;pvf:1;code:464;">抢救前生命支持</div>
      <div class="allergy-content">
        <div class="gray-item">
          <label class="radio-label" for="qjjl-rt-5">
            <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-jdmpp" value="无" data-value="0" id="qjjl-rt-5" pid="qjjl-rt-004" rt-sc="pageId:qjjl1;name:无;wt:5;desc:无;vt:;pvf:;">
            <span class="rt-sr-lb">无</span>
          </label>
          <label class="radio-label" for="qjjl-rt-6">
            <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-jdmpp" value="有" data-value="1" id="qjjl-rt-6" pid="qjjl-rt-004" rt-sc="pageId:qjjl1;name:有;wt:5;desc:有;vt:;pvf:;code:464;">
            <span class="rt-sr-lb">有</span>
          </label>
        </div>
        <div class="gray-content">
        <div class="gray-item" style="margin-left: 0px;flex-wrap: wrap;">
          <label class="radio-label" for="qjjl-rt-7">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="气管插管" id="qjjl-rt-7" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:气管插管;wt:4;desc:气管插管;vt:;pvf:;" >
            <span class="rt-sr-lb">气管插管</span>
          </label>
          <label class="radio-label" for="qjjl-rt-8">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="机械通气" id="qjjl-rt-8" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:机械通气;wt:4;desc:机械通气;vt:;pvf:;" >
            <span class="rt-sr-lb">机械通气</span>
          </label>
          <label class="radio-label" for="qjjl-rt-9">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="心电监护" id="qjjl-rt-9" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:心电监护;wt:4;desc:心电监护;vt:;pvf:;" >
            <span class="rt-sr-lb">心电监护</span>
          </label>
          <label class="radio-label" for="qjjl-rt-10">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="血管活性药物支持" id="qjjl-rt-10" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:血管活性药物支持;wt:4;desc:血管活性药物支持;vt:;pvf:;" >
            <span class="rt-sr-lb">血管活性药物支持</span>
          </label>
          <label class="radio-label" for="qjjl-rt-11">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="有创动脉监测" id="qjjl-rt-11" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:有创动脉监测;wt:4;desc:有创动脉监测;vt:;pvf:;" >
            <span class="rt-sr-lb">有创动脉监测</span>
          </label>
          <label class="radio-label" for="qjjl-rt-12">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="中心静脉导管" id="qjjl-rt-12" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:中心静脉导管;wt:4;desc:中心静脉导管;vt:;pvf:;" >
            <span class="rt-sr-lb">中心静脉导管</span>
          </label>
          <label class="radio-label" for="qjjl-rt-13">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="外周静脉留置针" id="qjjl-rt-13" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:IV通畅;wt:4;desc:IV通畅;vt:;pvf:;" >
            <span class="rt-sr-lb">外周静脉留置针</span>
          </label>
          <label class="radio-label" for="qjjl-rt-5">
            <input type="checkbox" class="rt-sr-w" name="ck-mb" value="IV通畅" id="qjjl-rt-14" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:IV通畅;wt:4;desc:IV通畅;vt:;pvf:;" >
            <span class="rt-sr-lb">IV通畅</span>
          </label>
        </div>
        <input type="text" style="width: calc(100% - 12px);margin-left:0px;margin-top: 6px;" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-15" pid="qjjl-rt-6" rt-sc="pageId:qjjl1;name:抢救前生命支持;wt:1;desc:抢救前生命支持;vt:;pvf:;">
      </div>
      </div>
      <div class="qjjl-title rt-sr-w" style="display: none;" id="qjjl-rtlist-1" rt-sc="pageId:qjjl1;name:生命体征记录;wt:;desc:生命体征记录;vt:;pvf:1;">
        生命体征记录
      </div>
      <div class="qjjl-title" style="display: flex;align-items: center;justify-content: space-between;" data-type="edit">
        <div>生命体征</div>
        <div class="add-btn" onclick="addLiveHandler()"><i class="layui-icon"></i> 添加记录</div>
      </div>
      <div class="table-content">
        <table class="layui-table">
          <colgroup>
            <col width="62">
            <col width="200">
            <col width="120">
            <col width="120">
            <col>
            <col>
            <col>
            <col data-type="edit" width="60">
          </colgroup>
          <thead>
            <tr>
              <th>序号</th>
              <th>记录时间</th>
              <th>心率 (次/分)</th>
              <th>呼吸 (次/分)</th>
              <th>血压 (mmHg)</th>
              <th>氧饱和度 (%)</th>
              <th>瞳孔 (mm)</th>
              <th data-type="edit">操作</th>
            </tr> 
          </thead>
          <tbody class="live-con" data-type="edit" tab-target="qjjl-rtlist-000">
            <tr class="bz-tr rt-sr-w" id="qjjl-rtlist-000" pid="qjjl-rtlist-1" rt-sc="pageId:qjjl1;name:体征记录;wt:;desc:体征记录;vt:;pvf:;">
              <td id="qjjl-rtlist-000-1" class="rt-sr-w" rt-sc="pageId:qjjl1;name:序号;wt:;desc:序号;vt:;pvf:1;" pid="qjjl-rtlist-000">1</td>
              <td><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rtlist-000-2" rt-sc="pageId:qjjl1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:;" pid="qjjl-rtlist-000"></td>
              <td><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rtlist-000-3" rt-sc="pageId:qjjl1;name:心率 (次/分);wt:1;desc:心率 (次/分);vt:;pvf:;" pid="qjjl-rtlist-000"></td>
              <td><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rtlist-000-4" rt-sc="pageId:qjjl1;name:呼吸 (次/分);wt:1;desc:呼吸 (次/分);vt:;pvf:;" pid="qjjl-rtlist-000"></td>
              <td><input type="text" class="rt-sr-w layui-input" placeholder="收缩压 / 舒张压" id="qjjl-rtlist-000-5" rt-sc="pageId:qjjl1;name:血压 (mmHg);wt:1;desc:血压 (mmHg);vt:;pvf:;" pid="qjjl-rtlist-000"></td>
              <td><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rtlist-000-6" rt-sc="pageId:qjjl1;name:氧饱和度 (%25);wt:1;desc:氧饱和度 (%25);vt:;pvf:;" pid="qjjl-rtlist-000"></td>
              <td><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rtlist-000-7" rt-sc="pageId:qjjl1;name:瞳孔 (mm);wt:1;desc:瞳孔 (mm);vt:;pvf:;" pid="qjjl-rtlist-000"></td>
              <td data-type="edit"><span style="color: #1885F2;cursor: pointer;" onclick="delTrFuc(this,'000')">删除</span></td>
            </tr>
          </tbody>
          <tbody class="live-con" data-type="preview"></tbody>
        </table>
      </div>
      <div class="qjjl-title">病人情况</div>
      <div class="layui-form-item layui-form-text">
        <textarea name="desc" class="rt-sr-w layui-textarea" id="qjjl-rt-29" placeholder="请输入" rt-sc="pageId:qjjl1;name:病人情况;wt:1;desc:病人情况;vt:;pvf:;code:453;"></textarea>
      </div>
      <div class="qjjl-title rt-sr-w" style="display: none;" id="qjjl-bzlist-1" rt-sc="pageId:qjjl1;name:处置医嘱;wt:;desc:处置医嘱;vt:;pvf:1;code:454;">
        处置医嘱
      </div>
      <div class="qjjl-title" style="display: flex;align-items: center;justify-content: space-between;" data-type="edit">
        <div>处置医嘱</div>
        <div class="add-btn" onclick="addBzHandler()"><i class="layui-icon"></i> 添加记录</div>
      </div>
      <div class="bz-list">
        <div class="bz-wrap">
          <div class="bz-con">
            <div class="bz-item" tab-target="qjjl-bzlist-000">
              <div class="drug-content rt-sr-w" id="qjjl-bzlist-000" rt-sc="pageId:qjjl1;name:医嘱记录;wt:;desc:医嘱记录;vt:;pvf:1;" pid="qjjl-bzlist-1">
                <div class="drug-top">
                  <div class="rt-sr-w" id="qjjl-bzlist-000-1" rt-sc="pageId:qjjl1;name:医嘱记录 1;wt:;desc:医嘱记录 1;vt:;pvf:;">医嘱记录 1</div>
                  <span class="close-icon" data-type="edit" onclick="delTab(this, '000')"><i
                    class="layui-icon"></i>删除</span>
                </div>
                <div class="form-content">
                  <div>记录时间：</div>
                  <div class="temperature"><input type="text" class="rt-sr-w layui-input" style="width: 242px" placeholder="请输入" id="qjjl-bzlist-000-2" rt-sc="pageId:qjjl1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:;" pid="qjjl-bzlist-000"></div>
                </div>
                <div class="form-content">
                  <div>医嘱内容：</div>
                  <div class="temperature"><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-bzlist-000-3" rt-sc="pageId:qjjl1;name:医嘱内容;wt:1;desc:医嘱内容;vt:;pvf:;" pid="qjjl-bzlist-000"></div>
                </div>
                <div class="form-content">
                  <div>医嘱复述：</div>
                  <div class="temperature"><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-bzlist-000-4" rt-sc="pageId:qjjl1;name:医嘱复述;wt:1;desc:医嘱复述;vt:;pvf:;" pid="qjjl-bzlist-000"></div>
                </div>
                <div class="sign-content" style="justify-content: space-between;margin-left: 16px;">
                  <div class="weight" style="width: 33%;">
                    <div style="width: 80px;text-align: right;">用法：</div>
                    <div class="temperature"><input type="text" class="rt-sr-w layui-input" style="width: 100%;" placeholder="请输入" id="qjjl-bzlist-000-5" rt-sc="pageId:qjjl1;name:用法;wt:1;desc:用法;vt:;pvf:;" pid="qjjl-bzlist-000"></div>
                  </div>
                  <div class="weight" style="width: 33%;">
                    <div style="min-width: 80px;">确认医生：</div>
                    <div data-type="edit" class="temperature showInt"><input type="text" class="rt-sr-w layui-input" style="width: 100%;" placeholder="请输入" id="qjjl-bzlist-000-6" rt-sc="pageId:qjjl1;name:确认医生;wt:1;desc:确认医生;vt:;pvf:;" pid="qjjl-bzlist-000"></div>
                    <div data-type="preview">
                      <input type="text" class="rt-sr-w layui-input" style="background: #FAFAFA;border: 0;" placeholder="请输入" id="qjjl-bzlist-000-6" rt-sc="pageId:qjjl1;name:确认医生;wt:1;desc:确认医生;vt:;pvf:;" pid="qjjl-bzlist-000">
                    </div>
                  </div>
                  <div class="weight" style="width: 33%;">
                    <div style="min-width: 80px;">确认护士：</div>
                    <div data-type="edit" class="temperature showInt"><input type="text" class="rt-sr-w layui-input" style="width: 100%;" placeholder="请输入" id="qjjl-bzlist-000-7" rt-sc="pageId:qjjl1;name:确认护士;wt:1;desc:确认护士;vt:;pvf:;" pid="qjjl-bzlist-000"></div>
                    <div data-type="preview">
                      <input type="text" class="rt-sr-w layui-input" style="background: #FAFAFA;border: 0;" placeholder="请输入" id="qjjl-bzlist-000-7" rt-sc="pageId:qjjl1;name:确认护士;wt:1;desc:确认护士;vt:;pvf:;" pid="qjjl-bzlist-000">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="qjjl-title rt-sr-w" id="qjjl-rt-002" rt-sc="pageId:qjjl1;name:处理结果;wt:;desc:处理结果;vt:;pvf:1;">处理结果</div>
      <div class="allergy-content" style="padding-left: 16px;">
        <div class="gray-item" style="margin-left: 0;">
          <label class="radio-label" for="qjjl-rt-16">
            <input type="checkbox" class="rt-sr-w" name="cljg" value="转急诊继续治疗" id="qjjl-rt-16" pid="qjjl-rt-002" rt-sc="pageId:qjjl1;name:转急诊继续治疗;wt:4;desc:转急诊继续治疗;vt:;pvf:;code:455;">
            <span class="rt-sr-lb">转急诊继续治疗</span>
          </label>
          <label class="radio-label" for="qjjl-rt-17">
            <input type="checkbox" class="rt-sr-w" name="cljg" value="转门诊继续治疗" id="qjjl-rt-17" pid="qjjl-rt-002" rt-sc="pageId:qjjl1;name:转门诊继续治疗;wt:4;desc:转门诊继续治疗;vt:;pvf:;code:455;">
            <span class="rt-sr-lb">转门诊继续治疗</span>
          </label>
          <label class="radio-label" for="qjjl-rt-18">
            <input type="checkbox" class="rt-sr-w" name="cljg" value="转回病房继续治疗" id="qjjl-rt-18" pid="qjjl-rt-002" rt-sc="pageId:qjjl1;name:转回病房继续治疗;wt:4;desc:转回病房继续治疗;vt:;pvf:;code:455;">
            <span class="rt-sr-lb">转回病房继续治疗</span>
          </label>
          <label class="radio-label" for="qjjl-rt-19">
            <input type="checkbox" class="rt-sr-w" name="cljg" value="生命体征平稳" id="qjjl-rt-19" pid="qjjl-rt-002" rt-sc="pageId:qjjl1;name:生命体征平稳;wt:4;desc:生命体征平稳;vt:;pvf:;code:455;">
            <span class="rt-sr-lb">生命体征平稳</span>
          </label>
        </div>
        <input type="text" style="margin-left: 16px;width: calc(100% - 32px);margin-top: 6px;" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-20" pid="qjjl-rt-002" rt-sc="pageId:qjjl1;name:处理结果;wt:1;desc:处理结果;vt:;pvf:;code:455;">
      </div>
      <div class="qjjl-title rt-sr-w" id="qjjl-rt-003" rt-sc="pageId:qjjl1;name:抢救结果;wt:;desc:抢救结果;vt:;pvf:1;">抢救结果</div>
      <input type="text" style="display: none;" class="rt-sr-w layui-input input-width" id="qiangjiuEffect" 
      placeholder="请选择" rt-sc="pageId:hlqj1;name:抢救效果;wt:1;desc:抢救效果;vt:;pvf:1;code:465">
      <div class="result-content">
        <div class="result-item" style="margin-right: 12px;">
          <div class="checkbox">
            <label class="check-label" for="qjjl-rt-21">
              <input type="checkbox" class="rt-sr-w" name="cpr" value="停止CPR" id="qjjl-rt-21" pid="qjjl-rt-003" rt-sc="pageId:qjjl1;name:停止CPR;wt:4;desc:停止CPR;vt:;pvf:;code:456;">
              <span class="rt-sr-lb">停止CPR</span>
            </label>
          </div>
          <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-22" rt-sc="pageId:qjjl1;name:停止CPR日期;wt:1;desc:停止CPR日期;vt:;pvf:;code:457;" pid="qjjl-rt-21">
        </div>
        <div class="result-item">
          <div class="checkbox">
            <label class="check-label" for="qjjl-rt-23">
              <input type="checkbox" class="rt-sr-w" name="ck-zzxh" value="自主循环恢复" id="qjjl-rt-23" pid="qjjl-rt-003" rt-sc="pageId:qjjl1;name:自主循环恢复;wt:4;desc:自主循环恢复;vt:;pvf:;code:458;">
              <span class="rt-sr-lb">自主循环恢复</span>
            </label>
          </div>
          <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-24" rt-sc="pageId:qjjl1;name:自主循环恢复日期;wt:1;desc:自主循环恢复日期;vt:;pvf:;code:459;" pid="qjjl-rt-23">
        </div>
      </div>
      <div class="result-content" style="margin-bottom: 12px;">
        <div class="result-item" style="margin-right: 12px;">
          <div class="checkbox">
            <label class="check-label" for="qjjl-rt-25">
              <input type="checkbox" class="rt-sr-w" name="ck-xgsw" value="宣告临床死亡" id="qjjl-rt-25" pid="qjjl-rt-003" rt-sc="pageId:qjjl1;name:宣告临床死亡;wt:4;desc:宣告临床死亡;vt:;pvf:;code:460;">
              <span class="rt-sr-lb">宣告临床死亡</span>
            </label>
          </div>
          <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-26" rt-sc="pageId:qjjl1;name:宣告临床死亡日期;wt:1;desc:宣告临床死亡日期;vt:;pvf:;code:461;" pid="qjjl-rt-25">
        </div>
        <div class="result-item">
          <div class="checkbox">
            <label class="check-label" for="qjjl-rt-27">
              <input type="checkbox" class="rt-sr-w" name="ck-fqqj" value="家属要求放弃抢救" id="qjjl-rt-27" pid="qjjl-rt-003" rt-sc="pageId:qjjl1;name:家属要求放弃抢救;wt:4;desc:家属要求放弃抢救;vt:;pvf:;code:462;">
              <span class="rt-sr-lb">家属要求放弃抢救</span>
            </label>
          </div>
          <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="qjjl-rt-28" rt-sc="pageId:qjjl1;name:家属要求放弃抢救日期;wt:1;desc:家属要求放弃抢救日期;vt:;pvf:;code:463;" pid="qjjl-rt-27">
        </div>
      </div>
      <div class="qjjl-title mt-20">记录信息</div>
      <div class="allergy-content sign-content" style="padding-bottom: 20px;padding-left: 6px;">
        <div class="weight">
          <div>记录人：</div>
          <div class="temperature">
            <div class="layui-inline showInt" data-type="edit">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;width: 190px;" id="qjjl-rt-005" rt-sc="pageId:qjjl1;name:记录人;wt:1;desc:记录人;vt:;pvf:;">
            </div>
            <span data-type="preview"data-key="qjjl-rt-005" ></span>
          </div>
        </div>
        <div class="weight">
          <div>记录时间：</div>
          <div class="temperature">
            <div>
              <input type="text" class="layui-input rt-sr-w" id="qjjl-rt-006" placeholder="记录时间" style="display: inline-block; width: 180px;" rt-sc="pageId:qjjl1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:;">
            </div>
          </div>
        </div>
        <input class="rt-sr-w" id="optInfo" style="display: none;" rt-sc="pageId:qjjl1;name:记录人;wt:1;desc:记录人;vt:;pvf:1;">
      </div>
    </div>
  </li>
</ul>