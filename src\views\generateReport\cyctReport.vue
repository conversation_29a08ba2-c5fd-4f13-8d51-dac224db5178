<template>
  <div class="report-wrap" id="report-wrap" :data-type="reportType">
    <div class="cyctReport rpt-wrap">
      <div class="rpt-header">
        <div class="main-title">
          <img width="100%" :src="`${publicPath}${configData.logoReportTypeMap[reportType]}`" alt="">
        </div>
        <div class="rpt-hospital-info" v-if="configData.departmentNamecy || configData.subTitlecy">
          <div class="depart-title" v-if="configData.departmentNamecy">{{configData.departmentNamecy}}</div>
          <div class="sub-title" v-if="configData.subTitlecy">{{configData.subTitlecy}}</div>
        </div>
      </div>
      <div class="mlr-30">
        <div class="patient-info">
          <div class="item">
            <span class="item-label">患者姓名：</span>
            <span class="item-info">{{patInfo.name}}</span>
          </div>
          <div class="item">
            <span class="item-label">性别：</span>
            <span class="item-info">{{patInfo.gender}}</span>
          </div>
          <div class="item">
            <span class="item-label">年龄：</span>
            <span class="item-info">{{patInfo.age}}</span>
          </div>
          <div class="item">
            <span class="item-label">出生日期：</span>
            <span class="item-info">{{patInfo.birthday ? patInfo.birthday.substr(0, 10) : ''}}</span>
          </div>
        </div>
        <div class="rpt-body">
          <div class="chart-title">
            <span style="color:#555;">检查设备：</span><span v-if="!isEditMode">{{formInfo.device}}</span>
            <el-select 
              placeholder="请选择" 
              size="mini" 
              v-model="formInfo.device" 
              @change="editHandler"
              v-if="isEditMode">
              <el-option
                v-for="(device, d) in deviceList" 
                :key="d"
                :label="device.name"  
                :value="device.name"  
              >
              </el-option>
            </el-select>
          </div>
          <div class="rpt-chart">
            <div class="chart-wrap right-chart">
              <div class="chart-side">
                <span class="side-name">右耳</span>
                <span v-if="!isEditMode">{{reportInfo.examDateAndTime ? reportInfo.examDateAndTime.slice(0,10) : ''}}</span>
                <el-date-picker
                  style="width: 200px;display: inline-block;"
                  size="mini"
                  v-model="reportInfo.examDateAndTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="date"
                  :picker-options="dateOption"
                  @change="editHandler"
                  v-if="isEditMode"
                  placeholder="选择日期">
                </el-date-picker>
              </div>
              <div class="chart" :id="'cyct_rightEar_' + sortIndex"></div>
            </div>
            <div class="chart-wrap left-chart">
              <div class="chart-side">
                <span class="side-name">左耳</span>
                <span v-if="!isEditMode">{{reportInfo.examDateAndTime ? reportInfo.examDateAndTime.slice(0,10) : ''}}</span>
                <el-date-picker
                  style="width: 200px;display: inline-block;"
                  size="mini"
                  v-model="reportInfo.examDateAndTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="date"
                  :picker-options="dateOption"
                  @change="editHandler"
                  v-if="isEditMode"
                  placeholder="选择日期">
                </el-date-picker>
              </div>
              <div class="chart" :id="'cyct_leftEar_' + sortIndex"></div>
            </div>
          </div>
          <div class="rpt-info">
            <div class="result-label">结果：</div>
            <div class="rpt-desc">
              <div style="white-space: pre-wrap;" v-if="!isEditMode">{{reportResult.description}}</div>
              <el-input :rows="8" type="textarea" 
                v-model="reportResult.description" 
                @blur="editHandler" 
                v-if="isEditMode"
              ></el-input>
            </div>
            <div style="font-size: 14px;color:#999">注：该检测结果仅反应受试者当时之情况</div>
          </div>
        </div>
        <div class="rpt-footer">
          <div class="chart-table">
            <div class="detail-table">
              <div>
                <div class="flag-title">纯音听阈均值(dB HL)</div>
                <div class="border-wrap">
                  <div class="tr">
                    <div class="bgGray"></div>
                    <div class="bgGray">气导</div>
                    <div class="bgGray">骨导</div>
                    <!--<div class="bgGray">AI</div>-->
                  </div>
                  <div class="tr">
                    <div class="bgGray">右</div>
                    <div class="border">{{reportResult.acr || reportResult.ACR}}</div>
                    <div class="border">{{reportResult.bcr || reportResult.BCR}}</div>
                    <!--<div class="border">{{reportResult.air}}</div>-->
                  </div>
                  <div class="tr">
                    <div class="bgGray">左</div>
                    <div class="border">{{reportResult.acl || reportResult.ACL}}</div>
                    <div class="border">{{reportResult.bcl || reportResult.BCL}}</div>
                    <!--<div class="border">{{reportResult.ail}}</div>-->
                  </div>
                </div>
              </div>
            </div>
            <div class="chart-flag">
              <div class="flag-title">图例</div>
              <div class="border-wrap">
                <div class="flag-list">
                  <div class="wrap">
                    <div style="color:#5F60F9">左</div>
                    <div></div>
                    <div style="color:#FE525D">右</div>
                    <div class="info"></div>
                    <div class="info">已屏蔽</div>
                  </div>
                  <div class="wrap" v-for="(item,i) in flagList" :key="i">
                    <div>
                      <img :src="item.leftIcon" alt="" v-if="item.leftIcon">
                    </div>
                    <div>
                      <img :src="item.centerIcon" alt="" v-if="item.centerIcon">
                    </div>
                    <div>
                      <img :src="item.rightIcon" alt="" v-if="item.rightIcon">
                    </div>
                    <div class="info">{{item.name}}</div>
                    <div class="info">
                      <img :src="item.coverIcon1" alt="" style="margin-right: 5px" v-if="item.coverIcon1">
                      <img :src="item.coverIcon2" alt="" v-if="item.coverIcon2">
                    </div>
                  </div>
                </div>
                <div class="tip">
                  <div style="padding-top:5px;box-sizing:border-box;">纯音测听</div>
                  <div class="tip-detail">
                    <div>气导:500,1k,2k</div>
                    <div>骨导:500,1k,2k</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="report-box">
            <div class="rpt-reporter">
              <span class="foot-label">检查者：</span>
              <span class="foot-info">
                <span v-if="!isEditMode">{{reportInfo.examStaff}}</span>
                <el-select 
                  size="mini"
                  style="width: 160px;display: inline-block;"
                  v-model="reportInfo.examStaff"
                  filterable 
                  :filter-method="filterExamUser"
                  placeholder="请选择"
                  @change="editHandler"
                  @visible-change="blurHandler"
                  v-if="isEditMode"
                >
                  <el-option
                    v-for="(item, i) in filterExamUserList"
                    :key="i"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
              </span>
            </div>
            <div class="rpt-reporter">
              <span class="foot-label">报告医生：</span>
              <span class="foot-info">
                <span v-if="!isEditMode">{{reportInfo.reportStaff}}</span>
                <el-select 
                  size="mini"
                  style="width: 160px;display: inline-block;"
                  v-model="reportInfo.reportStaff"
                  filterable 
                  :filter-method="filterReporter"
                  placeholder="请选择"
                  @change="editHandler"
                  @visible-change="blurHandler"
                  v-if="isEditMode"
                >
                  <el-option
                    v-for="(item, i) in filterReporterList"
                    :key="i"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
              </span>
            </div>
            <div class="rpt-reporter">
              <span class="foot-label">报告日期：</span>
              <span class="foot-info" v-if="!isEditMode">{{reportInfo.reportDateAndTime ? reportInfo.reportDateAndTime.slice(0,10) : ''}}</span>
              <el-date-picker
                style="width: 160px;display: inline-block;"
                size="mini"
                v-model="reportInfo.reportDateAndTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :picker-options="dateOption"
                @change="editHandler"
                v-if="isEditMode"
                placeholder="选择日期">
              </el-date-picker>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import leftIcon1 from '@/assets/images/flag/cyct_rpt/left_cross.png';
import leftIcon1_no from '@/assets/images/flag/cyct_rpt/left_cross_no.png';
import leftIcon2 from '@/assets/images/flag/cyct_rpt/left_direction.png';
import leftIcon2_no from '@/assets/images/flag/cyct_rpt/left_direction_no.png';
import leftIcon3 from '@/assets/images/flag/cyct_rpt/left_s.png';
import leftIcon3_no from '@/assets/images/flag/cyct_rpt/left_s_no.png';
import leftIcon4 from '@/assets/images/flag/cyct_rpt/left_m.png';
import leftIcon4_no from '@/assets/images/flag/cyct_rpt/left_m_no.png';
import leftIcon5 from '@/assets/images/flag/cyct_rpt/left_u.png';
import leftIcon5_no from '@/assets/images/flag/cyct_rpt/left_u_no.png';
import leftIcon6 from '@/assets/images/flag/cyct_rpt/left_arrow.png';

import centerIcon2 from '@/assets/images/flag/cyct_rpt/middle_direction.png';
import centerIcon3 from '@/assets/images/flag/cyct_rpt/middle_s.png';
import centerIcon6 from '@/assets/images/flag/cyct_rpt/middle_arrow.png';

import rightIcon1 from '@/assets/images/flag/cyct_rpt/right_circle.png';
import rightIcon1_no from '@/assets/images/flag/cyct_rpt/right_circle_no.png';
import rightIcon2 from '@/assets/images/flag/cyct_rpt/right_direction.png';
import rightIcon2_no from '@/assets/images/flag/cyct_rpt/right_direction_no.png';
import rightIcon3 from '@/assets/images/flag/cyct_rpt/right_s.png';
import rightIcon3_no from '@/assets/images/flag/cyct_rpt/right_s_no.png';
import rightIcon4 from '@/assets/images/flag/cyct_rpt/right_m.png';
import rightIcon4_no from '@/assets/images/flag/cyct_rpt/right_m_no.png';
import rightIcon5 from '@/assets/images/flag/cyct_rpt/right_u.png';
import rightIcon5_no from '@/assets/images/flag/cyct_rpt/right_u_no.png';
import rightIcon6 from '@/assets/images/flag/cyct_rpt/right_arrow.png';

import coverIcon1_1 from '@/assets/images/flag/cyct_rpt/left_squre.png';
import coverIcon1_1_no from '@/assets/images/flag/cyct_rpt/left_squre_no.png';

import coverIcon1_2 from '@/assets/images/flag/cyct_rpt/right_triangle.png';
import coverIcon1_2_no from '@/assets/images/flag/cyct_rpt/right_triangle_no.png';

import coverIcon2_1 from '@/assets/images/flag/cyct_rpt/left_gudao.png';
import coverIcon2_1_no from '@/assets/images/flag/cyct_rpt/left_gudao_no.png';

import coverIcon2_2 from '@/assets/images/flag/cyct_rpt/right_gudao.png';
import coverIcon2_2_no from '@/assets/images/flag/cyct_rpt/right_gudao_no.png';

import coverIcon3_1 from '@/assets/images/flag/cyct_rpt/left_shengchang.png';
import coverIcon3_1_no from '@/assets/images/flag/cyct_rpt/left_shengchang_no.png';

import coverIcon3_2 from '@/assets/images/flag/cyct_rpt/right_shengchang.png';
import coverIcon3_2_no from '@/assets/images/flag/cyct_rpt/right_shengchang_no.png';

// 声场助听部分
import leftIconA from '@/assets/images/flag/cyct_rpt/left_a.png';
import leftIconA_no from '@/assets/images/flag/cyct_rpt/left_a_no.png';
import leftIconA_2 from '@/assets/images/flag/cyct_rpt/left_a_2.png';
import leftIconA_2_no from '@/assets/images/flag/cyct_rpt/left_a_2_no.png';
import leftIconLa from '@/assets/images/flag/cyct_rpt/left_la.png';
import leftIconLa_no from '@/assets/images/flag/cyct_rpt/left_la_no.png';
import leftIconLa_2 from '@/assets/images/flag/cyct_rpt/left_la_2.png';
import leftIconLa_2_no from '@/assets/images/flag/cyct_rpt/left_la_2_no.png';

import rightIconA from '@/assets/images/flag/cyct_rpt/right_a.png';
import rightIconA_no from '@/assets/images/flag/cyct_rpt/right_a_no.png';
import rightIconA_2 from '@/assets/images/flag/cyct_rpt/right_a_2.png';
import rightIconA_2_no from '@/assets/images/flag/cyct_rpt/right_a_2_no.png';
import rightIconAr from '@/assets/images/flag/cyct_rpt/right_ar.png';
import rightIconAr_no from '@/assets/images/flag/cyct_rpt/right_ar_no.png';
import rightIconAr_2 from '@/assets/images/flag/cyct_rpt/right_ar_2.png';
import rightIconAr_2_no from '@/assets/images/flag/cyct_rpt/right_ar_2_no.png';

import api from '@/config/api.js';
import { request } from '@/utils/common.js';
import generateReportMixin from '@/mixins/generateReport.js';
import {inferAgeByBirthday} from 'rt-common-functions'
import dayjs from 'dayjs';
export default {
  name: "cyctReport",
  mixins: [generateReportMixin],
  props: {
    sortIndex: {
      type: Number,
      default: 0
    },
    reportType: {
      type: String,
      default: ''
    },
    patInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    reportInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    reportData: {
      type: Array,
      default() {
        return []
      }
    },
    reportResult: {
      type: [Object, Array],
      default() {
        return {}
      }
    },
  },
  watch: {
    reportType: {
      async handler(newVal) {
        if(this.cyctReportTypeList.includes(newVal)) {
          this.initReport();
          if(this.patInfo.birthday && !this.patInfo.age) {
            let age = inferAgeByBirthday(this.patInfo.birthday);
            this.$set(this.patInfo, 'age', age);
          }
          let device = '';
          if(this.reportData && this.reportData.length) {
            device = this.reportData[0].device || '';
          }else if(this.deviceList && this.deviceList.length) {
            device = this.deviceList[0].name || '';
          }
          this.$set(this.formInfo, 'device',device);
          let {examDate = '', examTime = '', reportDate = '', reportTime = ''} = this.reportInfo;
          let examDateAndTime = examDate ? `${examDate} ${examTime}` : this.nowDate;
          this.$set(this.reportInfo, 'examDateAndTime', examDateAndTime);
          let reportDateAndTime = reportDate ? `${reportDate} ${reportTime}` : this.nowDate;
          this.$set(this.reportInfo, 'reportDateAndTime', reportDateAndTime);
          this.examUser = this.reportInfo.examStaff || '';
          this.reporter = this.reportInfo.reportStaff || '';
          this.editHandler && this.editHandler();
          let transKeyList = ['ACR', 'BCR', 'ACL', 'BCL'];
          this.tansParamToUpper(this.reportResult, transKeyList);
        }
      },
      immediate: true
    },
  },
  computed: {
    configData() {
      return this.$store.state.generateReport.configData;
    },
    deviceList() {
      return configData.deviceList;
    },
    isEditMode() {
      return this.$store.state.generateReport.isEditMode;
    },
    dateOption() {
      return this.$store.state.generateReport.dateOption;
    },
    nowDate() {
      return this.$store.state.generateReport.nowDate;
    },
  },
  data() {
    return {
      publicPath: process.env.BASE_URL,
      flagList: [
        {leftIcon: leftIcon1, centerIcon: '', rightIcon: rightIcon1, name: '气导', coverIcon1: coverIcon1_1, coverIcon2: coverIcon1_2},
        {leftIcon: leftIcon2, centerIcon: centerIcon2, rightIcon: rightIcon2, name: '骨导', coverIcon1: coverIcon2_1, coverIcon2: coverIcon2_2},
        {leftIcon: leftIcon3, centerIcon: centerIcon3, rightIcon: rightIcon3, name: '声场', coverIcon1: coverIcon3_1, coverIcon2: coverIcon3_2},
        {leftIcon: leftIcon4, centerIcon: '', rightIcon: rightIcon4, name: '舒适阈', coverIcon1: '', coverIcon2: ''},
        {leftIcon: leftIcon5, centerIcon: '', rightIcon: rightIcon5, name: '不舒适阈', coverIcon1: '', coverIcon2: ''},
        {leftIcon: leftIcon6, centerIcon: centerIcon6, rightIcon: rightIcon6, name: '无响应', coverIcon1: '', coverIcon2: ''},
      ],
      leftEar: {
        '气导': {icon: leftIcon1, coverIcon: coverIcon1_1, iconNo: leftIcon1_no, coverIconNo: coverIcon1_1_no},
        '骨导': {icon: leftIcon2, coverIcon: coverIcon2_1, iconNo: leftIcon2_no, coverIconNo: coverIcon2_1_no},
        '声场': {icon: leftIcon3, coverIcon: coverIcon3_1, iconNo: leftIcon3_no, coverIconNo: coverIcon3_1_no},
        '助听声场': {icon: leftIconA, iconNo: leftIconA_no, aideIcon: leftIconA_2, aideIconNo: leftIconA_2_no, coverIcon: leftIconLa, coverIconNo: leftIconLa_no, coverAideIconNo: leftIconLa_2_no},
        '舒适阈': {icon: leftIcon4, iconNo: leftIcon4_no},
        '不舒适阈': {icon: leftIcon5, iconNo: leftIcon5_no},
        '无响应': {icon: leftIcon6},
      },
      rightEar: {
        '气导': {icon: rightIcon1, coverIcon: coverIcon1_2, iconNo: rightIcon1_no, coverIconNo: coverIcon1_2_no},
        '骨导': {icon: rightIcon2, coverIcon: coverIcon2_2, iconNo: rightIcon2_no, coverIconNo: coverIcon2_2_no},
        '声场': {icon: rightIcon3, coverIcon: coverIcon3_2, iconNo: rightIcon3_no, coverIconNo: coverIcon3_2_no},
        '助听声场': {icon: rightIconA, iconNo: rightIconA_no, aideIcon: rightIconA_2, aideIconNo: rightIconA_2_no, coverIcon: rightIconAr, coverIconNo: rightIconAr_no, coverAideIcon: rightIconAr_2, coverAideIconNo: rightIconAr_2_no},
        '舒适阈': {icon: rightIcon4, iconNo: rightIcon4_no},
        '不舒适阈': {icon: rightIcon5, iconNo: rightIcon5_no},
        '无响应': {icon: rightIcon6},
      },
      examNo: '',
      formInfo: {
        device: '',
      },
      xAxisData: ["125","250","500","750",'1000', '1500', '2000', '3000', '4000', '6000', '8000'],
      xIndex: [0,1,2,3,4,5,6,7,8,9,10],
      cyctReportTypeList: ['C001', 'C002', 'C005', 'C006', 'C009', 'C010'], // 属于纯声测听模板的id列表
      ztscReportTypeList: ['C002', 'C006', 'C010'], // 助听声场模板id列表
    }
  },
  methods: {
    // 初始化
    async initReport() {
      this.examNo = this.localQueryParams.examNo || this.localQueryParams.busId;
      // this.$store.dispatch('generateReport/getExamUserList', {vm: this, params: {}})
      this.initEarChart('右耳', 'rightEar', 'cyct_rightEar_' + this.sortIndex);
      this.initEarChart('左耳', 'leftEar', 'cyct_leftEar_' + this.sortIndex);
    },
    // 获取数据
    getDataByEarSide(earSide, earSideCode) {
      let reportData = this.reportData.filter(item => item.earSide === earSide);
      let visualMap = [];
      let series = reportData.map((item, j) => {
        let name = item.signalOutput;
        if(name === '声场' && this.ztscReportTypeList.includes(this.reportType)) {
          name = "助听声场";
        }
        let data = [];
        let aidedNo = item.aidedNo;
        let noResponseList = [];  //无响应的点下标，即不连线的点
        let coordIndexList = [];  //所有点下标
        item.curve.forEach((cur, i) => {
          let symbol = cur.occultation === '1' ? this[earSideCode][name].coverIcon : this[earSideCode][name].icon;
          // noResponse为1不连线
          let index = this.xAxisData.indexOf(cur.frequency);
          coordIndexList.push(index);
          if(cur.noResponse === '1') {
            if(!noResponseList.includes(index)) {
              noResponseList.push(index);
            }
          }
          if(aidedNo && aidedNo.endsWith('2')) {
            if(cur.occultation === '1') {
              symbol = this[earSideCode][name].coverAideIcon || symbol;
              if(cur.noResponse === '1' && this[earSideCode][name].coverAideIconNo) {
                symbol = this[earSideCode][name].coverAideIconNo;
              }
            }
            if(cur.occultation !== '1') {
              symbol = this[earSideCode][name].aideIcon || symbol;
              if(cur.noResponse === '1' && this[earSideCode][name].aideIconNo) {
                symbol = this[earSideCode][name].aideIconNo;
              }
            }
          } else {
            if(cur.noResponse === '1') {
              if(cur.occultation === '1' && this[earSideCode][name].coverIconNo) {
                symbol = this[earSideCode][name].coverIconNo;
              }
              if(cur.occultation !== '1' && this[earSideCode][name].iconNo) {
                symbol = this[earSideCode][name].iconNo;
              }
            }
          }
          data.push({
            value: [cur.frequency, cur.intensity],
            symbol: `image://${symbol}`,
          })
        });
        visualMap.push(this.computePieces(noResponseList, j, earSide === '左耳' ? '#6565FF' : '#FF1D1C', data, coordIndexList));
        return {
          name: name.includes('声场') ? '声场' : name,
          type: 'line',
          data,
          symbolSize: 25,
          // color: earSide === '左耳' ? '#6565FF' : '#FF1D1C',
        }
      })
      return {series, visualMap};
    },

    computePieces(noResponseList, seriesIndex, color, reportData, coordIndexList) {
      let piecesObj = {
        show: false,
        dimension: 0,
        seriesIndex: seriesIndex,
      };
      let pieces = [];
      // noResponseList = Array.from(new Set(noResponseList)).sort((a, b) => {return a - b});
      if(!noResponseList.length) {
        pieces = [{
          gte: 0, 
          lte: this.xAxisData.length - 1,
          color: color
        }]
      } else if(noResponseList.length === this.xAxisData.length) {
        pieces = [{
          gte: 0, 
          lte: this.xAxisData.length - 1,
          color: 'transparent'
        }]
      } else {
        // 如果上一个或本身无响应，则不连线
        coordIndexList.forEach((coord, i) => {
          if(i > 0) {
            let lastCoord = coordIndexList[i-1];  //上一个
            pieces.push({
              lt: coord,
              color: noResponseList.includes(coord) || noResponseList.includes(lastCoord) ? 'transparent' : color
            })
          }
        })
      }
      return {...piecesObj, pieces};
    },
    // 图表
    initEarChart(earSide, earSideCode, chartId) {
      let {series, visualMap} = this.getDataByEarSide(earSide, earSideCode);
      if(!series) {
        return;
      }
      // 指定图表的配置项和数据
      let option = {
        animation: false,
        grid: {
          y: 20,
          x: 50,
          x2: 20,
          borderColor: '#000',
          borderWidth: 1,
          show: true
        },
        title: {
          show: false,
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/> {c}',
          borderColor: earSide === '左耳' ? '#6565FF' : '#FF1D1C',
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            name: '频率(Hz)',
            nameLocation: 'middle',
            nameGap: 35,
            axisLine: {
              show: false,
              lineStyle: {
                color: '#000'
              }
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#ccc'
              }
            },
            axisLabel: {
              interval: 0,//代表显示所有x轴标签显示
              rotate: 45,
              color: '#000',
            },
            // min: 0,
            // max: 8000,
            data: this.xAxisData,
          }
        ],
        yAxis: [{
            name: '听力值(dB)',
            nameLocation: 'middle',
            nameGap: 30,
            inverse: true,
            min: -10,
            max: 120,
            splitNumber: 10,
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#000'
              }
            },
            axisLabel: {
              color: '#000',
            },
            splitLine: {
              lineStyle: {
                color: '#ccc'
              }
            }
        }],
        series: series,
        visualMap
      };
      this.$nextTick(() => {
        this.$store.dispatch('generateReport/createChart', {domIdName: chartId, option, vm: this})
      })
    },
    // 编辑
    editHandler() {
      this.examUser = this.reportInfo.examStaff;
      this.reporter = this.reportInfo.reportStaff;
      let now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      let [nowDay, nowTime] = now.split(' ');
      let [examDate, examTime] = this.reportInfo.examDateAndTime ? this.reportInfo.examDateAndTime.split(' ') : [];
      let [reportDate, reportTime] = this.reportInfo.reportDateAndTime ? this.reportInfo.reportDateAndTime.split(' ') : [];

      let params = {
        reportStaff: this.reportInfo.reportStaff,
        description: this.reportResult.description,
        device: this.formInfo.device,
        examDate: examDate,
        examTime: examDate === nowDay ? nowTime : examTime,
        reportDate: reportDate,
        reportTime: reportDate === nowDay ? nowTime : reportTime,
        examStaff: this.reportInfo.examStaff,
        reportType: this.reportType,
        rptImpression: this.reportResult.description
      }
      this.$store.commit('generateReport/setSaveParams', params);
    },
  }
}
</script>
<style>
.cyctReport .rpt-body .rpt-desc .el-textarea__inner {
  color: #000;
  font-family: Microsoft YaHei;
}
.cyctReport .chart-side .el-input__inner {
  border: none;
}
</style>
<style scoped>
.report-wrap {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20px;
}
.cyctReport {
  width: 780px;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  min-height: 1100px;
  padding-bottom: 270px;
  box-sizing: border-box;
  color: #000;
  position: relative;
}
.cyctReport .chart-side {
  width: 278px;
  margin-left: 50px;
  height: 28px;
  display: flex;
  align-items: center;
  color: #333;
  font-size: 12px;
  box-sizing: border-box;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  overflow: hidden;
}
.cyctReport .side-name {
  width: 60px;
  background: #D6DFF7;
  text-align: center;
  height: 100%;
  line-height: 26px;
  margin-right: 10px;
  border-right: 1px solid #D6DFF7;
}
.cyctReport .rpt-header {
  text-align: center;
  color: #000;
  overflow: hidden;
}
.cyctReport .rpt-header .main-title {
  margin-left: -15px;
  margin-right: -15px;
}
.cyctReport .rpt-header .sub-title {
  font-size: 16px;
  color: #000;
}
.cyctReport .patient-info {
  display: flex;
  padding: 10px 10px 10px 0;
  justify-content: space-between;
  border-top: 1px solid #F2F2F2;
  border-bottom: 1px solid #F2F2F2;
  margin-top: 16px;
  box-sizing: border-box;
  font-size: 12px;
}
.cyctReport .patient-info .item .item-label {
  margin-right: 5px;
  color: #555;
}
.cyctReport .patient-info .item .item-info {
 color: #000;
}
.cyctReport .rpt-body {
  margin-top: 10px;
}
.cyctReport .rpt-body .rpt-desc {
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;
  color: #000;
}
.cyctReport .rpt-body .rpt-desc > .i-text {
  font-size: 14px;
  margin-bottom: 5px;
  font-style: italic;
}
.cyctReport .rpt-body .rpt-chart {
  display: flex;
  justify-content: space-between;
}
.cyctReport .rpt-body .rpt-chart .chart-wrap {
  width: 48%;
}
.cyctReport .rpt-body .chart-title {
  text-align: left;
  font-size: 12px;
  margin-bottom: 10px;
}
.cyctReport .rpt-body .rpt-chart .chart {
  box-sizing: border-box;
  height: 380px;
}
.cyctReport .rpt-footer .detail-table {
  box-sizing: border-box;
  width: 135px;
  display: inline-block;
  margin-right: 10px;
}
.cyctReport .rpt-footer .chart-table {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
.cyctReport .rpt-footer .chart-flag {
  box-sizing: border-box;
  width: 185px;
  display: inline-block;
}
.cyctReport .rpt-footer .flag-title {
  background: #D6DFF7;
  text-align: center;
  padding: 3px 0;
  border: 1px solid #666;
  border-bottom: none;
}
.cyctReport .rpt-footer .border-wrap {
  box-sizing: border-box;
  border: 1px solid #666;
  font-size: 12px;
}
.cyctReport .rpt-footer .wrap {
  display: flex;
  justify-content: space-between;
}
.cyctReport .rpt-footer .wrap > div {
  height: 18px;
  line-height: 18px;
  text-align: center;
  flex: 1;
}
.cyctReport .rpt-footer .wrap img {
  width: 18px;
  vertical-align: middle;
}
.cyctReport .rpt-footer .wrap div.info {
  width: 60px;
  flex: unset;
}
.cyctReport .rpt-footer .tip {
  display: flex;
  padding-left: 10px;
  box-sizing: border-box;
  justify-content: space-between;
}
.cyctReport .rpt-footer .tip .tip-detail{
  box-sizing: border-box;
  padding-top: 5px;
  padding-right: 10px;
  padding-left: 10px;
  border-top: 1px solid #666;
}
/**.cyctReport .rpt-footer .test-way {
  box-sizing: border-box;
  border-top: 1px solid #666;
  margin-top: 10px;
  padding: 5px 10px;
}**/
.cyctReport .rpt-footer .border-wrap .tr {
  display: flex;
}
.cyctReport .rpt-footer .border-wrap .tr > div {
  flex: 1;
  text-align: center;
  height: 25px;
  line-height: 25px;
}
.cyctReport .rpt-footer .border-wrap .tr > div.bgGray {
  background: #eee;
}
.cyctReport .rpt-footer .border-wrap .tr > div.border {
  border-top: 1px solid #666;
  border-left: 1px solid #666;
  box-sizing: border-box;
}
.cyctReport .rpt-footer {
  position: absolute;
  bottom: 30px;
  right: 30px;
  left: 30px;
  font-size: 12px;
}
.cyctReport .rpt-footer .foot-label {
  color: #555;
}
.cyctReport .rpt-footer .foot-info {
  color: #000;
}
.cyctReport .report-box {
  margin-top: 10px;
  padding-top: 6px;
  border-top: 1px solid #F2F2F2;
  display: flex;
  justify-content: space-between;
}
.cyctReport .rpt-reporter {
  flex: 1;
}
.cyctReport .rpt-reporter + .rpt-reporter {
  margin-left: 12px;
}
.result-label {
  font-size: 16px;
  color: #000;
  margin-bottom: 12px;
}
</style>