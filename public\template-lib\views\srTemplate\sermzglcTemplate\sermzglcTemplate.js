$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; //结果集，用于判断是否为新报告
// var bzTitleCloneMap = null;   //作为病灶示例模板的title
// var bzConCloneMap = null;  //作为病灶示例模板具体内容
var bzTitleAndConCloneMap = {}; // 作为病灶示例模板的title、作为病灶示例模板具体内容
var isSavedReport = false; //模板是否填写过
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || {}) : {}; // 结果集
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    initPage();
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      
    } else {
      // 获取推导结果
      curElem.find('#sermzglc1 .rpt-con .rt-sr-w').change(function() {
        getImpCon();
        getZgGjDesc();
        clickNmnj('gqzwxbb');
      })
    }
  }
}

// 页面初始化
function initPage() {
  // 切换tab
  switchTabContent();
  changeLcTabContent();
  changeLcfjbbTabCon();
  
  // 设置默认值
  setDefaultVal();
  showLcfjbbRad();

  // 添加点击方法
  inferBbhsImp();
  clickJchsFun();

  var bzTypeList = [{name:'子宫局灶性病变',type: 'zgjzxbb'},{name:'宫腔占位性病变',type: 'gqzwxbb'}]
  for(let i=0;i<bzTypeList.length;i++) {
    let item = bzTypeList[i];
    let type = item.type;
    // 初始化将病灶参照代码复制存储起来
    initBzCloneEle(type);
    // 切换病灶
    toggleBzHandler(type);
    // 回显病灶内容
    displayBzContent(type);
  }

  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);
}

// 默认勾选上子宫
function setDefaultVal() {
  $('#sermzglc1 .tab-ls .tab-i.act input').prop('checked',true);
}

// 主框架tab切换
function switchTabContent() {
  curElem.find(".tab-i", "input::after").on("click", function (e) {
    if ($(this).find('.rt-sr-w').attr('type') === 'checkbox') {
      e.stopPropagation();
      e.preventDefault();
      var checkbox = $(this).find('.rt-sr-w');
      if (!checkbox.is(":checked")) {
        checkbox.click();
      }
      showPage(this);
    }
  })
  curElem.find(".tab-i").on("click", ".rt-sr-w", function (e) {
    if ($(this).attr('type') === 'checkbox') {
      e.stopPropagation();
    }
  })
  curElem.find(".tab-i").on("change", ".rt-sr-w", function (e) {
    if ($(this).attr('type') === 'checkbox') {
      $(this).parents('.tab-i').addClass('act');
      $(this).parents('.tab-i').siblings().removeClass('act');
    }
    showPage(this);
  })
}
// 展示对应页面
function showPage(that) {
  if($(that).find('input').is(':checked')) {
    $(that).addClass('act').siblings('.tab-i').removeClass('act');
    $('.sermzglc-b .tab-con').removeClass('act');
    var tabName = $(that).attr('tab-name');
    $('.sermzglc-b .tab-con[id="'+tabName+'"]').addClass('act');
  }
}

/** 子宫start **/ 
// 肌层回声,切换单选框，清除病灶
function clickJchsFun() {
  $('.sermzglc-b [name="jchs-type"]').on('click',function() {
    if($(this).is(':checked')) {
      let val = $(this).val();
      if(val === '均匀') {
        let bzTabList = $('.sermzglc-b .zgjzxbb-bz .bz-wrap .bz-tit-ls .bz-tit-i');
        if(rtStructure && rtStructure.idAndDomMap) {
          bzTabList.each(function(idx,e) {
            console.log($(e))
            let tabId = $(e).attr('tab-id');
            for(var key in rtStructure.idAndDomMap) {
              if(key.indexOf(tabId) > -1) {
                delete rtStructure.idAndDomMap[key];
              }
            }
          })
        }
        $('.sermzglc-b .zgjzxbb-bz .bz-wrap .bz-tit-ls').html('');
        $('.sermzglc-b .zgjzxbb-bz .bz-wrap .bz-con').html('');
      }
    }
  })
}
// 内膜——内见
function clickNmnj(type) {
  let flag = false;
  if(type === 'gqzwxbb') {
    let nmnjVal = getVal('[name="nmnj"]:checked');
    // if(!nmnjVal) {
    //   $('#sermzglc1 .sub-lab-item').css('border-bottom','none');
    //   $('#sermzglc1 .sub-lab-item').css('background','#EBEEF5');
    //   $(`#sermzglc1 .nm-item-box`).hide();
    //   flag = true;
    // }else {
    //   $(`#sermzglc1 .nm-item-box`).show();
    //   $('#sermzglc1 .sub-lab-item').css('background','#F5F7FA');
    //   $('#sermzglc1 .sub-lab-item').css('border-bottom','1px solid #C8D7E6');
    //   addBzHandler('gqzwxbb');
    // }
  }
  return flag;
}

// 切换病灶
function toggleBzHandler(type) {
  $(`#sermzglc1 .${type}-bz .bz-wrap`).on('click', '.bz-tit-i', function(e) {
    var target = $(this).attr('tab-id');
    $(this).siblings('.bz-tit-i').removeClass('act');
    $(this).addClass('act');
    $(`#sermzglc1 .${type}-bz .bz-item[tab-target=${target}]`).siblings(`#sermzglc1 .${type}-bz .bz-item`).removeClass('act');
    $(`#sermzglc1 .${type}-bz .bz-item[tab-target=${target}]`).addClass('act');
  })
}

// 复制暂存病灶的具体模板示例内容，为后续添加病灶做准备
function initBzCloneEle(type) {
  var bzTitle = curElem.find(`#sermzglc1 .${type}-bz .bz-wrap .bz-tit-i`).clone(true);  //作为病灶示例模板的title
  var bzTitleClone = `<div class="bz-tit-i act" tab-id=sermzg-rt-${type}-0000>`+bzTitle.html()+`</div>`;
  var bzCon = curElem.find(`#sermzglc1 .${type}-bz .bz-wrap .bz-item`).clone(true);
  var bzConClone = `<div class="bz-item act" tab-target="sermzg-rt-${type}-0000">`+bzCon.html()+`</div>`;
  if(!JSON.stringify(bzTitleAndConCloneMap[type])) {
    bzTitleAndConCloneMap[type] = {};
  }
  bzTitleAndConCloneMap[type] =  { bzTitleClone: bzTitleClone,bzConClone: bzConClone }
  curElem.find(`#sermzglc1 .${type}-bz .bz-wrap .bz-tit-ls`).html('');
  curElem.find(`#sermzglc1 .${type}-bz .bz-wrap .bz-con`).html('');
}

// 回显病灶内容
function displayBzContent(type) {
  if(resultData && resultData.length) {
    let pid = type === 'zgjzxbb' ? 'sermzg-rt-23' : 'sermzg-rt-100';
    let zgPageList = resultData.filter(item => item.id === 'sermzglc-rt-5');
    let zgPageData = zgPageList.length ? zgPageList[0].child : [];
    let curParData = [],childData = [];
    if(type === 'gqzwxbb') {
      curParData = zgPageData.filter(item => item.id === 'sermzg-rt-97');
    }else if(type === 'zgjzxbb') {
      curParData = zgPageData.filter(item => item.id === 'sermzg-rt-21');
    }
    childData = curParData.length ? curParData[0].child : [];
    var bzData = childData.filter(bzItem => bzItem.id === pid);
    if(bzData && bzData.length) {
      var bzList = bzData[0]?.child.filter(bzItem => bzItem.name === '病灶title') || [];
      if(!bzList.length) {
        $(`#sermzglc1 .${type}-bz .bz-wrap`).hide();
      }
      bzList.forEach((item) => {
        var paneId = item.id.replace(`sermzg-rt-${type}-`, '');
        addBzHandler(type,paneId);
      })
    }
  }else {
    addBzHandler(type);
  }
}

/**
 * 新增病灶
 * type  子宫局灶性病变:zgjzxbb  宫腔占位性病变:gqzwxbb
 * oldPaneId 已保存过的
**/
function addBzHandler(type,oldPaneId) {
  // 内膜——内见未选，不可点击【添加病灶】
  let flag = clickNmnj(type);
  if(flag) return;
  
  $(`#sermzglc1 .${type}-bz .bz-wrap`).show();
  var paneId = oldPaneId || createUUidFun();
  var activeTab = 'sermzg-rt-' + type + '-' + paneId;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  var {newPaneBlock} = appendBzHtml(type, paneId, oldIdAndDom);
  var bzLen = $(`#sermzglc1 .${type}-bz .bz-tit-ls .bz-tit-i`).length;
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    let pid = type === 'zgjzxbb' ? 'sermzg-rt-23' : 'sermzg-rt-100';
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '病灶',
      name: '病灶title',
      pid: pid,
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : '病灶' +(bzLen+1),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $(`#sermzglc1 .${type}-bz .bz-item[tab-target=${activeTab}]`).find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
    document.querySelector(`#sermzglc1 .${type}-bz .bz-wrap .bz-tit-ls`).scrollLeft = document.querySelector(`#sermzglc1 .${type}-bz .bz-wrap .bz-tit-ls`).scrollWidth;
  }
}

// 病灶的交互
function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    // newPaneBlock.find('.def-ck').click();
  }
  // 获取推导结果
  curElem.find('#sermzglc1 .rpt-con .rt-sr-w').change(function() {
    getImpCon();
  })
}

// 处理新增病灶的html
function appendBzHtml(type, paneId) {
  $(`#sermzglc1 .${type}-bz .bz-tit-ls .bz-tit-i`).removeClass('act');
  $(`#sermzglc1 .${type}-bz .bz-con .bz-item`).removeClass('act');
  var reg = new RegExp('0000', 'ig');
  var title = bzTitleAndConCloneMap[type]['bzTitleClone'].replace(reg, paneId);   //作为病灶示例模板的title
  var content = bzTitleAndConCloneMap[type]['bzConClone'].replace(reg, paneId);  //作为病灶示例模板具体内容
  $(`#sermzglc1 .${type}-bz .bz-tit-ls`).append(title);
  $(`#sermzglc1 .${type}-bz .bz-con`).append(content);
  var newPaneBlock = $(`#sermzglc1 .${type}-bz .bz-item[tab-target=sermzg-rt-${type}-${paneId}]`);
  setBzTitleNo(type,paneId);
  return {newPaneBlock};
}

// 设置病灶标题序号
function setBzTitleNo(type,paneId) {
  var bzTitleDom = $(`#sermzglc1 .${type}-bz .bz-tit-ls .bz-tit-i`);
  bzTitleDom.each(function(i,dom) {
    var tabId = $(dom).attr('tab-id');
    var titleIndex = i + 1;
    if(tabId === `sermzg-rt-${type}-${paneId}`) {
      $(`#sermzglc1 .${type}-bz .bz-tit-ls .bz-tit-i[tab-id=sermzg-rt-${type}-${paneId}] .bz-name`).html('病灶'+titleIndex);
    }
  })
}

// 删除病灶
var willBzVm = null;
function delTab(vm, paneId,type) {
  willBzVm = vm;
  var idx = $(`#sermzglc1 .${type}-bz .bz-tit-i .close-icon`).index(vm);
  var dialogContent = `<div style="">确认删除病灶${idx+1}?</div>`;
  dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  dialogContent += '<button onclick="removeBzHandler(\''+paneId+'\',\''+type+'\')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  dialogContent += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消</button>';
  dialogContent += '</div>';
  drawDialog({
    title: '提示',
    content: dialogContent,
    modal: true
  })
}

// 确认删除病灶
function removeBzHandler(paneId,type) {
  var vm = willBzVm;
  var idx = $(`#sermzglc1 .${type}-bz .bz-tit-i .close-icon`).index(vm);
  var isAct = $(vm).closest('.bz-tit-i').hasClass('act');
  $(`#sermzglc1 .${type}-bz .bz-tit-i:eq(${idx})`).remove();
  $(`#sermzglc1 .${type}-bz .bz-item:eq(${idx})`).remove();
  var resetBzLen = $(`#sermzglc1 .${type}-bz .bz-item`).length;
  if(resetBzLen > 0 && isAct) {
    let nextId = idx >= 1 ? idx - 1 : idx;
    $(`#sermzglc1 .${type}-bz .bz-tit-i:eq(${nextId})`).addClass('act');
    $(`#sermzglc1 .${type}-bz .bz-item:eq(${nextId})`).addClass('act');
  }
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
  removeRtDialog();
  getImpCon();
  if(resetBzLen > 0) {
    $(`#sermzglc1 .${type}-bz .bz-name`).each(function(i, dom) {
      $(dom).text('病灶' + (i+1));
    });
    $(`#sermzglc1 .${type}-bz .bz-wrap`).show();
  } else {
    $(`#sermzglc1 .${type}-bz .bz-wrap`).hide();
  }
}

// 1.描述：界面选择什么内容就一起拼接累加起来
// 2.超声提示：根据选项里面的提示选择后，按顺序累加起来。
// 子宫——位置
function getZgSiteDesc() {
  let str = getVal('[name="site-type"]:checked');
  return str;
}
// 子宫——大小
function getZgDxDesc() {
  let str = '', strArr = [], inpValArr = [];
  // 大小输入框
  let zgdxInpList = $('.zgdx-inp input');
  zgdxInpList.each(function(i,dom) {
    let val = $(dom).val();
    val && inpValArr.push(val + 'mm');
  })
  inpValArr.length && strArr.push('大小' + inpValArr.join('×'));

  // 大小正常
  let dxzcStr = getVal('[name="dxzc"]:checked')
  dxzcStr && strArr.push('大小' + dxzcStr);
  str = strArr.length && strArr.join('，');
  return str;
}
// 子宫——形态
function getZgXtDesc() {
  let str = '', strArr = [];
  let xtStr = getVal('[name="xt-type"]:checked')
  xtStr && strArr.push('形态' + xtStr);
  str = strArr.length && strArr.join('，');
  return str;
}
// 当复选框选"其他"时，显示其他输入框的内容
function getQtInpVal(ckVal,inpVal) {
  let str = '', strArr = [];
  if(ckVal) {
    strArr = ckVal.split('、');
    if(strArr[strArr.length-1] === '其他') {
      strArr.pop();
    }
    inpVal && strArr.push(inpVal);
  }
  str = strArr.join('、');
  return str
}
// 子宫——肌层回声
function getZgJchsDesc() {
  let jchsDesc = '', strArr = [], impStr = '', impArr = [];
  let jchsStr = getVal('[name="jchs-type"]:checked')
  if(jchsStr === '均匀') {
    jchsStr && strArr.push('肌层回声' + jchsStr);
  }else if(jchsStr === '不均匀') {
    let bzTitList = $('.zgjzxbb-bz .bz-tit-ls .bz-tit-i');
    // 不均匀
    bzTitList.each(function(i,dom) {
      let bzItem = $(dom);
      let bzId = bzItem.attr('tab-id');
      let key = bzId.split('-')[3];
      let obj = {
        bzId: bzId,
        bzsm: getVal(`[name="zgjzxbb-bzsm-${key}"]:checked`), // 数目
        bzsmQtInp: getVal(`[id=sermzg-rt-zgjzxbb-${key}-5]`), // 数目——其他输入框
        bbdxInp1: getVal(`[id=sermzg-rt-zgjzxbb-${key}-7]`), // 病变大小输入框1
        bbdxInp2: getVal(`[id=sermzg-rt-zgjzxbb-${key}-8]`), // 病变大小输入框2
        bbdxInp3: getVal(`[id=sermzg-rt-zgjzxbb-${key}-9]`), // 病变大小输入框3
        bbwz1: getVal(`[name="zgjzxbb-bbwz-${key}"]:checked`), // 病变位置1
        bzwzQtInp1: getVal(`[id=sermzg-rt-zgjzxbb-${key}-17]`), // 病变位置1——其他输入框
        bbwz2: getVal(`[name="zgjzxbb-bbwzqy-${key}"]:checked`), // 病变位置2
        bzwzQtInp2: getVal(`[id=sermzg-rt-zgjzxbb-${key}-22]`), // 病变位置2——其他输入框
        bbbj: getVal(`[name="zgjzxbb-bbbj-${key}"]:checked`), // 病变边界
        bbhslx: getVal(`[name="zgjzxbb-bbhslx-${key}"]:checked`), // 病变回声类型
        sy: getVal(`[name="zgjzxbb-sy-${key}"]:checked`), // 声影
        bbhssfjy: getVal(`[name="zgjzxbb-bbhssfjy-${key}"]:checked`), // 病变回声是否均匀
        nbxl: getVal(`[name="zgjzxbb-nbxl-${key}"]:checked`), // 病变彩色多普勒血流显像--内部血流
        byxl: getVal(`[name="zgjzxbb-byxl-${key}"]:checked`), // 病变彩色多普勒血流显像--边缘血流
        csts: getVal(`[name="zgjzxbb-xjlcsts-${key}"]:checked`), // 超声提示
      };
      if(obj.bzsm === '单发') {
        // 单发
        strArr.push('宫腔内见一个高回声团');
      }else if(obj.bzsm === '多发'){
        // 多发
        strArr.push('宫腔内见多个高回声团');
      }else {
        obj.bzsm && strArr.push('宫腔内见'+ obj.bzsmQtInp +'个高回声团');
      }
      // 大小输入框
      let bbdxInpArr = [];
      obj.bbdxInp1 && bbdxInpArr.push(obj.bbdxInp1 + 'mm');
      obj.bbdxInp2 && bbdxInpArr.push(obj.bbdxInp2 + 'mm');
      obj.bbdxInp3 && bbdxInpArr.push(obj.bbdxInp3 + 'mm');
      if(bbdxInpArr.length) {
        let smDxStr = '';
        if(obj.bzsm === '多发') {
          smDxStr = '较大者大小分别约';
        }else {
          smDxStr = '大小约';
        }
        strArr.push(smDxStr + bbdxInpArr.join('x'));
      }
      // 病变位置
      let allBbwzArr = [];
      // 当选“其他”时，显示输入框内容
      if(obj.bbwz1) {
        let bbwzStr1 = getQtInpVal(obj.bbwz1,obj.bzwzQtInp1);
        bbwzStr1 && allBbwzArr.push(bbwzStr1);
      }
      if(obj.bbwz2) {
        let bbwzStr2 = getQtInpVal(obj.bbwz2,obj.bzwzQtInp2);
        bbwzStr2 && allBbwzArr.push(bbwzStr2);
      }
      allBbwzArr.length && strArr.push('位于' + allBbwzArr.join('、'));
      // 病变边界
      obj.bbbj && strArr.push('边界' + obj.bbbj);
      // 病变回声
      obj.bbhslx && strArr.push('见' + obj.bbhslx + '病变回声');
      // 声影
      obj.sy && obj.sy === '有' ? strArr.push('可见声影') : obj.sy === '无' ? strArr.push('未见声影') : '';
      // 病变回声是否均匀
      obj.bbhssfjy && strArr.push('内回声分布' + obj.bbhssfjy);
      // 内部血流
      let nbxlStr = obj.nbxl && (obj.nbxl === '无血流' ? '未见' : '内见' + obj.nbxl);
      nbxlStr && strArr.push('CDFI:回声团内部' + nbxlStr + '血流信号');
      // 边缘血流
      let byxlStr = obj.byxl && (obj.byxl === '无血流' ? '未见' : '内见' + obj.byxl);
      byxlStr && strArr.push('CDFI:回声团边缘' + byxlStr + '血流信号');
      // 超声提示
      obj.csts && impArr.push(obj.csts);
    })
  }
  jchsDesc = strArr.length && strArr.join('，');
  impStr = impArr.length && impArr[0];
  return {jchsDesc,impStr};
}
// 子宫——子宫腺肌病
function getZgxjbDesc() {
  let zgxjzDesc = '', strArr = [];
  let zgzdVal = getVal('[name="zgzd"]:checked');
  let qbInp = getVal('[id="sermzg-rt-79"]');
  let hbInp = getVal('[id="sermzg-rt-81"]');
  let zgjchsVal = getVal('[name="zgjchs"]:checked');
  let zgnmjcfjbqVal = getVal('[name="zgnmjcfjbq"]:checked');
  let zgjcxnzVal = getVal('[name="zgjcxnz"]:checked');
  let zlysyVal = getVal('[name="zlysy"]:checked');
  let zgxjcstsImp = getVal('[name="zgxjcsts"]:checked');
  zgzdVal && strArr.push('子宫' + zgzdVal);
  qbInp && strArr.push('前壁厚度约' + qbInp + 'mm');
  hbInp && strArr.push('后壁厚度约' + hbInp + 'mm');
  zgjchsVal && strArr.push('子宫肌层回声' + zgjchsVal);
  if(zgnmjcfjbqVal === '有') {
    strArr.push('子宫内膜与肌层分界不清');
  }else if(zgnmjcfjbqVal === '无') {
    strArr.push('子宫内膜与肌层无分界不清');
  }
  zgjcxnzVal && strArr.push('子宫肌层内' + zgjcxnzVal + '小囊肿');
  zlysyVal && strArr.push(zlysyVal + '栅栏状声影');

  zgxjzDesc = strArr.length && strArr.join('，');
  return {zgxjzDesc,zgxjcstsImp};
}

// 子宫——内膜
function getZgNmDesc() {
  let nmStr = '', strArr = [], nmInpStr = '',nmImpStr = '',nmImpArr = [];
  let nmInpVal = getVal('[id="sermzg-rt-99"]');
  let nmnjVal = getVal('[name="nmnj"]:checked');
  nmInpStr = nmInpVal && '内膜厚约' + nmInpVal + 'mm';
  nmInpStr && strArr.push(nmInpStr);
  if(nmnjVal) {
    // 内见
    // 子宫内膜
    let nmhsspVal = getVal('[name="nmhssp"]:checked');
    let nmhsjyVal = getVal('[name="nmhsjy"]:checked');
    let zgnmzxVal = getVal('[name="zgnmzx"]:checked');
    let zgnmjcjjmVal = getVal('[name="zgnmjcjjm"]:checked');
    let zgnmdplxlVal = getVal('[name="zgnmdplxl"]:checked');
    let nmzhcstsVal = getVal('[name="nmzhcsts"]:checked');
    let xlxhMap = {
      '无血流': '未见',
      '少量': '内见少量',
      '中量': '内见中量',
      '丰富': '内见丰富',
    }
    nmhsspVal && strArr.push('内膜回声水平呈' + nmhsspVal);
    nmhsjyVal && strArr.push('内膜回声强弱' + nmhsjyVal);
    zgnmzxVal && strArr.push('内膜中线呈' + zgnmzxVal);
    zgnmjcjjmVal && strArr.push('内膜与肌层交界面' + zgnmjcjjmVal + '显示');
    zgnmdplxlVal && strArr.push('CDFI:其内' + xlxhMap[zgnmdplxlVal] + '血流信号');
    nmzhcstsVal && nmImpArr.push(nmzhcstsVal);

    // 宫腔占位性病变
    let bzTitList = $('.gqzwxbb-bz .bz-tit-ls .bz-tit-i');
    let gqzwxbbImpArr = [];
    // 不均匀
    bzTitList.each(function(i,dom) {
      let bzItem = $(dom);
      let bzId = bzItem.attr('tab-id');
      let key = bzId.split('-')[3];
      let obj = {
        bzId: bzId,
        bzsm: getVal(`[name="gqzwxbb-bzsm-${key}"]:checked`), // 数目
        bbdxInp1: getVal(`[id=sermzg-rt-gqzwxbb-${key}-5]`), // 病变大小输入框1
        bbdxInp2: getVal(`[id=sermzg-rt-gqzwxbb-${key}-6]`), // 病变大小输入框2
        bbdxInp3: getVal(`[id=sermzg-rt-gqzwxbb-${key}-7]`), // 病变大小输入框3
        bbwz: getVal(`[name="gqzwxbb-bbwz-${key}"]:checked`), // 病变位置
        bbhslx: getVal(`[name="zgjzxbb-bbhslx-${key}"]:checked`), // 病变回声类型
        bbbj: getVal(`[name="gqzwxbb-bbbj-${key}"]:checked`), // 病变边界
        nbxl: getVal(`[name="gqzwxbb-nbxl-${key}"]:checked`), // 病变彩色多普勒血流显像
        csts: getVal(`[name="gqzwxbb-xjlcsts-${key}"]:checked`), // 超声提示
      };
      if(obj.bzsm === '单发') {
        // 单发
        strArr.push('宫腔内见一个高回声团');
      }else if(obj.bzsm === '多发'){
        // 多发
        strArr.push('宫腔内见多个高回声团');
      }
      // 大小输入框
      let bbdxInpArr = [];
      obj.bbdxInp1 && bbdxInpArr.push(obj.bbdxInp1 + 'mm');
      obj.bbdxInp2 && bbdxInpArr.push(obj.bbdxInp2 + 'mm');
      obj.bbdxInp3 && bbdxInpArr.push(obj.bbdxInp3 + 'mm');
      if(bbdxInpArr.length) {
        let smDxStr = '';
        if(obj.bzsm === '多发') {
          smDxStr = '较大者大小分别约';
        }else {
          smDxStr = '大小约';
        }
        strArr.push(smDxStr + bbdxInpArr.join('x'));
      }
      // 病变位置
      obj.bbwz && strArr.push('位于' + obj.bbwz);
      // 病变回声
      obj.bbhslx && strArr.push('见' + obj.bbhslx + '病变回声');
      // 病变边界
      obj.bbbj && strArr.push('边界' + obj.bbbj);
      // 内部血流
      let nbxlStr = obj.nbxl && (obj.nbxl === '无血流' ? '未见' : '内见' + obj.nbxl);
      nbxlStr && strArr.push('CDFI:回声团' + nbxlStr + '血流信号');
      // 超声提示
      obj.csts && gqzwxbbImpArr.push(obj.csts);
    })
    // 过滤相同的提示
    gqzwxbbImpArr.map(item => {
      if(!nmImpArr.includes(item)) {
        nmImpArr.push(item);
      }
    })
  }else {
    nmInpStr && strArr.push('回声均匀');
  }
  nmStr = strArr.length && strArr.join('，');
  nmImpStr = nmImpArr.length && nmImpArr.join('，');
  return {nmStr,nmImpArr};
}
// 子宫——宫颈
function getZgGjDesc() {
  let gjStr = '', gjnzStrArr = [], gjxrStrArr = [], gjaStrArr = [], strArr = [], gjImpStr = '',impArr = [];
  let gjVal = getVal('[name="gj-status"]:checked');
  if(gjVal === '正常') {
    strArr.push('宫颈大小' + gjVal);
  }else {
    // 默认选上宫颈癌提示
    $('[name="gja-ts"]:checked').prop('checked',true);
    // 宫颈囊肿
    let gjnzNumVal = getVal('[name="gjnz-num"]:checked'); // 数目
    let gjnzBbdxVal = getVal('[name="gjnz-bbdx"]:checked'); // 病变大小
    let gjnzBbdxInp1 =  getVal(`[id=sermzg-rt-162]`); // 病变大小输入框1
    let gjnzBbdxInp2 = getVal(`[id=sermzg-rt-163]`); // 病变大小输入框2
    let gjnzBbdxInp3 = getVal(`[id=sermzg-rt-164]`); // 病变大小输入框3
    let gjnzBbhsVal = getVal('[name="gjnz-bbhs"]:checked'); // 病变回声
    let gjnzTsVal = getVal('[name="gjnz-ts"]:checked'); // 提示
    if(gjnzNumVal === '见1个无回声囊') {
      $('#sermzg-rt-169').prop('checked',true);
      $('#sermzg-rt-169').prop('disabled',false);
      $('#sermzg-rt-170').prop('disabled',true);
    }else if(gjnzNumVal === '见多个无回声囊') {
      $('#sermzg-rt-170').prop('checked',true);
      $('#sermzg-rt-170').prop('disabled',false);
      $('#sermzg-rt-169').prop('disabled',true);
    }
    // 大小输入框
    let bbdxInpArr = [];
    let gjnzBbdxStr = gjnzBbdxVal && gjnzBbdxVal + '约';
    gjnzBbdxInp1 && bbdxInpArr.push(gjnzBbdxInp1 + 'mm');
    gjnzBbdxInp2 && bbdxInpArr.push(gjnzBbdxInp2 + 'mm');
    gjnzBbdxInp3 && bbdxInpArr.push(gjnzBbdxInp3 + 'mm');

    gjnzNumVal && gjnzStrArr.push('宫腔内' + gjnzNumVal);
    bbdxInpArr.length && gjnzStrArr.push(gjnzBbdxStr + bbdxInpArr.join('x'));
    gjnzBbhsVal && gjnzStrArr.push(gjnzBbhsVal);
    gjnzTsVal && impArr.push(gjnzTsVal);

    // 宫颈息肉
    let gjxrNumVal = getVal('[name="gjxr-num"]:checked'); // 数目
    let gjxrBbwzVal = getVal('[name="gjxr-bbwz"]:checked'); // 病变位置
    let gjxrBbdxVal = getVal('[name="gjxr-bbdx"]:checked'); // 病变大小
    let gjxrBbdxInp1 =  getVal(`[id=sermzg-rt-185]`); // 病变大小输入框1
    let gjxrBbdxInp2 = getVal(`[id=sermzg-rt-186]`); // 病变大小输入框2
    let gjxrBbdxInp3 = getVal(`[id=sermzg-rt-187]`); // 病变大小输入框3
    let gjxrBbhsVal = getVal('[name="gjxr-bbhs"]:checked'); // 病变回声
    let gjxrBbxlVal = getVal('[name="gjxr-bbxl"]:checked'); // 病变血流
    let gjxrBbxlInp = getVal('[id="sermzg-rt-196"]'); // 病变血流输入框
    let gjxrTsVal1 = getVal('[name="gjxr-ts"]:checked'); // 提示
    let gjxrTsVal2 = getVal('[name="gjxr-gjgn"]:checked'); // 提示——宫颈管内
    let gjxrTsVal3 = getVal('[name="gjxr-hs"]:checked'); // 提示——回声
    let newGjxrStr = '';
    if(gjxrNumVal === '单发') {
      newGjxrStr = '一个';
    }else if(gjxrNumVal === '多发') {
      newGjxrStr = '多个';
    }
    newGjxrStr && gjxrStrArr.push('宫腔内见' + newGjxrStr + '高回声团');
    gjxrBbwzVal && gjxrStrArr.push('位于' + gjxrBbwzVal);
    // 大小输入框
    let gjxrBbdxInpArr = [];
    let gjxrBbdxStr = gjxrBbdxVal && gjxrBbdxVal + '约';
    gjxrBbdxInp1 && gjxrBbdxInpArr.push(gjxrBbdxInp1 + 'mm');
    gjxrBbdxInp2 && gjxrBbdxInpArr.push(gjxrBbdxInp2 + 'mm');
    gjxrBbdxInp3 && gjxrBbdxInpArr.push(gjxrBbdxInp3 + 'mm');
    gjxrBbdxInpArr.length && gjxrStrArr.push(gjxrBbdxStr + gjxrBbdxInpArr.join('x'));
    gjxrBbhsVal && gjxrStrArr.push('回声' + gjxrBbhsVal);
    let newGjxrBbxlVal = gjxrBbxlVal && (gjxrBbxlVal === '无血流' ? '无' : '内见' + gjxrBbxlVal);
    newGjxrBbxlVal && gjxrStrArr.push('CDFI:回声团' + newGjxrBbxlVal + '血流信号');
    gjxrBbxlInp && gjxrStrArr.push('探及动脉频谱，RI=' + gjxrBbxlInp);
    gjxrTsVal1 && impArr.push(gjxrTsVal1);
    gjxrTsVal3 && impArr.push(gjxrTsVal2 + '，' + gjxrTsVal3 + '考虑息肉可能');

    // 宫颈癌
    let gjaGjxtVal = getVal('[name="gja-gjxt"]:checked'); // 宫颈形态
    let gjaBbwzVal = getVal('[name="gja-bbhs"]:checked'); // 病变回声
    let gjaBbdxInp1 =  getVal(`[id=sermzg-rt-210]`); // 病变大小输入框1
    let gjaBbdxInp2 = getVal(`[id=sermzg-rt-211]`); // 病变大小输入框2
    let gjaBbdxInp3 = getVal(`[id=sermzg-rt-212]`); // 病变大小输入框3
    let gjaBbxlVal = getVal('[name="gja-bbxl"]:checked'); // 病变血流
    let gjaBbxlInp = getVal('[id="sermzg-rt-217"]'); // 病变血流输入框
    let gjaTsVal1 = getVal('[name="gja-ts"]:checked'); // 提示
    gjaGjxtVal && gjaStrArr.push(gjaGjxtVal);
    gjaBbwzVal && gjaStrArr.push('宫壁实质回声' + gjaBbwzVal);
    // 大小输入框
    let gjaBbdxInpArr = [];
    gjaBbdxInp1 && gjaBbdxInpArr.push(gjaBbdxInp1 + 'mm');
    gjaBbdxInp2 && gjaBbdxInpArr.push(gjaBbdxInp2 + 'mm');
    gjaBbdxInp3 && gjaBbdxInpArr.push(gjaBbdxInp3 + 'mm');
    gjaBbdxInpArr.length && gjaStrArr.push('范围约' + gjaBbdxInpArr.join('x'));
    gjaBbxlVal && gjaStrArr.push('CDFI:宫颈内见' + gjaBbxlVal + '血流信号');
    gjaBbxlInp && gjaStrArr.push('探及动脉频谱，RI=' + gjaBbxlInp);
    gjaTsVal1 && impArr.push(gjaTsVal1);
  }
  gjnzStrArr.length && strArr.push(gjnzStrArr);
  gjxrStrArr.length && strArr.push(gjxrStrArr);
  gjaStrArr.length && strArr.push(gjaStrArr);
  gjStr = strArr.length && strArr.join('。');
  gjImpStr = impArr.join('，');
  return {gjStr,gjImpStr};
}

// 子宫——宫内节育器
function getZgGnjyqDesc() {
  let str = '', strArr = [];
  let ywStr = getVal('[name="gnjyq-yw"]:checked');
  if(ywStr === '无') {
    strArr.push('宫腔内未探及节育器');
  }else if(ywStr === '有') {
    let jyqSiteStr = getVal('[name="gnjyq-site"]:checked');
    strArr.push('宫腔内探及节育器' + jyqSiteStr);
  }
  str = strArr.length && strArr.join('，');
  return str;
}

// 子宫——提示
function getZgTsImp() {
  let zgtsStr = getVal('[name="zgwyc-ts"]:checked');
  return zgtsStr;
}

// 获取描述——子宫
function getZgDesc() {
  let str = '', strArr = [];
  // 位置
  let siteStr = getZgSiteDesc();
  siteStr && strArr.push('子宫'+siteStr);

  // 大小
  let dxStr = getZgDxDesc();
  dxStr && strArr.push(dxStr);

  // 形态
  let xtStr = getZgXtDesc();
  xtStr && strArr.push(xtStr);

  // 肌层回声
  let {jchsDesc = ''} = getZgJchsDesc();
  jchsDesc && strArr.push(jchsDesc);

  // 子宫腺肌病
  let { zgxjzDesc = '' } = getZgxjbDesc();
  zgxjzDesc && strArr.push(zgxjzDesc);

  // 内膜
  let {nmStr = ''} = getZgNmDesc();
  nmStr && strArr.push(nmStr);

  // 宫颈
  let { gjStr = '' } = getZgGjDesc();
  gjStr && strArr.push(gjStr);

  // 子宫——宫内节育器
  let gnjyqStr = getZgGnjyqDesc();
  gnjyqStr && strArr.push(gnjyqStr);

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取印象——子宫
function getZgImp() {
  let str = '', strArr = [];

  // 子宫无异常提示
  let zgtsStr = getZgTsImp();
  zgtsStr && strArr.push(zgtsStr);
  // 肌层回声
  let {impStr = ''} = getZgJchsDesc();
  impStr && strArr.push(impStr);
  // 子宫腺肌病
  let { zgxjcstsImp = '' } = getZgxjbDesc();
  zgxjcstsImp && strArr.push(zgxjcstsImp);
  // 子宫内膜
  let { nmImpArr=[] } = getZgNmDesc();
  nmImpArr.length && strArr.push(...nmImpArr);
  // 宫颈
  let { gjImpStr='' } = getZgGjDesc();
  gjImpStr && strArr.push(gjImpStr);
  
  str = strArr.length && strArr.join('，');
  return strArr;
}

/** 子宫end **/ 

/** 卵巢start **/
// 卵巢tab切换
function changeLcTabContent() {
  $('.lc-tab-ls').on('click', '.lc-tab-i ', function() {
    if($(this).hasClass('act')) {
      return;
    }

    $(this).addClass('act').siblings('.lc-tab-i ').removeClass('act');
    $('.sermzglc-b .lc-tab-con').removeClass('act');
    var tabName = $(this).attr('tab-name');
    $('.sermzglc-b .lc-tab-con[id="'+tabName+'"]').addClass('act');
  })
}

// 卵巢附件病变tab切换
function changeLcfjbbTabCon() {
  $('#sermlc1 [name="lcfjbb-type"]').on('click', function() {
    $('.sermzglc-b .lcfjbb-con').removeClass('act');
    var tabName = $(this).attr('tab-name');
    $('.sermzglc-b .lcfjbb-con[id="'+tabName+'"]').addClass('act');
    getImpCon();
  })
}

// 显示卵巢附件病变
function showLcfjbbRad() {
  let lcfjbbData = resultData.filter(item => item.id === "sermzglc-rt-6");
  lcfjbbData.map(lcfjbbItem => {
    let childData1 = lcfjbbItem.child || [];
    childData1.map(child1 => {
      // 显示卵巢页面
      let bbPid = child1.id;
      let bbTabName = $('#'+bbPid).attr('tab-name');
      $('#'+bbPid).addClass('act').siblings('.lc-tab-i ').removeClass('act');
      $('.sermzglc-b .lc-tab-con').removeClass('act');
      $('#'+bbTabName).addClass('act');
      
      let childData2 = child1.child || [];
      let childData3 = childData2.length ? childData2[0].child : {};
      // 显示附件病变
      childData3.map(child3 => {
        let bbId = child3.id;
        let tabName = $('#'+bbId).attr('tab-name');
        $('.sermzglc-b .lcfjbb-con').removeClass('act');
        $('#'+tabName).addClass('act');
      })
    })
  })
}

// 获取大小值
function getDxInp(idArr) {
  let inpStr = '', InpArr = [];
  for(let id in idArr) {
    let dxInpVal = getVal(`[id="sermlc-rt-${idArr[id]}"]`);
    dxInpVal && InpArr.push(dxInpVal + 'mm');
  }
  inpStr = InpArr.length && InpArr.join('x');
  return inpStr;
}
// 卵巢基本情况
function getLcBaseInfoDesc() {
  let str = '', strArr = [];
  let lcDxArr = [],zclcInfoArr = [],yclcInfoArr = [];
  // 左右侧卵巢大小
  let zclcVal = getVal('[id="sermlc-rt-2"]:checked');
  let yclcVal = getVal('[id="sermlc-rt-15"]:checked');
  let zclcInp = getDxInp([4,5,6]);
  let yclcInp = getDxInp([17,18,19]);
  let zclcInpStr = zclcInp && '大小约' + zclcInp || '';
  let yclcInpStr = yclcInp && '大小约' + yclcInp || '';
  (zclcVal || zclcInpStr) && lcDxArr.push(zclcVal + zclcInpStr);
  (yclcVal || yclcInpStr) && lcDxArr.push(yclcVal + yclcInpStr);
  lcDxArr.length && strArr.push(lcDxArr.join('，'));

  // 左侧卵巢直径、回声
  let zclcDxRad = getVal('[name="zclc-dx"]:checked');
  let zclcZj = getVal('[name="zclc-zj"]:checked');
  let zclcHs = getVal('[name="zclc-hs"]:checked');

  // 右侧卵巢直径、回声
  let yclcDxRad = getVal('[name="yclc-dx"]:checked');
  let yclcZj = getVal('[name="yclc-zj"]:checked');
  let yclcHs = getVal('[name="yclc-hs"]:checked');

  // 左右侧卵巢大小正常时，描述为“双侧”
  if((zclcDxRad && yclcDxRad) === '大小正常') {
    strArr.push('双侧卵巢大小正常');
  }else {
    zclcDxRad && zclcInfoArr.push(zclcDxRad);
    yclcDxRad && yclcInfoArr.push(yclcDxRad);
  }
  zclcZj && zclcInfoArr.push(zclcZj);
  zclcHs && zclcInfoArr.push(zclcHs);
  zclcInfoArr.length && strArr.push('左侧卵巢' + zclcInfoArr.join('，'));

  yclcZj && yclcInfoArr.push(yclcZj);
  yclcHs && yclcInfoArr.push(yclcHs);
  yclcInfoArr.length && strArr.push('右侧卵巢' + yclcInfoArr.join('，'));

  str = strArr.length && strArr.join('。');
  return str;
}

// 获取卵巢基本情况的提示
function getLcBaseInfoImp() {
  let str = '', strArr = [];
  let zclc = getVal('[id="sermlc-rt-29"]:checked');
  let yclc = getVal('[id="sermlc-rt-30"]:checked');
  let sclc = getVal('[id="sermlc-rt-31"]:checked');
  let zclcbb = getVal('[name="zclcjb-ts"]:checked');
  let yclcbb = getVal('[name="yclcjb-ts"]:checked');
  let sclcbb = getVal('[name="sclcjb-ts"]:checked');

  zclc && strArr.push(zclc + zclcbb);
  yclc && strArr.push(yclc + yclcbb);
  sclc && strArr.push(sclc + sclcbb);

  str = strArr.length && strArr.join('，');
  return str;
}


// 获取卵巢生理性囊肿的左右侧卵巢情况
function getZylcDesc(type) {
  let str = '', strArr = [];
  let lcVal = getVal(`[name="lcslxnz-${type}lc"]:checked`);
  let lx = getVal(`[name="lcslxnz-${type}-lx"]:checked`);
  let dxIdArr = type === 'zc' ? [46,47,48] : type === 'yc' ? [69,70,71] : [];
  let dxStr = getDxInp(dxIdArr);
  let nb = getVal(`[name="lcslxnz-${type}-nb"]:checked`);
  let nbhs = getVal(`[name="lcslxnz-${type}-nbhs"]:checked`);
  let xlfb = getVal(`[name="lcslxnz-${type}-xlfb"]:checked`);
  let xlfbStr = xlfb ? (xlfb === '无血流' ? '未见明显' : '可见' + xlfb) : '';
  let lcAndLx = lcVal + (lx ? '见一个' + lx : '');

  lcAndLx && strArr.push(lcAndLx);
  dxStr && strArr.push('大小约' + dxStr);
  nb && strArr.push('囊壁' + nb);
  nbhs && strArr.push('内部' + nbhs);
  xlfbStr && strArr.push('CDFI:囊壁内' + xlfbStr + '血流信号');

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取卵巢生理性囊肿描述
function getLcslxnzDesc() {
  let str = '', strArr = [];
  let lcMsRad = getVal('[name="lcslxnz-ms"]:checked');
  if(lcMsRad === '有') {
    let zclcStr = getZylcDesc('zc');
    let yclcStr = getZylcDesc('yc');

    zclcStr && strArr.push(zclcStr);
    yclcStr && strArr.push(yclcStr);
  }
  str = strArr.length && strArr.join('。\n');
  return str;
}

// 获取卵巢生理性囊肿的提示
function getLcslxnzImp() {
  let str = '', strArr = [];
  let zclc = getVal('[id="sermlc-rt-88"]:checked');
  let yclc = getVal('[id="sermlc-rt-89"]:checked');
  let sclc = getVal('[id="sermlc-rt-90"]:checked');
  let zclcbb = getVal('[name="lcslxnz-zc-ts"]:checked');
  let yclcbb = getVal('[name="lcslxnz-yc-ts"]:checked');
  let sclcbb = getVal('[name="lcslxnz-sc-ts"]:checked');

  zclc && strArr.push(zclc + zclcbb);
  yclc && strArr.push(yclc + yclcbb);
  sclc && strArr.push(sclc + sclcbb);

  str = strArr.length && strArr.join('，');
  return str;
}

// 囊性病变——根据病变回声推导提示
function inferBbhsImp() {
  $('.lcfjbb-bbhs').on('click','[type="checkbox"]',function() {
    $('.lcfjbb-ts [type="radio"]').prop('checked',false);
    let ckVal = getVal('.lcfjbb-bbhs [type="checkbox"]:checked');
    let valMap = {
      '云雾状回声': 'sermlc-rt-160',
      '絮状回声、网格样回声、高回声成分': 'sermlc-rt-161',
      '透声清': 'sermlc-rt-162',
      '强回声团、短线样强回声、面团征、瀑布征、脂液分层征、杂乱回声征': 'sermlc-rt-163',
      '蘘内可见不完全分隔，呈迂曲管状结构，内透声好、肿块一端膨大，一端为迂曲管状': 'sermlc-rt-164',
      '形态欠规则，内透声清': 'sermlc-rt-165'
    }
    for(let val in valMap) {
      if(ckVal.includes(val)) {
        $('#' + valMap[val]).prop('checked',true);
      }
    }
  })
}
// 获取卵巢附件病变描述——囊性病变
function getNxbbDesc() {
  let str = '', strArr = [];
  let bbwz = getVal('[name="nxbb-bbwz"]:checked');
  bbwz && strArr.push(bbwz + '见一个囊性肿块');

  let dxInp = getDxInp([112,113,114]);
  bbwz && strArr.push('大小约' + dxInp);

  let nb = getVal('[name="nxbb-nbfg"]:checked');
  let nbStr = nb ? (nb === '无' ? '未见分隔' : '可见' + nb) : '';
  nbStr && strArr.push('内壁' + nbStr);

  let fg = getVal('[name="nxbb-nbjfg"]:checked');
  fg && strArr.push('分隔' + fg);

  let hslx = getVal('[name="nxbb-bbhs"]:checked');
  hslx && strArr.push('囊内见' + hslx + '病变回声');

  let nnj = getVal('[name="nxbb-bbhs-nnj"]:checked');
  nnj && strArr.push(nnj);

  let nnzk = getVal('[name="nxbb-bbhs-zk"]:checked');
  let pdfwInp = getDxInp([139,140,141]); //膨大范围
  let yqgzfwInp = getDxInp([142,143,144]); //膨大范围
  nnzk && strArr.push(nnzk);
  pdfwInp && strArr.push('膨大范围约' + pdfwInp);
  yqgzfwInp && strArr.push('迁曲管状范围约' + yqgzfwInp);

  let zkxt = getVal('[name="nxbnxbb-bbhs-xt"]:checked');
  zkxt && strArr.push(zkxt);

  let xlfb = getVal('[name="nxbb-xlfb"]:checked');
  let xlfbStr = xlfb ? (xlfb === '无' ? '未见明显' : '可见' + xlfb) : '';
  xlfbStr && strArr.push('CDFI:囊内' + xlfbStr + '血流信号');

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取卵巢附件病变描述——囊实性病变
function getNsxbbDesc() {
  let str = '', strArr = [];
  let bbInfoArr = [], bgzdhsArr = [], rtArr = [], nxArr = [];

  // 基本信息
  let bbwz = getVal('[name="nsxbb-bbwz"]:checked');
  if(bbwz === '其他') {
    let qtInpVal = $('#sermlc-rt-174').val();
    qtInpVal && bbInfoArr.push(qtInpVal + '见一个囊性肿块');
  }else {
    bbwz && bbInfoArr.push(bbwz + '见一个囊性肿块');
  }

  let dxInp = getDxInp([176,177,178]);
  dxInp && bbInfoArr.push('大小约' + dxInp);

  let nb = getVal('[name="nsxbb-nbfg"]:checked');
  let nbStr = nb ? (nb === '无' ? '未见分隔' : '可见' + nb) : '';
  nbStr && bbInfoArr.push('内壁' + nbStr);

  let fg = getVal('[name="nsxbb-nbjfg"]:checked');
  fg && bbInfoArr.push('分隔' + fg);

  let hs = getVal('[name="nsxbb-hs"]:checked');
  hs && bbInfoArr.push('囊内可见' + hs + '区');

  // 不规则低回声
  let bgzdhs = getVal('[name="bgzdhs-hs"]:checked');
  bgzdhs && bgzdhsArr.push('囊内见实性不规则' + bgzdhs);

  let bgzDxInp = getDxInp([194,195,196]);
  bgzDxInp && bgzdhsArr.push('大小约' + bgzDxInp);

  let bgzdhsSy = getVal('[name="bgzdhs-sy"]:checked');
  let syStr = bgzdhsSy === '无' ? '未见' : bgzdhsSy === '有' ? '可见' : '';
  syStr && bgzdhsArr.push(syStr + '声影');

  let xlfb = getVal('[name="bgzdhs-xlfb"]:checked');
  let xlfbStr = xlfb ? (xlfb === '无' ? '未见明显' : '可见' + xlfb) : '';
  xlfbStr && bgzdhsArr.push('CDFI:囊内' + xlfbStr + '血流信号');

  let tjpp = getVal('[name="bgzdhs-dtpp"]:checked');
  tjpp && bgzdhsArr.push(tjpp);
  let riInp = getVal('[id="sermlc-rt-260"]');
  riInp && bgzdhsArr.push('RI=' + riInp);

  // 乳头
  let rtyw = getVal('[name="rths-yw"]:checked');
  if(rtyw === '有') {
    rtyw && rtArr.push('囊内见乳头状突起');

    let rtDxInp = getDxInp([265,266,267]);
    rtDxInp && rtArr.push('大小约' + rtDxInp);

    let rths = getVal('[name="rths-hs"]:checked');
    rths && rtArr.push('囊内可见' + rths + '区');

    let rtsl = getVal('[name="rths-sl"]:checked');
    rtsl && rtArr.push('数量为' + rtsl);

    let rtlk = getVal('[name="rths-lk"]:checked');
    rtlk && rtArr.push('轮廓' + rtlk);

    let rtsy = getVal('[name="rths-sy"]:checked');
    let rtsyStr = rtsy === '无' ? '未见' : rtsy === '有' ? '可见' : '';
    rtsyStr && rtArr.push(rtsyStr + '声影');

    let rtxlfb = getVal('[name="rths-xlfb"]:checked');
    let rtxlfbStr = rtxlfb ? (rtxlfb === '无' ? '未见明显' : '可见' + rtxlfb) : '';
    rtxlfbStr && rtArr.push('CDFI:囊内' + rtxlfbStr + '血流信号');

    let rttjpp = getVal('[name="rths-dtpp"]:checked');
    rttjpp && rtArr.push(tjpp);
    let rtRiInp = getVal('[id="sermlc-rt-289"]');
    rtRiInp && rtArr.push('RI=' + rtRiInp);
  }

  // 囊性部分回声
  let nxbbhs = getVal('[name="nxbfhs-type"]:checked');
  nxbbhs && nxArr.push('囊性' + nxbbhs);
  let nxhsxlfb = getVal('[name="nbxl-xlfb"]:checked');
  let nxhsxlfbStr = nxhsxlfb ? (nxhsxlfb === '无' ? '未见明显' : '可见' + nxhsxlfb) : '';
  nxhsxlfbStr && nxArr.push('CDFI:囊内' + nxhsxlfbStr + '血流信号');

  bbInfoArr.length && strArr.push(bbInfoArr.join('，'));
  bgzdhsArr.length && strArr.push(bgzdhsArr.join('，'));
  rtArr.length && strArr.push(rtArr.join('，'));
  nxArr.length && strArr.push(nxArr.join('，'));

  str = strArr.length && strArr.join('。\n');
  return str;
}

// 获取卵巢附件病变描述——实性病变
function getSxbbDesc() {
  let str = '', strArr = [];
  let bbwz = getVal('[name="sxbb-bbwz"]:checked');
  bbwz && strArr.push(bbwz + '见一个囊性肿块');

  let dxInp = getDxInp([208,209,210]);
  bbwz && strArr.push('大小约' + dxInp);

  let bbhs = getVal('[name="sxbb-bbhs"]:checked');
  bbhs && strArr.push('囊内可见' + bbhs + '回声区');

  let bbby = getVal('[name="sxbb-bbby"]:checked');
  bbby && strArr.push('边缘' + bbby);

  let sfjy = getVal('[name="sxbb-sfjy"]:checked');
  bbby && strArr.push('内部' + sfjy);

  let sy = getVal('[name="sxbb-sy"]:checked');
  let syStr = sy === '无' ? '未见' : sy === '有' ? '可见' : '';
  syStr && strArr.push(syStr + '声影');

  let xlfb = getVal('[name="sxbb-xlfb"]:checked');
  let xlfbStr = xlfb ? (xlfb === '无' ? '未见明显' : '可见' + xlfb) : '';
  xlfbStr && strArr.push('CDFI:囊内' + xlfbStr + '血流信号');

  let tjpp = getVal('[name="sxbb-dtpp"]:checked');
  tjpp && strArr.push(tjpp);
  let riInp = getVal('[id="sermlc-rt-231"]');
  riInp && strArr.push('RI=' + riInp);

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取卵巢附件病变提示——囊性病变
function getNxbbImp() {
  let str = '';
  let nxbbTsStr = getVal('[name="nxbb-ts"]:checked');
  let nxzkTsStr = getVal('[name="nxbb-nxzk-ts"]:checked');
  str = nxbbTsStr + nxzkTsStr;
  return str;
}

// 获取卵巢附件病变提示——囊实性病变
function getNsxbbImp() {
  let str = '',strArr = [];
  let nxbbTsStr = getVal('[name="nsxbb-ts"]:checked');
  let nxzkTsStr = getVal('[name="lcfjbb-nsxzk"]:checked');
  nxbbTsStr && strArr.push(nxbbTsStr + '囊实性肿块');
  nxzkTsStr && strArr.push(nxzkTsStr + '类');

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取卵巢附件病变提示——实性病变
function getSxbbImp() {
  let str = '',strArr = [];
  let nxbbTsStr = getVal('[name="sxbb-ts"]:checked');
  let nxzkTsStr = getVal('[name="sxbb-sxzk"]:checked');
  nxbbTsStr && strArr.push(nxbbTsStr + '实性肿块');
  nxzkTsStr && strArr.push(nxzkTsStr + '建议进一步检查');

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取卵巢附件病变描述
function getLcfjbbDesc() {
  let str = '';
  let bbType = getVal('[name="lcfjbb-type"]:checked');
  if(bbType === '囊性病变') {
    str = getNxbbDesc();
  }else if(bbType === '囊实性病变') {
    str = getNsxbbDesc();
  }else if(bbType === '实性病变') {
    str = getSxbbDesc();
  }
  return str;
}

// 获取卵巢附件病变提示
function getLcfjbbImp() {
  let str = '', strArr = [];

  let nxbbImp = getNxbbImp();
  nxbbImp && strArr.push(nxbbImp);

  let nsxbbImp = getNsxbbImp();
  nsxbbImp && strArr.push(nsxbbImp);

  let sxbbImp = getSxbbImp();
  sxbbImp && strArr.push(sxbbImp);

  str = strArr.length && strArr.join('，');
  return str;
}

// 获取卵巢所有描述
function getLcDesc() {
  let str = '', strArr = [];
  let lcInfoStr = getLcBaseInfoDesc();
  let lcslxnzStr = getLcslxnzDesc();
  let lcfjbbStr = getLcfjbbDesc();

  lcInfoStr && strArr.push(lcInfoStr);
  lcslxnzStr && strArr.push(lcslxnzStr);
  lcfjbbStr && strArr.push(lcfjbbStr);

  str = strArr.length && strArr.join('。');
  return str;
}

// 获取卵巢所有印象
function getLcImp() {
  let str = '', strArr = [];

  let lcjbImp = getLcBaseInfoImp();
  lcjbImp && strArr.push(lcjbImp);

  let lcslxnzImp = getLcslxnzImp();
  lcslxnzImp && strArr.push(lcslxnzImp);

  let lcfjbbImp = getLcfjbbImp();
  lcfjbbImp && strArr.push(lcfjbbImp);

  str = strArr.length && strArr.join('，');
  return strArr;
}

/** 卵巢end **/ 


/** 积液start **/ 

// 获取描述——积液
function getJyDescAndImp() {
  let jyDescStr = '', strArr = [], jyImpStr = '', impArr = [];
  let pqjyStr = getVal('[name="jy-pqjy"]:checked');
  let fqjyStr = getVal('[name="jy-fqjy"]:checked');

  // 盆腔积液
  if(pqjyStr === '有') {
    let sjInpVal = getVal('[id="sermjy-rt-5"]');
    sjInpVal && strArr.push('最深约' + sjInpVal + 'mm');
    let jyhsVal = getVal('[name="jy-hs"]:checked');
    let jyTscVal = getVal('[name="jy-tsc"]:checked');
    jyhsVal && strArr.push(jyhsVal + jyTscVal);
    impArr.push('盆腔积液');
  }else if(pqjyStr === '无') {
    impArr.push('盆腔未见明显积液');
  }

  // 腹腔积液
  if(fqjyStr === '有') {
    let fbwzVal = getVal('[name="jy-fbwz"]:checked');
    fbwzVal && strArr.push('分布于肝前、肝肾隐窝、脾周、双侧髂窝、耻骨联合上，最多处位于' + fbwzVal);
    let fqSjInpVal = getVal('[id="sermjy-rt-23"]');
    fqSjInpVal && strArr.push('最深约' + fqSjInpVal + 'mm');
    impArr.push('腹腔积液');
  }else if(fqjyStr === '无') {
    impArr.push('腹腔未见明显积液');
  }

  jyDescStr = strArr.length && strArr.join('，');
  jyImpStr = impArr.length && impArr.join('，');
  return {jyDescStr, impArr};
}

/** 积液end **/ 


// 获取描述——子宫、卵巢、积液
function getDescCon() {
  let str = '', strArr = [];
  // 子宫
  let zgCon = getZgDesc();
  zgCon && strArr.push(zgCon);

  // 卵巢
  let lcCon = getLcDesc();
  lcCon && strArr.push(lcCon);

  // 积液
  let {jyDescStr} = getJyDescAndImp();
  jyDescStr && strArr.push(jyDescStr);

  str = strArr.length && strArr.join('，') + '。';
  // console.log('描述-str-->',str);
  return str;
}

// 获取印象——子宫、卵巢、积液
function getImpCon() {
  let str = '', strArr = [];
  // 子宫
  let zgCon = getZgImp();
  zgCon && strArr.push(...zgCon);

  // 卵巢
  let lcCon = getLcImp();
  lcCon && strArr.push(...lcCon);

  // 积液
  let {jyImpStr} = getJyDescAndImp();
  jyImpStr && strArr.push(...jyImpStr);

  // 添加序号
  let idxStrArr = [];
  for(let i in strArr) {
    let index = Number(i) + 1 + '.';
    index && idxStrArr.push(index + strArr[i]);
  }
  str = idxStrArr.join('\n');
  curElem.find('[id="sermzglc-rt-227"]').val(str);

  // console.log('印象-str-->',str);
  return str;
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescCon();
  rtStructure.impression = getImpCon();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}