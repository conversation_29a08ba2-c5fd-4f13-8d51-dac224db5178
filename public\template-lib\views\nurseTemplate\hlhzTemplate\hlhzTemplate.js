$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      inintPreview()
    } else {
      pageInit()
    }
  }
}
function pageInit(){
  getDeptList();
  window.getNurseList ? toNurseList() : '';
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    useServerTime('hlhz-rt-0005')
  }
  addLayDateTime('hlhz-rt-0001-003')
  // addLayDateTime('hlhz-rt-0005')
  setupDateSelection('hlhz-rt-0001-003')
  // setupDateSelection('hlhz-rt-0005')
  $('#hlhz-rt-0001-003').on('focus', function(value) {
    if($('#hlhz-rt-0001-003').val()){
      return
    }
    $('#hlhz-rt-0001-003').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  $('#hlhz-rt-0005').on('focus', function(value) {
    if($('#hlhz-rt-0005').val()){
      return
    }
    $('#hlhz-rt-0005').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  $('.count').on('change', function(value) {
    var total = 0;
    if($('#hlhz-rt-0002-001-001').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-001-001').attr('point'));
    }
    if($('#hlhz-rt-0002-001-002').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-001-002').attr('point'));
    }
    if($('#hlhz-rt-0002-001-003').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-001-003').attr('point'));
    }
    if($('#hlhz-rt-0002-002-001').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-001').attr('point'));
    }
    if($('#hlhz-rt-0002-002-002').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-002').attr('point'));
    }
    if($('#hlhz-rt-0002-002-003').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-003').attr('point'));
    }
    if($('#hlhz-rt-0002-002-004').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-004').attr('point'));
    }
    if($('#hlhz-rt-0002-002-005').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-005').attr('point'));
    }
    if($('#hlhz-rt-0002-002-006').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-006').attr('point'));
    }
    if($('#hlhz-rt-0002-002-007').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-007').attr('point'));
    }
    if($('#hlhz-rt-0002-002-008').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-008').attr('point'));
    }
    if($('#hlhz-rt-0002-002-009').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-009').attr('point'));
    }
    if($('#hlhz-rt-0002-002-0010').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-0010').attr('point'));
    }
    if($('#hlhz-rt-0002-002-0011').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-0011').attr('point'));
    }
    if($('#hlhz-rt-0002-002-0012').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-0012').attr('point'));
    }
    if($('#hlhz-rt-0002-002-0013').prop('checked')){
      total = total +  parseInt($('#hlhz-rt-0002-002-0013').attr('point'));
    }
    // console.log('total',total)
    $('#hlhz-rt-0002-003').val(total)
    if(total === 0){
      $('#hlhz-rt-0002-004').val(total)
    }else if(total > 0 && total <= 3){
      $('#hlhz-rt-0002-004').val(1)
    }else if(total > 3 && total <= 6){
      $('#hlhz-rt-0002-004').val(2)
    }else{
      $('#hlhz-rt-0002-004').val(3)
    }
  })
}
function inintPreview(){
  $('.layui-inline, .layui-form, .showInt').removeClass('showInt')
 var allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    console.log('allResData',rtStructure);
    if(allResData && allResData.length) {
      var alleryData = allResData.filter(alleryItem => alleryItem.id === 'hlhz-rt-0003')
      console.log('alleryData',alleryData);
      if(alleryData && alleryData.length) {
        var alleryList = alleryData[0].child.filter(item => item.id === 'hlhz-rt-0003-002') || [];
        console.log('alleryList',alleryList)
        if(alleryList.length){
          var alleryItem = alleryList[0].child || [];
          // console.log('alleryItem',alleryItem)
          alleryItem.forEach((item) => {
            // console.log('item',item)
            if((item.child && !item.child.length) || item.id === "hlhz-rt-0003-002-001"){
              $(".needle").hide()
            }
          })
        }else{
          $(".needle").hide()
        }
      }
    }else{
      $(".needle").hide()
    }
}
function toNurseList(){
  let list = window.getNurseList({});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
 
  initInpAndSel('hlhz-rt-0004', userList,optHandler);
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#hlhz-rt-0004').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#hlhz-rt-0004').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
// 获取申请科室列表
function getDeptList() {
  var nurseUserInfo = JSON.parse(window.localStorage.getItem('userInfo_drug') || '{}') || {};
  var hospitalName = nurseUserInfo&&nurseUserInfo.userInfo&&nurseUserInfo.userInfo.userVO&&nurseUserInfo.userInfo.userVO.hospitalName || '';
  var params = {
    hospitalName: hospitalName
  };
  fetchAjax({
    url: api.getDeptList,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        var list = res.result || [];
        let deptList = []
        if (list && list.length > 0) {
          for (var t = 0; t < list.length; t++) {
            deptList.push({ title: list[t].deptName, id: list[t].deptCode })
          }
        }
       
        initInpAndSel('hlhz-rt-0001-001', deptList);
      }
    },
  })
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}