var urlParams = {
  optId: '',
  optName: '',
  patternId: '',
  patDocId: '',
  applyNo: '',
  busId: '',
  busType: '',
  entryType: '',
  examClass: '',
  examSubClass: ''
}
var allContentListData = {};  //contentList返回的所有数据
var applyInfo = {};
var patternInfo = {};
var patternList = [];
var rtStructure = null;
var docId = '';  //用于新增还是更新的判断
var docNo = '';
var isUpdate = true;   //申请单是否可更新
var oldPatDocId = '';
var oldResult = [];
var applyStatusRes = {};  //申请单的相关状态判断
var sourcePrefixUrl = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
var pv = null;   //只显示打印页面
var isPreview = location.href.indexOf('preview=1') > -1;
var sysCode = '';
var srQueryParams = {};
var useByApplyInfo = false;   //使用申请单的数据渲染
var saveResultParams = {}; // 保存成功后返回的result
var gmDataMap = {}; // 病理申请单的标本信息数据
var curChargeFlagObj = {}; // 当前申请单的收费标识状态
var secondConfirmFlag = false;
var printVal = null;   //3为后端生成pdf
var templateObjId = {
  '3011': {                     // 病理活体组织送检单
    age: "htzz-rt-4", // 年龄
    ageUnit: "htzz-rt-72", // 年龄单位
    subAgeNum: "htzz-rt-73", // 年龄月份
    subUnit: "htzz-rt-74", // 年龄月份单位
    examItemEle: "htzz-rt-51", // 检查项目
    subExamClassVal: "htzz-rt-38", // 检查子类
    cost: "htzz-rt-53", // 项目费用
    sampleMessage: "htzz-rt-71", // 标本信息
    reqDept: "htzz-rt-47", // 申请科室
    reqDoctor: "htzz-rt-49", // 申请医生
    noLastMensesDateVal: "htzz-rt-31", // 绝经
  },
  '3012': {                     // 基液薄层细胞TCT检验申请单
    age: "jybc-rt-4", // 年龄
    ageUnit: "jybc-rt-5", // 年龄单位
    subAgeNum: "jybc-rt-6", // 年龄月份
    subUnit: "jybc-rt-7", // 年龄月份单位
    examItemEle: "jybc-rt-53", // 检查项目
    subExamClassVal: "jybc-rt-40", // 检查子类
    cost: "jybc-rt-55", // 项目费用
    sampleMessage: "jybc-rt-78", // 标本信息
    reqDept: "jybc-rt-49", // 申请科室
    reqDoctor: "jybc-rt-51", // 申请医生
    noLastMensesDateVal: "jybc-rt-34", // 绝经
    medRecord: "jybc-rt-63", // 病人病历
  },
  '3013': {                     // 河北儿童-组织病理申请单
    age: "zzbl-rt-4", // 年龄
    ageUnit: "zzbl-rt-5", // 年龄单位
    subAgeNum: "zzbl-rt-6", // 年龄月份
    subUnit: "zzbl-rt-7", // 年龄月份单位
    examItemEle: "zzbl-rt-54", // 检查项目
    subExamClassVal: "zzbl-rt-42", // 检查子类
    cost: "zzbl-rt-56", // 项目费用
    sampleMessage: "zzbl-rt-105", // 标本信息
    reqDept: "zzbl-rt-50", // 申请科室
    reqDoctor: "zzbl-rt-52", // 申请医生
    noLastMensesDateVal: "zzbl-rt-106", // 绝经
  },
  '3014': {                     // 河北儿童-组织病理申请单(不含系肿瘤)
    age: "zzblbh-rt-4", // 年龄
    ageUnit: "zzblbh-rt-5", // 年龄单位
    subAgeNum: "zzblbh-rt-6", // 年龄月份
    subUnit: "zzblbh-rt-7", // 年龄月份单位
    examItemEle: "zzblbh-rt-54", // 检查项目
    subExamClassVal: "zzblbh-rt-42", // 检查子类
    cost: "zzblbh-rt-56", // 项目费用
    sampleMessage: "zzblbh-rt-105", // 标本信息
    reqDept: "zzblbh-rt-50", // 申请科室
    reqDoctor: "zzblbh-rt-52", // 申请医生
    noLastMensesDateVal: "zzblbh-rt-106", // 绝经
  },
}
$(function() {
  // 从缓存中获取sysCode
  srQueryParams = JSON.parse(window.localStorage.getItem('sr_query_params') || '{}');
  var frontendParam = getUrlParam('frontendParam');
  var localKey = decryptFun(frontendParam);
  sysCode = srQueryParams[localKey].sysCode || '';

  for(var key in urlParams) {
    urlParams[key] = getParamByName(key) || '';
  }
  if(!urlParams.applyNo && urlParams.busId) {
    urlParams.applyNo = urlParams.busId;
  }
  if(!urlParams.busId && urlParams.applyNo) {
    urlParams.busId = urlParams.applyNo;
  }
  pv = getParamByName('pv') || null;
  printVal = getParamByName('print') || null;
  if(!pv && printVal === '3') {
    pv = '1';
  }
  if(pv === '1') {
    $(".layout-page").addClass('pv');
    isUpdate = false;
    addLoadCoverLayer();
    getApplyResult();
  } else {
    $(".layout-page").removeClass('pv');
    isUpdate = true;
    addLoadCoverLayer();
    if(isPreview) {
      getPatternList({});
    } else {
      getHisInfo(function(info){
        console.log('申请单详情-->', info);
        checkElectApplyIsUpdate(info);
        getPatternList(info);
      });  //申请单详情
    }
  }
  if(isPreview) {
    $(".layout-footer").hide();
    $(".layout-header").hide();
    $(".layout-page").css('padding', '0')
  }
  // 处理pdf样式
  if(printVal === '3') {
    beforePrintHandler();
  }
  
  // getParDoc(patDocId);
  rtStructure = new RtStructure('#blApply .main-content');  //实例化
  window.errorCallBack = function(widgetId) {
    $(".layout-body")[0].scrollTop = $('[id="'+widgetId+'"]')[0].offsetTop;
    if($('.rt-sr-inv').parents('.laydate-w').length) {
      $('.rt-sr-inv').parents('.laydate-w').css('borderColor', 'red');
    }
  }
  // 监听模板加载完成
  window.addEventListener('message', function(res) {
    var {message, data} = res.data || {};
    if(message === 'srHtmlLoaded') {
      var patternLoadedStatus = srQueryParams.entryType !== '0' ? '1' : '2';
      $('#blApply').attr('pattern-loaded', patternLoadedStatus);
    }
  })
})

// 获取模板列表
function getPatternList(applyInfo) {
  if(isPreview && urlParams.patDocId) {
    getParDoc(urlParams.patDocId);
    return;
  }
  // if(!isPreview && (!urlParams.applyNo || !applyInfo.examClass)) {
  //   $wfMessage({
  //     content: '找不到该申请单'
  //   })
  //   removeLoadCoverLayer();
  //   return;
  // }
  var params = {
    // applyNo: urlParams.applyNo,
    // patternId: urlParams.patternId,
    patternType: '2',
    examClass: applyInfo.examClass || urlParams.examClass,
    examSubclass: applyInfo.examSubClass || urlParams.examSubClass,
    // patientSource: applyInfo.patientSource,
    pageSize: 0,
    pageNo: 0,
    mainFlag: '1'
  }
  if(isPreview) {
    params.patternId = urlParams.patternId;
  }
  getPatternListHandler(params, {
    successCb: function(res) {
      var data = res.result || [];
      patternList = data;
      if(!urlParams.patDocId) {
        if(data.length) {
          var patDocId = data[0].patDocId;
          for(var i = 0; i < data.length; i++) {
            if(data[i].patternId === urlParams.patternId) {
              patDocId = data[i].patDocId;
              break;
            }
          }
          // urlParams.patDocId = patDocId;
          oldPatDocId = patDocId;
          // console.log('patternList--oldPatDocId', oldPatDocId);
        }
      } else {
        oldPatDocId = urlParams.patDocId;
      }
      getApplyResult();
    },
    errorCb: function() {
      getApplyResult();
    }
  })
}

// 获取申请单详情
function getHisInfo(callBack) {
  if(sysCode==='GM_SAMPLE') {
    if(callBack) {
      callBack({});
    }
    // $wfMessage({
    //   content: '参数applyNo不能为空'
    // })
    // removeLoadCoverLayer();
    return;
  }
  var params = {
    applyNo: urlParams.applyNo,
  }
  fetchAjax({
    url: api.getHISApplyInfo,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        if(data && data.length > 0) {
          var obj = data[0];
          var sheetContent = obj.sheetContent ? JSON.parse(obj.sheetContent) : {};
          $.extend(obj, sheetContent);
          $.extend(obj, sheetContent.applyInfo || {}, sheetContent.otherInfo || {}, sheetContent.patientInfo || {}, sheetContent.patternFilter || {}, sheetContent.surgeryInfo || {}, sheetContent.pathologyInfo || {});
          applyInfo = obj;
          applyInfo.optName = urlParams.optName;
        }
        if(JSON.stringify(applyInfo) === '{}') {
          $wfMessage({
            content: '找不到该申请单'
          })
          removeLoadCoverLayer();
          return;
        }
        if(callBack) {
          callBack(applyInfo);
        }
      } else {
        removeLoadCoverLayer();
      }
    },
    errorFn: function() {
      removeLoadCoverLayer();
    }
  })
}


// 获取文档内容, isChange是否是切换模板
function getParDoc(patDocId, isChange) {
  $("#blApply .main-content").html('');
  var params = {
    patDocId: patDocId,
  }
  patternInfo = {};
  getParDocHtmlHandler(params, {
    successCb: function(res) {
      var data = res.result || {};
      $(".ly-c").text(data.patternName || data.fileName || '');
      patternInfo = data;
      patternInfo.docId = docId;
      patternInfo.docNo = docNo;
      var html = data.patternDoc;
      if(window.loadTemplateScriptAndCss) {
        window.loadTemplateScriptAndCss(html);
      }
      if(html) {
        if(!isChange) {
          // getApplyResult(html);
          drawContentHtml(html, oldResult);
        } else {
          // var changeResult = rtStructure.enterOptions ? rtStructure.enterOptions.resultData || [] : [];
          var changeResult = oldResult || [];
          var resultData = patternInfo.patternId === urlParams.patternId ? changeResult : [];
          drawContentHtml(html, resultData, 'edit');
        }
      } else {
        $wfMessage({
          content: '模板出错'
        })
        toggleBtn(isUpdate, 'edit');
      }
    },
    errorCb: function() {
      $wfMessage({
        content: '模板出错'
      })
      toggleBtn(isUpdate, 'edit');
    }
  })
}

// 判断申请单是否可修改
function checkElectApplyIsUpdate(info) {
  if(!info.applyNo) return;
  var params = {
    applyNo: info.applyNo
  }
  applyStatusRes = {};
  fetchAjax({
    url: api.checkElectApplyIsUpdate,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status !== '0') {
        isUpdate = false;
      } else {
        applyStatusRes = res.result || {};
      }
    },
    errorFn: function() {
      isUpdate = false;
    }
  })
}

// 设置his中读取的值
function setValByHis(hisAndFormMap, reload) {
  applyInfo.moment = getCurDateAndTime().curDate;
  applyInfo.momentTime = getCurDateAndTime().curTime;
  for(var id in hisAndFormMap) {
    var value = hisAndFormMap[id].wt ? $('[id="'+id+'"].rt-sr-w:checked').val() : ($('[id="'+id+'"].rt-sr-w').val() || $('[id="'+id+'"].rt-sr-w').text());
    if(!value && hisAndFormMap[id].wt){
      value = $('[id="'+id+'"].rt-sr-w').val() || $('[id="'+id+'"].rt-sr-w:checked').text()
    }
    if(hisAndFormMap[id].wt === '5') {
      var name =  $('[id="'+id+'"].rt-sr-w').attr('name');
      value = $('[name="'+name+'"].rt-sr-w:checked').val();
    }
    if(value && !reload) {
      continue;
    }
    var keys = hisAndFormMap[id].hisKey.split(',');
    for(var j = 0; j < keys.length; j++) {
      var resVal = applyInfo[keys[j]]
      if(resVal) {
        if(keys[j] === 'identity' && resVal.length) {
          resVal = resVal[0].idNumber;
        } 
        if(keys[j] === 'flags' && hisAndFormMap[id].sex && hisAndFormMap[id].sex !== applyInfo.sex) {
          break;
        }
        if(keys[j] === 'flags' && resVal && typeof resVal === 'string') {
          resVal = resVal[hisAndFormMap[id].index];
        }
        if(keys[j] === 'momentTime' && resVal && typeof resVal === 'string') {
          var formatVal = $('[id="'+id+'"].rt-sr-w').attr('val-format');
          if(formatVal){
            resVal = dayjs(applyInfo.moment + ' ' +resVal).format(formatVal);
          }
        }
        if(hisAndFormMap[id].wt) {
          if(hisAndFormMap[id].wt === '4' || hisAndFormMap[id].wt === '5') {
            if(resVal === hisAndFormMap[id].value) {
              // $('.rt-sr-w[value="'+resVal+'"][id="'+id+'"]').prop('checked', true);
              $('.rt-sr-w[id="'+id+'"]').prop('checked', true);
            }
          } else if(hisAndFormMap[id].wt === '1') {
            $('[id="'+id+'"].rt-sr-w').val(resVal);
            $('[id="'+id+'"].rt-sr-w').text(resVal);
          } else {
            $('.rt-sr-w[id="'+id+'"]').find('option[value="'+resVal+'"]').attr('selected', true);
          }
        } else {
          $('[id="'+id+'"].rt-sr-w').val(resVal);
          $('[id="'+id+'"].rt-sr-w').text(resVal);
        }
        break;
      }
    }
  }
}

// 获取已保存的结果数据,isSave是否保存后重新渲染
function getApplyResult(isSave) {
  if(isPreview || !urlParams.applyNo) {
    getParDoc(oldPatDocId);
    return;
  }
  if(isSave && srQueryParams[localKey] && !srQueryParams[localKey].busId) {
    var localQuery = srQueryParams[localKey];
    localQuery.busId = urlParams.applyNo;
    getOrSetCurReportLocal('set', localQuery, localKey)
  }
  docId = '';
  docNo = '';
  oldResult = [];
  var params = {
    applyNo: urlParams.applyNo,
    busType: '2',
    busId: urlParams.applyNo,
    newestFlag: true,
    mainFlag: '1',
    patDocId: urlParams.patDocId ? oldPatDocId : ''
  }
  getResJsonHandler(params, {
    successCb: function(res) {
      var allContent = res.result && res.result.length ? res.result[0] : {};
      allContentListData = allContent;
      var docParse = allContent.docContent ? JSON.parse(allContent.docContent) : {}
      var result = docParse.docContent ? (docParse.docContent.docAttr || []) : [];
      oldResult = result;
      if(JSON.stringify(allContent) !== '{}') {
        docId = allContent.docId;
        patternInfo.docId = docId;
        docNo = allContent.docNo;
        patternInfo.docNo = docNo;
        oldPatDocId = allContent.patDocId ? allContent.patDocId : oldPatDocId;
        urlParams.patternId = allContent.patternId;
        urlParams.patDocId = allContent.patDocId;
      }
      if(urlParams.applyNo) {
        oldPatDocId = urlParams.patDocId || oldPatDocId || '';
        getApplyInfoWithoutExamNo();
      }
      if(isSave) {
        drawContentHtml(patternInfo.patternDoc, oldResult, 'view')
      } else {
        getParDoc(oldPatDocId);
      }
    },
    errorCb: function() {
      if(!isSave) {
        getParDoc(oldPatDocId);
      }
    }
  })
}

// 渲染页面
function drawContentHtml(html, result, editType) {
  $('#blApply').removeAttr('pattern-loaded');
  // var setNull = html.indexOf('data-setnull="false"') > -1 ? false : true;
  var setNull = false;
  // 申请单存在数据
  if(!editType && useByApplyInfo) {
    editType = urlParams.entryType === '1' ? 'view' : 'edit';
  }
  var isSavedReport = result && result.length > 0;
  var publicInfo = $.extend(urlParams, applyInfo);
  if(!result.length && isUpdate) {
    if(useByApplyInfo && editType === 'view') {
      rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: publicInfo, srcUrl: sourcePrefixUrl, setNull: setNull, patternInfo: patternInfo, setValFromApply: true, isSavedReport: isSavedReport, allContentListData: allContentListData});
      useByApplyInfo = false;
    } else {
      editType = 'edit';
      rtStructure.rebuildStructureReport({htmlContent:html, resultData:result, publicInfo: publicInfo, srcUrl: sourcePrefixUrl, setNull: setNull, patternInfo: patternInfo, setValFromApply: true, isSavedReport: isSavedReport, allContentListData: allContentListData});
    }
  } else {
    if(!editType && urlParams.entryType === '1') {
      // 预览
      editType = 'view';
      rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: publicInfo, srcUrl: sourcePrefixUrl, setNull: setNull, patternInfo: patternInfo, isSavedReport: isSavedReport, allContentListData: allContentListData});
    } else {
      if(editType === 'edit') {
        rtStructure.rebuildStructureReport({htmlContent:html, resultData:result, publicInfo: publicInfo, srcUrl: sourcePrefixUrl, setNull: setNull, patternInfo: patternInfo, isSavedReport: isSavedReport, allContentListData: allContentListData});
      } else {
        rtStructure.previewStructureReport({htmlContent:html, resultData:result, loadViewScript:true, publicInfo: publicInfo, srcUrl: sourcePrefixUrl, setNull: setNull, patternInfo: patternInfo, isSavedReport: isSavedReport, allContentListData: allContentListData});
      }
    }
  }
  toggleBtn(isUpdate, editType)
}

// 点击编写按钮事件
function editApply() {
  toggleBtn(true, 'edit');
  var html = rtStructure.enterOptions.htmlContent;
  var result = rtStructure.enterOptions.resultData;
  var applyInfo = rtStructure.enterOptions.publicInfo;
  drawContentHtml(html, result, 'edit')
}

// 调整操作按钮的显示隐藏
function toggleBtn(isUpdate, type) {
  // 不可编辑全部隐藏
  if(!isUpdate) {
    $("#editBtn, #saveBtn, #tempBtn, #reloadBtn").hide();
    if(type === 'view') {
      $("#printBtn").show().attr('disabled', false);
    }
  } else {
    if(type === 'edit') {
      $("#saveBtn, #tempBtn, #reloadBtn").show().attr('disabled', false);
      $("#editBtn, #printBtn").hide();
    } else {
      $("#saveBtn, #tempBtn, #reloadBtn").hide();
      $("#editBtn, #printBtn").show().attr('disabled', false);
    }
  }
  $("#applyBtn").show().attr('disabled', false);
  // sysCode=GM_SAMPLE时，隐藏【模板】【重载】【申请列表】按钮
  if(sysCode==='GM_SAMPLE') {
    // $("#tempBtn").hide();
    $("#reloadBtn").hide();
    $("#applyBtn").hide();
    $('#blApply.layout-page').addClass('bljcsdq-page');
    $('.bljcsdq-page .main-content').addClass('bljcsdq-content');
    $('.bljcsdq-page .layout-header').hide();
    $('.bljcsdq-page.layout-page').css('paddingTop','0');
    $("#closeBtn").show();
  }
}

// 确认保存
function confirmSaveApply(vm) {
  var applySaveTipMark = rtDialog.find("#applySaveTipMark").val();
  if(!applySaveTipMark) {
    $wfMessage({
      content: '请输入回溯原因'
    })
    return;
  }
  var params = {
    applyNo: urlParams.applyNo,
    optId: urlParams.optId,
    optName: urlParams.optName,
    reSaveApplyReason: applySaveTipMark
  }
  $(vm).attr('disabled', true);
  fetchAjax({
    url: api.saveAReason,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        saveApplyResult($('#saveBtn'), true);
        removeRtDialog()
      } else {
        $(vm).attr('disabled', false);
      }
    },
    errorFn: function() {
      $(vm).attr('disabled', false);
    }
  })
}

// 保存
function saveApplyResult(vm, ignoreTip) {
  // if(!ignoreTip) {
  //   var result = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.resultData : [];
  //   if(result && result.length) {
  //     var tipHtml = '<div>';
  //     tipHtml += '<textarea id="applySaveTipMark" class="rt-sr-t" rows="10" style="resize:none;width:100%" placeholder="请输入回溯原因"></textarea>';
  //     tipHtml += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  //     tipHtml += '<button onclick="confirmSaveApply(this)" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  //     tipHtml += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消保存</button>';
  //     tipHtml += '</div>';
  //     tipHtml += '</div>';
  //     drawDialog({
  //       title: '回溯原因',
  //       modal: true,
  //       content: tipHtml,
  //       style: {
  //         'width': '500px',
  //       }
  //     });
  //     return;
  //   }
  // }
  var ele = $(vm);
  $(".sampleTxt, .examTxt").css('color', '#606266');
  $('.rt-sr-inv').parents('.laydate-w').css('borderColor', '#DCDFE6');
  var sampleByTable = $('.t-pg').attr('data-sampleByTable') || '';
  var dataObj = createResultData();
  var resultData = dataObj.resultData
  var idMapResult = dataObj.idMapResult;
  var sampleEditPart = $('.sampleEditPart');
  var eItemEditPart = $('.eItemEditPart');
  if((!sampleByTable && sampleEditPart.length && sampleEditPart.html() === '') ||
      sampleByTable && !$(".sampleEditPart .sampleItem").length) {
    $wfMessage({
      content: ('【标本信息】不能为空')
    });
    if($(".sampleTxt").length) {
      $(".sampleTxt").css('color', 'red');
      $(".layout-body")[0].scrollTop = $(".sampleTxt")[0].offsetTop;
    }
    return;
  }
  if(!sampleByTable && eItemEditPart.length && eItemEditPart.html() === '') {
    $wfMessage({
      content: ('【检查项目】不能为空')
    });
    $(".examTxt").css('color', 'red');
    $(".layout-body")[0].scrollTop = $(".examTxt")[0].offsetTop;
    return;
  }
  var params = {
    optType: '0',  
    optId: urlParams.optId,
    optName: urlParams.optName,
    patientInfo: applyInfo,
    resultData: resultData,
    patternInfo: patternInfo,
    busType: urlParams.busType || '2',
    busId: urlParams.busId,
    docId: docId,
    docNo: docNo,
    mainFlag: '1'
  };
  var postParams = saveParams(params);
  ele.attr('disabled', true);
  let { patternId = '' } = postParams;
  // patternId === '3010' ? saveApplyInfoApi(idMapResult, postParams, ele) : "";
  templateObjId[patternId] !== undefined ? saveCommonApplyInfoApi(idMapResult, postParams, ele, patternId) : saveApplyInfoApi(idMapResult, postParams, ele)
}

// 保存json接口
function saveResultApi(postParams, ele) {
  saveHandler(postParams, {
    successCb: function(res) {
      getApplyResult(true);
      ele.attr('disabled', false);
    },
    errorCb: function() {
      ele.attr('disabled', false);
    }
  })
}

// 更新申请单详情接口
function saveApplyInfoApi(idMapResult, postParams, ele) {
  var applyUpdateParams = {
    applyNo: urlParams.applyNo,
    optId: urlParams.optId,
    optName: urlParams.optName,
  };
  if(saveApplyParams && JSON.stringify(saveApplyParams) !== '{}') {
    for(var key in saveApplyParams) {
      var id = saveApplyParams[key].id;
      if(['applyItemList', 'gmSampleList','bljcApplyItemList','bljcGmSampleList'].indexOf(key) > -1) {
        applyUpdateParams[key] = []
        if(key === 'applyItemList') {
          if(addExamItemList && addExamItemList.length) {
            addExamItemList.forEach(function(examItem) {
              applyUpdateParams[key].push({
                itemName: examItem.itemName,
                itemCode: examItem.itemCode || (examItem.parentName ? examItem.parentName.split('__')[1] : ''),
                examSubClass: examItem.parentName ? examItem.parentName.split('__')[0] : ''
              })
            })
          }
        }
        if(key === 'gmSampleList') {
          addSampleList.forEach(function(sItem) {
            var sid = sItem.id;
            if(!idMapResult[sid] || !idMapResult[sid].val) {
              return;
            }
            applyUpdateParams[key].push({
              sampleName: idMapResult[sid] ? idMapResult[sid].val : '',  //标本名称
              samplePart: idMapResult[sid + '.01'] ? idMapResult[sid + '.01'].val : '',  //标本部位
              inVitroDateTime: idMapResult[sid + '.02'] ? idMapResult[sid + '.02'].val : '',  //离体时间
              fixedDateTime: idMapResult[sid + '.03'] ? idMapResult[sid + '.03'].val : '',  //固定时间
              barcodeNo: idMapResult[sid + '.04'] ? idMapResult[sid + '.04'].val : '',  //标本编号
              sampleNo: idMapResult[sid + '.00'] ? idMapResult[sid + '.00'].val : '',  //标本编号(主键)
            })
          })
        }
        // 特殊处理病理检查申请单检查项目、标本信息
        if(key === 'bljcApplyItemList') {
          let examItemEle = $('#bljcsqd-rt-40');
          let itemName = examItemEle.val();
          let itemCode = examItemEle.attr('itemCode');
          let subExamClassVal = $('#bljcsqd-rt-33').val();
          let cost = $('#bljcsqd-rt-46').val();
          applyUpdateParams['applyItemList'] = [];
          if(itemCode || cost) {
            applyUpdateParams['applyItemList'].push({
              itemName: itemName || '',
              itemCode: itemCode || '',
              examSubClass: subExamClassVal || '',
              cost: cost || '',
            });
          }
        }
        if(key === 'bljcGmSampleList') {
          var curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
          if(curKeyValData['bljcsqd-rt-65']) {
            applyUpdateParams['gmSampleList'] = [];
            let parData = curKeyValData['bljcsqd-rt-65'];
            let childD1 = parData.child || [];
            let delSampleNo = [];
            let keyAndIdMap = {
              '01': 'sampleName',
              '02': 'samplePart',
              '03': 'sampleType',
              '04': 'inVitroDateTime',
              '05': 'fixedDateTime',
              '06': 'fixLiquid',
              '07': 'blockCount',
              '08': 'sampleWeight',
            }
            childD1.map(function(cItem1,cIdx1) {
              let childD2 = cItem1.child || [];
              var obj = {};
              let cId1 = cItem1.id;
              childD2.map(function(cItem2) {
                var flagId = cItem2.id ? cItem2.id.split('-')[2].split('.')[0] : '';
                obj[keyAndIdMap[flagId]] = keyAndIdMap[flagId] === 'blockCount' || keyAndIdMap[flagId] === 'sampleWeight' ?
                  Number(cItem2.val) : cItem2.val;
              });
              for(let k in keyAndIdMap) {
                if(!obj[keyAndIdMap[k]]) {
                  obj[keyAndIdMap[k]] = keyAndIdMap[k] === 'blockCount' || keyAndIdMap[k] === 'sampleWeight' ? 0 : '';
                }
              }
              if(gmDataMap[cId1]) {
                let {sampleNo='',sampleStatus=''} = gmDataMap[cId1];
                sampleNo ? obj['sampleNo'] = sampleNo : '';
                sampleStatus ? obj['sampleStatus'] = sampleStatus : '';
              }
              applyUpdateParams['gmSampleList'].push(obj);
            })
            // 删除节点
            for(let key in gmDataMap) {
              let {sampleNo=''} = gmDataMap[key];
              if(!curKeyValData[key]) {
                sampleNo ? delSampleNo.push(sampleNo) : '';
              }
            }
            applyUpdateParams['delSampleNo'] = delSampleNo.length ? delSampleNo.join(',') : '';
          }
        }
      } else if(key === 'flags') {
        var ids = saveApplyParams[key].ids;
        for(var k = 0; k < ids.length; k++) {
          var idItem = ids[k];
          var idVal = idMapResult[idItem.id] ? idMapResult[idItem.id].val : '';
          if(idVal) {
            var flags = applyInfo.flags || '0000000000000000000000';
            var flagArr = flags.split('');
            flagArr[idItem.index] = idItem.value;
            applyUpdateParams[key] = flagArr.join('');
            break;
          }
        }
      }else if(key === 'bljcExamClasses') {
        // let examClass = getParamByName('examClass');
        let examClass = applyInfo.examClass || urlParams.examClass;
        applyUpdateParams['examClass'] = examClass;
      }else if(key === 'bljcReqDept') {
        let reqDeptEle = $('#bljcsqd-rt-42');
        let reqDeptName = reqDeptEle.val();
        let reqDept = reqDeptEle.attr('deptCode');
        applyUpdateParams['reqDept'] = reqDept;
        applyUpdateParams['reqDeptName'] = reqDeptName;
      }else if(key === 'bljcReqPhysician') {
        let reqPhysicianEle = $('#bljcsqd-rt-44');
        let reqPhysician = reqPhysicianEle.val();
        let reqPhysicianCode = reqPhysicianEle.attr('staffNo');
        applyUpdateParams['reqPhysicianCode'] = reqPhysicianCode;
        applyUpdateParams['reqPhysician'] = reqPhysician;
      }else if(key==='infectiousFlag' || key === 'freezeFlag' || key === 'chargeFlag' || key === 'ihcFlag' || key === 'specificStainFlag') {
        var ids = saveApplyParams[key].ids;
        for(var k = 0; k < ids.length; k++) {
          var idItem = ids[k];
          var idVal = idMapResult[idItem.id] ? idMapResult[idItem.id].val : '';
          if(idVal) {
            idVal = idVal==='是' ? 1 : 0;
            applyUpdateParams[key] = idVal;
            break;
          }
        }
      }else if(key === 'ageAndMonth') {
        var age = $('#bljcsqd-rt-4').val() || '';
        var ageUnit = $('#bljcsqd-rt-68').val() || '';
        var subAgeNum = $('#bljcsqd-rt-69').val() || '';
        var subUnit = $('#bljcsqd-rt-70').text() || '';
        var ageStr1 = age ? age + ageUnit : '';
        var ageStr2 = subAgeNum ? subAgeNum + subUnit : '';
        var ageStr = ageStr1 + ageStr2;
        applyUpdateParams['age'] = ageStr;
      }else if(key === 'noLastMensesDate') {
        // √了传1，不√传0
        let noLastMensesDateVal = $('#bljcsqd-rt-25').is(':checked') ? '1' : '0';
        applyUpdateParams['noLastMensesDate'] = noLastMensesDateVal;
      }else {
        applyUpdateParams[key] = idMapResult[id] ? idMapResult[id].val : '';
      }
    }
  } else {
    // 更新申请单成功后更新结构化json数据
    saveResultApi(postParams, ele);
    return;
  }
  // 病理检查申请单的标本名称不能为空
  // 离体时间和固定时间的时间差不能超过30分钟
  if(sysCode === 'GM_SAMPLE') {
    applyUpdateParams['preExamNo'] = preExamNo; // 加入申请单回传examNo
    let bljcApplyItemList = applyUpdateParams['gmSampleList'];
    let sampleNameFlag = false;
    let compareTimeFlag = false;
    for(let item of bljcApplyItemList) {
      let {sampleName='',inVitroDateTime='',fixedDateTime=''} = item;
      compareTimeFlag = getTimeDifference(inVitroDateTime,fixedDateTime);
      if(compareTimeFlag) break;
      if(!sampleName) {
        sampleNameFlag = true;
        break;
      }
    }
    if(sampleNameFlag) {
      $wfMessage({
        content: ('【标本名称】不能为空')
      });
      ele.attr('disabled', false);
      return
    }
    if(compareTimeFlag) {
      $wfMessage({
        content: ('离体时间和固定时间的时间差不能超过30分钟')
      });
      ele.attr('disabled', false);
      return
    }
  }
  var changeResult = rtStructure.enterOptions ? rtStructure.enterOptions.resultData || [] : [];
  // 去掉二次确认提示。后端处理
  // if(sysCode==='GM_SAMPLE' && changeResult.length) {
  //   let chargeFlagVal = applyUpdateParams['chargeFlag'];
  //   getCurChargeFlag();
  //   let {result = ''} = curChargeFlagObj;
  //   if(result && parseInt(result) !== parseInt(chargeFlagVal)) {
  //     let option = {
  //       content: '当前申请单已由其他系统修改为【已收费】，是否确认更新为【未收费】',
  //       modal: true,
  //       isShowFooter: true,
  //       confirmBtnName: '确认',
  //       cancelBtnName: '取消',
  //       confirmFun:confirmToSave,
  //     }
  //     drawDialog(option);
  //     ele.attr('disabled', false);
  //     if(!secondConfirmFlag) {return;}
  //   }
  // }
  delete applyUpdateParams['bljcApplyItemList'];
  delete applyUpdateParams['bljcGmSampleList'];
  delete applyUpdateParams['ageAndMonth'];
  secondConfirmFlag = false;
  addLoadCoverLayer()
  fetchAjax({
    url: api.addOrUpdateApply,
    data: JSON.stringify(applyUpdateParams),
    successFn: function(res) {
      if(res.status == '0') {
        let applyNo = res.result && res.result.applyNo;
        !postParams.busId ?  postParams.docContent.busInfo.busId = applyNo : '';
        if(!urlParams.busId) {
          urlParams.busId = applyNo;
        }
        if(!urlParams.applyNo) {
          urlParams.applyNo = urlParams.busId;
        }
        saveResultParams = res.result || {};
        sendMessageHandle();
        // 更新申请单成功后更新结构化json数据
        saveResultApi(postParams, ele);
      } else {
        ele.attr('disabled', false);
        removeLoadCoverLayer();
      }
    },
    errorFn: function() {
      saveResultParams = {};
      ele.attr('disabled', false);
      removeLoadCoverLayer();
    }
  })
}

// 更新申请单详情接口通用版
function saveCommonApplyInfoApi(idMapResult, postParams, ele, patternId) {
  var specialIdObj = templateObjId[patternId]; // 特殊处理数据
  var applyUpdateParams = {
    applyNo: urlParams.applyNo,
    optId: urlParams.optId,
    optName: urlParams.optName,
  };
  if (saveApplyParams && JSON.stringify(saveApplyParams) !== '{}') {
    for (var key in saveApplyParams) {
      var id = saveApplyParams[key].id;
      if (['applyItemList', 'gmSampleList', 'bljcApplyItemList', 'bljcGmSampleList'].indexOf(key) > -1) {
        applyUpdateParams[key] = []
        if (key === 'applyItemList') {
          if (addExamItemList && addExamItemList.length) {
            addExamItemList.forEach(function (examItem) {
              applyUpdateParams[key].push({
                itemName: examItem.itemName,
                itemCode: examItem.itemCode || (examItem.parentName ? examItem.parentName.split('__')[1] : ''),
                examSubClass: examItem.parentName ? examItem.parentName.split('__')[0] : ''
              })
            })
          }
        }
        if (key === 'gmSampleList') {
          addSampleList.forEach(function (sItem) {
            var sid = sItem.id;
            if (!idMapResult[sid] || !idMapResult[sid].val) {
              return;
            }
            applyUpdateParams[key].push({
              sampleName: idMapResult[sid] ? idMapResult[sid].val : '',  //标本名称
              samplePart: idMapResult[sid + '.01'] ? idMapResult[sid + '.01'].val : '',  //标本部位
              inVitroDateTime: idMapResult[sid + '.02'] ? idMapResult[sid + '.02'].val : '',  //离体时间
              fixedDateTime: idMapResult[sid + '.03'] ? idMapResult[sid + '.03'].val : '',  //固定时间
              barcodeNo: idMapResult[sid + '.04'] ? idMapResult[sid + '.04'].val : '',  //标本编号
              sampleNo: idMapResult[sid + '.00'] ? idMapResult[sid + '.00'].val : '',  //标本编号(主键)
            })
          })
        }
        // 特殊处理病理检查申请单检查项目、标本信息
        if (key === 'bljcApplyItemList') {
          let examItemEle = $(`#${specialIdObj.examItemEle}`);
          let itemName = examItemEle.val();
          let itemCode = examItemEle.attr('itemCode');
          let subExamClassVal = $(`#${specialIdObj.subExamClassVal}`).val();
          let cost = $(`#${specialIdObj.cost}`).val();
          applyUpdateParams['applyItemList'] = [];
          if (itemCode || cost) {
            applyUpdateParams['applyItemList'].push({
              itemName: itemName || '',
              itemCode: itemCode || '',
              examSubClass: subExamClassVal || '',
              cost: cost || '',
            });
          }
        }
        if (key === 'bljcGmSampleList') {
          let { sampleMessage = '' } = specialIdObj
          var curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
          if (sampleMessage && curKeyValData[sampleMessage]) {
            applyUpdateParams['gmSampleList'] = [];
            let parData = curKeyValData[sampleMessage];
            let childD1 = parData.child || [];
            let delSampleNo = [];
            childD1.map(function (cItem1, cIdx1) {
              let childD2 = cItem1.child || [];
              var obj = {};
              let cId1 = cItem1.id;
              childD2.map(function (cItem2) {
                var flagId = cItem2.id ? cItem2.id.split('-')[2].split('.')[0] : '';
                flagId === '01' ? obj['sampleName'] = cItem2.val : '';
                flagId === '02' ? obj['samplePart'] = cItem2.val : '';
                flagId === '03' ? obj['sampleType'] = cItem2.val : '';
                flagId === '04' ? obj['inVitroDateTime'] = cItem2.val : '';
                flagId === '05' ? obj['fixedDateTime'] = cItem2.val : '';
                flagId === '06' ? obj['fixLiquid'] = cItem2.val : '';
              });
              if (gmDataMap[cId1]) {
                let { sampleNo = '', sampleStatus = '' } = gmDataMap[cId1];
                sampleNo ? obj['sampleNo'] = sampleNo : '';
                sampleStatus ? obj['sampleStatus'] = sampleStatus : '';
              }
              applyUpdateParams['gmSampleList'].push(obj);
            })
            // 删除节点
            for (let key in gmDataMap) {
              let { sampleNo = '' } = gmDataMap[key];
              if (!curKeyValData[key]) {
                sampleNo ? delSampleNo.push(sampleNo) : '';
              }
            }
            applyUpdateParams['delSampleNo'] = delSampleNo.length ? delSampleNo.join(',') : '';
          }
        }
      }else if(key === 'flags') {
        var ids = saveApplyParams[key].ids;
        for(var k = 0; k < ids.length; k++) {
          var idItem = ids[k];
          var idVal = idMapResult[idItem.id] ? idMapResult[idItem.id].val : '';
          if(idVal) {
            var flags = applyInfo.flags || '0000000000000000000000';
            var flagArr = flags.split('');
            flagArr[idItem.index] = idItem.value;
            applyUpdateParams[key] = flagArr.join('');
            break;
          }
        }
      }else if(key === 'bljcExamClasses') {
        // let examClass = getParamByName('examClass');
        let examClass = applyInfo.examClass || urlParams.examClass;
        applyUpdateParams['examClass'] = examClass;
      }else if(key === 'bljcReqDept') {
        let reqDeptEle = $(`#${specialIdObj.reqDept}`);
        let reqDeptName = reqDeptEle.val();
        let reqDept = reqDeptEle.attr('deptCode');
        applyUpdateParams['reqDept'] = reqDept;
        applyUpdateParams['reqDeptName'] = reqDeptName;
      }else if(key === 'bljcReqPhysician') {
        let reqPhysicianEle = $(`#${specialIdObj.reqDoctor}`);
        let reqPhysician = reqPhysicianEle.val();
        let reqPhysicianCode = reqPhysicianEle.attr('staffNo');
        applyUpdateParams['reqPhysicianCode'] = reqPhysicianCode;
        applyUpdateParams['reqPhysician'] = reqPhysician;
      }else if(key==='infectiousFlag' || key === 'freezeFlag' || key === 'chargeFlag' || key === 'ihcFlag' || key === 'specificStainFlag' || key === 'examMode') {
        var ids = saveApplyParams[key].ids;
        for(var k = 0; k < ids.length; k++) {
          var idItem = ids[k];
          var idVal = idMapResult[idItem.id] ? idMapResult[idItem.id].val : '';
          if(idVal) {
            if(key !== 'examMode') {
              idVal = idVal==='是' ? 1 : 0;
            }
            applyUpdateParams[key] = idVal;
            break;
          }
        }
      }else if(key === 'ageAndMonth') {
        var age = $(`#${specialIdObj.age}`).val() || '';
        var ageUnit = $(`#${specialIdObj.ageUnit}`).val() || '';
        var subAgeNum = $(`#${specialIdObj.subAgeNum}`).val() || '';
        var subUnit = $(`#${specialIdObj.subUnit}`).text() || '';
        var ageStr1 = age ? age + ageUnit : '';
        var ageStr2 = subAgeNum ? subAgeNum + subUnit : '';
        var ageStr = ageStr1 + ageStr2;
        applyUpdateParams['age'] = ageStr;
      }else if(key === 'noLastMensesDate') {
        // √了传1，不√传0
        let noLastMensesDateVal = $(`#${specialIdObj.noLastMensesDateVal}`).is(':checked') ? '1' : '0';
        applyUpdateParams['noLastMensesDate'] = noLastMensesDateVal;
      }else if(key === 'medRecord' && patternId === '3012'){
        var brblList = [];
        var ids = saveApplyParams[key].ids;
        for(var k = 0; k < ids.length; k++) {
          var idItem = ids[k];
          var idVal = idMapResult[idItem.id] ? idMapResult[idItem.id].val : '';
          if(idVal) {
            brblList.push(idVal)
          }
        }
        brblList && brblList.length > 0 ? applyUpdateParams[key] = brblList.join(',') : '';
      }else {
        applyUpdateParams[key] = idMapResult[id] ? idMapResult[id].val : '';
      }
    }
  }else {
    // 更新申请单成功后更新结构化json数据
    saveResultApi(postParams, ele);
    return;
  }
  // 病理检查申请单的标本名称不能为空
  // 离体时间和固定时间的时间差不能超过30分钟
  if(sysCode === 'GM_SAMPLE') {
    let bljcApplyItemList = applyUpdateParams['gmSampleList'];
    let sampleNameFlag = false;
    let compareTimeFlag = false;
    for(let item of bljcApplyItemList) {
      let {sampleName='',inVitroDateTime='',fixedDateTime=''} = item;
      compareTimeFlag = getTimeDifference(inVitroDateTime,fixedDateTime);
      if(compareTimeFlag) break;
      if(!sampleName) {
        sampleNameFlag = true;
        break;
      }
    }
    if(sampleNameFlag) {
      $wfMessage({
        content: ('【标本名称】不能为空')
      });
      ele.attr('disabled', false);
      return
    }
    if(compareTimeFlag) {
      $wfMessage({
        content: ('离体时间和固定时间的时间差不能超过30分钟')
      });
      ele.attr('disabled', false);
      return
    }
  }
  var changeResult = rtStructure.enterOptions ? rtStructure.enterOptions.resultData || [] : [];
  // 去掉二次确认提示。后端处理
  // if(sysCode==='GM_SAMPLE' && changeResult.length) {
  //   let chargeFlagVal = applyUpdateParams['chargeFlag'];
  //   getCurChargeFlag();
  //   let {result = ''} = curChargeFlagObj;
  //   if(result && parseInt(result) !== parseInt(chargeFlagVal)) {
  //     let option = {
  //       content: '当前申请单已由其他系统修改为【已收费】，是否确认更新为【未收费】',
  //       modal: true,
  //       isShowFooter: true,
  //       confirmBtnName: '确认',
  //       cancelBtnName: '取消',
  //       confirmFun:confirmToSave,
  //     }
  //     drawDialog(option);
  //     ele.attr('disabled', false);
  //     if(!secondConfirmFlag) {return;}
  //   }
  // }
  delete applyUpdateParams['bljcApplyItemList'];
  delete applyUpdateParams['bljcGmSampleList'];
  delete applyUpdateParams['ageAndMonth'];
  secondConfirmFlag = false;
  addLoadCoverLayer();
  fetchAjax({
    url: api.addOrUpdateApply,
    data: JSON.stringify(applyUpdateParams),
    successFn: function(res) {
      if(res.status == '0') {
        let applyNo = res.result && res.result.applyNo;
        !postParams.busId ?  postParams.docContent.busInfo.busId = applyNo : '';
        if(!urlParams.busId) {
          urlParams.busId = applyNo;
        }
        if(!urlParams.applyNo) {
          urlParams.applyNo = urlParams.busId;
        }
        saveResultParams = res.result || {};
        sendMessageHandle();
        // 更新申请单成功后更新结构化json数据
        saveResultApi(postParams, ele);
      } else {
        ele.attr('disabled', false);
        removeLoadCoverLayer();
      }
    },
    errorFn: function() {
      saveResultParams = {};
      ele.attr('disabled', false);
      removeLoadCoverLayer();
    }
  })
}



// 当不存在examNo,即examDoc不存在数据时，获取申请单详情
function getApplyInfoWithoutExamNo(cb) {
  var successCb = cb ? cb.successCb : null;
  var errorCb = cb ? cb.errorCb : null;
  var params = {
    applyNo: urlParams.applyNo,
  }
  addLoadCoverLayer();
  fetchAjax({
    url: api.getApplyDetailInfo,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        if(res && res.result) {
          useByApplyInfo = true;
        }
        applyInfo = $.extend(applyInfo, res.result || {});
        let { applyNo='' } = applyInfo;
        saveResultParams = { applyNo };
        if(successCb && typeof successCb === 'function') {
          successCb(res);
        }
      } else {
        if(errorCb && typeof errorCb === 'function') {
          errorCb();
        }
      }
    },
    errorFn: function() {
      if(errorCb && typeof errorCb === 'function') {
        errorCb();
      }
    },
    completeFn: function() {
      removeLoadCoverLayer();
    }
  })
}
// 打印前处理
function beforePrintHandler() {
  $(".wfmsgbox").remove();
  $("#blApply").addClass('print-status');
}
// 打印
function printApply() {
  beforePrintHandler();
  printAfterImgLoaded();
}

function printAfterImgLoaded() {
  var imageList = $('body img:visible')
  var imgCount = imageList.length;
  var loadedImgCount = 0;
  var loadFlag = false;
  var finishImgLoad = function() {
    loadedImgCount++;
    if(loadedImgCount !== imgCount) {
      waitImgLoad(imageList.eq(loadedImgCount)[0].src)
    }
  }
  var waitImgLoad = function(src) {
    loadFlag = true;
    var image = new Image();
    image.src = src;
    if(!src) {
      console.log('图片不存在');
      finishImgLoad();
    }
    image.onload = function() {
      console.log('图片加载完成');
      finishImgLoad();
    }
    image.error = function() {
      console.log('图片加载失败');
      finishImgLoad();
    }
  }
  var timer = setInterval(function() {
    if(imgCount > 0 && loadedImgCount !== imgCount) {
      if(!loadFlag) {
        waitImgLoad(imageList.eq(loadedImgCount)[0].src)
      }
      return;
    }
    clearInterval(timer);
    window.print();
    updateApplyPrintStatus();
    setTimeout(function() {
      $("#blApply").removeClass('print-status');
    }, 50)
  }, 50);
}

// 更新申请单打印状态
function updateApplyPrintStatus() {
  let params = { applyNo: urlParams.applyNo }
  console.log('更新申请单打印状态：', urlParams.applyNo);
  fetchAjax({
    url: api.updatePrintStatus,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
    },
    errorFn: function(e) {
      console.log(e);
    }
  })
}

// 打开模板
function openTemplateHandler() {
  var html = templateListHtml(patternList, selectPattern)
  drawDialog({
    title: '选择模板',
    modal: true,
    content: html,
    style: {
      'width': '413px',
    }
  });
}

// 重载
function reloadApply() {
  addLoadCoverLayer();
  getHisInfo(function(info) {
    if(rtStructure) {
      rtStructure.enterOptions.publicInfo = info;
      rtStructure.enterOptions.resultData = [];
      rtStructure.setValByCode(true);
      console.log('重载--->',info);
      try {
        if(setValByHis && hisAndFormMap) {
          setValByHis(hisAndFormMap, true)
        }
      } catch (err) {
        console.error(err);
      }
    }
    removeLoadCoverLayer();
  })
}

// 模板列表
function templateListHtml(data, successFn) {
  var html = '<div class="item-table" style="border:1px solid #C8D7E6;width:388px;">';
  html += '<style>.selText{color:#1885F2}</style>';
  html += '<div class="t-body" style="background: #F5F7FA;overflow: auto;height:300px">';
  if(data && data.length) {
    for(var i = 0; i < data.length; i++) {
      html += '<div id="'+data[i].patDocId+'" onclick="changeTempSelf(this)" style="padding: 8px 10px;cursor: pointer;" class="'+(patternInfo.patDocId === data[i].patDocId?'selText':'')+'">'+data[i].patternName+'</div>';
    }
  } else {
    html += '<div style="color:#666;padding:5px">暂无数据</div>';
  }
  html += '</div>';
  html += '</div>';
  html += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  html += '<button onclick="selectPattern(this, '+successFn+')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  html += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">关闭</button>';
  html += '</div>';
 return html;
}

// 切换模板
function selectPattern(patDocId) {
  if(rtDialog) {
    patDocId = rtDialog.find('.selText').attr('id');
  }
  if(!patDocId) {
    $wfMessage({
      content: '请选择模板'
    })
    return;
  }
  if(patDocId === patternInfo.patDocId) {
    // $wfMessage({
    //   type: 'warn',
    //   content: '该模板正在使用'
    // })
    rtDialog && removeRtDialog();
    return;
  }
  getParDoc(patDocId, true);
  rtDialog && removeRtDialog();
}

function changeTempSelf(ele) {
  if($(ele).hasClass('selText')){
    return;
  }
  $(ele).addClass('selText').siblings().removeClass('selText');
  selectPattern();
}
// 打开申请列表
function applyList() {
  var url = sourcePrefixUrl + '/template-lib/layouts/blApply/applies/applyList.html?reqDeptName=' + (applyInfo.reqDeptName || '') + '&reqDeptCode=' + (applyInfo.reqDept || '') + '&reqPhysician=' + (applyInfo.reqPhysicianName || '') + '&optId=' + (applyInfo.optId || '') + '&optName=' + (applyInfo.optName || '');
  window.open(url, 'apply-win')
}

// 计费后处理配置中不可编辑的项
function setDisabledByConfig(applyStatusRes) {
  if(!applyStatusRes || typeof applyStatusRes !== 'object') {
    return;
  }
  // 检查项目不可编辑
  if(applyStatusRes.cantEditItem === '1') {
    $('[onclick="addMarkInfoHandler(\'examItem\')"]').hide();
    $(".eItemEditPart [onclick]").hide();
  }
}

// 关闭页面
function closePage() {
  var flag = true;
  if(rtStructure.enterOptions.type !== 'view') {
    flag = confirm('数据尚未保存，确认关闭？');
  }
  if(flag) {
    if(sysCode==='GM_SAMPLE') {
      sendMessageHandle();
    }else {
      window.top.close();
    }
  }
}

// 病理申请单在保存、提交、关闭操作后发送消息
function sendMessageHandle() {
  // console.log('saveResultParams-->',saveResultParams);
  if(sysCode==='GM_SAMPLE') {
    window.top.postMessage({
      message: 'PATHOLOGY_APPLY_UPDATE',
      result: { ...saveResultParams }
    }, '*');
  }
}

// 病理申请单
function getCurChargeFlag() {
  // 当前申请单已由其他系统修改为【已收费】，是否确认更新为【未收费】
  // 目前仅针对收费标识进行处理
  // 保存申请单前，先调取接口
  // 获取当前申请单的收费标识
  // 如获取到的标识不为空且与当前填写值不一致，进行二次确认是否需要修改
  let params = { applyNo: urlParams.applyNo }
  fetchAjax({
    url: api.getCurChargeFlag,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      curChargeFlagObj = res;
    },
    errorFn: function(e) {
      console.log(e);
    }
  })
}

// 二次确认弹窗按钮事件
function confirmToSave() {
  secondConfirmFlag = true;
  saveApplyResult($('#saveBtn'), true);
}

// 判断时间差
function getTimeDifference(date1, date2) {
  let newDate1 = new Date(date1);
  let newDate2 = new Date(date2);
  var diffInMilliseconds = Math.abs(newDate1 - newDate2); // 获取两个日期相差的毫秒数
  if (diffInMilliseconds > 1800000) { // 如果小于等于30分钟（60 * 60 * 1000）则返回true
    return true;
  } else {
    return false;
  }
}