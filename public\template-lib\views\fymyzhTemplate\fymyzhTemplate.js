// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
  'myzh-rt-19': { label: '职业', wt: '', hisKey: 'degree'},
  'myzh-rt-23': { label: '身份证号码', wt: '', hisKey: 'identity'},
}
var saveApplyParams = {
  'degree': { label: '职业', id: 'myzh-rt-19'},
  'phoneNumber': { label: '联系电话', id: 'myzh-rt-21'},
  'applyItemList': { label: '检查项目', id: ''},
  // 'gmSampleList': { label: '标本信息', id: ''},
}

function initHtmlScript(ele) {
  // 编辑
  if($(ele).attr('isview') !== 'true') {
      initDatePicker();
      if($(".sampleViewPart").length) {
          specialBlockViewHandler('sample', false)
      }
      if($(".eItemViewPart").length) {
          specialBlockViewHandler('examItem', false)
      }
      setDisabledByConfig && setDisabledByConfig(applyStatusRes);
  } else { // 预览
      if($(".sampleViewPart").length) {
          specialBlockViewHandler('sample', true)
      }
      if($(".eItemViewPart").length) {
          specialBlockViewHandler('examItem', true)
      }
  }
  getExamSubClassList(applyInfo);  //检查子类
  getGmSamplePartByLevelList();   //检查部位
  getSampleList(applyInfo);  //标本
  setValByHis(hisAndFormMap)
}

// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('laydate', function () {
      var laydate = layui.laydate;
      //执行一个laydate实例
      laydate.render({
          elem: '.date-wrap', //指定元素
          type: 'date',
          trigger: 'click',
          format: 'yyyy-MM-dd'
      });
  });
}

$(function () {
  window.initHtmlScript = initHtmlScript;
})