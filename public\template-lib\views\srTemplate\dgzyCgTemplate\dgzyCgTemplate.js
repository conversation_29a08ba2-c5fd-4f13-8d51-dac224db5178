$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var cursorInfo = {};  //光标位置信息
var codeMap = {
  '312': '肉眼观察',
  '201': '镜下观察',
  '211': '特殊检查',
  '202': '病理诊断',
}; // 添加双击事件节点
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#dgzyCg1 .dgzyCg-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      // 有住院号显示住院号
      if(publicInfo.inpatientNo) {
        curElem.find('.outpatientNo-wrap').hide();
        curElem.find('.inpatientNo-wrap').show();
      } else {
        curElem.find('.outpatientNo-wrap').show();
        curElem.find('.inpatientNo-wrap').hide();
      }
      initViewCon();
    } else {
      pageInit();
    }
  }
}
function pageInit() {
  changeTextSize();
  getRelativeCon();
  for(let code in codeMap) {
    $('[code="'+code+'"]').on('dblclick',function() {
      dblHandler(code);
    })
    $('[code="'+code+'"]').on('blur',function() {
      blurTextAreaHandler(this, $(this).attr('data-key'));
    })
  }

  // 不包含审核、保存按钮时禁用输入框
  // var isDisabled = publicInfo.buttonList ? 
  //   !publicInfo.buttonList.includes('SAVE') && !publicInfo.buttonList.includes('AFFIRM') : false;
  
  // 高度随内容增高
  curElem.on('input', 'textarea.rt-sr-w', function() {
    autoAdjustTextareaHeight(this);
  })
  
  // 初始化高度
  curElem.find('textarea.rt-sr-w').each(function() {
    this.handlerTextareaHeight = handlerTextareaHeight;  //挂个内置方法供外部调用，插入常用词处理
    autoAdjustTextareaHeight(this, true);
    // if(isDisabled) {
    //   $(this).attr('disabled', 'disabled');
    //   $(this).addClass('dis-remove-disabled');  //不可移除禁用
    // }
  })
}

function handlerTextareaHeight(dom) {
  autoAdjustTextareaHeight(dom);
}

// 字号切换
function changeTextSize() {
  var localSize = getOrSetSizeType('get');
  setSizeDom(localSize);
  $('body').on('click', '.text-size img', function () {
    if ($(this).hasClass('on')) {
      return;
    }
    var size = $(this).closest('.img-wrap').attr('data-size');
    setSizeDom(size);
    getOrSetSizeType('set', size);
  })
  $(window.top.document.body).on('click', '.text-size img', function () {
    if ($(this).hasClass('on')) {
      return;
    }
    var size = $(this).closest('.img-wrap').attr('data-size');
    setSizeDom(size);
    getOrSetSizeType('set', size);
  })
}

// 设置字体大小元素展示
function setSizeDom(size) {
  // 区分直接插入到顶级窗口的操作
  let docList = ['body', 'window.top'];
  for(let item of docList) {
    let winDoc = item === 'window.top' ? $(window.top.document.body) : $(item);
    winDoc.find('.editor-wrap .on').hide();
    winDoc.find('.editor-wrap .off').show();
    winDoc.find('.editor-wrap .img-wrap[data-size="' + size + '"] .on').show();
    winDoc.find('.editor-wrap .img-wrap[data-size="' + size + '"] .off').hide();
    winDoc.find('.editor-area').removeClass('default large larger');
    winDoc.find('.editor-area').addClass(size);
  }
}

// 获取、设置缓存字体大小数据
function getOrSetSizeType(type, size) {
  var userId = publicInfo && publicInfo.userInfo ? publicInfo.userInfo.staffNo : publicInfo.optId;
  var sizeTypeList = getLocalStorage('rt_size_type') || {};
  if(type === 'get') {
    var sizeType = sizeTypeList[userId] || 'default';
    return sizeType;
  }
  if(type === 'set') {
    sizeTypeList[userId] = size;
    setLocalStorage('rt_size_type', sizeTypeList);
  }
}

// 提取相关内容
function getRelativeCon() {
  curElem.find('[data-get]').click(function () {
    var [type, valKey] = $(this).attr('data-get').split(',');
    getConFromApi(type, valKey);
  })
}

// 获取相关接口内容
function getConFromApi(type, valKey) {
  if (!publicInfo.examNo && !publicInfo.busId) {
    return;
  }
  var apiMap = {
    'sample': api.gmSampleForRpt,   //提标本
    'doctorAdvice': api.getGmOrderForRpt,  //提医嘱
    'sampleSeen': api.getSampleSeen,  //提肉眼观察
    'frozen': api.getFrozenForRpt,  //提冰冻诊断
    'examResult': api.getExamInspectInfo,  //提检验结果
  }
  var originVal = $('.dgzyCg-edit [data-key="' + valKey + '"]').val();
  var wrapText = '';
  if (originVal && originVal[originVal.length - 1] !== '\n') {
    wrapText = '\n';
  }
  var params = {
    examNo: publicInfo.examNo || publicInfo.busId
  }
  if (type === 'frozen') {
    params.rptType = '1';
  } else if (type === 'sample') {
    var targetCode = $('.dgzyCg-edit [data-key="' + valKey + '"]').attr('code');
    if(targetCode) {
      params.extractBy = targetCode;
    }
  }
  fetchAjax({
    url: apiMap[type],
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status == '0') {
        if (res.result) {
          const handler = function(resText) {
            if(cursorInfo && cursorInfo.dataKey === valKey) {
              insertTextAtCaret(resText, cursorInfo)
            } else {
              cursorInfo = {};
              let text = originVal + wrapText + resText;
              curElem.find('.dgzyCg-edit [data-key="' + valKey + '"]').val(text);
              curElem.find('.dgzyCg-edit [data-key="' + valKey + '"]').focus();
            }
          }
          let resText = type !== 'sampleSeen' ? res.result : (res.result.examRecord || '');
          if(type === 'examResult') {
            createExamResultTable(res.result ? res.result.higherItemList : [], handler);
          } else {
            handler(resText);
            // if(cursorInfo && cursorInfo.dataKey === valKey) {
            //   insertTextAtCaret(resText, cursorInfo)
            // } else {
            //   cursorInfo = {};
            //   let text = originVal + wrapText + resText;
            //   curElem.find('.dgzyCg-edit [data-key="' + valKey + '"]').val(text);
            // }
          }
        }
      }
    },
  })
}

// 检验结果表格
function createExamResultTable(data, cb) {
  var examResultData = data;
  if(!data || !data.length) {
    return;
  }
  var dialogContent = '';
  dialogContent += `<div style="padding:4px 0;height:100%;position: relative;">`;
  // 检验列表
  dialogContent += `<div style="font-size: 18px;font-weight: bold;color:#000;margin:12px 0;">检验列表：</div>`;
  dialogContent += `<div style="max-height:200px;overflow:auto;">`;
  dialogContent += `<table border="1" style="width:100%;table-layout: fixed;text-align: center;background:#fff;border-color:#C0C4CC">`;
  dialogContent += `<thead>`;
    dialogContent += `<tr style="font-size:16px;font-weight:bold;background:#F5F7FA;height:30px">`;
    dialogContent += `<td>检验名称</td>`;
    dialogContent += `<td>标本类型</td>`;
    dialogContent += `<td>检验日期</td>`;
    dialogContent += `<td>报告时间</td>`;
    dialogContent += `</tr>`;
  dialogContent += `</thead>`;
  dialogContent += `<tbody>`;
  for(let i = 0; i < data.length; i++) {
    let item = data[i];
    dialogContent += `<tr class="tr-row" style="font-size: 14px;cursor: pointer;">`;
    dialogContent += `<td style="padding:3px 0;">${item.higherItemName || ''}</td>`;
    dialogContent += `<td style="padding:3px 0;">${item.sampleType || ''}</td>`;
    dialogContent += `<td style="padding:3px 0;">${item.checkDateTime ? item.checkDateTime.slice(0, 16) : ''}</td>`;
    dialogContent += `<td style="padding:3px 0;">${item.reportDateTime ? item.reportDateTime.slice(0, 16) : ''}</td>`;
    dialogContent += `</tr>`;
  }
  dialogContent += `</tbody>`;
  dialogContent += `</table>`;
  dialogContent += `</div>`;

  // 检验项目详情
  dialogContent += `<div style="font-size: 18px;font-weight: bold;color:#000;margin:12px 0;">检验项目详情：</div>`;
  dialogContent += `<div style="max-height:300px;overflow:auto;">`;
  dialogContent += `<table border="1" style="width:100%;table-layout: fixed;text-align: center;background:#fff;">`;
  dialogContent += `<thead>`;
    dialogContent += `<tr style="font-size:16px;font-weight:bold;background:#F5F7FA">`;
    dialogContent += `<td width="50px" style="padding:4px 0;"><input type="checkbox" name="checkAll" id="checkAll" value="checkAll"/></td>`;
    dialogContent += `<td>项目名称</td>`;
    dialogContent += `<td width="80">结果</td>`;
    dialogContent += `<td width="60">单位</td>`;
    dialogContent += `<td width="100">参考值</td>`;
    dialogContent += `<td width="80">异常标记</td>`;
    dialogContent += `<td width="100">检验方法</td>`;
    dialogContent += `</tr>`;
  dialogContent += `</thead>`;
  dialogContent += `<tbody id="examitem-detail">`;
  dialogContent += `</tbody>`;
  dialogContent += `</table>`;
  dialogContent += `</div>`;
  dialogContent += '<div class="error-tip" style="position:absolute;right:160px;color:#f56c6c;bottom:-45px;display:none">请先选择检验项目</div>';
  dialogContent += '<div class="footer" style="text-align: right;padding-top: 12px;position:absolute;bottom:-55px;right:0;">';
  dialogContent += '<button class="close-btn" style="margin-left:8px;background:#fff;padding:9px 20px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;font-size: 16px;">取消</button>';
  dialogContent += '<button id="confirmBtn" style="margin-left:8px;background:#1885F2;padding:9px 20px;border-radius:3px;color:#FFF;border:1px solid #1885F2;cursor:pointer;font-size: 16px;">确定</button>';
  dialogContent += '</div>';

  var dbWindowStyle = `.rt-dialog-con {
    width: 800px!important;
    height: 80%;
    background: #F0F2F5!important;
    margin: 25px auto 0 auto;
  }
  .rt-dialog-con table{
    border-collapse: collapse;
    border-spacing: 0;
  }
  .rt-dialog-con table, .rt-dialog-con td, .rt-dialog-con th{
    border: 1px solid #C0C4CC;
  }
  .rt-dialog-header {
    padding: 16px 20px 0!important;
  }
  .rt-dialog-title{
    font-size: 18px!important;
    font-weight: bold;
  }
  .rt-dialog-content {
    height: 85%!important;
    padding: 16px 20px!important;
  }
  `;
  drawDialog({
    title: '提取检验结果',
    content: dialogContent,
    modal: false,
    wholeStyle: dbWindowStyle,
    appendTop: true,
    closeFun: function () {
      // $(window.frames.frameElement).removeClass('fixed-iframe');
    }
  })
  // $(window.frames.frameElement).addClass('fixed-iframe');
  // 点击行
  var examItemIndex = -1;
  rtDialog.find('.tr-row').click(function() {
    $(this).siblings().css('background', '#FFF');
    $(this).css('background', '#A0CFFF');
    var index = $(this).index();
    examItemIndex = index;
    setDetailData(examResultData[index].itemList);
  })
  rtDialog.find('.tr-row:eq(0)').click();
  rtDialog.find('#checkAll').click(function() {
    let isChecked = $(this).prop('checked');
    rtDialog.find('input[name="oneRes"]').prop('checked', isChecked);
  })
  rtDialog.on('change', 'input[name="oneRes"]', function() {
    let checkedLength = rtDialog.find('input[name="oneRes"]:checked').length;
    let dataLength = rtDialog.find('input[name="oneRes"]').length;
    rtDialog.find('#checkAll').prop('checked', checkedLength === dataLength);
  })
  // 取消
  rtDialog.on('click', '.close-btn', function() {
    removeRtDialog(true);
  })

  // 确定操作
  rtDialog.find('#confirmBtn').click(function() {
    let checked = rtDialog.find('input[name="oneRes"]:checked');
    rtDialog.find('.error-tip').hide();
    if(!checked.length) {
      rtDialog.find('.error-tip').show();
      return;
    }
    let res = [];
    let examItem = data[examItemIndex];
    let examItemDetailList = examItem.itemList || [];
    if(examItem.reportDateTime) {
      res.push(examItem.reportDateTime.slice(0, 16));
    }
    if(examItem.higherItemName) {
      res.push(examItem.higherItemName)
    }
    checked.each(function() {
      let index = $(this).val();
      let one = examItemDetailList[index];
      one.itemName && res.push(one.itemName);
      one.checkResult && res.push(one.checkResult);
      one.unit && res.push(one.unit);
    })
    if(res.length) {
      cb(res.join(' '));
    }
    removeRtDialog(true);
  })
}

// 获取检验详情
function setDetailData(data) {
  if(!data || !data.length) {
    rtDialog.find('#examitem-detail').html('<tr><td colspan="7" style="padding:3px 0;">暂无数据</td></tr>');
    return;
  }
  var dialogContent = '';
  for(let i = 0; i < data.length; i++) {
    let item = data[i];
    dialogContent += `<tr style="font-size: 14px;padding:3px 0">`;
    dialogContent += `<td style="padding:4px 0;"><input type="checkbox" name="oneRes" value="${i}"/></td>`;
    dialogContent += `<td>${item.itemName || ''}</td>`;
    dialogContent += `<td>${item.checkResult || ''}</td>`;
    dialogContent += `<td>${item.unit || ''}</td>`;
    dialogContent += `<td>${item.referenceValue || ''}</td>`;
    dialogContent += `<td>${item.normalFlag || ''}</td>`;
    dialogContent += `<td>${item.checkWay || ''}</td>`;
    dialogContent += `</tr>`;
  }
  rtDialog.find('#examitem-detail').html(dialogContent);
}

// 插入节点的光标位置
function insertTextAtCaret(value, srCursorInfo) {
  let {textarea, cursorPosition, selectionEnd} = srCursorInfo;
  let textBefore = textarea.value.substring(0, cursorPosition);
  let textAfter = textarea.value.substring(selectionEnd, textarea.value.length);
  textarea.value = textBefore + value + textAfter; // 插入文本
  // 重新定位光标位置
  textarea.selectionStart = cursorPosition + value.length;
  textarea.selectionEnd = cursorPosition + value.length;
  textarea.focus();
}

// 失去焦点获取光标位置
function blurTextAreaHandler(vm, dataKey) {
  let selection = getSelection();
  cursorInfo = {
    selection,
    cursorPosition: vm.selectionStart,  //记录最后光标对象
    selectionEnd: vm.selectionEnd,  //记录最后光标对象
    textarea: vm,
    dataKey
  }
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.dgzyCg-view [data-key]').each(function () {
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    $(this).closest('.desc-con').hide();
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      if(publicInfo[key] || idAnVal) {
        var value = publicInfo[key] || idAnVal;
        if(key === 'reqHospital') {
          value = value === '东莞市中医院' ? '总院' : '分院';
        }
        if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
          value = value + ' ' + publicInfo['affirmTime'];
        }
        if(value) {
          $(this).html(value);
          $(this).closest('.desc-con').show();
        }
        break;
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  curElem.find('.dgzyCg-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    var len = '';
    // if(rptImageList.length > 2) {
    //   len = rptImageList.length >= 4 ? 4 : rptImageList.length;
    // }
    rptImageList = rptImageList.slice(0, 4);
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="item-img">
          <img src="${image.src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      // curElem.find('.dgzyCg-view .rpt-img-ls').attr('data-len', len);
      curElem.find('.dgzyCg-view .rpt-img-ls').html(imgHtml);
      curElem.find('.dgzyCg-view .rpt-img-ls').css('display', 'flex');
    }
  }
}

// 双击方法事件
function dblHandler(code) {
  var dialogContent = '';
  var title = codeMap[code];
  var localSize = getOrSetSizeType('get') || 'default';
  var eleVal = $('[code="'+code+'"]').val();
  dialogContent += `<div style="padding:4px 0;background:#FFF;height:100%;position: relative;padding-top:30px;border:1px solid #C0C4CC;border-radius:3px;">`;
  dialogContent += `<div class="diag-text-size editor-wrap" style="height:100%">`;
  dialogContent += `<div class="text-size" style="height:25px;">
      <div class="img-wrap" data-size="default">
        <img src="/sreport/template-lib/assets/images/pacs/text-default-off.png" alt="" class="off">
        <img src="/sreport/template-lib/assets/images/pacs/text-default-on.png" alt="" class="on">
      </div>
      <div class="img-wrap" data-size="large">
        <img src="/sreport/template-lib/assets/images/pacs/text-large-off.png" alt="" class="off">
        <img src="/sreport/template-lib/assets/images/pacs/text-large-on.png" alt="" class="on">
      </div>
      <div class="img-wrap" data-size="larger">
        <img src="/sreport/template-lib/assets/images/pacs/text-larger-off.png" alt="" class="off">
        <img src="/sreport/template-lib/assets/images/pacs/text-larger-on.png" alt="" class="on">
      </div>
    </div>`;
    dialogContent += `<div class="editor-area ${localSize}" style="height: calc(100% - 25px);"><textarea code="${code}" id="edit-inp" placeholder="请输入内容" style="border:none;width:100%;height:100%;resize:none;padding:4px 10px;overflow:auto;">${eleVal}</textarea></div>`;
    dialogContent += `</div>`;
  dialogContent += '<div class="footer" style="text-align: right;padding-top: 12px;position:absolute;bottom:-55px;right:0;">';
  // dialogContent += '<button onclick="save(\''+code+'\')" style="background:#1885F2;padding:9px 20px;border-radius:3px;color:#fff;border:none;cursor:pointer;font-size: 16px;">保存</button>';
  dialogContent += '<button class="close-btn" style="margin-left:8px;background:#fff;padding:9px 20px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;font-size: 16px;">关闭</button>';
  dialogContent += '</div>';
  var dbWindowStyle = `.rt-dialog-con {
    width: 800px!important;
    height: 80%;
    background: #F0F2F5!important;
    margin: 25px auto 0 auto;
  }
  .rt-dialog-con textarea{
   outline:none;
  }
  .rt-dialog-header {
    padding: 16px 20px 0!important;
  }
  .rt-dialog-title{
    font-size: 18px!important;
    font-weight: bold;
  }
  .rt-dialog-content {
    height: 85%!important;
    padding: 16px 20px!important;
  }
  .rt-dialog-content .diag-text-size {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    border: none;
    border-radius: none;
  }
  .rt-dialog-con .editor-area.default textarea{
    font-size: 16px;
  }
  .rt-dialog-con .editor-area.large textarea{
    font-size: 18px;
  }
  .rt-dialog-con .editor-area.larger textarea{
    font-size: 20px;
  }
  .rt-dialog-con .diag-text-size .text-size {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-bottom: 1px solid #C0C4CC;
  }
  .rt-dialog-con .diag-text-size .text-size img {
    width: 24px;
    margin-right: 8px;
    vertical-align: middle;
  }
  .rt-dialog-con .diag-text-size .text-size img:hover {
    cursor: pointer;
    opacity: 0.8;
  }
  .rt-dialog-con .diag-text-size .text-size .on {
    display: none;
  }`;
  drawDialog({
    title: title,
    content: dialogContent,
    modal: false,
    wholeStyle: dbWindowStyle,
    appendTop: true,
    canUseOtherContent: true,  //允许操作弹框外的内容
    closeFun: function () {
      // $(window.frames.frameElement).removeClass('fixed-iframe');
    }
  })
  rtDialog.find('.diag-text-size [data-size="'+localSize+'"] .off').hide();
  rtDialog.find('.diag-text-size [data-size="'+localSize+'"] .on').show();
  var textarea = rtDialog.find('[code="'+code+'"]')[0];
  textarea.selectionStart = textarea.value.length;
  textarea.selectionEnd = textarea.value.length;
  textarea.focus();
  rtDialog.on('click', '.close-btn', function() {
    removeRtDialog(true);
  })
  rtDialog.find('#edit-inp').on('blur', function() {
    var editContent = $(this).val();
    var code = $(this).attr('code');
    $('[code="'+code+'"]').val(editContent);
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = curElem.find('.dgzyCg-edit [data-key="description"]').val() || '';
  rtStructure.impression = curElem.find('.dgzyCg-edit [data-key="impression"]').val() || '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmDate: '',  //审核日期
    examParam: curElem.find('.dgzyCg-edit [data-key="examParam"]').val() || '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: curElem.find('.dgzyCg-edit [data-key="sampleSeen"]').val() || '',  //肉眼观察
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}