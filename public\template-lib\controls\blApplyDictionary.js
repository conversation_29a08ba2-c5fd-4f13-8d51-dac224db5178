var examSubClassList = [];  //检查子类   
var examItemList = [];  //检查项目  
var organList = [];  //检查部位   
var organListLevel = [];  //检查部位分级平铺的数据   
var organTree = [];  //检查部位分级树形数据  
var tree = null; 
var sampleList = [];  //标本列表  
var addSampleList = [];  //已添加的标本数据 
var addExamItemList = [];  //已添加的项目数据 
var organObj = {};  //勾选的部位-标本
var curActId = '';
var sampleByTable = $('.t-pg').attr('data-sampleByTable') || '';
setTimeout(() => {
  sampleByTable = $('.t-pg').attr('data-sampleByTable') || '';
}, 500)
if(window.isRptStatistics) {
  var rtStructure = null;
  var applyInfo = {};
}
var pv = getParamByName('pv') || null;

// 获取检查子类列表
function getExamSubClassList(applyInfo) {
  if(pv === '1') {
    return;
  }
  var params = {
    examClassName: applyInfo.examClass || '',
  }
  if(!params.examClassName) {
    return;
  }
  if(applyInfo.examSubClass) {
    examSubClassList = [{examSubclassName:applyInfo.examSubClass}];
    getExamItemList(applyInfo.examSubClass);
    return;
  }
  fetchAjax({
    url: api.getExamSubClass,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        examSubClassList = data;
        if(applyInfo.examSubClass) {
          getExamItemList(applyInfo.examSubClass);
        } else {
          getExamItemList();
        }
      }
    },
  })
}
// 获取检查项目列表
function getExamItemList(subClass, setHtml) {
  if(pv === '1') {
    return;
  }
  var params = {
    examClass: applyInfo.examClass || '',
    examSubClass: subClass || ''
  }
  examItemList = [];
  if(!params.examClass) {
    return;
  }
  rtDialog && rtDialog.find("#ft-inp").val('');
  fetchAjax({
    url: api.getExamItemList,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        examItemList = data;
      }
    },
    completeFn: function() {
      if(setHtml) {
        var rHtml = createHtmlToRight(examItemList, 'examItem')
        rtDialog.find('.r-wrap').html(rHtml);
      }
    }
  })
}

// 获取检查部位列表
function getExamOrganList() {
  if(pv === '1') {
    return;
  }
  var params = {
    examClassName: applyInfo.examClass || '',
    examSubClass: applyInfo.examSubClass || ''
  }
  if(!params.examClassName) {
    return;
  }
  fetchAjax({
    url: api.getGmSamplePart,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        organList = data;
      }
    },
  })
}

// 分级获取标本部位列表
function getGmSamplePartByLevelList() {
  if(pv === '1') {
    return;
  }
  organListLevel = []
  var params = {
    examClassName: applyInfo.examClass || '',
    examSubClass: applyInfo.examSubClass || '',
  }
  if(!params.examClassName) {
    return;
  }
  fetchAjax({
    url: api.getGmSamplePartByLevel,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        organListLevel = data;
      }
    },
  })
}

// 获取标本数据列表
function getSampleList(applyInfo) {
  if(pv === '1') {
    return;
  }
  var params = {
    examClassName: applyInfo.examClass || '',
    examSubClass: applyInfo.examSubClass || ''
  }
  if(!params.examClassName) {
    return;
  }
  fetchAjax({
    url: api.getGmSimpleName,
    data: JSON.stringify(params),
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        sampleList = data;
      }
    },
  })
}
// 初始化树形组件
function initTreePicker() {
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('tree', function () {
    tree = layui.tree;
    organTree = handlerTreeData(organListLevel);
    tree.render({
      elem: '#treePart', //标本部位
      data: organTree,
      showCheckbox: true,
      showLine: false,
      id: 'treePart',
      oncheck: function(obj){  //复选框点击回调
        var nodeId = obj.data.id;
        $(".layui-tree-entry").removeClass('active');
        $(obj.elem).find('.layui-tree-entry').eq(0).addClass('active');
        curActId = nodeId;
        if(obj.checked) {
          if(!organObj[nodeId]) {
            organObj[nodeId] = [];
            var html = createHtmlToRight(sampleList, 'sample');
            rtDialog.find('.r-wrap').html(html);
          }
        } else {
          if(organObj[nodeId]) {
            delete organObj[nodeId];
            var html = createHtmlToRight(sampleList, 'sample');
            rtDialog.find('.r-wrap').html(html);
          }
        }
      },
    });
    $(".layui-tree-entry").click(function() {
      var curNode = $(this).parent('.layui-tree-set');
      var nodeId = curNode.attr('data-id');
      var checked = $('input[value="'+nodeId+'"]').prop('checked');
      curActId = nodeId;
      $(".layui-tree-entry").removeClass('active');
      $(this).addClass('active');
      if(checked) {
        if(!organObj[nodeId]) {
          organObj[nodeId] = [];
        }
      }else {
        if(organObj[nodeId]) {
          delete organObj[nodeId];
        }
      }
      var html = createHtmlToRight(sampleList, 'sample');
      rtDialog.find('.r-wrap').html(html);
    })
  });
}

// 遍历出树级节点的连接名
function getTreeNames(tree, obj, res) {
  for (var i = 0; i < tree.length; i++) {
    var item = tree[i]
    if (obj[item.upperPart]) {
      item.partName = obj[item.upperPart] + '/' + item.partName
    }
    obj[item.partCode] = item.partName
    if (item.children && item.children.length) {
      res[item.partCode] = item.partName;
      getTreeNames(item.children, obj, res)
    } else {
      res[item.partCode] = item.partName;
    }
  }
}

function handlerTreeData(data) {
  var res = JSON.parse(JSON.stringify(data || []));
  var parentList = [];
  var childrenList = [];
  if(res && res.length) {
    for(var i = 0; i < res.length; i++) {
      var item = res[i];
      item.title = item.partName;
      item.id = item.partCode;
      if(!item.upperPart) {
        parentList.push(item);
      } else {
        childrenList.push(item);
      }
    }
  }

  if(childrenList.length === 0) {
    return parentList;
  }
  var handler = function(parents, children) {
    parents.forEach(function(parent){
      children.forEach(function(current, index) {
        if (current.upperPart === parent.partCode) {
          var temp = JSON.parse(JSON.stringify(children));
          temp.splice(index, 1);
          handler([current], temp);
          (parent.children) ? parent.children.push(current) : parent.children = [current];
        }
      })
    })
  }
  handler(parentList, childrenList);
  return parentList;
}


/**
 * 添加标本/检查项目
*/
function addExamItemOrOrgan(type, successFn) {
  var lData = type === 'examItem' ? examSubClassList : organList;
  var rData = type === 'examItem' ? examItemList : sampleList;
  if(type === 'sample') {
    organObj = {};
    curActId = '';
  }
  var html = initExamContent(type, lData, rData, successFn)
  drawDialog({
    title: type === 'examItem' ? '添加检查项目' : '添加标本',
    modal: true,
    content: html,
    style: {
      'width': '600px',
    }
  });
  // 标本部位通过树形展示
  if(type === 'sample') {
    initTreePicker();
  }
  $("#ft-inp").on('input', function(e) {
    var inpVal = $(this).val();
    var temp = JSON.parse(JSON.stringify(type === 'examItem' ? examItemList : sampleList));
    var arr = [];
    for(var i = 0; i < temp.length; i++) {
      var name = type === 'examItem' ? temp[i].itemName : temp[i];
      var inputCode = type === 'examItem' ? temp[i].inputCode : '';
      if(name.indexOf(inpVal) > -1 || (inputCode && inputCode.indexOf(inpVal) > -1)) {
        arr.push(temp[i]);
      }
    }
    var html = createHtmlToRight(arr, type, inpVal ? true : false);
    rtDialog.find('.r-wrap').html(html);
  })
}

// 标本/检查项目弹框html
function initExamContent(type, lData, rData, successFn) {
  var html = '<div class="item-table" style="border:1px solid #C8D7E6;width:568px;">';
  html += '<style>:checked + span {color:#1885F2} .selText{color:#1885F2}</style>';
  html += '<div class="t-header" style="background:#EBEEF5;height:36px;line-height:36px;border-bottom:1px solid #C8D7E6;">';
  html += '<div style="padding:0 12px;width:224px;border-right:1px solid #C8D7E6;display: inline-block;">'+(type === 'examItem' ? '检查子类' : '部位')+'</div>';
  html += '<div style="padding:0 12px;width:340px;display: inline-block;vertical-align: middle;">';
  html += '<span style="float: left;">'+(type === 'examItem' ? '项目名称' : '标本名称')+'</span>';
  html += '<div style="float: right;width: 255px;background: #FFF;padding:0 10px;margin-top:2px;border: 1px solid #DCDFE6;border-radius: 3px;height:30px;">';
  html += '<input type="text" id="ft-inp" placeholder="输入'+(type === 'examItem' ? '项目名称' : '标本名称')+'搜索" style="width: 100%;height:28px;border: none;outline: none;vertical-align:top;">';
  html += '</div>';
  html += '</div>';
  html += '</div>';
  html += '<div class="t-body" style="background: #F5F7FA;overflow: hidden;">';
  html += '<div class="l-wrap" style="padding:0;width:224px;height:260px;overflow:auto;border-right:1px solid #C8D7E6;float: left;" id="lfData">';
  if(type === 'examItem' && !applyInfo.examSubClass) {
    html += '<div style="padding: 5px 10px;cursor: pointer;" class="'+(applyInfo.examSubClass?'':'selText')+'" onclick="filterByChild(this,\''+type+'\')" class="l-all">全部</div>';
  }
  if(type === 'examItem') {
    for(var i = 0; i < lData.length; i++) {
      var className = 'class=""';
      if(applyInfo.examSubClass === lData[i].examSubclassName) {
        className = 'class="selText"';
      }
      html += '<div style="padding: 5px 10px;cursor: pointer;" '+className+' onclick="filterByChild(this,\''+type+'\')">'+(type === 'examItem' ? lData[i].examSubclassName : lData[i].partName)+'</div>';
    }
  } else {
    // 树形
    html += '<div id="treePart"></div>';
  }
  html += '</div>';
  html += '<div class="r-wrap" style="padding:0 12px;width:340px;height:260px;overflow:auto;float: left;color:#000">';
  html += createHtmlToRight(rData, type);
  html += '</div>';
  html += '</div>';
  html += '</div>';
  if(type === 'sample') {
    html += '<div style="padding: 4px 12px">';
    html += '<div style="display:inline-block;width:224px">';
    html += '部位：';
    html += '<input style="width:165px;height:28px" class="rt-sr-t" name="inp-bw">';
    html += '</div>'
    html += '<div style="display:inline-block;width:320px;">';
    html += '标本名称：';
    html += '<input style="width:250px;height:28px" class="rt-sr-t" name="inp-bbmc">';
    html += '</div>'
    html += '</div>'
  }
  html += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  html += '<button onclick="addItemHandler(this, '+successFn+',\''+type+'\')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">添加</button>';
  html += '<button onclick="clearItemHandler(this)" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">清空</button>';
  html += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消</button>';
  html += '</div>';
  return html;
}

// 渲染右边的数据
function createHtmlToRight(rData, type, isFilter) {
  var html = '';
  if(rData && rData.length) {
    var selectArr = organObj[curActId] ? organObj[curActId] : [];
    var selectAll = '';
    var disable = '';
    if(type === 'sample') {
      selectAll = selectArr.length === rData.length ? 'checked' : '';
      disable = organObj[curActId] ? '' : ' disabled';
    }
    if(!isFilter) {
      html += '<div style="padding: 5px 0;">';
      html += '<span class="ck-w"><label>';
      html += '<input type="checkbox" style="min-height: 0;" class="sel-all" onclick="selectAll(this,\''+type+'\')" '+selectAll + disable+'>';
      html += '<span class="t-ck"></span>';
      html += '<span class="ck-txt" style="margin-left:5px;">全部</span></label>';
      html += '</span>'
      html += '</div>';
    }
    for(var i = 0; i < rData.length; i++) {
      var selectSelf = '';
      if(type === 'sample') {
        selectSelf = selectArr.indexOf(rData[i]) > -1 ? 'checked' : ''
      }
      html += '<div style="padding: 5px 0;position:relative;">';
      html += '<span class="ck-w"><label>';
      html += '<input '+selectSelf + disable +' onclick="selectSelf(this,\''+type+'\')" type="checkbox" style="min-height: 0;" value="'+(type === 'examItem' ? rData[i].itemName : rData[i])+'">';
      html += '<span class="t-ck"></span>';
      html += '<span class="ck-txt" style="margin-left:5px;line-height:22px;">'+(type === 'examItem' ? rData[i].itemName : rData[i])+'</span></label>';
      html += '</span>'
      html += '</div>';
    }
  } else {
    html = '<div style="color:#666;padding-top:5px">暂无数据</div>'
  }
  return html;
}


// 添加
function addItemHandler(ele, successFn, type) {
  var checked = rtDialog.find('[type="checkbox"]:not(.sel-all):checked');
  var result = [];
  var rData = type === 'examItem' ? examItemList : sampleList;
  var lItemVal = rtDialog.find('.selText').text();
  if(!rData || !rData.length) {
    $wfMessage({
      content: '暂无数据',
      type: 'err'
    })
    return;
  }
  if(type === 'examItem') {
    checked.each(function(i, dom) {
      var obj = {};
      if(type === 'examItem') {
        for(var i = 0; i < rData.length; i++) {
          if(rData[i].itemName === dom.value ) {
            obj = rData[i];
            break;
          }
        }
      }
      obj['parentName'] = lItemVal;
      obj['targetName'] = dom.value;
      result.push(obj);
    })
  } else {
    var treeNameObj = {};
    getTreeNames(organTree, {}, treeNameObj);
    for(var key in organObj) {
      var organ = organObj[key];
      if(organ && organ.length) {
        organ.forEach(function(item) {
          result.push({
            parentName: treeNameObj[key],
            parentCode: key,
            targetName: item
          });
        })
      }
    }
    if(rtDialog.find('[name="inp-bbmc"]').val().trim()) {
      if(!rtDialog.find('[name="inp-bw"]').val().trim()) {
        $wfMessage({
          content: '请填写标本部位',
          type: 'err'
        })
        return;
      }
      result.push({
        parentName: rtDialog.find('[name="inp-bw"]').val().trim(),
        parentCode: '',
        targetName: rtDialog.find('[name="inp-bbmc"]').val().trim()
      });
    }
  }
  if(result.length === 0) {
    $wfMessage({
      content: '请先勾选项再点添加',
      type: 'err'
    })
    return;
  }
  successFn(result);
  organObj = {};
  curActId = '';
  removeRtDialog();
}

// 全选标本/项目
function selectAll(el, type) {
  var target = $(el);
  target.closest('.r-wrap').find('input[type="checkbox"]').prop('checked', target.is(":checked"));
  if(type === 'sample') {
    var checked = rtDialog.find('.r-wrap [type="checkbox"]:not(.sel-all):checked');
    organObj[curActId] = [];
    checked.each(function(i, dom) {
      organObj[curActId].push($(dom).val());
    })
  }
}

// 选择本身判断是否勾选全选
function selectSelf(el, type) {
  var target = $(el);
  var checkedLen = target.closest('.r-wrap').find('input[type="checkbox"]:not(.sel-all):checked').length;
  var checkboxLen = target.closest('.r-wrap').find('input[type="checkbox"]:not(.sel-all)').length;
  target.closest('.r-wrap').find('.sel-all').prop('checked', checkboxLen === checkedLen);
  if(type === 'sample') {
    var checked = rtDialog.find('.r-wrap [type="checkbox"]:not(.sel-all):checked');
    organObj[curActId] = [];
    checked.each(function(i, dom) {
      organObj[curActId].push($(dom).val());
    })
  }
}

// 清空
function clearItemHandler(ele) {
  rtDialog.find('[type="checkbox"]:checked').prop('checked', false);
  rtDialog.find('[name="inp-bw"]').val('');
  rtDialog.find('[name="inp-bbmc"]').val('');
}

// 点击过滤子项
function filterByChild(ele, type) {
  $(ele).addClass('selText').siblings().removeClass('selText');
  var ftVal = $(ele).text();
  if(type === 'examItem') { 
    getExamItemList(ftVal === '全部' ? '' : ftVal, true);
  }
}

// 标本的html，通过标签展示
function createSampleHtml(data) {
  var id = data.id;
  var html = '';
  html += '<div style="display: inline-block;" class="sampleItem">';
  html += '<div class="w-con options">';
  html += '<span style="vertical-align: middle;">' +(data.parentName + '-') +'</span>'
  html += '<span class="rt-sr-w rt-sr-tit" id="'+id+'" pid="sample-0001" rt-sc="name:标本名称;wt:;vt:;pvf:;desc:标本名称;code:300;" style="text-align: right;padding-top: 0;vertical-align: middle;margin-right: 5px;">';
  html += data.targetName;
  html += '</span>';
  html += '<span onclick="removeItemHandler(this,\''+id+'\',\'sample\')">×</span>';
  html += '</div>';
  html += '<div class="w-con" style="display: none;">';
  html += '<div class="rt-sr-w rt-sr-tit" id="'+id+'.01" pid="'+id+'" rt-sc="name:标本采集部位;wt:;vt:;pvf:;desc:标本所属部位;code:301;">'+data.parentName+'</div>';
  html += '</div>';
  html += '</div>';

  if(rtStructure && rtStructure.idAndDomMap) {
    rtStructure.idAndDomMap[id] = {
      id: id,
      desc: '标本名称',
      name: '标本名称',
      // pageId: rtStructure.idAndDomMap['sample-0001'].pageId,
      pid: 'sample-0001',
      pvf: '',
      req: '',
      rtScPageNo: 1,
      value: data.targetName,
      wt: '',
      vt: '',
      code: '300'
    }
    rtStructure.idAndDomMap[id+'.01'] = {
      id: id+'.01',
      desc: '标本采集部位',
      name: '标本采集部位',
      // pageId: rtStructure.idAndDomMap['sample-0001'].pageId,
      pid: id,
      pvf: '',
      req: '',
      rtScPageNo: 1,
      value: data.parentName,
      wt: '',
      vt: '',
      code: '301'
    }
  }
  return html;
}

// 标本的html，通过表格展示
function createSampleHtmlInTable(data, isView) {
  var id = data.id;
  var html = '';
  html += '<tr class="sampleItem">';
  html += '<td class="sortNo">'+(addSampleList.length + 1)+'</td>';
  html += '<td>';
  html += '<div class="w-con">';
  html += isView ? '<span>'+data.targetName+'</span>' : 
    '<textarea class="rt-sr-w rt-sr-t" style="height: 48px;width: 100px;" id="'+id+'" pid="sample-0001" rt-sc="pageId:zjwjP;name:标本名称;wt:1;vt:;pvf:;code:300;desc:标本名称;">'+data.targetName+'</textarea>';
  html += '</div>';
  html += '</td>';
  html += '<td>';
  html += '<div class="w-con">';
  html += '<span class="rt-sr-w rt-sr-tit" id="'+id+'.01" pid="'+id+'" rt-sc="pageId:zjwjP;name:标本部位;wt:;vt:;pvf:;code:301;desc:标本部位;" style="text-align:center">'+data.parentName+'</span>'
  html += '</div>';
  html += '</td>';
  html += '<td>';
  var dateAndTime = getCurDateAndTime(true);
  var moment = dateAndTime.curDate;
  var momentTime = dateAndTime.curTime;
  if(isView) {
    html += '<span>'+data.ltTime+'</span>';
  } else {
    data.ltTime = data.ltTime ? data.ltTime : (moment + ' ' + momentTime);
    html += '<div class="laydate-w w-con">';
    html += '<i class="layui-icon layui-icon-date"></i>';
    html += '<textarea val-format="HH时mm分" class="rt-sr-w rt-sr-t lt-time" style="width: 135px;" id="'+id+'.02" pid="'+id+'" rt-sc="pageId:zjwjP;name:离体时间;wt:1;vt:;pvf:;desc:离体时间;">'+(data.ltTime || '')+'</textarea>';
    html += '</div>';
  }
  html += '</td>';
  html += '<td>';
  if(isView) {
    html += '<span>'+data.gdTime+'</span>';
  } else {
    data.gdTime = data.gdTime ? data.gdTime : (moment + ' ' + momentTime);
    html += '<div class="laydate-w w-con">';
    html += '<i class="layui-icon layui-icon-date"></i>';
    html += '<textarea val-format="HH时mm分" class="rt-sr-w rt-sr-t gd-time" style="width: 135px;" id="'+id+'.03" pid="'+id+'" rt-sc="pageId:zjwjP;name:固定时间;wt:1;vt:;pvf:;desc:固定时间;">'+(data.gdTime || '')+'</textarea>';
    html += '</div>';
  }
  html += '</td>';
  html += '<td>';
  if(isView) {
    html += '<span>'+data.barcodeNo+'</span>';
  } else {
    html += '<div class="w-con">';
    html += '<textarea class="rt-sr-w rt-sr-t" style="height: 48px;width: 100px" id="'+id+'.04" pid="'+id+'" rt-sc="pageId:zjwjP;name:标本编号;wt:1;vt:;pvf:;desc:标本编号;">'+(data.barcodeNo || '')+'</textarea>';
    html += '</div>';
    html += '<div class="w-con" style="display:none">';
    html += '<textarea class="rt-sr-w rt-sr-t" style="height: 48px;width: 100px" id="'+id+'.00" pid="'+id+'" rt-sc="pageId:zjwjP;name:标本编号主键;wt:1;vt:;pvf:;desc:标本编号主键;">'+(data.sampleNo || '')+'</textarea>';
    html += '</div>';
  }
  html += '</td>';
  html += '<td class="use-col">';
  // html += '<span class="use-txt">打印</span>';
  html += '<span class="use-txt" onclick="removeItemHandler(this,\''+id+'\',\'sample\')">删除</span>';
  html += '</td>';
  html += '</tr>';
  if(rtStructure && rtStructure.idAndDomMap) {
    // var pageId = rtStructure.idAndDomMap['sample-0001'].pageId;
    addIdAndDomMap(id, {
      desc: '标本名称',
      name: '标本名称',
      // pageId: pageId,
      pid: 'sample-0001',
      value: data.targetName,
      code: '300'
    });
    addIdAndDomMap(id+'.01', {
      desc: '标本部位',
      name: '标本部位',
      // pageId: pageId,
      pid: id,
      value: data.parentName,
      code: '301'
    });
    addIdAndDomMap(id+'.02', {
      desc: '标本离体时间',
      name: '标本离体时间',
      // pageId: pageId,
      pid: id,
      value: data.ltTime || '',
    });
    addIdAndDomMap(id+'.03', {
      desc: '标本固定时间',
      name: '标本固定时间',
      // pageId: pageId,
      pid: id,
      value: data.gdTime || '',
    });
    addIdAndDomMap(id+'.04', {
      desc: '标本编号',
      name: '标本编号',
      // pageId: pageId,
      pid: id,
      value: data.barcodeNo || '',
    });
    addIdAndDomMap(id+'.00', {
      desc: '标本编号(主键)',
      name: '标本编号(主键)',
      // pageId: pageId,
      pid: id,
      value: data.sampleNo || '',
    });
  }
  return html;
}

// 成功添加标本
function sampleSuccess(result, isView) {
  sampleByTable = $('.t-pg').attr('data-sampleByTable');
  for(var i = 0; i < result.length; i++) {
    if(!sampleByTable) {
      var isAdded = false;
      for(var j = 0; j < addSampleList.length; j++) {
        var existItem = addSampleList[j];  //已存在的项
        if(existItem.targetName === result[i].targetName && existItem.parentName === result[i].parentName) {
          isAdded = true;
          break;
        }
      }
      if(isAdded) {
        continue;
      }
    }
    result[i].id = result[i].id || 'sample-0001.' + createUUidFun();
    var html = sampleByTable ? createSampleHtmlInTable(result[i], isView) : createSampleHtml(result[i]);
    $(rtStructure.ele).find('.sampleEditPart').append(html);
    if(sampleByTable) {
      initDatePicker();
      $(rtStructure.ele).find('.sample-table').show();
    }
    addSampleList.push(result[i]);
  }
}

// 检查项目的html
function createExamItemHtml(data) {
  var id = data.id;
  var html = '';
  html += '<div class="w-con options">';
  html += '<span class="rt-sr-w rt-sr-tit" id="'+id+'" pid="exItem-0001" rt-sc="name:检查项目;wt:;vt:;pvf:;desc:项目名称;code:101;" style="text-align: right;padding-top: 0;vertical-align: middle;margin-right: 5px;">';
  html += data.targetName;
  html += '</span>';
  html += '<span onclick="removeItemHandler(this,\''+id+'\',\'examItem\')">×</span>';
  html += '</div>';

  if(rtStructure && rtStructure.idAndDomMap) {
    rtStructure.idAndDomMap[id] = {
      id: id,
      desc: data.parentName + (data.itemCode ? '__' + data.itemCode : ''),
      name: '检查项目',
      // pageId: rtStructure.idAndDomMap['exItem-0001'].pageId,
      pid: 'exItem-0001',
      pvf: '',
      req: '',
      rtScPageNo: 1,
      value: data.targetName,
      wt: '',
      vt: '',
      code: '101'
    }
  }
  return html;
}
// 成功添加项目
function examItemsSuccess(result) {
  for(var i = 0; i < result.length; i++) {
    var isAdded = false;
    for(var j = 0; j < addExamItemList.length; j++) {
      var existItem = addExamItemList[j];  //已存在的项
      if(existItem.targetName === result[i].targetName && 
        (existItem.parentName === result[i].parentName || existItem.parentName === (result[i].parentName + '__' + result[i].itemCode))
      ) {
        isAdded = true;
        break;
      }
    }
    if(isAdded) {
      continue;
    }
    result[i].id = result[i].id ? result[i].id : 'exItem-0001.' + createUUidFun();
    var html = createExamItemHtml(result[i]);
    $(rtStructure.ele).find('.eItemEditPart').append(html);
    addExamItemList.push(result[i]);
  }
  if(addExamItemList && addExamItemList.length) {
    setLocalExamItem(addExamItemList);
  }
}
// 添加标本信息/项目
function addMarkInfoHandler(type) {
  addExamItemOrOrgan(type, type === 'examItem' ? examItemsSuccess : sampleSuccess)
}

// 删除当前项
function removeItemHandler(ele, id, type) {
  sampleByTable = $('.t-pg').attr('data-sampleByTable');
  if(type === 'examItem') {
    $(ele).closest('.options').remove();
  } else {
    $(ele).closest('.sampleItem').remove();
    if(sampleByTable) {
      $('.sampleEditPart .sampleItem').each(function(i, dom) {
        $(dom).find('.sortNo').text(i + 1);
      })
    }
  }
  var list = type === 'examItem' ? addExamItemList : addSampleList;
  delete rtStructure.idAndDomMap[id];
  if(type === 'sample') {
    delete rtStructure.idAndDomMap[id + '.01'];  //标本部位
    if(sampleByTable) {
      delete rtStructure.idAndDomMap[id + '.02']; //离体时间	
      delete rtStructure.idAndDomMap[id + '.03']; //固定时间
      delete rtStructure.idAndDomMap[id + '.04']; //标本编号
      delete rtStructure.idAndDomMap[id + '.00']; //标本编号主键
    }
  }
  for(var i = 0; i < list.length; i++) {
    if(list[i].id === id) {
      list.splice(i, 1);
      break;
    }
  }
  if(type === 'examItem') {
    setLocalExamItem(addExamItemList)
  }
}

// 获取指定的数据集合
function getDataByFlag(flag) {
  if(!rtStructure) {
    return {};
  }
  var data = rtStructure.enterOptions.resultData;
  var res = {};
  for(var i = 0; i < data.length; i++) {
    if(data[i].id === flag) {
      res = data[i];
      break;
    }
  }
  return res;
}

// 获取指定id的值
function getDataByAid(data, aimId) {
  var value = '';
  for(var i = 0; i < data.length; i++) {
    if(data[i].id === aimId) {
      value = data[i].val;
      break;
    }
  }
  return value;
}

// 特殊处理标本和项目的展示 type: sample标本  examItem检查项目, isView是否预览
function specialBlockViewHandler(type, isView) {
  var allData = getDataByFlag(type === 'sample' ? 'sample-0001' : 'exItem-0001'); 
  var data = allData && allData.child ? JSON.parse(JSON.stringify(allData.child)) : [];
  sampleByTable = $('.t-pg').attr('data-sampleByTable');
  if(type === 'sample') {
    addSampleList = [];
    // 预览和编辑都是通过表格展示
    if(!sampleByTable && isView) {
      var tbody = $('.sampleViewPart tbody');
      var tr = '';
      if(data.length && data.length % 2 !== 0) {
        data.push({});
      }
      for(var i = 0; i < data.length; i++) {
        if(i%2===0) {
          tr += '<tr>';
        }
        if(JSON.stringify(data[i]) === '{}') {
          tr += '<td></td>';
          tr += '<td colspan="2"></td>';
          tr += '<td colspan="3"></td>';
        } else {
          tr += '<td>'+(i+1)+'</td>';
          tr += '<td colspan="2">'+data[i].child[0].val+'</td>';
          tr += '<td colspan="3">'+data[i].val+'</td>';
        }
        
        if(i%2!==0) {
          tr += '</tr>'
        }
      }
      tbody.html(tr);
      if(data.length) {
        $('.sampleViewPart').show();
      } else {
        $('.sampleViewPart').hide();
      }
    } else {
      var arr = [];
      for(var i = 0; i < data.length; i++) {
        var sampleObj = {
          id: data[i].id,
          targetName: data[i].val,
          parentName: '',
          ltTime: '',
          gdTime: '',
          sampleNo: '',
          barcodeNo: '',
        }
        if(data[i].child && data[i].child.length) {
          sampleObj.parentName = getDataByAid(data[i].child, data[i].id+'.01');
          sampleObj.ltTime = getDataByAid(data[i].child, data[i].id+'.02');
          sampleObj.gdTime = getDataByAid(data[i].child, data[i].id+'.03');
          sampleObj.barcodeNo = getDataByAid(data[i].child, data[i].id+'.04');
          sampleObj.sampleNo = getDataByAid(data[i].child, data[i].id+'.00');
        }
        if(!sampleObj.sampleNo && applyInfo.gmSampleList && applyInfo.gmSampleList.length) {
          for(var j = 0; j < applyInfo.gmSampleList.length; j++) {
            var resItem = applyInfo.gmSampleList[j];
            if(resItem.sampleName === sampleObj.targetName && resItem.samplePart === sampleObj.parentName) {
              sampleObj.sampleNo = resItem.sampleNo;
              $('[id="'+data[i].id+'.00"]').val(sampleObj.sampleNo);
              break;
            }
          }
        }
        arr.push(sampleObj)
      }
      if(sampleByTable && arr.length) {
        $('.sample-table').show();
      } else {
        $('.sample-table').hide();
      }
      sampleSuccess(arr, sampleByTable ? isView : false);
    }
  }

  if(type === 'examItem') {
    addExamItemList = [];
    var tempArr = []
    var arr = [];
    for(var i = 0; i < data.length; i++) {
      arr.push(data[i].val);
      tempArr.push({
        id: data[i].id,
        targetName: data[i].val,
        parentName: data[i].desc,
        itemName: data[i].val,
      })
    }
    if(isView) {
      $('.eItemViewPart').html(arr.join('、'));
    } else {
      examItemsSuccess(tempArr);
    }
  }
}

// 获取缓存中的检查项目
function getLocalExamItem() {
  var srExamItem = window.localStorage.getItem('srExamItem') || null;
  if(srExamItem) {
    var data = JSON.parse(srExamItem);
    if(srExamItem.length) {
      examItemsSuccess(data);
    }
  }
}

// 缓存检查项目
function setLocalExamItem(data) {
  try {
    if(data) {
      window.localStorage.setItem('srExamItem', JSON.stringify(data));
    } else {
      window.localStorage.removeItem('srExamItem');
    }
  } catch (err) {
    console.error(err)
  }
}
