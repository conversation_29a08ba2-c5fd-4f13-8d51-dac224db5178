/* 颜色类 */
.bor-b {
  border-bottom: 1px solid #DCDFE6;
}

/* 边距类 */
.mb-6 {
  margin-bottom: 6px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-8 {
  margin-left: 8px;
}

.ml-10 {
  margin-left: 10px;
}

.mt-4 {
  margin-top: 4px;
}

.mt-8 {
  margin-top: 8px;
}

.ml-12 {
  margin-left: 12px;
}

.mr-104 {
  margin-right: 104px;
}

.pl-8 {
  padding-left: 8px;
}

/* 宽度类 */
.wd-72 {
  display: inline-block;
  width: 72px !important;
}

.wd-84 {
  display: inline-block;
  width: 84px !important;
}

.mwd-68 {
  width: 68px !important;
  max-width: 68px !important;
}

.mwd-72 {
  width: 72px !important;
  max-width: 72px !important;
}

.mwd-80 {
  width: 80px !important;
  max-width: 80px !important;
}

.wd-106 {
  width: 106px !important;
}

.wd-108 {
  width: 108px !important;
}

.wd-115 {
  width: 115px !important;
}

.wd-132 {
  width: 132px !important;
}

.wd-140 {
  width: 140px !important;
}

.mwd-144 {
  width: 144px !important;
  max-width: 144px !important;
}

.mwd-208 {
  width: 208px !important;
  max-width: 208px !important;
}

.wd-218 {
  width: 218px !important;
}

.wd-234 {
  width: 234px !important;
}

.wd-230 {
  width: 230px !important;
}

.wd-240 {
  width: 240px !important;
}

.wd-336 {
  width: 336px !important;
}

.wd-340 {
  width: 340px !important;
}

.wd-472 {
  width: 472px !important;
}

/* 病理检查申请单 */
#blhtzzsyd1 {
  /* width: 1140px; */
  font-size: 14px;
  color: #606266;
  background: #fff;
  margin: 0 auto;
  border: 1px solid #DCDFE6;
}

.layout-page .main-content {
  width: 1140px;
}

#blhtzzsyd1 input[type="checkbox"], #blhtzzsyd1 input[type="radio"] {
  vertical-align: middle;
}

.bljcsqd-h {
  height: 48px;
  padding: 13px 20px;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: bold;
  border-radius: 2px 2px 0px 0px;
}

#blhtzzsyd1 .laydate-w .rt-sr-w {
  height: 28px;
}

.box-title {
  height: 44px;
  padding: 12px 44px;
  background: #F5F7FA;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  justify-content: space-between;
}

.box-title .bb-title {
  color: rgba(0, 0, 0, 0.88);
  font-weight: bold;
}

.box-title .span-tit {
  margin-left: 4px;
}

.box-con {
  padding: 12px 35px 12px 21px;
}

.row-item {
  display: inline-block;
  width: 365px;
}

.row-item:last-child {
  width: unset;
}

.row-title {
  text-align: right;
}

.row-lab {
  width: 109px;
  display: inline-block;
  text-align: right;
}

.inp-sty {
  width: 230px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
}

.ver-t {
  vertical-align: top;
}

.btn-sty {
  display: inline-block;
  height: 36px;
  line-height: 36px;
  color: #FFF;
  background: #1885F2;
  border-radius: 3px;
  padding: 0 12px;
  cursor: pointer;
}

.add-btn, .del-btn {
  margin-left: 8px;
}

.del-tbtn {
  color: #1885F2;
  cursor: pointer;
}

.bbxx-table {
  border: 1px solid #DCDFE6;
  border-bottom: none;
  border-right: none;
  width: 100%;
  box-sizing: border-box;
  table-layout: fixed;
}

.bbxx-table thead {
  width: 56px;
  height: 48px;
  line-height: 48px;
  font-weight: bold;
  color: #909399;
  background: #F5F7FA;
}

.bbxx-table tr {
  height: 48px;
  line-height: 28px;
  text-align: center;
}

.w-con {
  display: inline-block;
  vertical-align: middle;
}

.show-or-hide {
  position: relative;
}

.show-or-hide input {
  padding: 0 16px;
  position: relative;
  background: #fff;
  /* z-index: 2; */
  /* background: transparent; */
}

.show-or-hide:hover {
  opacity: .8;
}

.show-or-hide::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  /* z-index: 1; */
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}

.show-or-hide.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}

.show-or-hide.hide-more:after {
  transform: rotate(-45deg);
  margin-top: -2px;
}

.bbmcInpSel {
  height: 250px;
  overflow: auto;
  overflow-x: hidden;
}

.bbmcInpSel .layui-menu-item-none {
  display: none;
}

.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}

.laySelLab li, .bbmcInpSel li {
  height: 26px;
}

#blhtzzsyd1 .laydate-w textarea[val-format] {
  height: 29px;
}

.bljcsqdTableBody tr:last-child>td {
  border-bottom: none;
}

#blhtzzsyd1 .isk {
  color: #F56C6C;
}

.laydate-time-list:not(.show-seconds)>li:last-child {
  display: inline-block !important;
}

.laydate-time-list:not(.show-seconds)>li {
  width: 33.3% !important;
}

.laydate-time-list:not(.show-seconds)>li>ol>li {
  padding-left: 33px !important;
}

.form-item {
  display: inline-block;
}

#blhtzzsyd1 .form-item .laydate-w input {
  border: none;
  outline: none;
  display: block;
  width: 100%;
  height: 100%;
  padding: 5px 8px 5px 30px;
  box-sizing: border-box;
  height: 30px;
}

#blhtzzsyd1 .htzz-view {
  display: none;
}

[isview="true"] #blhtzzsyd1{
  border: none;
}

[isview="true"] #blhtzzsyd1 .htzz-edit {
  display: none;
}

[isview="true"] #blhtzzsyd1 .htzz-view {
  display: block;
}

[isview="true"].main-content.bljcsdq-content {
  width: unset !important;
}

#blhtzzsyd1 .htzz-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 14px;
  min-height: 1100px;
  background: #fff;
  padding: 44px 32px 32px;
  color: #000;
}

#blhtzzsyd1 .htzz-view * {
  font-family: "宋体", "SimSun", sans-serif;
}

#blhtzzsyd1 .htzz-view .view-head {
  padding-bottom: 8px;
  text-align: center;
}

#blhtzzsyd1 .msg-num {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#blhtzzsyd1 .head-title {
  font-size: 22px;
  font-family: "黑体", "SimHei", "方正黑体简体", "Microsoft YaHei", sans-serif !important;
  font-weight: 600;
}

#blhtzzsyd1 .view-form {
  width: 100%;
  min-height: calc(1100px - 136px);
  overflow: hidden;
  border: 1px solid #000;
  position: relative;
}

#blhtzzsyd1 .from-line {
  border-bottom: 1px solid #000;
  height: 28px;
  line-height: 28px;
}

.flex-flow {
  display: flex;
  align-items: center;
}

.view-item {
  padding-left: 8px;
}

.view-item, .view-item div {
  display: flex;
  flex-wrap: nowrap;
  box-sizing: border-box;
}

.view-item+.view-item {
  border-left: 1px solid #000;
}

.item-sty {
  overflow: hidden;
  white-space: nowrap;
  margin-right: 4px;
}

.label-tit {
  font-weight: 600;
}

#lines {
  height: calc(1000px - 300px);
  overflow: hidden;

}

.line-noborder {
  padding-top: 4px;
  padding-left: 8px;
  height: 20%;
  box-sizing: border-box;
  line-height: 20px;
}

.special-item {
  width: 100%;
  height: calc(100% - 20px);
  min-height: 20px;
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

.sample-tbl {
  font-size: 14px;
  border-color: #000;
}

.sample-tbl tr th, .sample-tbl tr td {
  padding: 0 4px;
  line-height: 28px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  border: 1px #000 solid;
}
.form-footer .sample-tbl tr td{
  white-space: unset;
  text-align: left;
}

.bot-tip {
  font-size: 12px;
  line-height: 16px;
  padding: 0 8px;
}

.form-footer {
  min-height: 92px !important;
}