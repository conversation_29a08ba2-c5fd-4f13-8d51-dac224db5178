@charset "UTF-8";
#hbetjybl21 {
  font-size: 16px;
  color: #000;
  background-color: #F5F7FA;
  min-height: 100%;
}
#hbetjybl21 * {
  font-family: "宋体";
}
#hbetjybl21 .layui-input {
  height: 28px;
}
#hbetjybl21 .edit-public {
  padding: 12px 24px;
}
#hbetjybl21 .edit {
  display: flex;
  flex-direction: column;
  row-gap: 4px;
}
#hbetjybl21 .edit .specimen {
  display: flex;
  align-items: center;
  height: 44px;
}
#hbetjybl21 .preview {
  display: none;
  flex-direction: column;
  width: 780px;
  min-height: 1100px;
  background-color: #fff;
  margin: 0 auto;
  padding: 40px 56px;
  padding-top: 36px;
  word-break: break-all;
}
#hbetjybl21 .preview .preview-header .header-title .code {
  width: 122px;
  height: 36px;
}
#hbetjybl21 .preview .preview-header .header-title .code img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
#hbetjybl21 .preview .preview-content .report-img {
  display: flex;
  justify-content: space-between;
}
#hbetjybl21 .preview .preview-content .report-img img {
  width: 320px;
  height: 240px;
  object-fit: cover;
}
#hbetjybl21 .preview .preview-footer .audit {
  height: 48px;
}
#hbetjybl21 .preview .preview-footer .audit img {
  width: 64px;
  height: 32px;
  object-fit: cover;
}

[isView=true] #hbetjybl21 .edit {
  display: none;
}

[isView=true] #hbetjybl21 .preview {
  display: flex;
}

.border-t {
  border-top: 2px solid #000;
}

.border-b {
  border-bottom: 2px solid #000;
}

.rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  width: 100%;
  padding: 4px;
  resize: both;
}
.rt-textarea::placeholder {
  color: #000;
}

.rt-sr-label {
  margin-right: 8px;
}

.custom-select {
  position: relative;
}
.custom-select::after {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 4px;
  content: "";
  width: 8px;
  height: 8px;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg) translateY(-100%);
}
.custom-select .layui-input {
  height: 28px;
}

.rt-sr-r {
  margin-right: 4px;
}

.pl-24 {
  padding-left: 24px;
}

.pr-24 {
  padding-right: 24px;
}

.pt-8 {
  padding-top: 8px;
}

.pb-8 {
  padding-bottom: 8px;
}

.pb-20 {
  padding-bottom: 20px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-6 {
  margin-top: 6px;
}

.mt-4 {
  margin-top: 4px;
}

.m-0-6 {
  margin: 0 6px;
}

.w-10 {
  width: 10px;
}

.w-15 {
  width: 15px;
}

.w-20 {
  width: 20px;
}

.w-25 {
  width: 25px;
}

.w-30 {
  width: 30px;
}

.w-35 {
  width: 35px;
}

.w-40 {
  width: 40px;
}

.w-45 {
  width: 45px;
}

.w-50 {
  width: 50px;
}

.w-55 {
  width: 55px;
}

.w-60 {
  width: 60px;
}

.w-65 {
  width: 65px;
}

.w-70 {
  width: 70px;
}

.w-75 {
  width: 75px;
}

.w-80 {
  width: 80px;
}

.w-85 {
  width: 85px;
}

.w-90 {
  width: 90px;
}

.w-95 {
  width: 95px;
}

.w-100 {
  width: 100px;
}

.w-105 {
  width: 106px;
}

.w-110 {
  width: 110px;
}

.w-115 {
  width: 115px;
}

.w-120 {
  width: 120px;
}

.w-125 {
  width: 125px;
}

.w-130 {
  width: 130px;
}

.w-135 {
  width: 135px;
}

.w-140 {
  width: 140px;
}

.w-145 {
  width: 145px;
}

.w-150 {
  width: 150px;
}

.w-155 {
  width: 155px;
}

.w-160 {
  width: 160px;
}

.w-165 {
  width: 165px;
}

.f-1 {
  flex: 1;
}

.f-2 {
  flex: 2;
}

.f-3 {
  flex: 3;
}

.fw-600 {
  font-weight: 600;
}

.a-center {
  display: flex;
  align-items: center;
}

.a-start {
  display: flex;
  align-items: flex-start;
}

.j-end {
  justify-content: flex-end;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-r {
  text-align: right;
}

.fs-36 {
  font-size: 36px;
}

.fs-24 {
  font-size: 24px;
}

.fs-20 {
  font-size: 20px;
}

.fs-21 {
  font-size: 21px;
}
