$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
  }
  desctiptHandler();
  curElem.find('.rt-sr-w').change(function() {
    desctiptHandler();
  })
}

function desctiptHandler() {
  var sideArr = {'左乳': '0001', '右乳': '0002'};
  var descriptArr = [];
  for(var key in sideArr) {
    var prefix = sideArr[key];
    var desc = '';
    var tps = [];

    // BI-RADS分级：
    if(getVal('[name="RG-'+prefix+'.001.01"]:checked')) {
      desc += 'BI-RADS分级：' + getVal('[name="RG-'+prefix+'.001.01"]:checked') + '。';
    }

    // 肿块：
    if(getVal('[name="RG-'+prefix+'.002.01"]:checked')) {
      if(getVal('[name="RG-'+prefix+'.002.01"]:checked') === '有') {
        if(getVal('[id="ctSP-0001.002.01.01"] .rt-sr-w')) {
          tps.push(getVal('[id="ctSP-0001.002.01.01"] .rt-sr-w') + 'mm')
        }
        if(getVal('[id="ctSP-0001.002.01.02"] .rt-sr-w')) {
          tps.push(getVal('[id="ctSP-0001.002.01.02"] .rt-sr-w') + 'mm')
        }
      }
      desc += '肿块：' + getVal('[name="RG-'+prefix+'.002.01"]:checked');
      if(tps.length) {
        desc += '(大小：' + tps.join(' * ') + ')';
      }
      desc += '。'
    }

    var radioObj = {
      '003': '可疑钙化：',
      '004': '结构絮乱：',
    }
    for(var radioKey in radioObj) {
      if(getVal('[name="RG-'+prefix+'.'+radioKey+'.01"]:checked')) {
        desc += radioObj[radioKey] + getVal('[name="RG-'+prefix+'.'+radioKey+'.01"]:checked') + '。';
      } 
    }

    // 部位：
    tps = [];
    for(var i = 1; i <= 6; i++) {
      // ctSP-0001.005.01-cItem--5
      if(getVal('[id="ctSP-'+prefix+'.005.01-cItem--'+i+'"]:checked')) {
        tps.push(getVal('[id="ctSP-'+prefix+'.005.01-cItem--'+i+'"]:checked'));
      }
    }
    if(tps.length) {
      desc += '部位：' + tps.join('、') + '。'
    }

    // 其他：
    if(getVal('[id="ctSP-'+prefix+'.006.01"] .rt-sr-w')) {
      desc += '其他：' + getVal('[id="ctSP-'+prefix+'.006.01"] .rt-sr-w') + '。';
    } 

    // 建议
    tps = [];
    for(var i = 1; i <= 4; i++) {
      // rxSP-0001.015.01-cItem--1
      if(getVal('[id="ctSP-'+prefix+'.007.01-cItem--'+i+'"]:checked')) {
        tps.push(getVal('[id="ctSP-'+prefix+'.007.01-cItem--'+i+'"]:checked'));
      }
    }
    if(tps.length) {
      desc += '建议：' + tps.join('、') + '。'
    }

    // 是否结案
    if(getVal('[name="RG-'+prefix+'.008.01"]:checked')) {
      desc += '是否结案：' + getVal('[name="RG-'+prefix+'.008.01"]:checked') + '。';
    } 

    if(desc) {
      descriptArr.push(key + '：' + desc);
    }
    
  }
  if(rtStructure) {
    rtStructure.description = descriptArr.join('\n');
  }
}

function getVal(selector) {
  var dom = curElem.find(selector);
  var value = '';
  if(dom.length) {
    value = dom.val();
  }
  return value;
}