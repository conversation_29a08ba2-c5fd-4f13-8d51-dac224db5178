#hbetcovid19 {
  font-size: 16px;
  min-height: 100%;
  margin: 0 auto;
  background-color: #F5F7FA;
  * {
    font-family: 宋体;
  }
  input {
    font-size: 16px;
    border: 1px solid #C0C4CC;
    line-height: 22px;
    border-radius: 3px;
    padding: 2px 8px;
  }
  .info-top-wrap {
    .tl-cont {
      position: relative;
      text-align: center;
      padding-bottom: 34px;
      border-bottom: 2px solid #000;
      h1, h3, h2 {
        color: #000;
      }
      h1 {
        font-family: 仿宋;
        font-size: 21px;
        line-height: 1;
      }
      h3 {
        margin-top: 3px;
        font-family: 仿宋;
        font-size: 21px;
        line-height: 1;
      }
      h2 {
        margin-top: 20px;
        font-size: 26px;
        line-height: 1;
      }
      .code {
        position: absolute;
        right: 0;
        top: 27px;
        height: 36px;
        img {
          height: 36px;
        }
      }
      .code-text {
        position: absolute;
        right: 0;
        bottom: 8px;
      }
    }
    .base-info {
      overflow: hidden;
      line-height: 1;
      .item {
        width: 25%;
        margin-top: 8px;
        float: left;
        &.per20 {
          width: 20%;
        }
        &.per30 {
          width: 30%;
        }
        &.per35 {
          width: 35%;
        }
        &.per40 {
          width: 40%;
        }
        &.per50 {
          width: 50%;
        }
        &.per55 {
          width: 55%;
        }
        &.per65 {
          width: 65%;
        }
        &.per75 {
          width: 75%;
        }
        &.per100 {
          width: 100%;
        }
      }
      .lb {
        width: 80px;
        display: inline-block;
        text-align: right;
      }
    }
  }
  .input-body-wrap {
    padding: 10px;
    .row {
      display: flex;
      margin-top: 8px;
      &:first-child {
        margin-top: 0;
      }
      &.hd {
        font-weight: bold;
      }
    }
    .cell {
      height: 100%;
      margin-left: 8px;
      &:first-child {
        margin-left: 0;
      }
      &:nth-of-type(1) {
        flex: 250;
      }
      &:nth-of-type(2) {
        flex: 150;
      }
      &:nth-of-type(3) {
        flex: 150;
      }
      &:nth-of-type(4) {
        flex: 200;
      }
      &.with-select {
        position: relative;
        &:after {
          content: "";
          position: absolute;
          top: 50%;
          right: 4px;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          border-left: 1px solid #999;
          border-bottom: 1px solid #999;
          transform: rotate(-45deg) translateY(-100%);
        }
      }
    }
    input {
      width: 100%;
      height: 100%;
    }
  }
  .remark-wrap {
    p {
      line-height: 22px;
      font-size: 14px;
    }
  }
  .bottom-info-cont {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    padding: 8px 0;
    margin-top: 8px;
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    img {
      vertical-align: middle;
      width: 64px;
      height: 32px;
      object-fit: cover;
    }
  }
  .rt-sr-footer {
    position: absolute;
    left: 50px;
    right: 50px;
    bottom: 30px;
    .tip {
      margin-top: 8px;
      font-size: 14px;
      &.flex {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .rt-sr-header {
    margin-bottom: 8px;
    display: none;
  }
  .rt-sr-footer {
    display: none;
  }
}

[isview="true"] #hbetcovid19 {
  padding: 30px 50px 190px;
  width: 780px;
  min-height: 1100px;
  background: white;
  .rt-sr-header, .rt-sr-footer {
    display: block;
  }
  .input-body-wrap {
    padding: 10px 0;
    border-top: 2px solid #000;
    .row {
      &.hd {
        padding-bottom: 10px;
        border-bottom: 2px solid #000;
      }
    }
    .cell {
      &.with-select {
        &:after {
          display: none;
        }
      }
    }
  }
}