$(function() {
  window.initHtmlScript = initHtmlScript;
})

function initHtmlScript(ele) {
  var ele = $(ele);
  initInpNumber(ele, i2typeByAge);   //带加减按钮
  inpNumberHandler(ele);  //仅有输入框

  // 初始化浅色和深色块的显示关系
  toggleHightBlock(ele);
  initLigthItemChange(ele);

  i2typeByAge(ele);

  // 判断a角度是否填写
  checkedAangleHandler(ele);

  // 定型
  confirmLevel(ele)
  
  ele.find(".level-wrap").on('change', '.rt-sr-w', function(){
    var key = $(this).attr("parent-id").split('-')[1];
    confirmLevel(ele, key);
  })
  ele.find("[level]").on('change blur', function(){
    var key = $(this).attr("level");
    confirmLevel(ele, key);
  })

  ele.find(".angle-wrap").on('blur', '.only-inp-num',  function(){
    var angleValue = $(this).val();
    if($(this).siblings('.p-lb').text() === 'β角：') {
      return;
    }
    if(angleValue) {
      var arr = [
        {min: 60, max: 360, gxkfy: '良好', gyq: '稍钝'},
        {min: 50, max: 59},
        {min: 43, max: 49, gxkfy: '不良', gyq: '圆钝至平坦'},
        {min: -360, max: 42, gxkfy: '差', gyq: '平坦'},
      ];
      var ageMap = {
        '0-5': {gxkfy: '尚可', gyq: '圆钝'},
        '6-12': {gxkfy: '尚可', gyq: '圆钝'},
        '13': {gxkfy: '不良', gyq: '圆钝'},
      }
      var index = -1;
      var item = {};
      for(var i = 0; i< arr.length; i++) {
        item = arr[i];
        if(Number(angleValue) >= item.min && Number(angleValue) <= item.max) {
          index = i;
          break;
        }
      }
      if(index > -1) {
        var wrap = $(this).closest(".angle-wrap");
        var activeId = wrap.find(".level-wrap .lb-row").find('.rt-sr-w:checked').attr("id");
        var hightItem  = wrap.find('.hight-item[contect-id="'+activeId+'"]');

        var radio = wrap.find(".level-wrap .lb-row").eq(index).find('.rt-sr-w');
        // if(radio.is(":checked")) {
        //   return;
        // }
        radio.prop('checked', true);
        ele.find('[parent-id="'+radio.attr('id')+'"]').removeAttr('disabled');
        // 获取已填写的值
        var obj = {};
        var age = $('.gt12ck').is(':checked') ? '13' : $('.age-inp').val();
        hightItem.find('[data-wname]').each(function(wIndex, wEle) {
          var name = $(wEle).attr('data-wname');
          var rtW = $(wEle).find('.rt-sr-w');
          var value = rtW.val();
          if(rtW.hasClass('rt-sr-r')) {
            value = $(wEle).find('.rt-sr-w:checked').val();
          }
          if(item[name]) {
            value = item[name];  //赋相关等级的默认值
          } else {
            if(ageMap['13'][name] || ageMap['6-12'][name] || ageMap['0-5'][name]) {
              if(age >= 13) {
                value = ageMap['13'][name];
              } else if(age >= 0 && age < 6) {
                value = ageMap['0-5'][name];
              } else if(age >= 6 && age <= 12) {
                if(name === 'gxkfy') {
                  if(Number(angleValue) >= 50 && Number(angleValue) <= 55) {
                    value = '不良';
                  } else {
                    value = '尚可';
                  }
                } else {
                  value = ageMap['0-5'][name];
                }
              }
            }
          }
          obj[name] = value;
        })
        positionToLight(ele, radio, obj);
        // 确定分级
        var key = radio.attr("parent-id").split('-')[1];
        confirmLevel(ele, key);
      }
    }
  })
}

function checkedAangleHandler(ele) {
  ele.find('.level-wrap').each(function(i, wrap) {
    if(!$(wrap).find('[type="radio"][name]:checked').val()) {
      $(wrap).find('[type="radio"][name]').eq(0).prop('checked', true);
      $(wrap).closest('.w-block').find(".hight-block").show();
      $(wrap).closest('.w-block').find(".hight-block > .hight-item").eq(0).show();
      var radioId = $(wrap).find('[type="radio"][name]').eq(0).attr('id');
      ele.find('[parent-id="'+radioId+'"]').removeAttr('disabled');
    }
  })
}

// 根据年龄周数判断定量分型
function i2typeByAge(ele, fromCk) {
  var age = fromCk && $('.gt12ck').is(":checked") ? 13 : Number(ele.find("#kgjP1 .age-inp").val());
  if(!isNaN(age)) {
    if(age < 6) {
      ele.find("#kgjP1 .i2type").text('Ⅱa型');
    } else if(age >= 6 && age <= 12) {
      ele.find("#kgjP1 .i2type").text('Ⅱa(+) Ⅱa(-)型');
    } else if(age > 12) {
      ele.find("#kgjP1 .i2type").text('Ⅱb型');
    }
    confirmLevel(ele);
    if(age > 0 && age <= 12) {
      $(".gt12ck").prop("checked", false);
    }
    $(".angle-wrap .hight-item:visible .only-inp-num").blur();
  }
}

// 确定分型
function confirmLevel(ele, key) {
  if(key) {
    lrSideHandler(ele, key);
  } else {
    lrSideHandler(ele, '0002');    //左
    lrSideHandler(ele, '0003');    //右
  }
}

function lrSideHandler(ele, key) {
  var side = ele.find("#kgj-" + key).siblings(".w-block");
  var sideValue = ele.find('[name="RG-'+key+'.001"]:checked').val();
  var age = $('.gt12ck').is(":checked") ? 13 : Number(ele.find(".age-inp").val());
  var levelDom = key === '0002' ? ele.find(".l-level") : ele.find(".r-level");
  var levelKey = key === '0002' ? 'kgj-l-level' : 'kgj-r-level';
  var level = '';
  if(sideValue === 'α角≥60°') {
    level = 'Ⅰ型';
  } else if(sideValue === '50°≤α角<60°') {
    if(age < 6) {
      level = 'Ⅱa型';
    } else if(age >= 6 && age <= 12) {
      var gjValue = $('[name="RG-'+key+'.001.08"]:checked').val(); //骨性髋臼顶的值
      if(gjValue === '尚可' || gjValue === '良好') {
        level = 'Ⅱa(+)型';
      } else {
        level = 'Ⅱa(-)型';
      }
    } else if(age > 12) {
      level = 'Ⅱb型';
    }
  } else if(sideValue === '43°≤α角<50°') {
    var BVal = $('[id="kgj-'+key+'.001.12"] .rt-sr-w').val();
    if(Number(BVal) > 77) {
      level = 'D型';
    } else {
      level = 'Ⅱc型';
    }
  } else if(sideValue === 'α角<43°') {
    if($('[name="RG-'+key+'.001.20.01"]:checked').val() === '向下移位') {
      level = 'Ⅳ型';
    } else {
      level = 'Ⅲ型';
    }
  } else {
    level = '';
  }
  levelDom.val(level);
  if(window.findCurStructure) {
    var rtStructure = window.findCurStructure(ele[0], window.instanceList)
    rtStructure.idAndDomMap[levelKey].value = level;
  }
}

// 年龄大于12周的勾选
function changeAgeHandler(vm) {
  if($(vm).is(":checked")) {
    if($(".age-inp").val() > 0 && $(".age-inp").val() <= 12) {
      $(".age-inp").val('0');
    }
  }
  i2typeByAge($("#defaultKgjPage"), true);
  $(".angle-wrap .hight-item:visible .only-inp-num").blur();
}