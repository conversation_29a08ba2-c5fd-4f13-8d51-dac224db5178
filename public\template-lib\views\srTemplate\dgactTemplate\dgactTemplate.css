#dgact1 {
  font-size: 14px
}

#dgact1 * {
  box-sizing: border-box;
}

#dgact1 .sel-light {
  color: #1885F2;
  background: #ECF5FF;
}

#dgact1 .pl-10 {
  padding-left: 10px;
}

#dgact1 .pl-12 {
  padding-left: 12px;
}

#dgact1 .pl-42 {
  padding-left: 42px;
}

#dgact1 .mb-2 {
  margin-bottom: 2px;
}

#dgact1 .mr-12 {
  margin-right: 12px;
}

#dgact1 .ml-8 {
  margin-left: 8px;
}

#dgact1 .ml-12 {
  margin-left: 12px;
}

#dgact1 .ml-24 {
  margin-left: 24px;
}

#dgact1 .ml-26 {
  margin-left: 26px;
}

#dgact1 .ml-28 {
  margin-left: 28px;
}

#dgact1 .ml-74 {
  margin-left: 74px;
}

#dgact1 .ml-102 {
  margin-left: 102px;
}

#dgact1 .wid-70 {
  min-width: 70px;
}

#dgact1 .wid-84 {
  width: 84px;
}

#dgact1 .wid-96 {
  width: 96px;
}

#dgact1 .wid-98 {
  width: 98px;
}

#dgact1 .wid-186 {
  width: 186px;
}

#dgact1 .top-bar {
  border-bottom: 1px solid #c8d7e6; 
  background: #e8f3ff
}

#dgact1 .top-bar .bar-inner {
  width: 960px;
  margin: 0 auto
}

#dgact1 .top-bar span {
  margin-left: 12px;
  font-weight: bold;
  font-size: 18px;
  color: #000;
  line-height: 39px
}

#dgact1 .body-wrap {
  width: 960px;
  margin: 0 auto;
  padding: 12px 12px 12px 0;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  background: #fff
}
#dgact1 .headbot{
  height: 40px;
  width: 960px;
  margin: 0 auto;
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  background: #F5F7FA;
  display: flex;
}
#dgact1 .headbot .head-itme{
  padding: 0 18px;
  text-align: center;
  line-height: 40px;
  border-right: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6
}
#dgact1 .headbot .head-right{
  width: calc(100% - 280px);
  border-bottom: 1px solid #dcdfe6;
}

#dgact1 .body-wrap .p-row {
  overflow: hidden;
  margin-bottom: 8px;
  color: #000
}

#dgact1 .body-wrap .p-row:last-child {
  margin-bottom: 0
}

#dgact1 .body-wrap .lb {
  width: 118px;
  text-align: right;
  margin-top: 3px;
  float: left;
  line-height: 28px;
  color: #303133
}

#dgact1 label {
  display: inline-block;
  cursor: pointer;
}

#dgact1 label+label {
  margin-left: 12px;
}

#dgact1 input[type="text"] {
  vertical-align: top;
  padding: 0 4px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

#dgact1 input[type="radio"], #dgact1 input[type="checkbox"] {
  vertical-align: middle;
  cursor: pointer;
}

#dgact1 textarea, #dgact1 select {
  padding: 4px 10px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

#dgact1 .body-wrap .g-item {
  padding: 3px 12px;
  margin-left: 118px;
  min-height: 36px;
  line-height: 28px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  overflow: hidden
}

#dgact1 .body-wrap .g-item.view-no {
  padding: unset;
  margin: unset;
  min-height: unset;
  line-height: unset;
  background: unset;
  border: unset;
  overflow: unset;
}

#dgact1 .body-wrap .g-item.with-textarea {
  padding: 0;
  border: none
}

#dgact1 .bz-item {
  display: inline-block;
  width: 140px;
  height: 32px;
  padding: 3px 8px 3px 12px;
  line-height: 22px;
  border-right: 1px solid #dcdfe6;
}

#dgact1 .visit-status {
  color: #1885F2;
}

#dgact1 .bz-item .edit-btn {
  display: inline-block;
  width: 44px;
  height: 24px;
  background: #ECF5FF;
  border-radius: 3px;
  border: 1px solid #B3D8FF;
  color: #1885F2;
  text-align: center;
}

#dgact1 .cont {
  display: inline-block;
  width: 727px;
  background: #EBEEF5;
  padding: 3px 8px 3px 12px;
  border: 1px solid #DCDFE6;
  margin-bottom: 8px;
}

#dgact1 .lb-wid {
  display: inline-block;
  text-align: right;
  vertical-align: top;
}

#dgact1 .left-cont {
  display: inline-block;
  background: #F5F7FA;
  border-right: 1px solid #DCDFE6;
  vertical-align: top;
}

#dgact1 .right-cont {
  display: inline-block;
  background: #EBEEF5;
  vertical-align: top;
}

#dgact1 .lb-box {
  width: 100%;
  height: 28px;
  padding-left: 10px
}

#dgact1 .lb-box+.lb-box {
  margin: 0;
}

#dgact1 .l1-box {
  background-color: unset;
  width: 102px;
  padding-top: 8px;
}

#dgact1 .r1-box {
  width: 698px;
  height: 174px;
  padding: 8px 12px;
}

#dgact1 .r2-box {
  width: 128px;
  height: 100%;
  border-right: 1px solid #DCDFE6;
}

#dgact1 .r3-box {
  width: 724px;
  height: 104px;
  padding: 8px 12px;
}

#dgact1 .r2-right {
  width: 565px;
  height: 100%;
  display: inline-block;
}

#dgact1 .footer-wrap {
  background: #f5f7fa;
  border-top: 1px solid #dcdfe6;
  width: 100%;
  position: fixed;
  bottom: 0;
}

#dgact1 .footer-wrap .footer-inner {
  width: 960px;
  margin: 0 auto;
  padding: 8px 0
}

#dgact1 .footer-wrap .info-item {
  overflow: hidden
}

#dgact1 .footer-wrap .info-item .lb {
  width: 70px;
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
  font-size: 18px;
  color: #000;
  text-align: right;
  margin-right: 8px;
}

#dgact1 .footer-wrap .info-item textarea {
  width: 100%;
  height: 186px;
  padding: 0 4px;
  background: #fff;
  border-radius: 3px;
  line-height: 24px;
  border: 1px solid #c0c4cc
}

#dgact1 .view-show {
  display: none;
}
#dgact1 .kj-item {
  border-bottom: 1px solid #dcdfe6;
  display: inline-block;
  width: 548px;
  height: 32px;
  vertical-align: top;
}
#dgact1 .dxcl-item {
  width: 727px;
  /* padding: 3px 8px 3px 12px; */
  border: 1px solid #DCDFE6;
  display: inline-block;
  margin-bottom: 5px;
}
#dgact1 .left-cont {
  width: 128px;
  border-right: 1px solid #DCDFE6;
  background: #F5F7FA;
  display: inline-block;
}
#dgact1 .right-cont {
  /* width: calc(100% - 135px); */
  background: #EBEEF5;
  display: inline-block;
}
#dgact1 .bracket {
  display: inline-block;
  vertical-align: top;
}
#dgact1 .view-none1 {
  display: none;
}
#dgact1 .yfz-view {
  display: none;
}
#dgact1 .md-cont {
  border: 1px solid #DCDFE6;
  display: inline-block;
  width: 630px;
  vertical-align: text-top;
}
.head-right span{
  opacity: 0;
}

[isview="true"] #dgact1 .view-show {
  display: block;
}
[isview="true"] #dgact1 .kj-item {
  display: none;
}
[isview="true"] #dgact1 .yfz-show,
[isview="true"] #dgact1 .zz-show,
[isview="true"] #dgact1 .zfg-view,
[isview="true"] #dgact1 .gzt-view {
  display: none;
}
[isview="true"] #dgact1 .yfz-view,
[isview="true"] #dgact1 .zz-view {
  display: inline-block;
}

[isview="true"] #dgact1 .view-show.inline {
  display: inline-block;
}

[isview="true"] #dgact1 .view-none {
  display: none;
}

[isview="true"] #dgact1 .sel-light {
  color: unset;
  background: unset;
}

[isview="true"] #dgact1 .edit-btn {
  display: none;
}

[isview="true"] #dgact1 .visit-status {
  color: unset;
}

[isview="true"] #dgact1 .bz-item {
  display: none;
}

[isview="true"] #dgact1 .zz-block {
  display: block !important;
}

[isview="true"] #dgact1 .cont,
[isview="true"] #dgact1 .md-cont,
[isview="true"] #dgact1 .left-cont,
[isview="true"] #dgact1 .right-cont {
  background: unset !important;
  border: unset;
  padding: unset !important;
  width: unset !important;
  height: unset !important;
}
[isview="true"] #dgact1 .md-cont{
  line-height: 20px;
}
[isview="true"] #dgact1 .cont{
  margin-bottom: 0 !important;
}
[isview="true"] #dgact1 .left-cont{
  line-height: 20px;
}
[isview="true"] #dgact1 .mb-8 {
  margin: 0 !important;
}
[isview="true"] #dgact1 .body-wrap .g-item {
  padding: 3px 12px !important;
  border-bottom: 1px solid #dcdfe6 !important;
}

[isview="true"] #dgact1 .body-wrap .g-item.view-no {
  padding: 3px 12px;
  margin-left: 118px;
  min-height: 36px;
  line-height: 28px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  overflow: hidden
}

[isview="true"] #dgact1 .lb-wid {
  display: inline;
}

[isview="true"] #dgact1 .view-nomg {
  margin: 0 !important;
}

[isview="true"] #dgact1 .mjm-view,
[isview="true"] #dgact1 .gjm-view,
[isview="true"] #dgact1 .xqjm-view{
  display: inline-block !important;
  width: 827px;
}
[isview="true"] #dgact1 .gdm-view{
  display: block !important;
}
[isview="true"] #dgact1 .yfzdxcl-view .left-cont,
[isview="true"] #dgact1 .zzdxcl-view .left-cont{
  line-height: 30px !important;
}

[isview="true"] #dgact1 .gjm-view,
[isview="true"] #dgact1 .xqjm-view{
  margin-left:  0;
}
[isview="true"]  #dgact1 .pl-12{
  padding: 0 !important;
}
[isview="true"] .right-cont{
  line-height: 20px !important;
  margin-left: 8px;
}
[isview="true"] #dgact1 .ml-28 {
  margin-left: 0 !important;
}