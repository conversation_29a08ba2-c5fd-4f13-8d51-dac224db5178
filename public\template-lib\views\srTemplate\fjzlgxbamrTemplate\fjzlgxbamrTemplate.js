$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var isFillInStatus1 = {}, isFillInStatus2 = {}, isFillInStatus3 = {};
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过
var raidoArr1 = [], radioArr2 = [], textareaIdArr = [];
var radioNameArr = ['mjmzgxs', 'mjmzgas', 'zzhbxs', 'zzhbas', 'zzszxs', 'zzszas', 'znzxs', 'znzas', 'zwzxs', 'zwzas', 'yzxs', 'yzas', 'yqzxs', 'yqzas',
  'yhzxs', 'yhzas', 'pjmxs', 'pjmas', 'cxmxs', 'cxmas', 'gzjmxs', 'gzjmas', 'gjmzxs', 'gjmzas', 'gyjmxs', 'gyjmas', 'zszxs', 'zszas', 'szzxs',
  'szzas', 'yszxs', 'yszas', 'yxgjmxs', 'yxgjmas', 'gsdxs', 'gsdas', 'gpmxs', 'gpmas', 'ghdxs', 'ghdas', 'gxdxs', 'gxdas',]
var gzfd = {
  s1: '尾状叶',
  s2: '左外叶上段',
  s3: '左外叶下段',
  s4: '左内叶',
  s5: '右前叶下段',
  s6: '右后叶下段',
  s7: '右后叶上段',
  s8: '右前叶上段',
}
var mjmxtArr = [
  ['mjmzgbzfj', 'mjmzgsq', 'mjmzgxs', 'mjmzgyxs', 'mjmzgas', 'mjmzgyas'], //门静脉主干
  ['zzhbbzfj', 'zzhbsq', 'zzhbxs', 'zzhbyxs', 'zzhbas', 'zzhbyas'],//左支横部
  ['zzszbzfj', 'zzszsq', 'zzszxs', 'zzszyxs', 'zzszas', 'zzszyas'],//左支矢状部
  ['znzbzfj', 'znzsq', 'znzxs', 'znzyxs', 'znzas', 'znzyas'],//左内支
  ['zwzbzfj', 'zwzsq', 'zwzxs', 'zwzyxs', 'zwzas', 'zwzyas'],//左外支
  ['yzbzfj', 'yzsq', 'yzxs', 'yzyxs', 'yzas', 'yzyas'],//右支
  ['yqzbzfj', 'yqzsq', 'yqzxs', 'yqzyxs', 'yqzas', 'yqzyas'],//右前支
  ['yhzbzfj', 'yhzsq', 'yhzxs', 'yhzyxs', 'yhzas', 'yhzyas'],//右后支
  ['pjmbzfj', 'pjmsq', 'pjmxs', 'pjmyxs', 'pjmas', 'pjmyas'],//脾静脉
  ['cxmbzfj', 'cxmsq', 'cxmxs', 'cxmyxs', 'cxmas', 'cxmyas'],//肠系膜上静脉
]
var gjmArr = [
  ['gzjmbzfj', 'gzjmsq', 'gzjmxs', 'gzjmyxs', 'gzjmas', 'gzjmyas'],//肝左静脉
  ['gjmzbzfj', 'gjmzsq', 'gjmzxs', 'gjmzyxs', 'gjmzas', 'gjmzyas'],//肝中静脉
  ['gyjmbzfj', 'gyjmsq', 'gyjmxs', 'gyjmyxs', 'gyjmas', 'gyjmyas'],//肝右静脉
  ['zszbzfj', 'zszsq', 'zszxs', 'zszyxs', 'zszas', 'zszyas',],//肝左静脉属支
  ['szzbzfj', 'szzsq', 'szzxs', 'szzyxs', 'szzas', 'szzyas',],//肝中静脉属支
  ['yszbzfj', 'yszsq', 'yszxs', 'yszyxs', 'yszas', 'yszyas',],//肝右静脉属支
  ['yxgjmbzfj', 'yxgjmsq', 'yxgjmxs', 'yxgjmyxs', 'yxgjmas', 'yxgjmyas'],//肝右下副肝静脉
]
var xqjmArr = [
  ['gsdbzfj', 'gsdsq', 'gsdxs', 'gsdyxs', 'gsdas', 'gsdyas'],//肝上段
  ['gpmbzfj', 'gpmsq', 'gpmxs', 'gpmyxs', 'gpmas', 'gpmyas'], //第二肝门平面
  ['ghdbzfj', 'ghdsq', 'ghdxs', 'ghdyxs', 'ghdas', 'ghdyas'],//肝后段
  ['gxdbzfj', 'gxdsq', 'gxdxs', 'gxdyxs', 'gxdas', 'gxdyas'],//肝下段
]
var veinNameData = {
  mjmName: ['门静脉主干', '左支横部', '左支矢状部', '左内支', '左外支', '右支', '右前支', '右后支', '脾静脉', '肠系膜上静脉'],
  gjmName: ['肝左静脉', '肝中静脉', '肝右静脉', '肝左静脉属支', '肝中静脉属支', '肝右静脉属支', '右下副肝静脉'],
  xqjmName: ['肝上段', '第二肝门平面', '肝后段', '肝下段'],
}
var xhtsData = [
  {title: '低信号', id: '低信号'},
  {title: '稍低信号', id: '稍低信号'},
  {title: '中等信号', id: '中等信号'},
  {title: '轻度高信号', id: '轻度高信号'},
  {title: '中度高信号', id: '中度高信号'},
  {title: '明显高信号', id: '明显高信号'},
]
var notes = [
  '　　肝S5段见稍长T1稍长T2信号结节/肿块，DWI高信号，ADC低信号，最大横截面积约9.8cm×15.8cm（3:12），增强扫描见不均匀强化；余肝脏信号均匀，平扫及动态增强扫描未见明显异常信号灶。肝静脉及门脉系统显影良好。胆道系统未见明显扩张。',
  '　　脾脏大小、信号未见明显异常。胰腺信号无明显异常，胰管未见扩张。',
  '　　腹膜后未见明显肿大淋巴结。未见明显腹水征。'
]
var descriptionStr = '';
var mjmxtStrList = [], gjmStrList = [], xqjmStrList = [], impressionList = [];
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#fjgxbamr1"),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    initPage();
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView();
    } else {
    }
  }
}
function showGmx(text,show) {
  $('.gxba').css('cssText', 'border-bottom: 1px solid #dcdfe6')
  $('.xzdd').css('cssText', 'border-bottom: 1px solid #dcdfe6')
  $('.xzdd-view').hide()
  $('.gxba-view').hide()
  if(text === '肝细胞癌' || !text){
    $('.gxba').css('cssText', 'border-bottom: none;background: #fff')
    $('.gxba-view').show()
    !isSavedReport && $('#fjgxba-rt-587').val(impressionList.join('\n'))
    $('#fjgxba-rt-148').removeClass('rt-hide')
    if(show){
      $('.xzdd').hide()
      $('.head-right').css('cssText', 'width: calc(100% - 95px)')
    }
  }
  if(text === '性质待定的肝占位'){
    $('.xzdd').css('cssText', 'border-bottom: none;background: #fff')
    $('.xzdd-view').show()
    !isSavedReport && $('#fjgxba-rt-611').val(notes.join('\n'))
    !isSavedReport && $('#fjgxba-rt-587').val('肝S5段占位，考虑')
    if(isSavedReport && idAndDomMap['fjgxba-rt-587']){
      $('#fjgxba-rt-587').val(idAndDomMap['fjgxba-rt-587'].value)
    }
    $('#fjgxba-rt-148').addClass('rt-hide')
    if(show){
      $('.gxba').hide()
      $('.head-right').css('cssText', 'width: calc(100% - 160px)')
      $('.xzdd-view .lb').css('cssText', 'width: 115px')
    }
  }
}
function initView() {
  let bracketArr1 = [], showArr1 = [];
  // bracketArr1.push(initId('132'));
  showGmx($('#fjgxba-rt-610').val(),true);
  bracketArr1.push(initId('507'));
  bracketArr1.push(initId('511'));
  bracketArr1.push(initId('513'));
  bracketArr1.push(initId('515'));
  bracketArr1.push(initId('525'));
  bracketArr1.push(initId('527'));
  bracketArr1.push(initId('529'));
  bracketArr1.push(initId('531'));
  bracketArr1.push(initId('533'));
  bracketArr1.push(initId('535'));
  bracketArr1.push(initId('545'));
  bracketArr1.push(initId('547'));
  bracketArr1.push(initId('555'));
  bracketArr1.push(initId('557'));
  bracketArr1.push(initId('559'));
  bracketArr1.push(initId('561'));
  bracketArr1.push(initId('563'));
  bracketArr1.push(initId('590'));
  bracketArr1.push(initId('593'));
  for (let i = 0; i < bracketArr1.length; i++) {
    let val = $('#' + bracketArr1[i]).val();
    if (val) {
      let rtsc = $('#' + bracketArr1[i]).attr('rt-sc');
      let rtscArr = rtsc.split(';');
      let nameVal = ''
      rtscArr.forEach(item => {
        let [key, value] = item.split(':');
        if (key === 'name') {
          nameVal = value;
        }
      })
      $('#' + bracketArr1[i]).parent().html(`${nameVal}（${val}）`)
    }
  }
  curElem.find('.bracket').each(function (i, dom) {
    let val = $(dom).find('.rt-sr-w').val();
    val ? $(dom).html(`（${val}）`) : ''
  })
  showArr1.push(initId('485'));
  showArr1.push(initId('489'));
  showArr1.push(initId('493'));
  showArr1.push(initId('497'));
  showArr1.forEach(obj => {
    let val = getVal($(`[id="${obj}"]:checked`)) || '';
    val ? $('#' + obj).parent().parent().show() : $('#' + obj).parent().parent().hide()
  })
  $('#fjgxba-rt-586').val() ? '' : $('#fjgxba-rt-586').parent().html('无');
  // console.log('>>>mjmxtStrList', mjmxtStrList);
  let mjmViewStrList = forJmArrFun3(mjmxtStrList, 'mjmName');
  let mmzz = '', mmyz = '', sz = '';
  let jmhbArr = [], line = []
  if (mjmViewStrList[1] || mjmViewStrList[2] || mjmViewStrList[3] || mjmViewStrList[4]) {
    jmhbArr = mjmViewStrList.slice(1, 5);
    jmhbArr = jmhbArr.filter(item => item);
    mmzz = `<span style="font-weight: 600">门脉左支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  if (mjmViewStrList[5] || mjmViewStrList[6] || mjmViewStrList[7]) {
    jmhbArr = mjmViewStrList.slice(5, 8);
    jmhbArr = jmhbArr.filter(item => item);
    mmyz = `<span style="font-weight: 600">门脉右支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  mjmViewStrList[0] ? line.push(mjmViewStrList[0]) : '';
  mmzz ? line.push(mmzz) : '';
  mmyz ? line.push(mmyz) : '';
  mjmViewStrList[8] ? line.push(mjmViewStrList[8]) : '';
  mjmViewStrList[9] ? line.push(mjmViewStrList[9]) : '';
  if (line.length > 0) {
    $('.mjm-view').html(line.map(item => `<div>${item}</div>`))
  }
  line = [];
  //=================================================================================
  // console.log('>>>gjmStrList', gjmStrList);
  let gjmViewStrList = forJmArrFun3(gjmStrList, 'gjmName');
  gjmViewStrList[0] ? line.push(gjmViewStrList[0]) : '';
  gjmViewStrList[1] ? line.push(gjmViewStrList[1]) : '';
  gjmViewStrList[2] ? line.push(gjmViewStrList[2]) : '';
  if (gjmViewStrList[3] || gjmViewStrList[4] || gjmViewStrList[5]) {
    jmhbArr = gjmViewStrList.slice(3, 6);
    jmhbArr = jmhbArr.filter(item => item);
    sz = `<span style="font-weight: 600">属支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  sz ? line.push(sz) : '';
  gjmViewStrList[6] ? line.push(gjmViewStrList[6]) : '';
  if (line.length > 0) {
    $('.gjm-view').html(line.map(item => `<div>${item}</div>`))
  }
  line = [];
  //=================================================================================
  let xqjmViewStrList = forJmArrFun3(xqjmStrList, 'xqjmName');
  // console.log('>>>xqjmStrList', xqjmStrList);
  if(xqjmViewStrList.length > 0){
    $('.xqjm-view').html(xqjmViewStrList.map(item => `<div>${item}</div>`))
  }
  var zzVal = getVal('[id="fjgxba-rt-17"]:checked') || '';
  if(!zzVal) {
    $('.zdShow').hide()
    $('.zz-block').css('cssText', 'display: none !important');
  }
  var msval = $('#fjgxba-rt-505').val() || '';
  if(msval){
    $('.view-none1').show()
  }
  var fmqdzh = getVal($('input[type=checkbox][name=fmqdzh]:checked')) || ''; //增厚位置
  if(!fmqdzh) {
    $('.qdzh-show').hide()
  }
  var ygqk = $('#fjgxba-rt-564').val() || '';
  if(!ygqk){
    $('.ygqk-show').hide()
  }
  var yfzData = ['fjgxba-rt-20','fjgxba-rt-21','fjgxba-rt-22','fjgxba-rt-23','fjgxba-rt-24','fjgxba-rt-25','fjgxba-rt-26','fjgxba-rt-27']
  var yfzHtml = []
  yfzData.forEach(item => {
    if(idAndDomMap[item].value){
      yfzHtml.push(idAndDomMap[item].value)
    }
  })
  $('.yfz-view').html(yfzHtml.join(',')+'段')
  var zzData = ['fjgxba-rt-75','fjgxba-rt-76','fjgxba-rt-77','fjgxba-rt-78','fjgxba-rt-79','fjgxba-rt-80','fjgxba-rt-81','fjgxba-rt-82']
  var zzHtml = []
  zzData.forEach(item => {
    if(idAndDomMap[item].value){
      zzHtml.push(idAndDomMap[item].value)
    }
  })
  $('.zz-view').html(zzHtml.join(',')+'段')
}

function forJmArrFun3(list, type) {
  let propName = ['与病灶分界', '受侵', '血栓', '', '癌栓', ''];
  let veinNames = [];
  veinNames = veinNameData[type];
  var jmarrData = {
    mjmName: [[], [], [], [], [], [], [], [], [], []],
    gjmName: [[], [], [], [], [], [], []],
    xqjmName: [[], [], [], [],],
  }
  let tempResults = jmarrData[type];
  for (let i = 0; i < list.length; i++) {
    for (let j = 0; j < propName.length; j++) {
      let val = list[i][j] || '';
      let prop = propName[j];
      if (val && prop !== '') {
        tempResults[i].push(prop + '：' + val);
      }
      if (j === 3 && val) {
        tempResults[i][j - 1] = tempResults[i][j - 1] + '（可见' + val + '充盈缺损影，增强未见强化）';
      }
      if (j === 5 && val ) {
        tempResults[i][j - 1] = tempResults[i][4 - 1] + '（可见' + val + '充盈缺损影，增强可见强化）';
      }
    }
  }
  let strList = [];
  for (let t = 0; t < tempResults.length; t++) {
    if (tempResults[t].length > 0) {
      if (type === 'mjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        t === 0 || t === 8 || t === 9 ?
          strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`) :
          strList.push(`${veinNameData[type][t]}（${tempResults[t].join('；')}）`)
      }
      if (type === 'gjmName') {
        t !== 3 && t !== 4 && t !== 5 ?
          strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`) :
          strList.push(`${veinNameData[type][t]}（${tempResults[t].join('；')}）`)
      }
      if (type === 'xqjmName') {
        strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`)
      }

    } else {
      strList.push('')
    }
  }
  // console.log('>>>strList', strList);
  return strList;
}

// 初始化数据
function initPage() {
  var inp1Arr = [], inp2Arr = [], inp3Arr = [];
  inp1Arr.push(initId('149'))
  inp1Arr.push(initId('150'))
  inp1Arr.push(initId('151'))
  inp1Arr.push(initId('152'))
  inp1Arr.push(initId('153'))
  inp1Arr.push(initId('301'))
  inp1Arr.push(initId('302'))
  inp1Arr.push(initId('303'))
  inp1Arr.push(initId('304'))
  inp1Arr.push(initId('305'))
  inp1Arr.push(initId('407'))
  inp1Arr.push(initId('408'))
  inp1Arr.push(initId('409'))
  inp1Arr.push(initId('410'))
  inp2Arr.push(initId('16'))
  inp2Arr.push(initId('17'))
  inp3Arr.push(initId('168'))
  inp3Arr.push(initId('169'))
  inp3Arr.push(initId('170'))
  inp3Arr.push(initId('171'))
  inp3Arr.push(initId('228'))
  inp3Arr.push(initId('229'))
  inp3Arr.push(initId('230'))
  inp3Arr.push(initId('348'))
  inp3Arr.push(initId('349'))
  inp3Arr.push(initId('350'))
  raidoArr1.push(initId('129'))
  raidoArr1.push(initId('130'))
  raidoArr1.push(initId('131'))
  radioArr2.push(initId('45'))
  radioArr2.push(initId('100'))
  radioArr2.push(initId('468'))
  radioArr2.push(initId('484'))
  radioArr2.push(initId('517'))
  radioArr2.push(initId('537'))
  radioArr2.push(initId('549'))
  textareaIdArr.push(initId('510'))
  textareaIdArr.push(initId('512'))
  textareaIdArr.push(initId('514'))
  textareaIdArr.push(initId('524'))
  textareaIdArr.push(initId('526'))
  textareaIdArr.push(initId('528'))
  textareaIdArr.push(initId('530'))
  textareaIdArr.push(initId('532'))
  textareaIdArr.push(initId('534'))
  textareaIdArr.push(initId('544'))
  textareaIdArr.push(initId('546'))
  textareaIdArr.push(initId('554'))
  textareaIdArr.push(initId('556'))
  textareaIdArr.push(initId('558'))
  textareaIdArr.push(initId('560'))
  textareaIdArr.push(initId('562'))
  textareaIdArr.push(initId('589'))
  textareaIdArr.push(initId('592'))

  // if (!isSavedReport) {
  //   inp1Arr.forEach(item => {
  //     isFillInStatus1[item] = '1'
  //   })
  // }
  inp1Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus1[item] = '1' : '';
  })

  inp2Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus2[item] = '1' : '';
  })

  inp3Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus3[item] = '1' : '';
  })

  radioArr2.forEach(item => {
    $('#' + item).is(':checked') ? $('#' + item + '-cont').show() : $('#' + item + '-cont').hide();
  })

  // $('.g-item').find('.block-ck1')
  showGmx($('#fjgxba-rt-610').val());
  changeBzSmText();
  showGyh();
  showRadioInfo('init');
  showTextarea('init');
  initClickFun();
  getTipText();
  curElem.find('#fjgxbamr1 .body-wrap .rt-sr-w').change(function () {
    getTipText();
  })
  let xhts = ['fjgxba-rt-594','fjgxba-rt-595','fjgxba-rt-596','fjgxba-rt-601','fjgxba-rt-602','fjgxba-rt-603']
  xhts.forEach(e => {
    initInpAndSel(e, xhtsData);
  })
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal) {
  let selLen = 200;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    className: 'laySelLab',
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: `width: ${selLen}px;`
  });
}
function getTipText() {
  console.log('444');
  
  let str = '', str2 = '', str3 = '';
  let impressionArr = [], impressionArr1 = [], impressionArr2 = [];
  // 原发灶
  var yfzVal = getVal('[id="fjgxba-rt-16"]:checked') || '';
  var yfbzsm = getVal($('input[type=radio][name=bzsm]:checked')) || '';//数目
  var yfzwz = getVal($('input[type=checkbox][name=yfzwz]:checked')) || '';//位置
  var yfzdx1 = $('#fjgxba-rt-28').val();//大小
  var yfzdx2 = $('#fjgxba-rt-29').val();//大小
  var yfzdx3 = $('#fjgxba-rt-615').val();//大小
  var yfzdx4 = $('#fjgxba-rt-616').val();//大小
  var yfzxz = getVal($('input[type=radio][name=yfzxz]:checked')) || '';//形状
  var yfzfx = getVal($('input[type=radio][name=yfzfx]:checked')) || '';//分型
  var yfzbj = getVal($('input[type=radio][name=yfzbj]:checked')) || '';//边界
  var yfT1WI = $('#fjgxba-rt-594').val();//T1WI
  var yfT2WI = $('#fjgxba-rt-595').val();//T2WI
  var yfDWI = $('#fjgxba-rt-596').val();//DWI
  var yfzT1 = getVal($('input[type=radio][name=yfzT1]:checked')) || '';//T1同反相位序列存在脂肪变性
  var yfzR2zsg = getVal($('input[type=radio][name=yfzR2zsg]:checked')) || '';//R2*值升高
  var yfzpsmd = getVal($('input[type=radio][name=yfzpsmd]:checked')) || '';//平扫密度
  var yfzpsmd2 = getVal($('input[type=radio][name=yfzpsmd2]:checked')) || '';//平扫密度是否均匀
  var yfzpsmdbj = getVal($('input[type=radio][name=yfzpsmdbj]:checked')) || '';//平扫密度不均匀
  var yfzbjyqt = $('#fjgxba-rt-49').val();//平扫密度不均匀其他填写
  var yfzzqdmwq = getVal($('input[type=radio][name=yfzzqdmwq]:checked')) || '';//动脉晚期
  var yfzzqmjmq = getVal($('input[type=radio][name=yfzzqmjmq]:checked')) || '';//门静脉期
  var yfzzqycq = getVal($('input[type=radio][name=yfzzqycq]:checked')) || '';//延迟期
  var yfzzqxs = getVal($('input[type=radio][name=yfzzqxs]:checked')) || '';//增强强化形式
  var yfzzqxsqt = $('#fjgxba-rt-69').val();//增强形式其他填写
  var yfzbmz = getVal($('input[type=radio][name=yfzbmz]:checked')) || '';//包膜征
  var yfzbmzInpVal = $('#fjgxba-rt-72').val();//包膜征有填写
  var yfgdqbxVal = $('#fjgxba-rt-608').val();//肝胆期表现填写


  // 子灶
  var txfs = getVal($('input[type=radio][name=txfs]:checked')) || '';//填写方式
  var zzVal = getVal('[id="fjgxba-rt-17"]:checked') || '';
  var zzsm = getVal($('input[type=radio][name=zzsm]:checked')) || '';//数目
  var zzwz = getVal($('input[type=checkbox][name=zzwz]:checked')) || '';//位置
  var zzdx1 = $('#fjgxba-rt-83').val();//大小
  var zzdx2 = $('#fjgxba-rt-84').val();//大小
  var zzdx3 = $('#fjgxba-rt-617').val();//大小
  var zzdx4 = $('#fjgxba-rt-618').val();//大小
  var zzxz = getVal($('input[type=radio][name=zzxz]:checked')) || '';//形状
  var zzfx = getVal($('input[type=radio][name=zzfx]:checked')) || '';//分型
  var zzbj = getVal($('input[type=radio][name=zzbj]:checked')) || '';//边界
  var zzT1WI = $('#fjgxba-rt-601').val();//T1WI
  var zzT2WI = $('#fjgxba-rt-602').val();//T2WI
  var zzDWI = $('#fjgxba-rt-603').val();//DWI
  var zzT1 = getVal($('input[type=radio][name=zzT1]:checked')) || '';//T1同反相位序列存在脂肪变性
  var zzR2zsg = getVal($('input[type=radio][name=zzR2zsg]:checked')) || '';//R2*值升高
  var zzpsmd = getVal($('input[type=radio][name=zzpsmd]:checked')) || '';//平扫密度
  var zzpsmd2 = getVal($('input[type=radio][name=zzpsmd2]:checked')) || '';//平扫密度是否均匀
  var zzpsmdbj = getVal($('input[type=radio][name=zzpsmdbj]:checked')) || '';//平扫密度不均匀
  var zzsmdbj = getVal($('input[type=radio][name=zzsmdbj]:checked')) || '';//平扫密度不均匀
  var zzbjyqt = $('#fjgxba-rt-104').val();//平扫密度不均匀其他填写
  var zzzqdmwq = getVal($('input[type=radio][name=zzzqdmwq]:checked')) || '';//动脉晚期
  var zzzqmjmq = getVal($('input[type=radio][name=zzzqmjmq]:checked')) || '';//门静脉期
  var zzzqycq = getVal($('input[type=radio][name=zzzqycq]:checked')) || '';//延迟期
  var zzzqxs = getVal($('input[type=radio][name=zzzqxs]:checked')) || '';//增强强化形式
  var zzzqxsqt = $('#fjgxba-rt-124').val();//增强形式其他填写
  var zzbmz = getVal($('input[type=radio][name=zzbmz]:checked')) || '';//包膜征
  var zzbmzInpVal = $('#fjgxba-rt-127').val();//包膜征有填写
  var zzgdqbxVal = $('#fjgxba-rt-609').val();//肝胆期表现填写

  //肝背景
  var gyhVal = getVal($('input[type=radio][name=gyh]:checked')) || '';
  var gyhInpVal = $('#fjgxba-rt-132').val() || ''; //肝硬化
  var zfgVal = getVal($('input[type=radio][name=zfg]:checked')) || ''; //脂肪肝
  var zfgqt = $('#fjgxba-rt-590').val();
  var gztgzVal = getVal($('input[type=radio][name= gztgz]:checked')) || ''; //肝脏铁过载
  var tgzqt = $('#fjgxba-rt-593').val();

  //门脉高压征象
  var jmqzVal = getVal($('input[type=checkbox][name=jmqz]:checked')) || '';
  var jmqzInpVal = $('#fjgxba-rt-136').val() || ''; //门脉高压静脉曲张
  var fqjmkfVal = getVal($('input[type=radio][name=fqjmkf]:checked')) || '';
  var fqjmkfInpVal = $('#fjgxba-rt-139').val() || ''; //附脐静脉开放
  var wsflVal = getVal($('input[type=radio][name=wsfl]:checked')) || '';
  var wsflInpVal = $('#fjgxba-rt-142').val() || ''; //胃-肾分流
  var psflVal = getVal($('input[type=radio][name=psfl]:checked')) || '';
  var psflInpVal = $('#fjgxba-rt-145').val() || ''; //脾-肾分流
  var mmgyqtInpVal = $('#fjgxba-rt-147').val() || ''; //门脉高压征象其他

  // 门静脉系统
  var mjmxtCheckbox = getVal($('input[type=checkbox][name=mjmxt]:checked')) || '';
  if (mjmxtCheckbox) {
    // for(let a = 0; a < mjmxtArr.length; a++){
    //   let arr = [];
    //   arr =  mjmxtArr[a].map(item => getVal($(`input[type=radio][name=${item}]:checked`)));
    //   mjmxtStrList.push(arr)
    // }
    mjmxtStrList = forJmArrFun(mjmxtArr);
  }
  // 肝静脉
  var gjmCheckbox = getVal($('input[type=checkbox][name=gjm]:checked')) || '';
  if (gjmCheckbox) {
    gjmStrList = forJmArrFun(gjmArr)
  }
  // 下腔静脉
  var xqjmCheckbox = getVal($('input[type=checkbox][name=xqjm]:checked')) || '';
  if (xqjmCheckbox) {
    xqjmStrList = forJmArrFun(xqjmArr)
  }

  //胆管
  var dgVal = getVal($('input[type=radio][name=dg]:checked')) || '';
  var dgsqVal = getVal($('input[type=radio][name=dgsq]:checked')) || ''; //胆管受侵val
  var dgsqkj = getVal($('input[type=checkbox][name=dgsqkj]:checked')) || '';
  var dgkzVal = getVal($('input[type=radio][name=dgkz]:checked')) || '';
  var dgkzInpVal = $('#fjgxba-rt-479').val() || '';//胆管扩张最宽val
  var dskzVal = getVal($('input[type=radio][name=dskz]:checked')) || '';
  var dskzInpVal = $('#fjgxba-rt-482').val() || '';//胆栓形成val

  //淋巴结
  var lbjVal = getVal($('input[type=radio][name=lbj]:checked')) || '';
  var xgjVal = getVal('[id="fjgxba-rt-485"]:checked') || '';//心膈角val
  var xgjsl = $("#fjgxba-rt-486").val();
  var xgjdj = $("#fjgxba-rt-487").val();
  var xgjSer = $("#fjgxba-rt-620").val();
  var xgjImage = $("#fjgxba-rt-621").val();
  var xgjzq = $("#fjgxba-rt-488").val();
  var gmbVal = getVal('[id="fjgxba-rt-489"]:checked') || '';//肝门部
  var gmbsl = $("#fjgxba-rt-490").val();
  var gmbdj = $("#fjgxba-rt-491").val();
  var gmbSer = $("#fjgxba-rt-622").val();
  var gmbImage = $("#fjgxba-rt-623").val();
  var gmbzq = $("#fjgxba-rt-492").val();
  var fmhVal = getVal('[id="fjgxba-rt-493"]:checked') || '';//腹膜后
  var fmhsl = $("#fjgxba-rt-494").val();
  var fmhdj = $("#fjgxba-rt-495").val();
  var fmhSer = $("#fjgxba-rt-624").val();
  var fmhImage = $("#fjgxba-rt-625").val();
  var fmhzq = $("#fjgxba-rt-496").val();
  var lbjqtVal = getVal('[id="fjgxba-rt-497"]:checked') || '';//其他
  var lbjqtInpVal = $('#fjgxba-rt-498').val() || '';//淋巴结其他val
  var lbjqtsl = $('#fjgxba-rt-499').val();
  var lbjqtdj = $('#fjgxba-rt-500').val();
  var qtSer = $('#fjgxba-rt-626').val();
  var qtImage = $('#fjgxba-rt-627').val();
  var qtzq = $('#fjgxba-rt-619').val();

  //远处转移
  var yczyfb = getVal('[id="fjgxba-rt-501"]:checked') || ''; //远处转移肺部
  var fbwz = getVal($('input[type=radio][name=fb]:checked')) || ''; //远处转移肺部位置
  var fbwzInpVal = $('#fjgxba-rt-505').val() || '';
  var yczygt = getVal('[id="fjgxba-rt-506"]:checked') || ''; //远处转移骨
  var yczygtInpVal = $('#fjgxba-rt-507').val() || '';
  var yczyqt = getVal('[id="fjgxba-rt-508"]:checked') || ''; //远处其他
  var yczyqtInpVal = $('#fjgxba-rt-509').val() || '';//远处转移其他val

  //肝良性占位
  var gnzVal = getVal('[id="fjgxba-rt-510"]:checked') || ''; //肝囊肿val
  var gnzInpVal = $('#fjgxba-rt-511').val() || '';
  var gxglVal = getVal('[id="fjgxba-rt-512"]:checked') || ''; //肝血管瘤val
  var gxglInpVal = $('#fjgxba-rt-513').val() || '';
  var glxqtVal = getVal('[id="fjgxba-rt-514"]:checked') || ''; //肝良性占位其他val
  var glxqInpVal = $('#fjgxba-rt-515').val() || '';

  //胆囊
  var dnVal = getVal($('input[type=radio][name=dn]:checked')) || '';
  var dnxt = getVal($('input[type=radio][name=dnxt]:checked')) || '';//形态
  var dnbzh = getVal($('input[type=radio][name=dnbzh]:checked')) || '';
  var dndx1 = $('#fjgxba-rt-522').val() || ''; //大小
  var dndx2 = $('#fjgxba-rt-523').val() || ''; //大小
  var dnjs = getVal('[id="fjgxba-rt-524"]:checked') || ''; //胆囊结石
  var dnjsInpVal = $('#fjgxba-rt-525').val() || '';
  var dznc = getVal('[id="fjgxba-rt-526"]:checked') || ''; //胆汁粘稠
  var dzncInpVal = $('#fjgxba-rt-527').val() || '';
  var xjz = getVal('[id="fjgxba-rt-528"]:checked') || ''; //腺肌症
  var xjzInpVal = $('#fjgxba-rt-529').val() || '';
  var dny = getVal('[id="fjgxba-rt-530"]:checked') || ''; //胆囊炎
  var dnyInpVal = $('#fjgxba-rt-531').val() || '';
  var xr = getVal('[id="fjgxba-rt-532"]:checked') || ''; //息肉/腺瘤
  var xrInpVal = $('#fjgxba-rt-533').val() || '';
  var dnqt = getVal('[id="fjgxba-rt-534"]:checked') || ''; //胆囊其他
  var dnqtInpVal = $('#fjgxba-rt-535').val() || ''; //胆囊其他

  //脾脏
  var pzVal = getVal($('input[type=radio][name=pz]:checked')) || '';
  var pzxt = getVal($('input[type=radio][name=pzxt]:checked')) || '';//形态
  var pzxtqt = $('#fjgxba-rt-541').val() || '';//形态其他
  var pzdx1 = $('#fjgxba-rt-542').val();
  var pzdx2 = $('#fjgxba-rt-543').val();
  var pnz = getVal('[id="fjgxba-rt-544"]:checked') || ''; //脾囊肿
  var pnzInpVal = $('#fjgxba-rt-545').val() || '';
  var pzqt = getVal('[id="fjgxba-rt-546"]:checked') || ''; //脾脏其他
  var pzqtInpVal = $('#fjgxba-rt-547').val() || '';

  // 胰腺
  var yxVal = getVal($('input[type=radio][name=yx]:checked')) || '';
  var yxxt = getVal($('input[type=radio][name=yxxt]:checked')) || '';//形态
  var yxxtqt = $('#fjgxba-rt-553').val() || '';//形态其他
  var yxnz = getVal('[id="fjgxba-rt-554"]:checked') || ''; //胰腺囊肿
  var yxnzInpVal = $('#fjgxba-rt-555').val() || '';
  var ipmn = getVal('[id="fjgxba-rt-556"]:checked') || ''; //ipmn
  var ipmnInpVal = $('#fjgxba-rt-557').val() || '';
  var yxy = getVal('[id="fjgxba-rt-558"]:checked') || ''; //胰腺炎
  var yxyInpVal = $('#fjgxba-rt-559').val() || '';
  var sjnfb = getVal('[id="fjgxba-rt-560"]:checked') || ''; //神经内分泌肿瘤
  var sjnfbInpVal = $('#fjgxba-rt-561').val() || '';
  var yxqt = getVal('[id="fjgxba-rt-562"]:checked') || ''; //脾脏其他
  var yxqtInpVal = $('#fjgxba-rt-563').val() || '';
  var ygqk = $('#fjgxba-rt-564').val() || ''; //胰管情况

  //腹腔
  var fqzfjx = getVal($('input[type=radio][name=fqzfjx]:checked')) || ''; //腹腔脂肪间隙
  var fs = getVal($('input[type=radio][name=fs]:checked')) || ''; //腹水
  var fsyl = getVal($('input[type=radio][name=fsyl]:checked')) || ''; //腹水
  var fsInpVal = getVal('#fjgxba-rt-573') || ''; //腹水详情
  var fqjx = getVal($('input[type=radio][name=fqjx]:checked')) || "";//积血
  var fqjxqt = $('#fjgxba-rt-576').val();//积血其他
  var fmzh = getVal($('input[type=radio][name=fmzh]:checked')) || ''; //增厚
  var fmqdzh = getVal($('input[type=checkbox][name=fmqdzh]:checked')) || ''; //增厚位置
  var fmzy = getVal($('input[type=radio][name=fmzy]:checked')) || ''; //腹膜种植转移
  var fmzyInpVal = $('#fjgxba-rt-585').val();//腹膜种植转移详情

  //其它征象
  var qtzx = $('#fjgxba-rt-586').val();

  // 印象推导
  if($('#fjgxba-rt-610').val() === '肝细胞癌'){
    $('#fjgxba-rt-587').val('');
  }
  yfzVal && yfzwz ? impressionArr1.push(`肝${yfzwz}段原发性${yfzfx}肝癌`) : ''
  fqjx && fqjx === '有' ? impressionArr1.push(`破裂出血并腹腔内积血/血肿形成`) : ''
  zzVal && zzsm ? impressionArr1.push(`肝内可见${zzsm}子灶`) : ''
  let mjmsq = isChecked(mjmxtStrList, 'sq');
  let gjmsq = isChecked(gjmStrList, 'sq');
  let xqjmsq = isChecked(xqjmStrList, 'sq');
  let mjmxs = isChecked(mjmxtStrList, 'xs');
  let gjmxs = isChecked(gjmStrList, 'xs');
  let xqjmxs = isChecked(xqjmStrList, 'xs');
  let mjmas = isChecked(mjmxtStrList, 'as');
  let gjmas = isChecked(gjmStrList, 'as');
  let xqjmas = isChecked(xqjmStrList, 'as');
  let sqArr = [], xsArr = [], asArr = [], sameJms = [];
  mjmsq ? sqArr.push('门静脉') : '';
  gjmsq ? sqArr.push('肝静脉') : '';
  xqjmsq ? sqArr.push('下腔静脉') : '';
  sqArr && sqArr.length > 0 ? impressionArr1.push(`${sqArr.join('、')}受侵`) : '';
  mjmxs ? xsArr.push('门静脉') : ''
  gjmxs ? xsArr.push('肝静脉') : ''
  xqjmxs ? xsArr.push('下腔静脉') : '';
  mjmas ? asArr.push('门静脉') : ''
  gjmas ? asArr.push('肝静脉') : ''
  xqjmas ? asArr.push('下腔静脉') : '';
  sameJms = xsArr.filter(x => asArr.includes(x));
  xsArr = xsArr.filter(x => !sameJms.includes(x));
  asArr = asArr.filter(x => !sameJms.includes(x));
  xsArr && xsArr.length > 0 ? impressionArr1.push(`${xsArr.join('、')}血栓形成`) : '';
  asArr && asArr.length > 0 ? impressionArr1.push(`${asArr.join('、')}癌栓形成`) : '';
  sameJms && sameJms.length ? impressionArr1.push(`${sameJms.join('、')}血栓、癌栓形成`) : '';
  dgsqVal === '可见' ? str += '胆管可见受侵' : ''
  if (dskzVal === '有') {
    str ? str += "、胆栓形成" : '胆管可见胆栓'
  }
  str ? impressionArr1.push(`${str}`) : '';
  str = '';
  let lbjArr = [];
  xgjVal ? lbjArr.push(xgjVal) : '';
  gmbVal ? lbjArr.push(gmbVal) : '';
  fmhVal ? lbjArr.push(fmhVal) : '';
  lbjqtVal ? lbjArr.push(lbjqtInpVal) : '';
  // lbjqtInpVal ? lbjArr.push(lbjqtInpVal) : '';
  lbjArr && lbjArr.length > 0 ? impressionArr1.push(`${lbjArr.join('、')}可见淋巴结转移`) : '';
  let yczyArr = [];
  if (yczyfb) {
    fbwz ? yczyArr.push(fbwz) : yczyArr.push(yczyfb)
    !isSavedReport && $('#fjgxba-rt-505').val('双肺见多发实性结节')
  }
  yczygt ? yczyArr.push(yczygt) : ''
  // yczyqtInpVal ? yczyArr.push(yczyqtInpVal) : ''
  yczyArr && yczyArr.length > 0 ? impressionArr1.push(`远处转移：${yczyArr.join('、')}`) : '';
  fmzy && fmzy === '有' ? impressionArr1.push(`腹膜种植转移`) : '';

  gyhVal && gyhVal !== '无' ? impressionArr2.push(gyhVal) : ''
  console.log('645646',$('#fjgxba-rt-132').val());
  $('.zqgyh-view').hide();
  $('.gyh-view').hide();
  $('.jjxgyh-view').hide();
  gyhVal === '早期肝硬化' ? $('.zqgyh-view').show() : ''
  gyhVal === '肝硬化' ? $('.gyh-view').show() : ''
  gyhVal === '结节性肝硬化' ? $('.jjxgyh-view').show() : ''
  if((!isSavedReport && gyhVal === '早期肝硬化') || (isSavedReport && gyhVal === '早期肝硬化' && $('#fjgxba-rt-132').val() === '')){
    $('#fjgxba-rt-132').val('肝实质内见弥漫性等T1等T2信号小结节，增强扫描未见明显强化')
  }
  if((!isSavedReport && gyhVal === '肝硬化') || (isSavedReport && gyhVal === '肝硬化' && $('#fjgxba-rt-628').val() === '')){
    $('#fjgxba-rt-628').val('肝实质内见弥漫性等/稍短T1等/稍短T2信号小结节，增强扫描未见明显强化')
  }
  if((!isSavedReport && gyhVal === '结节性肝硬化') || (isSavedReport && gyhVal === '结节性肝硬化' && $('#fjgxba-rt-629').val() === '')){
    $('#fjgxba-rt-629').val('肝实质内见弥漫性等/短T1等/短T2信号结节，增强扫描未见明显强化')
  }
  // !isSavedReport && gyhVal === '早期肝硬化' ? $('#fjgxba-rt-132').val('肝实质内见弥漫性等T1等T2信号小结节，增强扫描未见明显强化') : ''
  // !isSavedReport && gyhVal === '肝硬化' ? $('#fjgxba-rt-132').val('肝实质内见弥漫性等/稍短T1等/稍短T2信号小结节，增强扫描未见明显强化') : ''
  // !isSavedReport && gyhVal === '结节性肝硬化' ? $('#fjgxba-rt-132').val('肝实质内见弥漫性等/短T1等/短T2信号结节，增强扫描未见明显强化') : ''
  zfgVal && zfgVal !== '无' ? str += "脂肪肝" : ''
  console.log('idAndDomMap',idAndDomMap['fjgxba-rt-590'].value);
  
  if((!isSavedReport && zfgVal === '有') || (isSavedReport && zfgVal === '有' && $('#fjgxba-rt-590').val() === '')){
    $('#fjgxba-rt-590').val('反相位肝实质信号较正相位减低')
  }
  // !isSavedReport && zfgVal === '有' ? $('#fjgxba-rt-590').val('反相位肝实质信号较正相位减低') : ''
  if (gztgzVal === '有') {
    str ? str += "并铁过载" : str += '铁过载'
  }
  if((!isSavedReport && gztgzVal === '有') || (isSavedReport && gztgzVal === '有' && $('#fjgxba-rt-593').val() === '')){
    $('#fjgxba-rt-593').val('T2WI-fs肝实质信号弥漫性减低，反相位信号较正相位增高')
  }
  // !isSavedReport && gztgzVal === '有' ? $('#fjgxba-rt-593').val('T2WI-fs肝实质信号弥漫性减低，反相位信号较正相位增高') : ''
  str ? impressionArr2.push(str) : ''
  str = '';
  let mmgyArr = []
  jmqzVal ? mmgyArr.push(jmqzVal + '曲张') : ''
  fqjmkfVal === '是' ? mmgyArr.push('附脐静脉开放') : ''
  if((!isSavedReport && fqjmkfVal === '是') || (isSavedReport && fqjmkfVal === '是' && $('#fjgxba-rt-139').val() === '')){
    $('#fjgxba-rt-139').val('附脐静脉见开放')
  }
  if((!isSavedReport && wsflVal === '是') || (isSavedReport && wsflVal === '是' && $('#fjgxba-rt-142').val() === '')){
    $('#fjgxba-rt-142').val('胃底与左肾静脉间见迂曲扩张血管')
  }
  if((!isSavedReport && psflVal === '是') || (isSavedReport && psflVal === '是' && $('#fjgxba-rt-145').val() === '')){
    $('#fjgxba-rt-145').val('脾门与左肾静脉间见迂曲扩张血管')
  }
  // !isSavedReport && fqjmkfVal === '是' ? $('#fjgxba-rt-139').val('附脐静脉见开放') : ''
  wsflVal === '是' ? mmgyArr.push('胃-肾分流') : ''
  // !isSavedReport && wsflVal === '是' ? $('#fjgxba-rt-142').val('胃底与左肾静脉间见迂曲扩张血管') : ''
  psflVal === '是' ? mmgyArr.push('脾-肾分流') : ''
  // !isSavedReport && psflVal === '是' ? $('#fjgxba-rt-145').val('脾门与左肾静脉间见迂曲扩张血管') : ''
  // mmgyArr && mmgyArr.length > 0 ? impressionArr2.push(mmgyArr.join('，')) : ''

  fsyl ? str += `${fsyl}腹水` : '';
  if (fqzfjx && fqzfjx !== '清晰' || fmzh === '轻度增厚') {
    str ? str += "，腹膜炎" : str = '腹膜炎'
  }
  mmgyArr.length > 0 ? impressionArr2.push(`门脉高压：${mmgyArr.join('，')}${str ? `，${str}` : ''}`) : ''
  impressionArr1.length > 0 ? impressionArr.push(impressionArr1.join('，')) : ''
  impressionArr2.length > 0 ? impressionArr.push(impressionArr2.join('，')) : ''
  mmgyArr.length === 0 && str ? impressionArr.push(str) : ''
  str = '';
  let glxzwArr = [];
  gnzVal ? glxzwArr.push(gnzVal) : ''
  console.log('111',$('#fjgxba-rt-511').val());
  if((!isSavedReport && gnzVal) || (isSavedReport && gnzVal && $('#fjgxba-rt-511').val() === '')){
    $('#fjgxba-rt-511').val('肝内见多发类圆形无强化长T2信号，大者直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)')
  }
  gxglVal ? glxzwArr.push(gxglVal) : ''
  if((!isSavedReport && gxglVal) || (isSavedReport && gxglVal && $('#fjgxba-rt-513').val() === '')){
    $('#fjgxba-rt-513').val('肝内见一结节状长T1长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)，增强扫描边缘见结节样强化，延迟期对比剂向心性填充')
  }
  // !isSavedReport && gxglVal ? $('#fjgxba-rt-513').val('肝内见一结节状长T1长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)，增强扫描边缘见结节样强化，延迟期对比剂向心性填充') : ''
  glxzwArr.length > 0 ? impressionArr.push(glxzwArr.join('、')) : ''
  let dnArr = [];
  dnjs ? dnArr.push(dnjs) : ''
  if((!isSavedReport && dnjs) || (isSavedReport && dnjs && $('#fjgxba-rt-525').val() === '')){
    $('#fjgxba-rt-525').val('胆囊腔内见短T2信号结节')
  }
  if((!isSavedReport && dznc) || (isSavedReport && dznc && $('#fjgxba-rt-527').val() === '')){
    $('#fjgxba-rt-527').val('胆囊内T1WI呈高信号')
  }
  if((!isSavedReport && xjz) || (isSavedReport && xjz && $('#fjgxba-rt-529').val() === '')){
    $('#fjgxba-rt-529').val('胆囊底壁增厚并见多发小囊状长T2信号，增强不均匀强化')
  }
  if((!isSavedReport && dny) || (isSavedReport && dny && $('#fjgxba-rt-531').val() === '')){
    $('#fjgxba-rt-531').val('胆囊壁水肿、增厚，周围见局限性积液')
  }
  if((!isSavedReport && xr) || (isSavedReport && xr && $('#fjgxba-rt-533').val() === '')){
    $('#fjgxba-rt-533').val('胆囊壁见T2WI-fs低信号小结节，增强明显强化，直径约0.7cm')
  }
  // !isSavedReport && dnjs ? $('#fjgxba-rt-525').val('胆囊腔内见短T2信号结节') : ''
  dznc ? dnArr.push(dznc) : ''
  // !isSavedReport && dznc ? $('#fjgxba-rt-527').val('胆囊内T1WI呈高信号') : ''
  xjz ? dnArr.push(xjz) : ''
  // !isSavedReport && xjz ? $('#fjgxba-rt-529').val('胆囊底壁增厚并见多发小囊状长T2信号，增强不均匀强化') : ''
  dny ? dnArr.push(dny) : ''
  // !isSavedReport && dny ? $('#fjgxba-rt-531').val('胆囊壁水肿、增厚，周围见局限性积液') : ''
  xr ? dnArr.push(xr) : ''
  // !isSavedReport && xr ? $('#fjgxba-rt-533').val('胆囊壁见T2WI-fs低信号小结节，增强明显强化，直径约0.7cm') : ''
  dnArr.length > 0 ? impressionArr.push(`胆囊${dnArr.join('、')}`) : ''
  pnz ? impressionArr.push(`脾${pnz}`) : ''
  !isSavedReport && pnz ? $('#fjgxba-rt-545').val('脾内见类圆形无强化长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)') : ''
  let yxArr = []
  yxnz ? yxArr.push(yxnz) : '';
  if((!isSavedReport && yxnz) || (isSavedReport && yxnz && $('#fjgxba-rt-555').val() === '')){
    $('#fjgxba-rt-555').val('胰头见一类圆形无强化长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)')
  }
  if((!isSavedReport && ipmn) || (isSavedReport && ipmn && $('#fjgxba-rt-557').val() === '')){
    $('#fjgxba-rt-557').val('胰体部见囊状、管状长T1长T2信号，与胰管相通，近端及远端胰管扩张，增强扫描边缘轻度强化')
  }
  if((!isSavedReport && yxy) || (isSavedReport && yxy && $('#fjgxba-rt-559').val() === '')){
    $('#fjgxba-rt-559').val('胰腺肿大，T2WI信号增高，胰周脂肪间隙模糊伴积液')
  }
  if((!isSavedReport && sjnfb) || (isSavedReport && sjnfb && $('#fjgxba-rt-561').val() === '')){
    $('#fjgxba-rt-561').val('胰尾部见一结节状长T1稍长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)，增强可见明显强化')
  }
  // !isSavedReport && yxnz ? $('#fjgxba-rt-555').val('胰头见一类圆形无强化长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)') : '';
  ipmn ? yxArr.push(ipmn) : '';
  // !isSavedReport && ipmn ? $('#fjgxba-rt-557').val('胰体部见囊状、管状长T1长T2信号，与胰管相通，近端及远端胰管扩张，增强扫描边缘轻度强化') : '';
  yxy ? yxArr.push(yxy) : '';
  // !isSavedReport && yxy ? $('#fjgxba-rt-559').val('胰腺肿大，T2WI信号增高，胰周脂肪间隙模糊伴积液') : '';
  sjnfb ? yxArr.push(sjnfb) : '';
  // !isSavedReport && sjnfb ? $('#fjgxba-rt-561').val('胰尾部见一结节状长T1稍长T2信号，直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)，增强可见明显强化') : '';
  yxArr.length > 0 ? impressionArr.push(`胰腺（${yxArr.join('、')}）`) : ''
  if (impressionArr.length > 0 && $('#fjgxba-rt-610').val() === '肝细胞癌') {
    for (let i = 0; i < impressionArr.length; i++) {
      impressionArr[i] = `${i + 1}.${impressionArr[i]}`
    }
    impressionList = impressionArr
    impressionArr.length > 0 ? $('#fjgxba-rt-587').val(impressionArr.join('\n')) : ''
  }

  // 影像描述推导
  descriptionStr = '';
  let descriptionArr = [], line = [],tionStr1 = [],tionStr2 = [],tionStr3 = [],tionStr4 = [];
  var gztj = getVal($('input[type=radio][name=gztj]:checked')) || ""; //肝脏体积
  var gzxt = getVal($('input[type=radio][name=xt]:checked')) || ""; //形态
  var gybl = getVal($('input[type=radio][name=gybl]:checked')) || ""; //肝叶比例
  var gyblqt = $('#fjgxba-rt-13').val();//肝叶比例其他
  var gl = getVal($('input[type=radio][name=gl]:checked')) || ""; //肝裂
  gztj === '正常' && gzxt === '光滑' ? line.push('肝脏大小、形态未见异常') : line.push(`肝脏整体形态：肝脏体积${gztj}，表面${gzxt}`)
  gyblqt ? str = gyblqt : str = gybl
  gybl === '其他' ? line.push(`肝叶比例${str}`) : ''
  str = '';
  gl === '增宽' ? line.push(`肝裂${gl}`) : '';
  // descriptionArr.push("　　" + line.join('，'));
  tionStr1.push("　　" + line.join('，'));
  line = [];
  //-------------------------------------------------------------------------------
  if (yfzVal) {
    yfbzsm === '单发' ? str = '一' : '';
    yfbzsm === '多发' ? str = '多发' : '';
    yfzwz ? line.push(`肝内${yfzwz}段见${str}${yfzxz}病灶`) : '';
    str = '';
    yfzfx ? line.push(`形态呈${yfzfx}`) : '';
    yfzbj ? line.push(`边界${yfzbj}`) : '';
    yfzdx1 || yfzdx2 ? line.push(`${yfbzsm === '多发' ? '较大者大小约' : '大小约'}（横轴位最大层面测量，${yfzdx1}mmx${yfzdx2}mm${yfzdx3 || yfzdx4 ? `（Ser${yfzdx3},Image${yfzdx4}）`  : ''}）`) : ''
    yfT1WI ? line.push(`T1WI呈${yfT1WI}`) : '';
    yfT2WI ? line.push(`T2WI呈${yfT2WI}`) : '';
    yfDWI ? line.push(`DWI呈${yfDWI}`) : '';
    yfzT1 === '是' ? line.push(`T1同反相位序列存在脂肪变性`) : ''
    yfzR2zsg === '是' ? line.push(`R2*值升高`) : ''
    yfzpsmd ? line.push(`病灶平扫呈${yfzpsmd}`) : '';
    yfzpsmd2 ? str = `密度${yfzpsmd2}` : '';
    if (yfzpsmdbj) {
      str2 = yfzpsmdbj === '其他' ? yfzbjyqt : yfzpsmdbj;
      str += `（${str2}）`;
    }
    str ? line.push(str) : ''
    str = '', str2 = '';
    yfzzqdmwq ? line.push(`增强动脉晚期${yfzzqdmwq}`) : '';
    yfzzqmjmq ? str = `门静脉期${yfzzqmjmq}` : '';
    yfzzqycq ? str2 = `延迟期${yfzzqycq}` : '';
    yfzzqmjmq === yfzzqycq ? str3 = `门静脉期、延迟期${yfzzqmjmq}` : ''
    if (str3) {
      line.push(str3)
    } else {
      str ? line.push(str) : ''
      str2 ? line.push(str2) : ''
    }
    str = '', str2 = '', str3 = '';
    yfzzqxs ? line.push(`呈${yfzzqxs === '其他' ? yfzzqxsqt : yfzzqxs}`) : '';
    if((!isSavedReport && yfzbmz === '有') || (isSavedReport && yfzbmz === '有' && $('#fjgxba-rt-72').val() === '')){
      $('#fjgxba-rt-72').val('可见强化的包膜')
    }
    // !isSavedReport && yfzbmz === '有' ? $('#fjgxba-rt-72').val('可见强化的包膜')  : ''
    yfzbmzInpVal ? line.push(`${yfzbmzInpVal}`) : line.push('未见明显延迟性强化包膜');
    yfgdqbxVal ? line.push(`${yfgdqbxVal}`) : ''
    // descriptionArr.push("　　原发灶：" + line.join('，'));
    tionStr1.push(line.join('，'));
    line = [];
  }
  //-------------------------------------------------------------------------------
  if (zzVal) {
    if(txfs === '手工填写'){
      $('.sbtx-view').show()
      if(!isSavedReport || (isSavedReport && $('#fjgxba-rt-614').val() === '')){
        $('#fjgxba-rt-614').val('肝内另见多发大小不等的结节，大者直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)，信号及强化方式同上述病灶类似。')
      }
      // !isSavedReport && $('#fjgxba-rt-614').val('肝内另见多发大小不等的结节，大者直径约5cm(1:1)/最大横截面积约1cm×2cm(1:1)，信号及强化方式同上述病灶类似。')
    }else {
      $('.jgs-view').show()
    }
    zzsm === '单发' ? str = '一' : '';
    zzsm === '多发' ? str = '多发' : '';
    str || zzxz ? line.push(`肝内可见${str}大小不等的${zzxz}病灶`) : '';
    str = '';
    zzfx ? line.push(`形态呈${zzfx}`) : '';
    zzbj ? line.push(`边界${zzbj}`) : '';
    zzwz ? line.push(`最大位于${zzwz}段`) : '';
    zzdx1 || zzdx2 ? line.push(`${zzsm === '多发' ? '较大者大小约' : '大小约'}${zzdx1}mmx${zzdx2}mm${zzdx3 || zzdx4 ? `（Ser${zzdx3},Image${zzdx4}）`  : ''}`) : ''
    zzT1WI ? line.push(`T1WI呈${zzT1WI}`) : '';
    zzT2WI ? line.push(`T2WI呈${zzT2WI}`) : '';
    zzDWI ? line.push(`DWI呈${zzDWI}`) : '';
    zzT1 === '是' ? line.push(`T1同反相位序列存在脂肪变性`) : ''
    zzR2zsg === '是' ? line.push(`R2*值升高`) : ''
    zzpsmd ? line.push(`病灶平扫呈${zzpsmd}`) : '';
    zzpsmd2 ? str = `密度${zzpsmd2}` : '';
    if (zzsmdbj) {
      str2 = zzsmdbj === '其他' ? zzbjyqt : zzsmdbj;
      str += `（${str2}）`;
    }
    str ? line.push(str) : '';
    str = '', str2 = '';
    if (zzzqdmwq === yfzzqdmwq &&
      zzzqmjmq === yfzzqmjmq &&
      zzzqycq === yfzzqycq &&
      zzzqxs === yfzzqxs
    ) {
      line.push('增强强化形式同上述病灶类似')
    } else {
      zzzqdmwq ? line.push(`增强动脉晚期${zzzqdmwq}`) : '';
      zzzqmjmq ? str = `门静脉期${zzzqmjmq}` : '';
      zzzqycq ? str2 = `延迟期${zzzqycq}` : '';
      zzzqmjmq === zzzqycq ? str3 = `门静脉期、延迟期${yfzzqmjmq}` : ''
      if (str3) {
        line.push(str3)
      } else {
        str ? line.push(str) : ''
        str2 ? line.push(str2) : ''
      }
      str = '', str2 = '', str3 = '';
      zzzqxs ? line.push(`呈${zzzqxs === '其他' ? zzzqxsqt : zzzqxs}`) : '';
    }
    if((!isSavedReport && zzbmz === '有') || (isSavedReport && zzbmz === '有' && $('#fjgxba-rt-127').val() === '')){
      $('#fjgxba-rt-127').val('可见强化的包膜')
    }
    // !isSavedReport && zzbmz === '有' ? $('#fjgxba-rt-127').val('可见强化的包膜')  : ''
    zzbmzInpVal ? line.push(`包膜征${zzbmzInpVal}`) : line.push('未见明显延迟性强化包膜');
    zzgdqbxVal ? line.push(`${zzgdqbxVal}`) : ''
    // descriptionArr.push("　　子灶：" + line.join('，'));
    tionStr1.push(line.join('，'));
    line = [];
  }
  //-------------------------------------------------------------------------------
  gyhInpVal ? line.push(gyhInpVal) : ""
  zfgqt ? line.push(zfgqt) : ""
  tgzqt ? line.push(tgzqt) : ""
  // line.length > 0 ? descriptionArr.push("　　肝背景：" + line.join('，')) : ''
  line.length > 0 ? tionStr1.push(line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  jmqzInpVal ? line.push(jmqzInpVal) : "";
  fqjmkfInpVal ? line.push(fqjmkfInpVal) : "";
  wsflInpVal ? line.push(wsflInpVal) : "";
  psflInpVal ? line.push(psflInpVal) : "";
  mmgyqtInpVal ? line.push(mmgyqtInpVal) : "";
  // line.length > 0 ? descriptionArr.push("　　门脉高压：" + line.join('，')) : ''
  line.length > 0 ? tionStr1.push(line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  var mjmzgkd = $('#fjgxba-rt-148').val();
  let mjStr = '';
  mjStr = forJmArrFun2(mjmxtStrList, 'mjmName') || ''
  let gjmStr = '';
  gjmStr = forJmArrFun2(gjmStrList, 'gjmName') || ''
  let xqjmStr = "";
  xqjmStr = forJmArrFun2(xqjmStrList, 'xqjmName') || ''
  // console.log('gjmArr2', gjmStr);
  // console.log('xqjmArr2', xqjmStr);
  mjmzgkd ? mjStr = `门脉主干宽约${mjmzgkd}mm。肝内` + mjStr : ''
  if(mjStr.slice(-7) === '尚清，未见受侵'){
    mjStr += '，未见充盈缺损影，增强未见强化'
  }
  if(gjmStr.slice(-7) === '尚清，未见受侵'){
    gjmStr += '，未见充盈缺损影，增强未见强化'
  }
  if(xqjmStr.slice(-7) === '尚清，未见受侵'){
    xqjmStr += '，未见充盈缺损影，增强未见强化'
  }
  // mjStr ? descriptionArr.push("　　门静脉系统：" + mjStr) : ''
  mjStr ? tionStr1.push(mjStr) : ''
  // gjmStr ? descriptionArr.push("　　肝静脉：" + gjmStr) : ''
  gjmStr ? tionStr1.push(gjmStr) : ''
  // xqjmStr ? descriptionArr.push("　　下腔静脉：" + xqjmStr) : ''
  xqjmStr ? tionStr1.push(xqjmStr) : ''
  //-------------------------------------------------------------------------------
  if (dgVal && dgVal === '无异常') { 
    line.push('未见异常');
  }else {
    dgsqkj ? line.push(`${dgsqkj}可见受侵`) : ''
    dgkzVal === '可见' ? line.push(`肝内胆管扩张`) : ''
    dgkzInpVal ? line.push(`最宽处约${dgkzInpVal}mm`) : ''
    dskzVal === '有' ? str += '胆管可见胆栓形成' : '';
    if((!isSavedReport && dskzVal === '有') || (isSavedReport && dskzVal === '有' && $('#fjgxba-rt-482').val() === '')){
      $('#fjgxba-rt-482').val('局部胆管内见条状软组织影，增强扫描可见强化')
    }
    // !isSavedReport && dskzVal === '有' ? $('#fjgxba-rt-482').val('局部胆管内见条状软组织影，增强扫描可见强化') : '';
    dskzInpVal ? str += '，' + dskzInpVal : ''
    str ? line.push(str) : '';
    str = '';
  }
  // line.length > 0 ? descriptionArr.push("　　胆管：" + line.join('，')) : ''
  line.length > 0 ? tionStr1.push(line.join('，')) : ''
  line = []
  //-------------------------------------------------------------------------------
  if (lbjVal && lbjVal === '无异常') { 
    line.push('未见异常');
  }else {
    xgjVal ? $('.xgj-view').show() : $('.xgj-view').hide()
    xgjVal ? str += xgjVal : '' 
    xgjsl ? str += `，${xgjsl}枚` : ''
    xgjdj ? str += `，最大短径约${xgjdj}mm` : '';
    xgjSer || xgjImage ? str += `（Ser${xgjSer}，Image${xgjImage}）` : '';
    xgjzq ? str += `，增强${xgjzq}` : '';
    str ? line.push(str) : '';
    str = '';
    gmbVal ? $('.gmb-view').show() : $('.gmb-view').hide()
    gmbVal ? str += gmbVal : ''
    gmbsl ? str += `，${gmbsl}枚` : ''
    gmbdj ? str += `，最大短径约${gmbdj}mm` : '';
    gmbSer || gmbImage ? str += `（Ser${gmbSer}，Image${gmbImage}）` : '';
    gmbzq ? str += `，增强${gmbzq}` : '';
    str ? line.push(str) : '';
    str = '';
    fmhVal ? $('.mmh-view').show() : $('.mmh-view').hide()
    fmhVal ? str += fmhVal : ''
    fmhsl ? str += `，${fmhsl}枚` : ''
    fmhdj ? str += `，最大短径约${fmhdj}mm` : '';
    fmhSer || fmhImage ? str += `（Ser${fmhSer}，Image${fmhImage}）` : '';
    fmhzq ? str += `，增强${fmhzq}` : '';
    str ? line.push(str) : '';
    str = '';
    lbjqtVal ? $('.qt-view').show() : $('.qt-view').hide()
    lbjqtInpVal ? str += lbjqtInpVal : ''
    lbjqtsl ? str += `，${lbjqtsl}枚` : ''
    lbjqtdj ? str += `，最大短径约${lbjqtdj}mm` : '';
    qtSer || qtImage ? str += `（Ser${qtSer}，Image${qtImage}）` : '';
    qtzq ? str += `，增强${qtzq}` : '';
    str ? line.push(str) : '';
    str = '';
    var lbjzs = Number(xgjsl || '0') + Number(gmbsl || '0') + Number(fmhsl || '0') + Number(lbjqtsl || '0');
    var timst = line.length > 1 ? '分别位于' : ''
    lbjzs ? line[0] = `${lbjzs}枚可疑淋巴结转移，${timst}${line[0]}` : ''
  }
  // line.length > 0 ? descriptionArr.push("　　淋巴结：" + line.join('；')) : ''
  line.length > 0 ? tionStr1.push(line.join('；')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  fbwz ? str += fbwz : ''
  fbwzInpVal ? str += `（${fbwzInpVal}）` : '';
  str ? line.push(str) : ''
  str = ''
  yczygt ? str += yczygt : '';
  if((!isSavedReport && yczygt) || (isSavedReport && yczygt && $('#fjgxba-rt-507').val() === '')){
    $('#fjgxba-rt-507').val('腰椎可见多发斑片状长T1长T2信号，增强明显强化')
  }
  // !isSavedReport && yczygt ? $('#fjgxba-rt-507').val('腰椎可见多发斑片状长T1长T2信号，增强明显强化') : '';
  yczygtInpVal ? str += `（${yczygtInpVal}）` : '';
  str ? line.push(str) : ''
  str = ''
  yczyqtInpVal ? line.push(yczyqtInpVal) : '';
  // line.length > 0 ? descriptionArr.push("　　远处转移：" + line.join('，')) : ''
  line.length > 0 ? tionStr1.push(line.join('，')) : ''
  let dataSet = tionStr1.join('。');
  console.log('dataSet',dataSet);
  descriptionArr.push(dataSet);
  console.log('descriptionArr1', descriptionArr);
  
  line = [];
  //-------------------------------------------------------------------------------
  gnzInpVal ? line.push(gnzInpVal) : ''
  gxglInpVal ? line.push(gxglInpVal) : ''
  glxqInpVal ? line.push(glxqInpVal) : ''
  // line.length > 0 ? descriptionArr.push("　　肝良性占位：" + line.join('，')) : ''
  line.length > 0 ? tionStr2.push(line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (dnVal && dnVal === '无异常') { 
    line.push('大体形态未见异常，壁无增厚，未见异常密度影');
  } else {
    pzxt && dnxt === '增大' ? line.push(`形态${dnxt}`) : '';
    dnbzh ? line.push(`胆囊壁${dnbzh}`) : '';
    dndx1 || dndx2 ? line.push(`大小约：${dndx1}mmx${dndx2}mm`) : '';
    dnjsInpVal ? line.push(dnjsInpVal) : '';
    dzncInpVal ? line.push(dzncInpVal) : '';
    xjzInpVal ? line.push(xjzInpVal) : '';
    dnyInpVal ? line.push(dnyInpVal) : '';
    xrInpVal ? line.push(xrInpVal) : '';
    dnqtInpVal ? line.push(dnqtInpVal) : '';
  }
  // line.length > 0 ? descriptionArr.push("　　胆囊：" + line.join('，')) : ''
  line.length > 0 ? tionStr2.push(line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (pzVal && pzVal === '无异常') {
    line.push('大体形态未见异常，未见异常密度影');
  } else {
    pzxt && pzxt !== '其他' && pzxt !== '正常' ? line.push(`形态${pzxt}`) : '';
    pzxtqt ? line.push(`形态${pzxtqt}`) : '';
    pzdx1 || pzdx2 ? line.push(`大小约：${pzdx1}mmx${pzdx2}mm`) : '';
    pnzInpVal ? line.push(pnzInpVal) : '';
    pzqtInpVal ? line.push(pzqtInpVal) : '';
  }
  // line.length > 0 ? descriptionArr.push("　　脾脏：" + line.join('，')) : ''
  line.length > 0 ? tionStr2.push(line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (yxVal && yxVal === '无异常') {
    line.push('大体形态未见异常，未见异常密度影');
  } else {
    yxxt && yxxt !== '其他' && pzxt !== '正常' ? line.push(`形态${yxxt}`) : '';
    yxxtqt ? line.push(`形态${yxxtqt}`) : '';
    yxnzInpVal ? line.push(yxnzInpVal) : '';
    ipmnInpVal ? line.push(ipmnInpVal) : '';
    yxyInpVal ? line.push(yxyInpVal) : '';
    sjnfbInpVal ? line.push(sjnfbInpVal) : '';
    yxqtInpVal ? line.push(yxqtInpVal) : '';
    ygqk ? line.push(ygqk) : '';
  }
  // line.length > 0 ? descriptionArr.push("　　胰腺：" + line.join('，')) : ''
  line.length > 0 ? tionStr2.push(line.join('，')) : ''
  descriptionArr.push("　　" + tionStr2.join('。'))
  line = [];
  //-------------------------------------------------------------------------------
  fqzfjx && fqzfjx !== '清晰' ? line.push(`腹腔脂肪间隙${fqzfjx}`) : '';
  // fs === "无" ? line.push(`腹腔内无液体密度影`) : line.push(`腹腔内可见${fsyl || ''}液体密度影`);
  // fs === "无" ? '' : line.push(`腹腔内可见${fsyl || ''}液体密度影`);
  if((!isSavedReport && fs === "有") || (isSavedReport && fs === "有" && $('#fjgxba-rt-573').val() === '')){
    $('#fjgxba-rt-573').val('腹腔积液')
  }
  if((!isSavedReport && fqjx === "有") || (isSavedReport && fqjx === "有" && $('#fjgxba-rt-576').val() === '')){
    $('#fjgxba-rt-576').val('腹腔内见液性异常信号，部分T1WI呈高信号')
  }
  if((!isSavedReport && fmzy === "有") || (isSavedReport && fmzy === "有" && $('#fjgxba-rt-585').val() === '')){
    $('#fjgxba-rt-585').val('腹膜不规则增厚，可见多发强化的结节、斑片影')
  }
  // !isSavedReport && fs === "有" ? $('#fjgxba-rt-573').val('腹腔积液') : '';
  // !isSavedReport && fqjx === '有' ? $('#fjgxba-rt-576').val('腹腔内见液性异常信号，部分T1WI呈高信号') : '';
  // !isSavedReport && fmzy === '有' ? $('#fjgxba-rt-585').val('腹膜不规则增厚，可见多发强化的结节、斑片影') : '';
  fsInpVal ? line.push(`${fsInpVal}`) : ''
  fqjxqt ? line.push(fqjxqt) : ''
  fmqdzh ? line.push(fmqdzh + fmzh) : ''
  fmzyInpVal ? line.push(fmzyInpVal) : ''
  console.log('descriptionArr2', descriptionArr);
  // line.length > 0 ? descriptionArr.push("　　腹腔：" + line.join('，')) : descriptionArr.push("　　腹腔：未见异常")
  line.length > 0 ? tionStr3.push(line.join('，')) : tionStr3.push("未见异常")
  line = [];
  descriptionArr.push("　　" + tionStr3.join('。'))
  console.log('descriptionArr3', descriptionArr);
  //-------------------------------------------------------------------------------
  qtzx ? descriptionArr.push("　　其他征象：" + qtzx) : '';
  console.log('descriptionArr4', descriptionArr);
  descriptionStr = descriptionArr.join('。\n')+'。';
  console.log('>>>descriptionStr', descriptionStr);
}

function forJmArrFun(list) {
  let arr = []
  for (let i = 0; i < list.length; i++) {
    arr.push(list[i].map(item => getVal($(`input[type=radio][name=${item}]:checked`))));
  }
  return arr;
}

//门静脉、 肝静脉、下腔静脉内容返回
function forJmArrFun2(list, type) {
  let propName = ['病灶分界', '受侵', '血栓', '血栓密度', '癌栓', '癌栓密度'];
  let veinNames = [];
  veinNames = veinNameData[type];
  let tempResults = [];
  for (let i = 0; i < propName.length; i++) {
    let prop = propName[i];
    for (let j = 0; j < list.length; j++) {
      let val = list[j][i] || '';
      if (val) {
        tempResults.push({
          veinNames: veinNames[j],
          keyName: prop,
          value: val,
        })
      }
    }
  }
  let fjArr = [], sqArr = [], xs = [], xsmd = [], as = [], asmd = [];
  fjArr[0] = tempResults.filter(item => item.value === '尚清')
  fjArr[1] = tempResults.filter(item => item.value === '不清')
  sqArr[0] = tempResults.filter(item => item.value === '未见')
  sqArr[1] = tempResults.filter(item => item.value === '可见')
  xs[0] = tempResults.filter(item => item.keyName === '血栓' && item.value === '无')
  xs[1] = tempResults.filter(item => item.keyName === '血栓' && item.value === '有')
  xsmd[0] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '低信号')
  xsmd[1] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '等信号')
  xsmd[2] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '稍高信号')
  as[0] = tempResults.filter(item => item.keyName === '癌栓' && item.value === '无')
  as[1] = tempResults.filter(item => item.keyName === '癌栓' && item.value === '有')
  asmd[0] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '低信号')
  asmd[1] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '等信号')
  asmd[2] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '稍高信号')
  let fjStr = jmStrSet(fjArr, 'fjData');
  let sqStr = jmStrSet(sqArr, 'sqData');
  let xsmdStr = jmStrSet(xsmd, 'mdData', '未');
  let asmdStr = jmStrSet(asmd, 'mdData', '可');
  let strArr = [];
  fjStr ? strArr.push(fjStr) : ''
  // sqStr ? strArr.push(sqStr) : ''
  xsmdStr ? strArr.push(xsmdStr) : ''
  asmdStr ? strArr.push(asmdStr) : ''
  let str = '';
  strArr && strArr.length > 0 ? str = strArr.join('，') : ''
  if(sqStr.length > 1){
    var modifiedStr = str.replace(/尚清/g, '尚清，未见受侵');
    modifiedStr = modifiedStr.replace(/不清/g, '不清，可见受侵');
    str = modifiedStr
  }
  return str;
}
//门静脉、 肝静脉、下腔静脉拼接内容
function jmStrSet(list, type, desc) {
  let keyObj = {
    fjData: { 0: '尚清', 1: '不清' },
    sqData: { 0: '未见', 1: '可见' },
    mdData: { 0: '可见低信号', 1: '可见等信号', 2: '可见稍高信号' }
  }
  let str = '';
  type === 'fjData' ? str = '与病灶分界' : ''
  type === 'sqData' ? str = '受侵' : ''
  type === 'mdData' ? str = `充盈缺损影，增强${desc}见强化` : ''
  let arr = [];
  for (let i = 0; i < list.length; i++) {
    if (list[i] && list[i].length > 0) {
      // type === 'fjData' ? arr.push(list[i].map(item => item.veinNames).join('、')+ str + keyObj[type][i]) : '';
      // type === 'sqData' ? arr.push(list[i].map(item => item.veinNames).join('、') + keyObj[type][i] + str) : '';
      // type === 'mdData' ? arr.push(list[i].map(item => item.veinNames).join('、') + '可见' + keyObj[type][i] + str) : '';
      // arr.push(list[i].map(item => item.veinNames).join('、') + (str === '与病灶分界' ? str : '') + keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      if(str === '与病灶分界' || type === 'mdData') {
        arr.push(list[i].map(item => item.veinNames).join('、') + (str === '与病灶分界' ? str : '') + keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      }else {
        arr.push( keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      }
    }
  }
  str = ''
  arr && arr.length > 0 ? str = arr.join('，') : '';
  return str;
}
//门静脉、 肝静脉、下腔静脉勾选值获取
function isChecked(list, type) {
  let check = false;
  for (let i = 0; i < list.length; i++) {
    if (type === 'sq' && list[i].indexOf('可见') !== -1) {
      check = true;
      i = list.length;
    }
    if (type === 'xs' && list[i][2] === '有') {
      check = true;
      i = list.length;
    }
    if (type === 'as' && list[i][4] === '有') {
      check = true;
      i = list.length;
    }
  }
  return check;
}

function initId(num) {
  return 'fjgxba-rt-' + num
}

function showRadioInfo(type, radioName) {
  if (type === 'init') {
    for (let i = 0; i < radioNameArr.length; i++) {
      let name = radioNameArr[i];
      let val = getVal($(`input[type=radio][name=${name}]:checked`));
      let secondLastChar = name.charAt(name.length - 2).toUpperCase();
      let id = name.slice(0, name.length - 2) + secondLastChar + name.charAt(name.length - 1);
      val && val === '有' ? $('#' + id).css('display', 'inline-block') : $('#' + id).hide();
    }
  } else {
    let secondLastChar = radioName.charAt(radioName.length - 2).toUpperCase();
    let val = $("input[type='radio'][name='" + radioName + "']:checked").val();
    let id = radioName.slice(0, radioName.length - 2) + secondLastChar + radioName.charAt(radioName.length - 1);
    val && val === '有' ? $('#' + id).css('display', 'inline-block') : $('#' + id).hide();
  }

}

function showTextarea(type, id) {
  if (type === 'init') {
    for (let i = 0; i < textareaIdArr.length; i++) {
      let changeId = Number(textareaIdArr[i].slice(-3));
      changeId = changeId + 1
      $('#' + textareaIdArr[i]).is(':checked') ? $('#fjgxba-rt-' + changeId).show() : $('#fjgxba-rt-' + changeId).hide();
    }
  } else {
    let changeId = Number(id.slice(-3));
    changeId = changeId + 1
    $('#' + id).is(':checked') ? $('#fjgxba-rt-' + changeId).show() : $('#fjgxba-rt-' + changeId).hide();
  }
}

function changeBzSmText() {
  let val1 = $('input[type="radio"][name="bzsm"]:checked').val();
  $('#yfzwzText').text(val1 === '多发' ? '最大位于：' : '位置：');
  $('#yfzdxText').text(val1 === '多发' ? '较大者大小约：' : '大小约：');
  let val2 = $('input[type="radio"][name="zzsm"]:checked').val();
  $('#zzwzText').text(val2 === '多发' ? '最大位于：' : '位置：')
  $('#zzdxText').text(val2 === '多发' ? '较大者大小约：' : '大小约：')
}

function showGyh() {
  for (let i = 0; i < raidoArr1.length; i++) {
    if ($(`#${raidoArr1[i]}`).is(':checked')) {
      $('#fjgxba-rt-132').show();
      i = raidoArr1.length;
      return
    }
    $('#fjgxba-rt-132').hide();
  }
}
function initClickFun() {

  for (let j = 0; j < radioNameArr.length; j++) {
    $(`input[type="radio"][name="${radioNameArr[j]}"]`).click(function (e) {
      showRadioInfo('click', $(this).attr('name'));
    })
  }

  $('input[type=radio][name=zfg]').click(function (e) {
    let val = $('input[type=radio][name=zfg]:checked');
    val === '有' ? $('#fjgxba-rt-590').show() : $('#fjgxba-rt-590').hide()
  })

  $('input[type=radio][name=gztgz]').click(function (e) {
    let val = $('input[type=radio][name=gztgz]:checked');
    val === '有' ? $('#fjgxba-rt-593').show() : $('#fjgxba-rt-593').hide()
  })

  $('.bz-item').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus2[id]) {
      e.preventDefault();
    } else {
      // target.prop('checked', true)
    }
    var block = _this.attr('block-name2');
    var radioButtons = $( `.${block} input[type="radio"]`);
    if (target.is(':checked')) {
      isFillInStatus2[id] = '1';
      radioChed(radioButtons)
      // radioButtons.filter('[value="类圆形"]').prop('checked', true);
      // radioButtons.filter('[value="清楚"]').prop('checked', true);
      // radioButtons.filter('[value="均匀"]').prop('checked', true);
      // radioButtons.filter('[value="无"]').prop('checked', true);
    }else {
      radioButtons.filter('[value="类圆形"]').prop('checked', false);
      radioButtons.filter('[value="清楚"]').prop('checked', false);
      radioButtons.filter('[value="均匀"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('visit-status').siblings().removeClass('visit-status');
    $("." + block).show().siblings('.block2').hide();
    block === 'zz-block' ? $('.boryfz').css('cssText', 'border-bottom: 1px solid #dcdfe6') : $('.boryfz').css('cssText', 'border-bottom: none')
    block === 'yfz-block' ? $('.borzd').css('cssText', 'border-bottom: 1px solid #dcdfe6') : $('.borzd').css('cssText', 'border-bottom: none')
    // $("." + block).css('cssText', 'border-bottom: none').siblings('.block2').css('cssText', 'border-bottom: 1px solid #dcdfe6');
  })
  function radioChed(data) {
    if(!data.filter('[value="类圆形"]').is(':checked') && !data.filter('[value="不规则形"]').is(':checked')){
      data.filter('[value="类圆形"]').prop('checked', true);
    }
    if(!data.filter('[value="清楚"]').is(':checked') && !data.filter('[value="欠清"]').is(':checked') && !data.filter('[value="不清楚"]').is(':checked')){
      data.filter('[value="清楚"]').prop('checked', true);
    }
    if(!data.filter('[value="均匀"]').is(':checked') && !data.filter('[value="不均匀"]').is(':checked')){
      data.filter('[value="均匀"]').prop('checked', true);
    }
    if(!data.filter('[value="无"]').is(':checked') && !data.filter('[value="有"]').is(':checked')){
      data.filter('[value="无"]').prop('checked', true);
    }
  }
  $('.head-itme').click(function (e) {
    $('#fjgxba-rt-610').val($(this).text())
    showGmx($(this).text())
  })
  $('.ck-inp2').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus2[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus2[id];
      // $(this).parents().find('.block3').hide();
    }
  })

  $("input[type='radio'][name='txfs']").click(function (e) {
    console.log('44',$(this).val());
    $('.sbtx-view').hide()
    $('.jgs-view').hide()
    if($(this).val() === '手工填写'){
      $('.sbtx-view').show()
    }else {
      $('.jgs-view').show()
    }
    
  })
  $("input[type='radio'][name='bzsm']").click(function (e) {
    changeBzSmText();
  })
  $("input[type='radio'][name='zzsm']").click(function (e) {
    changeBzSmText();
  })
  $("input[type='radio'][name='dg']").click(function (e) {
    let val = $('input[type="radio"][name="dg"]:checked').val();
    if(val === '可见异常') {
      $('input[type="radio"][name="dgsq"][value="未见"]').prop('checked', true);
      $('input[type="radio"][name="dgkz"][value="未见"]').prop('checked', true);
      $('input[type="radio"][name="dskz"][value="无"]').prop('checked', true);
    }
  })


  var changeRadio2 = {
    yfzpsmd2: '45',
    zzpsmd2: '100',
    dg: '468',
    lbj: '484',
    dn: '517',
    pz: '537',
    yx: '549',
  }
  $("input[type='radio'][name='gyh']").click(function (e) {
    showGyh();
  })
  for (let key in changeRadio2) {
    $(`input[type='radio'][name='${key}']`).click(function (e) {
      $(`#fjgxba-rt-${changeRadio2[key]}`).is(':checked') ? $(`#fjgxba-rt-${changeRadio2[key]}-cont`).show() : $(`#fjgxba-rt-${changeRadio2[key]}-cont`).hide();
    })
  }

  for (let i = 0; i < textareaIdArr.length; i++) {
    $('#' + textareaIdArr[i]).click(function (e) {
      showTextarea('click', e.target.id)
    })
  }

  $('.block-ck1').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus1[id]) {
      e.preventDefault();
    }
    var block = _this.attr('block-name1');
    var radioButtons = $( `.${block} input[type="radio"]`);
    
    
    if (target.is(':checked')) {
      isFillInStatus1[id] = '1'; 
      radioBto(radioButtons)
    }else {
      radioButtons.filter('[value="尚清"]').prop('checked', false);
      radioButtons.filter('[value="未见"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    $("." + block).show().siblings('.block1').hide();
  })
  function radioBto(data) {
    if(!data.filter('[value="尚清"]').is(':checked') && !data.filter('[value="不清"]').is(':checked')){
      data.filter('[value="尚清"]').prop('checked', true);
    }
    if(!data.filter('[value="未见"]').is(':checked') && !data.filter('[value="可见"]').is(':checked')){
      data.filter('[value="未见"]').prop('checked', true);
    }
    if(!data.filter('[value="无"]').is(':checked') && !data.filter('[value="有"]').is(':checked')){
      data.filter('[value="无"]').prop('checked', true);
    }
  }

  $('.ck-inp1').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus1[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus1[id];
      if (id === 'fjgxba-rt-149') {
        let list = radioNameArr.slice(0, 2);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-150') {
        delete isFillInStatus3['fjgxba-rt-168'];
        delete isFillInStatus3['fjgxba-rt-169'];
        delete isFillInStatus3['fjgxba-rt-170'];
        delete isFillInStatus3['fjgxba-rt-171'];
        $('#zzhbXs').hide();
        $('#zzhbAs').hide();
        $('#zzhbXs').hide();
        $('#zzhbAs').hide();
        let list = radioNameArr.slice(2, 10);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-151') {
        delete isFillInStatus3['fjgxba-rt-228'];
        delete isFillInStatus3['fjgxba-rt-229'];
        delete isFillInStatus3['fjgxba-rt-230'];
        let list = radioNameArr.slice(10, 16);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-152') {
        let list = radioNameArr.slice(16, 18);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-152') {
        let list = radioNameArr.slice(18, 20);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-301') {
        let list = radioNameArr.slice(20, 22);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-302') {
        let list = radioNameArr.slice(22, 24);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-303') {
        let list = radioNameArr.slice(24, 26);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-304') {
        delete isFillInStatus3['fjgxba-rt-348'];
        delete isFillInStatus3['fjgxba-rt-349'];
        delete isFillInStatus3['fjgxba-rt-350'];
        let list = radioNameArr.slice(26, 32);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-305') {
        let list = radioNameArr.slice(32, 34);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-407') {
        let list = radioNameArr.slice(34, 36);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-408') {
        let list = radioNameArr.slice(36, 38);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-409') {
        let list = radioNameArr.slice(38, 40);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-410') {
        let list = radioNameArr.slice(40, 42);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
    }
  })

  $('.block-ck3').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus3[id]) {
      e.preventDefault();
    }
    var block = _this.attr('block-name3');
    var radioButtons = $( `.${block} input[type="radio"]`);
    if (target.is(':checked')) {
      isFillInStatus3[id] = '1';
      radioBto(radioButtons)
    }else {
      radioButtons.filter('[value="尚清"]').prop('checked', false);
      radioButtons.filter('[value="未见"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    $("." + block).show().siblings('.block3').hide();
  })

  $('.ck-inp3').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus3[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus3[id];
      // $(this).parents().find('.block3').hide();
      if (id === 'fjgxba-rt-168') {
        let list = radioNameArr.slice(2, 4);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-169') {
        let list = radioNameArr.slice(4, 6);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-170') {
        let list = radioNameArr.slice(6, 8);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-171') {
        let list = radioNameArr.slice(8, 10);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-228') {
        let list = radioNameArr.slice(10, 12);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-229') {
        let list = radioNameArr.slice(12, 14);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-230') {
        let list = radioNameArr.slice(14, 16);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-348') {
        let list = radioNameArr.slice(26, 28);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-349') {
        let list = radioNameArr.slice(28, 30);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'fjgxba-rt-350') {
        let list = radioNameArr.slice(30, 32);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
    }
  })
}

function hideRadioInfo(radioName) {
  let secondLastChar = radioName.charAt(radioName.length - 2).toUpperCase();
  let id = radioName.slice(0, radioName.length - 2) + secondLastChar + radioName.charAt(radioName.length - 1);
  $('#' + id).hide();
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = descriptionStr;
  rtStructure.impression = curElem.find('#fjgxba-rt-587').val();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}