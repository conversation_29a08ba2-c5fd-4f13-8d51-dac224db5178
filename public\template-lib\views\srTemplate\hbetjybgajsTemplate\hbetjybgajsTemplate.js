$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#jybgajs1 .view-container'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView();
    } else {
      previewRange()
      handleRange();
    }
  }
}
// 处理范围
function handleRange() {
  $('[data-over-mark]').each(function () {
    var $cur = $(this);
    var attr = $cur.attr('data-over-mark');
    if (!attr) {
      return;
    }
    try {
      var params = attr.split(',');
      if (params.length < 2) {
        return;
      }
      var $val = $(params[0]);
      var $range = $(params[1]);
      $val.on('input', function() {
        showMark($cur, $val, $range);
      });
      $range.on('input', function() {
        showMark($cur, $val, $range);
      });
    } catch (error) {
      console.error(error);
    }
  });
}
// 主动计算箭头
function previewRange() {
  $('[data-over-mark]').each(function () {
    var $cur = $(this);
    var attr = $cur.attr('data-over-mark');
    if (!attr) {
      return;
    }
    try {
      var params = attr.split(',');
      if (params.length < 2) {
        return;
      }
      var $val = $(params[0]);
      var $range = $(params[1]);
      showMark($cur, $val, $range);
    } catch (error) {
    }
  });
}
function showMark($cur, $val, $range) {
  var rangeTxt = $range.text();
  if (!rangeTxt) {
    return;
  }
  var mark = '';
  var curVal = parseFloat($val.val().trim());
  var rangeList = rangeTxt.split('\n');
  for (var i = 0; i < rangeList.length; i++) {
    var curRange = rangeList[i];
    let curRanges = curRange.replace("＜24个月", "0-1岁").replace("＜11", "0-10").replace("＜7", "0-6");
    var rangeSplit = curRanges.split(/[:：]/);
    var rangeMinMax;
    if (rangeSplit.length >= 2) {
      var curAgeText = publicInfo.age || '';
      var curAgeNum;
      if (/\D/.test(curAgeText) && curAgeText.indexOf('岁') === -1) {
        curAgeNum = 0;
      } else {
        curAgeNum = parseFloat(curAgeText);
      }
      var rangeAgeTxt = rangeSplit[0];
      if (rangeAgeTxt.indexOf('-') !== -1) {
        var ageMinMax = rangeAgeTxt.split('-');
        var ageMin = parseFloat(ageMinMax[0].replace(/[^.\d]/g, ''));
        var ageMax = parseFloat(ageMinMax[1].replace(/[^.\d]/g, ''));
        if (curAgeNum < ageMin || curAgeNum >= ageMax) {
          continue;
        }
      } else if (rangeAgeTxt.indexOf('≥') !== -1) {
        var ageOver = parseFloat(rangeAgeTxt.replace(/[^.\d]/g, ''));
        if (curAgeNum < ageOver) {
          continue;
        }
      }
      rangeMinMax = rangeSplit[1];
    } else {
      rangeMinMax = rangeSplit[0];
    }
    if (rangeMinMax.indexOf('-') !== -1) {
      var arr = rangeMinMax.split('-');
      var rangeMin = parseFloat(arr[0].replace(/[^.\d]/g, ''));
      var rangeMax = parseFloat(arr[1].replace(/[^.\d]/g, ''));
      if (curVal < rangeMin) {
        mark = '↓';
      }
      if (curVal > rangeMax) {
        mark = '↑';
      }
    } else if (rangeMinMax.indexOf('≥') !== -1) {
      var rangeOver = parseFloat(rangeMinMax.replace(/[^.\d]/g, ''));
      if (curVal < rangeOver) {
        mark = '↓';
      }
    } 
  }
  $cur.text(mark);
}
function initView() {
  curElem.find('.ajs-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
  curElem.find('.ajs-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      $(this).html(value);
    } else {
      $(this).html('')
    }
    // 处理没数据回显问题
    let alls = ['ajs-rt-2','ajs-rt-4','ajs-rt-6','ajs-rt-8','ajs-rt-10','ajs-rt-12','ajs-rt-14','ajs-rt-16',
      'ajs-rt-18','ajs-rt-20','ajs-rt-22','ajs-rt-24','ajs-rt-26','ajs-rt-28','ajs-rt-30','ajs-rt-32','ajs-rt-34',
      'ajs-rt-36','ajs-rt-38','ajs-rt-40','ajs-rt-42'
    ]
    let nums = []
    for (var i = 44; i < 64; i++) {
      let hts = `ajs-rt-${i}`
      nums.push(hts)
    }
    alls.forEach((e,index) => {
      if(!idAndDomMap[e].value){
        if(key === nums[index]){
          $(this).text('')
        }
      }
    })
    addIdToNodeByView(this, key, idAndDomMap);
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let strList = []
  $('.ajs-edit .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length > 0) {
        strList.push(curPid.val + '：' + child[0].val)
      }
    }
  })
  strList && strList.length > 0 ? rtStructure.impression = strList.join('；\n') : ''
}