<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<!-- 如果需要用到layui库，则引入layui.min.css，不需要可删除 -->
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<link rel="stylesheet" href="/template-lib/views/blApplyTemplate/blhtzzsydTemplate/blhtzzsydTemplate.css">
<script src="/template-lib/plugins/jquery.min.js"></script>
<!-- 如果需要用到layui库，则引入layui.js，不需要可删除 -->
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/controls/blApplyDictionary.js"></script>
<script src="/template-lib/views/blApplyTemplate/blhtzzsydTemplate/blhtzzsydTemplate.js"></script>

<ul class="t-pg" style="height: 100%;">
  <li class="page" id="blhtzzsyd1">
    <div class="htzz-edit">
      <div class="bljcsqd-h">
        病理检查申请单
      </div>
      <div class="box-item">
        <div class="box-title">病人信息</div>
        <div class="box-con">
          <div class="mb-12">
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-1" rt-req="1"
                  rt-sc="req:1;pageId:htzz1;name:姓名label;wt:;desc:姓名label;vt:;pvf:1;">姓名：</span>
              </span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-2" pid="htzz-rt-1"
                rt-sc="pageId:htzz1;name:姓名;wt:1;desc:姓名;vt:;pvf:;code:1;" pat-info="name">
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-3"
                rt-sc="pageId:htzz1;name:年龄label;wt:;desc:年龄label;vt:;pvf:1;">年龄：</span>
              <input class="rt-sr-w inp-sty wd-72" type="text" autocomplete="off" id="htzz-rt-4" pid="htzz-rt-3"
                rt-sc="pageId:htzz1;name:年龄;wt:1;desc:年龄;vt:;pvf:;code:3;">
              <select class="rt-sr-w inp-sty wd-72" id="htzz-rt-72" pid="htzz-rt-3"
                rt-sc="pageId:htzz1;name:年龄单位下拉;wt:2;desc:年龄单位下拉;vt:;pvf:;">
                <option label="岁" value="岁">岁</option>
                <option label="月" value="月">月</option>
                <option label="周" value="周">周</option>
                <option label="天" value="天">天</option>
                <option label="时" value="时">时</option>
                <option label="分" value="分">分</option>
              </select>
              <input class="rt-sr-w inp-sty wd-72" type="text" autocomplete="off" pat-info="ageMonth" id="htzz-rt-73"
                pid="htzz-rt-3" rt-sc="pageId:htzz1;name:年龄月份;wt:1;desc:年龄月份;vt:;pvf:;">
              <span class="rt-sr-w age-sub-unit" id="htzz-rt-74" pid="htzz-rt-73"
                rt-sc="pageId:htzz1;name:年龄子单位;wt:;desc:年龄子单位;vt:;pvf:;">月</span>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-5" rt-sc="req:1;pageId:htzz1;name:性别label;wt:;desc:性别label;vt:;pvf:1;"
                  rt-req="1">性别：</span>
              </span>
              <div class="w-con show-or-hide more">
                <input class="rt-sr-w inp-sty sex-sel" type="text" autocomplete="off" id="htzz-rt-6" pid="htzz-rt-5"
                  rt-sc="pageId:htzz1;name:性别;wt:1;desc:性别;vt:;pvf:;code:2;" pat-info="sex">
              </div>
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-7" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:病人来源label;wt:;desc:病人来源label;vt:;pvf:1;">病人来源：</span>
              </span>
              <div class="w-con show-or-hide more">
                <input class="rt-sr-w inp-sty patient-sel" type="text" autocomplete="off" id="htzz-rt-8" pid="htzz-rt-7"
                  rt-sc="pageId:bljcsqd1;name:病人来源;wt:1;desc:病人来源;vt:;pvf:;code:103;" pat-info="patientSource">
              </div>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-9" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:出生日期label;wt:;desc:出生日期label;vt:;pvf:1;">出生日期：</span>
              </span>
              <span class="csrq-item">
                <div class="laydate-w" style="display: inline-block;vertical-align: middle;">
                  <i class="layui-icon layui-icon-date" style="top: 5px;"></i>
                  <textarea val-format="YYYY-MM-DD" class="rt-sr-w rt-sr-t date-wrap03" style="width: 225px;"
                    id="htzz-rt-10" pid="htzz-rt-9" rt-sc="pageId:bljcsqd1;name:出生日期;wt:1;desc:出生日期;vt:;pvf:;code:7;"
                    pat-info="birthDate"></textarea>
                </div>
              </span>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-11" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:病人IDlabel;wt:;desc:病人IDlabel;vt:;pvf:1;">病人ID：</span>
              </span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-12" pid="htzz-rt-11"
                rt-sc="pageId:bljcsqd1;name:病人ID;wt:1;desc:病人ID;vt:;pvf:;code:10;" pat-info="sickId">
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-13"
                rt-sc="pageId:bljcsqd1;name:门诊号label;wt:;desc:门诊号label;vt:;pvf:1;">门诊号：</span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-14" pid="htzz-rt-13"
                rt-sc="pageId:bljcsqd1;name:门诊号;wt:1;desc:门诊号;vt:;pvf:;code:11;" pat-info="inpatientNo">
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-15"
                rt-sc="pageId:bljcsqd1;name:住院号label;wt:;desc:住院号label;vt:;pvf:1;">住院号：</span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-16" pid="htzz-rt-15"
                rt-sc="pageId:bljcsqd1;name:住院号;wt:1;desc:住院号;vt:;pvf:;code:12;" pat-info="outpatientNo">
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-17"
                rt-sc="pageId:bljcsqd1;name:床号label;wt:;desc:床号label;vt:;pvf:1;">床号：</span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-18" pid="htzz-rt-17"
                rt-sc="pageId:bljcsqd1;name:床号;wt:1;desc:床号;vt:;pvf:;code:14;" pat-info="bedNo">
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-19"
                rt-sc="pageId:bljcsqd1;name:手机号label;wt:;desc:手机号label;vt:;pvf:1;">手机号：</span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-20" pid="htzz-rt-19"
                rt-sc="pageId:bljcsqd1;name:手机号;wt:1;desc:手机号;vt:;pvf:;code:17;" pat-info="phoneNumber">
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-21"
                rt-sc="pageId:bljcsqd1;name:身份证号label;wt:;desc:身份证号label;vt:;pvf:1;">身份证号：</span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-22" pid="htzz-rt-21"
                rt-sc="pageId:bljcsqd1;name:身份证号;wt:1;desc:身份证号;vt:;pvf:;code:9;" pat-info="identityCard">
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-23"
                rt-sc="pageId:bljcsqd1;name:籍贯label;wt:;desc:籍贯label;vt:;pvf:1;">籍贯：</span>
              <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-24" pid="htzz-rt-23"
                rt-sc="pageId:bljcsqd1;name:籍贯;wt:1;desc:籍贯;vt:;pvf:;code:25;" pat-info="hometown">
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-25"
                rt-sc="pageId:bljcsqd1;name:是否传染病label;wt:;desc:是否传染病label;vt:;pvf:1;">是否传染病：</span>
              <span class="crb-item">
                <label class="ml-12" for="htzz-rt-26">
                  <input class="rt-sr-w" type="radio" name="sfcrb" value="是" id="htzz-rt-26" pid="htzz-rt-25"
                    rt-sc="pageId:bljcsqd1;name:是;wt:5;desc:是;vt:;pvf:;code:310;">
                  <span>是</span>
                </label>
                <input class="rt-sr-w inp-sty ml-8" type="text" autocomplete="off" id="htzz-rt-28" pid="htzz-rt-26"
                  rt-sc="pageId:bljcsqd1;name:传染病;wt:1;desc:传染病;vt:;pvf:;" style="width: 122px;">
                <label class="ml-12" for="htzz-rt-27">
                  <input class="rt-sr-w" type="radio" name="sfcrb" value="否" id="htzz-rt-27" pid="htzz-rt-25"
                    rt-sc="pageId:bljcsqd1;name:否;wt:5;desc:否;vt:;pvf:;code:310;">
                  <span>否</span>
                </label>
              </span>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-29"
                  rt-sc="req:1;pageId:bljcsqd1;name:末次月经label;wt:;desc:末次月经label;vt:;pvf:1;" rt-req="1">末次月经：</span>
              </span>
              <span class="mcyj-item">
                <div class="laydate-w" style="display: inline-block;vertical-align: middle;">
                  <i class="layui-icon layui-icon-date" style="top: 5px;"></i>
                  <textarea val-format="YYYY-MM-DD" class="rt-sr-w rt-sr-t date-wrap01" style="width: 185px;"
                    id="htzz-rt-30" pid="htzz-rt-29" rt-sc="pageId:bljcsqd1;name:末次月经;wt:1;desc:末次月经;vt:;pvf:;code:32;"
                    pat-info="lastMensesDate"></textarea>
                </div>
                <label class="ml-10" for="htzz-rt-31">
                  <input class="rt-sr-w" type="checkbox" name="wyj" value="绝经" id="htzz-rt-31" pid="htzz-rt-29"
                    rt-sc="pageId:bljcsqd1;name:绝经;wt:4;desc:绝经;vt:;pvf:;">
                  <span>绝经</span>
                </label>
              </span>
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-75"
                rt-sc="name:婚姻状况labe;wt:;desc:婚姻状况labe;vt:;pvf:;">婚姻状况：</span>
              <div class="wd-230 layui-form" style="display: inline-block;vertical-align: middle;">
                <select lay-filter="selChange" lay-verify="" style="width: 100%;border: 1px solid #C0C4CC;"
                  name="template" class="rt-sr-w" id="htzz-rt-76" pid="htzz-rt-75"
                  rt-sc="name:婚姻状况;wt:2;desc:婚姻状况;vt:;pvf:;">
                  <option value="">请选择</option>
                  <option value="0">未知</option>
                  <option value="1">已婚</option>
                  <option value="2">未婚</option>
                </select>
              </div>
            </div>
          </div>
          <div>
            <span class="rt-sr-w row-lab" id="htzz-rt-35"
              rt-sc="pageId:bljcsqd1;name:通信地址label;wt:;desc:通信地址label;vt:;pvf:1;">通信地址：</span>
            <input class="rt-sr-w inp-sty" type="text" autocomplete="off" style="width: calc(100% - 114px);"
              id="htzz-rt-36" pid="htzz-rt-35" rt-sc="pageId:bljcsqd1;name:通信地址;wt:1;desc:通信地址;vt:;pvf:;code:28;"
              pat-info="mailingAddress">
          </div>
        </div>
      </div>
      <div class="box-item">
        <div class="box-title">检查信息</div>
        <div class="box-con">
          <div class="mb-12">
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-37" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:检查子类label;wt:;desc:检查子类label;vt:;pvf:1;">检查子类：</span>
              </span>
              <div class="w-con show-or-hide more">
                <input class="rt-sr-w inp-sty examsub-sel" type="text" autocomplete="off" id="htzz-rt-38"
                  pid="htzz-rt-37" rt-sc="pageId:bljcsqd1;name:检查子类;wt:1;desc:检查子类;vt:;pvf:;code:106;">
              </div>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-39" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:是否冰冻label;wt:;desc:是否冰冻label;vt:;pvf:1;">是否冰冻：</span>
              </span>
              <span class="bd-item">
                <label class="ml-12" for="htzz-rt-40">
                  <input class="rt-sr-w" type="radio" name="sfbd" value="是" id="htzz-rt-40" pid="htzz-rt-39"
                    rt-sc="pageId:bljcsqd1;name:是;wt:5;desc:是;vt:;pvf:;code:311;">
                  <span>是</span>
                </label>
                <label class="ml-12" for="htzz-rt-41">
                  <input class="rt-sr-w" type="radio" name="sfbd" value="否" id="htzz-rt-41" pid="htzz-rt-39"
                    rt-sc="pageId:bljcsqd1;name:否;wt:5;desc:否;vt:;pvf:;code:311;">
                  <span>否</span>
                </label>
              </span>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-42"
                  rt-sc="req:1;pageId:bljcsqd1;name:预约时间label;wt:;desc:预约时间label;vt:;pvf:1;" pid="htzz-rt-40"
                  rt-req="1">预约时间：</span>
              </span>
              <div class="laydate-w schedule-date" style="display: inline-block;vertical-align: middle;">
                <i class="layui-icon layui-icon-date" style="top: 5px;"></i>
                <textarea val-format="YYYY-MM-DD" class="rt-sr-w rt-sr-t date-wrap02" style="width: 228px;"
                  id="htzz-rt-43" pid="htzz-rt-42"
                  rt-sc="pageId:bljcsqd1;name:预约时间;wt:1;desc:预约时间;vt:;pvf:;code:150;"></textarea>
              </div>
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-44" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:申请医院label;wt:;desc:申请医院label;vt:;pvf:;">申请医院：</span>
              </span>
              <div class="w-con show-or-hide more">
                <input class="rt-sr-w inp-sty hosp-sel" type="text" autocomplete="off" id="htzz-rt-45" pid="htzz-rt-44"
                  rt-sc="pageId:bljcsqd1;name:申请医院;wt:1;desc:申请医院;vt:;pvf:;code:107;">
              </div>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-46" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:申请科室label;wt:;desc:申请科室label;vt:;pvf:1;">申请科室：</span>
              </span>
              <div class="w-con show-or-hide more">
                <input class="rt-sr-w inp-sty dept-sel" type="text" autocomplete="off" id="htzz-rt-47" pid="htzz-rt-46"
                  rt-sc="pageId:bljcsqd1;name:申请科室;wt:1;desc:申请科室;vt:;pvf:;code:109;">
              </div>
            </div>
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-48" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:申请医生label;wt:;desc:申请医生label;vt:;pvf:1;">申请医生：</span>
              </span>
              <div class="w-con show-or-hide more">
                <input class="rt-sr-w inp-sty doct-sel" type="text" autocomplete="off" id="htzz-rt-49" pid="htzz-rt-48"
                  rt-sc="pageId:bljcsqd1;name:申请医生;wt:1;desc:申请医生;vt:;pvf:;code:119;">
              </div>
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="row-lab">
                <span class="isk">*</span>
                <span class="rt-sr-w" id="htzz-rt-50" rt-req="1"
                  rt-sc="req:1;pageId:bljcsqd1;name:检查项目label;wt:;desc:检查项目label;vt:;pvf:1;">检查项目：</span>
              </span>
              <div class="w-con show-or-hide more jcxm-item">
                <input class="rt-sr-w inp-sty examitem-sel" type="text" autocomplete="off" id="htzz-rt-51"
                  pid="htzz-rt-50" rt-sc="pageId:bljcsqd1;name:检查项目;wt:1;desc:检查项目;vt:;pvf:;code:101;">
              </div>
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-52"
                rt-sc="pageId:bljcsqd1;name:项目费用label;wt:;desc:项目费用label;vt:;pvf:1;">项目费用：</span>
              <span class="xmfy-item">
                <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-53" pid="htzz-rt-52"
                  rt-sc="pageId:bljcsqd1;name:项目费用;wt:1;desc:项目费用;vt:;pvf:;code:134;">
              </span>
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-54"
                rt-sc="pageId:bljcsqd1;name:是否缴费label;wt:;desc:是否缴费label;vt:;pvf:1;">是否缴费：</span>
              <span class="jf-item">
                <label class="ml-12" for="htzz-rt-55">
                  <input class="rt-sr-w" type="radio" name="sfjf" value="是" id="htzz-rt-55" pid="htzz-rt-54"
                    rt-sc="pageId:bljcsqd1;name:是;wt:5;desc:是;vt:;pvf:;code:131;">
                  <span>是</span>
                </label>
                <label class="ml-12" for="htzz-rt-56">
                  <input class="rt-sr-w" type="radio" name="sfjf" value="否" id="htzz-rt-56" pid="htzz-rt-54"
                    rt-sc="pageId:bljcsqd1;name:否;wt:5;desc:否;vt:;pvf:;code:131;">
                  <span>否</span>
                </label>
              </span>
            </div>
          </div>
          <div class="mb-12">
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-57"
                rt-sc="pageId:bljcsqd1;name:检查目的label;wt:;desc:检查目的label;vt:;pvf:1;">检查目的：</span>
              <span class="xmfy-item">
                <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-58" pid="htzz-rt-57"
                  rt-sc="pageId:bljcsqd1;name:检查目的;wt:1;desc:检查目的;vt:;pvf:;">
              </span>
            </div>
            <div class="row-item">
              <span class="rt-sr-w row-lab" id="htzz-rt-59"
                rt-sc="pageId:bljcsqd1;name:取自身体何处label;wt:;desc:取自身体何处label;vt:;pvf:1;">取自身体何处：</span>
              <span class="xmfy-item">
                <input class="rt-sr-w inp-sty" type="text" autocomplete="off" id="htzz-rt-60" pid="htzz-rt-59"
                  rt-sc="pageId:bljcsqd1;name:取自身体何处;wt:1;desc:取自身体何处;vt:;pvf:;">
              </span>
            </div>
          </div>
          <div class="mb-12">
            <span class="rt-sr-w row-lab ver-t" id="htzz-rt-61"
              rt-sc="pageId:bljcsqd1;name:病历摘要label;wt:;desc:病历摘要label;vt:;pvf:1;">病历摘要：</span>
            <textarea class="rt-sr-w rt-sr-t" style="width: calc(100% - 114px);height: 80px;resize:vertical;"
              id="htzz-rt-62" pid="htzz-rt-61" rt-sc="pageId:bljcsqd1;name:病历摘要;wt:1;desc:病历摘要;vt:;pvf:;"></textarea>
          </div>
          <div class="mb-12">
            <div class="rt-sr-w row-lab ver-t" id="htzz-rt-63"
              rt-sc="pageId:bljcsqd1;name:病史label;wt:;desc:病史label;vt:;pvf:1;">传染病、既往史等特殊病史：</div>
            <textarea class="rt-sr-w rt-sr-t" style="width: calc(100% - 114px);height: 80px;resize:vertical;"
              id="htzz-rt-64" pid="htzz-rt-63"
              rt-sc="pageId:bljcsqd1;name:病史;wt:1;desc:病史;vt:;pvf:;code:29;"></textarea>
          </div>
          <div class="mb-12">
            <div class="rt-sr-w row-lab ver-t" id="htzz-rt-65"
              rt-sc="pageId:bljcsqd1;name:特殊检查label;wt:;desc:特殊检查label;vt:;pvf:1;">特殊检查：</div>
            <textarea class="rt-sr-w rt-sr-t" style="width: calc(100% - 114px);height: 80px;resize:vertical;"
              placeholder="包括CT、MIR等影像学、超声、检验等" id="htzz-rt-66" pid="htzz-rt-65"
              rt-sc="pageId:bljcsqd1;name:特殊检查;wt:1;desc:特殊检查;vt:;pvf:;code:140;"></textarea>
          </div>
          <div class="mb-12">
            <div class="rt-sr-w row-lab ver-t" id="htzz-rt-67"
              rt-sc="pageId:bljcsqd1;name:临床与手术检查所见label;wt:;desc:临床与手术检查所见label;vt:;pvf:1;">临床与手术检查所见：</div>
            <textarea class="rt-sr-w rt-sr-t" style="width: calc(100% - 114px);height: 80px;resize:vertical;"
              id="htzz-rt-68" pid="htzz-rt-67"
              rt-sc="pageId:bljcsqd1;name:临床与手术检查所见;wt:1;desc:临床与手术检查所见;vt:;pvf:;code:304;"></textarea>
          </div>
          <div>
            <span class="rt-sr-w row-lab ver-t" id="htzz-rt-69"
              rt-sc="pageId:bljcsqd1;name:临床诊断label;wt:;desc:临床诊断label;vt:;pvf:1;">临床诊断：</span>
            <textarea class="rt-sr-w rt-sr-t" style="width: calc(100% - 114px);height: 80px;resize:vertical;"
              id="htzz-rt-70" pid="htzz-rt-69"
              rt-sc="pageId:bljcsqd1;name:临床诊断;wt:1;desc:临床诊断;vt:;pvf:;code:138;"></textarea>
          </div>
        </div>
      </div>
      <div class="box-item">
        <div class="box-title" style="padding: 4px 44px;border-bottom: none;line-height: 28px;">
          <div class="tit-left">
            <span class="rt-sr-w bb-title" style="line-height: 36px;" id="htzz-rt-71"
              rt-sc="pageId:bljcsqd1;name:标本信息;wt:;desc:标本信息;vt:;pvf:;">标本信息</span>
            <div class="form-item">
              <span class="span-tit">固定液：</span>
              <div class="w-con show-or-hide more">
                <input class="inp-sty gdy-sel new-gdy" type="text" autocomplete="off" style="width: 160px;"
                  sel-name="gdy-sel">
              </div>
              <span class="span-tit">离体时间：</span>
              <div class="laydate-w" style="vertical-align: middle;">
                <i class="layui-icon layui-icon-date" style="top: 0;"></i>
                <input type="text" val-format="HH时mm分ss秒" class="time-wrap01 new-ltsj" style="width: 180px;">
              </div>
              <span class="span-tit">固定时间：</span>
              <div class="laydate-w" style="vertical-align: middle;">
                <i class="layui-icon layui-icon-date" style="top: 0;"></i>
                <input type="text" val-format="HH时mm分ss秒" class="time-wrap02 new-gdsj" style="width: 180px;">
              </div>
            </div>
          </div>
          <div class="tit-right">
            <span class="btn-sty edit-btn" onclick="refresh()">
              <span style="vertical-align: top;">刷新</span>
            </span>
            <span class="btn-sty add-btn" onclick="addRow(this,'add')">
              <span>+</span>
              <span style="vertical-align: top;">添加</span>
            </span>
            <span class="btn-sty del-btn" onclick="removeRow(this,'all')">
              <span style="vertical-align: top;">删除</span>
            </span>
          </div>
        </div>
        <table class="bbxx-table" border="1">
          <thead>
            <tr class="gray">
              <th width="48" class="view-none">
                <label>
                  <input class="all-check" type="checkbox" onchange="changeTableCk(this,'all')">
                </label>
              </th>
              <th width="144">标本名称</th>
              <th width="144">标本部位</th>
              <th width="105">标本类别</th>
              <th width="225">离体时间</th>
              <th width="225">固定时间</th>
              <th width="188">固定液</th>
              <th width="60" class="use-col view-none">操作</th>
            </tr>
          </thead>
          <tbody class="htzzTableBody">
            <tr class="cloneTr" style="display: none;">
              <td class="view-none">
                <label>
                  <input class="single-check" type="checkbox" onchange="changeTableCk(this,'single')">
                </label>
              </td>
              <td>
                <input class="rt-sr-w" style="display:none;" type="hidden" value="1" id="htzz-rt-00.1234"
                  rt-sc="pageId:bljcsqd1;name:列表行;wt:;desc:列表行;vt:;pvf:1;">
                <div class="w-con show-or-hide more">
                  <input class="rt-sr-w inp-sty bbmc-sel" type="text" autocomplete="off" style="width: 120px;"
                    id="htzz-rt-01.1234" pid="htzz-rt-00.1234"
                    rt-sc="pageId:bljcsqd1;name:标本名称;wt:1;desc:标本名称;vt:;pvf:1;code:300;" inp-name="标本名称"
                    sel-name="bbmc-sel">
                </div>
              </td>
              <td>
                <div class="w-con show-or-hide more">
                  <input class="rt-sr-w inp-sty bbbw-sel" type="text" autocomplete="off" style="width: 120px;"
                    id="htzz-rt-02.1234" pid="htzz-rt-00.1234"
                    rt-sc="pageId:bljcsqd1;name:标本部位;wt:1;desc:标本部位;vt:;pvf:1;code:301;" inp-name="标本部位"
                    sel-name="bbbw-sel">
                </div>
              </td>
              <td>
                <div class="w-con show-or-hide more">
                  <input class="rt-sr-w inp-sty bblb-sel" type="text" autocomplete="off" style="width: 80px;"
                    id="htzz-rt-03.1234" pid="htzz-rt-00.1234"
                    rt-sc="pageId:bljcsqd1;name:标本类别;wt:1;desc:标本类别;vt:;pvf:1;code:302;" inp-name="标本类别"
                    sel-name="bblb-sel">
                </div>
              </td>
              <td>
                <div class="laydate-w" style="vertical-align: middle;">
                  <i class="layui-icon layui-icon-date" style="top: 0;"></i>
                  <input type="text" val-format="HH时mm分ss秒" class="rt-sr-w rt-sr-t time-wrap01" style="width: 200px;"
                    id="htzz-rt-04.1234" pid="htzz-rt-00.1234"
                    rt-sc="pageId:bljcsqd1;name:离体时间;wt:1;desc:离体时间;vt:;pvf:1;" inp-name="离体时间">
                </div>
              </td>
              <td>
                <div class="laydate-w" style="vertical-align: middle;">
                  <i class="layui-icon layui-icon-date" style="top: 0;"></i>
                  <input type="text" val-format="HH时mm分ss秒" class="rt-sr-w rt-sr-t time-wrap02" style="width: 200px;"
                    id="htzz-rt-05.1234" pid="htzz-rt-00.1234"
                    rt-sc="pageId:bljcsqd1;name:固定时间;wt:1;desc:固定时间;vt:;pvf:1;" inp-name="固定时间">
                </div>
              </td>
              <td>
                <div class="w-con show-or-hide more">
                  <input class="rt-sr-w inp-sty gdy-sel" type="text" autocomplete="off" style="width: 160px;"
                    id="htzz-rt-06.1234" pid="htzz-rt-00.1234" rt-sc="pageId:bljcsqd1;name:固定液;wt:1;desc:固定液;vt:;pvf:1;"
                    inp-name="固定液" sel-name="gdy-sel">
                </div>
              </td>
              <td class="del-tbtn view-none" onclick="removeRow(this,'single')">删除</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="htzz-view">
      <div class="view-head">
        <div class="head-title">东莞松山湖中心医院病理活体组织送验单</div>
      </div>
      <div class="msg-num">
        <div class="ml-8 mb-6">
          <span class="label-tit">申请号：</span>
          <span data-key="applyNo"></span>
        </div>
        <div class="mr-104">
          <span class="label-tit">活检列号：</span>
          <span></span>
        </div>
      </div>
      <div class="view-form">
        <div class="from-line flex-flow">
          <div class="view-item wd-340">
            <div class="wd-108 item-sty">
              <span class="label-tit">姓名：</span>
              <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;" data-key="name"></span>
            </div>
            <div class="wd-106 item-sty">
              <span class="label-tit">性别：</span>
              <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;" data-key="sex"></span>
            </div>
            <div class="wd-106 item-sty">
              <span class="label-tit">年龄：</span>
              <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;" data-key="age"></span>
            </div>
          </div>
          <div class="view-item wd-132 item-sty">
            <span class="label-tit">科室：</span>
            <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;"
              data-key="reqDeptName"></span>
          </div>
          <div class="view-item wd-234 item-sty">
            <span class="label-tit">住院号：</span>
            <span style="display: inline-block;width: calc(100% - 60px);overflow: hidden;"
              data-key="inpatientNo"></span>
          </div>
        </div>
        <div class="from-line flex-flow">
          <div class="view-item wd-340">
            <div class="wd-108 item-sty">
              <span class="label-tit">来源：</span>
              <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;"
                data-key="patientSource"></span>
            </div>
            <div class="wd-218 item-sty">
              <span class="label-tit">籍贯：</span>
              <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;"
                data-key="birthPlace"></span>
            </div>
          </div>
          <div class="view-item wd-132 item-sty">
            <span class="label-tit">婚姻状况：</span>
            <span style="display: inline-block;width: calc(100% - 75px);overflow: hidden;"
              data-key="marriedFlag"></span>
          </div>
          <div class="view-item  wd-115 item-sty">
            <span class="label-tit">床号：</span>
            <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;" data-key="bedNo"></span>
          </div>
          <div class="view-item  wd-115 item-sty">
            <span class="label-tit">门诊号：</span>
            <span style="display: inline-block;width: calc(100% - 60px);overflow: hidden;"
              data-key="outpatientNo"></span>
          </div>
        </div>
        <div class="from-line flex-flow">
          <div class="view-item wd-472 item-sty">
            <span class="label-tit">地址：</span>
            <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;"
              data-key="mailingAddress"></span>
          </div>
          <div class="view-item wd-234 item-sty">
            <span class="label-tit">上次病理号：</span>
            <span></span>
          </div>
        </div>
        <div class="from-line flex-flow">
          <div class="view-item  wd-336 item-sty">
            <span class="label-tit">送验医院：</span>
            <span style="display: inline-block;width: calc(100% - 75px);overflow: hidden;"
              data-key="reqHospital"></span>
          </div>
          <div class="view-item wd-132 item-sty">
            <span class="label-tit">电话：</span>
            <span style="display: inline-block;width: calc(100% - 45px);overflow: hidden;"
              data-key="phoneNumber"></span>
          </div>
          <div class="view-item wd-234 item-sty">
            <span class="label-tit">患病久暂：</span>
            <span></span>
          </div>
        </div>
        <div class="from-line" id="jyyb">
          <div class="pl-8">
            <div class="label-tit" style="display: inline-block;">检验样本：</div>
            <div style="display: inline-block;width: calc(100% - 82px);vertical-align:top" id="sampleInfo">
              <div data-key="htzz-rt-51" style="line-height: 20px;"></div>
            </div>
          </div>
        </div>
        <div>
          <table border="1" cellspacing="0" cellpadding="0" class="sample-tbl" id="sampleTable">
            <thead>
              <tr>
                <th class="item-sty mwd-68">序号</th>
                <th class="item-sty mwd-208">标本名称</th>
                <th class="item-sty mwd-80">标本部位</th>
                <th class="item-sty mwd-72">标本数量</th>
                <th class="item-sty mwd-144">离体时间</th>
                <th class="item-sty mwd-144">固定时间</th>
              </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
        <div class="from-line">
          <div class="view-item item-sty pl-8">
            <span class="label-tit">取自身体何处：</span>
            <span style="display: inline-block;width: calc(100% - 105px);overflow: hidden;"
              data-key="htzz-rt-60"></span>
          </div>
        </div>
        <div id="lines">
          <div class="line-noborder">
            <div class="label-tit">病历摘要：</div>
            <div class="special-item" style="overflow: hidden;" data-key="clinSymp"></div>
          </div>
          <div class="line-noborder">
            <div class="label-tit">传染病、既往史等特殊病史：</div>
            <div class="special-item" style="overflow: hidden;" data-key="medRecord"></div>
          </div>
          <div class="line-noborder">
            <div class="label-tit">特殊检查:(包括CT、MIR等影像学、超声、检验等)</div>
            <div class="special-item" style="overflow: hidden;" data-key="htzz-rt-66"></div>
          </div>
          <div class="line-noborder">
            <div class="label-tit">临床与手术检查所见：</div>
            <div class="special-item" style="overflow: hidden;" data-key="surgerySeen"></div>
          </div>
          <div class="line-noborder">
            <div class="label-tit">临床诊断：</div>
            <div class="special-item" style="overflow: hidden;" data-key="clinDiag"></div>
          </div>
        </div>
        <div class="form-footer">
          <!-- <div class="from-line flex-flow mt-8" style="border-top: 1px #000 solid;height: ;">
            <div class="view-item" style="width: 33%;">
              <span class="label-tit">检验目的：</span>
              <span style="display: inline-block;width: calc(100% - 75px);overflow: hidden;"
                data-key="examMotive"></span>
            </div>
            <div class="view-item" style="width: 34%;">
              <span class="label-tit">申请日期：</span>
              <span style="display: inline-block;width: calc(100% - 75px);overflow: hidden;" data-key="reqDate"></span>
            </div>
            <div class="view-item" style="width: 33%;">
              <span class="label-tit">医生签名：</span>
              <span style="display: inline-block;width: calc(100% - 75px);overflow: hidden;"
                data-key="reqPhysician"></span>
            </div>
          </div> -->
          <table border="1" cellspacing="0" cellpadding="0" class="sample-tbl" style="width: 100%;">
            <tr>
              <td style="width: 400px;border-left: unset;">
                <span class="label-tit">检验目的：</span>
                <span data-key="examMotive"></span>
              </td>
              <td style="width: 190px;">
                <span class="label-tit">申请日期：</span>
                <span data-key="reqDate"></span>
              </td>
              <td style="width: 190px;border-right: unset;">
                <span class="label-tit">医生签名：</span>
                <span data-key="reqPhysician"></span>
              </td>
            </tr>
          </table>
          <div class="mt-4 bot-tip">
            <div class="label-tit">注意事项：</div>
            <div>一、请珍惜国家财产，本单勿移作其他用。</div>
            <div>二、若系再次送检的病例，务请注明以前的病理诊断和列号，以便查对。</div>
          </div>
        </div>
      </div>
    </div>
  </li>
</ul>