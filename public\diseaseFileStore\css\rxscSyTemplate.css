#rxscSyP1 .rt-sr-tit { width: 110px; }
#rxscSyP1 .sketch-map { 
  background: #fff;
  /* display: flex;
  align-items: center; */
  width: 325px;
  height: 177px;
}
#rxscSyP1 .map-img {
  position: relative;
  flex: 1;
  width: 320px;
  height: 140px;
}
#rxscSyP1 .map-img img {
  width: 100%;
  height: 100%;
}
#rxscSyP1 .map-color {
  padding-left: 8px;
  display: flex;
  align-items: center;
  border-top: 1px solid #C8D7E6;
  padding-top: 8px;
}
#rxscSyP1 .map-color .map-tl {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
  border-radius: 4px;
  padding: 4px;
  height: 20px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
#rxscSyP1 .map-color .map-tl + .map-tl {
  margin-left: 4px;
}
#rxscSyP1 .map-color .c-pot {
  width: 12px;
  height: 12px;
}
#rxscSyP1 .c-pot {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 1px solid #000;
  border-radius: 50%; 
  line-height: 1;
  text-align: center;
  font-size: 12px;
}
#rxscSyP1 .c-angle {
  border-radius: 0;
  border-left: 7px transparent solid;
  border-right: 7px transparent solid;
  border-bottom: 14px #303133 solid;
  border-top: 0 transparent solid;
  background: none !important;
}
#rxscSyP1 .map-color .c-angle {
  border-bottom-width: 12px;
}
#rxscSyP1 .c-pot.abs {
  position: absolute;
  cursor: pointer;
}
#rxscSyP1 .c-pot:not(.c-angle).abs:hover,
#rxscSyP1 .c-pot:not(.c-angle).abs.act {
  border-color: #FFF443;
}
#rxscSyP1 .c-pot.c-angle.abs:hover,
#rxscSyP1 .c-pot.c-angle.abs.act {
  border-bottom-color: #000;
}
#rxscSyP1 .c-angle .ft{
  text-align: left;
  margin-left: -3px;
  margin-top: 2px;
  display: block;
}
#rxscSyP1 .num-flag {
  color: #000;
  font-size: 12px;
  margin-left: 4px;
}
#rxscSyP1 .con-i {
  color: #303133;
  cursor: pointer;
  line-height: 22px;
}
#rxscSyP1 .con-i:hover, #rxscSyP1 .con-i.act {
  color: #1885F2;
}
#rxscSyP1 .s-map-con { 
  flex: 1;
  padding: 8px;
  overflow: auto;
  word-break: break-all;
  height: 177px;
  border-left: 1px solid #C8D7E6;
}
.tooltip-wrap {
  line-height: 20px;
  white-space: pre-wrap;
}
#rxscSyP1 .bz-item {
  display: flex;
  width: 100%;
}
#rxscSyP1 .bz-item + .bz-item {
  margin-top: 16px;
}
#rxscSyP1 .bz-side {
  width: 100px;
  text-align: right;
  color: #303133;
  font-weight: bold;
}
#rxscSyP1 .bz-detail {
  flex: 1;
}
#rxscSyP1 .bz-title {
  color: #000;
  margin-bottom: 8px;
}
#rxscSyP1 .bz-table {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#rxscSyP1 .bz-table-head {
  padding: 4px 8px;
  background: #EBEEF5;
  border-bottom: 1px solid #C8D7E6;
  color: #000;
}
#rxscSyP1 .bz-table-body {
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
}
#rxscSyP1 .bz-info {
  margin: 3px 0;
  margin-right: 30px;
  color: #000;
}
.hs-block.w-block {
  display: block !important;
} 
.hs-block.w-block .hight-block{
  border-top: 1px solid #C8D7E6;
  border-left: none;
}
.hs-block.w-block .light-block {
  padding: 4px 8px;
}
.hs-block.w-block .lb-row.lb-active > input:checked::after,
.hs-block.w-block .lb-row.lb-active > input:checked::before {
  content: unset !important;
}
.hs-block.w-block .light-block .w-con:not(.v) .r-i + .r-i {
  margin-left: 0;
}
.level-radio.w-con:not(.v) .r-i + .r-i {
  margin-left: 0;
}
.level-radio.w-con {
  padding-left: 15px;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  height: 28px;
  border-radius: 3px 0 0 3px;
}
.level-radio.w-con .r-i {
  margin-right: 15px;
}
.level-select select.rt-sr-s {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  padding-left: 5px;
}
.rxscSy-wrap *:focus {
  box-shadow: 0 0 7px 1px rgb(24,133,242,.7)!important;
}     
.rxscSy-wrap label:not(.lb-row) [type="radio"]:focus, 
.rxscSy-wrap label:not(.lb-row):not(.lb-new) [type="checkbox"]:focus {
  min-height: unset !important;
  height: 14px !important;
  box-shadow: 0 0 7px 1px rgb(24,133,242,.7)!important;
}
.rxscSy-wrap .lb-d .p-lb.w{
  display: inline-block;
  width: 70px;
  text-align: right;
}
.rxscSy-wrap .lb-d .rt-sr-lb {
  color: #303133;
}