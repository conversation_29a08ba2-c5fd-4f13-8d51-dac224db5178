$(function() {
  window.initHtmlScript = initHtmlScript;
})
var rtStructure = null;
var niceTypeList = {
  '1+1+1+1': '1',   //为所有选项属性nice-type用'+'拼接起来的值
  '1+2+1+1': '1',
  '1+3+1+1': '1',
  '1+2+2+2': '2',
  '1+2+3+3': '3',
  '1+3+3+3': '3',
};
var bwCheckedList = {
  "结肠": { 
    isChecked: true,
    isShow: true,
    className: '.sjc-checked',
    blockName: '.sjc-block'
  },
  "直肠": { 
    isChecked: false,
    isShow: false,
    className: '.zc-checked',
    blockName: '.zc-block'
  }
};
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }
  // resultData为空则报告为未填写过的状态
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  initJZCPage();
  if(!resultData.length) {
    setTimeout(function() {
      $('#jzcxr-rt-20').click();
    }, 200)
  }
  $('#jzcxr .jzcxr-checked').click(function(e) {
    e.preventDefault();
    let val = e.target.textContent.replace(/\ +/g, "").replace(/[\r\n]/g, "");
    for(var key in bwCheckedList) {
      if(val === key) {
        bwCheckedList[key].isShow = true;
      }else {
        bwCheckedList[key].isShow = false;
      }
    }
    showBlock();
  })
  $('#jzcxr .bw-inp').click(function(e) {
    e.stopPropagation();
    let val = $(this).val();
    for(var key in bwCheckedList) {
      if(val === key) {
        bwCheckedList[key].isShow = true;
      }else {
        bwCheckedList[key].isShow = false;
      }
    }
    let isChecked = $(this).is(":checked");
    bwCheckedList[val].isChecked = isChecked;
    showBlock();
    initBwBlock(val,isChecked);
  })
  // 回肠末端
  $('[name="sd-organ"]').change(function() {
    changeHCTail();
  })
  $('.edit-bw .sjc-site input[type="checkbox"]').change(function() {
    changeNiceType('.edit-bw .sjc-block','38','40');
  })
  $(".edit-bw .sjc-block [nice-type]").change(function() {
    changeNiceType('.edit-bw .sjc-block','38','40');
  })
  $(".edit-bw .zc-block [nice-type]").change(function() {
    changeNiceType('.edit-bw .zc-block','84','86');
  })
}
// 初始化
function initJZCPage() {
  if(($('#jzcxr-rt-20').is(':checked')) || ($('#jzcxr-rt-20').is(':checked') && $('#jzcxr-rt-21').is(':checked'))) {
    bwCheckedList['结肠'].isShow = true;
  }else if(!$('#jzcxr-rt-20').is(':checked') && $('#jzcxr-rt-21').is(':checked')) {
    bwCheckedList['结肠'].isShow = false;
    bwCheckedList['直肠'].isShow = true;
  }
  // 数量输入框和单选框添加事件
  $('#jzcxr-rt-119').change(function() {
    if($('[name="sjc-num"]:checked') && $('#jzcxr-rt-119').val()) {
      $('[name="sjc-num"]').prop('checked',false);
    }
  });
  $('[name="sjc-num"]').change(function() {
    $('#jzcxr-rt-119').val('');
  });
  $('#jzcxr-rt-127').change(function() {
    if($('[name="zc-num"]:checked') && $('#jzcxr-rt-127').val()) {
      $('[name="zc-num"]').prop('checked',false);
    }
  });
  $('[name="zc-num"]').change(function() {
    $('#jzcxr-rt-127').val('');
  });

  changeHCTail();
  showFooter();
  showBlock();
  changeNiceType('.edit-bw .sjc-block','38','40');
  changeNiceType('.edit-bw .zc-block','84','86');
}

// 显示页面底部内容
function showFooter() {
  let bwLeft = $('.bw-left input[type="checkbox"]:checked');
  $('.sjc-footer').css('display','none');
  $('.zc-footer').css('display','none');
  bwLeft.length > 1 ? $('.jzxcr-sort').css('display','inline-block') :  $('.jzxcr-sort').css('display','none');
  bwLeft.each((i,dom) => {
    showBlock($(dom).val());
    if($(dom).val() === '结肠') {
      $('.sjc-footer').css('display','block');
    }
    if($(dom).val() === '直肠') {
      $('.zc-footer').css('display','block');
    }
  })
}

// 判断NICE分型
function changeNiceType(className,idNum1,idNum2) {
  var ntList = [];
  var niceType = '';
  $(`${className} [nice-type]:checked`).each(function(i, dom) {
    var ntTxt = $(dom).attr('nice-type');
    ntList.push(ntTxt);
  })
  var ntKey = ntList.join('+');
  // if($(`#jzcxr-rt-${idNum1}`).val() === '10' && $(`#jzcxr-rt-${idNum2}`).val() === '8' && niceTypeList[ntKey]) {
  if(niceTypeList[ntKey]) {
    if(className === '.edit-bw .sjc-block') {
      if($('.edit-bw .sjc-site input[type="checkbox"]:checked').length) {
        niceType = 'type ' + niceTypeList[ntKey];
      }
    }else {
      niceType = 'type ' + niceTypeList[ntKey];
    }
  }
  if(className === '.edit-bw .sjc-block') {
    $('.jzc-inpval').val(niceType);
    $('.sjc-type').val(niceType);
  }else {
    $('.zc-inpval').val(niceType);
    $('.zc-type').val(niceType);
  }
}

// 切换显示的部位块及样式处理
function showBlock() {
  for(var key in bwCheckedList) {
    if(bwCheckedList[key].isShow) {
      $(bwCheckedList[key].blockName).css('display','inline-block');
      $(bwCheckedList[key].className).addClass('b-eb');
    }else if(!bwCheckedList[key].isShow) {
      $(bwCheckedList[key].blockName).css('display','none');
      $(bwCheckedList[key].className).removeClass('b-eb');
    }
  }
}

// 是否勾选结肠或直肠
function initBwBlock(val,isChecked) {
  if(val === '结肠') {
    if(isChecked) {
      $('.edit-bw .sjc-init').prop('checked',isChecked);
    }else {
      $('.edit-bw .sjc-block [type="radio"]').prop('checked',isChecked);
      $('.edit-bw .sjc-block [type="checkbox"]').prop('checked',isChecked);
      $('.jzc-inpval').val('');
      $('.sjc-type').val('');
    }
  }
  if(val === '直肠') {
    $('.edit-bw .zc-init').prop('checked',isChecked);
    if(isChecked) {
    }else {
      $('.edit-bw .zc-block [type="radio"]').prop('checked',isChecked);
      $('.edit-bw .zc-block [type="checkbox"]').prop('checked',isChecked);
      $('.zc-inpval').val('');
      $('.zc-type').val('');
    }
  }
  showFooter();
}

// 回肠末端
function changeHCTail() {
  if($('[name="sd-organ"]:checked').val()) {
    $('.hc-item').css('display','block');
    $('.hc-item [type="checkbox"]').prop('checked',true);
    $('.hc-inp').prop('disabled',false);
    $('.hc-inp').val('15');
  }else {
    $('.hc-item').css('display','none');
    $('.hc-item [type="checkbox"]').prop('checked',false);
    $('.hc-inp').val('');
  }
}

// 处理黏膜字符串
function handlerNmStr(ml,ky,zw) {
  let findObj = {
    '未见': [],
    '可见': [],
  };
  
  let findStr = '';
  let findNoStr = '';
  let radioStr = '';
  for(let key in findObj) {
    if(key === ml) {
      findObj[key].push('糜烂');
    }
    if(key === ky) {
      findObj[key].push('溃疡');
    }
    if(key === zw) {
      findObj[key].push('肿物');
    }
  }
  findNoStr = findObj['未见'].length ? '未见' + findObj['未见'].join('、') + (findObj['可见'].length ? '，' : '') : '';
  findStr = findObj['可见'].length ? '可见' + findObj['可见'].join('、') : '';
  radioStr = findNoStr + findStr;
  return radioStr;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 生成pacs报告描述
function createPacsDescFun() {
  var description = '';
  var strList = [];
  var sjcStrList = [];
  var zcStrList = [];
  var allList = [];
  $('#jzcxr .jzcxr-item .rt-sr-w:not([pid])').each(function(pIdx, par) {
    var parent = $(par);
    var pid = parent.attr('id');
    // 插镜情况
    if(pid === 'jzcxr-rt-2') {
      if($('[name="cj-state"]:checked').val()) {
        strList.push($('[name="cj-state"]:checked').val());
      }
    }
    // 回肠末端
    var hcList = [];
    var hcStr = '';
    if(pid === 'jzcxr-rt-4') {
      if($('[name="sd-organ"]:checked').val()) {
        hcStr += '插镜至回肠末端';
      }
    }
    if(pid === 'jzcxr-rt-6') {
      if($('[name="sd-organ"]:checked').val()) {
        if($('[name="jl"]:checked').val()) {
          hcStr += '插镜至回肠末端约' + $('#jzcxr-rt-9').val() + 'cm';
          hcList.push(hcStr);
        }
        if($('[name="nm"]:checked').val()) {
          hcList.push('回肠末端黏膜' + $('[name="nm"]:checked').val());
        }
        strList.push(hcList.length ? hcList.join(',') : '');
      }
    }

    // 回盲瓣、阑尾拼接
    var hmblwStr = '';
    if(pid === 'jzcxr-rt-12') {
      if(!$('[name="nk"]:checked').val() && $('[name="xt"]:checked').val()) {
        hmblwStr += '回盲瓣形态' + $('[name="xt"]:checked').val();
      }
      hmblwStr ? strList.push(hmblwStr) : '';
    }
    if(pid === 'jzcxr-rt-15') {
      if($('[name="xt"]:checked').val()) {
        hmblwStr += '回盲瓣形态' + $('[name="xt"]:checked').val() + ',';
      }
      if($('[name="nk"]:checked').val()) {
        hmblwStr += '阑尾内口' + $('[name="nk"]:checked').val();
      }
      strList.push(hmblwStr);
    }

    // 结肠
    if(pid === 'jzcxr-rt-20' && $('[name="bw-sjc"]:checked').val()) {
      var sjcStr = '';
      // 位置
      if($('.edit-bw [name="sjc-site"]:checked').length) {
        var sjcSite = [];
        $('.edit-bw [name="sjc-site"]:checked').each((i,dom) => {
          sjcSite.push($(dom).val());
        })
        sjcStr += sjcSite.join('/');
      }else if($('.edit-bw .sjc-checked [name="bw"]:checked').val()) {
        sjcStr += $('.edit-bw .sjc-checked [name="bw"]:checked').val();
      }
      // 数量
      let sjcNumVal = $('.edit-bw [name="sjc-num"]:checked').val() || $('#jzcxr-rt-119').val() && $('#jzcxr-rt-119').val() + '处' || '';
      sjcStr += '可见' + sjcNumVal;
      // 息肉大小
      let sxrList1 = [],sxrList2 = [],sxrList = [];
      $('.sxr-left1').val() && sxrList1.push($('.sxr-left1').val());
      $('.sxr-right1').val() && sxrList1.push($('.sxr-right1').val());
      $('.sxr-left2').val() && sxrList2.push($('.sxr-left2').val());
      $('.sxr-right2').val() && sxrList2.push($('.sxr-right2').val());
      sxrList1.length && sxrList.push(sxrList1.join('*') + 'mm');
      sxrList2.length && sxrList.push(sxrList2.join('*') + 'mm');
      sjcStr += sxrList.length ? '大小约' +  sxrList.join('-') + '的息肉' : '';
      sjcStr += $('.edit-bw [name="sjc-color"]:checked').val() ? ',' + '颜色' + $('.edit-bw [name="sjc-color"]:checked').val() : '';
      sjcStrList.push(sjcStr);

      // 表面结构、表面血管
      sjcStr = '';
      var sjcBMJG = $('.edit-bw [name="sjc-bmjg"]:checked').val();
      var sjcBMXG = $('.edit-bw [name="sjc-bmxg"]:checked').val();
      sjcStr += sjcBMJG ? '表面结构' + sjcBMJG + ',' : '';
      sjcStr += sjcBMXG ? '表面血管' + sjcBMXG : '';
      sjcStr ? sjcStrList.push(sjcStr) : '';

      // 黏膜
      sjcStr = '';
      var sjcML = $('.edit-bw [name="sjc-ml"]:checked').val() || '';
      var sjcKY = $('.edit-bw [name="sjc-ky"]:checked').val() || '';
      var sjcZW = $('.edit-bw [name="sjc-zw"]:checked').val() || '';
      var sNmRadioStr = handlerNmStr(sjcML,sjcKY,sjcZW);
      var sjcNm = $('.edit-bw [name="sjc-nm"]:checked');
      if(sjcNm.length) {
        sjcNm.each((i,dom) => {
          let dot = (i+1 === sjcNm.length && !$('[name="bw-zc"]:checked').val()) ? '。' : '';
          sjcStr = '';
          sjcStr += $(dom).val() + ',' + sNmRadioStr + dot;
          sjcStrList.push(sjcStr);
        })
      }else {
        sjcStrList.push(sNmRadioStr);
      }
      // allList.push(...strList,...sjcStrList);
    }
    
    // 直肠
    if(pid === 'jzcxr-rt-21' && $('[name="bw-zc"]:checked').val()) {
      var zcStr = '';
      var zcStr = '';
      // 位置
      var zcSite = $('.edit-bw [name="zc-site"]:checked').val() || '';
      // 黏膜
      if(zcSite === '没有') {
        var zcML = $('.edit-bw [name="zc-ml"]:checked').val() || '';
        var zcKY = $('.edit-bw [name="zc-ky"]:checked').val() || '';
        var zcZW = $('.edit-bw [name="zc-zw"]:checked').val() || '';
        var zNmRadioStr = handlerNmStr(zcML,zcKY,zcZW);
        if($('.edit-bw [name="zc-nm"]:checked').length) {
          $('.edit-bw [name="zc-nm"]:checked').each((i,dom) => {
            zcStr += $(dom).val() + ',' + zNmRadioStr;
            zcStrList.push('直肠' + zcStr);
          })
        }else {
          zcStrList.push(zNmRadioStr);
        }
      }

      zcStr = '';
      zcStr = $('.edit-bw .zc-checked [name="bw-zc"]:checked').val() || '';
      // 数量
      let zcNumVal = $('.edit-bw [name="zc-num"]:checked').val() || $('#jzcxr-rt-127').val() && $('#jzcxr-rt-127').val() + '处' || '';
      zcStr += zcNumVal && '可见' + zcNumVal;
      let zcList1 = [],zcList2 = [],zcList = [];
      $('.zxr-left1').val() && zcList1.push($('.zxr-left1').val());
      $('.zxr-right1').val() && zcList1.push($('.zxr-right1').val());
      $('.zxr-left2').val() && zcList2.push($('.zxr-left2').val());
      $('.zxr-right2').val() && zcList2.push($('.zxr-right2').val());
      zcList1.length && zcList.push(zcList1.join('*') + 'mm');
      zcList2.length && zcList.push(zcList2.join('*') + 'mm');
      zcStr += zcList.length ? '大小约' +  zcList.join('-') + '的息肉' : '';
      zcStr += $('.edit-bw [name="zc-color"]:checked').val() ? ',' + '颜色' + $('.edit-bw [name="zc-color"]:checked').val() : '';
      zcStr !== '直肠' ? zcStrList.push(zcStr) : '';

      // 表面结构、表面血管
      zcStr = '';
      var zcBMJG = $('.edit-bw [name="zc-bmjg"]:checked').val();
      var zcBMXG = $('.edit-bw [name="zc-bmxg"]:checked').val();
      zcStr += zcBMJG ? '表面结构' + zcBMJG + ',' : '';
      zcStr += zcBMXG ? '表面血管' + zcBMXG + '。' : '';
      zcStr ? zcStrList.push(zcStr) : '';
      // allList.push(...strList,...zcStrList);
    }
  })
  allList.push(...strList,...sjcStrList,...zcStrList);
  description = allList.join(';\n');
  console.log(description);
  return description;
}

// 生成pacs报告结论
function createImpressionFun() {
  let impression = '';
  let jzcVal = $('.jzc-inpval').val() || '';
  let zcVal = $('.zc-inpval').val() || '';
  if(jzcVal && zcVal) {
    impression += '（1）结肠息肉（NICE分型：'+jzcVal+'）;\n';
    impression += '（2）直肠息肉（NICE分型：'+zcVal+'）;\n';
  }else if(jzcVal) {
    impression += '结肠息肉（NICE分型：'+jzcVal+'）;';
  }else if(zcVal) {
    impression += '直肠息肉（NICE分型：'+zcVal+'）;';
  }
  // console.log(impression);
  return impression;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = createPacsDescFun();
  rtStructure.impression = createImpressionFun();
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}