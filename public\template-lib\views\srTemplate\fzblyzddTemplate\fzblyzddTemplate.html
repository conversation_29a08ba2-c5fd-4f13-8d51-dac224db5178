<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<link rel="stylesheet" href="/template-lib/views/srTemplate/fzblyzddTemplate/fzblyzddTemplate.css">
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/plugins/dayjs.min.js"></script>
<script src="/template-lib/plugins/elementUI/vue.min.js"></script>
<script src="/template-lib/plugins/jsBarcode.all.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/srTemplate/fzblyzddTemplate/fzblyzddTemplate.js"></script>

<ul class="t-pg" style="height: 100%;overflow: auto;">
  <li class="page" id="fzblyzdd1">
    <table border=0 cellSpacing=0 cellPadding=0 width="100%" class="rt-sr-header">
      <thead>
        <tr>
          <td width="100%" colspan="5">
            <div align="center" class="page-title" style="position: relative;">
              <span class="tl">分子医嘱单</span>
              <div style="position: absolute; left:0; top:0" align="left">
                病理号：<img id="outterGmId-code"/>
                <p style="margin-top: 4px;">蜡块号：{{ candleId }}</p>
              </div>
              <div class="patient-code">
                <img id="patLocalId-code"/>
                <p style="margin-top: 2px;">{{publicInfo.patLocalId}}</p>
                <p style="margin-top: 2px;">{{orgExamInfo.name}}/{{orgExamInfo.sex}}/{{orgExamInfo.age}}/{{orgExamInfo.inpatientNo}}</p>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td width="20%" class="font-size-16">姓名：{{orgExamInfo.name}}</td>
          <td width="16%" class="font-size-16">性别：{{orgExamInfo.sex}}</td>
          <td width="20%" class="font-size-16">年龄：{{orgExamInfo.age}}</td>
          <td width="16%" class="font-size-16">床号：{{orgExamInfo.bedNo}}</td>
          <td width="28%" class="font-size-16">住院号：{{orgExamInfo.inpatientNo}}</td>
        </tr>
        <tr>
          <td width="36%" colspan="2" class="font-size-16">科室：{{orgExamInfo.reqDeptName}}</td>
          <td width="36%" colspan="2" class="font-size-16">申请时间：{{orderDate}}</td>
          <td width="28%" class="font-size-16">门诊号：{{orgExamInfo.outpatientNo}}</td>
        </tr>
        <tr>
          <td width="100%" colspan="5" class="font-size-16" style="border-bottom: 1px solid #999999;">标记物：{{ marker }}</td>
        </tr>
      </thead>
    </table>
    <div class="rt-sr-body">
      <div class="side2">
        <div class="td" class="pt-16 font-size-16">浓度：_________________________</div>
        <div class="td" align="right" class="pt-16 font-size-16">肿瘤比例：{{ tumorProp || '_________________________' }}</div>
      </div>
      <template v-if="normalExamRpt || examRecord">
        <div>
          <div class="td"><div class="content-title">常规报告：</div></div>
        </div>
        <div v-if="examRecord">
          <div class="td">
            <span class="content-label">肉眼所见：</span>
            <pre><div class="content-item" v-html="examRecord"></div></pre>			
          </div>
        </div>
        <div v-if="normalExamRpt.description">
          <div class="td">
            <span class="content-label">镜下所见：</span>
            <pre><div class="content-item" v-html="normalExamRpt.description"></div></pre>				
          </div>
        </div>
        <div v-if="normalExamRpt.examParam">
          <div class="td">
            <span class="content-label">特殊检查：</span>
            <pre><div class="content-item" v-html="normalExamRpt.examParam"></div></pre>			
          </div>
        </div>
        <div v-if="normalExamRpt.impression">
          <div class="td">
            <span class="content-label">病理诊断：</span>
            <pre><div class="content-item" v-html="normalExamRpt.impression"></div></pre>
          </div>
        </div>
      </template>
      <template v-if="extendExamRptList.length > 0">
        <div>
          <div class="td"><div class="content-title">补充报告：</div></div>
        </div>
        <span v-for="(item, index) in extendExamRptList" :key="index">
          <div>
            <div class="td"><div>补充报告[{{index+1}}]：</div></div>
          </div>
          <div v-if="item.description">
            <div class="td">
              <span class="content-label">镜下所见：</span>
              <pre><div class="content-item" v-html="item.description"></div></pre>
            </div>
          </div>
          <div v-if="item.examParam">
            <div class="td">
              <span class="content-label">特殊检查：</span>
              <pre><div class="content-item" v-html="item.examParam"></div></pre>
            </div>
          </div>
          <div v-if="item.impression">
            <div class="td">
              <span class="content-label">病理诊断：</span>
              <pre><div class="content-item" v-html="item.impression"></div></pre>
            </div>
          </div>
        </span>
      </template>
    </div>
    <div class="rt-sr-footer">
      打印时间：<span class="printTime">{{ printTime }}</span>
      <span class="rt-page-counter">第{CURPAGE}页，共{TOTALPAGE}页</span>
    </div>
  </li>
</ul>