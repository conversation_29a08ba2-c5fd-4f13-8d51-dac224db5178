
$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var bzConClone = null;
var liveConClone = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //模版是否保存
var nurseList = [];
var doctorList = [];
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      inintPreview('preview')
    } else {
      inintPreview('edit')
      pageInit()
    }
  }
}

function pageInit(){
  initBzCloneEle();
  hideBlock()
  window.getNurseList ? toNurseList() : '';
  window.getNurseList ? toDoctorList() : '';
  window.getCommonUseList ? getCommonList('NURSE_RESCUE_PROCESS_MODE') : '';
  window.getCommonUseList ? getCommonList('NURSE_RESCUE_PROCESS_RESULT') : '';
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    window.getDateTime() ? toDateTime() : '';
    addBzHandler();
    addzjHandler();
    window.getCommonUseList ? addDictionaryHandler('','NURSE_RESCUE_PROCESS_MODE') : '';
    window.getCommonUseList ? addDictionaryHandler('','NURSE_RESCUE_PROCESS_RESULT') : '';
  } else {
    displaygyContent();
  }
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    //执行一个laydate实例
    // laydate.render({
    //   elem: '#hlqj-rt-0001-001',//指定元素
    //   type: 'datetime',
    //   value: ''
    // });
    // laydate.render({
    //   elem: '#hlqj-rt-0001-002',//指定元素
    //   type: 'datetime',
    //   value: ''
    // });
    // laydate.render({
    //   elem: '#hlqj-rt-0005',//指定元素
    //   type: 'datetime',
    //   value: !isSavedReport ? new Date() : ''
    // });
    // addLayDateTime('hlqj-rt-0005')
    addLayDateTime('hlqj-rt-0001-001')
    addLayDateTime('hlqj-rt-0001-002')
    setupDateSelection('hlqj-rt-0001-001')
    setupDateSelection('hlqj-rt-0001-002')
    // setupDateSelection('hlqj-rt-0005')
    $('#hlqj-rt-0001-001').on('focus', function(value) {
      if($('#hlqj-rt-0001-001').val()){
        return
      }
      $('#hlqj-rt-0001-001').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
    })
    $('#hlqj-rt-0001-002').on('focus', function(value) {
      if($('#hlqj-rt-0001-002').val()){
        return
      }
      $('#hlqj-rt-0001-002').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
    })
    $('#hlqj-rt-0007-001').on('change', function(value) {
      var is_addChoice = $("#hlqj-rt-0007-001").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#hlqj-rt-0007-001-001',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#hlqj-rt-0007-002').on('change', function(value) {
      var is_addChoice = $("#hlqj-rt-0007-002").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#hlqj-rt-0007-002-001',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#hlqj-rt-0007-003').on('change', function(value) {
      var is_addChoice = $("#hlqj-rt-0007-003").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#hlqj-rt-0007-003-001',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#hlqj-rt-0007-004').on('change', function(value) {
      var is_addChoice = $("#hlqj-rt-0007-004").is(':checked');
      if (is_addChoice==true){
        laydate.render({
          elem: '#hlqj-rt-0007-004-001',//指定元素
          type: 'datetime',
          value: new Date(),
          done: function(value, date, endDate){
            rescueResult()
          }
        });
      }
      rescueResult()
    })
    $('#hlqj-rt-0007-001-001').on('blur', function(value) {
      rescueResult()
    })
    $('#hlqj-rt-0007-002-001').on('blur', function(value) {
      rescueResult()
    })
    $('#hlqj-rt-0007-003-001').on('blur', function(value) {
      rescueResult()
    })
    $('#hlqj-rt-0007-004-001').on('blur', function(value) {
      rescueResult()
    })
    addLayDateTime('hlqj-rt-0007-001-001')
    addLayDateTime('hlqj-rt-0007-002-001')
    addLayDateTime('hlqj-rt-0007-003-001')
    addLayDateTime('hlqj-rt-0007-004-001')
    setupDateSelection('hlqj-rt-0007-001-001')
    setupDateSelection('hlqj-rt-0007-002-001')
    setupDateSelection('hlqj-rt-0007-003-001')
    setupDateSelection('hlqj-rt-0007-004-001')
  })
}

function rescueResult(){
  let str = '',obj = {
    '停止CPR': '停止CPR：' + $('#hlqj-rt-0007-001-001').val(),
    '自主循环恢复': '自主循环恢复：' + $('#hlqj-rt-0007-002-001').val(),
    '宣告临床死亡': '宣告临床死亡：' + $('#hlqj-rt-0007-003-001').val(),
    '家属要求放弃抢救': '家属要求放弃抢救:' + $('#hlqj-rt-0007-004-001').val(),
  };
  if($("#hlqj-rt-0007-001").is(':checked') == false){
    delete obj['停止CPR']
  }
  if($("#hlqj-rt-0007-002").is(':checked') == false){
    delete obj['自主循环恢复']
  }
  if($("#hlqj-rt-0007-003").is(':checked') == false){
    delete obj['宣告临床死亡']
  }
  if($("#hlqj-rt-0007-004").is(':checked') == false){
    delete obj['家属要求放弃抢救']
  }
  let list = [];
  Object.entries(obj).forEach(([key, value]) => {
    list.push(value)
  })
  str = list.join(',');
  $("#qiangjiuEffect").val(str);
}

function inintPreview(type){
  $(`[data-type]`).hide();
  $(`[data-type=${type}]`).show();
  if(type==='preview'){    
    $('[data-key]').each(function(){
      let key = $(this).attr('data-key')
      if(idAndDomMap){
        let result = [],list = []
        Object.keys(idAndDomMap).forEach(idKey=>{
          if(idKey.startsWith(key)){
           let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value:'';
           if(value){
            result.push(value)
           }
          }
        })
        // console.log('result',result)
        if(key === 'hlqj-rt-0002' || key === 'hlqj-rt-0006' || key === 'hlzj-rt-0004-003' || key === 'hlzj-rt-0004-004'){
          result.shift();
          $(this).html(result.join(','))
        }else{
          $(this).html(result.join(','))
        }
        
        this.style.color = '#000'  
      }
    })
    initBzCloneEle();
    // 回显镇静记录
    allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    console.log('allResData',allResData);
    if(allResData && allResData.length) {
      var zjData = allResData.filter(bzItem => bzItem.id === 'hlqj-zjlist-1')
      console.log('zjData',zjData);
      if(zjData && zjData.length) {
        var zjList = zjData[0].child || [];
        zjList.forEach((item) => {
          var paneId = item.id.replace('hlqj-zjlist-', '');
          addzjHandler(paneId)
        })
      }
      var bzData = allResData.filter(bzItem => bzItem.id === 'hlqj-bzlist-1')
      // console.log('bzData',bzData);
      if(bzData && bzData.length) {
        var bzList = bzData[0].child || [];
        bzList.forEach((item) => {
          var paneId = item.id.replace('hlqj-bzlist-', '');
          addBzHandler(paneId)
        })
      }
    }
  }else{
    $(`.live-con[data-type="preview"]`).remove()
  }
}

function displaygyContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'hlqj-bzlist-1')
    // console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('hlqj-bzlist-', '');
        addBzHandler(paneId)
      })
    }
    var zjData = allResData.filter(bzItem => bzItem.id === 'hlqj-zjlist-1')
    // console.log('zjData',zjData);
    if(zjData && zjData.length) {
      var zjList = zjData[0].child || [];
      zjList.forEach((item) => {
        var paneId = item.id.replace('hlqj-zjlist-', '');
        addzjHandler(paneId)
      })
    }
    var zjhData = allResData.filter(bzItem => bzItem.id === 'hlqj-rt-0002');
    if(zjhData && zjhData.length){
      var zjhList = zjhData[0].child || [];
      if(zjhList && zjhList.length) {
        var bzList = zjhList;
        bzList.forEach((item) => {
          // console.log('item',item)
          window.getCommonUseList ? addDictionaryHandler(item.id,'NURSE_RESCUE_PROCESS_MODE') : '';
          if(item.id === 'hlqj-rt-0002-900'){
            $('.second-content').show();
          }
        })
      }
    }else{
      addDictionaryHandler('','NURSE_RESCUE_PROCESS_MODE')
    }
    var resultData = allResData.filter(bzItem => bzItem.id === 'hlqj-rt-0006');
    if(resultData && resultData.length){
      var resultList = resultData[0].child || [];
      if(resultList && resultList.length) {
        var bzList = resultList;
        bzList.forEach((item) => {
          // console.log('item',item)
          window.getCommonUseList ? addDictionaryHandler(item.id,'NURSE_RESCUE_PROCESS_RESULT') : '';
        })
      }else{
        window.getCommonUseList ? addDictionaryHandler('','NURSE_RESCUE_PROCESS_RESULT') : '';
      }
    }else{
      window.getCommonUseList ? addDictionaryHandler('','NURSE_RESCUE_PROCESS_RESULT') : '';
      // console.log('resultData',resultData)
    }
  } else {
    curElem.find('.advice-list .advice-item').hide();
  }
  // 监听表单内所有input, textarea, select的input或change事件  
  $('#hlqj1').on('input change blur', 'input, textarea, select', function() { 
    // console.log('input change blur', this) 
    // 向父页面发送消息
    window.parent.postMessage({
      message: 'formChangeNotice',
      data: '表单内容已修改'
    }, '*');
  });  
}

function toDateTime(){
  var dateTime = window.getDateTime();
  $('#hlqj-rt-0005').val(dateTime);
  // console.log('dateTime',dateTime)
}

function getCommonList(type){
  let nurseList = window.getCommonUseList({name: type});
  let html = '',id = '',pid = '',name = '',num = 1,code='';
  if(type === 'NURSE_RESCUE_PROCESS_MODE'){
    id = 'hlqj-rt-0002-00';
    pid = 'hlqj-rt-0002';
    name = 'rescueHowToHandle';
    num = 1;
  }else if(type === 'NURSE_RESCUE_PROCESS_RESULT'){
    id = 'hlqj-rt-0006-00';
    pid = 'hlqj-rt-0006';
    name = 'rescueHandleResult';
    num = 1;
    code = '455'
  }
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="${id +( t + num)}"><input type="checkbox" class="rt-sr-w" name="${name}" value="${nurseList[t].value}" id="${id + (t + num)}" pid="${pid}" rt-sc="pageId:hlzj1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;code:${code}" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    if(type === 'NURSE_RESCUE_PROCESS_MODE'){
      $('#rescueHowToHandle').html(html);
    }else if(type === 'NURSE_RESCUE_PROCESS_RESULT'){
      $('#rescueHandleResult').html(html);
    }  
  }
}

// 添加过字典内容
function addDictionaryHandler(oldPaneId,type) {
  var newPaneBlock = '';
  if(type === 'NURSE_RESCUE_PROCESS_MODE'){
    newPaneBlock = $('#rescueHowToHandle');
  }else if(type === 'NURSE_RESCUE_PROCESS_RESULT'){
    newPaneBlock = $('#rescueHandleResult');
  }
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = '';
    if(type === 'NURSE_RESCUE_PROCESS_MODE'){
      wCon = $('#rescueHowToHandle').find("[rt-sc]");
    }else if(type === 'NURSE_RESCUE_PROCESS_RESULT'){
      wCon = $('#rescueHandleResult').find("[rt-sc]");
    }
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function toNurseList(){
  let list = window.getNurseList({});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
 
  initInpAndSel('hlqj-rt-0003', userList,optHandler);
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    // console.log('publicInfo',publicInfo)
    $('#hlqj-rt-0003').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#hlqj-rt-0003').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
function toDoctorList(){
  let list = window.getNurseList({},'','doctor');
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  doctorList = userList;
}

// 添加生命体征记录,oldPaneId已保存过的
function addzjHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.table-content .live-con .bz-tr').length;
  var activeTab = 'hlqj-zjlist-' + paneId; // 表格每列的id
  var newPaneBlock = appendLiveHtml(paneId, bzLen,oldPaneId);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '生命体征',
      name: '生命体征',
      pid: 'hlqj-zjlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('生命体征' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('.table-content .live-con .bz-tr').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc'); // 获取rt-sc的节点
      var scArr = sc.split(';');//获取属性值
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      }; // 值放在此
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    // $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function appendLiveHtml(paneId, bzLen,oldPaneId) {
  var reg = new RegExp('000', 'ig');
  var content = liveConClone.replace(reg, paneId); 
  $('.table-content .live-con').append(content);
  $('#hlqj-zjlist-'+paneId+'-1').html((bzLen + 1));
  // layui.use(['laydate', 'table'], function(){
  //   var laydate = layui.laydate;
  //   laydate.render({
  //     elem: '#hlqj-zjlist-'+paneId+'-2',//指定元素
  //     type: 'datetime',
  //     value: oldPaneId ? '' : ''
  //   });
  // })
  addLayDateTime('hlqj-zjlist-'+paneId+'-2')
  setupDateSelection('hlqj-zjlist-'+paneId+'-2')
  $('#hlqj-zjlist-'+paneId+'-2').on('focus', function(value) {
    if($('#hlqj-zjlist-'+paneId+'-2').val()){
      return
    }
    $('#hlqj-zjlist-'+paneId+'-2').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  var newPaneBlock =  $('.table-content .live-con .bz-tr[tab-target="hlqj-zjlist-'+paneId+'"]');
  return newPaneBlock;
}

function delzjFuc(vm, paneId){
  var allSiblings = $(vm).parent().parent().siblings();
  allSiblings.each(function(index) {
      // console.log($(this).children().first(),index); // 输出：first 和 third
      $(this).children().first().html(index + 1)
  });
  $(vm).parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}

// 处理新增处理医嘱的html
function appendBzHtml(paneId, bzLen,oldPaneId) {
  console.log('paneId',paneId,oldPaneId)
  var reg = new RegExp('000', 'ig');
  var content = bzConClone.replace(reg, paneId);  
  $('.advice-list').append(content);
  $('#hlqj-bzlist-'+paneId+'-1').html('医嘱记录' + (bzLen + 1));
  // layui.use(['laydate', 'table'], function(){
  //   var laydate = layui.laydate;
  //   laydate.render({
  //     elem: '#hlqj-bzlist-'+paneId+'-2',//指定元素
  //     type: 'datetime',
  //     value: !oldPaneId ? '' : ''
  //   });
  // })
  addLayDateTime('hlqj-bzlist-'+paneId+'-2')
  setupDateSelection('hlqj-bzlist-'+paneId+'-2')
  $('#hlqj-bzlist-'+paneId+'-2').on('focus', function(value) {
    if($('#hlqj-bzlist-'+paneId+'-2').val()){
      return
    }
    $('#hlqj-bzlist-'+paneId+'-2').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  initInpAndSel('hlqj-bzlist-'+paneId+'-3', doctorList);
  initInpAndSel('hlqj-bzlist-'+paneId+'-4', nurseList);
  var newPaneBlock = $('.advice-list .advice-item[tab-target="hlqj-bzlist-'+paneId+'"] .advice-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}

// 添加处理医嘱,oldPaneId已保存过的
function addBzHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.advice-content').length;
  console.log('bzLen',bzLen,paneId)
  var activeTab = 'hlqj-bzlist-' + paneId;
  var newPaneBlock = appendBzHtml(paneId, bzLen,oldPaneId);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '处理医嘱',
      name: '处理医嘱',
      pid: 'hlqj-bzlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('医嘱记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.advice-list .advice-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('oldIdAndDom',childOldIdAndDom)
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function delTab(vm, paneId){
  var allSiblings = $(vm).parent().parent().parent().siblings();
  // console.log('allSiblings',allSiblings)
  allSiblings.each(function(index) {
      console.log($(this).children().children()[0],index); // 输出：first 和 third
      $(this).children().children().children().each(function(cIndex) {
        // console.log('$(this)',$(this));
        if(cIndex === 0){
          $(this).html('医嘱记录'+(index + 1))
        }
      })
  });
  $(vm).parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}

function initBzTigger(newPaneBlock, oldFlag) {
  // console.log('oldFlag',newPaneBlock);
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  }
}

function initBzCloneEle() {
  var bzCon = curElem.find('.advice-list .advice-item').clone(true);
  bzConClone = '<div class="advice-item" tab-target="hlqj-bzlist-000">' + bzCon.html() +'</div>';
  // console.log('bzConClone',bzConClone);
  curElem.find('.advice-list').html('');
  var liveCon = curElem.find('.table-content .live-con .bz-tr').clone(true);
  liveConClone = '<tr class="bz-tr rt-sr-w"  tab-target="hlqj-zjlist-000" id="hlqj-zjlist-000" pid="hlqj-zjlist-1" rt-sc="pageId:hlqj1;name:镇静中记录;wt:;desc:镇静中记录;vt:;pvf:;">'+liveCon.html()+'</tr>';
  curElem.find('.table-content .live-con').html('');
}
function hideBlock() {
  let el = $('#hlqj-rt-0002-900')
  hide(el.checked)
  function hide(val){
    if(val){
      $('.second-content').show()
     }else{
      $('.second-content').hide()
    }
  } 
  el.on('change',function(){
    hide(this.checked)
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }