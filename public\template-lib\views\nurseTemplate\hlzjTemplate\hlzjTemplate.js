$(function() {
  window.initHtmlScript = initHtmlScript;
})

var druyList = [
  {title: '口服',id: '1'},
  {title: '滴鼻',id: '2'},
  {title: '静脉',id: '3'}
]
var agentOralList = [];
var agentSnortList = [];
var agentMainlineList = [];
var nurseList = []; 

var rtStructure = null;
var curElem = null;
var gyConClone = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //模版是否保存
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      inintPreview('preview')
    } else {
      inintPreview('edit')
      pageInit()
    }
  }
}

function inintPreview(type){
  $(`[data-type]`).hide();
  $(`[data-type=${type}]`).show();
  if(type==='preview'){    
    $('[data-key]').each(function(){
      let key = $(this).attr('data-key')
      if(idAndDomMap){
        let result = [],list = []
        Object.keys(idAndDomMap).forEach(idKey=>{
          if(idKey.startsWith(key)){
           let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value:'';
           if(value){
            result.push(value)
           }
          }
        })
        // console.log('result',result)
        if(key === 'hlzj-rt-0004-001' || key === 'hlzj-rt-0004-002' || key === 'hlzj-rt-0004-003' || key === 'hlzj-rt-0004-004'){
          result.shift();
          $(this).html(result.join(','))
        }else{
          $(this).html(result.join(','))
        }
        
        this.style.color = '#000'  
      }
    })
    initgyCloneEle();
    // 回显镇静记录
    allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
    if(allResData && allResData.length) {
      var bzData = allResData.filter(bzItem => bzItem.id === 'hlzj-gylist-1')
      // console.log('bzData',bzData);
      if(bzData && bzData.length) {
        var bzList = bzData[0].child || [];
        bzList.forEach((item) => {
          var paneId = item.id.replace('hlzj-gylist-', '');
          addgyHandler(paneId)
        })
      }
      var zjData = allResData.filter(bzItem => bzItem.id === 'hlzj-zjlist-1')
      // console.log('zjData',zjData);
      if(zjData && zjData.length) {
        var zjList = zjData[0].child || [];
        zjList.forEach((item) => {
          var paneId = item.id.replace('hlzj-zjlist-', '');
          addzjHandler(paneId)
        })
      }
    }
  }else{
    $(`.live-con[data-type="preview"]`).remove()
  }
}

function pageInit() {
  initgyCloneEle();
 
  window.getNurseList ? toNurseList() : '';
  window.getCommonUseList ? toCommonUseList('NURSE_CONSCIOUSNESS') : '';
  window.getCommonUseList ? toCommonUseList('NURSE_INSTRUCTION_ACTIVITY') : '';
  window.getCommonUseList ? toCommonUseList('NURSE_AGENT_ORAL') : '';
  window.getCommonUseList ? toCommonUseList('NURSE_AGENT_SNORT') : '';
  window.getCommonUseList ? toCommonUseList('NURSE_AGENT_MAINLINE') : '';
  window.getCommonUseList ? getCommonList('NURSE_CALM_AFTER_PROCESS_MODE') : '';
  window.getCommonUseList ? getCommonList('NURSE_CALM_RESULT') : '';
  // console.log('publicInfo',publicInfo)
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    window.getDateTime() ? toDateTime() : '';
    addgyHandler();
    addzjHandler();
    window.getCommonUseList ? addDictionaryHandler('','consciousness') : '';
    window.getCommonUseList ? addDictionaryHandler('','instructionActivity') : '';
    window.getCommonUseList ? addDictionaryHandler('','calmDownHowToHandle') : '';
    window.getCommonUseList ? addDictionaryHandler('','calmDownResult') : '';
    var calmdownLocation = window.localStorage.getItem('calmdownLocation') ? JSON.parse(window.localStorage.getItem('calmdownLocation')) : '';
    // console.log('calmdownLocation',calmdownLocation);
    if(calmdownLocation === "放射科") {
      $('#hlzj-rt-0002-001').click()
    }else if(calmdownLocation === "镇静室") {
      $('#hlzj-rt-0002-002').click()
    }else if(calmdownLocation === "住院部") {
      $('#hlzj-rt-0002-003').click()
    }
    $("#hlzj-rt-0004-001-001").attr('checked',true);
    $("#hlzj-rt-0004-002-001").attr('checked',true);
    $("#hlzj-rt-0004-0012-003").attr('checked',true);
    $("#hlzj-rt-0004-0013-003").attr('checked',true);
    $("#hlzj-rt-0004-0014-003").attr('checked',true);
  } else {
    
    displaygyContent();
  }
  // layui.use(['laydate', 'table'], function(){
  //   var laydate = layui.laydate;
  //   //执行一个laydate实例
  //   laydate.render({
  //     elem: '#hlzj-rt-0001-005',//指定元素
  //     type: 'datetime',
  //     value: '',
  //   });

  //   laydate.render({
  //     elem: '#hlzj-rt-0003',//指定元素
  //     type: 'datetime',
  //     value: ''
  //   });
  //   laydate.render({
  //     elem: '#hlzj-rt-0004-005',//指定元素
  //     type: 'datetime',
  //     value: ''
  //   });
  // })
  addLayDateTime('hlzj-rt-0001-005')
  addLayDateTime('hlzj-rt-0004-005')
  addLayDateTime('hlzj-rt-0004-0010')
  setupDateSelection('hlzj-rt-0001-005')
  setupDateSelection('hlzj-rt-0004-005')
  setupDateSelection('hlzj-rt-0004-0010')
  $('#hlzj-rt-0001-005').on('focus', function(value) {
    if($('#hlzj-rt-0001-005').val()){
      return
    }
    $('#hlzj-rt-0001-005').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  $('#hlzj-rt-0003').on('focus', function(value) {
    if($('#hlzj-rt-0003').val()){
      return
    }
    $('#hlzj-rt-0003').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  $('#hlzj-rt-0004-005').on('focus', function(value) {
    if($('#hlzj-rt-0004-005').val()){
      return
    }
    $('#hlzj-rt-0004-005').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })

  $('#hlzj-rt-0004-0010').on('focus', function(value) {
    if($('#hlzj-rt-0004-0010').val()){
      return
    }
    $('#hlzj-rt-0004-0010').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })

}
function displaygyContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'hlzj-gylist-1')
    // console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('hlzj-gylist-', '');
        addgyHandler(paneId)
      })
    }
    var zjData = allResData.filter(bzItem => bzItem.id === 'hlzj-zjlist-1')
    // console.log('zjData',zjData);
    if(zjData && zjData.length) {
      var zjList = zjData[0].child || [];
      zjList.forEach((item) => {
        var paneId = item.id.replace('hlzj-zjlist-', '');
        addzjHandler(paneId)
      })
    }
    var zjhData = allResData.filter(bzItem => bzItem.id === 'hlzj-rt-0004');
    if(zjhData && zjhData.length&&zjhData[0].child){
      var zjhList = zjhData[0].child || [];
      var symptoms = zjhList.filter(bzItem => bzItem.id === 'hlzj-rt-0004-001')
      if(symptoms && symptoms.length) {
        var bzList = symptoms[0].child || [];
        bzList.forEach((item) => {
          // console.log('item',item)
          window.getCommonUseList ? addDictionaryHandler(item.id,'consciousness') : '';
        })
      }else{
        window.getCommonUseList ? addDictionaryHandler('','consciousness') : '';
      }
      var handleData = zjhList.filter(metalItem => metalItem.id === 'hlzj-rt-0004-002')
      if(handleData && handleData.length) {
        var metalList = handleData[0].child || [];
        metalList.forEach((item) => {
          // console.log('item',item)
          window.getCommonUseList ? addDictionaryHandler(item.id,'instructionActivity') : '';
        })
      }else{
        window.getCommonUseList ? addDictionaryHandler('','instructionActivity') : '';
      }
      
    var irritabilityData = zjhList.filter(metalItem => metalItem.id === 'hlzj-rt-0004-003');
    // console.log('irritabilityData',irritabilityData)
    if(irritabilityData && irritabilityData.length&&irritabilityData[0].child) {
      var metalList = irritabilityData[0].child || [];
      metalList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList ? addDictionaryHandler(item.id,'calmDownHowToHandle') : '';
      })
    }else{
      window.getCommonUseList ? addDictionaryHandler('','calmDownHowToHandle') : '';
    }
    var calmDownData = zjhList.filter(metalItem => metalItem.id === 'hlzj-rt-0004-004');
    // console.log('calmDownData',calmDownData)
    if(calmDownData && calmDownData.length&&calmDownData[0].child) {
      var metalList = calmDownData[0].child || [];
      metalList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList ? addDictionaryHandler(item.id,'calmDownResult') : '';
      })
    }else{
      window.getCommonUseList ? addDictionaryHandler('','calmDownResult') : '';
    }
    }else{
      addDictionaryHandler('','consciousness') 
      addDictionaryHandler('','instructionActivity') 
      addDictionaryHandler('','calmDownHowToHandle') 
      addDictionaryHandler('','calmDownResult')
    }
   
  } else {
    curElem.find('.advice-list .advice-item').hide();
  } 
  // 监听表单内所有input, textarea, select的input或change事件  
  $('#hlzj1').on('input change blur', 'input, textarea, select', function() { 
    // console.log('input change blur', this) 
    // 向父页面发送消息
    window.parent.postMessage({
      message: 'formChangeNotice',
      data: '表单内容已修改'
    }, '*');
  });  
}

function toDateTime(){
  var dateTime = window.getDateTime();
  $('#hlzj-rt-0003').val(dateTime);
  // console.log('dateTime',dateTime)
}
function toNurseList(){
  let list = window.getNurseList({deptCode: publicInfo.userInfo&&publicInfo.userInfo.param&&publicInfo.userInfo.param.deptCode || ''});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
  initInpAndSel('hlzj-rt-0005', userList,optHandler  );
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#hlzj-rt-0005').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#hlzj-rt-0005').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, cb,id) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList,dropdown)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
     typeof cb === 'function' && cb(obj)
      // console.log('title',obj.title,id)
      if(id){
        $(`#${id}`).val('');
        if(obj.title === '口服'){
          //先拿到dropdown.render对象
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentOralList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }else if(obj.title === '滴鼻'){
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentSnortList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }else if(obj.title === '静脉'){
          var inst = dropdown.render();
          //然后用它去调reload方法，并将下拉框中的数据置为空
          inst.reload({
              elem: `#${id}`
              , data: []
          });
          //然后再重载新的数据进去就可以了
          inst.reload({
              elem: `#${id}`
              , data: agentMainlineList
              , show: true
              , click: function (obj) {
                  this.elem.val(obj.title);
              },
              style: 'width: 186px'
          });
        }
      }
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
  // if(id){
    // 使用dropdown.setData方法重新赋值
    
  // }
}
function toCommonUseList(type){
  let nurseList = window.getCommonUseList({name: type});
  let html = '',id = '',pid = '',name = '';
  if(type === 'NURSE_CONSCIOUSNESS'){
    id = 'hlzj-rt-0004-001-00';
    pid = 'hlzj-rt-0004-001';
    name = 'consciousness';
  }else if(type === 'NURSE_INSTRUCTION_ACTIVITY'){
    id = 'hlzj-rt-0004-002-00';
    pid = 'hlzj-rt-0004-002';
    name = 'instructionActivity';
  }
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="${id +( t + 1)}"><input type="radio" class="rt-sr-w" name="${name}" value="${nurseList[t].value}" id="${id + (t + 1)}" pid="${pid}" rt-sc="pageId:hlzj1;name:${nurseList[t].value};wt:5;desc:${nurseList[t].value};vt:;pvf:;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    if(type === 'NURSE_CONSCIOUSNESS'){
      $('#allergy-consciousness').html(html);
    }else if(type === 'NURSE_INSTRUCTION_ACTIVITY'){
      $('#allergy-instructionActivity').html(html);
    }else if(type === 'NURSE_AGENT_ORAL'){
      agentOralList = nurseList.map((item)=>{
        return {
          title: item.code,
          id: item.code
        }
      });
      // console.log('agentOralList',agentOralList)
    }else if(type === 'NURSE_AGENT_SNORT'){
      agentSnortList = nurseList.map((item)=>{
        return {
          title: item.code,
          id: item.code
        }
      });
      // console.log('agentOralList',agentOralList)
    }else if(type === 'NURSE_AGENT_MAINLINE'){
      agentMainlineList = nurseList.map((item)=>{
        return {
          title: item.code,
          id: item.code
        }
      });
      // console.log('agentOralList',agentOralList)
    }
  }
}

function getCommonList(type){
  let nurseList = window.getCommonUseList({name: type});
  let html = '',id = '',pid = '',name = '';
  if(type === 'NURSE_CALM_AFTER_PROCESS_MODE'){
    id = 'hlzj-rt-0004-003-00';
    pid = 'hlzj-rt-0004-003';
    name = 'calmDownHowToHandle';
  }else if(type === 'NURSE_CALM_RESULT'){
    id = 'hlzj-rt-0004-004-00';
    pid = 'hlzj-rt-0004-004';
    name = 'calmDownResult';
  }
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="${id +( t + 1)}"><input type="checkbox" class="rt-sr-w" name="${name}" value="${nurseList[t].value}" id="${id + (t + 1)}" pid="${pid}" rt-sc="pageId:hlzj1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    if(type === 'NURSE_CALM_AFTER_PROCESS_MODE'){
      $('#allergy-calmDownHowToHandle').html(html);
    }else if(type === 'NURSE_CALM_RESULT'){
      $('#allergy-calmDownResult').html(html);
    }  
  }
}

// 添加镇静中记录,oldPaneId已保存过的
function addzjHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.table-content .live-con .bz-tr').length;
  var activeTab = 'hlzj-zjlist-' + paneId; // 表格每列的id
  var newPaneBlock = appendLiveHtml(paneId, bzLen,oldPaneId);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '镇静中',
      name: '镇静中',
      pid: 'hlzj-zjlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('镇静中' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('.table-content .live-con .bz-tr').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc'); // 获取rt-sc的节点
      var scArr = sc.split(';');//获取属性值
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      }; // 值放在此
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    // $('.bz-list .bz-wrap').show();
    initgyTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function appendLiveHtml(paneId, bzLen,oldPaneId) {
  var reg = new RegExp('000', 'ig');
  var content = liveConClone.replace(reg, paneId);  
  // console.log('content',content)
  $('.table-content .live-con').append(content);
  $('#hlzj-zjlist-'+paneId+'-1').html((bzLen + 1));
  // layui.use(['laydate', 'table'], function(){
  //   var laydate = layui.laydate;
  //   laydate.render({
  //     elem: '#hlzj-zjlist-'+paneId+'-2',//指定元素
  //     type: 'datetime',
  //     value: oldPaneId ? '' : ''
  //   });
  // })
  addLayDateTime('hlzj-zjlist-'+paneId+'-2')
  setupDateSelection('hlzj-zjlist-'+paneId+'-2')
  $('#hlzj-zjlist-'+paneId+'-2').on('focus', function(value) {
    if($('#hlzj-zjlist-'+paneId+'-2').val()){
      return
    }
    $('#hlzj-zjlist-'+paneId+'-2').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  var newPaneBlock =  $('.table-content .live-con .bz-tr[tab-target="hlzj-zjlist-'+paneId+'"]');
  return newPaneBlock;
}

function delzjFuc(vm, paneId){
  var allSiblings = $(vm).parent().parent().siblings();
  allSiblings.each(function(index) {
      // console.log($(this).children().first(),index); // 输出：first 和 third
      $(this).children().first().html(index + 1)
  });
  $(vm).parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}

function initgyCloneEle() {
  var gyCon = curElem.find('.drug-list .drug-item').clone(true);
  gyConClone = '<div class="drug-item" tab-target="hlzj-gylist-000">' + gyCon.html() +'</div>';
  // console.log('gyConClone',gyConClone);
  curElem.find('.drug-list').html('');
  var liveCon = curElem.find('.table-content .live-con .bz-tr').clone(true);
  liveConClone = '<tr class="bz-tr rt-sr-w"  tab-target="hlzj-zjlist-000" id="hlzj-zjlist-000" pid="hlzj-zjlist-1" rt-sc="pageId:hlzj1;name:镇静中记录;wt:;desc:镇静中记录;vt:;pvf:;">'+liveCon.html()+'</tr>';
  curElem.find('.table-content .live-con').html('');
}

// 添加过字典内容
function addDictionaryHandler(oldPaneId,type) {
  var newPaneBlock = '';
  if(type === 'consciousness'){
    newPaneBlock = $('#allergy-consciousness');
  }else if(type === 'instructionActivity'){
    newPaneBlock = $('#allergy-instructionActivity');
  }else if(type === 'calmDownHowToHandle'){
    newPaneBlock = $('#allergy-calmDownHowToHandle');
  }else if(type === 'calmDownResult'){
    newPaneBlock = $('#allergy-calmDownResult');
  }
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = '';
    if(type === 'consciousness'){
      wCon = $('#allergy-consciousness').find("[rt-sc]");
    }else if(type === 'instructionActivity'){
      wCon = $('#allergy-instructionActivity').find("[rt-sc]");
    }else if(type === 'calmDownHowToHandle'){
      wCon = $('#allergy-calmDownHowToHandle').find("[rt-sc]");
    }else if(type === 'calmDownResult'){
      wCon = $('#allergy-calmDownResult').find("[rt-sc]");
    }
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initgyTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function delTab(vm, paneId){
  var allSiblings = $(vm).parent().parent().parent().siblings();
  // console.log('allSiblings',allSiblings)
  allSiblings.each(function(index) {
      console.log($(this).children().children()[0],index); // 输出：first 和 third
      $(this).children().children().children().each(function(cIndex) {
        // console.log('$(this)',$(this));
        if(cIndex === 0){
          $(this).html('记录'+(index + 1))
        }
      })
  });
  $(vm).parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}

// 添加处理医嘱,oldPaneId已保存过的
function addgyHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var gyLen = $('.drug-content').length;
  // console.log('gyLen',gyLen,paneId)
  var activeTab = 'hlzj-gylist-' + paneId;
  var newPaneBlock = appendgyHtml(paneId, gyLen,oldPaneId);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '处理医嘱',
      name: '处理医嘱',
      pid: 'hlzj-gylist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('医嘱记录' + (gyLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.drug-list .drug-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('oldIdAndDom',childOldIdAndDom)
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initgyTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function initgyTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      // console.log('id999',id)
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  }
}

// 处理新增处理医嘱的html
function appendgyHtml(paneId, gyLen,oldPaneId) {
  // console.log('paneId',paneId,oldPaneId)
  var reg = new RegExp('000', 'ig');
  var content = gyConClone.replace(reg, paneId);  
  // console.log('content',content)
  $('.drug-list').append(content);
  $('#hlzj-gylist-'+paneId+'-1').html('记录' + (gyLen + 1));
  initInpAndSel('hlzj-gylist-'+paneId+'-2', druyList, 186,'hlzj-gylist-'+paneId+'-3');
  $('#hlzj-gylist-'+paneId+'-3').on('click', function() {
      console.log('Input field clicked!');
      let title = $('#hlzj-gylist-'+paneId+'-2').val();
      let id = 'hlzj-gylist-'+paneId+'-3';
      var dropdown = layui.dropdown;
      if(title === '口服'){
        //先拿到dropdown.render对象
        var inst = dropdown.render();
        //然后用它去调reload方法，并将下拉框中的数据置为空
        inst.reload({
            elem: `#${id}`
            , data: []
        });
        //然后再重载新的数据进去就可以了
        inst.reload({
            elem: `#${id}`
            , data: agentOralList
            , show: true
            , click: function (obj) {
                this.elem.val(obj.title);
            },
            style: 'width: 186px'
        });
      }else if(title === '滴鼻'){
        var inst = dropdown.render();
        //然后用它去调reload方法，并将下拉框中的数据置为空
        inst.reload({
            elem: `#${id}`
            , data: []
        });
        //然后再重载新的数据进去就可以了
        inst.reload({
            elem: `#${id}`
            , data: agentSnortList
            , show: true
            , click: function (obj) {
                this.elem.val(obj.title);
            },
            style: 'width: 186px'
        });
      }else if(title === '静脉'){
        var inst = dropdown.render();
        //然后用它去调reload方法，并将下拉框中的数据置为空
        inst.reload({
            elem: `#${id}`
            , data: []
        });
        //然后再重载新的数据进去就可以了
        inst.reload({
            elem: `#${id}`
            , data: agentMainlineList
            , show: true
            , click: function (obj) {
                this.elem.val(obj.title);
            },
            style: 'width: 186px'
        });
      }
  });
  // layui.use(['laydate', 'table'], function(){
  //   var laydate = layui.laydate;
  //   laydate.render({
  //     elem: '#hlzj-gylist-'+paneId+'-5',//指定元素
  //     type: 'datetime',
  //     offset: 'bottom',
  //     value: oldPaneId ? '' : '',
  //   });
  // })
  addLayDateTime('hlzj-gylist-'+paneId+'-5')
  setupDateSelection('hlzj-gylist-'+paneId+'-5')
  $('#hlzj-gylist-'+paneId+'-5').on('focus', function(value) {
    if($('#hlzj-gylist-'+paneId+'-5').val()){
      return
    }
    $('#hlzj-gylist-'+paneId+'-5').val(getCurDateAndTime().curDate + ' ' + getCurDateAndTime().curTime);
  })
  var newPaneBlock = $('.drug-list .drug-item[tab-target="hlzj-gylist-'+paneId+'"] .drug-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }