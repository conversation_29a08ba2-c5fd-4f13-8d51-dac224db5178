$(function() {
  window.initHtmlScript = initHtmlScript;
})
var mmUnit = 'mm';
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  // initTabContentHandler();
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }

  keydown_to_tab('asdfd1');

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 获取描述
function getDescription() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let description = '';
  let blockStrList1 = [];
  let blockParId1 = ['asdfd-rt-1','asdfd-rt-10','asdfd-rt-13','asdfd-rt-26','asdfd-rt-31'];
  let blockParId2 = ['asdfd-rt-39'];
  let blockParId3 = ['asdfd-rt-45','asdfd-rt-48','asdfd-rt-53','asdfd-rt-56'];
  $('#asdfd1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      if(blockParId1.includes(parData.id)) {
        blockStrList1.push(parData.name);
        childD1.map(function(cItem1) {
          let childD2 = cItem1.child || [];
          if(cItem1.id === 'asdfd-rt-32') {
            blockStrList1.push(cItem1.val);
          }
          childD2.map(function(cItem2) {
            let unit = ''; 
            if(cItem1.id === 'asdfd-rt-35') {
              unit = '分流方向：L→R分流';
            }else if(cItem1.id === 'asdfd-rt-37') {
              unit = '';
            }else {
              unit = mmUnit;
            }
            blockStrList1.push(cItem1.name + cItem2.val + unit);
          })
        })
      }
      else if(blockParId2.includes(parData.id)) {
        blockStrList1.push(parData.name);
        childD1.map(function(cItem1) {
          let childD2 = cItem1.child || [];
          if(childD2.length) {
            childD2.map(function(cItem2) {
              blockStrList1.push(cItem1.name + cItem2.val);
            })
          }
        })
      }
      else if(blockParId3.includes(parData.id)) {
        blockStrList1.push(parData.name);
        childD1.map(function(cItem1) {
          blockStrList1.push(cItem1.name);
          let childD2 = cItem1.child || [];
          if(childD2.length) {
            childD2.map(function(cItem2) {
              blockStrList1.push(cItem2.name);
            })
          }
        })
      }
    }
  });
  description = blockStrList1.length ? blockStrList1.join('，') + '。': '';
  return description;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = getDescription();
  // rtStructure.impression = '';
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}