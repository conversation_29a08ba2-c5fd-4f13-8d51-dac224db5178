
/* 间距 */
.mb-6 {
  margin-bottom: 6px;
}
.ml-27 {
  margin-left: 27px;
}
/* 颜色 */
.bg-blue {
  background: #D6DFF7!important;
}
.bg-gray {
  background: #EAEAEA;
}
/* 边框类 */
.bor-b {
  border-bottom: 1px solid #000!important;
}
.bor-r {
  border-right: 1px solid #000!important;
}
.bor-lr {
  border-left: 1px solid #000!important;
  border-right: 1px solid #000!important;
}
.bor-rb {
  border-right: 1px solid #000!important;
  border-bottom: 1px solid #000!important;
}
/* 宽度 */
.wd-17 {
  width: 20px!important;
}
.wd-23 {
  width: 23px!important;
}
.wd-26 {
  width: 26px!important;
}
.wd-27 {
  width: 27px!important;
  text-align: right;
}
.wd-32 {
  width: 32px!important;
}
.wd-35 {
  width: 35px!important;
  text-align: center;
}
.wd-38 {
  width: 38px;
}
.wd-41 {
  width: 41px!important;
}
.wd-42 {
  width: 42px!important;
}
.wd-60 {
  width: 60px;
}
.wd-70 {
  width: 70px;
}
.wd-150 {
  width: 150px;
}
.wd-213 {
  width: 213px!important;
  text-align: center;
}
.wd-216 {
  width: 216px!important;
}
.wd-260 {
  width: 260px;
}

/* gdfysczt页面 */
#gdfysczt1 {
  width: 780px;
  min-height: 1100px;
  position: relative;
  margin: 0 auto;
  padding-bottom: 40px;
  font-size: 14px;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
}
#gdfysczt1 .gdfysczt-h {
  text-align: center;
}
#gdfysczt1 .gdfysczt-h img {
  margin-left: -1px;
  margin-top: -1px;
}
#gdfysczt1 .gdfysczt-h h1 {
  font-size: 22px;
  line-height: 22px;
  font-weight: bold;
  color: #000;
  margin-top: 12px;
}
#gdfysczt1 .gdfysczt-h h2 {
  font-size: 20px;
  line-height: 20px;
  color: #000;
  margin-top: 12px;
}
#gdfysczt1 .gdfysczt-h .top-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 32px;
  margin: 8px 50px 0;
  border-top: 1px solid #606266 ;
  border-bottom: 1px solid #606266 ;
  font-size: 16px;
}
#gdfysczt1 .info-lb {
  color: #303133;
}
#gdfysczt1 .info-con {
  color: #000;
}
#gdfysczt1 .rpt-info {
  display: flex;
  justify-content: space-between;
  margin: 0 50px;
}
#gdfysczt1 .gdfysczt-b {
  margin-top: 12px;
  padding: 0 50px;
}
#gdfysczt1 .con-flex {
  display: flex;
  justify-content: space-between;
}
#gdfysczt1 .box-left,#gdfysczt1 .box-right {
  position: relative;
  width: 334px;
  border: 1px solid #000000;
}
#gdfysczt1 .box-header {
  height: 32px;
  line-height: 32px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  background: #191975;
}
#gdfysczt1 .qd-block {
  font-size: 12px;
  text-align: center;
  margin: 8px 0 5px;
}
#gdfysczt1 .qd-sel {
  font-size: 12px;
  border: 1px solid #d7dae2;
}
#gdfysczt1 .serial-no {
  width: 150px;
  height: 16px;
  position: absolute;
  top: 56%;
  right: -67px;
  transform: rotate(-90deg);
  font-size: 12px;
}
#gdfysczt1 .echart-data {
  width: 320px;
  height: 329px;
  margin: auto;
}
#gdfysczt1 .echart-data img {
  width: 100%;
  height: 100%;
}
#gdfysczt1 .tb-title {
  font-size: 18px;
  line-height: 22px;
  text-align: center;
  color: #333333;
  margin-bottom: 4px;
}
#gdfysczt1 .no-bor {
  border: none;
}
#gdfysczt1 .table-data,#gdfysczt1 .pta-box {
  border: 1px solid #000000;
}
#gdfysczt1 .table-data tr {
  color: #333333;
  font-weight: 400!important;
}
#gdfysczt1 .table-data tbody tr td:first-child {
  border-right: 1px solid #000;
}
#gdfysczt1 .table-data tbody tr td:first-child {
  line-height: 24px;
}
#gdfysczt1 .tb-inp {
  display: inline-block;
  width: 32px;
  box-sizing: border-box;
  min-height: 20px!important;
  border: none!important;
  outline: none;
  background: transparent!important;
}
#gdfysczt1 .table-h {
  height: 36px;
  background: #D6DFF7;
  padding: 8px 6px;
  font-weight: bold;
  border: 1px solid #000;
}
#gdfysczt1 .multi-tb tr td {
  width: 38px;
  height: 24px;
  border: 1px solid #000;
  border-top: none;
  border-right: none;
}
#gdfysczt1 .cytljz-tb tr td {
  height: 24px;
}
#gdfysczt1 .gdfysczt-b .remark-lb {
  display: inline-block;
  min-width: 54px;
  font-size: 18px;
  color: #828282;
}
#gdfysczt1 .remark-inp {
  width: 480px;
  height: 196px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
  padding: 5px 15px;
}
.icon-block {
  width: 134px;
  margin-left: 12px;
  border: 1px solid #000;
  color: #333;
  font-size: 10px;
  text-align: center;
}
#gdfysczt1 .icon-h {
  height: 24px;
  background: #D6DFF7;
  font-size: 16px;
}
#gdfysczt1 .ctff-box {
  text-align: left;
  padding: 5px 4px;
}
#gdfysczt1 .gdfysczt-f {
  position: absolute;
  left: 50px;
  right: 50px;
  bottom: 5px;
  border-top: 1px solid #F2F2F2;
  padding-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#gdfysczt1 .gdfysczt-f input[type="text"] {
  height: 36px;
  border-radius: 3px;
  padding: 0 4px;
  font-size: 14px;
  border: 1px solid #DCDFE6;
}
#gdfysczt1 .icon-tip {
  display: flex;
  justify-content: space-between;
  padding: 2px 4px;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}