<template>
  <div :class="['report-main','ear-wrap',createPdfFlag?'ear-print':'']" id="report-main" ref="reportWrap" v-loading="saveFlag">
    <h3 v-if="!saveFlag && (!reportType || !templateList[reportType])" style="padding:20px">找不到报告类型{{reportType}}</h3>
    <disease-layout 
      v-else
      :showHeader="false"
      :readonly="false"
      :setClsName="!createPdfFlag"
      :patientInfo="patientInfo" 
      headTitle="影像所见"
      footTitle=""
      :showFooter="!createPdfFlag && !isFromYunPacs && !sourceType"
    >
      <div slot="body" style="height:100%;">
        <!-- 纯声测听 -->
        <cyct-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="cyctReportTypeList.includes(reportType)"
        ></cyct-report>

        <!-- 纯声言语 -->
        <yysbl-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="yysblReportTypeList.includes(reportType)"
        ></yysbl-report>

        <!-- 常规声导抗类/声导抗鼓膜穿孔 -->
        <sdkgmck-report
          ref="gmckDom"
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="reportType==='S001'"
        ></sdkgmck-report>

        <!-- 常规声导抗类/声导抗鼓膜完整 -->
        <sdkgmwz-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="reportType==='S002'"
        ></sdkgmwz-report>

        <!-- 常规声导抗类/声导抗鼓室镫骨 -->
        <sdkgsdg-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="reportType==='S003'"
        ></sdkgsdg-report>

        <!-- 宽频声导抗类/声导抗DPOAE -->
        <sdkdpoae-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="reportType==='S004'"
        ></sdkdpoae-report>

        <!-- 宽频声导抗类/声导抗宽频 -->
        <sdkkp-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="reportType==='S005'"
        ></sdkkp-report>

        <!-- 视频眼震电图（VNG）报告 -->
        <vng-report
          :patInfo="patInfo"
          :reportInfo="reportInfo"
          :reportData="reportData"
          :reportResult="reportResult"
          :reportType="reportType"
          v-if="reportType==='V000'"
        ></vng-report>
      </div>
      <div slot="footer-btn">
        <template v-if="localQueryParams && localQueryParams.entryType !== '0'">
          <el-button
            size="mini"
            type="primary"
            class="btn-reset btn-blue"
            icon="el-icon-edit"
            @click="srCommonOuterOptHandler({type:'4'})"
          >编写</el-button>
        </template>
        <template v-else>
          <el-button
            id="submitSrBtn"
            size="mini"
            type="primary"
            class="btn-reset btn-blue"
            icon="el-icon-position"
            :disabled="submitFlag"
            v-if="!isFromYunPacs || ypacsRptInfo.examStatus < 60 || ypacsRptInfo.examStatus == 65"
            @click="submitForm({isSubmit:'1'})"
          >提交</el-button>
          <el-button
            id="saveSrBtn"
            size="mini"
            type="primary"
            class="btn-reset btn-green"
            icon="el-icon-tickets"
            :disabled="submitFlag"
            @click="submitForm({isSubmit:'0'})"
          >保存</el-button>
        </template>
        <el-button
          id="submitSrBtn"
          size="mini"
          type="primary"
          class="btn-reset btn-blue"
          icon="el-icon-position"
          :disabled="submitFlag"
          v-if="!isFromYunPacs || ypacsRptInfo.examStatus <= 70"
          @click="submitForm({isSubmit:'2'})"
        >确认</el-button>
      </div>
    </disease-layout>

    <!-- <div class="opera-btn" id="opera-btn" v-show="reportType&&templateList[reportType]">
      <div class="btn-item" v-if="isEditMode" @click="saveRptHandler">保存</div>
      <div class="btn-item" v-if="!isEditMode" @click="updateHandler">修改</div>
      <div class="btn-item" v-if="!isEditMode" @click="toPrint">打印</div>
    </div> -->
  </div>
</template>

<script>
import diseaseLayout from '@/components/diseaseLayout.vue';
import cyctReport from './cyctReport';   //纯声测听
import yysblReport from './yysblReport'; //纯声言语
import sdkgmckReport from './sdkgmckReport'; //常规声导抗类/声导抗鼓膜穿孔
import sdkgmwzReport from './sdkgmwzReport'; //常规声导抗类/声导抗鼓膜完整
import sdkgsdgReport from './sdkgsdgReport'; //常规声导抗类/声导抗鼓室镫骨
import sdkdpoaeReport from './sdkdpoaeReport'; //宽频声导抗类/声导抗DPOAE
import sdkkpReport from './sdkkpReport'; //宽频声导抗类/声导抗宽频
import vngReport from './vngReport'; //视频眼震电图（VNG）报告

import api from '@/config/api.js';
import { request } from '@/utils/common.js';
import generateReportMixin from '@/mixins/generateReport.js'
import newDiseaseReportMixin from '@/mixins/newDiseaseReport.js'

export default {
  name: "earReport",
  components: {diseaseLayout, cyctReport, yysblReport, sdkgsdgReport, sdkgmwzReport, sdkgmckReport, sdkdpoaeReport, 
    sdkkpReport, vngReport},
  mixins: [generateReportMixin, newDiseaseReportMixin],
  data() {
    return {
      // examNO: '0004050762',
      reportType: '',
      examNo: '',
      index: 0,
      reportNo: 0,
      patInfo: {},
      reportInfo: {},
      reportData: [],
      reportResult: {},
      resultList: [],
      templateList: {
        'C001': '省二-纯声测听裸耳',
        'C002': '省二-纯声测听声场助听',
        'C003': '省二-言语裸耳',
        'C004': '省二-言语声场助听',

        'C005': '福州市第二总医院-纯声测听裸耳',
        'C006': '福州市第二总医院-纯声测听声场助听',
        'C007': '福州市第二总医院-言语裸耳',
        'C008': '福州市第二总医院-言语声场助听',

        'C009': '福州市第二总医院妇幼保健院-纯声测听裸耳',
        'C010': '福州市第二总医院妇幼保健院-纯声测听声场助听',
        'C011': '福州市第二总医院妇幼保健院-言语裸耳',
        'C012': '福州市第二总医院妇幼保健院-言语声场助听',

        'S001': '省二-声导抗鼓膜穿孔',
        'S002': '省二-声导抗鼓膜完整',
        'S003': '省二-声导抗鼓室镫骨',
        'S004': '省二-声导抗DPOAE',
        'S005': '省二-声导抗宽频',
        'V000': '省二-视频眼震电图（VNG）',
      },
      cyReportTypeList: ['C001', 'C002', 'C003', 'C004', 'C005', 'C006', 'C007', 'C008', 'C009', 'C010', 'C011', 'C012'], // 纯声类模板的id列表
      cyctReportTypeList: ['C001', 'C002', 'C005', 'C006', 'C009', 'C010'], // 属于纯声测听模板的id列表
      yysblReportTypeList: ['C003', 'C004', 'C007', 'C008', 'C011', 'C012'], // 属于纯声言语模板的id列表
      saveFlag: false,
      patientInfo: {
        sex: ''
      },
      resultFormData: [],
      createPdfFlag: false,
      submitFlag: false,
      print: ''
    }
  },
  computed: {
    resParams() {
      return this.$store.state.generateReport.resParams;
    },
    isEditMode() {
      return this.$store.state.generateReport.isEditMode;
    }
  },
  mounted() {
    let {entryType = '0', print, examNo, idx, busId} = this.localQueryParams;
    this.examNo = examNo || busId || '';
    this.index = idx || 0;
    this.print = print;
    this.$store.commit('generateReport/setEditMode', entryType === '0');  //除了0都是非编辑状态
    this.pageInit();
  },
  methods: {
    async pageInit() {
      // 获取地址栏用户信息
      this.saveFlag = true;
      await this.getPatientInfo();
      let examInfo = await this.getExamInfoForSR();
      if(examInfo) {
        this.patientInfo = {...this.patientInfo, ...examInfo};
      }
      this.resultFormData = await this.getData();

      let params = { examNo: this.patientInfo.examNo, fileType: 'RptImg', reportNo: this.patientInfo.docNo || this.patientInfo.reportNo || '1' };
      if(!this.isFromYunPacs) {
        if(this.patientInfo.patDocId) {
          await this.getReportDoc();
          await this.$store.dispatch('report/getReportImageList', { params, vm: this });
        }
      } else {
        if(this.patientInfo.patDocId) {
          await this.$store.dispatch('report/getReportImageList', { params, vm: this });
        }
        window.top.postMessage({
          message: 'srLoadSuccess',
          data: {}
        }, '*');
      }

      this.getReportDetail();
    },
    async getReportDetail() {
      let reportJson = this.docOrign;
      let { patInfo = {}, reportInfo = {}, reportData = [], result = [], updateFlag} = reportJson || {};
      console.log('reportJson', reportJson);
      if(this.isPreview) {
        reportInfo = {
          reportType: this.patientInfo.patDocId
        }
      }
      if(this.isEditMode && !updateFlag) {
        reportInfo.reportStaff = '万建';
      }
      this.patInfo = patInfo;
      this.reportInfo = reportInfo;
      this.$set(this.reportInfo, 'examStaff', this.reportInfo.examStaff || this.reportInfo.reportStaff || '');
      this.reportData = reportData;
      if(reportInfo.reportType === 'S005') {
        this.reportData = {...reportJson};
      }
      if(this.cyReportTypeList.includes(reportInfo.reportType)) {
        this.reportResult = result[0] || {};
      } else {
        this.reportResult = result;
      }
      this.reportType = reportInfo.reportType; 
      this.reportNo = reportInfo.reportNo;
      // this.reportType = 'S005';
      if(!this.reportType || !this.templateList[this.reportType]) {
        this.$message.error('找不到报告类型');
        this.saveFlag = false;
        return;
      }
      document.title = this.templateList[this.reportType];
      window.sessionStorage.removeItem('srResult');
      this.saveFlag = false;

      this.$nextTick(() => {
        setTimeout(() => {
          if(['1', '3'].includes(this.print)) {
            this.viewToPrintPdf();
          }
          if(this.print === '2') {
            this.toPrint();
          }
          if(this.reportType === 'V000') {
            let patternLoadedStatus = this.localQueryParams.entryType !== '0' ? '1' : '2';
            document.querySelector('#app')?.setAttribute('pattern-loaded', patternLoadedStatus);
          }
        }, 500)
      })
    },

    async submitForm(subParams) {
      let {isSubmit, newMode = false, toRedis} = subParams;
      if(toRedis) {
        return;
      }
      if(isSubmit === '2' && !this.isEditMode) {
        // 确认并上传pdf
        this.viewToPrintPdf();
        return;
      }
      let bool = true;
      if(!toRedis && this.isEditMode) {
        bool = await this.saveRptHandler();
      }
      if(!bool) {
        return 'stop';
      }
      this.submitFlag = true;
      let data = {
        patternName: this.templateList[this.reportType],
        optType: isSubmit,
        description: '',
        impression: this.resParams && this.resParams.rptImpression ? this.resParams.rptImpression : '',
        recommendation: this.resParams && this.resParams.rptRecommendation ? this.resParams.rptRecommendation : '',
        docAttr: [],
        busInfo: {},
        mainFlag: '1',
        docOrign: {
          updateFlag: '1',
          ...this.resParams
        }
      };
      let postParams = this.saveParams(data);
      if(toRedis) {
        console.log(postParams);
        this.submitFlag = false;
        return [postParams];
      }

      // console.log(postParams);
      // this.submitFlag = false;
      // return;

      // 调云pacs相关报告接口
      let yParams = {
        ...postParams,
        ypacsRptInfo: this.ypacsRptInfo,
      }
      let apiRes = await this.saveDataByApi([postParams]);
      if(!apiRes) {
        this.submitFlag = false;
        return false;
      }

      if(this.sourceType === '1') {
        this.submitFlag = false;
      }
      let pacsRes = true;
      if(Number(this.sourceType) > 1 || this.isFromYunPacs) {
        pacsRes = await this.saveOrSubmitToYPacs(yParams, isSubmit, !this.docId);
        if(!pacsRes) {
          this.submitFlag = false;
          return false;
        }
      } else {
        pacsRes = await this.$store.dispatch('report/uploadRptImage', {vm: this, params: {examNo: this.patientInfo ? this.patientInfo.examNo : ''}});
      }
      if(apiRes && pacsRes) {
        setTimeout(() => {
          if(isSubmit === '2') {
            this.createPdfFlag = true;
          }
          if(!newMode) {
            this.srCommonInnerOpt(isSubmit, true);  //2确认时才上传pdf2，其他传1
          }
        }, 1000);
        return apiRes;
      }
    },

    // 上传pdf
    async viewToPrintPdf() {
      let container = document.querySelector("#report-wrap");
      this.srCommonUploadPdf(container);
    },

    // 打印
    toPrint() {
      this.createPdfFlag = true;
      this.$store.dispatch('generateReport/printHandler', {vm: this})
    },

    // 修改
    updateHandler() {
      this.$store.commit('generateReport/setEditMode', true);
    },
    // 保存
    async saveRptHandler() {
      let bool = true;
      if(this.reportType === 'S001') {
        bool = this.$refs["gmckDom"].valiData();
      }
      if(!bool) {
        return false;
      }
      return true;
    }
  }
}
</script>
<style lang="scss">
.report-main textarea {
  resize: none;
}
.report-main .el-loading-mask {
  position: fixed;
}
.report-main .disease-header {
  min-height: auto;
}
.report-main .disease-footer {
    min-height: unset;
    border-top: none;
    .widthAndAuto {
      justify-content: center;
    }
    .title, .descript {display: none;}
  }
.mlr-30 {
  margin-left: 30px;
  margin-right: 30px;
}
.rpt-hospital-info {
  margin-top: 20px;
  color: #000;
}
.rpt-hospital-info .depart-title {
  margin-bottom: 5px;
  white-space: pre-wrap;
  font-size: 20px;
  font-weight: bold;
}
.ear-wrap {
  #report-wrap {
    width: 780px !important;
  }
  .rpt-wrap {
    margin: unset !important;
  }
  .widthAndAuto {
    // width: 100%;
    width: unset;
  }
  &.ear-print {
    .disease-body {
      overflow: unset;
      padding: 0;
    }
  }
}
@page {
  margin: 0;
}
@media print {
  html, body, #app{
    overflow: unset !important;
  }
}
</style>
<style lang="scss" scoped>
.report-main {
  width: 100%;
  box-sizing: border-box;
  // overflow-y: auto;
  height: 100%;
}
</style>