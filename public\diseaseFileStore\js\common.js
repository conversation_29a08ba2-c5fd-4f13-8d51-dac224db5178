//radio点击前的选中状态
var _is_checked_ = false;
// 初始化数值输入框,带加减按钮
function initInpNumber(ele, callbackFun) {
  var inpNum = ele.find(".inp-number");
  inpNum.each(function(index, dom) {
    var inp = $(dom).find('.inp_inner');
    var min = inp.attr('min') || '';
    if(inp.val() === '') {
      inp.val(min);
    }
    setDisabled($(dom));
  })
  tiggerInpClick(ele, callbackFun);
}

// 绑定数值输入框的点击事件
function tiggerInpClick(ele, callbackFun) {
  ele.find(".inp-number").on("click", ".inp-number_decrease, .inp-number_increase", function(e) {
    var inpNum = $(this).parent().find(".inp_inner");
    var value = inpNum.val();
    if($(this).hasClass('inp-number_decrease')) {
      inpNum.val(--value);
    }
    if($(this).hasClass('inp-number_increase')) {
      inpNum.val(++value);
    }
    setDisabled($(this).parent());
    if(callbackFun && typeof callbackFun == 'function') {
      callbackFun(ele);
    }
  })
  ele.find(".inp-number").on("blur", ".inp_inner", function() {
    // var numReg = /\d/ig;
    var numReg = new RegExp($(this).attr("data-reg"), 'ig');
    var value = Number($(this).val());
    var min = $(this).attr('min');
    var max = $(this).attr('max');
    if(!numReg.test(value)) {
      $(this).val(min || '');
    } else {
      if(max !== undefined && value > Number(max)) {
        $(this).val(max);
      }
      if(min !== undefined && value < Number(min)) {
        $(this).val(min);
      }
    }
    setDisabled($(this).parent());
    if(callbackFun && typeof callbackFun == 'function') {
      callbackFun(ele);
    }
  })
}    

// 判断是否超过临界值
function setDisabled(dom) {
  var inp = $(dom).find('.inp_inner');
  var min = inp.attr('min');
  var max = inp.attr('max');
  if(min !== undefined && inp.val() <= Number(min)) {
    $(dom).find(".inp-number_decrease").addClass("is-disabled");
  } else{
    $(dom).find(".inp-number_decrease").removeClass("is-disabled");
  }
  if(max !== undefined && inp.val() >= Number(max)) {
    $(dom).find(".inp-number_increase").addClass("is-disabled");
  } else {
    $(dom).find(".inp-number_increase").removeClass("is-disabled");
  }
}

// 纯数字输入类型
function inpNumberHandler(ele) {
  ele.find(".w-con").on("blur", ".only-inp-num", function() {
    // var numReg = /\d/ig;
    var numReg = new RegExp($(this).attr("data-reg"), 'ig');
    var inpVal = $(this).val().trim();
    if(inpVal === '') {
      return;
    }
    if(inpVal[inpVal.length - 1] === '.') {
      $(this).val(inpVal.replace('.', ''));
    }
    var value = Number($(this).val().trim());
    var min = $(this).attr('min');
    var max = $(this).attr('max');
    if(!numReg.test(value)) {
      $(this).val(min || '');
    } else {
      if(max !== undefined && value > Number(max)) {
        $(this).val(max);
      }
      if(min !== undefined && value < Number(min)) {
        $(this).val(min);
      }
    }
  })
}


// 当浅色块下的值没有选择任何一个的话，深色块隐藏
function toggleHightBlock(ele, firstIn) {
  var hightBlock = ele.find(".hight-block");
  ele.find(".hight-item").hide();
  hightBlock.hide();
  hightBlock.each(function(idx, block) {
    // $(block).find('.rt-sr-w').addClass('rt-hide');
    var lightId = $(block).attr("light-id");
    var lightW = ele.find('[id="'+lightId+'"]').find('.rt-sr-w');
    if(lightId === 'rxsc-rt-2-dg') {
      console.log(lightW);
    }
    if(lightW && lightW.length) {
      // 纯文本则判断下级的值是否为空
      if(lightW.hasClass('rt-sr-tit')) {
        var childArr = nodesToArray(ele.find('[parent-id="'+lightId+'"]'));
        for(var i = 0; i < childArr.length; i++) {
          var child = childArr[i];
          if($(child).attr('type') === 'radio') {
            if($(child).is(':checked') && $(child).parents('.lb-active').length) {
              var childId = $(child).attr('id');
              ele.find('[contect-id="'+childId+'"]').show();
              $('[parent-id="'+childId+'"]').removeAttr('disabled');
              $(block).show();
              break;
            }
          } else {
            if($(child).parents('.on').length || (firstIn && $(child).is(':checked'))) {
              var childId = $(child).attr('id');
              ele.find('[contect-id="'+childId+'"]').show();
              $(child).parents('.lb-row').addClass('on');
              $(block).show();
              break;
            }
          }
        }
      } else {
        lightW.each(function(j,dom) {
          if($(dom).is(':checked') && 
            $(dom).parents('.lb-active').length) {
            var childId = $(dom).attr('id');
            ele.find('[contect-id="'+childId+'"]').show();
            $('[parent-id="'+childId+'"]').removeAttr('disabled');
            $(block).show();
          }
        })
      }
    }
  })
}

// 绑定浅色块中选择的应显示的深色块，并把其他值清空
function initLigthItemChange(ele) {
  // ele.find(".lb-row").on("change", ".rt-sr-w", function() {
  //   positionToLight(ele, this);
  // })
  ele.find(".lb-row","input::after").on("click", function(e) {
    if($(this).find('.rt-sr-w').attr('type')==='checkbox') {
      e.stopPropagation();
      e.preventDefault();
      var checkbox = $(this).find('.rt-sr-w');
      if(!checkbox.is(":checked")) {
        checkbox.click();
      }
      $(this).addClass('on');
      $(this).siblings().removeClass('on');
      positionToLight(ele, checkbox);
    }
  })
  ele.find(".lb-row").on("click", ".rt-sr-w", function(e) {
    if($(this).attr('type')==='checkbox') {
      e.stopPropagation();
    }
  })
  ele.find(".lb-row").on("change", ".rt-sr-w", function(e) {
    if($(this).attr('type') === 'checkbox') {
      $(this).parents('.lb-row').addClass('on');
      $(this).parents('.lb-row').siblings().removeClass('on');
    }
    positionToLight(ele, this);
  })
}

// 定位置某个对应的框
// 已经填写的项的值
function positionToLight(ele, that, hasFillItem) {
  var id = $(that).attr('id');
  // 清空其他表单的值
  var hightBlock = $(that).parents(".light-block").siblings(".hight-block");
  if(hightBlock.length === 0) {
    hightBlock = $(that).parents('.w-block').find(".hight-block");
  }
  if(!$(that).hasClass('nclear')) {
    if(ele.attr('type') === 'radio') {
      hightBlock.find('.rt-sr-w').each(function(index, wgt) {
        clearFormItemVal(wgt);
      })
    }
  }
  $(that).parents('.page').find(".hight-item").hide();
  var contectBlock = null;
  if($(that).is(':checked')) {
    contectBlock = $(that).parents('.page').find('[contect-id="'+id+'"]').show();
  }
  toggleHightBlock(ele);
  if(hasFillItem && contectBlock) {
    for(var key in hasFillItem) {
      var rtSrW = contectBlock.find('[data-wname="'+key+'"] .rt-sr-w');
      if(rtSrW.hasClass('rt-sr-r')) {
        contectBlock.find('[data-wname="'+key+'"] .rt-sr-w[value="'+hasFillItem[key]+'"]').prop('checked', true);
      } else if(rtSrW.hasClass('rt-sr-s')) {
        if(rtSrW.find('option[value="'+hasFillItem[key]+'"]').length === 0) {
          continue;
        }
        rtSrW.val(hasFillItem[key]);
      } else {
        rtSrW.val(hasFillItem[key]);
      }
    }
  }
}

// 清除表单的值
function clearFormItemVal(widget) {
  var type = getWgtType(widget);
  if (type === 'text') {
    $(widget).val('');
  } else if (['radio', 'checkbox'].indexOf(type) > -1) {
    $(widget).attr('checked', false);
  } else if (type === 'select') {
    $(widget).find('option').each(function(i, option){
      // if(i === 0) {
      //   $(option).attr('selected', true);
      // } else {
      // }
      $(option).attr('selected', false);
    })
  }
}

// 判断控件类型
function getWgtType(widget) {
  var widgetTypes = ['ck-checkbox', 'r-radio', 'tit-title', 't-text', 's-select'];
  for (var i = 0; i < widgetTypes.length; i++) {
    var name = widgetTypes[i].split('-')[0];
    var type = widgetTypes[i].split('-')[1];
    if ($(widget).hasClass('rt-sr-' + name)) {
      return type;
    }
  }
}

function nodesToArray(nodeList) {
  return Array.prototype.slice.call(nodeList);
}

// 消息提示框
function $wfMessage(option) {
  var defaultOption = {
    "type":"err",  //类型，err,success,warn
    "position":"top-center",   //位置，top-left,top-center,top-right,center-left,center-center,center-right,bottom-left,bottom-center,bottom-right
    "showicon":true,  //是否显示图标
    "content":"提示",
    "shadow":false,
    "shadowclickclose":true,
    "autoclose":true,
  }
  option = $.extend(defaultOption, option)
  WfMsg(option)
}

// 生成uuid
function createUUidFun() {
  return 'xxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
// 获取元素的值
function getVal(selector, joinChar) {
  var dom = curElem.find(selector);
  var valArr = [];
  if(dom.length) {
    dom.each(function(i, el){
      if($(el).val()) {
        valArr.push($(el).val());
      }
    })
  }
  return valArr.length ? valArr.join(joinChar || '、') : '';
}

// 点击单选按钮取消选中
function toggleRadioCheckStatus() {
  curElem.find("input[type=radio]:not(.uncancel)").off('mousedown').off('click');
  curElem.find("input[type=radio]:not(.uncancel)").parent().off('click');
  curElem.find("input[type=radio]:not(.uncancel)").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
      rtStructure.setChildrenDisabled(this);
      iput.trigger('change');
    }
  });
  curElem.find("input[type=radio]:not(.uncancel)").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

/**
 * @param pageId 传入需要回车键跳转的页面id
 * 注意：不需要回车键跳转的输入框添加 n-enterjump="1" 属性
 */
function keydown_to_tab(pageId){
  curElem.on('keydown', 'input:text:not([n-enterjump="1"]):not(:disabled):not(.rt-hide)', function(e) {
    // 找出可见且可编辑的输入框
    var inpList = pageId ? $(`#${pageId} input:text:not([n-enterjump="1"]):not(:disabled):not(.rt-hide):visible`) : [];
    if(!inpList.length) inpList = $('input:text:not([n-enterjump="1"]):not(:disabled):not(.rt-hide):visible');
    var n = inpList.length;
    if (e.which == 13 || e.which == 40){
      e.preventDefault(); 
      var nextIndex = inpList.index(this) + 1;
      if(nextIndex < n) {
        inpList[nextIndex].focus();
      } else {
        inpList[nextIndex-1].blur();
        nextIndex = 0;
        inpList[nextIndex].focus();
      }
    }
    if (e.which == 38){
      e.preventDefault(); 
      var nextIndex = inpList.index(this) - 1;
      if(nextIndex >= 0) {
        inpList[nextIndex].focus();
      } else {
        nextIndex = 0;
        inpList[nextIndex].blur();
        nextIndex = n - 1;
        inpList[nextIndex].focus();
      }
    }
  })
}