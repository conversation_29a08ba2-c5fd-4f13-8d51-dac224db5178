/* 内间距 */
.mt-2 {
  margin-top: 2px;
}
.mt-5 {
  margin-top: 5px;
}
.mt-6 {
  margin-top: 6px;
}
.mt-8 {
  margin-top: 8px;
}
.ml-4 {
  margin-left: 4px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-10 {
  margin-left: 10px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-20 {
  margin-left: 20px;
}
.ml-22 {
  margin-left: 22px;
}
.ml-28 {
  margin-left: 28px;
}
/* 宽度 */
.wd-50 {
  width: 50px!important;
}
.wd-80 {
  width: 80px!important;
}
.wd-112 {
  width: 112px!important;
}
.wd-136 {
  width: 136px!important;
}
.wd-560 {
  width: 560px!important;
}
/* 结肠癌ct页面 */
#jcact1 {
  height: 100%;
  position: relative;
  padding-top: 40px;
  font-size: 14px;
  color: #303133;
}
#jcact1 .jcact-h {
  height: 40px;
  line-height: 40px;
  width: 100%;
  background: #E8F3FF;
  border-bottom: 1px solid #C8D7E6;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
#jcact1 .pat-info {
  width: 960px;
  margin: 0 auto;
  font-size: 18px;
  font-weight: bold;
  color: #000;
}
#jcact1 .flex-sty {
  display: flex;
}
#jcact1 input[type="checkbox"],
#jcact1 input[type="radio"] {
  min-height: unset;
  vertical-align: middle;
}
#jcact1 input[type="text"] {
  width: 60px;
  padding: 3px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
}
#jcact1 .bor {
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}
/* 编辑页面 */
#jcact1 .jcact-edit {
  position: relative;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
}
#jcact1 .jcact-edit .rpt-con {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  overflow: auto;
}
#jcact1 .jcact-edit .rpt-form {
  width: 960px;
  min-height: 100%;
  margin: 0 auto;
  padding: 12px;
  flex: 1;
  border-left: 1px solid #E3E3E3;
  border-right: 1px solid #E3E3E3;
}
#jcact1 .p-item {
  width: auto;
  flex-wrap: nowrap;
}
#jcact1 .lb-txt {
  display: inline-block;
  min-width: 70px;
  text-align: right;
}
#jcact1 .mlb-txt {
  display: inline-block;
  min-width: 44px;
  text-align: right;
}
#jcact1 .jcact-edit .add-bz-btn {
  display: inline-block;
  height: 28px;
  background: #1885F2;
  color: #FFF;
  border-radius: 3px;
  padding: 4px 12px;
  cursor: pointer;
}
#jcact1 .jcact-edit .add-bz-btn:hover {
  opacity: 0.7;
}
#jcact1 .jcact-edit .bz-wrap {
  margin-top: 8px;
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}
#jcact1 .jcact-edit .bz-tit-ls {
  width: 862px;
  overflow-x: auto;
  overflow-y: hidden;
  height: 32px;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  display: -webkit-box;
  display: -moz-box;
}
#jcact1 .bz-tit-ls::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
}
#jcact1 .jcact-edit .bz-tit-i {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 20px;
  color: #303133;
  border-right: 1px solid #DCDFE6;
}
#jcact1 .jcact-edit .bz-tit-i.act {
  position: relative;
  background: #EBEEF5;
  color: #1885F2;
  font-weight: bold;
}
#jcact1 .jcact-edit .bz-tit-i .close-icon,#jcact1 .jcact-edit .del-icon {
  color: #303133;
  width: 16px;
  height: 16px;
  line-height: 16px;
  font-weight: normal;
  font-size: 18px;
  font-family: "Microsoft YaHei";
  text-align: center;
  cursor: pointer;
}
#jcact1 .jcact-edit .bz-tit-i .close-icon:hover {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #e1e1e1;
  color: #fff;
}
#jcact1 .jcact-edit .bz-item {
  display: none;
  padding: 8px 12px;
}
#jcact1 .jcact-edit .bz-item.act {
  display: block;
}
#jcact1 .jcact-edit .bz-lb {
  display: inline-block;
  width: 98px;
  text-align: right;
}
#jcact1 .jcact-edit .add-btn {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background: #1885F2;
  color: #fff;
  border-radius: 16px;
  font-weight: bold;
  cursor: pointer;
}
#jcact1 .jcact-edit .highlight-block {
  flex: 1;
  padding: 4px 8px;
  background: #EBEEF5;
  border: 1px solid #C8D7E6;
}
#jcact1 .jcact-edit .fqylbz-block {
  display: none;
}
#jcact1 .jcact-edit .hight-block {
  color: #303133;
}
#jcact1 .jcact-edit .light-block .lb-row {
  min-height: 28px;
}
#jcact1 .jcact-edit .rpt-bot {
  background: #E8F3FF;
  border-top: 1px solid #C8D7E6;
  padding: 8px 0;
}
#jcact1 .jcact-edit .rpt-bot .rpt-imp {
  width: 960px;
  margin: 0 auto;
  display: flex;
  word-break: break-all;
  white-space: pre;
}
#jcact1 .jcact-edit .rpt-bot .imp-lb {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}
#jcact1 .view-show {
  display: none;
}
[isview="true"] #jcact1 .view-show {
  display: block;
}
#jcact1 .jcact-edit select:first-child {
  color: #606266;
}
/* 预览页面 */
#jcact1 .jcact-view {
  display: none;
  height: 100%;
  overflow: auto;
  background: #F5F7FA;
  padding: 12px 0;
  color: #000;
}
#jcact1 .jcact-view .pdf-con {
  width: 960px;
  min-height: 1100px;
  background: #FFF;
  margin: 0 auto;
  border: 1px solid #E6E6E6;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
#jcact1 .jcact-view .pdf-con .pdf-top {
  flex: 1;
  padding: 0 26px 16px 26px;
  display: flex;
  flex-direction: column;
}
#jcact1 .jcact-view .view-rpt-wrap {
  position: relative;
}
#jcact1 .jcact-view .view-rpt-wrap .rpt-block {
  display: flex;
  flex: 1;
  margin-top: 12px;
}
#jcact1 .jcact-view .view-rpt-wrap .rpt-tit {
  width: 84px;
  text-align: right;
  color: #303133;
  font-size: 14px;
}
#jcact1 .jcact-view .view-rpt-wrap .rpt-desc {
  color: #303133;
  font-size: 14px;
  word-break: break-all;
  white-space: pre-line;
  line-height: 22px;
  flex: 1;
}
#jcact1 .jcact-view .view-rpt-wrap .bzlb-con .flex-sty { 
  margin-bottom: 4px;
}
#jcact1 .jcact-view .view-rpt-wrap .bor-gray {
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
  padding: 8px 12px;
}
#jcact1 .jcact-view .pdf-con .pdf-bottom {
  min-height: 40px;
  background: #E8F3FF;
  border-top: 1px solid #E6E6E6;
  padding: 12px 44px;
  display: flex;
  font-size: 14px;
  color: #000;
}
#jcact1 .jcact-view .pdf-con .pdf-bottom .bt-tit {
  font-weight: bold;
}
#jcact1 .jcact-view .pdf-con .pdf-bottom .bt-imp {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
[isview="true"] .jcact-edit {
  display: none !important;
}
[isview="true"] .jcact-view {
  display: block !important;
}