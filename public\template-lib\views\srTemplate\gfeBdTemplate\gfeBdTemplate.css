.singleDisEditReport.main-page{
  min-width: unset;
}
#gfeBd1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#gfeBd1 * {
  font-family: '宋体';
}
#gfeBd1 .gfeBd-edit{
  padding: 8px 12px;
}
#gfeBd1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#gfeBd1 .black-lb {
  color: #303133;
}
#gfeBd1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#gfeBd1 .blue-lb:hover {
  opacity: 0.8;
}
#gfeBd1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#gfeBd1 .text-size, .diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#gfeBd1 .text-size img, .diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#gfeBd1 .text-size img:hover, .diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#gfeBd1 .text-size .on, .diag-text-size .text-size .on {
  display: none;
}
#gfeBd1 .editor-area {
  padding: 4px 0;
  min-height: 100px;
}
.editor-area textarea {
  width: 100%;
  min-height: 100px;
  border: none;
  font-size: 16px;
}
#gfeBd1 .p-item + .p-item {
  margin-top: 12px;
}
.editor-area.default textarea{
  font-size: 16px;
}
.editor-area.large textarea{
  font-size: 18px;
}
.editor-area.larger textarea{
  font-size: 20px;
}
#gfeBd1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#gfeBd1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#gfeBd1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#gfeBd1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#gfeBd1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#gfeBd1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

*:disabled {
  background: #fff;
  color: unset;
}
textarea[readonly] {
  cursor: not-allowed;
}
#gfeBd1 .gfeBd-view {
  display: none;
}
[isview="true"] #gfeBd1 .gfeBd-edit {
  display: none;
}
[isview="true"] #gfeBd1 .gfeBd-view {
  display: flex;
}
#gfeBd1 .gfeBd-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 115px 56px;
  flex-direction: column;
  position: relative;
}
#gfeBd1 .gfeBd-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#gfeBd1 .gfeBd-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
}
#gfeBd1 .gfeBd-view .logo-tit img {
  width: 68px;
  height: 68px;
  margin-right: 20px;
}
#gfeBd1 .gfeBd-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
#gfeBd1 .gfeBd-view .hos-tit{
  font-size: 24px;
  font-weight: bold;
  color: #000;
}
#gfeBd1 .gfeBd-view .eng-tit{
  font-size: 14px;
  color: #000;
}
#gfeBd1 .gfeBd-view .sub-tit{
  font-size: 18px;
  color: #000;
  font-weight: bold;
  margin-top: 8px;
}
#gfeBd1 .gfeBd-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#gfeBd1 .gfeBd-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
}
#gfeBd1 .gfeBd-view .black-txt {
  color: #000;
  font-size: 16px;
}
#gfeBd1 .gfeBd-view .red-txt {
  color: #E64545;
  font-size: 16px;
}
#gfeBd1 .gfeBd-view .bold {
  font-weight: bold;
}
#gfeBd1 .gfeBd-view .info-i {
  width: 210px;
  display: flex;
  flex-wrap: wrap;
}
#gfeBd1 .gfeBd-view .info-i + .info-i {
  margin-left: 8px;
}
#gfeBd1 .gfeBd-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#gfeBd1 .gfeBd-view .view-patient .p-item {
  margin-top: 8px;
}
#gfeBd1 .gfeBd-view .body-exam-info {
  border-bottom: 1px solid #999;
  padding: 8px 0;
  font-size: 16px;
  color: #000;
}
#gfeBd1 .gfeBd-view .body-exam-info .b-exam-lb {
  font-weight: bold;
}
#gfeBd1 .gfeBd-view .body-exam-info .b-exam-info {
  flex: 1;
}
#gfeBd1 .gfeBd-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#gfeBd1 .gfeBd-view .desc-con {
  padding: 8px 0;
  display: none;
  margin-bottom: 100px;
}
#gfeBd1 .gfeBd-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#gfeBd1 .gfeBd-view .desc-con {
  display: flex;
  align-items: baseline;
}
#gfeBd1 .gfeBd-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#gfeBd1 .gfeBd-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#gfeBd1 .gfeBd-view .reporter-i span:last-child {
  flex: 1;
}
#gfeBd1 .gfeBd-view .reporter-i img {
  width: 90px;
  height: 40px;
  object-fit: contain;
}
#gfeBd1 .gfeBd-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#gfeBd1 .gfeBd-view .tip-wrap {
  margin-top: 8px;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
}
#gfeBd1 .gfeBd-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  margin-bottom: 12px;
  padding-left: 80px;
}
#gfeBd1 .gfeBd-view .item-img {
  display: inline-block;
  width: 240px;
  height: 180px;
  border: 1px solid #eee;
  margin: 0 6px 8px 6px;
}
#gfeBd1 .gfeBd-view .rpt-img-ls[data-len="3"] .item-img{
  width: 210px;
  height: 160px;
}
#gfeBd1 .gfeBd-view .rpt-img-ls[data-len="3"],
#gfeBd1 .gfeBd-view .rpt-img-ls[data-len="4"] {
  padding-left: 0;
}
#gfeBd1 .gfeBd-view .rpt-img-ls[data-len="4"] .item-img {
  width: 158px;
  height: 120px;
  margin: 0 4px 8px 4px;
}
#gfeBd1 .gfeBd-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#gfeBd1 .p-item:nth-child(2) .text-size,#gfeBd1 .p-item:nth-child(3) .text-size,#gfeBd1 .p-item:nth-child(4) .text-size {
  display: none;
}
#gfeBd1 .p-item:nth-child(2) .editor-area,#gfeBd1 .p-item:nth-child(3) .editor-area {
  min-height: 90px;
}
#gfeBd1 .p-item:nth-child(2) textarea,#gfeBd1 .p-item:nth-child(3) textarea {
  min-height: 90px;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #gfeBd1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #gfeBd1 .gfeBd-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #gfeBd1 .gfeBd-view .view-head,
[entry-type="5"] #gfeBd1 .gfeBd-view .view-patient,
[entry-type="5"] #gfeBd1 .gfeBd-view .tip-wrap {
  display: none;
}
[entry-type="5"] #gfeBd1 .gfeBd-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #gfeBd1 .gfeBd-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
