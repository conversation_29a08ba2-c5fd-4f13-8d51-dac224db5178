// 存在实际字段可能不一样的，用,号隔开，第1个为公共代码表的字段
var publicCode = {
  // 患者信息（1~99）
  '-2': 'optId',  //编辑器默认为当前登录人optId使用
  '-1': 'optName',  //编辑器默认为当前登录人使用
  '1': 'name',
  '2': 'sex',
  '3': 'age',
  '4': 'married,maritalStatus',
  '5': 'weight',
  '6': 'height',
  '7': 'birthDate,birthDay',
  '8': 'miCard',
  '9': 'idCard,identityCard,identity',
  '10': 'sickId',
  '11': 'outPatNo,outPatientNo,outpatientNo',
  '12': 'inPatNo,inPatientNo,inpatientNo',
  '13': 'caseNo',
  '14': 'bedNo',
  '15': 'healthCard',
  '16': 'cardNo',
  '17': 'phoneNo,phoneNumber',
  '18': 'fixPhoneNo',
  '19': 'HMIdentityId',
  '20': 'TWIdentityId',
  '21': 'motherSickId',
  '22': 'physicalNo',
  '23': 'nation',
  '24': 'nationality',
  '25': 'nativePlace',
  '26': 'bearStatus',
  '27': 'zipCode',
  '28': 'address,mailingAddress',
  '29': 'medRecord',
  '30': 'allergy',
  '31': 'mensesStatus',
  '32': 'lastMensesDate',
  '33': 'signContent',
  '34': 'wristNo',

  // 检查检验信息（100~199）
  '101': 'itemName,examItemNames',
  '102': 'organName',
  '103': 'patSource,patientSource',
  '104': 'priority,priorityOrder',
  '105': 'examClass',
  '106': 'examSubClass',
  '107': 'reqOrg,reqHospitalName,reqHospital',
  '108': 'regWard,reqWardName',
  '109': 'reqDeptName,regDept',
  '110': 'performOrg',
  '111': 'performWard',
  '112': 'performDept,performDeptName',
  '113': 'reqOrgCode,reqHospitalCode',
  '114': 'regWardCode',
  '115': 'regDeptCode,reqDeptCode',
  '116': 'performOrgCode',
  '117': 'performWardCode',
  '118': 'performDeptCode',
  '119': 'reqPhysician,reqPhysicianName',
  '120': 'reqPhysicianCode',
  '121': 'register',
  '122': 'registerCode',
  '123': 'applyId',
  '124': 'reqTime',
  '125': 'registerTime',
  '126': 'scheduledTime',
  '127': 'examTime',
  '128': 'examDration',
  '129': 'cost',
  '130': 'charge',
  '131': 'chargeFlag',
  '132': 'itemCode',
  '133': 'itemCost',
  '134': 'itemCharge',
  '135': 'itemcount',
  '136': 'physSign',
  '137': 'clinSymp',
  '138': 'clinDiag',
  '139': 'clinDiagTime',
  '140': 'relevantDiag,relevanDiag',
  '141': 'relevantLabTest',
  '142': 'examMotive',
  '143': 'examDevice',
  '144': 'examStatus',
  '145': 'patLocalId',
  '146': 'accessionNo',
  '147': 'studyUid',
  '148': 'reqDate',
  '149': 'registerDate',
  '150': 'scheduledDate',
  '151': 'examDate',
  '152': 'examStaff',
  '153': 'examStaffNo',
  '154': 'reqMemo',
  '155': 'chargeType',
  

  // 检查检验报告信息（200~299）
  '201': 'description',
  '202': 'impression',
  '203': 'recommendation',
  '204': 'reporter',
  '205': 'reportTime',
  '206': 'affirmReporter',
  '207': 'affirmTime',
  '208': 'abnormal',
  '209': 'reportDate',
  '210': 'affirmDate',
  '211': 'examParam',
  '212': 'priReporter',
  '213': 'priReportTime',
  '214': 'priReportDate',
  '215': 'priReporterNo',
  '216': 'reporterNo',
  '217': 'affirmReporterNo',
  '218': 'reDiagReporterNo',
  '219': 'reDiagReporter',

  // 病理标本信息（300~399）
  '300': 'sampleName',
  '301': 'samplePart',
  '302': 'sampleType',
  '303': 'sampleSource',
  '304': 'sampleSourceDetail',
  '305': 'sampleSourcePhone',
  '306': 'sampleDoctorName',
  '307': 'sampleNurseName',
  '308': 'samplePackageNo',
  '309': 'sampleBarcodeNo',
  '310': 'infectiousFlag',
  '311': 'freezeFlag',
  '312': 'sampleSeen',
  '313': 'candleId',
  '314': 'outterGmId',
  '315': 'materialStaff',
  '316': 'materialStaffNo',
  '317': 'ihcFlag',
  '318': 'ihcMemo',
  '319': 'stainFlag',
  '320': 'stainMemo',
  '321': 'receiveDate',
  '322': 'receiveTime',

  // 手术信息（400~499）
  '400': 'surgeryName',
  '401': 'surgeryFingding,surgerySeen',
  '402': 'surgerDoctorName',
  '403': 'surgerNurseName',
  '404': 'surgerTime,surgeryTime',
  
  // 诊断部分(500~599)
  '500': 'usShape',
  '501': 'usParallel',
  '502': 'usBorder',
  '503': 'usEchoType',
  '504': 'usPosteriorEcho',
  '505': 'usCalcification',
  '506': 'usBiGadsGrade',
  '507': 'usLength',
  '508': 'usWidth',

  // 患者信息（3400~3450）
  '3400': 'temperature',
  '3401': 'pulse',
  '3402': 'heartRate',
  '3403': 'respiration',
  '3404': 'bp1',
  '3405': 'bp2',
  '3406': 'bloodSugar',
  '3407': 'spo',
  '3408': 'pupilSize',
}