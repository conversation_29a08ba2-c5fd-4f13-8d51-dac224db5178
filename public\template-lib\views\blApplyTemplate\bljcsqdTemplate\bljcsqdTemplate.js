$(function() {
  window.initHtmlScript = initHtmlScript;
  window.errorCallBack = errorCallBack;
})
// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
  // 'cdjc-rt-21': { label: '送检医生', wt: '1', hisKey: 'optName' }
}
// 需要更新的申请单数据
// applyItemList根据blApplyDictionary.js的全局变量addSampleList确定，id直接为''//已添加的标本数据 
// gmSampleList根据blApplyDictionary.js的全局变量addExamItemList确定，id直接为''//已添加的项目数据
// bljcGmSampleList为自定义键值，最终还是存在gmSampleList字段中
var saveApplyParams = {
  'name': { label: '姓名', id: 'bljcsqd-rt-2'},
  'ageAndMonth': { label: '年龄', id: ''},
  'sex': { label: '性别', id: 'bljcsqd-rt-6'},
  'sickId': { label: '病人ID', id: 'bljcsqd-rt-8'},
  'outpatientNo': { label: '门诊号', id: 'bljcsqd-rt-10'},
  'inpatientNo': { label: '住院号', id: 'bljcsqd-rt-12'},
  'bedNo': { label: '床号', id: 'bljcsqd-rt-14'},
  'identityCard': { label: '身份证号', id: 'bljcsqd-rt-18'},
  'phoneNumber': { label: '手机号', id: 'bljcsqd-rt-20'},
  'patientSource': { label: '病人来源', id: 'bljcsqd-rt-22'},
  'lastMensesDate': { label: '末次月经', id: 'bljcsqd-rt-24'},
  'noLastMensesDate': { label: '末次月经', id: 'bljcsqd-rt-25'},
  'birthDate': { label: '出生日期', id: 'bljcsqd-rt-67'},
  'infectiousFlag': { label: '是否传染病', ids: [
      {id:'bljcsqd-rt-27', index: 13, value: '1'},
      {id:'bljcsqd-rt-28', index: 13, value: '0'},
    ]
  },
  'mailingAddress': { label: '通信地址', id: 'bljcsqd-rt-31'},
  'bljcExamClasses': { label: '检查类别', id: '' },
  'examSubClass': { label: '检查子类', id: 'bljcsqd-rt-33'},
  'scheduledDate': { label: '预约时间', id: 'bljcsqd-rt-38'},
  'reqHospital': { label: '申请医院', id: 'bljcsqd-rt-74'},
  'bljcReqDept': { label: '申请科室', id: ''},
  'bljcReqPhysician': { label: '申请医生', id: ''},
  // 'reqPhysician': { label: '申请医生', id: 'bljcsqd-rt-44'},
  'freezeFlag': { label: '是否冰冻', ids: [
      {id:'bljcsqd-rt-35', index: 13, value: '1'},
      {id:'bljcsqd-rt-36', index: 13, value: '0'},
    ]
  },
  'chargeFlag': {
    label: '是否缴费', ids: [
      {id:'bljcsqd-rt-48', index: 13, value: '1'},
      {id:'bljcsqd-rt-49', index: 13, value: '0'},
    ]
  },
  'clinDiag': { label: '临床诊断', id: 'bljcsqd-rt-56'},
  'physSign': { label: '体征', id: 'bljcsqd-rt-58'},
  'medRecord': { label: '病史', id: 'bljcsqd-rt-60'},
  'relevantLabTest': { label: '化验结果', id: 'bljcsqd-rt-62'},
  'bljcApplyItemList': { label: '检查项目', id: ''},
  'bljcGmSampleList': { label: '标本信息', id: ''},
  'ihcFlag': {
    label: '是否免疫组化', ids: [
      {id:'bljcsqd-rt-51', index: 13, value: '1'},
      {id:'bljcsqd-rt-52', index: 13, value: '0'},
    ]
  },
  'ihcMemo': { label: '免疫组化', id: 'bljcsqd-rt-54'},
  'specificStainFlag': {
    label: '是否特殊染色', ids: [
      {id:'bljcsqd-rt-76', index: 13, value: '1'},
      {id:'bljcsqd-rt-77', index: 13, value: '0'},
    ]
  },
  'stainMemo': { label: '特殊染色', id: 'bljcsqd-rt-79'},
  'infection': { label: '传染病', id: 'bljcsqd-rt-29'},
  'surgerySeen': { label: '术中所见', id: 'bljcsqd-rt-64'},
  'wristNo': { label: '手腕号', id: 'bljcsqd-rt-72'},
  // 示例，当一个键值对有多重含义的通过ids
  // 'flags': { label: '是否绝经', ids: [
  //     {id:'cgbljc-0022.001-rItem--1', index: 10, value: '1'},
  //     {id:'cgbljc-0022.001-rItem--2', index: 10, value: '0'},
  //   ]
  // }
}
var preExamNo = ''; // 加入申请单后回传的检查号
var checkDays = 30; // 默认查询提取内镜诊断天数
var rtStructure = null;
var curElem = null;
var genderList = []; // 性别列表
var nationList = []; // 民族列表
var patientSourceList = []; // 病人来源列表
var examItemList = []; // 检查项目列表
var examSubClassList = []; // 检查子类列表
var hospitalList = []; // 申请医院列表
var deptList = []; // 申请科室列表
var dictUsersList = []; // 申请医生列表
var gmSamplePartList = []; // 标本部位列表
var gmTypeNameList = []; // 标本类别列表
var gmSampleNameList = []; // 标本名称列表
var gmFixativeList = []; // 固定液列表
var defaultGmFixative = [];  //默认固定液
var deptCode = ''; // 科室参数
var examClass = ''; // 检查类别
var selList = [];
var fixativeGap = ''; // 固定时间与离体时间差
var firstLoad = true;  //新增
var setValFromApply = false;  //是否直接读取申请单数据
var disabledDate = [];
var sampleType = null; // 标本类别默认值,是否冰冻
var disableCode = []; // 用code禁用对应文本框
var unEditSampleSta = []; // 标本信息状态
var unEditAttrExcept = []; // 不受限制的字段
var hideAttrCode = []; // 申请单隐藏字段属性集配置
var isShowTqnjzdBtn = false; // 是否显示‘提取内镜诊断’按钮
var isShowBbzl = true;  //是否显示标本重量
var dropdown = null;
var bbmcDropdown = null;
var pageParams = {
  pageNo: 1, 
  pageSize: 20
};
var valueLike = '';
var curInpEle = null;
var bbmcId = '';
var isSavedReport = false; //模板是否填写过
var publicInfo = {};  //公共报告属性内容
var endoscopeRptList = []; // 提取诊断列表
var frozeAppointConfig = {};  //冰冻预约相关配置
function errorCallBack(widgetId) {
  // 滚动至必填位置
  if(widgetId) {
    var targetDom = $('[id="'+widgetId+'"]');
    $(".layout-body")[0].scrollTop = targetDom.closest('.bbxx-table').length ? 
      targetDom.closest('.bbxx-table')[0].offsetTop + targetDom[0].offsetTop : targetDom[0].offsetTop;
  }
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    setValFromApply = rtStructure.enterOptions.setValFromApply || false;
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      specialDescContent();
    } else {
      initPage();
    }
    showItemAndGm();
    
    // console.log('setValFromApply-->',setValFromApply);
    // if(setValFromApply) {
    //   showItemAndGm();
    // }
  }
  // setValByHis(hisAndFormMap);
}
// 初始化
function initPage() {
  $('#bljcsqd1 .date-wrap02').prop('readonly',true);
  // 年龄-岁输入框存在值时，只取数字部分
  // let ageYear = $('#bljcsqd-rt-4').val() || '';
  // let regAge = ageYear.replace(/[^\d.]/g,'');
  // $('#bljcsqd-rt-4').val(regAge);
  splitAgeAndUnit($('#bljcsqd-rt-4').val() || '')

  // 末次月经是否必填
  let sexVal = $('#bljcsqd-rt-6').val() || '';
  lastMensesDateIsReq(sexVal);

  examClass = getParamByName('examClass');
  let deptCode = getParamByName('reqDeptCode');
  let examSubClass = getParamByName('examSubClass') || $('#bljcsqd-rt-33').val() || '';
  getGmSettings();
  initDatePicker();
  initDatePicker(true);
  getGenderList();
  getNationList();
  getPatientSourceList();
  getHospitalList();
  getDeptList();
  if(!$('#bljcsqd-rt-42').val() && deptCode) {
    let filterDeptList = deptCode ? deptList.filter(item => item.deptCode===deptCode) : [];
    let deptName = filterDeptList.length ? filterDeptList[0].deptName : '';
    $('#bljcsqd-rt-42').val(deptName);
    $('#bljcsqd-rt-42').attr('deptCode', deptCode);
  }
  getDictUsersList(deptCode);
  getGmSamplePartList();
  getGmTypeNameList();
  // getGmSampleNameListV2();
  getGmFixativeList();
  getExamItemList(examSubClass);
  getExamSubClassList2();
  gmFixativeGap();
  initSelFun();
  // showTableVal('input[type="text"]');
  validateIdCard();
  validatePhoneNum();
  // 根据年龄推算出生日期事件
  var ageList = [
    { id: 'bljcsqd-rt-4', evType: 'blur'},
    { id: 'bljcsqd-rt-68', evType: 'change'},
    { id: 'bljcsqd-rt-69', evType: 'blur'}
  ];
  for(var i=0;i<ageList.length;i++) {
    let id = ageList[i].id;
    let evType = ageList[i].evType;
    getDateByAge(id,evType)
  }
  // 添加回车事件
  var evIdList = ['bljcsqd-rt-8','bljcsqd-rt-10','bljcsqd-rt-12'];
  for(var i=0;i<evIdList.length;i++) {
    enterEvFun(evIdList[i]);
  }
  // 模糊查询--检查项目、申请科室、申请医生、标本名称、标本部位、标本类别
  var inpSelIdList = ['bljcsqd-rt-40','bljcsqd-rt-42','bljcsqd-rt-44'];
  for(var i=0;i<inpSelIdList.length;i++) {
    bindEleFun(inpSelIdList[i]);
  }
  // 是否冰冻
  $('#bljcsqd1 .bd-item input[type="radio"]').on('click',function() {
    setBbTypeDefault('.bblb-sel');
  })
  // 末次月经“无”复选框
  noLastMensesDateCk();
  firstLoad = false;

  // 初始化标本信息日期时间控件事件
  $('.time-wrap01').on('blur',function(e) {
    curInpEle = e.target;
    compareDateTime('lt', curInpEle.value);
  });
  $('.time-wrap02').on('blur',function(e) {
    curInpEle = e.target;
    compareDateTime('gd', curInpEle.value);
  });
  initBbmcSelEv();
  $('.tqnjzd-btn').click(function() {
    showDescListDialog();
  })
}

// 直接从申请单读取数据时，特殊回显标本和检查信息
function showItemAndGm() {
  // 回显检查项目，标本信息
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ? 
    rtStructure.enterOptions.publicInfo : {};
  var applyItem = enterData.applyItemList ? enterData.applyItemList[0] : {};
  var gmList = enterData.gmSampleList || [];
  var gmData = [];
  gmDataMap = {};
  if(gmList.length) {
    var keyAndId = {
      'sampleName': '01-标本名称',
      'samplePart': '02-标本部位',
      'sampleType': '03-标本类别',
      'blockCount': '07-标本数量',
      'sampleWeight': '08-标本重量',
      'inVitroDateTime': '04-离体时间',
      'fixedDateTime': '05-固定时间',
      'fixLiquid': '06-固定液',
    }
    var pre = 'bljcsqd-rt-';
    for(var i = 0; i < gmList.length; i++) {
      var gmItem = gmList[i];
      var flagId = createUUidFun();
      var oneGm = {
        desc: "列表行",
        id: `${pre}00.${flagId}`,
        name: "列表行",
        pid: "bljcsqd-rt-65",
        val: `${i+1}`,
        child: [],
      }
      for(var key in keyAndId) {
        var [id, name] = keyAndId[key].split('-');
        // 不存在该元素时，跳过
        if(curElem.find(`[id^="${pre}${id}."]`).length === 0) {
          continue;
        }
        oneGm.child.push({
          desc: name,
          id: `${pre}${id}.${flagId}`,
          name: name,
          pid: `${pre}00.${flagId}`,
          val: gmItem[key] || '',
        })
      }
      gmData.push(oneGm);
      gmDataMap[`${pre}00.${flagId}`] = {
        sampleNo: gmItem.sampleNo,
        sampleStatus: gmItem.sampleStatus,
      }
    }
  }
  if(rtStructure.enterOptions.type==='view') {
    $('.jcxm-item').text(applyItem.itemName);
    $('.xmfy-item').text(applyItem.charge);
    // 标本
    showTableVal('span:last', gmData);
  } else {
    $('[id="bljcsqd-rt-40"]').val(applyItem.itemName);
    $('[id="bljcsqd-rt-40"]').attr('itemCode', applyItem.itemCode);
    $('[id="bljcsqd-rt-46"]').val(applyItem.charge);
    showTableVal('input[type="text"]', gmData);
  }
  // 申请医院
  let {reqHospitalName=''} = srQueryParams[localKey];
  if(enterData.reqHospital || reqHospitalName) {
    let hospitalVal = enterData.reqHospital || reqHospitalName || '';
    $('#bljcsqd-rt-74').val(hospitalVal)
  }
  // 申请科室
  if(enterData.reqDept || enterData.deptCode) {
    $('#bljcsqd-rt-42').attr('deptCode', enterData.reqDept || enterData.deptCode);
  }
  // 申请医生
  if(enterData.reqPhysicianCode) {
    $('#bljcsqd-rt-44').attr('staffNo', enterData.reqPhysicianCode);
  }
  // 年龄
  if(enterData.age) {
    splitAgeAndUnit(enterData.age);
  }
  // 性别
  if(enterData.sex) {
    lastMensesDateIsReq(enterData.sex);
  }
  // 手腕号
  if(enterData.wristNo) {
    $('#bljcsqd-rt-72').val(enterData.wristNo);
  }
  // 预约时间
  if(enterData.scheduledDate && enterData.scheduledTime) {
    let date = enterData.scheduledDate +' '+ enterData.scheduledTime;
    if(rtStructure.enterOptions.type==='view') {
      $('.schedule-date').val(date);
    }else {
      $('#bljcsqd-rt-38').val(date);
    }
  }
  // 是否冰冻
  if(enterData.freezeFlag) {
    resetEleVal('bljcsqd1','bd-item','freezeFlag',enterData);
  }
  // 是否缴费
  if(enterData.chargeFlag) {
    resetEleVal('bljcsqd1','jf-item','chargeFlag',enterData);
  }
  // 是否免疫组化
  if(enterData.ihcFlag) {
    resetEleVal('bljcsqd1','myzh-item','ihcFlag',enterData);
  }
  // 是否特殊染色
  if(enterData.specificStainFlag) {
    resetEleVal('bljcsqd1','tsrs-item','specificStainFlag',enterData);
  }
}

// 拆分年龄和单位
function splitAgeAndUnit(str) {
  if(!str) {
    return;
  }
  let regAge = '';
  if(isSavedReport && $('#bljcsqd-rt-4').val()) {
    regAge = $('#bljcsqd-rt-4').val();
  }else {
    let {age='',ageType='',subAge=''} = getAgeAndUnit(str);
    regAge = age;
    if(ageType === '日' || ageType === '天') {
      ageType = '天';
    }
    $('#bljcsqd-rt-68').val(ageType);
    $('#bljcsqd-rt-69').val(subAge);
    changeAgeUnit(ageType);
  }
  $('#bljcsqd-rt-4').val(regAge);
}
// 获取年龄和单位
function getAgeAndUnit(str){
  var index = escape(str).indexOf( "%u" );
  if(index > 0){
    var age = str.substring(0,index);
    var ageType = str.substring(index,index+1);
    if(str.length > index+1) {
      var subAge = str.substring(index+1,str.length-1);
    }
  }
  return {age,subAge,ageType}
}
// 回车键事件
function enterEvFun(id) {
  $('#'+id).on('keydown',function(e) {
    if(e.which == 13) {
      getApplyPatient(id);
    }
  })
}
// 切换子类时，标本信息相关接口内容获取
function getGmInfoListHandler(dropdown) {
  // 标本部位
  getGmSamplePartList();
  $('.bbbw-sel:visible').each(function(i, dom) {
    var id = $(dom).attr('id');
    $(dom).val('');
    dropdown.reload(id, {
      data: gmSamplePartList, 
      show: false // 重载即显示组件面板
    });
  })
  // 标本类别
  getGmTypeNameList();
  $('.bblb-sel:visible').each(function(i, dom) {
    var id = $(dom).attr('id');
    $(dom).val('');
    dropdown.reload(id, {
      data: gmTypeNameList, 
      show: false // 重载即显示组件面板
    });
  })
}
// 预览页面特殊处理
function specialDescContent() {
  let idAndDomMap = rtStructure ? rtStructure.idAndDomMap : {};
  let str = '';
  if(!setValFromApply) {
    // 末次月经
    let bljc24 = idAndDomMap['bljcsqd-rt-24'].value;
    let bljc25 = idAndDomMap['bljcsqd-rt-25'].value;
    str = `${ bljc24 || bljc25 }`;
    $(".mcyj-item").html(str);
  
    // 是否传染病
    let bljc27 = idAndDomMap['bljcsqd-rt-27'].value;
    let bljc28 = idAndDomMap['bljcsqd-rt-28'].value;
    let bljc29 = idAndDomMap['bljcsqd-rt-29'].value;
    str = `${ bljc27 ? bljc27 + (bljc29 ? '，'+bljc29 : ''):'' || bljc28 }`;
    $(".crb-item").html(str);
    // 是否冰冻
    // let bljc35 = idAndDomMap['bljcsqd-rt-35'].value;
    // let bljc36 = idAndDomMap['bljcsqd-rt-36'].value;
    // str = `${ bljc35 || bljc36 }`;
    // $(".bd-item").html(str);
    // 是否缴费
    // let bljc48 = idAndDomMap['bljcsqd-rt-48'].value;
    // let bljc49 = idAndDomMap['bljcsqd-rt-49'].value;
    // str = `${ bljc48 || bljc49 }`;
    // $(".jf-item").html(str);
    // 是否免疫组化
    // let bljc51 = idAndDomMap['bljcsqd-rt-51'].value;
    // let bljc52 = idAndDomMap['bljcsqd-rt-52'].value;
    // str = `${ bljc51 || bljc52 }`;
    // $(".myzh-item").html(str);
  }
  // showTableVal('span:last');
  $('.form-item').hide();
}
// 末次月经复选框
function noLastMensesDateCk() {
  $('#bljcsqd-rt-25').on('click',function() {
    if($('#bljcsqd-rt-25').is(':checked')) {
      $('#bljcsqd-rt-24').val('');
    }
  });
}
// 下拉框事件
function initSelFun() {
  selList = [
    { id: 'bljcsqd-rt-6', className: 'sex-sel', data: genderList },
    { id: 'bljcsqd-rt-16', className: 'mz-sel', data: nationList },
    { id: 'bljcsqd-rt-22', className: 'patient-sel', data: patientSourceList },
    { id: 'bljcsqd-rt-33', className: 'examsub-sel', data: examSubClassList },
    { id: 'bljcsqd-rt-40',className: 'examitem-sel', data: examItemList },
    { id: 'bljcsqd-rt-74', className: 'hosp-sel', data: hospitalList },
    { id: 'bljcsqd-rt-42', className: 'dept-sel', data: deptList },
    { id: 'bljcsqd-rt-44', className: 'doct-sel', data: dictUsersList },
    { className: 'bbmc-sel', data: gmSampleNameList, width: 120 },
    { className: 'bbbw-sel', data: gmSamplePartList, width: 120 },
    { className: 'bblb-sel', data: gmTypeNameList, width: 80 },
    { className: 'gdy-sel', data: gmFixativeList, width: 160 },
  ];
  let classNameList = ['hosp-sel','dept-sel','doct-sel','examsub-sel','examitem-sel','sex-sel','mz-sel','patient-sel'];
  selList.map(item => {
    let { id='',className, data, width='' } = item;
    if(classNameList.indexOf(className) > -1) {
      initDeptInpAndSel(id,className,data,width)
    }
    // else if(className==='bbmc-sel') {
    //   initBbmcInpAndSel(className,data,width);
    // }
    else {
      initInpAndSel(className,data,width);
    }
  });
}
// 输入框事件 支持模糊查询
function bindEleFun(eleId,isClass) {
  let eleAttr = isClass ? '.'+eleId : '#'+eleId;
  $(eleAttr).on('input',function() {
    var _this = $(this); 
    let val = _this.val();
    let _thisId = _this.attr('id');
    let selName = _this.attr('sel-name');
    selList.map(item => {
      let { id='',className='' } = item;
      let allList = []; // 所有的下拉数据
      let filterOptList = []; // 过滤的下拉数据
      let optionList = []; // 最终的下拉数据
      // 检查项目、申请科室、申请医生
      if(_thisId === id) {
        let inpSelOptList = {
          'bljcsqd-rt-40': examItemList,
          'bljcsqd-rt-42': deptList,
          'bljcsqd-rt-44': dictUsersList,
        };
        allList = inpSelOptList[id];
        if(_thisId === 'bljcsqd-rt-40') {
          filterOptList = allList.filter(dataItem => dataItem.itemName.includes(val));
          _this.removeAttr('itemcode');
        }else if(_thisId === 'bljcsqd-rt-42') {
          filterOptList = allList.filter(dataItem => dataItem.deptName.includes(val));
          _this.removeAttr('deptcode');
          $('#bljcsqd-rt-44').val('');
        }else if(_thisId === 'bljcsqd-rt-44') {
          filterOptList = allList.filter(dataItem => dataItem.title.includes(val));
        }
        optionList = val ? filterOptList : allList;
        // 重载方法
        dropdown.reload(id, {
          data: optionList, 
          show: true // 重载即显示组件面板
        });
      }else if(selName!=='gdy-sel' && selName !== 'bbmc-sel' && selName === className) {
        // 标本名称、标本部位、标本类别
        let inpSelOptList = {
          // 'bbmc-sel': gmSampleNameList,
          'bbbw-sel': gmSamplePartList,
          'bblb-sel': gmTypeNameList,
        };
        allList = inpSelOptList[selName];
        // if(selName === 'bbmc-sel') {
        //   filterOptList = allList.filter(dataItem => dataItem.value && dataItem.value.includes(val));
        // }else 
        if(selName === 'bbbw-sel') {
          filterOptList = allList.filter(dataItem => dataItem.partName.includes(val));
        }else if(selName === 'bblb-sel') {
          filterOptList = allList.filter(dataItem => dataItem.title.includes(val));
        }
        optionList = val ? filterOptList : allList;
        // 重载方法
        dropdown.reload(_thisId, {
          data: optionList, 
          show: true // 重载即显示组件面板
        });
        
      }
    });
  });
}
// 初始化申请科室和申请医生下拉菜单，含id
function initDeptInpAndSel(id,selClass,optionList,lenVal) {
  let isExpect = isInitInpAndSel(selClass);
  if(!isExpect) return;
  let selLen = 230;
  lenVal ? selLen = lenVal : '';
  // let selLen = ''
  dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${id}`,
    data: optionList,
    className: 'laySelLab',
    id: id,
    click: function(obj){
      this.elem.val(obj.title);
      if(selClass === 'hosp-sel') {
        // 申请医院联动申请科室，科室联动医生
        getDeptList();
        dictUsersList = [];
        $('#bljcsqd-rt-42').removeAttr('deptCode');
        $('#bljcsqd-rt-42').val('');
        $('#bljcsqd-rt-44').removeAttr('staffNo');
        $('#bljcsqd-rt-44').val('');
        // 重载方法
        let initSelList = {
          'bljcsqd-rt-42': deptList,
          'bljcsqd-rt-44': dictUsersList,
        }
        for(let key in initSelList) {
          dropdown.reload(key, {
            data: initSelList[key], 
            show: false // 重载即显示组件面板
          });
        }
      }
      if(selClass === 'dept-sel') {
        let deptCode = obj.deptCode;
        $('#bljcsqd-rt-42').attr('deptCode',obj.deptCode);
        // 申请科室联动申请医生
        if(deptCode) {
          $('#bljcsqd-rt-44').removeAttr('staffNo');
          $('#bljcsqd-rt-44').val('');
          getDictUsersList(deptCode);
          // 重载方法
          dropdown.reload('bljcsqd-rt-44', {
            data: dictUsersList, 
            show: false // 重载即显示组件面板
          });
        }
      }else if(selClass === 'doct-sel') {
        $('#bljcsqd-rt-44').attr('staffNo','');
      }else if(selClass === 'examsub-sel') {
        $('#bljcsqd-rt-40').val('');
        $('#bljcsqd-rt-46').val('');
        $('#bljcsqd-rt-40').removeAttr('itemCode');
        getExamItemList(obj.title);
        getGmInfoListHandler(dropdown);
        // 重载方法
        dropdown.reload('bljcsqd-rt-40', {
          data: examItemList, 
          show: false // 重载即显示组件面板
        });
      }else if(selClass==='examitem-sel') {
        $('#bljcsqd-rt-40').attr('itemCode',obj.itemCode);
        $('#bljcsqd-rt-46').val(obj.price);
      }
      if(selClass === 'sex-sel') {
        lastMensesDateIsReq(obj.title);
      }
    },
    style: `width: ${selLen}px;`
  });
}
// 初始化输入选择下拉框
function initInpAndSel(selClass,optionList,lenVal,id) {
  let isExpect = isInitInpAndSel(selClass);
  if(!isExpect) return;
  let selLen = 230;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: id ? `[id="${id}"]` : `.${selClass}`,
    data: optionList,
    className: 'laySelLab',
    id: id || '',
    click: function(obj){
      this.elem.val(obj.title);
      // if(selClass === 'sex-sel') {
      //   lastMensesDateIsReq(obj.title);
      // }
    },
    style: `width: ${selLen}px;`
  });
}
// 初始化标本名称输入选择下拉框
function initBbmcInpAndSel(selClass,optionList,lenVal,id) {
  let isExpect = isInitInpAndSel(selClass);
  if(!isExpect) return;
  let selLen = 230;
  lenVal ? selLen = lenVal : '';
  bbmcDropdown = layui.dropdown;
  bbmcDropdown.render({
    elem: id ? `[id="${id}"]` : `.${selClass}`,
    data: optionList,
    className: 'bbmcInpSel',
    id: id || '',
    ready: function() {
      valueLike = '';
      initBbmcParams();
      initFlowFun(id);
    },
    click: function(obj){
      this.elem.val(obj.title);
    },
    style: `width: ${selLen}px;`
  });
}
// 是否初始化输入选择下拉框
function isInitInpAndSel(selClass) {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ? rtStructure.enterOptions.publicInfo : {};
  var gmList = enterData.gmSampleList || []; // 标本信息列表
  let selCode = $('.'+selClass).attr('code');
  for(let i=0;i<gmList.length;i++) {
    let sampleStatus = gmList[i].sampleStatus;
    if(unEditSampleSta.includes(sampleStatus)) {
      let isExpect = unEditAttrExcept.includes(selCode);
      return isExpect;
    }
  }
  if(disableCode.includes(selCode) && isSaveApply) {
    return false;
  }
  return true;
}
// 末次月经是否必填
function lastMensesDateIsReq(title) {
  if(title === '男') {
    $('#bljcsqd-rt-23').siblings().css('display','none');
    $('#bljcsqd-rt-23').removeAttr('rt-req');
  }else {
    $('#bljcsqd-rt-23').siblings().css('display','inline-block');
    $('#bljcsqd-rt-23').attr('rt-req','1');
  }
}
// 初始化日期时间插件
function initDatePicker(timeType) {
  // 时间控件的code
  var timeOrDateArr = ['date-wrap01','date-wrap02','date-wrap03'];
  for(let dateClass of timeOrDateArr) {
    let isExpect = isInitInpAndSel(dateClass);
    if(!isExpect) continue;
  }
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('laydate', function () {
      var laydate = layui.laydate;
      //执行一个laydate实例
      if(timeType) {
        // 离体时间
        laydate.render({
          elem: '.time-wrap01', //指定元素
          type: 'datetime',
          trigger: 'click',
          format: 'yyyy-MM-dd HH:mm:ss',
          done: function(value){
            compareDateTime('lt',value);
          },
        });
        // 固定时间
        laydate.render({
          elem: '.time-wrap02', //指定元素
          type: 'datetime',
          trigger: 'click',
          format: 'yyyy-MM-dd HH:mm:ss',
          done: function(value){
            compareDateTime('gd',value);
          },
        });
      }else {
        laydate.render({
          elem: '.date-wrap01', //指定元素
          type: 'date',
          trigger: 'click',
          format: 'yyyy-MM-dd',
          done: function(value){
            if(value && $('#bljcsqd-rt-25').is(':checked')) {
              $('#bljcsqd-rt-25').click();
            }
          }
        });
        laydate.render({
          elem: '.date-wrap02', //指定元素
          type: 'datetime',
          trigger: 'click',
          format: 'yyyy-MM-dd HH:mm:ss', // 需求10538改为时分秒
          className: 'yyDate',
          min: Date.now(),
          ready: function (value) {
            $('.laydate-btns-confirm').removeClass('laydate-disabled');
            $(".laydate-btns-time").off('click').on('click',function(){
              disabled_time();
              $(".laydate-time-list li").on('click',function(){
                disabled_time(value);
              });
            });
            $('.laydate-btns-now').off('click').on('click',function() {
              compareCurAndDisTime();
            })
            disabled_date();
          },
          change: function (value, date, endDate) {
            disabled_date();
          },
          done: function(value){
            if(value && frozeAppointConfig.weekendTipFlag) {
              var day = dayjs(value).day();
              // 如果是周末做提醒
              if([0, 6].indexOf(day) > -1) {
                layer.alert(frozeAppointConfig.weekendTipText || '', {title: '温馨提示'})
              }
            }
          }
        });
        laydate.render({
          elem: '.date-wrap03', //指定元素
          type: 'date',
          trigger: 'click',
          format: 'yyyy-MM-dd',
          done: function(value){
            getAgeByDate(value);
          }
        });
      }
  });
}

/**
  *设置不可选择的星期
  *
*/
function disabled_date() {
  var trElems = $(".layui-laydate-content tbody").find('tr');

  trElems.each(function () {
    $(this).find('td').each(function (tdIndex, tdElem) {
      //遍历td，index===0表示周日，index===6表示周六
      var targetIndex = tdIndex === 0 ? '7' : (tdIndex).toString();
      disabledDate.map(item => {
        let { day=[],time=[] } = item;
        if (day.indexOf(targetIndex) > -1 && !time) {
          // laydate-disabled是layui的样式类，添加后会禁用元素
          $(this).addClass('laydate-disabled');
        }
      })
      // if (disabledDate.indexOf(targetIndex) > -1) {
      //   // laydate-disabled是layui的样式类，添加后会禁用元素
      //   $(this).addClass('laydate-disabled');
      // }
    });
  });
}
// 禁用对应日期的时间
function disabled_time(value) {
  var hourListElems = $('.layui-laydate-content .laydate-time-list li:nth-child(1)').find('ol');
  var minListElems = $('.layui-laydate-content .laydate-time-list li:nth-child(2)').find('ol');
  var thisDay = $('.layui-this').attr('lay-ymd');
  var week = getweekday(thisDay);
  var curHour = $('li:nth-child(1)').find('ol li.layui-this').text();
  // 禁用时间段——时
  hourListElems.each(function() {
    $(this).find('li').each(function(liIndex,liElem) {
      //遍历li
      disabledDate.map(item => {
        let { day=[],time=[] } = item;
        if (day.indexOf(week) > -1 && time.length) {
          for(let i=0;i<time.length-1;i++) {
            let startTimeItem = time[i];
            let endTimeItem = time[i+1];
            let [hour1='',min1=''] = startTimeItem.split(':');
            let [hour2='',min2=''] = endTimeItem.split(':');
            for(let h=hour1;h<=hour2;h++) {
              // 当分钟的禁用不是00-59整段，时不可禁用
              if(Number(h) === Number(liIndex) && ((Number(h) === Number(hour1) && min1 === '00') || (Number(h) === Number(hour2) && min2 === '59'))) {
                $(this).addClass('laydate-disabled');
              }else if (Number(h) === Number(liIndex) && h != hour1 && h != hour2) {
                // laydate-disabled是layui的样式类，添加后会禁用元素
                $(this).addClass('laydate-disabled');
              }
            }
          }
        }
      })
    })
  });
  // 禁用时间段——分
  minListElems.each(function() {
    $(this).find('li').each(function(liIndex,liElem) {
      //遍历li
      disabledDate.map(item => {
        let { day=[],time=[] } = item;
        if (day.indexOf(week) > -1 && time.length) {
          for(let i=0;i<time.length-1;i++) {
            let startTimeItem = time[i];
            let endTimeItem = time[i+1];
            let [hour1='',min1=''] = startTimeItem.split(':');
            let [hour2='',min2=''] = endTimeItem.split(':');
            if(Number(curHour) === Number(hour1)) {
              // 起始时间
              for(let min=min1;min<=59;min++) {
                if (Number(min) === Number(liIndex)) {
                  // laydate-disabled是layui的样式类，添加后会禁用元素
                  $(this).addClass('laydate-disabled');
                }
              }
            }else if(Number(curHour) === Number(hour2)) {
              // 结束时间
              for(let min=0;min<=min2;min++) {
                if (Number(min) === Number(liIndex)) {
                  // laydate-disabled是layui的样式类，添加后会禁用元素
                  $(this).addClass('laydate-disabled');
                }
              }
            }else {
              for(let min=min1;min<=min2;min++) {
                if (Number(min) === Number(liIndex) && curHour === Number(hour1)) {
                  // laydate-disabled是layui的样式类，添加后会禁用元素
                  $(this).addClass('laydate-disabled');
                }
              }
            }
          }
        }
      })
    })
  });
}
// 推算当前日期时间是否在配置时间的禁用时间段
function compareCurAndDisTime() {
  let curDateAndTime = new Date();
  let year = curDateAndTime.getFullYear();
  let month = curDateAndTime.getMonth() + 1;
  let date = curDateAndTime.getDate();
  let hours = curDateAndTime.getHours();
  let minutes = curDateAndTime.getMinutes();
  let curDate = year + '-' + month + '-' + date;
  let week = getweekday(curDate);
  disabledDate.map(item => {
    // 2024-3-18
    let { day=[],time=[] } = item;
    if (day.indexOf(week) > -1) {
      if(time.length) {
        // 有时间段
        for(let i=0;i<time.length-1;i++) {
          let startTimeItem = time[i];
          let endTimeItem = time[i+1];
          let [hour1='',min1=''] = startTimeItem.split(':');
          let [hour2='',min2=''] = endTimeItem.split(':');
          if((Number(hours) > Number(hour1) && Number(hours) < Number(hour2)) || (Number(hours) === Number(hour1) && Number(minutes) > Number(min1) && Number(minutes) < 59) || (Number(hours) === Number(hour2) && Number(minutes) > 0 && Number(minutes) < Number(min2))) {
            confirm('当前时间不在可选日期范围');
            $('#bljcsqd-rt-38').val('');
          }
        }
      }else {
        confirm('当前时间不在可选日期范围');
        $('#bljcsqd-rt-38').val('');
      }
    }
  })
}
// 根据日期推算星期
function getweekday(date){
  var weekArray = new Array("7", "1", "2", "3", "4", "5", "6");
  var week = weekArray[new Date(date).getDay()];//注意此处必须先new一个Date
  return week;
}
// 固定时间要大于离体时间
function compareDateTime(type,value) {
  var curInpDom = $(curInpEle);
  // 离体时间
  if(type === 'lt') {
    var id = curInpDom.attr('id');
    var gdDateVal = '';
    if(id) {
      var tailId = id.split('.')[1];
      var gdDateId = 'bljcsqd-rt-05.'+tailId;
      gdDateVal = $('[id="'+gdDateId+'"]').val();
    }else {
      gdDateVal = $('.new-gdsj').val();
    }
    var newLtDate = new Date(value).getTime();
    var newGdDate = new Date(gdDateVal).getTime();
    if(newLtDate > newGdDate) {
      $wfMessage({
        content: ('离体时间不能超过固定时间')
      });
      curInpDom.val('');
    }
  }else if(type === 'gd') {
    var id = curInpDom.attr('id');
    var ltDateVal = '';
    if(id) {
      var tailId = id.split('.')[1];
      var ltDateId = 'bljcsqd-rt-04.'+tailId;
      ltDateVal = $('[id="'+ltDateId+'"]').val();
    }else {
      ltDateVal = $('.new-ltsj').val();
    }
    var newGdDate = new Date(value).getTime();
    var newLtDate = new Date(ltDateVal).getTime();
    if(newLtDate > newGdDate) {
      confirm('离体时间不能超过固定时间');
      curInpDom.val('');
    }
  }
}
// 标本类别默认值
// 为true时，当是否冰冻选“是”，标本类别默认“冰冻”；“否”时，标本类别默认“常规”
// 为false时，不处理
function setBbTypeDefault(idOrClass) {
  if(sampleType) {
    let bdRadioVal = $('#bljcsqd1 .bd-item input[type="radio"]:checked').val();
    $(`#bljcsqd1 ${idOrClass}:visible`).each(function(i, dom) {
      if(bdRadioVal === '是') {
        $(dom).val('冰冻');
      }else if(bdRadioVal === '否') {
        $(dom).val('常规');
      }
    });
  }
}
// 回显表格
function showTableVal(findDom, data) {
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  let resultDataLen = resultData.length;
  let childData = resultData[resultDataLen-1] && resultData[resultDataLen-1].child ? JSON.parse(JSON.stringify(resultData[resultDataLen-1].child)) : [];
  let flagId = '';
  if(data) {
    childData = data;
  }
  if(data || resultDataLen>1) {
    childData.map(function(item) {
      let childArr = item.child || [];
      childArr.map(function(cItem) {
        flagId = cItem.id ? cItem.id.split('.')[1] : '';
      });
      flagId ? addRow(flagId,findDom,childArr) : '';
    })
  }
}
// 新增行
function addRow(flagId,findDom,childArr){
  let cloneTr = $('.cloneTr:first').clone(true);
  let cloneTd = cloneTr.find('td');
  let notHiddenInp = cloneTr.find('input[type="text"]');
  let hiddenInp = cloneTr.find('input[type="hidden"]');
  let randomNum = createUUidFun();
  // 需求16914增加“标本数量”
  let hasBBsl = cloneTr.find('.bbsl').length > 0;
  let hasBBzl = cloneTr.find('.bbzl').length > 0 && isShowBbzl;  //标本重量req17107
  let inpSortArr = hasBBsl ? ['01','02','03','07','04','05','06'] : ['01','02','03','04','05','06'];
  if(hasBBzl) {
    inpSortArr = ['01','02','03','07','08','04','05','06']
  }
  let pid = '', selNameList = [];
  let bblbId = '', ltTimeId = '', gdTimeId = '',gdyId = '',bbslId = '';
  if(flagId && typeof flagId === 'string') {
    pid = 'bljcsqd-rt-00' + '.' + flagId;
  }else {
    pid = 'bljcsqd-rt-00' + '.' + randomNum;
  }
  rtStructure.idAndDomMap[pid] = {
    id: pid,
    desc: '列表行',
    name: '列表行',
    pid: 'bljcsqd-rt-65',
    pvf: '',
    value: '1',
    wt: '',
    vt: '',
  }
  $(hiddenInp).attr('id',pid);
  $(hiddenInp).attr('pid','bljcsqd-rt-65');
  $(hiddenInp).addClass('rt-sr-w');

  // 预览
  if(flagId && findDom === 'span:last') {
    inpSortArr = hasBBsl ? [ '','01','02','03','07','04','05','06'] : [ '','01','02','03','04','05','06'];
    if(hasBBzl) {
      inpSortArr = [ '','01','02','03','07','08','04','05','06'];
    } else {
      $('.bbxx-table .bbzl-th').remove();
      $('.bbxx-table .bbzl').remove();
      $('.bbxx-table th:eq(5)').attr('width', '262');
      $('.bbxx-table th:eq(6)').attr('width', '260');
      $('.bbxx-table th:eq(7)').attr('width', '180');
    }
    childArr.map(function(cItem) {
      if(findDom === 'span:last') {
        cloneTd.each(function(i,dom) {
          id = 'bljcsqd-rt-' + inpSortArr[i]  + '.' + flagId;
          let tdEle = $(dom);
          i!==0 && cItem.id.includes(id) ? tdEle.text(cItem.val + `${cItem.val && inpSortArr[i] === '08' ? 'kg' : ''}`) : '';
        })
      }
    })
    // $('thead tr th:last').css('display','none');
    // $('tbody tr td:last').css('display','none');
  }
  // 新增与编辑
  notHiddenInp.each(function(i,dom){
    let ele = $(dom);
    let inpName = ele.attr('inp-name');
    let selName = ele.attr('sel-name');
    let id = '';
    if(flagId && findDom === 'input[type="text"]') {
      id = 'bljcsqd-rt-' + inpSortArr[i]  + '.' + flagId;
      childArr.map(function(cItem) {
        cItem.id.includes(id) ? ele.val(cItem.val) : '';
        // 设置标本重量必填
        if(inpSortArr[i] === '08' && cItem.val && String(cItem.val).indexOf('胎盘') > -1) {
          ele.attr('rt-req', '1');
        }
      })
    }else {
      id = 'bljcsqd-rt-' + inpSortArr[i]  + '.' + randomNum;
      if(inpSortArr[i]==='03') {
        bblbId = id;
      }if(inpSortArr[i]==='04') {
        ltTimeId = id
      }else if(inpSortArr[i]==='05') {
        gdTimeId = id
      }else if(inpSortArr[i]==='06') {
        gdyId = id;
      }else if(inpSortArr[i]==='07') {
        bbslId = id;
      }
    }
    rtStructure.idAndDomMap[id] = {
      id: id,
      desc: inpName,
      name: inpName,
      pid: pid,
      pvf: '',
      value: '',
      wt: '1',
      vt: '',
      aa: ''
    }
    ele.addClass('rt-sr-w');
    ele.attr('id',id);
    ele.attr('pid',pid);
    if(selName) {
      selNameList.push({selName, id: `${id}`});
    }
  })
  cloneTr.css('display','table-row');
  $('.bljcsqdTableBody tr:last').after(cloneTr);
  selList.map(item => {
    selNameList.map(classItem => {
      if(item.className === classItem.selName) {
        let { className, data, width='' } = item;
        // 部位和类型根据检查子类联动
        if(className === 'bbbw-sel') {
          data = gmSamplePartList;
        }
        if(className === 'bblb-sel') {
          data = gmTypeNameList;
        }
        if(className === 'bbmc-sel') {
          initBbmcInpAndSel(className,data,width,classItem.id);
        }else {
          initInpAndSel(className,data,width,classItem.id);
        }
        bindEleFun(className,true);
      }
    });
  });
  initDatePicker(true);
  // 新增默认值
  if(findDom==='add') {
    var dateAndTime = getCurDateAndTime(false);
    var moment = dateAndTime.curDate;
    var momentTime = dateAndTime.curTime;
    var defaultDate = moment + ' ' + momentTime;
    var ltsjToHm = new Date(defaultDate).getTime();
    var sjcTohm = fixativeGap * 60 * 1000;
    var fixativeGapDate = ltsjToHm + sjcTohm;
    var fixativeGapDateFormat = dayjs(fixativeGapDate).format('YYYY-MM-DD HH:mm:ss');
    var gdyList = $('.gdy-sel:not(:first)');
    var lastGdyVal = '';
    gdyList.each(function(i,dom) {
      if(i===getRowLength()-2) {
        lastGdyVal = $(dom).val();
      }
    })
    var newGdyVal = $('.new-gdy').val();
    var newLtTimeVal = $('.new-ltsj').val();
    var newGdTimeVal = $('.new-gdsj').val();
    // 输入离体时间、固定时间、固定液后，按当前输入的显示
    $('[id="'+gdyId+'"]').val(newGdyVal || lastGdyVal || defaultGmFixative);
    $('[id="'+ltTimeId+'"]').val(newLtTimeVal || defaultDate);
    $('[id="'+gdTimeId+'"]').val(newGdTimeVal || fixativeGapDateFormat);
    $('[id="'+bbslId+'"]').val('1');
    setBbTypeDefault('[id="'+bblbId+'"]');
    initBbmcParams();
  }
  $(".layout-body")[0].scrollTop = $(".layout-body")[0].scrollHeight;
  hideOrShowTable();
}
// 获取当前日期
function getCurDT(noSecond,minNum) {
  var date = new Date(minNum);
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var minute = date.getMinutes();
  var second = date.getSeconds();
  var curDate = year + '-' + addZero(month) + '-' + addZero(day)
  var curTime = addZero(hour) + ':' + addZero(minute) + (!noSecond ? ':' + addZero(second) : '');
  return {curDate: curDate, curTime: curTime};
}
// 刷新标本信息的固定液、离体时间、固定时间
function refresh() {
  var curELeTr = $(".bljcsqdTableBody").find("tr:not(:first)");
  var checkList = curELeTr.find('input[type="checkbox"]:checked');
  var checkTrList = checkList.closest('tr');
  if(!checkList.length) return;
  let refreshValList = {
    '离体时间': $('.new-ltsj').val(),
    '固定时间': $('.new-gdsj').val(),
    '固定液': $('.new-gdy').val(),
  };
  let flag = confirm('确认刷新吗？');
  if(flag) {
    checkTrList.each(function(i,dom) {
      let tdEleList = $(dom).find('input[type="text"]');
      tdEleList.each(function(tdIdx,td) {
        let tdEle = $(td);
        let inpName = tdEle.attr('inp-name');
        for(let key in refreshValList) {
          let newVal = refreshValList[key];
          if(inpName===key) {
            newVal ? tdEle.val(newVal) : '';
          }
        }
      })
    })
  }
}
// 删除行
function removeRow(item,type) {
  var curELeTr = $(".bljcsqdTableBody").find("tr:not(:first)");
  var checkList = curELeTr.find('input[type="checkbox"]:checked');
  if(type==='all'&&!checkList.length) return;
  let flag = confirm('确认删除吗？');
  if(flag) {
    if(type==='all') {
      $('.all-check').prop('checked',false);
      checkList.each(function(i,dom) {
        $(dom).closest("tr").remove();
      })
    }else {
      $(item).closest("tr").remove();
    }
  }
  hideOrShowTable();
}
// 表格复选框
function changeTableCk(inpEle,type) {
  var curELeTr = $(".bljcsqdTableBody").find("tr:not(:first)");
  var isChecked = $(inpEle).is(':checked');
  var checkList = curELeTr.find('input[type="checkbox"]:checked');
  if(type==='all') {
    var curEleTrCk = curELeTr.find('input[type="checkbox"]');
    curEleTrCk.each(function(i,dom) {
      $(dom).prop('checked',isChecked);
    })
    // checkList = curELeTr.find('input[type="checkbox"]:checked');
  }else {
    isChecked = checkList.length === getRowLength() ? true : false;
    $('.all-check').prop('checked',isChecked);
  }
  // !isChecked ? checkList = null : '';
}
function hideOrShowTable() {
  if(getRowLength() < 1) {
    $('.bbxx-table').hide();
  }else {
    $('.bbxx-table').show();
  }
}
function getRowLength()
{
  // 统计当前表格行数，防止删空
  return $(".bljcsqdTableBody").find("tr:not(:first)").length;
}
// 转换下拉数据格式
function transformOptList(list,titleName) {
  let arr = [];
  list.forEach((item,index) => {
    if(titleName) {
      arr.push({ ...item,title:item[titleName],id:index,templet: `<span title='${item[titleName]}'>${item[titleName]}</span>` });
    }else {
      arr.push({ title:item,id:index,templet: `<span title='${item}'>${item}</span>` });
    }
  })
  return arr;
}
// 获取系统配置项
function getGmSettings() {
  var enterData = rtStructure && rtStructure.enterOptions && rtStructure.enterOptions.publicInfo ? rtStructure.enterOptions.publicInfo : {};
  var gmList = enterData.gmSampleList || []; // 标本信息列表
  var paramNameArr = ['apply.cannotScheduleWeekTime','print.sampleTypeDefault','unableEDitApplyAttribute', 'apply.chargedFlag','apply.autoGenerationSickId',
    'apply.unableEditSampleStatus','apply.unableEditAttributeExcepted','apply.hideApplyAttribute','apply.associatedExamRptEnabled',
    'apply.frozenAppointmentScheduleWeekendTip', 'apply.frozenAppointmentScheduleWeekendTipContent',
    'print.fillSampleWeightEnable'
  ];
  var params = { paramName: paramNameArr.join(',') };
  disabledDate = [];
  fetchAjax({
    url: api.getGmSettings,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0' && res.result) {
        res.result.map(item => {
          let { paramName='',paramValue='' } = item;
          if(paramName === 'apply.cannotScheduleWeekTime') {
            let parseParamValue = JSON.parse(paramValue);
            disabledDate = parseParamValue;
            // disabledDate = [
            //   { day: ['3','4'],
            //     time: ['14:10','19:10']
            //   },
            //   {
            //     day: ['6','7'],
            //     time: null
            //   }
            // ]
            // disabledDate = [
            //   {
            //     "day": ["1","3","5"],    //日期周一~周日，可多选
            //     "time": ["12:00", "18:30"]    //时间，第一个元素为开始时间， 第二个元素为结束时间
            //   },
            //   {
            //     "day": ["2","7"],  //周二，周日
            //     "time": ["10:10","18:30"]  //10:10 到 18:30
            //   }
            // ]
            // console.log('disabledDate-->',disabledDate);
          }else if(paramName === 'print.sampleTypeDefault') {
            sampleType = paramValue;
          }else if(paramName === 'unableEDitApplyAttribute') {
            disableCode = paramValue.split(',');
            codeToDisEdit();
          }else if(paramName === 'apply.chargedFlag') {
            // 南沙区域病理需求,是否默认已收费;值为true时,默认选中已缴费
            if(paramValue === 'true') {
              let chargeConfig = {
                chargeFlag: paramValue === 'true' ? '1' : '0'
              }
              resetEleVal('bljcsqd1','jf-item','chargeFlag',chargeConfig);
            }
          }else if(paramName === 'apply.autoGenerationSickId') {
            // 为true时，病人ID非必填；其余情况必填
            if(paramValue === 'true') {
              $('#bljcsqd-rt-7').siblings().css('display','none');
              $('#bljcsqd-rt-7').removeAttr('rt-req');
            }
          }else if(paramName === 'apply.unableEditSampleStatus') {
            // 限制不能修改申请单信息的标本状态,已打包流程就不能修改
            unEditSampleSta = paramValue.split(',');
            for(let i=0;i<gmList.length;i++) {
              let sampleStatus = gmList[i].sampleStatus;
              if(unEditSampleSta.includes(sampleStatus)) {
                setUnEditSampleInfo();
                return;
              }
            }
            
          }else if(paramName === 'apply.unableEditAttributeExcepted') {
            // 不受限制的字段
            // 1、“不受限制的字段”配置 需要排除 “不可编辑的申请单信息配置” 中配置的字段，“不可编辑的申请单信息配置”优先级最高；
            // 2、当状态到达已打包50，“不受限制的字段”存在标本相关信息，可修改，不受“状态”限制
            unEditAttrExcept = paramValue.split(',');
            if(unEditAttrExcept.length) {
              unEditAttrExceptFun();
            }
          }else if(paramName === 'apply.hideApplyAttribute') {
            hideAttrCode = paramValue.split(',');
            setHideCodeItem();
          }else if(paramName === 'apply.associatedExamRptEnabled') {
            isShowTqnjzdBtn = paramValue;
            if(!isShowTqnjzdBtn) {
              $('.tqnjzd-btn').hide();
            }
          }else if(paramName === 'apply.frozenAppointmentScheduleWeekendTip') {
            frozeAppointConfig['weekendTipFlag'] = paramValue === 'true';
          } else if(paramName === 'apply.frozenAppointmentScheduleWeekendTipContent') {
            frozeAppointConfig['weekendTipText'] = paramValue;
          } else if(paramName === 'print.fillSampleWeightEnable') {
            if(paramValue !== 'true') {
              $('.bbxx-table .bbzl-th').remove();
              $('.bbxx-table .bbzl').remove();
              isShowBbzl = false;
            }
          }
        });
      }
    },
  })

  if(isShowBbzl) {
    // 标本重量只允许输入数字和小数点
    curElem.find('.bbxx-table').on('input', '.bbzl .inp-sty', function() {
      const value = $(this).val();
      const regex = /^\d{0,1}(\.\d{0,5})?$/;

      if (!regex.test(value)) {
          // 移除不符合条件的字符
          const cleanedValue = value.replace(/[^0-9.]/g, '').replace(/\.(?=.*\.)/g, '');
          // 确保小数点前最多1位
          const parts = cleanedValue.split('.');
          if (parts[0].length > 1) {
            parts[0] = parts[0].slice(0, 1);
          }
          if (parts[1] && parts[1].length > 5) {
            parts[1] = parts[1].slice(0, 5);
          }
          $(this).val(parts.join('.'));
      }
    })
  }
}
// 通过code禁用对应控件
function codeToDisEdit() {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  if(!isSaveApply) {
    return;
  }
  // 已知code的关联项
  var concatItemMap = {
    '3': ['#bljcsqd-rt-68', '#bljcsqd-rt-69'],  //年龄
    '310': ['#bljcsqd-rt-29'],  //是否传染病
    '32': ['#bljcsqd-rt-25'],  //末次月经
  }
  for(let code of disableCode) {
    var eleList = $('[code="'+code+'"]');
    if(eleList.length) {
      if(concatItemMap[code]) {
        var concatList = concatItemMap[code];
        concatList.forEach(function(cItem) {
          $(`${cItem}`).attr('disabled',true);
        })
      }
      eleList.each(function(i,dom) {
        var type = $(dom).attr('type');
        if(type === 'radio' || type === 'checkbox') {
          eleList.prop('disabled',true);
          eleList.addClass('rt-disabled');
          eleList.css('cursor','not-allowed');
        }else {
          $(dom).attr('disabled',true);
          $(dom).attr('readonly',true);
          $(dom).addClass('rt-disabled');
          $(dom).css('background','#fafafa');
        }
      })
    }
  }
}
// 设置不可编辑的标本信息
function setUnEditSampleInfo() {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  if(!isSaveApply) {
    return;
  }
  // 配置中的标本状态包含当前标本信息状态时，禁用所有控件
  var allEle = $('#bljcsqd1').find('[rt-sc]');
  // 禁用按钮
  $('#bljcsqd1 .tit-right .btn-sty').css('cursor','not-allowed');
  $('#bljcsqd1 .tit-right .btn-sty').removeAttr('onclick');
  $('#bljcsqd1 .del-tbtn').css('cursor','not-allowed');
  $('#bljcsqd1 .del-tbtn').removeAttr('onclick');
  allEle.each(function(i,dom) {
    var type = $(dom).attr('type');
    var tagName = dom.tagName;
    if(type === 'radio' || type === 'checkbox') {
      $(dom).prop('disabled',true);
      $(dom).addClass('rt-disabled');
      $(dom).css('cursor','not-allowed');
    }else if(type === 'text' || tagName === 'SELECT' || tagName === 'TEXTAREA'){
      $(dom).attr('readonly',true);
      $(dom).css('cursor','not-allowed');
      $(dom).css('background','#fafafa');
    }
  })
}
// 不受配置的标本状态限制的控件,“不可编辑的申请单信息配置”优先级最高
function unEditAttrExceptFun() {
  var isSaveApply = publicInfo.busId || publicInfo.applyNo;  //通过这个标识为编写过的申请单
  if(!isSaveApply) {
    return;
  }
  for(let code of unEditAttrExcept) {
    if(!disableCode.includes(code)) {
      var exceptCode = $('[code="'+code+'"]');
      exceptCode.prop('disabled',false);
      exceptCode.attr('readonly',false);
      exceptCode.removeClass('rt-disabled');
      exceptCode.css('cursor','unset');
      exceptCode.css('background','#fff');
    }
  }
}
// 同个code隐藏属性
function setHideCodeItem() {
  for(let code of hideAttrCode) {
    var eleList = $('[code="'+code+'"]');
    if(eleList.length) {
      eleList.each(function(i,dom) {
        $(dom).closest('.apply-item').remove();
      })
    }
  }
}
// 获取性别列表
function getGenderList() {
  var params = {};
  fetchAjax({
    url: api.getGenderList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        genderList = transformOptList(data,'sexName');
      }
    },
  })
}
// 获取民族列表
function getNationList() {
  var params = {};
  fetchAjax({
    url: api.getNationList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        nationList = transformOptList(data,'nationName');
      }
    },
  })
}
// 获取病人来源列表
function getPatientSourceList() {
  var params = {};
  fetchAjax({
    url: api.getPatientSourceList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        patientSourceList = transformOptList(data,'patientSourceName');
      }
    },
  })
}
// 获取申请医院列表
function getHospitalList() {
  var params = {}
  fetchAjax({
    url: api.getHospitalList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        hospitalList = transformOptList(data,'hospitalName');
      }
    },
  })
}
// 获取申请科室列表
function getDeptList() {
  var params = {
    hospitalName: $('#bljcsqd-rt-74').val() || ''
  };
  fetchAjax({
    url: api.getDeptList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        deptList = transformOptList(data,'deptName');
        deptList.map(function(item) {
          let curDeptName = $('#bljcsqd-rt-42').val();
          if(curDeptName===item.deptName) {
            $('#bljcsqd-rt-42').attr('deptCode',item.deptCode);
          }
        })
      }
    },
  })
}
// 获取申请医生列表
function getDictUsersList(deptCode) {
  var params = { deptCode };
  if(!params.deptCode) return;
  fetchAjax({
    url: api.getDictUsersList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        dictUsersList = transformOptList(data);
        if(firstLoad && getParamByName('optName') && data.length) {
          $('#bljcsqd-rt-44').val(getParamByName('optName'));
        }
        let curDoctName = $('#bljcsqd-rt-44').val();
        dictUsersList.map(item => {
          if(curDoctName===item.name) {
            $('#bljcsqd-rt-44').attr('staffNo',item.staffNo);
          }
        })
      }
    },
  })
}
// 获取标本部位列表
function getGmSamplePartList() {
  var params = {
    examSubClass: $('.examsub-sel').val() || ''
  };
  fetchAjax({
    url: api.getGmSamplePartList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        gmSamplePartList = transformOptList(data,'partName');
      }
    },
  })
}
// 获取标本类别列表
function getGmTypeNameList() {
  var params = {
    examSubClass: $('.examsub-sel').val() || ''
  };
  fetchAjax({
    url: api.getGmTypeNameList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        gmTypeNameList = transformOptList(data);
      }
    },
  })
}
// 初始化流加载
function initFlowFun(id) {
  if(!id) return;
  bbmcId = id;
  layui.use('flow', function(){
    var flow = layui.flow;
    flow.load({
      elem: '.layui-dropdown-menu' //指定列表容器
      ,scrollElem: '.bbmcInpSel'
      ,done: function(flowPageNo, next){ //到达临界点（默认滚动触发），触发下一页
        var lis = [];
        //请求下一页数据（注意：pageNo是从2开始返回）
        let { pageNo=1, pageSize=20 } = pageParams;
        let params = {
          pageNo: flowPageNo,
          pageSize,
          valueLike,
        }
        fetchAjax({
          url: api.getGmSampleNameListV2,
          data: JSON.stringify(params),
          async: false,
          successFn: function(res) {
            layui.each(res.result, function(index, item){
              lis.push('<li title="'+item.value+'" onclick="selBbmcLi(this)">'+ item.value +'</li>');
            });
            // 执行下一页渲染，第二参数为：满足“加载更多”的条件，即后面仍有分页
            // pages为接口返回的总页数，只有当前页小于总页数的情况下，才会继续出现加载更多
            next(lis.join(''), flowPageNo < res.page.pages);
          },
        })
      }
    });
  });
}
function selBbmcLi(vm) {
  let value = $(vm).text();
  $('.bbmcInpSel').hide();
  $('[id="'+bbmcId+'"]').val(value);
  // req17107,标本数量开启时，标本名称包含“胎盘”，重量必填
  var bbzlTarget =  $('[id="'+bbmcId+'"]').closest('tr').find('.bbzl .rt-sr-w');
  if(isShowBbzl && value.indexOf('胎盘') > -1) {
    bbzlTarget.attr('rt-req','1');
  } else {
    bbzlTarget.removeAttr('rt-req');
  }
}
// 初始化标本名称下拉事件
function initBbmcSelEv() {
  var _thisId = null;
  var bbmcInp = $('.bbmc-sel');
  bbmcInp.on("input",function(e) {
    _thisId = $(this).attr('id');
    valueLike = e.target.value;
    // 重载方法
    bbmcDropdown.reload(_thisId, {
      data: [], 
      show: true // 重载即显示组件面板
    });
    initBbmcParams();
    initFlowFun(_thisId);
  });

  bbmcInp.on("blur",function(e) {
    var bbmcValue = e.target.value;
    // req17107,标本数量开启时，标本名称包含“胎盘”，重量必填
    var bbzlTarget = $(e.target).closest('tr').find('.bbzl .rt-sr-w');
    if(isShowBbzl && bbmcValue.indexOf('胎盘') > -1) {
      bbzlTarget.attr('rt-req','1');
    } else {
      bbzlTarget.removeAttr('rt-req');
    }
  });
}
// 初始化标本名称相关参数
function initBbmcParams() {
  pageParams.pageNo = 1;
  // gmSampleNameList = [];
}
// 获取标本名称列表
function getGmSampleNameListV2() {
  let { pageNo=1, pageSize=20 } = pageParams;
  var params = {
    pageNo,
    pageSize,
    valueLike,
  };
  fetchAjax({
    url: api.getGmSampleNameListV2,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        gmSampleNameList = gmSampleNameList.concat(transformOptList(data,'value'));
      }
    },
  })
}
// 获取固定液列表
function getGmFixativeList() {
  var params = {};
  fetchAjax({
    url: api.getGmFixativeList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        var defaultGm = data.filter(item => item.defaultFlag);
        if(defaultGm && defaultGm.length) {
          defaultGmFixative = defaultGm[0].value || '';
        }
        gmFixativeList = transformOptList(data,'value');
      }
    },
  })
}
// 获取检查项目列表
function getExamItemList(examSubClass) {
  var params = { 
    examClass,
    examSubClass
  }
  if(!params.examClass) return;
  fetchAjax({
    url: api.getExamItemList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        examItemList = transformOptList(data,'itemName');
        examItemList.map(function(item) {
          let curExamItemName = $('#bljcsqd-rt-40').val();
          if(curExamItemName===item.itemName) {
            $('#bljcsqd-rt-40').attr('itemCode',item.itemCode);
          }
        })
      }
    },
  })
}
// 获取检查子类列表
function getExamSubClassList2() {
  var params = { examClass }
  if(!params.examClass) return;
  fetchAjax({
    url: api.getExamSubClassList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        var data = res.result || [];
        examSubClassList = transformOptList(data,'examSubclassName');
      }
    },
  })
}
// 获取病理固定时间与离体时间的间隔
function gmFixativeGap() {
  var params = {}
  fetchAjax({
    url: api.gmFixativeGap,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        fixativeGap = res.result || '';
      }
    },
  })
}
// 获取病人信息
function getApplyPatient(id) {
  // 测试数据，3个不同病人信息
  // sickId = 00026248 00026263
  // impatinetNo = **********
  // outpatientNo = 4548784
  let params = {
    sickId: $('#bljcsqd-rt-8').val() || '',
    inpatientNo: $('#bljcsqd-rt-10').val() || '',
    outpatientNo: $('#bljcsqd-rt-12').val() || '',
  };
  fetchAjax({
    url: api.getApplyPatient,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        let applyPatientInfo = res.result || {};
        if(JSON.stringify(applyPatientInfo) !== '{}') {
          let flag = confirm('是否确认覆盖当前病人信息？');
          if(flag) {
            showPatientInfo(id,applyPatientInfo);
          }
          getAgeByDate(applyPatientInfo.birthDate);
        }
      }
    },
  })
}
// 回显病人信息
function showPatientInfo(id,info) {
  $("#bljcsqd1 [pat-info]").each(function(t, tEle) {
    var tKey = $(tEle).attr('pat-info');
    var eleId = $(tEle).attr('id');
    eleId !== id ? $(tEle).val(info[tKey]) : '';
  });
  // 末次月经是否必填
  let sexVal = $('#bljcsqd-rt-6').val() || '';
  lastMensesDateIsReq(sexVal);
}
// 校验身份证号
function validateIdCard() {
  $('#bljcsqd-rt-18').on('blur',function(e) {
    var _this = $(this);
    var idCardVal = _this.val();
    var idCardMainLand = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/;//大陆
    if(idCardVal && !idCardMainLand.test(idCardVal.trim())) {
      $('#bljcsqd-rt-18').val('');
      confirm('请输入正确格式的身份证号');
    }
  })
}
// 校验手机号
function validatePhoneNum() {
  $('#bljcsqd-rt-20').on('blur',function(e) {
    var _this = $(this);
    var phoneNum = _this.val();
    var phoneReg = /^1[3456789]\d{9}$/;
    if(phoneNum && !phoneReg.test(phoneNum.trim())) {
      $('#bljcsqd-rt-20').val('');
      confirm('请输入正确格式的手机号');
    }
  })
}
// 年龄单位联动
function changeAgeUnit(ageUnit) {
  var ageUnitMap = {
    "岁":"月",
    "月":"天",
    "周":"天",
    "天":"时",
    "时":"分",
    "分":"秒",
  };
  var ageSubUnit = ageUnitMap[ageUnit];
  $('#bljcsqd-rt-70').html(ageSubUnit);
}
// 拆分年龄年月日
function splitAgeNum(ageUnit,firstInpVal,secondInpVal) {
  var ageYear = '',ageMonth = '',ageDay = '';
    if(ageUnit !== '岁') {
      ageYear = 0;
      ageMonth = 0;
      if(ageUnit === '月') {
        ageMonth = firstInpVal;
        ageDay = secondInpVal;
      }
      if(ageUnit === '周') {
        ageDay = Number(firstInpVal) * 7 + Number(secondInpVal);
      }
      if(ageUnit === '天') {
        ageDay = firstInpVal;
      }
      if(ageUnit === '时') {
        ageDay = firstInpVal >= Number(24) ? parseInt(firstInpVal/24) : 0;
      }
      if(ageUnit === '秒') {
        ageDay = 0;
      }
    } else {
      ageYear = firstInpVal;
      ageMonth = secondInpVal;
    }
    var birthDate = inferBirthdayByAge(ageYear, ageMonth, ageDay);
    $('#bljcsqd-rt-67').val(birthDate);
}
// 根据年龄推算出生日期
function getDateByAge(id,evType) {
  $('#'+id).on(evType,function(e) {
    var firstInpVal = $('#bljcsqd-rt-4').val();
    var ageUnit = $('#bljcsqd-rt-68').val();
    changeAgeUnit(ageUnit);
    var secondInpVal = $('#bljcsqd-rt-69').val();
    if(firstInpVal === '' && secondInpVal === '') {
      $('#bljcsqd-rt-67').val('');
      return;
    }
    splitAgeNum(ageUnit,firstInpVal,secondInpVal);
  })
}
// 根据出生年月日生成年龄
function getAgeByDate(birthDate) {
  if(!birthDate) {
    return;
  }
  let age = inferAgeByBirthday(birthDate, ',');
  if(age === -1 || !birthDate) {
    $('#bljcsqd-rt-4').val('');
    $('#bljcsqd-rt-68').val('岁');
    $('#bljcsqd-rt-69').val('');
  } else {
    let obj = splitStrFun(age);
    if(obj.ageUnit==='日') {
      obj.ageUnit = '天';
    }
    $('#bljcsqd-rt-4').val(obj.age);
    $('#bljcsqd-rt-68').val(obj.ageUnit);
    $('#bljcsqd-rt-69').val(obj.monthNum);
  }
}
function splitStrFun(str) {
  let res = str.split(',');
  let obj = {
    age: res[0],
    ageUnit: '岁',
    monthNum: '',
  }
  if(res.length === 3) {
    res[3] = '月';
  }
  if(res.length === 2) {
    if(res[1]==='月') {
      obj.age = '';
      obj.monthNum = res[0];
    } else {
      obj.age = res[0];
      obj.ageUnit = res[1];
    }
  } else if(res.length === 4) {
    obj.age = res[0];
    obj.ageUnit = res[1];
    obj.monthNum = res[2];
  }
  // console.log('res-->',res);
  return obj;
}
// 获取提取内镜诊断列表
function getEndoscopeRptList(orderType) {
  let { examClass='',examStatus='' } = publicInfo;
  if(!$('#bljcsqd-rt-8').val()) return;
  let params = {
    sickId: $('#bljcsqd-rt-8').val() || '',
    examClass: examClass,
    checkDays: checkDays,
    reportStatus: examStatus,
    orderType: orderType ? orderType : 0,
  };
  fetchAjax({
    url: api.getEndoscopeRptList,
    data: JSON.stringify(params),
    async: false,
    successFn: function(res) {
      if(res.status == '0') {
        endoscopeRptList = res.result || [];
      }
    },
  })
}
// 提取内镜诊断表格
function createNjzdTable() {
  // if(!endoscopeRptList || !endoscopeRptList.length) return;
  var dialogContent = '';
  dialogContent += `<div style="border-top: 1px solid #DCDFE6;height:100%;position: relative;padding: 0 8px;">`;
  // 列表
  dialogContent += `<div style="padding:12px 0;">`;
  dialogContent += `<span>查询近</span>`;
  dialogContent += `<input id="recentDayInp" type="text" style="width: 80px;height: 40px;border-radius: 4px;border: 1px solid #C0C4CC;padding: 0 12px;margin: 0 8px;">`;
  dialogContent += `<span>天内的结果</span>`;
  dialogContent += '<button id="searchBtn" style="margin-left:8px;padding:8px 20px;border-radius:3px;border: 1px solid #C0C4CC;background:#fff;cursor:pointer;"><img src="/sreport/template-lib/layouts/assets/images/search-icon.png">查询</button>';
  dialogContent += `</div>`;
  dialogContent += `<div style="height:528px;">`;
  dialogContent += `<table border="1" style="width:100%;height:100%;display: block;table-layout: fixed;text-align: left;border-color:rgba(0,0,0,0.1);color: #303133;">`;
  dialogContent += `<thead>`;
  dialogContent += `<tr style="color: #909399;height:48px;border: 1px solid rgba(0,0,0,0.1);">`;
  dialogContent += `<td style="width:172px;padding-left:12px;border-right: 1px solid rgba(0,0,0,0.1);"><div class="angle-btn-item"><div>检查时间</div><i id="ascSort" class="sort-btn angle-bottom inactive-color"></i><i id="desSort" class="sort-btn angle-top inactive-color"></i></div></td>`;
  dialogContent += `<td style="width:480px;padding-left:12px;border-right: 1px solid rgba(0,0,0,0.1);">内镜所见</td>`;
  dialogContent += `<td style="width:479px;padding-left:12px;">内镜结果</td>`;
  dialogContent += `</tr>`;
  dialogContent += `</thead>`;
  dialogContent += `<tbody style="height:calc(100% - 48px);display:block;overflow-y: auto;">`;
  for(let i = 0; i < endoscopeRptList.length; i++) {
    let item = endoscopeRptList[i];
    dialogContent += `<tr class="tr-row" style="font-size: 14px;cursor: pointer;">`;
    dialogContent += `<td style="max-width:172px;min-width:172px;padding: 14px 0 14px 12px;border: 1px solid rgba(0,0,0,0.1);border-top: none;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" title="${item.examDate + ' ' + item.examTime || ''}">${item.examDate + ' ' + item.examTime || ''}</td>`;
    dialogContent += `<td style="max-width:480px;min-width:480px;padding: 14px 0 14px 12px;border: 1px solid rgba(0,0,0,0.1);border-top: none;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" title="${item.description || ''}">${item.description || ''}</td>`;
    dialogContent += `<td style="max-width:479px;min-width:479px;padding: 14px 0 14px 12px;border: 1px solid rgba(0,0,0,0.1);border-top: none;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" title="${item.impression || ''}">${item.impression || ''}</td>`;
    dialogContent += `</tr>`;
  }
  dialogContent += `</tbody>`;
  dialogContent += `</table>`;
  dialogContent += `</div>`;
  dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0 8px 0;">';
  dialogContent += '<button id="confirmBtn" style="margin-left:8px;background:#1885F2;padding:9px 20px;border-radius:3px;color:#FFF;border:1px solid #1885F2;cursor:pointer;font-size: 16px;">加入申请单</button>';
  dialogContent += '</div>';
  drawDialog({
    title: '提取内镜诊断',
    content: dialogContent,
    modal: true,
    style: {
      width: '1180px',
      background: '#F5F7FA',
    },
  })
  // 输入框回车键查询
  rtDialog.find('#recentDayInp').on('keyup',function(e) {
    checkDays = Number(rtDialog.find('#recentDayInp').val());
    if(e.which === 13) {
      reloadList();
    }
  })
  // 点击查询
  rtDialog.find('#searchBtn').click(function() {
    reloadList();
  })
  // 升序
  rtDialog.find('#ascSort').click(function() {
    sortChangeExamDate($(this),'ascSort');
  })
  // 降序
  rtDialog.find('#desSort').click(function() {
    sortChangeExamDate($(this),'desSort');
  })
  // 点击行
  var curRowData = {};
  rtDialog.find('.tr-row').click(function() {
    $(this).siblings().css('background', '#FFF');
    $(this).css('background', '#A0CFFF');
    var index = $(this).index();
    curRowData = endoscopeRptList[index];
  })
  rtDialog.find('#confirmBtn').click(function() {
    addToApply(curRowData);
  })
}
// 加入申请单
function addToApply(curRowData) {
  var desc = $('#bljcsqd-rt-81').val() || '';
  var imp = $('#bljcsqd-rt-83').val() || '';
  var curDesc = curRowData.description || '';
  var curImp = curRowData.impression || '';
  if(desc || imp) {
    var dialogContent = '';
    dialogContent += `<div style="font-size: 18px;">当前内镜所见和内镜结果已有内容，</div>`;
    dialogContent += `<div style="font-size: 18px;">新内容是直接覆盖还是叠加显示？</div>`;
    dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0 8px 0;">';
    dialogContent += '<button id="directOverWrite" style="margin-left:8px;background:#1885F2;padding:9px 20px;border-radius:3px;color:#FFF;border:1px solid #1885F2;cursor:pointer;font-size: 16px;">直接覆盖</button>';
    dialogContent += '<button id="overlayDisplay" style="margin-left:8px;background:#1885F2;padding:9px 20px;border-radius:3px;color:#FFF;border:1px solid #1885F2;cursor:pointer;font-size: 16px;">叠加显示</button>';
    dialogContent += '<button onclick="closeTipHander()" style="margin-left:8px;background:#fff;padding:9px 20px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;font-size: 16px;">取消</button>';
    dialogContent += '</div>';
    drawDialog({
      title: '提示',
      content: dialogContent,
      modal: true,
      style: {
        width: '340px',
      },
    })
    // 直接覆盖
    rtDialog.find('#directOverWrite').click(function() {
      $('#bljcsqd-rt-81').val(curDesc);
      $('#bljcsqd-rt-83').val(curImp);
      preExamNo = curRowData.examNo;
      closeDiaHander();
    })
    // 叠加显示
    rtDialog.find('#overlayDisplay').click(function() {
      let overDesc = desc + curDesc;
      let overImp = imp + curImp;
      $('#bljcsqd-rt-81').val(overDesc);
      $('#bljcsqd-rt-83').val(overImp);
      preExamNo = curRowData.examNo;
      closeDiaHander();
    })
  }else {
    $('#bljcsqd-rt-81').val(curDesc);
    $('#bljcsqd-rt-83').val(curImp);
    preExamNo = curRowData.examNo;
    closeDiaHander();
  }
}
// 重置提取诊断列表
function reloadList(orderType) {
  getEndoscopeRptList(orderType);
  closeTipHander();
  createNjzdTable();
  rtDialog.find('#recentDayInp').val(checkDays);
}
// 升降序检查时间
function sortChangeExamDate(sortBtn,type) {
  sortBtn.addClass('active-color');
  sortBtn.siblings('.sort-btn').removeClass('active-color').addClass('inactive-color');
  setTimeout(function() {
    let orderType = type === 'ascSort' ? 1 : 0;
    // orderType=1是正序
    reloadList(orderType);
    rtDialog.find('#'+type).addClass('active-color');
    rtDialog.find('#'+type).siblings('.sort-btn').removeClass('active-color').addClass('inactive-color');
  },50);
}
// 显示"提取内镜诊断"弹窗
function showDescListDialog() {
  getEndoscopeRptList();
  createNjzdTable();
  rtDialog.find('#recentDayInp').val(checkDays);
}
// 关闭"提取内镜诊断"弹窗
function closeDiaHander() {
  // 关闭所有弹窗
  var closeBtnList = $(".rt-dialog-close");
  closeBtnList.each(function(i,dom) {
    var dateVal = $(dom).attr('date-val');
    $('.rt-dialog-wrap' + '-' + dateVal).remove();
  })
}
// 关闭“提示”弹窗
function closeTipHander() {
  removeRtDialog();
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
}