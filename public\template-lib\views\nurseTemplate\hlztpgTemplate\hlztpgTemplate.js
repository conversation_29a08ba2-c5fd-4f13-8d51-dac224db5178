$(function() {
  window.initHtmlScript = initHtmlScript;
})

var relationship = [
  {title: '父母',id: '1'},
  {title: '夫妻',id: '2'},
  {title: '亲属',id: '3'},
  {title: '朋友',id: '4'},
  {title: '其它',id: '5'},
]

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //模版是否保存
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      inintPreview('preview')
      window.getNurseList ? toNurseList('','') : '';
    } else {
      inintPreview('edit')
      layui.use(['laydate', 'form'], function(){
        var laydate = layui.laydate;
        var form = layui.form;
        form.render();
        //执行一个laydate实例
        laydate.render({
          elem: '#hlztpg-rt-0002',//指定元素
          type: 'datetime',
          value: !isSavedReport ? new Date() : ''
        });
      })
      inintPage()
      // console.log('rtStructure.enterOptions',rtStructure.enterOptions)
      // console.log('idAndDomMap',idAndDomMap)
    }
  }
}

function inintPreview(type){
  $(`[data-type]`).hide();
  $(`[data-type=${type}]`).show();
  if(type==='preview'){    
    $('[data-key]').each(function(){
      let key = $(this).attr('data-key')
      if(idAndDomMap){
        let result = []
        Object.keys(idAndDomMap).forEach(idKey=>{
          if(idKey.startsWith(key)){
           let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value:'';
           if(value){
            result.push(value)
           }
          }
        })
        console.log('result',result)
        
        if(key==='hlztpg-rt-0003' || key==='hlztpg-rt-0005' || key==='hlztpg-rt-0016' || key==='hlztpg-rt-0017' || key==='hlztpg-rt-0018' || key==='hlztpg-rt-0011'){
          result.shift();
          if(key==='hlztpg-rt-0005'&&result[0] == '正常'){
            $('.temperatureUnit').hide();
          }
          $(this).html(result.join(','))
        }else{
          $(this).html(result.join(','))
        }
        this.style.color = '#000'  
      }
    })
    if ($("#hlztpg-rt-0004-003").prop("checked")) {
        // 复选框被选中
    }else{
        $('.infect').hide();
    }
  }
}

function inintPage(){
  console.log('publicInfo',publicInfo);
  window.getNurseList ? toNurseList('','') : '';
  window.getCommonUseList ? toMetalList('NURSE_BODY_METAL','0012-002','metal') : '';
  window.getCommonUseList ? toMetalList('NURSE_HEALTH_EDUCATION','0016','education') : '';
  window.getCommonUseList ? toMetalList('NURSE_MENTAL','0017','psychology') : '';
  window.getCommonUseList ? toCommonUseList() : '';
  checkedHideHandler()
  initInpAndSel('hlztpg-rt-0013', relationship);
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    window.getDateTime() ? toDateTime() : '';
    window.getCommonUseList ? addBzHandler() : '';
    window.getCommonUseList ? addDictionaryHandler('','NURSE_BODY_METAL') : '';
    window.getCommonUseList ? addDictionaryHandler('','NURSE_HEALTH_EDUCATION') : '';
    window.getCommonUseList ? addDictionaryHandler('','NURSE_MENTAL') : '';
    $('#hlztpg-rt-0011-001').attr('checked',true)
    $('#hlztpg-rt-0010-001').attr('checked',true)
    $('#hlztpg-rt-0012-001').attr('checked',true)
    $('#hlztpg-rt-0008-001').attr('checked',true)
    $('#hlztpg-rt-0009-001').attr('checked',true)
    $('#hlztpg-rt-0014-001').attr('checked',true)
    $('#hlztpg-rt-0005-001').attr('checked',true)
    $('#hlztpg-rt-0004-002').attr('checked',true)
    $('.education').prop('checked', true);
    $('.psychology').prop('checked', true);
  } else {
    displayBzContent();
  }
  $('input[name="r-xz-zhenji"]').on('change', function() {
    if ($(this).is(':checked')) {
      var value = $(this).val();
      console.log('value',value)
      if(value === '需要'){
        $('.clamAdress').show();
      }else{
        $('#hlztpg-rt-0015-001').attr('checked',false)
        $('#hlztpg-rt-0015-002').attr('checked',false)
        $('#hlztpg-rt-0015-003').attr('checked',false)
        $('.clamAdress').hide();
      }
      // 向父页面发送消息
      window.parent.postMessage({
        message: 'clamNotice',
        data: value
      }, '*');
    }
  });
  $('input[name="ck-hlcd"]').on('change', function() {
    if ($(this).is(':checked')) {
      var value = $(this).val();
      console.log('value',value)
      window.localStorage.setItem('calmdownLocation',JSON.stringify(value))
    }
  });
}

function displayBzContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    // console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'hlztpg-rt-0003')
    // console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child[0].child || [];
      console.log('bzList',bzList)
      bzList.forEach((item) => {
        // console.log('item',item)
        addBzHandler(item.id)
      })
    }
    var metalData = allResData.filter(metalItem => metalItem.id === 'hlztpg-rt-0012')
    console.log('metalData',metalData);
    if(metalData && metalData.length) {
      var metalList = metalData[0].child[0].child || [];
      metalList.forEach((item) => {
        // console.log('item',item)
        addDictionaryHandler(item.id,'NURSE_BODY_METAL');
      })
    }else{
     addDictionaryHandler('','NURSE_BODY_METAL');
    }
    var educationData = allResData.filter(educationItem => educationItem.id === 'hlztpg-rt-0016')
    console.log('educationData',educationData);
    if(educationData && educationData.length) {
      var educationList = educationData[0].child || [];
      educationList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList && addDictionaryHandler(item.id,'NURSE_HEALTH_EDUCATION');
      })
    }else{
      window.getCommonUseList && addDictionaryHandler('','NURSE_HEALTH_EDUCATION');
    }
    var clamData = allResData.filter(educationItem => educationItem.id === 'hlztpg-rt-0014')
    console.log('educationData',educationData);
    if(clamData && clamData.length) {
      var educationList = clamData[0].child || [];
      educationList.forEach((item) => {
        console.log('item',item)
        if(item.val === '需要'){
          $('.clamAdress').show();
          // 向父页面发送消息
          window.parent.postMessage({
            message: 'clamNotice',
            data: item.val
          }, '*');
        }
      })
    }
    var psychologyData = allResData.filter(psychologyItem => psychologyItem.id === 'hlztpg-rt-0017')
    console.log('psychologyData',psychologyData);
    if(psychologyData && psychologyData.length) {
      var psychologyList = psychologyData[0].child || [];
      psychologyList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList && addDictionaryHandler(item.id,'NURSE_MENTAL');
      })
    }else{
      window.getCommonUseList && addDictionaryHandler('','NURSE_MENTAL');
    }
  } else {
    curElem.find('#allergy-history').hide();
  }
  // 监听表单内所有input, textarea, select的input或change事件  
  $('#hlztpg1').on('input change blur', 'input, textarea, select', function() { 
    // console.log('input change blur', this) 
    // 向父页面发送消息
    window.parent.postMessage({
      message: 'formChangeNotice',
      data: '表单内容已修改'
    }, '*');
  });  
}

function toDateTime(){
  var dateTime = window.getDateTime();
  $('#hlztpg-rt-0002').val(dateTime);
  // console.log('dateTime',dateTime)
}

function toNurseList(deptCode,userName){
  let nurseList = window.getNurseList({deptCode: deptCode});
  let userList = []
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      userList.push({ title: nurseList[t].name, id: nurseList[t].userId })
    }
  }
  initInpAndSel('hlztpg-rt-0001', userList,optHandler );
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#hlztpg-rt-0001').val(publicInfo.optName || '')
  }else{
   let val =  $('#hlztpg-rt-0001').val()
   if(val){
     let obj =  userList.find(item=>item.title === val) 
     if(obj){
       optHandler(obj)
     }
   }
  }
}
function toCommonUseList(){
  let nurseList = window.getCommonUseList({name: 'nurse_irritability'});
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="hlztpg-rt-0003-002-${padNumberWithZeros(t+1,3)}"><input type="checkbox" class="rt-sr-w" name="ck-alery" value="${nurseList[t].value}" id="hlztpg-rt-0003-002-${padNumberWithZeros(t+1,3)}" pid="hlztpg-rt-0003-002" rt-sc="pageId:hlztpg1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    $('#allergy-history').html(html);
  }
}
function toMetalList(type,id,name){
  let nurseList = window.getCommonUseList({name: type});
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label"><input type="checkbox" class="rt-sr-w ${name}" name="${name}" value="${nurseList[t].value}" id="hlztpg-rt-${id}-00${t + 1}" pid="hlztpg-rt-${id}" rt-sc="pageId:hlztpg1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    if(type === 'NURSE_BODY_METAL'){
      $('#metal-content').html(html);
    }else if(type === 'NURSE_HEALTH_EDUCATION'){
      $('#education-content').html(html);  
    }else if(type === 'NURSE_MENTAL'){
      $('#psychology-content').html(html);
    }   
  }
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, cb) {
  let dropdown = layui.dropdown;
  console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      cb && cb(obj)
      this.elem.val(obj.title);
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}

// 添加过敏史及禁忌征
function addBzHandler(oldPaneId) {
  var newPaneBlock = $('#allergy-history');
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('#allergy-history').find("[rt-sc]");
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
// 添加过字典内容
function addDictionaryHandler(oldPaneId,type) {
  var paneId = oldPaneId || createUUidFun();
  var newPaneBlock = '';
  if(type === 'NURSE_BODY_METAL'){
    newPaneBlock = $('#metal-content');
  }else if(type === 'NURSE_HEALTH_EDUCATION'){
    newPaneBlock = $('#education-content');
  }else if(type === 'NURSE_MENTAL'){
    newPaneBlock = $('#psychology-content');
  }
  console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = '';
    if(type === 'NURSE_BODY_METAL'){
      wCon = $('#metal-content').find("[rt-sc]");
    }else if(type === 'NURSE_HEALTH_EDUCATION'){
      wCon = $('#education-content').find("[rt-sc]");
    }else if(type === 'NURSE_MENTAL'){
      wCon = $('#psychology-content').find("[rt-sc]");
    }
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function checkedHideEle(pid,hideSelector,val='无'){
  let el = $(`[pid="${pid}"]:checked`)
  if(el.val()===val || !el.val()){
    $(hideSelector).hide()
  }else{
    $(hideSelector).show()
  }
  $(`[pid="${pid}"]`).on('change',function(){
    if($(this).val()===val){
      $(hideSelector).hide()
    }else{
      $(hideSelector).show()
    }
  })
}
function checkedHideHandler(){
  
  checkedHideEle('hlztpg-rt-0003','.allergy-content .item-content')
  checkedHideEle('hlztpg-rt-0012','#hlztpg-rt-0012-002-900')
  checkedHideEle('hlztpg-rt-0011','#hlztpg-rt-0011-002-900')
  checkedHideEle('hlztpg-rt-0005','#body_temperature','正常')
}

function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }