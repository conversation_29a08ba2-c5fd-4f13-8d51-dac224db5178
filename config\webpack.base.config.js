const StatsPlugin = require('stats-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const CompressionPlugin = require("compression-webpack-plugin");

const config = {
  externals: {
    './cptable': 'var cptable',
  },
  output: {
    library: "sreport",
    libraryTarget: "window",
  },
  plugins: [
    new StatsPlugin('manifest.json', {
      chunkModules: false,
      entrypoints: true,
      source: false,
      chunks: false,
      modules: false,
      assets: false,
      children: false,
      exclude: [/node_modules/]
    }),
    // new CompressionPlugin({ algorithm: "gzip" }),
    // new BundleAnalyzerPlugin({
    //   analyzerPort: 8000,
    // })
  ]
};

module.exports = config;
