const cloudpacsName = `/${configData.interfaceName}/sr`;
const pacsName = '/specialistpacs'
const srName = `/${configData.interfaceName}/sr`;
const pypacsName = '/pypacsApi';
const api = {
  filePatch: `${cloudpacsName}/v1/fileUpload/filePatch2`,
  uploadReportImgFile: `${cloudpacsName}/creatPdf`,  //上传网页图至后端合成pdf
  creatSrPdf: `${cloudpacsName}/creatSrPdf`,  //后端进入预览页生成pdf
  
  getSrDoc: `${pacsName}/examsrdoc/getSrDoc`,
  updateSrDoc: `${pacsName}/examsrdoc/update`,
  getUsersList: `${pacsName}/dict/getUsersList`,  //获取检查技师列表、已废弃
  uploadImage: `${pacsName}/upload/image`,  //上传
  downloadFile: `${pacsName}/download/file`,  //加载
  updateRptStatus: `${pacsName}/exams/updateStatus`,  //更新报告状态

  // 病理统计
  getTableHeader: `${cloudpacsName}/v1/dict/dictArAttr/getAttrAndValueList`,  //获取表头
  getTableList: `${cloudpacsName}/v2/statistics/getSrAttrStatistics`,  //获取统计列表
  
  // 结构化报告
  getContentList: `${srName}/doc/contentList`,  //查询报告结果
  saveSrResult: `${srName}/doc/save`,  //保存报告
  updateSrResult: `${srName}/doc/update`,  //更新报告
  getAudioList: `${srName}/listVoiceFileName`,  //获取结构化语音记录列表
  getAudioStream: `${srName}/getVoiceFile`,  //获取结构化语音文件流
  saveClientLog: `${srName}/saveClientLog`, // 保存日志记录

  // 上传文件
  uploadReportFileNew: `${cloudpacsName}/v1/fileUpload/filePatch2`,
  // 通过文件类型删除FTP服务器上的图像
  deleteByImageType: `${cloudpacsName}/v1/fileUpload/deleteByImageType`,
  // 图文报告多种操作接口合并
  addOrUpdateOrSubimitOrAffirmExamRpt: `${cloudpacsName}/v2/examRpt/addOrUpdateOrSubimitOrAffirmExamRpt`,
  // 获取申请单图像
  getApplyImageList: `${cloudpacsName}/v1/fileUpload/getApplyImageList`,
  getRptImg: `${cloudpacsName}/v1/exam/getRptImg`,
  // 获取历史检查
  getHistoryRpt: `${cloudpacsName}/v1/structure/getHistoryRptList`,
  // 获取录入员
  getEntryPeopleList: `${cloudpacsName}/v1/dict/dictUsers/baseList`,
  // 获取签名图片
  getSignImage: `${cloudpacsName}/v1/examReport/getSignImage`,
  // 获取报告内容
  getReportDocNew: `${cloudpacsName}/v1/examReport/getReportDocNew`,
  // 获取报告信息
  getExamInfoForSR: `${cloudpacsName}/v2/exam/examInfoForSR`,

  // 报告痕迹-获取报告历史列表接口
  // listNoContent: `${srName}/doc/listNoContent`,
  historyList: `${srName}/doc/historyList`,
  // 报告痕迹-获取报告痕迹详情内容
  contentWithTrace: `${srName}/doc/contentWithTrace`,
  
  // 登录接口，通过ticket换取token
  srLogin: `${srName}/login`,
  patternList: srName + '/dictPatternInfo/getList',  //模板列表
  patternInfo: srName + '/dictPatternInfo/getDocInfo',  //模板详情
  saveSrNodeHtml: srName + `/saveSrNodeHtml`, // 更新预览报告样式html

  getPdfFile: srName + '/getFile',  //报告pdf文件
  deviceOriginData: srName + '/doc/deviceOriginData',  //获取设备原始数据-听力报告
  creatRptPdf: pypacsName + '/examRpt/creatRptPdf',  // 生成结构化PDF文件
  updatePrintStatus: pypacsName + '/examRpt/updatePrintStatus',  // 更新报告状态
  docHistoryListWithTrace: srName + '/docHistoryListWithTrace',  // 更新报告状态
  saveExamSysLog: pypacsName + `/log/saveExamSysLog`, // 保存系统日志接口
  updateReportId: pypacsName + `/examRpt/updateReportId`, // 更新报告reportId

  sseConnet: `${srName}/sse/connect/{deviceId}`, // sse连接
  sseSendSign: `${srName}/consent/sendSign`, // 通知签署接口(推送到sse)
  dictPatternInfoListGroupByBus: `${srName}/dictPatternInfo/listGroupByBus`, // 获取当前符合业务的模板分组
  dictPatternInfoListByGroupCode: `${srName}/dictPatternInfo/listByGroupCode`, // 根据模板分组代码获取模板列表
  dictPatternInfoListPatternsByBus: `${srName}/dictPatternInfo/listPatternsByBus`, // 获取当前符合业务的所有结构化模板
  getSignOrgData: `${srName}/ca/getSignOrgData`, // 获取结构化文档CA签名原文
  saveExamDocSign: `${srName}/ca/saveExamDocSign`, // 保存CA已签署知情同意书签名数据接口
  creatSrPdfById: `${srName}/creatSrPdfById`, // 生成知情同意书pdf接口
};

export default api;