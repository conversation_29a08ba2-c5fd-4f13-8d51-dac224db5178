<template>
  <div class="report-wrap" id="report-wrap" :data-type="reportType">
    <div class="yysblReport rpt-wrap">
      <div class="rpt-header">
        <div class="main-title">
          <img width="100%" :src="`${publicPath}${configData.logoReportTypeMap[reportType]}`" alt="">
        </div>
        <div class="rpt-hospital-info" v-if="configData.departmentNameyy || configData.subTitleyy">
          <div class="depart-title" v-if="configData.departmentNameyy">{{configData.departmentNameyy}}</div>
          <div class="sub-title" v-if="configData.subTitleyy">{{configData.subTitleyy}}</div>
        </div>
      </div>
      <div class="mlr-30">
        <div class="patient-info">
          <div class="item">
            <span class="item-label">患者姓名：</span>
            <span class="item-info">{{patInfo.name}}</span>
          </div>
          <div class="item">
            <span class="item-label">性别：</span>
            <span class="item-info">{{patInfo.gender}}</span>
          </div>
          <div class="item">
            <span class="item-label">年龄：</span>
            <span class="item-info">{{patInfo.age}}</span>
          </div>
          <div class="item">
            <span class="item-label">出生日期：</span>
            <span class="item-info">{{patInfo.birthday ? patInfo.birthday.substr(0, 10) : ''}}</span>
          </div>
        </div>
        <div class="rpt-body">
          <div class="exam-info">
            <div class="exam-item">
              <span style="color:#555;">检查设备：</span><span v-if="!isEditMode">{{formInfo.device}}</span>
              <el-select 
                placeholder="请选择" 
                size="mini" 
                style="width: 200px"
                v-model="formInfo.device" 
                @change="editHandler"
                v-if="isEditMode">
                <el-option
                  v-for="(device, d) in deviceList" 
                  :key="d"
                  :label="device.name"  
                  :value="device.name"  
                >
                </el-option>
              </el-select>
            </div>
            <div class="exam-item" style="margin-left:25px">
              <span style="color:#555;">检查日期：</span>
              <span v-if="!isEditMode">{{reportInfo.examDateAndTime ? reportInfo.examDateAndTime.slice(0,10) : ''}}</span>
              <el-date-picker
                style="width: 200px;display: inline-block;"
                size="mini"
                v-model="reportInfo.examDateAndTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :picker-options="dateOption"
                @change="editHandler"
                v-if="isEditMode"
                placeholder="选择日期">
              </el-date-picker>
            </div>
          </div>
          <div class="chart-wrap" :id="'yysblChart_' + sortIndex"></div>
          <div class="rpt-info" style="margin-top:-15px">
            <div class="result-label">结果：</div>
            <div class="rpt-desc">
              <div style="white-space: pre-wrap;" v-if="!isEditMode">{{description}}</div>
              <el-input :rows="8" type="textarea" 
                v-model="description" 
                @blur="editHandler"
                v-if="isEditMode"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="rpt-footer">
          <div class="chart-table">
            <div class="chart-flag">
              <div class="wrap flag-title">
                <div class="block">右</div>
                <div class="info">图标解释</div>
                <div class="block">左</div>
              </div>
              <div class="wrap" v-for="(item,i) in flagList" :key="i">
                <div class="block">
                  <img :src="item.rightIcon" alt="">
                </div>
                <div class="info">{{item.name}}</div>
                <div class="block">
                  <img :src="item.leftIcon" alt="">
                </div>
              </div>
            </div>
          </div>
          <div class="report-box">
            <div class="rpt-reporter">
              <span class="foot-label">检查者：</span>
              <span class="foot-info">
                <span v-if="!isEditMode">{{reportInfo.examStaff}}</span>
                <el-select 
                  size="mini"
                  style="width: 160px;display: inline-block;"
                  v-model="reportInfo.examStaff"
                  filterable 
                  :filter-method="filterExamUser"
                  placeholder="请选择"
                  @change="editHandler"
                  @visible-change="blurHandler"
                  v-if="isEditMode"
                >
                  <el-option
                    v-for="(item, i) in filterExamUserList"
                    :key="i"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
              </span>
            </div>
            <div class="rpt-reporter">
              <span class="foot-label">报告医生：</span>
              <span class="foot-info">
                <span v-if="!isEditMode">{{reportInfo.reportStaff}}</span>
                <el-select 
                  size="mini"
                  style="width: 160px;display: inline-block;"
                  v-model="reportInfo.reportStaff"
                  filterable 
                  :filter-method="filterReporter"
                  placeholder="请选择"
                  @change="editHandler"
                  @visible-change="blurHandler"
                  v-if="isEditMode"
                >
                  <el-option
                    v-for="(item, i) in filterReporterList"
                    :key="i"
                    :label="item.name"
                    :value="item.name">
                  </el-option>
                </el-select>
              </span>
            </div>
            <div class="rpt-reporter">
              <span class="foot-label">报告日期：</span>
              <span class="foot-info" v-if="!isEditMode">{{reportInfo.reportDateAndTime ? reportInfo.reportDateAndTime.slice(0,10) : ''}}</span>
              <el-date-picker
                style="width: 160px;display: inline-block;"
                size="mini"
                v-model="reportInfo.reportDateAndTime"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :picker-options="dateOption"
                @change="editHandler"
                v-if="isEditMode"
                placeholder="选择日期">
              </el-date-picker>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import rightIcon1 from '@/assets/images/flag/yysbl_rpt/right_circle.png';
import rightIcon2 from '@/assets/images/flag/yysbl_rpt/right_triangle.png';
import rightIcon3 from '@/assets/images/flag/yysbl_rpt/right_s.png';
import rightIcon4 from '@/assets/images/flag/yysbl_rpt/right_shengchang.png';
import rightIcon5 from '@/assets/images/flag/yysbl_rpt/right_a.png';
import rightIcon5_2 from '@/assets/images/flag/yysbl_rpt/right_a_2.png';
import rightIcon6 from '@/assets/images/flag/yysbl_rpt/right_ar.png';
import rightIcon6_2 from '@/assets/images/flag/yysbl_rpt/right_ar_2.png';

import leftIcon1 from '@/assets/images/flag/yysbl_rpt/left_cross.png';
import leftIcon2 from '@/assets/images/flag/yysbl_rpt/left_squre.png';
import leftIcon3 from '@/assets/images/flag/yysbl_rpt/left_s.png';
import leftIcon4 from '@/assets/images/flag/yysbl_rpt/left_shengchang.png';
import leftIcon5 from '@/assets/images/flag/yysbl_rpt/left_a.png';
import leftIcon5_2 from '@/assets/images/flag/yysbl_rpt/left_a_2.png';
import leftIcon6 from '@/assets/images/flag/yysbl_rpt/left_la.png';
import leftIcon6_2 from '@/assets/images/flag/yysbl_rpt/left_la_2.png';
import api from '@/config/api.js';
import { request } from '@/utils/common.js';
import generateReportMixin from '@/mixins/generateReport.js';
import dayjs from 'dayjs';
import {inferAgeByBirthday} from 'rt-common-functions'
export default {
  name: "yysblReport",
  mixins: [generateReportMixin],
  props: {
    sortIndex: {
      type: Number,
      default: 0
    },
    reportType: {
      type: String,
      default: ''
    },
    patInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    reportInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    reportData: {
      type: Array,
      default() {
        return []
      }
    },
    reportResult: {
      type: [Object, Array],
      default() {
        return {}
      }
    },
  },
  watch: {
    reportType: {
      async handler(newVal) {
        if(this.yysblReportTypeList.includes(newVal)) {
          this.examNo = this.localQueryParams.examNo || this.localQueryParams.busId;
          this.initReport();
          if(this.patInfo.birthday && !this.patInfo.age) {
            let age = inferAgeByBirthday(this.patInfo.birthday);
            this.$set(this.patInfo, 'age', age);
          }
          let device = '';
          if(this.reportData && this.reportData.length) {
            device = this.reportData[0].device || '';
          }else if(this.deviceList && this.deviceList.length) {
            device = this.deviceList[0].name || '';
          }
          this.$set(this.formInfo, 'device',device);
          let {examDate = '', examTime = '', reportDate = '', reportTime = ''} = this.reportInfo;
          let examDateAndTime = examDate ? `${examDate} ${examTime}` : this.nowDate;
          this.$set(this.reportInfo, 'examDateAndTime', examDateAndTime);
          let reportDateAndTime = reportDate ? `${reportDate} ${reportTime}` : this.nowDate;
          this.$set(this.reportInfo, 'reportDateAndTime', reportDateAndTime);
          this.examUser = this.reportInfo.examStaff || '';
          this.reporter = this.reportInfo.reportStaff || '';
          this.editHandler && this.editHandler();
        }
      },
      immediate: true
    },
    reportData: {
      handler(newVal) {
        if(this.reportResult.description) {
          this.description = this.reportResult.description;
          return;
        }
        if(newVal && newVal.length) {
          let list = JSON.parse(JSON.stringify(newVal));
          let obj = {};
          list.forEach(item => {
            obj[item.signalOutput] = obj[item.signalOutput] ? obj[item.signalOutput] : [];
            obj[item.signalOutput].push(item);
          })
          let code = this.fztscReportTypeList.includes(this.reportType) ? '非助听言语识别率：\n' : '助听言语识别率：\n';
          let message = '';
          for(let key in obj) {
            message += key + code;
            let temp = obj[key];
            let leftEar = temp.filter(item => item.earSide === '左耳');
            let rightEar = temp.filter(item => item.earSide === '右耳');
            if(rightEar.length) {
              let curveRight = rightEar[0].curve.sort((a, b) => {
                return b.scorePct - a.scorePct;
              });
              message += `右耳 ${curveRight[0].scorePct}%(${curveRight[0].intensity}dBHL)\n` 
            }
            if(leftEar.length) {
              let curveLeft = leftEar[0].curve.sort((a, b) => {
                return b.scorePct - a.scorePct;
              });
              message += `左耳 ${curveLeft[0].scorePct}%(${curveLeft[0].intensity}dBHL)\n` 
            }
          }
          this.description = message;
        }
      },
      immediate: true,
      deep: true
    },
  },
  computed: {
    configData() {
      return this.$store.state.generateReport.configData;
    },
    deviceList() {
      return configData.deviceList;
    },
    isEditMode() {
      return this.$store.state.generateReport.isEditMode;
    },
    dateOption() {
      return this.$store.state.generateReport.dateOption;
    },
    nowDate() {
      return this.$store.state.generateReport.nowDate;
    },
  },
  data() {
    return {
      examNo: '',
      flagList: [
        {rightIcon: rightIcon1, name: '裸耳气导', leftIcon: leftIcon1},
        {rightIcon: rightIcon2, name: '裸耳气导掩蔽', leftIcon: leftIcon2},
        {rightIcon: rightIcon3, name: '裸耳声场', leftIcon: leftIcon3},
        {rightIcon: rightIcon4, name: '裸耳声场掩蔽', leftIcon: leftIcon4},
        {rightIcon: rightIcon5, name: '助听声场', leftIcon: leftIcon5},
        {rightIcon: rightIcon6, name: '助听声场掩蔽', leftIcon: leftIcon6},
      ],

      leftEar: {
        '气导': {icon: leftIcon1, coverIcon: leftIcon2},
        '声场': {icon: leftIcon3, coverIcon: leftIcon4},
        '助听声场': {icon: leftIcon5, coverIcon: leftIcon6, coverAideIcon: leftIcon6_2, aideIcon: leftIcon5_2},
      },
      // , coverAideIcon: rightIcon4, aideIcon: rightIcon6
      rightEar: {
        '气导': {icon: rightIcon1, coverIcon: rightIcon2},
        '声场': {icon: rightIcon3, coverIcon: rightIcon4},
        '助听声场': {icon: rightIcon5, coverIcon: rightIcon6, coverAideIcon: rightIcon6_2, aideIcon: rightIcon5_2},
      },
      description: '',
      publicPath: process.env.BASE_URL,
      formInfo: {
        device: '',
      },
      yysblReportTypeList: ['C003', 'C004', 'C007', 'C008', 'C011', 'C012'], // 属于纯声言语模板的id列表
      fztscReportTypeList: ['C003', 'C007', 'C011'], // 非助听声场模板id列表
      ztscReportTypeList: ['C004', 'C008', 'C012'], // 助听声场模板id列表
    }
  },
  methods: {
    // 初始化
    async initReport() {
      // this.$store.dispatch('generateReport/getExamUserList', {vm: this, params: {}})
      this.initEarChart('yysblChart_' + this.sortIndex);
    },

    // 获取数据
    getDataByEarSide() {
      let reportData = this.reportData;
      let series = reportData.map((item) => {
        let earSideCode = item.earSide === '左耳' ? 'leftEar' : 'rightEar';
        let name = item.signalOutput;
        if(name === '声场' && this.ztscReportTypeList.includes(this.reportType)) {
          name = "助听声场";
        }
        if(this[earSideCode][name]) {
          let data = [];
          let aidedNo = item.aidedNo;
          item.curve.forEach(cur => {
            let symbol = cur.occultation === '1' ? this[earSideCode][name].coverIcon : this[earSideCode][name].icon;
            if(aidedNo && aidedNo.endsWith('2')) {
              if(cur.occultation === '1' && this[earSideCode][name].coverAideIcon) {
                symbol = this[earSideCode][name].coverAideIcon;
              }
              if(cur.occultation !== '1' && this[earSideCode][name].aideIcon) {
                symbol = this[earSideCode][name].aideIcon;
              }
            }
            data.push({
              value: [Number(cur.intensity), Number(cur.scorePct)],
              symbol: `image://${symbol}`
            })
          });
          let lineColor = item.earSide === '左耳' ? '#6565FF' : '#FF1D1C';
          return {
            name,
            type: 'line',
            data,
            // symbol: `image://${this[earSideCode][item.signalOutput].icon}`,
            symbolSize: 25,
            color: this.ztscReportTypeList.includes(this.reportType) ? 'transparent' : lineColor,
          }
        }
      })
      return series;
    },
    // 图表
    initEarChart(chartId) {
      let series = this.getDataByEarSide();
      if(!series) {
        return;
      }
      // 指定图表的配置项和数据
      let option = {
        animation: false,
        grid: {
          y: 30,
          borderColor: '#000',
          borderWidth: 1,
          show: true,
          x: 30
        },
        title: {
          show: false,
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/> {c}'
        },
        xAxis: [
          {
            name: 'dB',
            nameLocation: 'middle',
            nameTextStyle: {
              padding: [-3, 0, 0, 630]
            },
            nameGap: 10,
            min: -15,
            max: 125,
            splitNumber: 10,
            axisLine: {
              show: false,
              lineStyle: {
                color: '#000'
              }
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              showMinLabel: false,
              showMaxLabel: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#ccc'
              }
            },
          },
        ],
        yAxis: [{
          name: '%',
          nameLocation: 'end',
          nameTextStyle: {
            padding: [0, 0, 0, -160]
          },
          nameGap: 0,
          min: -5,
          max: 105,
          splitNumber: 10,
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#000'
            }
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            showMinLabel: false,
            showMaxLabel: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
        }],
        series: series
      };
      this.$nextTick(() => {
        this.$store.dispatch('generateReport/createChart', {domIdName: chartId, option, vm: this})
      })
    },

    // 编辑
    editHandler() {
      this.examUser = this.reportInfo.examStaff;
      this.reporter = this.reportInfo.reportStaff;
      let now = dayjs().format('YYYY-MM-DD HH:mm:ss');
      let [nowDay, nowTime] = now.split(' ');
      let [examDate, examTime] = this.reportInfo.examDateAndTime ? this.reportInfo.examDateAndTime.split(' ') : [];
      let [reportDate, reportTime] = this.reportInfo.reportDateAndTime ? this.reportInfo.reportDateAndTime.split(' ') : [];

      let params = {
        reportStaff: this.reportInfo.reportStaff,
        description: this.description,
        device: this.formInfo.device,
        examDate: examDate,
        examTime: examDate === nowDay ? nowTime : examTime,
        reportDate: reportDate,
        reportTime: reportDate === nowDay ? nowTime : reportTime,
        examStaff: this.reportInfo.examStaff,
        reportType: this.reportType,
        rptImpression: this.description,
      }
      this.$store.commit('generateReport/setSaveParams', params);
    }
  }
}
</script>
<style>
.yysblReport .rpt-body .rpt-desc .el-textarea__inner {
  font-size: 14px;
  font-family: Microsoft YaHei;
  line-height: 24px;
}
</style>
<style scoped>
.report-wrap {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20px;
}
.yysblReport {
  width: 780px;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  min-height: 1100px;
  padding-bottom: 220px;
  box-sizing: border-box;
  color: #000;
  position: relative;
}
.yysblReport .rpt-header {
  text-align: center;
  color: #000;
  overflow: hidden;
}
.yysblReport .rpt-header .rpt-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 10px;
  vertical-align: bottom;
}
.yysblReport .rpt-header .main-title {
  margin-left: -15px;
  margin-right: -15px;
}
.yysblReport .rpt-header .hospital-name {
  font-size: 28px;
  font-weight: bold;
  color: #000;
}
.yysblReport .rpt-header .sub-title {
  font-size: 16px;
  color: #000;
}
.yysblReport .patient-info {
  display: flex;
  padding: 10px 10px 10px 0;
  justify-content: space-between;
  border-top: 1px solid #F2F2F2;
  border-bottom: 1px solid #F2F2F2;
  margin-top: 16px;
  box-sizing: border-box;
  font-size: 12px;
}
.yysblReport .patient-info .item .item-label {
  margin-right: 5px;
  color: #555;
}
.yysblReport .patient-info .item .item-info {
 color: #000;
}
.yysblReport .rpt-class-name {
  font-size: 30px;
  font-weight: bold;
  text-align: center;
  margin-top: 30px;
}
.yysblReport .exam-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-top: 10px;
}
.yysblReport .exam-item {
  flex: 1;
}
.yysblReport .rpt-body .chart-wrap {
  width: 100%;
  height: 400px;
  box-sizing: border-box;
}
.yysblReport .rpt-body .rpt-desc {
  font-size: 14px;
  color: #000;
  flex: 1;
  box-sizing: border-box;
  line-height: 24px;
  word-break: break-all;
  white-space: pre-wrap;
}
.yysblReport .rpt-footer .chart-flag {
  width: 160px;
  border: 1px solid #666;
  box-sizing: border-box;
  font-size: 14px;
}
.yysblReport .rpt-footer .chart-flag .wrap.flag-title > div {
  background: #eee;
  font-weight: bold;
}
.yysblReport .rpt-footer .chart-flag .wrap {
  height: 18px;
  line-height: 18px;
  text-align: center;
  display: flex;
  justify-content: space-between;
  color: #000;
}
.yysblReport .rpt-footer .chart-flag .wrap:not(:first-child) {
  border-top: 1px solid #666;
}
.yysblReport .rpt-footer .chart-flag .wrap .info {
  width: 100px;
  border-left: 1px solid #666;
  border-right: 1px solid #666;
  box-sizing: border-box;
}
.yysblReport .rpt-footer .chart-flag .wrap .block {
  flex: 1;
}
.yysblReport .rpt-footer .chart-flag .wrap .block img {
  width: 18px;
  vertical-align: middle;
}
.yysblReport .rpt-body .rpt-sign {
  width: 175px;
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
}
.yysblReport .rpt-body .rpt-sign .sign-label {
  font-weight: bold;
}
.yysblReport .rpt-body .rpt-sign .sign-info {
  margin-top: 10px;
  width: 100%;
  text-align: right;
}
.yysblReport .rpt-footer {
  position: absolute;
  bottom: 30px;
  right: 30px;
  left: 30px;
  font-size: 12px;
}
.yysblReport .rpt-footer .foot-label {
  color: #555;
}
.yysblReport .rpt-footer .foot-info {
  color: #000;
}
.yysblReport .rpt-footer .chart-table {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
.yysblReport .report-box {
  margin-top: 10px;
  padding-top: 6px;
  border-top: 1px solid #F2F2F2;
  display: flex;
  justify-content: space-between;
}
.yysblReport .rpt-reporter {
  flex: 1;
}
.yysblReport .rpt-reporter + .rpt-reporter {
  margin-left: 12px;
}
.result-label {
  font-size: 16px;
  color: #000;
  margin-bottom: 12px;
}
</style>