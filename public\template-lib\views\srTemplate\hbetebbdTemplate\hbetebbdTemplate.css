/* 间距 */
.ml-12 {
  margin-left: 12px;
}
.ml-56 {
  margin-left: 56px;
}
/* 宽度 */
#hbetebbd1 .full-wd {
  width: 100%;
}
#hbetebbd1 .w-8 {
  width: 15%!important;
  /* min-width: 74px; */
}
#hbetebbd1 .w-24 {
  width: 40%!important;
}
#hbetebbd1 .w-68 {
  width: 45%!important;
}
/* 边框 */
.bor-b {
  border-bottom: 1px solid #C0C4CC;
}
/* 颜色 */
.gray-bg {
  background: #F5F7FA!important;
}

#hbetebbd1 {
  width: 100%;
  min-height: 100%;
  font-family: '宋体';
  font-size: 16px;
  color: #000;
  background: #F5F7FA;
  overflow: auto;
}
#hbetebbd1 * {
  font-family: '宋体';
}
#hbetebbd1 .inp-sty {
  background: #FFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 11px;
  font-size: 16px;
  margin-bottom: 11px;
}
#hbetebbd1 .row-item {
  padding: 8px 24px;
}
#hbetebbd1 .row-lb {
  display: inline-block;
  min-width: 80px;
}
#hbetebbd1 .con-flex {
  display: flex;
}
#hbetebbd1 .flex-item {
  flex: 1;
}
#hbetebbd1 .lb-sty {
  width: 100%;
  text-align: center;
  font-weight: bold;
  margin-bottom: 4px;
}
#hbetebbd1 .ckfw-txt {
  height: 28px;
  line-height: 28px;
  text-align: center;
  margin-left: 6px;
  margin-bottom: 11px;
}
#hbetebbd1 .showInt {
  position: relative;
  background: #fff;
  height: 28px;
  width: 100%;
  margin-bottom: 11px;
}
#hbetebbd1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#hbetebbd1 .layui-input {
  height: 28px;
}
[isview="true"] #hbetebbd1 .hbetebbd-edit {
  display: none;
}
[isview="true"] #hbetebbd1 .hbetebbd-view {
  display: block;
}
/* 预览页面 */
#hbetebbd1 .hbetebbd-view {
  display: none;
  position: relative;
  margin: 0 auto;
  width: 780px;
  min-height: 1100px;
  background: #FFF;
  border: 1px solid #DCDFE6;
  padding: 37px 56px 114px 56px;
}
#hbetebbd1 .hbetebbd-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#hbetebbd1 .hbetebbd-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}
#hbetebbd1 .hbetebbd-view .hos-tit {
  font-family: 仿宋;
  font-size: 21px;
  line-height: 1;
  text-align: center;
}
#hbetebbd1 .hbetebbd-view h3 {
  margin-top: 3px;
  font-family: 仿宋;
  font-size: 21px;
  line-height: 1;
  font-weight: normal;
  text-align: center;
}
#hbetebbd1 .hbetebbd-view .sub-tit {
  margin-top: 20px;
  font-size: 26px;
  text-align: center;
}
#hbetebbd1 .hbetebbd-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
}
#hbetebbd1 .hbetebbd-view .logo-tit .code img {
  width: 122px;
  height: 36px;
}
#hbetebbd1 .hbetebbd-view .logo-tit .code span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
}
#hbetebbd1 .hbetebbd-view .blh-tit {
  font-size: 16px;
  line-height: 23px;
  text-align: right;
}
#hbetebbd1 .hbetebbd-view .patient-info {
  margin-top: 8px;
  padding: 8px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
}
#hbetebbd1 .hbetebbd-view .p-item + .p-item {
  margin-top: 4px!important;
}
#hbetebbd1 .hbetebbd-view .bgjg-h {
  font-weight: bold;
  text-align: center;
  padding: 8px;
  border-bottom: 2px solid #000;
}
#hbetebbd1 .hbetebbd-view .bgjg-b {
  text-align: center;
  padding: 16px 8px 0;
}
#hbetebbd1 .hbetebbd-view .bgjg-b .con-flex {
  line-height: 28px;
}
#hbetebbd1 .hbetebbd-view .bgjg-h > div {
  margin-left: 4px;
}
#hbetebbd1 .hbetebbd-view .bgjg-b .con-flex > div {
  white-space: pre-line;
  word-break: break-all;
  margin-left: 4px;
}
#hbetebbd1 .hbetebbd-view .jcxm-txt {
  width: 278px;
  text-align: left;
}
#hbetebbd1 .hbetebbd-view .jcjg-txt {
  width: 265px;
}
#hbetebbd1 .hbetebbd-view .ckfw-txt {
  width: 95px;
}
#hbetebbd1 .hbetebbd-view .report-wrap {
  padding: 8px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hbetebbd1 .hbetebbd-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  line-height: 32px;
}
#hbetebbd1 .hbetebbd-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#hbetebbd1 .hbetebbd-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
}
#hbetebbd1 .hbetebbd-view .rpt-footer {
  width: 666px;
  position: absolute;
  bottom: 40px;
}