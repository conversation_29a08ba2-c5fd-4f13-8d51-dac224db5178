$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }
  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
  let str = '';
  let strList = [], ejbList = [], sjbList = [], zdmbList = [];
  let ejbId = ['bmsc-rt-1', 'bmsc-rt-2'];
  let sjbId = ['bmsc-rt-3', 'bmsc-rt-4', 'bmsc-rt-5'];
  let zdmbId = ['bmsc-rt-6', 'bmsc-rt-7', 'bmsc-rt-8'];
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};

  $('#bmsczhz1 .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      if (ejbId.indexOf(curPid.id) !== -1) {
        ejbList.push(curPid.val)
      }
      if (sjbId.indexOf(curPid.id) !== -1) {
        sjbList.push(curPid.val)
      }
      if (zdmbId.indexOf(curPid.id) !== -1) {
        zdmbList.push(curPid.val)
      }
    }
  })
  ejbList && ejbList.length ? str = '二尖瓣' + ejbList.join('、') + '脱垂' : '';
  str ? strList.push(str) : '';
  str = '';
  sjbList && sjbList.length ? str = '三尖瓣' + sjbList.join('、') + '脱垂' : '';
  str ? strList.push(str) : '';
  str = '';
  zdmbList && zdmbList.length ? str = '主动脉瓣' + zdmbList.join('、') + '脱垂' : '';
  str ? strList.push(str) : '';
  str = '';
  rtStructure.impression = '瓣膜松弛综合征' + strList.join('，')
}