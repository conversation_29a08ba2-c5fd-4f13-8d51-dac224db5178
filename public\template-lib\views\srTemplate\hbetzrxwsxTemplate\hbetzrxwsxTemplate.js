$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#hbetzrxwsx1', 'edit');
  // initHtmlScript('#hbetzrxwsx1', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('hbetzrxwsx1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    examRptMemo: getExamRptMemo(),
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initPage(type) {
  if (type === 'edit') {
    previewRelative();
    previewRange();
    handleRange();
    autoCalculate();
  }
  if (type === 'view') {
    initPreview();
  }
}

function showMark($cur, $val, $range) {
  var rangeTxt = $range.val().trim();
  if (!rangeTxt) {
    return;
  }
  var mark = '';
  var curVal = parseFloat($val.val().trim());
  var rangeList = rangeTxt.split('\n');
  for (var i = 0; i < rangeList.length; i++) {
    var curRange = rangeList[i];
    var rangeSplit = curRange.split(/[:：]/);
    var rangeMinMax;
    if (rangeSplit.length >= 2) {
      var curAgeText = publicInfo.age || '';
      var curAgeNum;
      if (/\D/.test(curAgeText) && curAgeText.indexOf('岁') === -1) {
        curAgeNum = 0;
      } else {
        curAgeNum = parseFloat(curAgeText);
      }
      var rangeAgeTxt = rangeSplit[0];
      if (rangeAgeTxt.indexOf('-') !== -1) {
        var ageMinMax = rangeAgeTxt.split('-');
        var ageMin = parseFloat(ageMinMax[0].replace(/[^.\d]/g, ''));
        var ageMax = parseFloat(ageMinMax[1].replace(/[^.\d]/g, ''));
        if (curAgeNum < ageMin || curAgeNum >= ageMax) {
          continue;
        }
      } else if (rangeAgeTxt.indexOf('≥') !== -1) {
        var ageOver = parseFloat(rangeAgeTxt.replace(/[^.\d]/g, ''));
        if (curAgeNum < ageOver) {
          continue;
        }
      }
      rangeMinMax = rangeSplit[1];
    } else {
      rangeMinMax = rangeSplit[0];
    }
    if (rangeMinMax.indexOf('-') !== -1) {
      var arr = rangeMinMax.split('-');
      var rangeMin = parseFloat(arr[0].replace(/[^.\d]/g, ''));
      var rangeMax = parseFloat(arr[1].replace(/[^.\d]/g, ''));
      if (curVal < rangeMin) {
        mark = '↓';
      }
      if (curVal > rangeMax) {
        mark = '↑';
      }
    } else if (rangeMinMax.indexOf('≥') !== -1) {
      var rangeOver = parseFloat(rangeMinMax.replace(/[^.\d]/g, ''));
      if (curVal < rangeOver) {
        mark = '↓';
      }
    }
  }
  $cur.text(mark);
}

// 处理范围
function handleRange() {
  $('[data-over-mark]').each(function () {
    var $cur = $(this);
    var attr = $cur.attr('data-over-mark');
    if (!attr) {
      return;
    }
    try {
      var params = attr.split(',');
      if (params.length < 2) {
        return;
      }
      var $val = $(params[0]);
      var $range = $(params[1]);
      $val.on('input', function() {
        showMark($cur, $val, $range);
      });
      $range.on('input', function() {
        showMark($cur, $val, $range);
      });
    } catch (error) {
      console.error(error);
    }
  });
}

function calculateRelative($cur, attr, matchPatterns) {
  for (var i = 0; i < matchPatterns.length; i++) {
    var pattern = matchPatterns[i];
    var id = pattern.replace(/[{}]/g, '');
    var val = $(id).val().trim();
    attr = attr.replace(pattern, val || 0);
  }
  if (eval(attr) == 0) {
    $cur.val('');
    return;
  }
  $cur.val(eval(attr).toFixed(3));
  $cur.trigger('input');
}
// 关联计算
function autoCalculate() {
  $('[data-auto-calculate]').each(function () {
    var $cur = $(this);
    var attr = $cur.attr('data-auto-calculate');
    if (!attr) {
      return;
    }
    try {
      let matchPatterns = attr.match(/{[^}]+}/g);
      if (matchPatterns) {
        for (var i = 0; i < matchPatterns.length; i++) {
          var pattern = matchPatterns[i];
          var id = pattern.replace(/[{}]/g, '');
          $(id).on('input', function() {
            calculateRelative($cur, attr, matchPatterns);
          });
        }
      }
    } catch (error) {
      console.error(error);
    }
  });
}

function initPreview(){
  // 签名
  curElem.find('[data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
      } 
    }
  });
  // 文字值
  curElem.find('[data-key]').each(function() {
    var key = $(this).attr('data-key');
    var keyList = key.split(',');
    if (keyList.length > 1) {
      let result = [];
      keyList.forEach(item => {
        result.push(publicInfo[item]);
      });
      $(this).html(result.join(' '));
      return;
    }
    
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'sampleName' || key === 'barcodeNos') {
      value = value.replace(/,/g, ';');
    }
    $(this).html(value);
    addIdToNodeByView(this, keyList, idAndDomMap);
  });

  previewRelative();
  previewRange();
}

// 主动计算箭头
function previewRange() {
  $('[data-over-mark]').each(function () {
    var $cur = $(this);
    var attr = $cur.attr('data-over-mark');
    if (!attr) {
      return;
    }
    try {
      var params = attr.split(',');
      if (params.length < 2) {
        return;
      }
      var $val = $(params[0]);
      var $range = $(params[1]);
      showMark($cur, $val, $range);
    } catch (error) {
    }
  });
}

// 主动计算关联
function previewRelative() {
  $('[data-auto-calculate]').each(function () {
    var $cur = $(this);
    var attr = $cur.attr('data-auto-calculate');
    if (!attr) {
      return;
    }
    try {
      let matchPatterns = attr.match(/{[^}]+}/g);
      if (matchPatterns) {
        for (var i = 0; i < matchPatterns.length; i++) {
          calculateRelative($cur, attr, matchPatterns);
        }
      }
    } catch (error) {
      console.error(error);
    }
  });
}

function getImpression() {
  var arr = [];
  curElem.find('.input-body-wrap .row:not(.hd)').each(function() {
    var item = getVal($(this).find('.inp:eq(0)'));
    var val = getVal($(this).find('.inp:eq(1)'));
    var unit = getVal($(this).find('.inp:eq(3)'));
    if (item && val) {
      arr.push(item + '：' + val + unit);
    }
  });
  return arr.join('；');
}

// 获取检验备注
function getExamRptMemo() {
  return getVal('#hbetzrxw-rt-17');
}