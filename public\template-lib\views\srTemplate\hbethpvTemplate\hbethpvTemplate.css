.t-pg {
  background-color: #F5F7FA;
}
#hbethpv1 {
  background-color: #F5F7FA;
}
#hbethpv1 * {
  font-family: '宋体';
}
#hbethpv1 .view-wrap{display:none}
[isview='true'] #hbethpv1 .edit-wrap{display:none}
[isview='true'] #hbethpv1 .view-wrap{display:block}
#hbethpv1 .edit-wrap {
  font-size: 14px;
  /* width: 904px; */
  margin: 0 auto;
  background-color: #fff;
  min-height: 100%;
  padding: 12px 24px;
}
#hbethpv-rt-33, .text-con {
  opacity: 0;
}
.fw6 {
  font-weight: 600;
}
.bt {
  border-top: solid 1px #C0C4CC;
}
.xbt, .text_t_l {
  font-size: 16px;
  color: #000000;
}
.view-flex .text_t_l {
  line-height: 28px;
}
.text_t_l {
  display: inline-block;
  min-width: 80px;
  text-align: right;
}
.bn {
  border: none;
}
.ta-c{
  text-align: center;
}
.item-flex {
  display: flex;
}
.item_bd {
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  height: 36px;
  line-height: 34px;
  box-sizing: border-box;
  padding-left: 12px;
}
.box {
  background-color: #fff;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  height: 86px;
  padding: 0 8px;
}
#hbethpv1 .mb-8 {
  margin-bottom: 4px !important;
}

#hbethpv1 .mb-12 {
  margin-bottom: 8px !important;
}

.dib{
  display: inline-block;
  position: relative;
}
#hbethpv1 .dib::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 10px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#hbethpv1 .dib .layui-input {
  padding-right: 20px;
}

#hbethpv1 .flex-sty {
  overflow: hidden;
}
#hbethpv1 .lbj-item {
  float: left;
  width: 32.4%;
  min-width: 240px;
  margin-bottom: 4px;
}
#hbethpv1 .lbj-item input {
  min-width: 120px;
  width: calc(100% - 115px);
}
#hbethpv1 .myzh-table {
  width: 100%;
  border: 1px solid #DCDFE6;
  border-collapse:collapse;
  vertical-align: top;
  background: #fff;
  margin: 5px 0;
}
#hbethpv1 .tb-th th {
  height: 36px;
  padding: 7px 12px;
  text-align: left;
}
#hbethpv1 tbody .td-lab {
  display: inline-block;
  width: 90px;
}
#hbethpv1  tbody td{
  height: 40px;
  padding: 4px 12px;
}

.view-wrap {
  position: relative;
  width: 780px;
  min-height: 1100px;
  margin: 0 auto;
  background: #FFFFFF;
  /* border: 1px solid #C0C4CC; */
  padding: 30px 56px 138px 56px;
  box-sizing: border-box;
}
#hbethpv1 .view-head {
  /* border-bottom: 1px solid #999; */
  padding-bottom: 9px;
  text-align: center;
}
#hbethpv1 .view-head .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  position: relative;
}
#hbethpv1 .view-head .logo-tit .code {
  position: absolute;
  right: 0;
  top: 27px;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
 span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
 }
}
#hbethpv1 .view-head .blh-tit {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #000;
  font-size: 16px;
}
.blh-tit span {
  color: #000 !important;
}
#hbethpv1 .view-head .hos-tit{
  font-size: 21px;
  /* font-weight: bold; */
  color: #000;
  font-family: "仿宋";
}
#hbethpv1 .view-head .sub-tit{
  font-size: 26px;
  color: #000;
  margin-top: 20px;
}
.view-wrap .title {
  font-size: 24px;
  font-weight: 600;
  color: #000000;
  text-align: center;
}
.v_hzxx {
  padding: 8px 0 0 0;
  border-top: solid 1px #999999;
  border-bottom: solid 1px #999999;
  font-size: 16px;
}
.tr{
  text-align: right;
}
.tc {
  text-align: center;
}

.rt_zt_l {
  color: #000;
  font-size: 16px;
  white-space: pre-line;
  word-break: break-all; 
}
.rt_zt_r {
  font-size: 16px; 
  color: #000000;
}
.rt-sr-header {
  margin-bottom: 5px;
}
.bb{
  border-bottom: solid 1px #999999;
  margin-bottom: 5px;
}
.btt{
  border-top: solid 1px #999999;
}
#hbethpv1 .btt > div{
  display: flex;
  flex: 1;
  flex-wrap: wrap;
}
#hbethpv1 .btt > div + div {
  margin-left: 4px;
}
#hbethpv1 .btt  > div .rt_zt_l {
  width: 70px;
}
#hbethpv1 .btt  > div:nth-child(3) .rt_zt_l {
  width: 42px;
}
#hbethpv1 .btt  > div .rt_zt_r {
  flex: 1;
}
.ib-p {
  display: inline-block;
}
.ib-p .rt_zt_r {
  margin-right: 10px;
}
.view-flex{
  display: flex;
} 
.flex_1 {
  flex: 1;
}
.flex_1-5 {
  flex: 1.5;
}
.flex_2 {
  flex: 2;
}
.flex_2-5 {
  flex: 2.5;
}
.flex_3 {
  flex: 3;
}
.flex_3-5 {
  flex: 3.5;
}
.flex_4 {
  flex: 4;
}
.flex_4-5 {
  flex: 4.5;
}
.flex_5 {
  flex: 5;
}
.flex_5-5 {
  flex: 5.5;
}
.flex_6 {
  flex: 6;
}
.mb-10 {
  margin-bottom: 8px !important;
}
.mb-20 {
  margin-bottom: 20px !important;
}
.border_Bold {
  font-weight: 600;
}
.view_img_box {
  display: none;
  overflow: hidden;
}
.view_img {
  float: left;
  width: 50%;
  margin-bottom: 8px;
}
.view_img img {
  display: block;
  margin: 0 auto;
  width: 265px;
  height: 200px;
}
.clac86 {
  width: calc(100% - 86px);
}
.item-fl {
  overflow: hidden;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.item-flex-1 {
  float: left;
  min-width: 400px;
  margin-bottom: 4px;
}
.item-flex-2 {
  float: left;
  width: 50%;
  min-width: 400px;
  margin-bottom: 4px;
}
.item-fl .item-flex-3{
  float: left;
  width: 33.333%;
  min-width: 400px;
  margin-bottom: 4px;
}
.view-wrap .flex_2-5 img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
.item {
  padding-bottom: 4px;
  /* padding-top: 5px; */
}
.item-title {
  color: #000;
  font-size: 16px;
  /* display: inline-block; */
  /* padding-bottom: 20px; */
}
.itme_l {
  margin-bottom: 1px;
}
.itme_l p {
  white-space: pre-line;
  word-break: break-all;
  color: #000;
  font-size: 16px;
}

/* 弹框字体 */
.diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
.diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
.diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
.diag-text-size .text-size .on {
  display: none;
}
#hbethpv1 input {
  border: solid 1px #C0C4CC;
  font-size: 16px;
  padding-left: 12px;
  box-sizing: border-box;
}
#hbethpv1 .checkbox-label input {
  border: none;
}
#hbethpv1 .textarea {
  resize: vertical;
  padding: 9px 0 0 11px !important;
  outline: none !important;
  border: solid 1px #C0C4CC;
  font-size: 16px;
}
#hbethpv1 .textarea:focus {
  outline: none !important;
}
.rt-dialog-con .edit-wrap.default textarea{
  font-size: 16px;
}
.rt-dialog-con .edit-wrap.large textarea{
  font-size: 18px;
}
.rt-dialog-con .edit-wrap.larger textarea{
  font-size: 20px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}

.rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
.report-wrap {
  min-height: 48px;
  line-height: 40px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hbethpv1 .view-wrap .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#hbethpv1 .view-wrap.reporter-i.w120 {
  width: 120px;
}
#hbethpv1 .view-wrap .reporter-i.w150 {
  width: 150px;
}
#hbethpv1 .view-wrap .reporter-i span {
  width: 70px;
}
#hbethpv1 .view-wrap .reporter-i span:last-child {
  flex: 1;
}
#hbethpv1 .view-wrap .reporter-i img {
  width: 70px;
  height: 40px;
  object-fit: contain;
}
#hbethpv1 .view-wrap .reporter-i + .reporter-i {
  margin-left: 8px;
}
#hbethpv1 .view-wrap .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 12px;
  line-height: 16px;
}
#hbethpv1 .view-wrap .tip-wrap .tip-text {
  flex: 1;
  color: red;
}

#hbethpv1 .checkbox-label {
  overflow: hidden;
  padding: 12px 0 0 8px;
  flex: 1;
  height: 56px;
  border-right: solid 1px #C0C4CC;
  color: #000;
  /* font-size: #000; */
}

#hbethpv1 .rt-sr-footer  img {
  width: 64px;
  height: 32px;
  margin-top: 4px;
  margin-left: 10px;
  object-fit: cover;
}

.labelBox {
  border-left: solid 1px #C0C4CC;
  border-bottom: solid 1px #C0C4CC;
}
.checkbox-label .text {
  font-size: 20px;
  font-weight: 600;
}
/* 隐藏原生复选框 */
input[type="checkbox"] {
  /* display: none; */
  width: 0;
  opacity: 0;
  visibility: hidden;
}

.bui-radios {
  display: inline-block;
  width: 0;
  height: 0;
}
/* 当复选框被选中时，label显示为带有勾选标记的样式 */
input[type="checkbox"]:checked + .bui-radios:after {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: #000;
  border-radius: 50%;
}
.lineBreak {
  white-space: pre-line;
  word-break: break-all;
  font-size: 16px !important;
}
.onCheckbox .text::after {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: #000;
  border-radius: 50%;
  margin-left: 4px;
}