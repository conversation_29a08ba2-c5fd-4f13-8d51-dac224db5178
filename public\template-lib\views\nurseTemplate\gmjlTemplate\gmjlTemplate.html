<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/nurseTemplate/gmjlTemplate/gmjlTemplate.css">
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/nurseTemplate/gmjlTemplate/gmjlTemplate.js"></script>

<ul class="t-pg" style="height: 100%;overflow-y: auto;overflow-x: hidden;">
  <li class="page" id="gmjl1">
    <div class="ct-content">
      <div class="ct-title">对比剂信息</div>
      <div class="allergy-content sign-content" style="padding-bottom: 20px;">
        <div class="weight">
          <div class="form-title">名称：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input class="layui-input rt-sr-w" placeholder="请输入" readonly="" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-1" rt-sc="pageId:gmjl1;name:名称;wt:1;desc:名称;vt:;pvf:;">
            </div>
          </div>
        </div>
        <div class="weight">
          <div class="form-title">批号：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-2" placeholder="批号" style="display: inline-block;" rt-sc="pageId:gmjl1;name:批号;wt:1;desc:批号;vt:;pvf:;">
            </div>
          </div>
        </div>
        <div class="weight">
          <div class="form-title">有效期：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-3" placeholder="有效期" style="display: inline-block;" rt-sc="pageId:gmjl1;name:有效期;wt:1;desc:有效期;vt:;pvf:;">
            </div>
          </div>
        </div>
      </div>
      <div class="allergy-content sign-content" style="padding-bottom: 20px;">
        <div class="weight">
          <div class="form-title">速度：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-4" rt-sc="pageId:gmjl1;name:速度;wt:1;desc:速度;vt:;pvf:;">
            </div>
          </div>
        </div>
        <div class="weight">
          <div class="form-title">总量：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-5" placeholder="总量" style="display: inline-block;" rt-sc="pageId:gmjl1;name:总量;wt:1;desc:总量;vt:;pvf:;">
            </div>
          </div>
        </div>
      </div>
      <div class="allergy-content sign-content" style="padding-bottom: 20px;">
        <div class="weight">
          <div class="form-title">分管技师：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-6" rt-sc="pageId:gmjl1;name:分管技师;wt:1;desc:分管技师;vt:;pvf:;">
            </div>
          </div>
        </div>
        <div class="weight">
          <div class="form-title">分管护士：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-7" placeholder="分管护士" style="display: inline-block;" rt-sc="pageId:gmjl1;name:分管护士;wt:1;desc:分管护士;vt:;pvf:;">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ct-title">饮水情况</div>
    <div class="allergy-content">
      <div class="hydration-item layui-form sign-content">
        <div class="item-left">
          <div style="margin-left: 16px;margin-right: 5px;">饮用类型：</div>
          <div class="layui-input-block" style="margin-left: 0;min-height: 18px;">
            <select name="city" class="rt-sr-w" id="gmjl-rt-8" lay-verify="required" rt-sc="pageId:gmjl1;name:饮用类型1;wt:2;desc:饮用类型1;vt:;pvf:;">
              <option value=""></option>
              <option value="温开水">温开水</option>
              <option value="甘露醇">甘露醇</option>
            </select>
          </div>
          <div style="margin-left: 16px;margin-right: 5px;">饮用：</div>
          <div class="temperature"><input type="text" id="gmjl-rt-9" class="rt-sr-w layui-input" rt-sc="pageId:gmjl1;name:饮用毫升1;wt:1;desc:饮用毫升1;vt:;pvf:;"><span data-type="preview">ml</span><div data-type="edit" class="unit">ml</div></div>
          <div style="margin-left: 16px;margin-right: 5px;">饮用时间：</div>
          <div class="temperature"><input type="text" id="gmjl-rt-10" class="rt-sr-w layui-input" rt-sc="pageId:gmjl1;name:饮用时间1;wt:1;desc:饮用时间1;vt:;pvf:;"></div>
        </div>
      </div>
      <div class="hydration-item layui-form sign-content">
        <div class="item-left">
          <div style="margin-left: 16px;margin-right: 5px;">饮用类型：</div>
          <div class="layui-input-block" style="margin-left: 0;min-height: 18px;">
            <select name="city" class="rt-sr-w" id="gmjl-rt-11" lay-verify="required" rt-sc="pageId:gmjl1;name:饮用类型2;wt:2;desc:饮用类型2;vt:;pvf:;">
              <option value=""></option>
              <option value="温开水">温开水</option>
              <option value="甘露醇">甘露醇</option>
            </select>
          </div>
          <div style="margin-left: 16px;margin-right: 5px;">饮用：</div>
          <div class="temperature"><input type="text" id="gmjl-rt-12" class="rt-sr-w layui-input" rt-sc="pageId:gmjl1;name:饮用毫升2;wt:1;desc:饮用毫升2;vt:;pvf:;"><span data-type="preview">ml</span><div data-type="edit" class="unit">ml</div></div>
          <div style="margin-left: 16px;margin-right: 5px;">饮用时间：</div>
          <div class="temperature"><input type="text" id="gmjl-rt-13" class="rt-sr-w layui-input" rt-sc="pageId:gmjl1;name:饮用时间2;wt:1;desc:饮用时间2;vt:;pvf:;"></div>
        </div>
      </div>
    </div>
    <div class="ct-title mt-20 rt-sr-w" id="gmjl-rt-001" rt-sc="pageId:gmjl1;name:过敏症状;wt:;desc:过敏症状;vt:;pvf:1;">过敏症状</div>
    <div class="ct-title mt-20 rt-sr-w" style="display: none;" id="gmjl-rt-002" rt-sc="pageId:gmjl1;name:过敏程度;wt:;desc:过敏程度;vt:;pvf:1;">过敏症状</div>
    <div class="gray-item" style="margin-left: 16px;">
      <label class="radio-label" for="gmjl-rt-14">
        <input type="radio" class="rt-sr-w def-ck" name="ck-gmzz" value="轻度" id="gmjl-rt-14" pid="gmjl-rt-002" rt-sc="pageId:gmjl1;name:轻度;wt:5;desc:轻度;vt:;pvf:;code:3510;">
        <span class="rt-sr-lb">轻度</span>
      </label>
      <label class="radio-label" for="gmjl-rt-15">
        <input type="radio" class="rt-sr-w" name="ck-gmzz" value="中度" id="gmjl-rt-15" pid="gmjl-rt-002" rt-sc="pageId:gmjl1;name:中度;wt:5;desc:中度;vt:;pvf:;code:3510;">
        <span class="rt-sr-lb">中度</span>
      </label>
      <label class="radio-label" for="gmjl-rt-16">
        <input type="radio" class="rt-sr-w" name="ck-gmzz" value="重度" id="gmjl-rt-16" pid="gmjl-rt-002" rt-sc="pageId:gmjl1;name:重度;wt:5;desc:重度;vt:;pvf:;code:3510;">
        <span class="rt-sr-lb">重度</span>
      </label>
    </div>
    <div class="gray-item" style="margin-left: 16px;">
      <label class="radio-label" for="gmjl-rt-17">
        <input type="checkbox" class="rt-sr-w def-ck" name="ck-mb" value="眼睑水肿" id="gmjl-rt-17" pid="gmjl-rt-001" rt-sc="pageId:gmjl1;name:眼睑水肿;wt:4;desc:眼睑水肿;vt:;pvf:;code:3511;">
        <span class="rt-sr-lb">眼睑水肿</span>
      </label>
      <label class="radio-label" for="gmjl-rt-18">
        <input type="checkbox" class="rt-sr-w" name="ck-mb" value="呼吸困难" id="gmjl-rt-18" pid="gmjl-rt-001" rt-sc="pageId:gmjl1;name:呼吸困难;wt:4;desc:呼吸困难;vt:;pvf:;code:3511;">
        <span class="rt-sr-lb">呼吸困难</span>
      </label>
    </div>
    <div class="layui-inline" style="margin-left: 16px;">
      <input type="text" class="rt-sr-w layui-input" id="gmjl-rt-19" pid="gmjl-rt-001" placeholder="请输入" rt-sc="pageId:gmjl1;name:过敏症状其他;wt:1;desc:过敏症状其他;vt:;pvf:;code:3511;">
    </div>
    <div class="ct-title mt-20">处理方法</div>
    <div class="allergy-content" style="padding: 6px 16px;">
      <div class="weight" style="margin-bottom: 10px;">
        <div>检查完成：</div>
        <div class="temperature">
          <div class="layui-inline showInt">
            <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-20" placeholder="完成时间" readonly="" style="display: inline-block; width: 240px;" rt-sc="pageId:gmjl1;name:完成时间;wt:1;desc:完成时间;vt:;pvf:;">
          </div>
        </div>
      </div>
      <div class="physical-content">
      <div class="form-title">患者体征：</div>
      <div class="form-content">
        <div class="allergy-content sign-content" style="padding-bottom: 20px;">
          <div class="weight">
            <div class="form-title" style="min-width: 96px;">体重：</div>
            <div class="temperature">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-21" rt-sc="pageId:gmjl1;name:体重;wt:1;desc:体重;vt:;pvf:;"><span data-type="preview">kg</span><div data-type="edit" class="unit">kg</div>
            </div>
          </div>
          <div class="weight">
            <div>体温：</div>
            <div class="temperature">
              <label class="radio-label" for="gmjl-rt-22">
                <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-tw" value="正常" data-value="0" id="gmjl-rt-22" rt-sc="pageId:gmjl1;name:正常;wt:5;desc:正常;vt:;pvf:;">
                <span class="rt-sr-lb">正常</span>
              </label>
              <label class="radio-label" for="gmjl-rt-23">
                <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-tw" value="异常" data-value="1" id="gmjl-rt-23" rt-sc="pageId:gmjl1;name:异常;wt:5;desc:异常;vt:;pvf:;">
                <span class="rt-sr-lb">异常</span>
              </label>
              <input type="text" class="rt-sr-w layui-input" id="gmjl-rt-24" pid="gmjl-rt-23" rt-sc="pageId:gmjl1;name:体温;wt:1;desc:体温;vt:;pvf:;"><span data-type="preview">℃</span><div data-type="edit" class="unit">℃</div>
            </div>
          </div>
        </div>
        <div class="allergy-content sign-content" style="padding-bottom: 20px;">
          <div class="weight">
            <div class="form-title" style="min-width: 96px;">血压：</div>
            <div class="temperature">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;width: 95px;" id="gmjl-rt-25" rt-sc="pageId:gmjl1;name:血压;wt:1;desc:血压;vt:;pvf:;">/<input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;width: 87px;" id="gmjl-rt-26" rt-sc="pageId:gmjl1;name:血压;wt:1;desc:血压;vt:;pvf:;"><span data-type="preview">mmHg</span><div data-type="edit" class="unit">mmHg</div>
            </div>
          </div>
          <div class="weight">
            <div style="min-width: 50px;">心率：</div>
            <div class="temperature">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-27" rt-sc="pageId:gmjl1;name:心率;wt:1;desc:心率;vt:;pvf:;"><span data-type="preview">次/分</span><div data-type="edit" class="unit">次/分</div>
            </div>
          </div>
          <div class="weight">
            <div style="min-width: 50px;">呼吸：</div>
            <div class="temperature">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-28" rt-sc="pageId:gmjl1;name:呼吸;wt:1;desc:呼吸;vt:;pvf:;"><span data-type="preview">次/分</span><div data-type="edit" class="unit">次/分</div>
            </div>
          </div>
        </div>
        <div class="allergy-content sign-content" style="padding-bottom: 8px;">
          <div class="weight">
            <div class="form-title" style="min-width: 96px;">血氧饱和度：</div>
            <div class="temperature">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;" id="gmjl-rt-29" rt-sc="pageId:gmjl1;name:血氧饱和度;wt:1;desc:血氧饱和度;vt:;pvf:;"><span data-type="preview">%</span><div data-type="edit" class="unit">%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="physical-content">
      <div class="form-title">患者情况：</div>
      <div class="form-content">
      <div class="weight" style="margin-left: 10px;">
        <div>记录时间：</div>
        <div class="temperature">
          <div class="layui-inline showInt">
            <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-30" placeholder="记录时间" style="display: inline-block; width: 240px;" rt-sc="pageId:gmjl1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:;">
          </div>
        </div>
      </div>
      <div class="weight" style="margin-left: 10px;margin-top: 12px;">
        <div>描述部位：</div>
        <div class="gray-item" style="margin-left: 0px;">
          <label class="radio-label" for="gmjl-rt-31">
            <input type="checkbox" class="rt-sr-w" name="ck-msbw" value="头部" id="gmjl-rt-31" rt-sc="pageId:gmjl1;name:头部;wt:4;desc:头部;vt:;pvf:;">
            <span class="rt-sr-lb">头部</span>
          </label>
          <label class="radio-label" for="gmjl-rt-32">
            <input type="checkbox" class="rt-sr-w" name="ck-msbw" value="胸部" id="gmjl-rt-32" rt-sc="pageId:gmjl1;name:胸部;wt:4;desc:胸部;vt:;pvf:;">
            <span class="rt-sr-lb">胸部</span>
          </label>
          <label class="radio-label" for="gmjl-rt-33">
            <input type="checkbox" class="rt-sr-w" name="ck-msbw" value="手部" id="gmjl-rt-33" rt-sc="pageId:gmjl1;name:手部;wt:4;desc:手部;vt:;pvf:;">
            <span class="rt-sr-lb">手部</span>
          </label>
          <label class="radio-label" for="gmjl-rt-34">
            <input type="checkbox" class="rt-sr-w" name="ck-msbw" value="腹部" id="gmjl-rt-34" rt-sc="pageId:gmjl1;name:腹部;wt:4;desc:腹部;vt:;pvf:;">
            <span class="rt-sr-lb">腹部</span>
          </label>
        </div>
      </div>
      <div class="weight" style="margin-left: 10px;margin-top: 12px;align-items: start;">
        <div>具体表现：</div>
        <div class="allergy-content" style="margin-left: 0px;">
          <div class="gray-item" style="margin-left: 0px;">
            <label class="radio-label" for="gmjl-rt-35">
              <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-jtbx" value="自诉无其他不适" data-value="0" id="gmjl-rt-35" rt-sc="pageId:gmjl1;name:自诉无其他不适;wt:5;desc:自诉无其他不适;vt:;pvf:;">
              <span class="rt-sr-lb">自诉无其他不适</span>
            </label>
            <label class="radio-label" for="gmjl-rt-36">
              <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-jtbx" value="有不适" data-value="1" id="gmjl-rt-36" rt-sc="pageId:gmjl1;name:有不适;wt:5;desc:有不适;vt:;pvf:;">
              <span class="rt-sr-lb">有不适</span>
            </label>
          </div>
          <div class="thirth-content">
            <div class="gray-item">
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-37" pid="gmjl-rt-36" placeholder="请输入" style="display: inline-block; width: 160px;margin-right: 8px;" rt-sc="pageId:gmjl1;name:瘙痒，查体可见;wt:1;desc:瘙痒，查体可见;vt:;pvf:;">
              <div>瘙痒，查体可见</div>
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-38" pid="gmjl-rt-36" placeholder="请输入" style="display: inline-block; width: 160px;margin-right: 8px;margin-left: 8px;" rt-sc="pageId:gmjl1;name:皮疹;wt:1;desc:皮疹;vt:;pvf:;">
              <div>皮疹。</div>
            </div>
            <div class="layui-inline" style="margin-left: 12px;margin-top: 6px;">
              <input type="text" class="rt-sr-w layui-input" id="gmjl-rt-39" pid="gmjl-rt-36" placeholder="请输入其他具体表现" rt-sc="pageId:gmjl1;name:具体表现其他;wt:1;desc:具体表现其他;vt:;pvf:;">
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
      <div class="physical-content">
        <div class="form-title" data-type="edit">处理模板：</div>
        <div class="form-title" data-type="preview">处理过程：</div>
        <div class="gray-item" style="margin-left: 0;">
          <div class="template-content" style="flex: 1;">
            <div class="template-title" data-type="edit">处理过程</div>
            <textarea class="rt-sr-w layui-textarea" id="gmjl-rt-40" placeholder="请输入" style="height: 233px;" rt-sc="pageId:gmjl1;name:处理过程;wt:1;desc:处理过程;vt:;pvf:;code:3512;"></textarea>
          </div>
          <div class="template-content" data-type="edit">
            <div class="template-title">选择模板</div>
            <div class="template-box">
              <div class="template-item">患者在家属陪同下步行来报到，询问过敏..</div>
            </div>
          </div>
        </div>
      </div>
      <div class="physical-content">
      <div class="form-title">其他：</div>
      <div class="layui-inline" style="margin-bottom: 12px;">
        <input type="text" class="rt-sr-w layui-input" id="gmjl-rt-41" placeholder="请输入内容" rt-sc="pageId:gmjl1;name:过敏记录其他;wt:1;desc:过敏记录其他;vt:;pvf:;">
      </div>
    </div>
    </div>
    <div class="ct-title mt-20">记录信息</div>
      <div class="allergy-content sign-content" style="padding-bottom: 20px;padding-left: 6px;">
        <div class="weight">
          <div>记录人：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input class="layui-input rt-sr-w" placeholder="请输入" autocomplete="off" style="margin-left: 0;width: 190px;" id="gmjl-rt-42" rt-sc="pageId:gmjl1;name:记录人;wt:1;desc:记录人;vt:;pvf:;">
            </div>
          </div>
        </div>
        <div class="weight">
          <div>记录时间：</div>
          <div class="temperature">
            <div>
              <input type="text" class="layui-input rt-sr-w" id="gmjl-rt-43" placeholder="记录时间" style="display: inline-block; width: 180px;" rt-sc="pageId:gmjl1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:;">
            </div>
          </div>
        </div>
        <input class="rt-sr-w" id="optInfo" style="display: none;" rt-sc="pageId:gmjl1;name:记录人;wt:1;desc:记录人;vt:;pvf:1;">
      </div>
  </li>
</ul>