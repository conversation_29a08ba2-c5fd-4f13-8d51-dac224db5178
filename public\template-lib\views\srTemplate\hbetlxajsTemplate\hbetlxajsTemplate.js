$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var idSate = {
  '2': {
    first: [48, 175],
    second: [51, 196],
    third: [68, 183]
  },
  '4': {
    first: [31, 105],
    second: [30, 111],
    third: [36, 107]
  },
  '6': {
    first: [83, 300],
    second: [106, 320],
    third: [136, 309]
  },
  '8': {
    first: [28, 80],
    second: [30, 95],
    third: [35, 80]
  },
  '10': {
    first: [17, 75],
    second: [23, 80],
    third: [29, 77]
  },
  '12': {
    first: [26, 115],
    second: [31, 106],
    third: [31, 90]
  }
}
var ageNum = '', ageUnit = '', ageYear = '';
const regex2 = /\d+/g;
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#lxajs1 .view-container'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView()
    } else {
      initPage()
    }
  }
}
function initView() {
  let showYc = false;
  let { age = '' } = publicInfo;
  const numbers = age.match(regex2);
  if (numbers && numbers.length > 0) {
    ageNum = Number(numbers[0]);
    ageUnit = age.slice(-1);
    ageYear = convertAgeToYears(ageNum, ageUnit)
  }
  curElem.find('.ajs-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
  curElem.find('.ajs-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      $(this).html(value);
      let idNum = key.match(regex2) || [];
      if (idNum && idNum.length > 0 && idNum[0] !== '14' && ageYear) {
        let list = [];
        let imgName = ''
        ageYear < 2 ? list = idSate[idNum[0]].first : '';
        ageYear >= 2 && ageYear <= 17 ? list = idSate[idNum[0]].second : '';
        ageYear >= 18 ? list = idSate[idNum[0]].third : '';
        if (list.length > 0) {
          Number(value) < list[0] ? imgName = 'down' : ''
          Number(value) > list[1] ? imgName = 'up' : ''
          if(imgName){
            $(this).siblings('span').html(`<img src="./template-lib/assets/images/icons/${imgName}-arrow.png">`)
            showYc = true;
          }
        }
      }
      addIdToNodeByView(this, key, idAndDomMap);
    } else {
      $(this).text('')
    }
  })
  if(!showYc){
    $('.view-second').hide()
    $('.view-third').hide()
  }
}
function initPage() {
  let { age = '' } = publicInfo;
  const numbers = age.match(regex2);
  if (numbers) {
    ageNum = Number(numbers[0]);
    ageUnit = age.slice(-1);
    ageYear = convertAgeToYears(ageNum, ageUnit)
  }

  $('input[type=text]').on('change', function (e) {
    const regex1 = /^\d+(\.\d+)?$/; // 匹配正整数或小数
    let { target = {} } = e;
    let { id = '' } = target;
    let value = $('#' + id).val()
    if (!regex1.test(value)) {
      $('#' + id).val('')
      $('#' + id).siblings('span').html('')
      return;
    }
    let idNum = id.match(regex2);
    if (ageYear) {
      let list = [];
      let imgName = ''
      ageYear < 2 ? list = idSate[idNum[0]].first : '';
      ageYear >= 2 && ageYear <= 17 ? list = idSate[idNum[0]].second : '';
      ageYear >= 18 ? list = idSate[idNum[0]].third : '';
      if (list.length > 0) {
        Number(value) < list[0] ? imgName = 'down' : ''
        Number(value) > list[1] ? imgName = 'up' : ''
        imgName ? $('#' + id).siblings('span').html(`<img src="./template-lib/assets/images/icons/${imgName}-arrow.png">`) : $('#' + id).siblings('span').html('')
      }
    }
  });

  showArrow();
}
// 进入编辑回显箭头
function showArrow() {
  curElem.find('input[type=text]').each(function () {
    const regex1 = /^\d+(\.\d+)?$/; // 匹配正整数或小数
    let { id = '' } = this;
    let value = $(this).val();
    if (!regex1.test(value)) {
      $(this).val('');
      $(this).siblings('span').html('');
      return;
    }
    let idNum = id.match(regex2);
    if (ageYear) {
      let list = [];
      let imgName = ''
      ageYear < 2 ? list = idSate[idNum[0]].first : '';
      ageYear >= 2 && ageYear <= 17 ? list = idSate[idNum[0]].second : '';
      ageYear >= 18 ? list = idSate[idNum[0]].third : '';
      if (list.length > 0) {
        Number(value) < list[0] ? imgName = 'down' : ''
        Number(value) > list[1] ? imgName = 'up' : ''
        imgName ? $(this).siblings('span').html(`<img src="./template-lib/assets/images/icons/${imgName}-arrow.png">`) : $(this).siblings('span').html('');
      }
    }
  });
}
// 处理年龄单位换算
function convertAgeToYears(num, unit) {
  const daysInYear = 365;  // 简化处理，不考虑闰年
  const weeksInYear = 52.14;  // 每年大致有 52 周和 1-2 天的额外时间
  const monthsInYear = 12;  // 平均每年 12 个月

  let years;
  switch (unit) {
    case '天':
      years = num / daysInYear;
      break;
    case '周':
      years = num / weeksInYear;
      break;
    case '月':
      years = num / monthsInYear;
      break;
    default:
      years = Number(num);
  }

  // 保留两位小数
  return years.toFixed(2);
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let strList = []
  $('.ajs-edit .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid && pid !== 'lxajs-rt-13') {
      let child = curPid.child;
      if (child && child.length > 0) {
        strList.push(curPid.val + '：' + child[0].val)
      }
    }
  })
  strList && strList.length > 0 ? rtStructure.impression = strList.join('；\n') : ''
}