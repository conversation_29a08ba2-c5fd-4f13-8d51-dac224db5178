$(function () {
  window.initHtmlScript = initHtmlScript;
})

var organNamesArr = [];
// id列表
var organIdList = [], sortIdList = [], nmIdList = [], mxyzIdList = [], zxlIdList = [], jrIdList = [], jgIdList = [], jgSubIdList = [], spIdList = [], hsIdList = [], xrIdList = [], hdxyIdList = [], ryzIdList = [], siteIdList = [], numIdList = [], maxDiamIdList = [], ksrsIdList = [], yxzsIdList = [], cmvIdList = [], emrIdList = [];
// 下拉列表
var organOptList = [
  { title: '', id: '0' },
  { title: '回肠末端', id: '1' },
  { title: '盲肠', id: '2' },
  { title: '升结肠', id: '3' },
  { title: '横结肠', id: '4' },
  { title: '降结肠', id: '5' },
  { title: '乙状结肠', id: '6' },
  { title: '直肠', id: '7' },
];
var sortOptList = [
  { title: '', id: '0' },
  { title: 1, id: '1' },
  { title: 2, id: '2' },
  { title: 3, id: '3' },
  { title: 4, id: '4' },
  { title: 5, id: '5' },
  { title: 6, id: '6' },
  { title: 7, id: '7' },
];
var nmOptList = [
  { title: '', id: '0' },
  { title: '正常', id: '1' },
  { title: '肠炎', id: '2' },
  { title: '慢性肠炎', id: '3' },
];
var stateOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '轻度增多', id: '2' },
  { title: '中度增多', id: '3' },
  { title: '重度增多', id: '4' },
];
var jrOptList = [
  { title: '', id: '0' },
  { title: '局灶性', id: '1' },
  { title: '弥漫性', id: '2' },
];
var jgOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '局灶性', id: '2' },
]
var jgSubOptList = [
  { title: '', id: '0' },
  { title: '弥漫性', id: '1' },
  { title: '隐窝分支', id: '2' },
  { title: '隐窝加长', id: '3' },
  { title: '隐窝缺失', id: '4' },
  { title: '隐窝缩短', id: '5' },
  { title: '基底淋巴浆细胞增多', id: '6' },
  { title: '结肠表面绒毛化', id: '7' },
  { title: '小肠绒毛变短、变平', id: '8' },
];
var spOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '局灶性', id: '2' },
  { title: '广泛性', id: '3' },
];
var hsOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '幽门腺化生', id: '2' },
  { title: '潘氏细胞化生(脾曲以后)', id: '3' },
];
var xrOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '炎性息肉', id: '2' },
];
var hdxyOptList = [
  { title: '', id: '0' },
  { title: '非活动性', id: '1' },
  { title: '表面上皮炎', id: '2' },
  { title: '散在隐窝炎', id: '3' },
  { title: '明显隐窝炎', id: '4' },
  { title: '隐窝脓肿', id: '5' },
  { title: '糜烂或溃疡', id: '6' },
];
var ryzOptList = [
  { title: '', id: '0' },
  { title: '未见肉芽肿', id: '1' },
  { title: '可见肉芽肿', id: '2' },
  { title: '可疑肉芽肿', id: '3' },
  { title: '异物肉芽肿', id: '4' },
  { title: '隐窝破裂性肉芽肿', id: '5' },
];
var siteOptList = [
  { title: '', id: '0' },
  { title: '黏膜层', id: '1' },
  { title: '黏膜下层', id: '2' },
];
var numOptList = [
  { title: '', id: '0' },
  { title: '单个', id: '1' },
  { title: '多个', id: '2' },
];
var maxDiamOptList = [
  { title: '', id: '0' },
  { title: '1', id: '1' },
  { title: '2', id: '2' },
  { title: '3', id: '3' },
  { title: '4', id: '4' },
  { title: '5', id: '5' },
  { title: '6', id: '6' },
];
var yyxOptList = [
  { title: '', id: '0' },
  { title: '未做', id: '1' },
  { title: '阴性', id: '2' },
  { title: '阳性', id: '3' },
];
var yxzsOptList = [
  { title: '', id: '0' },
  { title: '未见异型增生', id: '1' },
  { title: '低级别异型增生', id: '2' },
  { title: '高级别异型增生', id: '3' },
  { title: '不确定性异型增生', id: '4' },
];
var letterList = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#ibdCjhj1 .cjhj-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView()
    } else {
      initPage();
    }
  }
}

// 回显预览内容
function initView() {
  let hideKey = [];
  curElem.find('.cjhj-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'sampleSeen') {
      if (value) {
        $(this).html(value)
      } else {
        $(this).parent().hide();
        hideKey.push(key)
      }
    } else if (key === 'impression') {
      let pathDiagVal = getImpression();
      if (pathDiagVal) {
        $(this).html(pathDiagVal)
      } else {
        $(this).parent().hide();
        hideKey.push(key)
      }
    } else {
      value ? $(this).html(value) : $(this).html('')
      if (!value && key === 'clinDiag') {
        $(this).parent().parent().hide()
      }
    }
    addIdToNodeByView(this, key, idAndDomMap);
  })
  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (let i = 0; i < rptImageList.length; i++) {
      if (rptImageList[i].src && i < 4) {
        imgHtml += `
        <div class="item-img">
          <img src="${rptImageList[i].src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find('.cjhj-view .rpt-img-ls').html(imgHtml);
      curElem.find('.cjhj-view .rpt-img-ls').css('display', 'flex');
    }
  }
  curElem.find('.cjhj-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}
// 页面初始化
function initPage() {
  // id列表数据初始化
  organIdList = initIdList('2');
  // sortIdList = initIdList('4');
  nmIdList = initIdList('6');
  mxyzIdList = initIdList('8');
  zxlIdList = initIdList('10');
  jrIdList = initIdList('12');
  jgIdList = initIdList('14');
  jgSubIdList = initIdList('15');
  spIdList = initIdList('17');
  hsIdList = initIdList('19');
  xrIdList = initIdList('21');
  hdxyIdList = initIdList('23');
  ryzIdList = initIdList('25');
  siteIdList = initIdList('27');
  numIdList = initIdList('29');
  maxDiamIdList = initIdList('31');
  ksrsIdList = initIdList('33');
  yxzsIdList = initIdList('35');
  cmvIdList = initIdList('37');
  emrIdList = initIdList('40');

  initInpAndSel(organIdList, organOptList);
  // initInpAndSel(sortIdList,sortOptList);
  initInpAndSel(nmIdList, nmOptList);
  initInpAndSel(mxyzIdList, stateOptList, 152);
  initInpAndSel(zxlIdList, stateOptList, 168);
  initInpAndSel(jrIdList, jrOptList, 152);
  initInpAndSel(jgIdList, jgOptList, 92);
  initInpAndSel(jgSubIdList, jgSubOptList, 168);
  initInpAndSel(spIdList, spOptList, 104);
  initInpAndSel(hsIdList, hsOptList, 104);
  initInpAndSel(xrIdList, xrOptList, 104);
  initInpAndSel(hdxyIdList, hdxyOptList, 104);
  initInpAndSel(ryzIdList, ryzOptList, 120);
  initInpAndSel(siteIdList, siteOptList, 104);
  initInpAndSel(numIdList, numOptList, 104);
  initInpAndSel(maxDiamIdList, maxDiamOptList, 72);
  initInpAndSel(ksrsIdList, yyxOptList, 104);
  initInpAndSel(yxzsIdList, yxzsOptList, 232);
  initInpAndSel(cmvIdList, yyxOptList, 152);
  initInpAndSel(emrIdList, yyxOptList, 152);

  for (let letter of letterList) {
    changeSel(`cjhj${letter}-rt-37`);
    changeSel(`cjhj${letter}-rt-40`);
  }
  // curElem.find('#ibdCjhj1 .cjhj-edit .rt-sr-w').change(function () {
  //   let str = getImpression();
  //   console.log('>>>str', str);
  // })
}

// 初始化id列表
function initIdList(num) {
  let arr = [];
  for (let letter of letterList) {
    arr.push(`cjhj${letter}-rt-${num}`);
  }
  return arr;
}

// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal, isNoList) {
  let selLen = 200;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  if (isNoList) {
    dropdown.render({
      elem: `#${idList}`,
      data: optionList,
      className: 'laySelLab',
      click: function (obj) {
        this.elem.val(obj.title);
      },
      style: `width: ${selLen}px;`
    });
  } else {
    idList.forEach(item => {
      dropdown.render({
        elem: `#${item}`,
        data: optionList,
        className: 'laySelLab',
        click: function (obj) {
          this.elem.val(obj.title);
          changeSel(item);
        },
        style: `width: ${selLen}px;`
      });
    });
  }

}

// 转换下拉数据格式
function transformOptList(list) {
  let arr = [];
  list.forEach((item, index) => {
    arr.push({ title: item.name, id: index });
  })
  return arr;
}


// 排序
function sortKeysFun(list) {
  let sortKey = Object.keys(list).sort((a, b) => {
    // a-b是升序   b-a是降序
    return list[a].sort - list[b].sort;
  });
  return sortKey;
}

// CMV、EMR
function changeSel(item) {
  for (let letter of letterList) {
    if (item === `cjhj${letter}-rt-37`) {
      if ($(`#cjhj${letter}-rt-37`).val() === '阳性') {
        $(`#cjhj${letter}-rt-38`).prop('disabled', false);
      } else {
        $(`#cjhj${letter}-rt-38`).val('');
        $(`#cjhj${letter}-rt-38`).prop('disabled', true);
      }
    }
    if (item === `cjhj${letter}-rt-40`) {
      if ($(`#cjhj${letter}-rt-40`).val() === '阳性') {
        $(`#cjhj${letter}-rt-41`).prop('disabled', false);
      } else {
        $(`#cjhj${letter}-rt-41`).val('');
        $(`#cjhj${letter}-rt-41`).prop('disabled', true);
      }
    }
  }
}

// 诊断/印象
function getImpression() {
  let impression = '';
  // let impreStr = $('#cjhj-rt-2').val();
  let impreStr = '';
  // let strList = {
  //   'A': { result: [], organName: '', sort: '' },
  //   'B': { result: [], organName: '', sort: '' },
  //   'C': { result: [], organName: '', sort: '' },
  //   'D': { result: [], organName: '', sort: '' },
  //   'E': { result: [], organName: '', sort: '' },
  //   'F': { result: [], organName: '', sort: '' },
  //   'G': { result: [], organName: '', sort: '' },
  // };
  let strArr = [], impArr = [];
  let sortIndex = 0;
  for (let i = 0; i < letterList.length; i++) {
    // let str = '';
    let orgVal = $(`#cjhj${letterList[i]}-rt-2`).val() || '';
    // let sortVal = $(`#cjhj${letter}-rt-4`).val();
    let examResult = $(`#cjhj${letterList[i]}-rt-43`).val() || '';
    let impStr = $(`#cjhj${letterList[i]}-rt-45`).val() || '';
    impStr ? impArr.push(impStr) : ''
    if (orgVal || examResult) {
      sortIndex++;
      organNamesArr.push(orgVal)
      strArr.push(`${sortIndex}${orgVal ? '、' + orgVal : ''}：${examResult}`)
      // strList[letterList[i]].organName = orgVal;
      // str += orgVal + ' ';
      // str += examResult;
      // strList[letterList[i]].sort = sortIndex;
      // strList[letterList[i]].result.push(str);
    }
  }
  // let sortKeyArr = sortKeysFun(strList);
  // let index = 1;
  // strList.forEach(key => {
  //   let result = strList[key].result;
  //   let organName = strList[key].organName;
  //   let resStr = '';
  //   if (result.length) {
  //     organNamesArr.push(organName);
  //     resStr += index + '、' + result;
  //     strArr.push(resStr);
  //     index++;
  //   }
  // })
  impArr.length > 0 ? impreStr = impArr.join('；') : ""
  impreStr ? strArr.push('综上：' + impreStr) : '';
  impression = strArr.join('\n');
  // console.log(impression);
  return impression;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  rtStructure.reportInfo.organNames = organNamesArr.join(',')
}