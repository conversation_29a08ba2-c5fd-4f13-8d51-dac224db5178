#gfeksyg1 {
  position: relative;
  font-size: 16px;
  min-height: 100%;
  margin: 0 auto;
  padding: 8px 24px;
  background-color: #F5F7FA;
  * {
    font-family: 宋体;
  }
  .edit-wrap {
    .item-wrap {
      display: flex;
    }
    .label {
      font-size: 16px;
      color: #000000;
      line-height: 28px;
    }
    .options {
      flex: 1;
    }
    input[type="text"] {
      width: 100%;
      padding: 0 8px;
      border-radius: 3px;
      border: 1px solid #C0C4CC;
      margin-bottom: 4px;
    }
  }
  .view-wrap {
    display: none;
  }
  .hpt-info{
    position: relative;
    text-align: center;
    img {
      position: absolute;
      left: 80px;
      top: 0;
    }
    h1 {
      font-weight: 800;
      font-size: 24px;
      color: #000000;
      line-height: 32px;
    }
    h5 {
      font-size: 14px;
      color: #000000;
      line-height: 20px;
    }
    h2 {
      font-weight: 800;
      font-size: 18px;
      color: #000000;
      line-height: 26px;
    }
  }
  .patient-info {
    position: relative;
    border-top: 1px solid #999999;
    border-bottom: 1px solid #999999;
    margin-top: 20px;
    padding: 4px 0;
    display: flex;
    flex-wrap: wrap;
    li {
      width: 30%;
      flex-grow: 0;
      flex-shrink: 0;
      line-height: 30px;
      font-size: 16px;
      &:nth-of-type(3n-2) {
        width: 40%;
      }
      &.out {
        position: absolute;
        right: 0;
        top: -30px;
        width: auto;
      }
    }
    .lb {
      color: #000000;
      line-height: 23px;
    }
    .val {
      color: #000000;
      line-height: 23px;
    }
  }
  .rt-sr-body {
    margin-top: 8px;
    .detail-item {
      margin-top: 8px;
      &.flex {
        display: flex;
      }
    }
    .separator {
      margin-top: 8px;
      border-top: 1px solid #999999;
    }
    .imgs {
      margin-top: 8px;
      img {
        margin-right: 10px;
        margin-bottom: 10px;
        width: 320px;
        height: 240px;
        object-fit: contain;
      }
    }
    .lb {
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      line-height: 23px;
      flex-grow: 0;
      flex-shrink: 0;
    }
    .txt, .text-con {
      font-size: 16px;
      color: #000000;
      line-height: 23px;
      word-break: break-all;
    }
  }
  .rt-sr-footer {
    position: absolute;
    left: 50px;
    right: 50px;
    bottom: 20px;
    .btm-info {
      border-top: 1px solid #999999;
      border-bottom: 1px solid #999999;
      padding: 5px 0;
      display: flex;
      justify-content: space-between;
      img {
        height: 36px;
        vertical-align: top;
      }
    }
    .itm {
      font-size: 16px;
    }
    .lb {
      color: #000000;
      line-height: 36px;
    }
    .val {
      color: #000000;
      line-height: 36px;
    }
    .tip {
      margin-top: 8px;
      font-size: 12px;
      color: #000000;
      line-height: 17px;
      text-align: right;
      &.flex {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
  * {
    font-family: 宋体;
    font-size: 16px;
  }
}

[isview="true"] #gfeksyg1 {
  padding: 30px 50px 100px;
  width: 780px;
  min-height: 1100px;
  background: white;
  .edit-wrap {
    display: none;
  }
  .view-wrap {
    display: block;
  }
}