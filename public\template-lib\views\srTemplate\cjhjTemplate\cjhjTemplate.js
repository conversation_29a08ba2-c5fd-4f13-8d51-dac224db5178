$(function() {
  window.initHtmlScript = initHtmlScript;
})
var organNamesArr = [];
// id列表
var organIdList = [], sortIdList = [], nmIdList = [], mxyzIdList = [], zxlIdList = [],jrIdList = [], jgIdList = [], jgSubIdList = [], spIdList = [], hsIdList = [],xrIdList = [], hdxyIdList = [], ryzIdList = [], siteIdList = [],numIdList = [], maxDiamIdList = [], ksrsIdList = [], yxzsIdList = [], cmvIdList = [], emrIdList = [];
// 下拉列表
var organOptList = [
  { title: '', id: '0' },
  { title: '回肠末端', id: '1' },
  { title: '盲肠', id: '2' },
  { title: '升结肠', id: '3' },
  { title: '横结肠', id: '4' },
  { title: '降结肠', id: '5' },
  { title: '乙状结肠', id: '6' },
  { title: '直肠', id: '7' },
];
var sortOptList = [
  { title: '', id: '0' },
  { title: 1, id: '1' },
  { title: 2, id: '2' },
  { title: 3, id: '3' },
  { title: 4, id: '4' },
  { title: 5, id: '5' },
  { title: 6, id: '6' },
  { title: 7, id: '7' },
];
var nmOptList = [
  { title: '', id: '0' },
  { title: '正常', id: '1' },
  { title: '肠炎', id: '2' },
  { title: '慢性肠炎', id: '3' },
];
var stateOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '轻度增多', id: '2' },
  { title: '中度增多', id: '3' },
  { title: '重度增多', id: '4' },
];
var jrOptList = [
  { title: '', id: '0' },
  { title: '局灶性', id: '1' },
  { title: '弥漫性', id: '2' },
];
var jgOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '局灶性', id: '2' },
]
var jgSubOptList = [
  { title: '', id: '0' },
  { title: '弥漫性', id: '1' },
  { title: '隐窝分支', id: '2' },
  { title: '隐窝加长', id: '3' },
  { title: '隐窝缺失', id: '4' },
  { title: '隐窝缩短', id: '5' },
  { title: '基底淋巴浆细胞增多', id: '6' },
  { title: '结肠表面绒毛化', id: '7' },
  { title: '小肠绒毛变短、变平', id: '8' },
];
var spOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '局灶性', id: '2' },
  { title: '广泛性', id: '3' },
];
var hsOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '幽门腺化生', id: '2' },
  { title: '潘氏细胞化生(脾曲以后)', id: '3' },
];
var xrOptList = [
  { title: '', id: '0' },
  { title: '无', id: '1' },
  { title: '炎性息肉', id: '2' },
];
var hdxyOptList = [
  { title: '', id: '0' },
  { title: '非活动性', id: '1' },
  { title: '表面上皮炎', id: '2' },
  { title: '散在隐窝炎', id: '3' },
  { title: '明显隐窝炎', id: '4' },
  { title: '隐窝脓肿', id: '5' },
  { title: '糜烂或溃疡', id: '6' },
];
var ryzOptList = [
  { title: '', id: '0' },
  { title: '未见肉芽肿', id: '1' },
  { title: '可见肉芽肿', id: '2' },
  { title: '可疑肉芽肿', id: '3' },
  { title: '异物肉芽肿', id: '4' },
  { title: '隐窝破裂性肉芽肿', id: '5' },
];
var siteOptList = [
  { title: '', id: '0' },
  { title: '黏膜层', id: '1' },
  { title: '黏膜下层', id: '2' },
];
var numOptList = [
  { title: '', id: '0' },
  { title: '单个', id: '1' },
  { title: '多个', id: '2' },
];
var maxDiamOptList = [
  { title: '', id: '0' },
  { title: '1', id: '1' },
  { title: '2', id: '2' },
  { title: '3', id: '3' },
  { title: '4', id: '4' },
  { title: '5', id: '5' },
  { title: '6', id: '6' },
];
var yyxOptList = [
  { title: '', id: '0' },
  { title: '未做', id: '1' },
  { title: '阴性', id: '2' },
  { title: '阳性', id: '3' },
];
var yxzsOptList = [
  { title: '', id: '0' },
  { title: '未见异型增生', id: '1' },
  { title: '低级别异型增生', id: '2' },
  { title: '高级别异型增生', id: '3'},
  { title: '不确定性异型增生', id: '4' },
];
var letterList = ['A','B','C','D','E','F','G'];
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }
  initPage();
}

// 页面初始化
function initPage() {
  // id列表数据初始化
  organIdList = initIdList('2');
  sortIdList = initIdList('4');
  nmIdList = initIdList('6');
  mxyzIdList = initIdList('8');
  zxlIdList = initIdList('10');
  jrIdList = initIdList('12');
  jgIdList = initIdList('14');
  jgSubIdList = initIdList('15');
  spIdList = initIdList('17');
  hsIdList = initIdList('19');
  xrIdList = initIdList('21');
  hdxyIdList = initIdList('23');
  ryzIdList = initIdList('25');
  siteIdList = initIdList('27');
  numIdList = initIdList('29');
  maxDiamIdList = initIdList('31');
  ksrsIdList = initIdList('33');
  yxzsIdList = initIdList('35');
  cmvIdList = initIdList('37');
  emrIdList = initIdList('40');
  
  initInpAndSel(organIdList,organOptList);
  initInpAndSel(sortIdList,sortOptList);
  initInpAndSel(nmIdList,nmOptList);
  initInpAndSel(mxyzIdList,stateOptList);
  initInpAndSel(zxlIdList,stateOptList);
  initInpAndSel(jrIdList,jrOptList);
  initInpAndSel(jgIdList,jgOptList);
  initInpAndSel(jgSubIdList,jgSubOptList);
  initInpAndSel(spIdList,spOptList);
  initInpAndSel(hsIdList,hsOptList);
  initInpAndSel(xrIdList,xrOptList);
  initInpAndSel(hdxyIdList,hdxyOptList);
  initInpAndSel(ryzIdList,ryzOptList);
  initInpAndSel(siteIdList,siteOptList);
  initInpAndSel(numIdList,numOptList);
  initInpAndSel(maxDiamIdList,maxDiamOptList,148);
  initInpAndSel(ksrsIdList,yyxOptList);
  initInpAndSel(yxzsIdList,yxzsOptList);
  initInpAndSel(cmvIdList,yyxOptList);
  initInpAndSel(emrIdList,yyxOptList);

  getRepDate();
  if(rtStructure.enterOptions.type === 'edit') {
    getDoctList();
  }
  for(let letter of letterList) {
    changeSel(`cjhj${letter}-rt-37`);
    changeSel(`cjhj${letter}-rt-40`);
  }
}

// 初始化id列表
function initIdList(num) {
  let arr = [];
  for(let letter of letterList) {
    arr.push(`cjhj${letter}-rt-${num}`);
  }
  return arr;
}

// 初始化输入选择下拉框
function initInpAndSel(idList,optionList,lenVal,isNoList) {
  let selLen = 200;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  if(isNoList) {
    dropdown.render({
      elem: `#${idList}`,
      data: optionList,
      className: 'laySelLab',
      click: function(obj){
        this.elem.val(obj.title);
      },
      style: `width: ${selLen}px;`
    });
  }else {
    idList.forEach(item => {
      dropdown.render({
        elem: `#${item}`,
        data: optionList,
        className: 'laySelLab',
        click: function(obj){
          this.elem.val(obj.title);
          changeSel(item);
        },
        style: `width: ${selLen}px;`
      });
    });
  }
  
}

// 显示报告时间
function getRepDate() {
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  if(!resultData.length) {
    let curDate = getCurDateAndTime().curDate;
    $('.repDate').text(curDate);
  }
}

// 转换下拉数据格式
function transformOptList(list) {
  let arr = [];
  list.forEach((item,index) => {
    arr.push({ title:item.name,id:index });
  })
  return arr;
}

// 获取科室人员列表,预览页面不用请求
function getDoctList() {
  let resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  let deptUserList = getDepartUserList();
  deptUserList.length ? deptUserList = transformOptList(deptUserList) : [];
  // 该报告未填写过,取到当前登录人姓名，默认赋给报告医生和审核医生
  if(!resultData.length) {
    let optName = getParamByName('optName');
    $('.repDoct').val(optName);
    $('.verifyDoct').val(optName);
  }
  initInpAndSel('cjhj-rt-4',deptUserList,200,true);
  initInpAndSel('cjhj-rt-6',deptUserList,200,true);
}

// 排序
function sortKeysFun(list) {
  let sortKey = Object.keys(list).sort((a,b)=>{
    // a-b是升序   b-a是降序
    return list[a].sort - list[b].sort;
  });
  return sortKey;
}

// CMV、EMR
function changeSel(item) {
  for(let letter of letterList) {
    if(item === `cjhj${letter}-rt-37`) {
      if($(`#cjhj${letter}-rt-37`).val() === '阳性') {
        $(`#cjhj${letter}-rt-38`).prop('disabled',false);
      }else {
        $(`#cjhj${letter}-rt-38`).val('');
        $(`#cjhj${letter}-rt-38`).prop('disabled',true);
      }
    }
    if(item === `cjhj${letter}-rt-40`) {
      if($(`#cjhj${letter}-rt-40`).val() === '阳性') {
        $(`#cjhj${letter}-rt-41`).prop('disabled',false);
      }else {
        $(`#cjhj${letter}-rt-41`).val('');
        $(`#cjhj${letter}-rt-41`).prop('disabled',true);
      }
    }
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 描述
// function getdescription() {
//   let description = '';
//   let strList = {
//     'A': {result:[],sort:''},
//     'B': {result:[],sort:''},
//     'C': {result:[],sort:''},
//     'D': {result:[],sort:''},
//     'E': {result:[],sort:''},
//     'F': {result:[],sort:''},
//     'G': {result:[],sort:''},
//   };
//   let strArr = [];
//   for(let letter of letterList) {
//     let sortVal = $(`#cjhj${letter}-rt-4`).val();
//     let orgVal = $(`#cjhj${letter}-rt-2`).val();
//     orgVal ? strList[letter].result.push(`(${orgVal})`) : '';
//     for(let i=1;i<43;i++) {
//       if(orgVal && $(`#cjhj${letter}-rt-${i}`).is(":checked")) {
//         strList[letter].sort = sortVal;
//         strList[letter].result.push($(`#cjhj${letter}-rt-${i}`).val());
//       }
//     }
//   }
//   let sortKeyArr = sortKeysFun(strList);
//   let index = 1;
//   sortKeyArr.forEach(key => {
//     let result = strList[key].result.join(',');
//     let resStr = '';
//     if(result.length) {
//       resStr += index + '、' + result;
//       strArr.push(resStr);
//       index++;
//     }
//   })
//   description = strArr.join('\n');
//   console.log(description);
//   return description;
// }

// 诊断/印象
function getImpression() {
  let impression = '';
  let impreStr = $('#cjhj-rt-2').val();
  let strList = {
    'A': {result:[],organName:'',sort:''},
    'B': {result:[],organName:'',sort:''},
    'C': {result:[],organName:'',sort:''},
    'D': {result:[],organName:'',sort:''},
    'E': {result:[],organName:'',sort:''},
    'F': {result:[],organName:'',sort:''},
    'G': {result:[],organName:'',sort:''},
  };
  let strArr = [];
  for(let letter of letterList) {
    let str = '';
    let orgVal = $(`#cjhj${letter}-rt-2`).val();
    let sortVal = $(`#cjhj${letter}-rt-4`).val();
    let examResult = $(`#cjhj${letter}-rt-43`).val();
    if(orgVal) {
      strList[letter].organName = orgVal;
      str += orgVal + ' ';
      str += examResult;
      strList[letter].sort = sortVal;
      strList[letter].result.push(str);
    }
  }
  let sortKeyArr = sortKeysFun(strList);
  let index = 1;
  sortKeyArr.forEach(key => {
    let result = strList[key].result;
    let organName = strList[key].organName;
    let resStr = '';
    if(result.length) {
      organNamesArr.push(organName);
      resStr += index + '、' + result;
      strArr.push(resStr);
      index++;
    }
  })
  impreStr ? strArr.push('综上，' + impreStr) : '';
  impression = strArr.join('\n');
  // console.log(impression);
  return impression;
}

// 获取报告信息
function getReportInfo() {
  let reportInfo = {};
  let reporter = $('#cjhj-rt-4').val();
  let reportDate = $('#cjhj-rt-8').text();
  let affirmReporter = $('#cjhj-rt-6').val();
  reportInfo = {
    organNames: organNamesArr.join(','), //检查部位，多个，用','号隔开
    reporter: reporter, // 报告医生
    reportDate: reportDate, // 报告日期
    affirmReporter: affirmReporter, // 审核医生
    affirmDate: '', // 审核日期
  }
  // console.log(reportInfo);
  return reportInfo;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  // rtStructure.description = getdescription();
  rtStructure.impression = getImpression();
  rtStructure.reportInfo = getReportInfo();
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}