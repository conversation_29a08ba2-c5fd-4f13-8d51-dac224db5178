/* 宽度 */
.wd-24 {
  width: 24px;
}
.wd-26 {
  width: 26px;
}
.wd-36 {
  width: 36px;
}
.wd-42 {
  width: 42px;
}
.wd-70 {
  width: 70px;
}
.wd-212 {
  width: 212px;
}
/* 颜色 */
.tx-red {
  color: #E64545;
}
.tx-blue {
  color: #0000FF;
}
.bg-blue {
  background: #D6DFF7;
}
.bg-gray {
  background: #EAEAEA;
}
/* 间距 */
.ml-8 {
  margin-left: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.mb-24 {
  margin-bottom: 24px;
}
/* 边框 */
.bor-b {
  border-bottom: 1px solid #000;
}
.no-bor-r {
  border-right: none!important;
}
/* gdfyyyct页面 */
#gdfyyyct1 {
  width: 780px;
  min-height: 1100px;
  position: relative;
  margin: 0 auto;
  padding-bottom: 40px;
  font-size: 14px;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
}
#gdfyyyct1 .gdfyyyct-h {
  text-align: center;
}
#gdfyyyct1 .gdfyyyct-h img {
  margin-left: -1px;
  margin-top: -1px;
}
#gdfyyyct1 .gdfyyyct-h h1 {
  font-size: 22px;
  line-height: 22px;
  font-weight: bold;
  color: #000;
  margin-top: 12px;
}
#gdfyyyct1 .gdfyyyct-h h2 {
  font-size: 20px;
  line-height: 20px;
  color: #000;
  margin-top: 12px;
}
#gdfyyyct1 .gdfyyyct-h .top-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 32px;
  margin: 8px 50px 0;
  border-top: 1px solid #606266 ;
  border-bottom: 1px solid #606266 ;
  font-size: 16px;
}
#gdfyyyct1 .info-lb {
  color: #303133;
}
#gdfyyyct1 .info-con {
  color: #000;
}
#gdfyyyct1 .rpt-info {
  display: flex;
  justify-content: space-between;
  margin: 0 50px;
}
#gdfyyyct1 .gdfyyyct-b {
  margin-top: 12px;
  padding: 0 50px;
}
#gdfyyyct1 .yyct-echart {
  width: 680px;
  border: 1px solid #000;
}
#gdfyyyct1 .echart-h {
  height: 32px;
  line-height: 32px;
  text-align: center;
  background: #191975;
  border-bottom: 1px solid #000000;
  font-size: 16px;
  font-weight: bold;
  color: #FFF;
}
#gdfyyyct1 .echart-data {
  width: 656px;
  height: 278px;
  margin: auto;
}
#gdfyyyct1 .echart-data img {
  width: 100%;
  height: 100%;
}
#gdfyyyct1 .con-flex {
  display: flex;
}
#gdfyyyct1 .flex-item {
  flex: 1;
}
#gdfyyyct1 .box-left {
  width: 520px;
}
#gdfyyyct1 .box-right {
  flex: 1;
}
#gdfyyyct1 .jsy-tb thead tr,#gdfyyyct1 .tyjz-tb thead tr {
  height: 36px;
}
#gdfyyyct1 .jsy-tb thead tr th,#gdfyyyct1 .tyjz-tb thead tr th {
  border: 1px solid #000;
}
#gdfyyyct1 .jsy-tb thead tr th:not(:first-child) {
  border-left: none;
}
#gdfyyyct1 .jsy-tb thead tr th:not(:last-child) {
  border-right: none;
}
#gdfyyyct1 .jsy-tb tbody tr,#gdfyyyct1 .tyjz-tb tbody tr {
  height: 24px;
}
#gdfyyyct1 .jsy-tb tbody tr td {
  font-size: 12px;
  border: 1px solid #000;
}
#gdfyyyct1 .jsy-tb tbody tr td {
  border-top: none;
}
#gdfyyyct1 .jsy-tb tbody tr td:not(:last-child) {
  border-right: none;
}
#gdfyyyct1 .tyjz-tb tbody tr td:first-child {
  border-left: 1px solid #000;
  border-right: 1px solid #000;
}
#gdfyyyct1 .tyjz-tb tbody tr td:last-child,#gdfyyyct1 .stenger-tb tbody tr td:last-child {
  border-right: 1px solid #000;
}
#gdfyyyct1 .tyjz-tb tbody tr td:not(:first-child) {
  border-bottom: 1px solid #000;
  border-right: 1px solid #000;
}
#gdfyyyct1 .stenger-tb thead tr {
  height: 24px;
}
#gdfyyyct1 .stenger-tb thead tr th {
  border: 1px solid #000;
}
#gdfyyyct1 .stenger-tb tbody tr td {
  text-align: left;
  padding-left: 6px;
  border-left: 1px solid #000;
  border-bottom: 1px solid #000;
}
#gdfyyyct1 .gdfyyyct-b .remark-lb {
  display: inline-block;
  min-width: 54px;
  font-size: 18px;
  color: #828282;
}
#gdfyyyct1 .remark-inp {
  width: 100%;
  height: 168px;
  border: 1px solid #DCDFE6;
}
#gdfyyyct1 .gdfyyyct-f {
  position: absolute;
  left: 50px;
  right: 50px;
  bottom: 5px;
  border-top: 1px solid #F2F2F2;
  padding-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#gdfyyyct1 .gdfyyyct-f input[type="text"] {
  height: 36px;
  border-radius: 3px;
  padding: 0 4px;
  font-size: 14px;
  border: 1px solid #DCDFE6;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}