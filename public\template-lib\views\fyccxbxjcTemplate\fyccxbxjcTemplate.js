// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
  'ccxbx-rt-28': { label: '送检医生', wt: '1', hisKey: 'optName' },
}
var saveApplyParams = {
  'medRecord': { label: '病史及辅助检查结果', id: 'ccxbx-rt-24'},
  'clinDiag': { label: '临床诊断', id: 'ccxbx-rt-26'},
  'optName': { label: '送检医生', id: 'ccxbx-rt-28'},
  'applyItemList': { label: '检查项目', id: ''},
  'gmSampleList': { label: '标本信息', id: ''},
}

function initHtmlScript(ele) {
  // 编辑
  if($(ele).attr('isview') !== 'true') {
    initDatePicker();
    if($(".sampleViewPart").length) {
        specialBlockViewHandler('sample', false)
    }
    if($(".eItemViewPart").length) {
        specialBlockViewHandler('examItem', false)
    }
    setDisabledByConfig && setDisabledByConfig(applyStatusRes);
  } else { // 预览
    if($(".sampleViewPart").length) {
        specialBlockViewHandler('sample', true)
    }
    if($(".eItemViewPart").length) {
        specialBlockViewHandler('examItem', true)
    }
  }
  getExamSubClassList(applyInfo);  //检查子类
  getGmSamplePartByLevelList();   //检查部位
  getSampleList(applyInfo);  //标本
  setValByHis(hisAndFormMap)
}

// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('laydate', function () {
      var laydate = layui.laydate;
      //执行一个laydate实例
      laydate.render({
          elem: '.date-wrap', //指定元素
          type: 'date',
          trigger: 'click',
          format: 'yyyy-MM-dd'
      });
  });
}

$(function () {
  window.initHtmlScript = initHtmlScript;
})