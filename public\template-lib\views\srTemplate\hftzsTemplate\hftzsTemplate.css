#hftzs1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#hftzs1 * {
  font-family: '宋体';
}
.box-p{
  padding: 8px 20px;
}
.box-t{
  font-weight: 600;
  margin-bottom: 9px;
}
.mb-4{
  margin-bottom: 4px;
}
#hftzs1 .mb-4 label {
  align-items: center;
  margin-top: 2px;
  color: #000;
}
.r-5{
  margin-right: 5px;
  vertical-align: middle;
}
.t-8{
  margin-top: 8px;
}
.in-sly{
  height: 28px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
  width: calc(100% - 400px);
  display: block;
  margin-left: 20px;
}
#hftzs1 .hftzs-view {
  display: none;
}
[isview="true"] #hftzs1 .hftzs-edit {
  display: none;
}
[isview="true"] #hftzs1 .hftzs-view {
  display: block;
}
/* 预览部分 */
#hftzs1 .hftzs-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 20px;
}
#hftzs1 .hftzs-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 19px;
  text-align: center;
}
#hftzs1 .hftzs-view .head-img {
  position: absolute;
  top: 30px;
  left: 110px;
  width: 64px;
  height: 64px;
}
#hftzs1 .hftzs-view .botrs {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#hftzs1 .hftzs-view .head-img img {
  width: 100%;
  height: 100%;
}
#hftzs1 .hftzs-view .page-tit {
  font-size: 24px;
  font-weight: 800;
  font-family: "宋体", sans-serif !important;
}
#hftzs1 .hftzs-view .right-num {
  position: absolute;
  top: 76px;
  right: 56px;
}
#hftzs1 .hftzs-view .right-num .black-txt {
  display: inline-block;
  max-width: 148px;
}
#hftzs1 .hftzs-view .sub-tit {
  font-size: 18px;
}
#hftzs1 .hftzs-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  /* margin-bottom: 8px; */
}
#hftzs1 .hftzs-view .p-item+.p-item {
  margin-top: 8px;
}
#hftzs1 .hftzs-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}
#hftzs1 .hftzs-view .info-i + .info-i {
  margin-left: 8px;
}
#hftzs1 .hftzs-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#hftzs1 .hftzs-view .item-l{
  font-weight: 800;
}
#hftzs1 .hftzs-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 56px;
  right: 56px;
}
#hftzs1 .hftzs-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hftzs1 .hftzs-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#hftzs1 .hftzs-view .reporter-i img {
  width: 90px;
  height: 40px;
  object-fit: contain;
}
#hftzs1 .hftzs-view .reporter-i span:last-child {
  flex: 1;
}
#hftzs1 .hftzs-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#hftzs1 .hftzs-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
}
#hftzs1 .hftzs-view .item-img {
  width: 264px;
  /* height: 200px; */
  border: 1px solid #eee;
  margin-right: 34px;
  margin-bottom: 8px;
}
#hftzs1 .hftzs-view .item-img img {
  width: 100%;
  height: 160px;
  object-fit: contain;
}
#hftzs1 .hftzs-view .jcjg-table th, .jcjg-table td{
  border-color: #DCDFE6;
  text-align: center;
  height: 28px;
  text-align: left;
  padding-left: 12px;
}
#hftzs1 .hftzs-view .gray-txt{
  color: #000;
}
#hftzs1 .hftzs-view .black-txt{
  color: #000;
}