.singleDisEditReport.main-page {
  min-width: unset;
}

#ibdWjhj1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  /* background: #F5F7FA; */
  overflow: auto;
}

#ibdWjhj1 * {
  font-family: '宋体';
}

.text-blod {
  font-weight: bold;
}

.p-item+.p-item {
  margin-top: 4px;
}

.disease-item {
  padding: 8px 24px;
  margin-bottom: 8px;
  background: #F5F7FA;
}

#ibdWjhj1 .fot .rt-sr-w.sel {
  width: 148px;
  height: 28px;
}

#ibdWjhj1 .layui-inline input {
  height: 28px;
  position: relative;
  z-index: 10;
  background: transparent;
  padding: 0 16px;
  border-radius: 3px;
  border: 1px solid #DCDFE6;
}

#ibdWjhj1 .showInt {
  background: #fff;
}

#ibdWjhj1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}

#ibdWjhj1 .showInt.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}

.laySelLab li {
  min-height: 26px;
}

#ibdWjhj1 .layui-inline input:disabled {
  background-color: rgba(240, 242, 245, 1)
}

.c-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

#ibdWjhj1 .wjhj-view {
  display: none;
}

[isview="true"] #ibdWjhj1 .wjhj-edit {
  display: none;
}

[isview="true"] #ibdWjhj1 .wjhj-view {
  display: block;
}

#ibdWjhj1 .wjhj-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 16px;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 130px;
  color: #000;
}

#ibdWjhj1 .wjhj-view .view-head {
  width: 100%;
  border-bottom: 1px solid #999;
  padding-bottom: 24px;
  text-align: center;
}

#ibdWjhj1 .wjhj-view .head-img {
  margin: 0 auto 10px;
  width: 308px;
  height: 40px;
}

#ibdWjhj1 .wjhj-view .head-img img {
  width: 100%;
  height: 100%;
}

#ibdWjhj1 .wjhj-view .right-num {
  display: flex;
  position: absolute;
  top: 110px;
  right: 56px;
  color: #41898A;
  line-height: 24px;
}

#ibdWjhj1 .wjhj-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#ibdWjhj1 .wjhj-view .black-txt {
  color: #000;
  font-size: 16px;
  line-height: 24px;
}

#ibdWjhj1 .wjhj-view .page-tit {
  font-size: 24px;
  font-weight: 800;
}

#ibdWjhj1 .wjhj-view .page-tit.sub-tit {
  font-size: 28px;
}

#ibdWjhj1 .wjhj-view .gray-txt {
  color: #000;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 24px;
}

#ibdWjhj1 .wjhj-view .info-i {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

#ibdWjhj1 .wjhj-view .info-i+.info-i {
  margin-left: 8px;
}

#ibdWjhj1 .wjhj-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

#ibdWjhj1 .wjhj-view .view-patient .p-item {
  align-items: center;
}

#ibdWjhj1 .wjhj-view .rpt-img-ls {
  display: none;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 0;
  justify-content: center;
}

#ibdWjhj1 .wjhj-view .item-img {
  /* display: inline-block; */
  width: 214px;
  height: 160px;
  box-sizing: border-box;
  border: 1px solid #eee;
  margin-right: 12px;
  margin-top: 12px;
}

#ibdWjhj1 .wjhj-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

#ibdWjhj1 .wjhj-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}

#ibdWjhj1 .wjhj-view .report-wrap {
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#ibdWjhj1 .wjhj-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

#ibdWjhj1 .wjhj-view .reporter-i.flex-1 {
  flex: 1.5;
}

#ibdWjhj1 .wjhj-view .reporter-i > span:first-child {
  width: 80px;
}
#ibdWjhj1 .wjhj-view .reporter-i > span:last-child {
  flex: 1;
}

#ibdWjhj1 .wjhj-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}

#ibdWjhj1 .wjhj-view .reporter-i+.reporter-i {
  margin-left: 8px;
}

/* 宽度 */
.wd-64 {
  width: 64px;
}

.wd-72 {
  width: 72px;
}

.wd-80 {
  width: 80px;
}

.wd-104 {
  width: 104px;
}

.wd-120 {
  width: 120px;
}

.wd-152 {
  width: 152px;
}

.wd-168 {
  width: 168px;
}

.wd-232 {
  width: 232px;
}

.wd-340 {
  width: 340px;
}

/* 间距 */
.mlr-4 {
  margin: 0 4px;
}

.mt-4 {
  margin-right: 4px;
}

.mr-4 {
  margin-right: 4px;
}

.mr-58 {
  margin-right: 58px;
}

.mb-12 {
  margin-bottom: 12px;
}

.pl-72 {
  padding-left: 72px;
}