#hlws1 {
  font-size: 14px;
}
#hlws1 .card {
  border: 1px solid #DCDFE6;
}
#hlws1 .card .card-header {
  height: 36px;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
  line-height: 36px;
  padding-left: 16px;
  border-bottom: 1px solid #DCDFE6;
  background: #ECF5FF;
}
#hlws1 .card .card-content {
  display: flex;
  flex-direction: column;
  row-gap: 8px;
  padding: 12px 16px;
}
#hlws1 .card .card-content .agent-line {
  column-gap: 8px;
}
#hlws1 .card .card-content .agent-info {
  width: 33%;
}
#hlws1 .add-row {
  color: #1885F2;
  cursor: pointer;
}
#hlws1 .suffix-input {
  display: flex;
  align-items: center;
  height: 36px;
}
#hlws1 .suffix-input input {
  padding-left: 6px;
  border-color: #DCDFE6;
  height: 100%;
  width: 60px;
}
#hlws1 .suffix-input .suffix {
  line-height: 36px;
  text-align: center;
  width: 48px;
  background: #F5F7FA;
  height: 100%;
  border: 1px solid #DCDFE6;
}

.custom-select {
  position: relative;
}
.custom-select::after {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 4px;
  content: "";
  width: 8px;
  height: 8px;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg) translateY(-100%);
}

.label-color {
  color: #606266;
}

.bt {
  border-top: 1px solid #DCDFE6;
}

.rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  width: 100%;
  padding: 4px;
}

.rt-sr-label {
  margin-right: 8px;
  column-gap: 8px;
}

.rt-sr-r {
  margin-right: 4px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-6 {
  margin-top: 6px;
}

.m-0-6 {
  margin: 0 6px;
}

.w-10 {
  width: 10px;
}

.w-15 {
  width: 15px;
}

.w-20 {
  width: 20px;
}

.w-25 {
  width: 25px;
}

.w-30 {
  width: 30px;
}

.w-35 {
  width: 35px;
}

.w-40 {
  width: 40px;
}

.w-45 {
  width: 45px;
}

.w-50 {
  width: 50px;
}

.w-55 {
  width: 55px;
}

.w-60 {
  width: 60px;
}

.w-65 {
  width: 65px;
}

.w-70 {
  width: 70px;
}

.w-75 {
  width: 75px;
}

.w-80 {
  width: 80px;
}

.w-85 {
  width: 85px;
}

.w-90 {
  width: 90px;
}

.w-95 {
  width: 95px;
}

.w-100 {
  width: 100px;
}

.w-105 {
  width: 106px;
}

.w-110 {
  width: 110px;
}

.w-115 {
  width: 115px;
}

.w-120 {
  width: 120px;
}

.w-125 {
  width: 125px;
}

.w-130 {
  width: 130px;
}

.w-135 {
  width: 135px;
}

.w-140 {
  width: 140px;
}

.f-1 {
  flex: 1;
}

.fw-600 {
  font-weight: 600;
}

.a-center {
  display: flex;
  align-items: center;
}

.a-start {
  display: flex;
  align-items: flex-start;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.layui-input:read-only {
  background: #F2F6FC;
  cursor: not-allowed;
}
