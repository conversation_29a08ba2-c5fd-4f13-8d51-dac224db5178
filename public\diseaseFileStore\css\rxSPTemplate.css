.rx-style.flex-p {
  display: flex;
  flex-direction: column;
  border: 1px solid #C8D7E6;
  height: 100%;
}
.rx-style .p-content {
  flex: 1;
  width: 100%;
  display: flex;
}
.rx-style .rt-sr-tit {
  width: 100px;
}
.rx-style .l-part, .rx-style .r-part {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.rx-style .r-part {
  border-left: 1px solid #C8D7E6;
}
.rx-style .rx-header {
  height: 45px;
  line-height: 45px;
  text-align: center;
  font-size: 16px;
  background: #EBEEF5;
  border-bottom: 1px solid #C8D7E6;
}
.rx-style .rt-sr-tit {
  color: #606266;
}
.rx-style .rx-body {
  padding: 12px;
}
.rx-style .p-item + .p-item {
  margin-top: 10px;
}
.rx-style .p-footer {
  text-align: right;
  font-size: 16px;
  color: #000;
  background: #EBEEF5;
  border-top: 1px solid #C8D7E6;
  padding: 12px;
}
.rx-style .prin-textarea {
  min-height: 100px;
  border: 1px solid #DCDFE6;
  padding: 4px 10px;
  border-radius: 3px;
  font-family: Microsoft YaHei;
  width: 100%;
  word-break: break-all;
  display: none;
  white-space: pre-wrap;
}
.rx-style .readonly .rt-sr-w, .rx-style .readonly .r-i {
  pointer-events: none;
}