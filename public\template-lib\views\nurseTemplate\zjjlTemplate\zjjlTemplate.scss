#zjjl1 {
  font-size: 16px;
}
#zjjl1 .zjjl-content {
  height: 100%;
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#zjjl1 .zjjl-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  margin-bottom: 4px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}
#zjjl1 .item-content {
  margin-left: 16px;
  margin-top: 10px;
  margin-bottom: 20px;
}
#zjjl1 .form-title {
  color: #606266;
}
#zjjl1 .sign-content {
  display: flex;
  align-items: center;
  margin-left: 16px;
  margin-top: 12px;
}
#zjjl1 .sign-content .layui-input {
  // width: 240px;
  flex:1;
}
#zjjl1 .weight {
  margin-right: 20px;
  display: flex;
  align-items: center;
}
#zjjl1 .sign-content .temperature {
  display: flex;
  align-items: center;
  flex: 1;
}
#zjjl1 .sign-content .unit {
  width: 52px;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #DCDFE6;
  border-left: 0;
  line-height: 38px;
  text-align: center;
  color: #606266;
}
#zjjl1 .drug-content {
  margin-left: 16px;
  margin-top: 10px;
  margin-bottom: 12px;
  width: calc(100% - 32px);
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
}
#zjjl1 .zjjl-content .gray-item {
  margin-left: 16px;
  display: flex;
  align-items: center;
}
#zjjl1 .zjjl-content .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#zjjl1 .zjjl-content .radio-label .rt-sr-lb {
  margin-left: 4px;
}
#zjjl1 .drug-content .drug-top {
  width: 100%;
  padding: 0 16px;
  height: 40px;
  border-radius: 6px 6px 0px 0px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#zjjl1 .table-content {
  padding: 0 16px;
}
#zjjl1 .add-btn {
  width: 82px;
  color: #1885F2;
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  cursor: pointer;
}
#zjjl1 .drug-btn {
  width: 100px;
  color: #1885F2;
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  cursor: pointer;
}
#zjjl1 .close-icon {
  color: #606266;
  margin-left: 5px;
  width: 55px;
  height: 16px;
  text-align: right;
  line-height: 14px;
  font-size: 14px;
  font-family: "Microsoft YaHei";
  cursor: pointer;
}

.custom-select {
  position: relative;
  
  &::after{
    display: inline-block;
    position: absolute;
    top: 50%;
    right: 4px;
    content: '';
    width: 8px;
    height: 8px;
    border-left: 1px solid #999;
    border-bottom:1px solid #999;
    transform: rotate(-45deg) translateY(-100%);
  }

}
/*# sourceMappingURL=zjjlTemplate.css.map */
