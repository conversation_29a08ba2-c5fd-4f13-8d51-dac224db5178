$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; //结果集，用于判断是否为新报告
var bzTitleClone = null;   //作为病灶示例模板的title
var isSavedReport = false; //模板是否填写过
var bzConClone = null;  //作为病灶示例模板具体内容
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || {}) : {}; // 结果集
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    pageInit();
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewPageCon();
    } else {
      // 获取推导结果
      curElem.find('#jcact1 .rpt-con .rt-sr-w').change(function() {
        getImpressionText();
      })
    }
  }
}
// 页面初始化
function pageInit() {
  // 初始化将病灶参照代码复制存储起来
  initBzCloneEle();
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);
  // 切换病灶
  toggleBzHandler();
  // 回显病灶内容
  displayBzContent();
  // 其他征象状态回显
  toggleOtherCon();
}

// 其他征象状态回显
function toggleOtherCon() {
  let keys = ['jcact-rt-18', 'jcact-rt-22', 'jcact-rt-26', 'jcact-rt-30'];
  let handler = (target) => {
    for(let key of keys) {
      if(target && key !== target) {
        continue;
      }
      let checkNode = curElem.find('[pid="'+key+'"]:checked');
      let checkId = curElem.find('[pid="'+key+'"][value="是"]').attr('id');
      if(checkNode.val() === '是') {
        curElem.find('[pid="'+checkId+'"]').show();
      } else {
        curElem.find('[pid="'+checkId+'"]').hide();
      }
      if(!target) {
        curElem.find('[id="'+key+'"]').css('min-width', '69px');
        // 未保存过默认选中否
        if(!isSavedReport) {
          curElem.find('[pid="'+key+'"][value="否"]').click();
        }
        curElem.find('[pid="'+key+'"]').change(function() {
          handler(key);
        })
      }
    }
  }
  handler();
}

// 切换病灶
function toggleBzHandler() {
  $('#jcact1 .bz-wrap').on('click', '.bz-tit-i', function(e) {
    var target = $(this).attr('tab-id');
    $(this).siblings('.bz-tit-i').removeClass('act');
    $(this).addClass('act');
    $('#jcact1 .bz-item[tab-target="'+target+'"]').siblings('#jcact1 .bz-item').removeClass('act');
    $('#jcact1 .bz-item[tab-target="'+target+'"]').addClass('act');
  })
}

// 复制暂存病灶的具体模板示例内容，为后续添加病灶做准备
function initBzCloneEle() {
  var bzTitle = curElem.find('#jcact1 .jca-bz .bz-wrap .bz-tit-i').clone(true);  //作为病灶示例模板的title
  bzTitleClone = '<div class="bz-tit-i act" tab-id="jcact-rt-000">'+bzTitle.html()+'</div>';
  var bzCon = curElem.find('#jcact1 .jca-bz .bz-wrap .bz-item').clone(true);
  bzConClone = '<div class="bz-item act" tab-target="jcact-rt-000">'+bzCon.html()+'</div>';
  curElem.find('#jcact1 .jca-bz .bz-wrap .bz-tit-ls').html('');
  curElem.find('#jcact1 .jca-bz .bz-wrap .bz-con').html('');
}

// 回显病灶内容
function displayBzContent() {
  if(resultData && resultData.length) {
    var bzData = resultData.filter(bzItem => bzItem.id === 'jcact-bz-1');
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      if(!bzList.length) {
        $('#jcact1 .jca-bz .bz-wrap').hide();
      }
      bzList.forEach((item) => {
        var paneId = item.id.replace('jcact-rt-', '');
        addBzHandler(paneId);
        // 非区域淋巴组回显
        var bzChildData = item.child || [];
        var lbjData = bzChildData.filter(childItem => childItem.id === `jcact-rt-${paneId}-74`);
        lbjData.map(lbjItem => {
          var lbjChild = lbjItem.child || [];
          var fqylbjData = lbjChild.filter(fqylbjItem => fqylbjItem.id === `jcact-rt-${paneId}-88`);
          var fqylbjChildData = fqylbjData.length ? fqylbjData[0].child : [];
          fqylbjChildData.map(fqylbjItem => {
            var fqylbjId = fqylbjItem.id.replace(`jcact-rt-${paneId}-0-`, '');
            addFqylbz(paneId,fqylbjId)
          })
        })
      })
    }
  }else {
    addBzHandler();
  }
}

// 添加病灶,oldPaneId已保存过的
function addBzHandler(oldPaneId) {
  $('#jcact1 .jca-bz .bz-wrap').show();
  var paneId = oldPaneId || createUUidFun();
  var activeTab = 'jcact-rt-' + paneId;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  var {newPaneBlock} = appendBzHtml(paneId, oldIdAndDom);
  var bzLen = $('#jcact1 .jca-bz .bz-tit-ls .bz-tit-i').length;
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '病灶',
      name: '病灶title',
      pid: 'jcact-bz-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : '病灶' +(bzLen+1),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('#jcact1 .jca-bz .bz-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
    document.querySelector("#jcact1 .jca-bz .bz-wrap .bz-tit-ls").scrollLeft = document.querySelector("#jcact1 .jca-bz .bz-wrap .bz-tit-ls").scrollWidth;
  }
}

// 病灶的交互
function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    // newPaneBlock.find('.def-ck').click();
  }
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);
  // 获取推导结果
  curElem.find('#jcact1 .rpt-con .rt-sr-w').change(function() {
    getImpressionText();
  })
}

// 处理新增病灶的html
function appendBzHtml(paneId) {
  $('#jcact1 .jca-bz .bz-tit-ls .bz-tit-i').removeClass('act');
  $('#jcact1 .jca-bz .bz-con .bz-item').removeClass('act');
  var reg = new RegExp('000', 'ig');
  var title = bzTitleClone.replace(reg, paneId);   //作为病灶示例模板的title
  var content = bzConClone.replace(reg, paneId);  //作为病灶示例模板具体内容
  $('#jcact1 .jca-bz .bz-tit-ls').append(title);
  $('#jcact1 .jca-bz .bz-con').append(content);
  var newPaneBlock = $('#jcact1 .jca-bz .bz-item[tab-target="jcact-rt-'+paneId+'"]');
  setBzTitleNo(paneId);
  return {newPaneBlock};
}

// 设置病灶标题序号
function setBzTitleNo(paneId) {
  var bzTitleDom = $('#jcact1 .jca-bz .bz-tit-ls .bz-tit-i');
  bzTitleDom.each(function(i,dom) {
    var tabId = $(dom).attr('tab-id');
    var titleIndex = i + 1;
    if(tabId === 'jcact-rt-'+paneId) {
      $('#jcact1 .jca-bz .bz-tit-ls .bz-tit-i[tab-id="jcact-rt-'+paneId+'"] .bz-name').html('病灶'+titleIndex);
    }
  })
}

// 删除病灶
var willBzVm = null;
function delTab(vm, paneId) {
  willBzVm = vm;
  var idx = $('#jcact1 .jca-bz .bz-tit-i .close-icon').index(vm);
  var dialogContent = `<div style="">确认删除病灶${idx+1}?</div>`;
  dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  dialogContent += '<button onclick="removeBzHandler(\''+paneId+'\')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  dialogContent += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消</button>';
  dialogContent += '</div>';
  drawDialog({
    title: '提示',
    content: dialogContent,
    modal: true
  })
}

// 确认删除病灶
function removeBzHandler(paneId) {
  var vm = willBzVm;
  var idx = $('#jcact1 .jca-bz .bz-tit-i .close-icon').index(vm);
  var isAct = $(vm).closest('.bz-tit-i').hasClass('act');
  $('#jcact1 .jca-bz .bz-tit-i:eq('+idx+')').remove();
  $('#jcact1 .jca-bz .bz-item:eq('+idx+')').remove();
  var resetBzLen = $('.jca-bz .bz-item').length;
  if(resetBzLen > 0 && isAct) {
    let nextId = idx >= 1 ? idx - 1 : idx;
    $('#jcact1 .jca-bz .bz-tit-i:eq('+nextId+')').addClass('act');
    $('#jcact1 .jca-bz .bz-item:eq('+nextId+')').addClass('act');
  }
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
  removeRtDialog();
  getImpressionText();
  if(resetBzLen > 0) {
    $('.jca-bz .bz-name').each(function(i, dom) {
      $(dom).text('病灶' + (i+1));
    });
    $('#jcact1 .jca-bz .bz-wrap').show();
  } else {
    $('#jcact1 .jca-bz .bz-wrap').hide();
  }
}

// 复制暂存病灶的具体模板示例内容，为后续添加病灶做准备
function initFqylbzCloneEle(tabId) {
  var fqylbzCon = curElem.find('#jcact1 .jca-bz .bz-item[tab-target="jcact-rt-'+tabId+'"] .fqylbz-block .fqylbz-item').clone(true);  //作为非区域淋巴组内容
  var fqylbzConClone = '<div class="highlight-block fqylbz-item">'+fqylbzCon.html()+'</div>';
  return fqylbzConClone;
}

// 添加非区域淋巴组
function addFqylbz(tabId,oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var activeTab = 'jcact-rt-' + tabId + '-0-' + paneId;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  var {newPaneBlock} = appendFqylbzHtml(tabId, paneId, oldIdAndDom);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '非区域淋巴组',
      name: '非区域淋巴组title',
      pid: 'jcact-rt-' + tabId + '-88',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : '',
      wt: '',
      vt: '',
    }
    var wCon = $('#jcact1 .jca-bz .bz-item[tab-target="jcact-rt-'+tabId+'"] .fqylbz-block .fqylbz-item:not(:first-child').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
    $('.fqylbz-block').show();
  }
}

// 处理新增非区域淋巴组的html
function appendFqylbzHtml(tabId, paneId, oldIdAndDom) {
  var fqylbzConClone = initFqylbzCloneEle(tabId);
  var regCon = new RegExp('123', 'ig');
  var content = fqylbzConClone.replace(regCon, paneId);
  $('#jcact1 .jca-bz .bz-item[tab-target="jcact-rt-'+tabId+'"] .fqylbz-block').append(content);
  var newPaneBlock = $('#jcact1 .jca-bz .bz-item[tab-target="jcact-rt-'+tabId+'"] .fqylbz-block');
  return {newPaneBlock};
}

// 删除非区域淋巴组
function delFqylbz(vm) {
  $(vm).closest(".fqylbz-item").remove();
  getImpressionText();
}

// 获取病灶内容
function getBzInfoCon(type) {
  var strArr = [];
  var bzInfoList = [];
  $('.bz-wrap .bz-tit-ls .bz-tit-i').each(function(i,dom) {
    var bzItem = $(dom);
    var bzId = bzItem.attr('tab-id');
    var key = bzId.split('-')[2];
    var obj = {
      bzId: bzId,
      zlwz: getVal(`[name="zlwz-site${key}"]:checked`), // 肿瘤位置
      zlOrgan: getVal(`[name="zlwz-organ${key}"]:checked`), // 肿瘤部位
      zlxzVal: getVal(`[name="zlxz${key}"]:checked`),// 肿瘤形状
      dxclVal: getVal(`[name="zldxcl${key}"]:checked`), // 大小测量
      zkdxInp1: getVal(`[id="jcact-rt-${key}-21"]`), // 肿块大小输入框1
      zkdxInp2: getVal(`[id="jcact-rt-${key}-22"]`), // 肿块大小输入框2
      zlzhcmInp: getVal(`[id="jcact-rt-${key}-24"]`), // 肿瘤最厚层面输入框
      ljcdInp: getVal(`[id="jcact-rt-${key}-26"]`), // 累及长度输入框
      ycbgxVal: getVal(`[name="ycbgx${key}"]:checked`), // 与肠壁关系
      nbqkVal: getVal(`[name="nbqk${key}"]:checked`), // 内壁情况
      szqkVal: getVal(`[name="szqk${key}"]:checked`), // 生长情况
      jbcqbzVal: getVal(`[name="jbcqbz${key}"]:checked`), // 局部肠腔变窄
      psmdVal: getVal(`[name="psmd${key}"]:checked`), // 平扫密度
      zqdmqmdVal: getVal(`[name="zqdmqmd${key}"]:checked`), // 增强动脉期密度
      zqjmqmdVal: getVal(`[name="zqjmqmd${key}"]:checked`), // 增强静脉期密度
      zqycmdVal: getVal(`[name="zqycqmd${key}"]:checked`), // 增强延迟期密度
      zlfqTVal: getVal(`[name="zlfq-t${key}"]:checked`), // T期
      zlfqT3: getVal(`[name="zlfq-t3${key}"]:checked`), // T3
      t4bInpVal: getVal(`[id="jcact-rt-${key}-73"]`), // T4b输入框
      qylbzVal: getVal(`[name="qylbz${key}"]:checked`),// 区域淋巴结
      jcxmpCk: getVal(`[id="jcact-rt-${key}-76"]:checked`), // 结肠系膜旁LN——复选框
      jcxmpNum: getVal(`[id="jcact-rt-${key}-77"]`),// 结肠系膜旁LN——数量
      jcxmpMaxDj: getVal(`[id="jcact-rt-${key}-78"]`),// 结肠系膜旁LN——最大短径
      jcxmpXz: getVal(`[id="jcact-rt-${key}-79"] option:not(:first-child):selected`),// 结肠系膜旁LN——形状
      jcxmpBy: getVal(`[id="jcact-rt-${key}-80"] option:not(:first-child):selected`),// 结肠系膜旁LN——边缘
      jcxmpZqsm: getVal(`[id="jcact-rt-${key}-81"] option:not(:first-child):selected`),// 结肠系膜旁LN——增强扫描
      xcxmCk: getVal(`[id="jcact-rt-${key}-82"]:checked`), // 小肠系膜LN——复选框
      xcxmNum: getVal(`[id="jcact-rt-${key}-83"]`),// 小肠系膜LN——数量
      xcxmMaxDj: getVal(`[id="jcact-rt-${key}-84"]`),// 小肠系膜LN——最大短径
      xcxmXz: getVal(`[id="jcact-rt-${key}-85"] option:not(:first-child):selected`),// 小肠系膜LN——形状
      xcxmBy: getVal(`[id="jcact-rt-${key}-86"] option:not(:first-child):selected`),// 小肠系膜LN——边缘
      xcxmZqsm: getVal(`[id="jcact-rt-${key}-87"] option:not(:first-child):selected`),// 小肠系膜LN——增强扫描
      fqylbjList: [], // 非区域淋巴组
      fxajjVal: getVal(`[name="fzajj${key}"]:checked`), // 发现癌结节-复选框
      fxajjNum: getVal(`[id="jcact-rt-${key}-102"]`), // 发现癌结节-数量
      fxajjMaxDj: getVal(`[id="jcact-rt-${key}-104"]`), // 发现癌结节-最大短径
      emviVal: getVal(`[name="emvi${key}"]:checked`), // EMVI
      emviInp: getVal(`[id="jcact-rt-${key}-108"]`), // EMVI阳性输入框
    }
    // 描述第一段
    var zlInfoStr = '', zlInfoArr = [], mdArr = [], zlInfoAndMdArr = [];
    zlInfoStr += obj.zlwz ? obj.zlwz + '结肠' : '';
    zlInfoStr += obj.zlOrgan ? '（' + obj.zlOrgan + '）' : '';
    zlInfoStr ? zlInfoArr.push(zlInfoStr + '发现一病灶') : '';
    obj.dxclVal ? zlInfoArr.push('病灶为' + obj.dxclVal) : '';
    obj.zlxzVal ? zlInfoArr.push('形状呈' + obj.zlxzVal) : '';
    // 肿块大小
    if(obj.zkdxInp1 || obj.zkdxInp2) {
      var zkdxArr = [];
      obj.zkdxInp1 ? zkdxArr.push(obj.zkdxInp1 + 'mm') : '';
      obj.zkdxInp2 ? zkdxArr.push(obj.zkdxInp2 + 'mm') : '';
      zkdxArr.length ? zlInfoArr.push('大小：' + zkdxArr.join('x')) : '';
    }
    // 肿瘤最厚层面
    obj.zlzhcmInp ? zlInfoArr.push('病灶最厚层面：' + obj.zlzhcmInp + 'mm') : '';
    // 累及长度
    obj.ljcdInp ?  zlInfoArr.push('累及长度' + obj.ljcdInp + 'mm') : '';
    obj.ycbgxVal ?  zlInfoArr.push('累及' + obj.ycbgxVal + '肠壁') : '';
    obj.nbqkVal ?  zlInfoArr.push('内壁' + obj.nbqkVal) : '';
    obj.szqkVal ?  zlInfoArr.push(obj.szqkVal) : '';
    (obj.jbcqbzVal && obj.jbcqbzVal === '是') ? zlInfoArr.push('局部肠腔变窄') : '';
    obj.psmdVal ? mdArr.push('病灶平扫呈' + obj.psmdVal) : '';
    obj.zqdmqmdVal ? mdArr.push('增强动脉期呈' + obj.zqdmqmdVal) : '';
    obj.zqjmqmdVal ? mdArr.push('增强静脉期呈' + obj.zqjmqmdVal) : '';
    obj.zqycmdVal ? mdArr.push('增强延迟期呈' + obj.zqycmdVal) : '';

    zlInfoArr.length ? zlInfoAndMdArr.push(zlInfoArr) : '';
    mdArr.length ? zlInfoAndMdArr.push(mdArr) : '';
    zlInfoAndMdArr.length ? strArr.push('　　' + zlInfoAndMdArr.join('。')) : '';

    // 描述第二段-T分期（肿瘤分期）
    var zlfqArr = [];
    if(obj.zlfqTVal === '侵犯黏膜下层') {
      zlfqArr.push('T1期：病灶' + obj.zlfqTVal);
    }else if(obj.zlfqTVal === '侵犯固有肌层但未穿透固有肌层') {
      zlfqArr.push('T2期：病灶' + obj.zlfqTVal);
    }else if(obj.zlfqTVal === 'T3') {
      if(obj.zlfqT3) {
        zlfqArr.push('T3期：病灶' + obj.zlfqT3);
      }
    }else if(obj.zlfqTVal === '穿透腹膜脏层') {
      T = 'T4a';
      zlfqArr.push('T4a期：病灶' + obj.zlfqTVal);
    }else if(obj.zlfqTVal === '侵犯邻近脏器') {
      let t4bInpStr = obj.t4bInpVal ? '，' + obj.t4bInpVal : '';
      zlfqArr.push('T4b期：病灶' + obj.zlfqTVal + t4bInpStr);
    }
    zlfqArr.length ? strArr.push('　　' + zlfqArr) : '';

    // 描述第三段-N淋巴结
    let jcxmpArr = [], xcxmArr = [], qylbzArr = [], lbjStr = '';
    if(!isNaN(Number(obj.jcxmpNum)) || !isNaN(Number(obj.xcxmNum))) {
      var qylbjNum = Number(obj.jcxmpNum) + Number(obj.xcxmNum);
      var kylbjStr = qylbjNum ? qylbjNum + '枚可疑淋巴结转移，' : '';
      if(obj.jcxmpNum) {
        obj.jcxmpCk && jcxmpArr.push(obj.jcxmpCk);
        obj.jcxmpNum && jcxmpArr.push(obj.jcxmpNum + '枚');
        obj.jcxmpMaxDj && jcxmpArr.push('最大短径' + obj.jcxmpMaxDj + 'mm');
        obj.jcxmpXz && jcxmpArr.push('形状呈' + obj.jcxmpXz);
        obj.jcxmpBy && jcxmpArr.push('边缘' + obj.jcxmpBy);
        obj.jcxmpZqsm && jcxmpArr.push('增强扫描' + obj.jcxmpZqsm);
      }
      if(obj.xcxmNum) {
        obj.xcxmCk && xcxmArr.push(obj.xcxmCk);
        obj.xcxmNum && xcxmArr.push(obj.xcxmNum + '枚');
        obj.xcxmMaxDj && xcxmArr.push('最大短径' + obj.xcxmMaxDj + 'mm');
        obj.xcxmXz && xcxmArr.push('形状呈' + obj.xcxmXz);
        obj.xcxmBy && xcxmArr.push('边缘' + obj.xcxmBy);
        obj.xcxmZqsm && xcxmArr.push('增强扫描' + obj.xcxmZqsm);
      }
    }
    // 非区域淋巴结
    var fqylbzAllArr = [];
    $('#jcact1 .jca-bz .bz-item[tab-target="'+bzId+'"] .fqylbz-block .fqylbz-item:not(:first-child').each(function(fqylbzIdx,fqylbzEle) {
      var fqylbzId = $(fqylbzEle).find('[type="hidden"]').attr('id');
      var fqylbzKey = fqylbzId.split('-')[4];
      var fqylbzArr = [];
      var fqylbjObj = {
        fqylbzSite: getVal(`[name="fqylbz-site${key}-${fqylbzKey}"]:checked`), // 位置单选框
        fqylbzOrgan: getVal(`[id="jcact-rt-${key}-3-${fqylbzKey}"] option:not(:first-child):selected`), // 部位下拉框
        fqylbzNum: getVal(`[id="jcact-rt-${key}-4-${fqylbzKey}"]`), // 数量
        fqylbzMaxDj: getVal(`[id="jcact-rt-${key}-5-${fqylbzKey}"]`), // 最大短径
        fqylbzXz: getVal(`[id="jcact-rt-${key}-6-${fqylbzKey}"] option:not(:first-child):selected`), // 形状
        fqylbzBy: getVal(`[id="jcact-rt-${key}-7-${fqylbzKey}"] option:not(:first-child):selected`), // 边缘
        fqylbzZqsm: getVal(`[id="jcact-rt-${key}-8-${fqylbzKey}"] option:not(:first-child):selected`), // 增强扫描
      }
      obj.fqylbjList.push(fqylbjObj);

      if(fqylbjObj.fqylbzOrgan) {
        fqylbjObj.fqylbzOrgan && fqylbzArr.push(fqylbjObj.fqylbzSite + fqylbjObj.fqylbzOrgan);
        fqylbjObj.fqylbzNum && fqylbzArr.push(fqylbjObj.fqylbzNum + '枚');
        fqylbjObj.fqylbzMaxDj && fqylbzArr.push('最大短径' + fqylbjObj.fqylbzMaxDj + 'mm');
        fqylbjObj.fqylbzXz && fqylbzArr.push('形状呈' + fqylbjObj.fqylbzXz);
        fqylbjObj.fqylbzBy && fqylbzArr.push('边缘' + fqylbjObj.fqylbzBy);
        fqylbjObj.fqylbzZqsm && fqylbzArr.push('增强扫描' + fqylbjObj.fqylbzZqsm);
      }
      fqylbzArr.length ? fqylbzAllArr.push(fqylbzArr.join('，')) : '';
    })

    jcxmpArr.length ? qylbzArr.push(jcxmpArr.join('，')) : '';
    xcxmArr.length ? qylbzArr.push(xcxmArr.join('，')) : '';
    fqylbzAllArr.length ? qylbzArr.push(fqylbzAllArr.join('；')) : '';
    // 发现癌结节
    var fxajjArr = [];
    obj.fxajjNum && fxajjArr.push('发现' + obj.fxajjNum + '个癌结节');
    obj.fxajjVal && fxajjArr.push('位于' + obj.fxajjVal);
    obj.fxajjMaxDj && fxajjArr.push('最大短径' + obj.fxajjMaxDj + 'mm');
    fxajjArr.length ? qylbzArr.push(fxajjArr.join('，')) : '';

    let wyStr = obj.jcxmpCk && obj.xcxmCk ? '分别位于' : '位于';
    lbjStr = qylbzArr.length  ? '　　' + 'N（淋巴结）：' + kylbjStr +  wyStr + qylbzArr.join('；') : '';
    lbjStr && strArr.push(lbjStr);

    // 描述第四段-EMVI
    let emviMap = {
      '阴性(-)': '阴性',
      '阳性(+)': '阳性'
    };
    if(obj.emviVal) {
      let emviVal = emviMap[obj.emviVal];
      let emviStr =  '　　' + 'E（EMVI,肠壁外血管侵犯）：' + emviVal + (obj.emviInp ? `，${obj.emviInp}` : '');
      strArr.push(emviStr);
    }
    bzInfoList.push(obj);   //整合各个数据
  })

  if(type === 'view') {
    return bzInfoList;
  } else {
    return strArr.length ? (strArr.join('。\n')) : '';
  }
}

// 远处转移
function getYczyCon(type) {
  let str = '', strArr = [];
  let gzCkVal = getVal('[id="jcact-rt-6"]:checked');
  let gzInp = getVal('[id="jcact-rt-7"]');
  let fbCkVal = getVal('[id="jcact-rt-8"]:checked');
  let fbRadVal = getVal('[name="fb-site"]:checked');
  let fbInp = getVal('[id="jcact-rt-12"]');
  let fmzzCkVal = getVal('[id="jcact-rt-13"]:checked');
  let fmzzInp = getVal('[id="jcact-rt-14"]');
  let qtzyCkVal = getVal('[id="jcact-rt-15"]:checked');
  let qtzyInp = getVal('[id="jcact-rt-16"]');
  gzCkVal && strArr.push(gzCkVal + (gzInp ? '（' + gzInp + '）' : ''));
  fbCkVal && strArr.push((fbRadVal ? fbRadVal : fbCkVal ? fbCkVal : '') + (fbInp ? '（' + fbInp + '）' : ''));
  fmzzCkVal && strArr.push(fmzzCkVal + (fmzzInp ? '（' + fmzzInp + '）' : ''));
  qtzyCkVal && strArr.push(qtzyCkVal + (qtzyInp ? '（' + qtzyInp + '）' : ''));
  if(type === 'view') {
    str = strArr.length ? strArr.join('\n') : '-';
  }else {
    str = strArr.length ? '　　' + '远处转移：' + strArr.join('，') : '';
  }
  return str;
}

// 其他征象
function getQtzxCon(type) {
  let str = '', qtzxArr = [], moreQtzxArr = [], strArr = [];
  let zlckVal = getVal('[name="zlck-sf"]:checked');
  let zlckInp = getVal('[id="jcact-rt-21"]');
  let cgzVal = getVal('[name="cgz-sf"]:checked');
  let cgzInp = getVal('[id="jcact-rt-25"]');
  let ctdVal = getVal('[name="ctd-sf"]:checked');
  let ctdInp = getVal('[id="jcact-rt-29"]');
  let fsVal = getVal('[name="fs-sf"]:checked');
  let fsInp = getVal('[id="jcact-rt-33"]');
  let gdqtzxInp = getVal('[id="jcact-rt-34"]');

  if(type === 'view') {
    zlckVal==='是' ? strArr.push('肿瘤穿孔：是' + (zlckInp ? '（' + zlckInp + '）' : '')) : zlckVal==='否' ? strArr.push('肿瘤穿孔：否') : '';
    cgzVal==='是' ? strArr.push('肠梗阻：是' + (cgzInp ? '（' + cgzInp + '）' : '')) : cgzVal==='否' ? strArr.push('肠梗阻：否') : '';
    ctdVal==='是' ? strArr.push('肠套叠：是' + (ctdInp ? '（' + ctdInp + '）' : '')) : ctdVal==='否' ? strArr.push('肠套叠：否') : '';
    fsVal==='是' ? strArr.push('腹水：是' + (fsInp ? '（' + fsInp + '）' : '')) : fsVal==='否' ? strArr.push('腹水：否') : '';
    gdqtzxInp && strArr.push(gdqtzxInp ? gdqtzxInp : '');
  }else {
    zlckVal==='是' && qtzxArr.push('肿瘤穿孔' + (zlckInp ? '（' + zlckInp + '）' : ''));
    cgzVal==='是' && qtzxArr.push('肠梗阻' + (cgzInp ? '（' + cgzInp + '）' : ''));
    ctdVal==='是' && qtzxArr.push('肠套叠' + (ctdInp ? '（' + ctdInp + '）' : ''));
    fsVal==='是' && qtzxArr.push('腹水' + (fsInp ? '（' + fsInp + '）' : ''));
    gdqtzxInp && moreQtzxArr.push(gdqtzxInp ? gdqtzxInp : '');
  }
  if(type === 'view') {
    str = strArr.length ? strArr.join('\n') : '-';
  }else {
    qtzxArr.length ? strArr.push(qtzxArr.join('，')) : '';
    moreQtzxArr.length ? strArr.push(moreQtzxArr) : '';
    str = '　　' + '其他征象：' + (strArr.length ? '可见' + strArr.join('。') : '无') + '。';
  }
  return str;
}

// 获取描述
function getDescContent() {
  var descArr = [];
  // 病灶
  var allBzCon = getBzInfoCon();
  allBzCon && descArr.push(allBzCon);

  // 远处转移
  var yczyCon = getYczyCon();
  yczyCon && descArr.push(yczyCon);

  // 其他征象
  var qtzxCon = getQtzxCon();
  qtzxCon && descArr.push(qtzxCon);

  // console.log(descArr.join('。\n'));
  return descArr.join('。\n');
}

// 推导肿瘤分期
function getJcaLevel() {
  var levelArr = [];
  var T = '', N = 'N0', M = 'M0', levStr = '';
  $('.bz-wrap .bz-tit-ls .bz-tit-i').each(function(i,dom) {
    T = '', N = 'N0', M = 'M0';
    var bzItem = $(dom);
    var bzId = bzItem.attr('tab-id');
    var key = bzId.split('-')[2];
    var obj = {
      bzId: bzId,
      zlwz: getVal(`[name="zlwz-site${key}"]:checked`),
      zlOrgan: getVal(`[name="zlwz-organ${key}"]:checked`),
      zlfqTVal: getVal(`[name="zlfq-t${key}"]:checked`),
      zlfqT3: getVal(`[name="zlfq-t3${key}"]:checked`),
      qylbzVal: getVal(`[name="qylbz${key}"]:checked`),// 区域淋巴结
      jcxmpNum: getVal(`[id="jcact-rt-${key}-77"]`),// 结肠系膜旁LN
      xcxmNum: getVal(`[id="jcact-rt-${key}-83"]`), // 小肠系膜LN
      fqylbjVal: getVal(`[name="fqylbz${key}"]:checked`), // 非区域淋巴结
      fxajjVal: getVal(`[name="fzajj${key}"]:checked`), // 发现癌结节
      emviVal: getVal(`[name="emvi${key}"]:checked`),
    }

    // T分期（肿瘤分期）
    if(obj.zlfqTVal === '侵犯黏膜下层') {
      T = 'T1';
    }else if(obj.zlfqTVal === '侵犯固有肌层但未穿透固有肌层') {
      T = 'T2';
    }else if(obj.zlfqTVal === 'T3') {
      if(obj.zlfqT3) {
        T = 'T3';
      }
    }else if(obj.zlfqTVal === '穿透腹膜脏层') {
      T = 'T4a';
    }else if(obj.zlfqTVal === '侵犯邻近脏器') {
      T = 'T4b';
    }

    // N分期（淋巴结）
    if(obj.qylbzVal) {
      if(!isNaN(Number(obj.jcxmpNum)) || !isNaN(Number(obj.xcxmNum))) {
        var qylbzNum = Number(obj.jcxmpNum) + Number(obj.xcxmNum);
        if(qylbzNum >= 7) {
          N = 'N2b';
        }else if(4 <= qylbzNum && qylbzNum <=6) {
          N = 'N2a';
        }else if(1 <= qylbzNum && qylbzNum <=3) {
          if(obj.fxajjVal) {
            N = 'N1c';
          }else if(2 <= qylbzNum && qylbzNum <=3) {
            N = 'N1b';
          }else if(1 <= qylbzNum) {
            N = 'N1a';
          }
        }
      }
    }else if(obj.fxajjVal) {
      N = 'N1c';
    }
    
    // M分期（远处转移）
    var yczyVal = getVal('[name="yczy"]:checked');
    if(yczyVal.includes('腹膜种植') || yczyVal.includes('其他转移')) {
      M = 'M1c';
    }else if(yczyVal.split('、').length > 1 || (obj.fqylbjVal && yczyVal)) {
      M = 'M1b';
    }else if(yczyVal === '肝脏' || yczyVal === '肺部' || obj.fqylbjVal) {
      M = 'M1a';
    }
    // 肿瘤位置
    var zlwzStr = obj.zlwz + (obj.zlOrgan ? '（' + obj.zlOrgan +'）' : '');
    zlwzStr += zlwzStr ? '结肠癌，' : '';

    // 淋巴结
    var lbjStr = '';
    if(obj.qylbzVal) {
      var qylbzArr = obj.qylbzVal.split('、');
      var resetQylbzArr = qylbzArr.map(item => item + 'LN');
      lbjStr = resetQylbzArr.length ? ('区域淋巴结：' + resetQylbzArr.join('、') + '，') : '';
    }
    lbjStr += getFqylbzImp(bzId);
    lbjStr += obj.fxajjVal ? '癌结节，' : '';

    levStr = `${zlwzStr}${lbjStr}影像分期：ct${T}${N}${M}`;

    // emvi
    if(obj.emviVal === '阴性(-)') {
      levStr += '，EMVI（-）';
    }else if(obj.emviVal === '阳性(+)') {
      levStr += '，EMVI（+）';
    }
    levStr ? levelArr.push(levStr) : '';
  })
  return levelArr.join('；');
}
// 获取印象——非区域淋巴结
function getFqylbzImp(bzId) {
  var fqylbzStr = '';
  if(!$('#jcact1 .jca-bz .bz-item[tab-target="'+bzId+'"] #'+bzId+'-88:checked').length) {
    return fqylbzStr;
  }
  var fqylbzArr = [];
  $('#jcact1 .jca-bz .bz-item[tab-target="'+bzId+'"] .fqylbz-block .fqylbz-item:not(:first-child').each(function(fqylbzIdx,fqylbzEle) {
    var key = bzId.split('-')[2];
    var fqylbzId = $(fqylbzEle).find('[type="hidden"]').attr('id');
    var fqylbzKey = fqylbzId.split('-')[4];
    var fqylbjObj = {
      fqylbzSite: getVal(`[name="fqylbz-site${key}-${fqylbzKey}"]:checked`), // 位置单选框
      fqylbzOrgan: getVal(`[id="jcact-rt-${key}-3-${fqylbzKey}"] option:not(:first-child):selected`), // 部位下拉框
    }

    if(fqylbjObj.fqylbzOrgan) {
      fqylbjObj.fqylbzOrgan && fqylbzArr.push(fqylbjObj.fqylbzSite + fqylbjObj.fqylbzOrgan + 'LN');
    }
  })
  fqylbzStr = fqylbzArr.length ? '非区域淋巴结：' + fqylbzArr.join('、') + '，' : '';
  return fqylbzStr;
}
// 获取印象——远处转移
function getYczyImp() {
  let str = '', strArr = [];
  let gzCkVal = getVal('[id="jcact-rt-6"]:checked');
  let fbCkVal = getVal('[id="jcact-rt-8"]:checked');
  let fbRadVal = getVal('[name="fb-site"]:checked');
  let fmzzCkVal = getVal('[id="jcact-rt-13"]:checked');
  gzCkVal && strArr.push(gzCkVal);
  fbCkVal && strArr.push(fbRadVal ? fbRadVal : fbCkVal ? fbCkVal : '');
  fmzzCkVal && strArr.push(fmzzCkVal);
  str = strArr.length ? '远处转移：' + strArr.join('、') : '';
  return str;
}
// 获取印象——其他征象
function getQtzxImp() {
  // 其他征象
  var qtzxArr = [];
  var zlckVal = getVal('[id="jcact-rt-20"]:checked');
  zlckVal && qtzxArr.push('肿瘤穿孔');
  var cgzVal = getVal('[id="jcact-rt-24"]:checked');
  cgzVal && qtzxArr.push('肠梗阻');
  var ctdVal = getVal('[id="jcact-rt-28"]:checked');
  ctdVal && qtzxArr.push('肠套叠');
  return qtzxArr.length ? '其他征象：' + qtzxArr.join('、') : '';
}
// 获取印象结果
function getImpressionText() {
  var strArr = [];
  // 获取分期等级,其他征象
  var firstArr = [];
  var levelStr = getJcaLevel();
  var yczyStr = getYczyImp();
  var qtzxStr = getQtzxImp();
  var fsVal = getVal('[id="jcact-rt-32"]:checked');
  if(fsVal && levelStr) {
    firstArr.push(`1、${levelStr}`);
  }else {
    levelStr && firstArr.push(`${levelStr}`);
  }
  yczyStr && firstArr.push(yczyStr);
  qtzxStr && firstArr.push(qtzxStr);
  firstArr.length ? strArr.push(firstArr.join('；')) : '';

  // 腹水
  fsVal && strArr.push('2、腹水');

  strArr.push('（分期标准：结肠癌UICC/AJCC分期第8版 / 中国分期2017版）');

  curElem.find('[id="jcact-rt-35"]').val(strArr.join('\n'));
  return strArr.join('\n');
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescContent();
  rtStructure.impression = curElem.find('#jcact-rt-35').val() || '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

// 预览页面回显
function initViewPageCon() {
  var idAndDomMap = rtStructure.idAndDomMap;
  var examInfo = rtStructure.examInfo;
  // 患者信息
  $('.qlx-view .patient-info [data-id]').each(function(i, dom){
    var id = $(dom).attr('data-id');
    $(dom).text(idAndDomMap && idAndDomMap[id] ? (idAndDomMap[id].value || '') : (examInfo[id]) || '');
  })

  // 病灶列表
  getViewBzList('view');

  // 远处转移
  var yczyText = getYczyCon('view');
  $('.jcact-view .yczy .rpt-desc').html(yczyText);

  // 其他征象
  var qtzxText = getQtzxCon('view');
  $('.jcact-view .qtzx .rpt-desc').html(qtzxText);

  // 印象
  $('.jcact-view .bt-imp').html(idAndDomMap['jcact-rt-35']?.value || '');
}

// 预览页面，病灶内容
function getViewBzList(type) {
  var bzInfoList = getBzInfoCon(type);
  var index = 1;
  if(bzInfoList && bzInfoList.length) {
    for(var item of bzInfoList) {
      var cloneBzlb = $('.jcact-view .bzlb:first').clone(true);
      cloneBzlb.find('.bz-name').hide();
      if(bzInfoList.length > 1) {
        var bzName = '病灶' + index++ + '：';
        cloneBzlb.find('.bz-name').show();
        cloneBzlb.find('.bz-name').html(bzName);
        cloneBzlb.find('.bzlb-con').addClass('bor-gray');
      }
      var zlwzStr = item.zlwz + (item.zlOrgan ? '（' + item.zlOrgan +'）' : '');
      cloneBzlb.find('.zlwz .rpt-desc').html(zlwzStr || '-');
      cloneBzlb.find('.xz .rpt-desc').html(item.zlxzVal || '-');

      // 大小测量
      var zkdxStr = item.dxclVal || '';
      // zlzhcmInp: getVal(`[id="jcact-rt-${key}-24"]`), // 肿瘤最厚层面输入框
      // ljcdInp: getVal(`[id="jcact-rt-${key}-26"]`), // 累及长度输入框
      if(zkdxStr === '肿块型' && (item.zkdxInp1 || item.zkdxInp2)) {
        var zkdxArr = [];
        item.zkdxInp1 ? zkdxArr.push(item.zkdxInp1 + 'mm') : '';
        item.zkdxInp2 ? zkdxArr.push(item.zkdxInp2 + 'mm') : '';
        zkdxStr += zkdxArr.length ? '，肿块大小：' + zkdxArr.join('x') : '';
      }
      if(zkdxStr === '肠壁浸润型' && (item.zlzhcmInp || item.ljcdInp)) {
        var zkdxArr = [];
        item.zlzhcmInp ? zkdxArr.push('病灶最厚层面：' + item.zlzhcmInp + 'mm') : '';
        item.ljcdInp ? zkdxArr.push('累及长度：' + item.ljcdInp + 'mm') : '';
        zkdxStr += zkdxArr.length ? '，' + zkdxArr.join('，') : '';
      }
      cloneBzlb.find('.dxcl .rpt-desc').html(zkdxStr || '-');
      cloneBzlb.find('.ycbgx .rpt-desc').html(item.ycbgxVal || '-');
      cloneBzlb.find('.nbqk .rpt-desc').html(item.nbqkVal || '-');
      cloneBzlb.find('.szqk .rpt-desc').html(item.szqkVal || '-');
      cloneBzlb.find('.jbcqz .rpt-desc').html(item.jbcqbzVal || '-');

      // 密度
      var mdText = '';
      mdText += `<div>平扫密度：${item.psmdVal || '-'}</div>`;
      mdText += `<div>增强动脉期密度：${item.zqdmqmdVal || '-'}</div>`;
      mdText += `<div>增强静脉期密度：${item.zqjmqmdVal || '-'}</div>`;
      mdText += `<div>增强延迟期密度：${item.zqycmdVal || '-'}</div>`;
      cloneBzlb.find('.md .rpt-desc').html(mdText);

      // 肿瘤分期
      var zlfqStr = '';
      if(item.zlfqTVal === '侵犯黏膜下层') {
        zlfqStr = 'T1：' + item.zlfqTVal;
      }else if(item.zlfqTVal === '侵犯固有肌层但未穿透固有肌层') {
        zlfqStr = 'T2：' + item.zlfqTVal;
      }else if(item.zlfqTVal === 'T3') {
        if(item.zlfqT3) {
          zlfqStr = 'T3：' + item.zlfqT3;
        }
      }else if(item.zlfqTVal === '穿透腹膜脏层') {
        zlfqStr = 'T4:T4a：' + item.zlfqTVal;
      }else if(item.zlfqTVal === '侵犯邻近脏器') {
        let t4bInpStr = item.t4bInpVal ? '，' + item.t4bInpVal : '';
        zlfqStr = 'T4:T4b：' + item.zlfqTVal + t4bInpStr;
      }
      cloneBzlb.find('.zlfq .rpt-desc').html(zlfqStr);

      // 淋巴结
      var jcxmpArr = [], xcxmArr = [], qylbzArr = [], lbjStr = '';
      if(!isNaN(Number(item.jcxmpNum)) || !isNaN(Number(item.xcxmNum))) {
        if(item.jcxmpNum) {
          item.jcxmpCk && jcxmpArr.push(item.jcxmpCk);
          item.jcxmpNum && jcxmpArr.push(item.jcxmpNum + '枚');
          item.jcxmpMaxDj && jcxmpArr.push('最大短径：' + item.jcxmpMaxDj + 'mm');
          item.jcxmpXz && jcxmpArr.push('形状：' + item.jcxmpXz);
          item.jcxmpBy && jcxmpArr.push('边缘：' + item.jcxmpBy);
          item.jcxmpZqsm && jcxmpArr.push('增强扫描：' + item.jcxmpZqsm);
        }
        if(item.xcxmNum) {
          item.xcxmCk && xcxmArr.push(item.xcxmCk);
          item.xcxmNum && xcxmArr.push(item.xcxmNum + '枚');
          item.xcxmMaxDj && xcxmArr.push('最大短径：' + item.xcxmMaxDj + 'mm');
          item.xcxmXz && xcxmArr.push('形状：' + item.xcxmXz);
          item.xcxmBy && xcxmArr.push('边缘：' + item.xcxmBy);
          item.xcxmZqsm && xcxmArr.push('增强扫描：' + item.xcxmZqsm);
        }
      }
      // 非区域淋巴结
      var fqylbzAllArr = [];
      for(var fqylbjObj of item.fqylbjList) {
        var fqylbzArr = [];
        if(fqylbjObj.fqylbzOrgan) {
          fqylbjObj.fqylbzOrgan && fqylbzArr.push(fqylbjObj.fqylbzSite + fqylbjObj.fqylbzOrgan);
          fqylbjObj.fqylbzNum && fqylbzArr.push(fqylbjObj.fqylbzNum + '枚');
          fqylbjObj.fqylbzMaxDj && fqylbzArr.push('最大短径：' + fqylbjObj.fqylbzMaxDj + 'mm');
          fqylbjObj.fqylbzXz && fqylbzArr.push('形状：' + fqylbjObj.fqylbzXz);
          fqylbjObj.fqylbzBy && fqylbzArr.push('边缘：' + fqylbjObj.fqylbzBy);
          fqylbjObj.fqylbzZqsm && fqylbzArr.push('增强扫描：' + fqylbjObj.fqylbzZqsm);
        }
        fqylbzArr.length ? fqylbzAllArr.push(fqylbzArr.join('，')) : '';
      }
      
      jcxmpArr.length ? qylbzArr.push(jcxmpArr.join('，')) : '';
      xcxmArr.length ? qylbzArr.push(xcxmArr.join('，')) : '';
      fqylbzAllArr.length ? qylbzArr.push(fqylbzAllArr.join('；\n')) : '';
      var lbjStr = qylbzArr.length ? qylbzArr.join('；\n') + '；' : '';
      cloneBzlb.find('.lbj .rpt-desc').html(lbjStr || '-');

      // 发现癌结节
      var fxajjArr = [];
      item.fxajjNum && fxajjArr.push(item.fxajjNum + '个');
      item.fxajjVal && fxajjArr.push('位于' + item.fxajjVal);
      item.fxajjMaxDj && fxajjArr.push('最大短径：' + item.fxajjMaxDj + 'mm');
      if(fxajjArr.length) {
        cloneBzlb.find('.n1c-lbj').css('display','flex');
        cloneBzlb.find('.n1c-lbj .rpt-desc').html(fxajjArr.join('，'));
      }else {
        cloneBzlb.find('.n1c-lbj').hide();
      }

      var emviMap = {
        '阴性(-)': '阴性',
        '阳性(+)': '阳性'
      };
      var emviStr = '-';
      if(item.emviVal) {
        emviStr = emviMap[item.emviVal] + (item.emviInp ? ('，' + item.emviInp) : '');
      }
      cloneBzlb.find('.emvi .rpt-desc').html(emviStr);

      cloneBzlb.css('display','flex');
      $('.jcact-view .bzlb:last').after(cloneBzlb);
    }
  }
}