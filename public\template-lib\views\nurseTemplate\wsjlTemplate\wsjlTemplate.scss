#wsjl1 {
  font-size: 14px;
  padding: 16px;
  color: #303133;
  .add-btn{
    color: #1885F2;
    font-weight: normal;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
  }
  .page-card {
    padding: 12px 16px;
    border-radius: 4px;
    border: 1px solid #EBEEF5;
    background: #FAFAFA;
  }
  .page-sub-title {
    margin-bottom: 8px;
    color: #E68A2E;
    font-weight: 600;
  }
  .page-label{
    padding-left: 8px;
    border-left: 2px solid #1885F2;
    font-weight: 600;
    color: #303133;
    font-size: 16px;
    margin-bottom: 10px;
  }
  .page-content {
    padding-left: 10px;
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    row-gap:12px;
    padding-right: 16px;
  }
  .bump {
    display: flex;
    border: 1px solid #C0C4CC;
    border-radius: 4px;
    box-sizing: border-box;
    align-items: center;
    input {
      flex:1;
      border: none;
      padding:0 12px;
      text-align: center;
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
    }
    
    .bump-unit {
      display: inline-block;
      background-color: #F5F7FA;
      border-left: 1px solid #C0C4CC;
      padding: 6px 8px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
  .sub-label {
    color: #606266;
    text-align: right;
  }
}
.rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  width: 100%;
  padding: 4px;

}
.rt-sr-label {
  margin-right: 8px;
}

.rt-sr-r {
  margin-right: 4px;
}

.mt-12 {
  margin-top: 12px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-6 {
  margin-top: 6px;
}
.m-0-6 {
  margin: 0 6px;
}

.w-10 {
  width: 10px;
}
.w-15 {
  width: 15px;
}

.w-20 {
  width: 20px;
}
.w-25 {
  width: 25px;
}

.w-30 {
  width: 30px;
}
.w-35 {
  width: 35px;
}

.w-40 {
  width: 40px;
}
.w-45 {
  width: 45px;
}
.w-50 {
  width: 50px;
}
.w-55 {
  width: 55px;
}
.w-60 {
  width: 60px;
}
.w-65 {
  width: 65px;
}
.w-70 {
  width: 70px;
}
.w-75 {
  width: 75px;
}
.w-80 {
  width: 80px;
}
.w-85 {
  width: 85px;
}
.w-90 {
  width: 90px;
}
.w-95 {
  width: 95px;
}
.w-100 {
  width: 100px;
}
.w-105 {
  width: 106px;
}
.w-110 {
  width: 110px;
}
.w-115 {
  width: 115px;
}
.w-120 {
  width: 120px;
}
.w-125 {
  width: 125px;
}
.w-130 {
  width: 130px;
}
.w-135 {
  width: 135px;
}
.w-140 {
  width: 140px;
}
.f-1 {
  flex: 1;
}

.fw-600 {
  font-weight: 600;
}

.a-center {
  display: flex;
  align-items: center;
}

.a-start {
  display: flex;
  align-items: flex-start;
}

.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.radio-buttons {
  display: flex;
}

  /* 隐藏原生单选框 */
  .radio-buttons input[type="radio"] {
    display: none;
  }
  
  .radio-buttons label {
    flex: 1;
    
    
  }
  /* 定义按钮样式 */
  .radio-buttons label span {
    display: flex;
    width: 100%;
    background-color: #FFFFFF;
    border-radius: 0;
    padding: 8px 0;
    margin-right: -1px; /* 这里使用负值使按钮相连 */
    cursor: pointer;
    transition: all 0.2s ease;
    justify-content: center;
    border: 1px solid #C0C4CC;
  }
  
  /* 当对应的单选框被选中时改变按钮样式 */
  .radio-buttons input[type="radio"]:checked + span{
    background-color: #ECF5FF ;
    color: #1885F2;
    border-color: #1885F2 ;
  }
  
  /* 鼠标悬停效果 */
  .radio-buttons label:hover {
    background-color: #ECF5FF;
  }
  
  /* 添加左右边框圆角 */
  .radio-buttons label:first-child  span{
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  
  // .radio-buttons label:last-child  span{
  //   border-top-right-radius: 4px;
  //   border-bottom-right-radius: 4px;
  //   margin-right: 0; /* 最后一个按钮不需要负margin */
  //   border-left-color: #1885F2;
  // }
  
  /* 避免中间按钮的右边框显示 */
  .radio-buttons label:not(:last-child) {
    border-right: none;
  }
  .bt {
    border-top: 1px solid #DCDFE6;
  }
  .card {
    border: 1px solid #DCDFE6;
    padding-bottom: 12px;
    .card-header {
      height: 36px;
      font-size: 16px;
      color: #303133;
      font-weight: 600;
      line-height: 36px;
      padding-left: 16px;
      border-bottom: 1px solid #DCDFE6;
      background: #ECF5FF;
    }
    .card-content {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      padding: 12px 16px;
      .agent-line {
        column-gap: 8px;
      }
      .agent-info {
       flex: 1;
      }
    }
   
  }
  input[readonly],input[disabled]{
    background-color: #F2F6FC !important;
  }
  .cg-6 {
    column-gap: 6px;
  }
  .custom-select {
    position: relative;
    
    &::after{
      display: inline-block;
      position: absolute;
      top: 50%;
      right: 4px;
      content: '';
      width: 8px;
      height: 8px;
      border-left: 1px solid #999;
      border-bottom:1px solid #999;
      transform: rotate(-45deg) translateY(-100%);
    }
  
  }
 