$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;
var rtStructure = null;
var allResData = [];
var vueComp = null;
var curBzImageStatus = '';  //结节图状态，正常zc,增大zd,缩小sx
var curBzImageStatusMap = {'zc':0,'sx':1,'zd':2};   //结节图标索引
var hasZzyBz = false;   //判断是否存在锥状叶，有则使用正常的锥状叶图
var jzxInfo = {}  //甲状腺基本情况
var jzxShInfo = {}  //甲状腺术后
var lbjInfo = {}  //颈部淋巴结
var canvasLbEle = null;
var lbjImage = null;
var lbjCtx = null;
// var firstInImp = true;
// 错误反馈回调处理
function errorCallBack(id) {
  var detailDom = $('[id="'+id+'"]').parents('.mb-detail');
  if(detailDom && detailDom.length) {
    if(!detailDom.is(":visible")) {
      var pid = detailDom.attr('data-pid');
      detailDom.show().siblings('.mb-detail').hide();
      $('[id="'+pid+'"]').addClass('act').siblings().removeClass('act');
    }
  }
  var curDom = $('[id="'+id+'"]').parents('[role="tabpanel"]');
  if(curDom && curDom.length) {
    var key = curDom.attr('id').replace('pane-', '');
    vueComp.activeTab = key;
  }
}
function initVueComp() {
  vueComp = new Vue({
		el: '#gdseJzxVue',
		data() {
			return {
        activeTab: 'jzx-0000.001',
        bzPaneList: [],  //模板内结节tabPane
        jzxPointList: [],  //结节点
        bzDetailList: [],  //结节详情
			}
		},
    computed: {
    },
		methods: {
      // 删除结节
      delTab(idx, paneId) {
        this.$confirm('确认删除该结节?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let delId = 'jzx-0000.' + paneId;
          this.bzPaneList.splice(idx, 1);
          if(this.bzPaneList.length > 0 && this.activeTab === delId) {
            let nextId = idx >= 1 ? idx - 1 : idx;
            this.activeTab = 'jzx-0000.' + this.bzPaneList[nextId].paneId;
          }
          this.$nextTick(() => {
            if(rtStructure && rtStructure.idAndDomMap) {
              for(var key in rtStructure.idAndDomMap) {
                if(key.indexOf(paneId) > -1) {
                  delete rtStructure.idAndDomMap[key];
                }
              }
            }
            getBzDetailInfo();
          })
        })
      },

      // 添加结节,oldPaneId已保存过的
      addBzHandler(oldPaneId) {
        var paneId = oldPaneId || createUUidFun();
        this.bzPaneList.push({
          paneId: paneId
        })
        let activeTab = 'jzx-0000.' + paneId;
        this.activeTab = activeTab;
        this.$nextTick(() => {
          // 兼容导出数据用到的判断
          if(rtStructure) {
            if(rtStructure.idAndDomMap['']) {
              delete rtStructure.idAndDomMap[''];
            }
            var newPaneBlock = curElem.find('.rt-pane:eq('+(this.bzPaneList.length-1)+')');
            var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
            rtStructure.idAndDomMap[activeTab] = {
              id: activeTab,
              desc: '结节',
              name: '结节title',
              pid: 'jzx-0000',
              pvf: '',
              req: '1',
              rtScPageNo: 1,
              value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('结节' + (this.bzPaneList.length)),
              wt: '',
              vt: '',
              itemList: oldIdAndDom.itemList,
              lastVal: oldIdAndDom.lastVal
            }
            var wCon = curElem.find("[rt-sc]");
            wCon.each(function(wIndex, wItem) {
              var node = $(wItem);
              var id = node.attr('id') || '';
              var pid = node.attr('pid') || '';
              var groupId = node.attr('name') || '';
              var sc = node.attr('rt-sc');
              var scArr = sc.split(';');
              var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
              var scObj = {
                id: id,
                pid: pid,
                rtScPageNo: 1,
                value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
                itemList: childOldIdAndDom.itemList,
                lastVal: childOldIdAndDom.lastVal
              };
              if(groupId) {
                scObj['groupId'] = groupId;
              }
              scArr.forEach(function(scItem) {
                var key = scItem.split(':')[0];
                var value = scItem.split(':')[1];
                if(key) {
                  if(key === 'code') {
                    scObj[key] = value;
                    var findCodeNode = $('[id="'+id+'"].rt-sr-w');
                    findCodeNode.attr('code', value);
                  } else {
                    var numberList = ['left', 'top', 'wt'];
                    scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
                  }
                }
              })
              rtStructure.idAndDomMap[id] = {...scObj};
            })
            rtStructure.init(true);
            rtStructure.initChildDisabled(newPaneBlock[0]);
            if(!oldPaneId) {
              // 默认值
              defValInBzTemp(newPaneBlock);
              document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollLeft = document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollWidth;
              // 默认分数
              // computeScoreHandler(newPaneBlock)
            }
          }
          setTimeout(function() {
            displayBzImage();
            if(!oldPaneId) {
              toggleRadioCheckStatus();
            }
          }, 50)
          getBzDetailInfo(oldPaneId);
          $(".bz-con .rt-sr-w").off('change')
          // 切换结节点位置
          // $(".bz-con .rt-sr-w[pointKey]").change(function() {
          //   setBzPosition();
          // })
          // 切换分数
          $(".bz-con .rt-sr-w[score]").change(function() {
            computeScoreHandler($(this).closest('.t-content'));
          })
          
          $(".bz-con .rt-sr-w:not(.sel-temp)").change(function(){
            if($(this).attr('bzkey') != undefined) {
              displayBzImage();
            }
            getBzDetailInfo();
          })
        })
      }
    }
  })
}

function initHtmlScript(ele) {
  curElem = $(ele);
  initVueComp();
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList);
    if(rtStructure) {
      // 回显结节
      allResData = rtStructure.enterOptions 
        && rtStructure.enterOptions.resultData ? 
        rtStructure.enterOptions.resultData : [];
      if(allResData && allResData.length) {
        var bzData = allResData.filter(bzItem => bzItem.id === 'jzx-0000')
        if(bzData && bzData.length) {
          var bzList = bzData[0].child || [];
          bzList.forEach((item) => {
            var paneId = item.id.replace('jzx-0000.', '');
            vueComp.addBzHandler(paneId, true)
          })
        }
      }
    }
  }
  vueComp.$nextTick(function() {
    // 淋巴的canvas初始化
    if(!canvasLbEle) {
      initCanvas();
    }

    // 模版默认的内容赋值
    initTempPage();
    // 结节数据回显
    if(vueComp.bzPaneList && vueComp.bzPaneList.length) {
      $('[data-pid="jzx-0000"] .rt-sr-w').each(function(i, widget) {
        var id = $(widget).attr('id');
        if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
          rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
        }
      })
    }
    if(window.initPageHandler && typeof window.initPageHandler === 'function') {
      setTimeout(() => {
        window.initPageHandler();
      }, 100)
    }
    toggleRadioCheckStatus();
  })
}

// 描述的内容初始化整体加载一遍
function setDescription(firstIn) {
  getJzxInfo();  //甲状腺基本信息
  getJzxShInfo();   //甲状腺术后
  // 甲状腺结节动态加载的，不在此处渲染
  getLbjInfo(firstIn);  //颈部淋巴结信息
  getImpression(firstIn);  //诊断内容
}

// 初始化模板默认内容
function initTempPage() {
  // 未编辑过
  if(!curElem.attr('data-edited')) {
    curElem.find('.def-ck').prop('checked', true);
    curElem.find('.hide-in').hide();
  }
  
  // 模版类型切换
  toggleMbHandler();
  curElem.find('[data-mtab]').change(function() {
    var mtab = $(this).attr('data-mtab');
    if($(this).is(':checked')) {
      // 甲状腺基本情况 和 甲状腺术后互斥
      if(mtab === 'sejz-rt-6') {
        $('[data-mtab="sejz-rt-7"]').prop('checked', false);
      }
      if(mtab === 'sejz-rt-7') {
        $('[data-mtab="sejz-rt-6"]').prop('checked', false);
      }
      if(mtab === 'jzx-0000') {
        if(!vueComp.bzPaneList.length) {
          vueComp.addBzHandler();
        } else {
          getBzDetailInfo();
        }
      }
    } else {
      if(mtab === 'jzx-0000') {
        getBzDetailInfo();
      }
    }
    displayBzImage();
    toggleMbHandler(mtab);
    setDescription();
  });

  // 标签切换
  $(".mb-hd-i").click(function() {
    var id = $(this).attr('id');
    $(this).addClass('act').siblings().removeClass('act');
    $('[data-pid="'+id+'"]').show().siblings('[data-pid]').hide();
  })
  // 相关联模块显示隐藏
  toggleContectModule();
  curElem.find('[data-show]').change(function() {
    var mName = $(this).attr('data-show');
    toggleContectModule(mName);
  })

  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem);
  initLigthItemChange(curElem);

  // // 甲状腺术后，根据值的情况展示内容
  // jzxSHDisplay();
  
  // 确定用哪张结节图
  displayBzImage();
  curElem.find('[bzkey]').change(function() {
    displayBzImage();
  })

  setDescription(true);
  curElem.find('.mb-detail[data-pid="sejz-rt-6"] .rt-sr-w').change(function() {
    getJzxInfo();
    getImpression();
  })
  curElem.find('.mb-detail[data-pid="sejz-rt-7"] .rt-sr-w').change(function() {
    getJzxShInfo();
    getImpression();
  })
  curElem.find('.mb-detail[data-pid="sejz-rt-9"] .rt-sr-w').change(function() {
    getLbjInfo();
    getImpression();
  })

  // 甲状腺术后，剩余侧大小非正常，填写大小
  var shSizeStatus = curElem.find('.mb-detail[data-pid="sejz-rt-7"] [name="sh-size"]:checked').val();
  // curElem.find('.mb-detail[data-pid="sejz-rt-7"] .sh-size-inp .rt-sr-w').attr('disabled', !shSizeStatus || shSizeStatus === '正常');
  // curElem.find('.mb-detail[data-pid="sejz-rt-7"] [name="sh-size"]').change(function() {
  //   var val = $(this).val();
  //   if(val === '正常') {
  //     curElem.find('.mb-detail[data-pid="sejz-rt-7"] .sh-size-inp .rt-sr-w').val('').attr('disabled', true);
  //   } else {
  //     curElem.find('.mb-detail[data-pid="sejz-rt-7"] .sh-size-inp .rt-sr-w').attr('disabled', false);
  //   }
  //   getJzxShInfo();
  // })
}
function changeLbj(vm) {
  if($(vm).is(":checked")) {
    // 加延时是为了错开原有默认的公共高亮方法
    setTimeout(function() {
      $('[contect-id="sejz-rt-131"] .def-ck').prop('checked', true);
    }, 50)
  }
}
// 模版类型显示/隐藏
function toggleMbHandler(tab) {
  var actTab = '';
  var curActIsHide = false;
  curElem.find('[data-mtab]').each(function(i, dom) {
    var mtab = $(dom).attr('data-mtab');
    if($(dom).is(':checked')) {
      if(!actTab) {
        actTab = mtab;
      }
      $('.mb-hd-i[id="'+mtab+'"]').removeClass('rt-hide').show();
      if(tab === mtab) {
        $('.mb-hd-i[id="'+mtab+'"]').addClass('act').siblings('.mb-hd-i').removeClass('act');
        $('.mb-detail[data-pid="'+mtab+'"]').show().siblings('.mb-detail').hide();
      }
      if(tab) {
        actTab = mtab;
        $('.mb-detail[data-pid="'+mtab+'"]').find('.def-ck').click();
      }
      $('.mb-detail[data-pid="'+mtab+'"]').find('.rt-sr-w').each(function(i, rtW){
        if($(rtW).parents('[data-name]').length === 0) {
          $(rtW).removeClass('rt-hide'); 
        }
      })
    } else {
      if(tab === mtab && $('.mb-hd-i.act').attr('id') === tab) {
        curActIsHide = true;
      }
      $('.mb-hd-i[id="'+mtab+'"]').addClass('rt-hide').hide();
      $('.mb-hd-i[id="'+mtab+'"]').removeClass('act')
      $('.mb-detail[data-pid="'+mtab+'"]').hide();
      $('.mb-detail[data-pid="'+mtab+'"]').find('.rt-sr-w').addClass('rt-hide');  //带rt-hide的节点不回保存数据
    }
  })
  if((!tab && actTab) || (tab && curActIsHide)) {
    $('.mb-hd-i[id="'+actTab+'"]').removeClass('rt-hide').show();
    $('.mb-hd-i[id="'+actTab+'"]').addClass('act').siblings('.mb-hd-i').removeClass('act');
    $('.mb-detail[data-pid="'+actTab+'"]').show().siblings('.mb-detail').hide();
  }
  if(curElem.find('[data-mtab]:checked').length === 1) {
    curElem.find('[data-mtab]').attr('disabled', false)
    curElem.find('[data-mtab]:checked').attr('disabled', true)
  } else {
    curElem.find('[data-mtab]').attr('disabled', false)
  }
  if(!tab || tab === 'sejz-rt-7') {
    jzxSHDisplay();
  }
}

// 关联模块显示隐藏
function toggleContectModule(mName) {
  $('[data-name]').hide();
  $('[data-name]').find('.rt-sr-w').addClass('rt-hide'); 
  curElem.find('[data-show]').each(function(i, dom) {
    var showName = $(dom).attr('data-show');
    if($(dom).is(':checked')) {
      $('[data-name="'+showName+'"]').show();
      $('[data-name="'+showName+'"]').find('.rt-sr-w').each(function(i, rtW){
        if($(rtW).parents('[data-name]').attr('data-name')===showName) {
          $(rtW).removeClass('rt-hide'); 
        }
      })
    }
  })
}

// 切换结节模版
function changeBzTempHandler(vm) {
  var val = $(vm).val();
  var temp = $(vm).find('option[value="'+val+'"]').attr('data-val');
  var pane = $(vm).closest('.t-content');
  defValInBzTemp(pane, temp);
  getBzDetailInfo();
  computeScoreHandler(pane);
}

// 结节模板默认值切换
function defValInBzTemp(pane, temp) {
  temp = temp || 'def';
  pane.find('.rt-sr-w:not(.sel-temp)').each(function(i, rtW){
    if($(rtW).attr('data-temp') && $(rtW).attr('data-temp').indexOf(temp) > -1) {
      $(rtW).click();
    } else {
      $(rtW).prop('checked', false);
    }
  })
}

// 分数计算，推出等级
function computeScoreHandler(pane, detail) {
  var totalScore = 0;
  pane.find('[score]:not(.rt-hide):checked').each(function(i,ele){
    var score = $(ele).attr('score');
    totalScore += score ? Number(score) : 0;
  })
  // pane.find('.lvTxt').text(scoreAndLevelMap[totalScore]?scoreAndLevelMap[totalScore].level:'0');
  if(!detail) {
    pane.find('.lvTxt').find(pane.find('[name$=".12.01"]:checked').val());
  } else {
    pane.find('.lvTxt').text(detail.level);
  }
  pane.find('.scTxt').text(totalScore);
}

// 确定用哪张结节图
function displayBzImage() {
  var preSrc = './template-lib/assets/images/xmJzx/';
  var imgName = 'jzx-normal.png';
  curBzImageStatus = 'zc';
  // 是否存在锥状叶的
  var zzyFlag = $("#sejz-rt-4:checked").length && $('[bzkey="zzy"]:checked').length;
  if(zzyFlag) {
    imgName = jzxBzImageObj['zzy'];
  } else {
    var mtab = $("#sejz-rt-2").is(":checked") ? 'sejz-rt-6' : ($("#sejz-rt-3").is(":checked") ? 'sejz-rt-7' : '');
    if(mtab) {
      var tempArr = [];
      var part = $('[data-pid="'+mtab+'"]');
      for(var i = 0; i < jzxBzJoinKey.length; i++) {
        var key = jzxBzJoinKey[i]
        if(part.find('[bzkey="'+key+'"]:checked').length === 0){
          continue;
        }
        tempArr.push(key);
      }
      if(tempArr.length){
        var joinKy = tempArr.join('-');
        curBzImageStatus = joinKy;
        if(jzxBzImageObj[joinKy]){
          imgName = jzxBzImageObj[joinKy];
        }
      }
    }
  }
  $("#jzx-iName").text(imgName);
  $("#jzx-img").attr('src', preSrc + imgName);
  // 换图的同时，图上的结节点同步更换，并更新结节信息
  // setBzPosition()
  if(rtStructure) {
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['bzImageName'] = $("#jzx-iName").text();
  }
  if($('[data-mtab="sejz-rt-6"]').is(':checked')) {
    getBzDetailInfo();
  }
}

// 确定结节位置
function setBzPosition(dom) {
  var pointStyle = {};
  var pointKey = $(dom).find('[pointKey]:checked');
  var tempArr = [];
  hasZzyBz = false;
  pointKey.each(function(i,point){
    if($(point).attr('pointKey')) {
      var pkey = $(point).attr('pointKey')
      tempArr.push(pkey);
      if(!hasZzyBz && pkey === 'zzy') {
        hasZzyBz = true;
      }
    }
  })
  if(tempArr.length) {
    var pointName = tempArr.join('-');
    var bzImgStatus = 'zc';
    if(jzxPosObj[pointName]) {
      for(var key in curBzImageStatusMap) {
        if(curBzImageStatus.indexOf(key) > -1) {
          bzImgStatus = key;
          break;
        }
      }
      var potIndex = curBzImageStatusMap[bzImgStatus];
      var point = jzxPosObj[pointName][potIndex] ? jzxPosObj[pointName][potIndex] : jzxPosObj[pointName][0];
      if(point && point.length) {
        pointStyle = {
          left: point[0],
          top: point[1],
          background: 'red',
        }
      }
    }
  }
  return pointStyle;
}

// 获取所有结节的字段
function getBzFormVal(dom, paneId) {
  var bmLen = dom.find(`[id="jzx-0000.${paneId}.02.04"]:not(.rt-hide)`).val();
  var obj = {
    // level: dom.find(`.lvTxt:not(.rt-hide)`).text() || '',  //等级
    score: dom.find(`.scTxt:not(.rt-hide)`).text() || '',  //评分
    posSide: dom.find(`[name="RG-0000.${paneId}.02.01"]:checked:not(.rt-hide)`).val() || '',  //位置-左\右\峡部\锥状叶
    posTorB: dom.find(`[name="RG-0000.${paneId}.02.02"]:checked:not(.rt-hide)`).val() || '',  //位置-上极\下...
    posOther: getVal(`[name="RG-0000.${paneId}.02.03"]:checked:not(.rt-hide)`) || '',  //位置-具体位置
    bmLen: bmLen ? bmLen + 'mm' : '',  //距包膜最小距离
    hs: dom.find(`[name="RG-0000.${paneId}.04.01"]:checked:not(.rt-hide)`).val() || '',  //回声
    fw: dom.find(`[name="RG-0000.${paneId}.05.01"]:checked:not(.rt-hide)`).val() || '',  //方位
    by: dom.find(`[name="RG-0000.${paneId}.06.01"]:checked:not(.rt-hide)`).val() || '',  //边缘
    jzxQhs: dom.find(`[name="RG-0000.${paneId}.07.01"]:checked:not(.rt-hide)`).val() || '',  //局灶性强回声
    jzxQhsDetail: dom.find(`[name="RG-0000.${paneId}.07.02"]:checked:not(.rt-hide)`).val() || '',  //局灶性强回声
    jg: dom.find(`[name="RG-0000.${paneId}.08.01"]:checked:not(.rt-hide)`).val() || '',  //结构性质
    bzCount: dom.find(`[name="RG-0000.${paneId}.09.01"]:checked:not(.rt-hide)`).val() || '',  //结节数目
    xl: dom.find(`[name="RG-0000.${paneId}.10.01"]:checked:not(.rt-hide)`).val() || '',  //血流
    otherDesc: dom.find(`[id="jzx-0000.${paneId}.11.01"]:not(.rt-hide)`).val() || '',  //其他征象
    level: dom.find(`[name="RG-0000.${paneId}.12.01"]:checked:not(.rt-hide)`).val() || '',  //TI-RADS分类
    yxtHs: getVal(`[name="CK-0000.${paneId}.13.01"]:checked:not(.rt-hide)`, '，') || '',  //余腺体回声
    xtXl: getVal(`[name="RG-0000.${paneId}.14.01"]:checked:not(.rt-hide)`, '，') || '',  //腺体血流
    levelAdvice: getVal(`[name="RG-0000.${paneId}.16.01"]:checked:not(.rt-hide)`, '，') || '',  //建议
  }
  var sizeTxt = [];
  var decimLen = 0;
  for(var bIdx = 1; bIdx < 4; bIdx++) {
    var bI = bIdx < 9 ? `0${bIdx}` : bIdx;
    var sizeVal = dom.find(`[id="jzx-0000.${paneId}.03.${bI}"]:not(.rt-hide)`).val();
    if(sizeVal) {
      var [num, decim = ''] = sizeVal.split('.');
      if(decim && decimLen < decim.length) {
        decimLen = decim.length;
      }
      sizeTxt.push(sizeVal);
    }
  }
  var sizeRes = setDecimCount(decimLen, sizeTxt, 'mm')
  if(sizeRes.length) {
    obj.bzSize = sizeRes.join(' x ');   //大小
  }
  
  // 弹性评估
  var txArr = [];
  var txVal = getVal(`[name="RG-0000.${paneId}.15.01"]:checked:not(.rt-hide)`);
  if(txVal) {
    txVal += '分';
  }
  var txBi = getVal(`[id="jzx-0000.${paneId}.15.01.06"]:not(.rt-hide)`);
  if(txBi) {
    if(txVal) {
      txVal += '，';
    }
    txVal += '应变比：' + txBi;
  }
  if(txVal) {
    txArr.push('应变弹性评估：' + txVal);
  }
  var ROI = getVal(`[id="jzx-0000.${paneId}.15.02"]:not(.rt-hide)`);
  ROI && (txArr.push('ROI：' + ROI + 'mm'));
  var txCx = getVal(`[name="RG-0000.${paneId}.15.03"]:checked:not(.rt-hide)`);  //剪切波弹性成像
  txCx && txArr.push('剪切波弹性成像：' + txCx);
  var txSWV = getVal(`[pid="jzx-0000.${paneId}.15.04"]:not(.rt-hide)`, 'm/s，');  //剪切波弹性值(SWV)
  txSWV && txArr.push('剪切波弹性值(SWV)：' + txSWV + 'm/s');
  obj.txpg = txArr.length ? txArr.join('，') : '';
  
  return obj;
}

// 将大小的小数位拼成同样的个数
function setDecimCount(decimLen, sizeArr, unit) {
  var res = []
  for(var i = 0; i < sizeArr.length; i++) {
    var value = sizeArr[i];
    if(decimLen > 0) {
      var [num, decim = ''] = value.split('.');
      var len = decimLen - decim.length;
      if(len > 0) {
        var fillText = new Array(len).fill('0').join('');
        value = num + '.' + decim + fillText;
      }
    }
    res.push(value + unit);
  }
  return res;
}

// 甲状腺术后另一侧未手术的情况
function jzxSHDisplay() {
  if(!$("#sejz-rt-3").is(":checked")) {
    $(".unqc-wrap .rt-sr-w").addClass('rt-hide');
    return;
  }
  var shSide = $('[name="r-qcbw"]:checked').val();  //手术侧
  var shRG = $('[name="r-qcbwp"]:checked').val();  //切除情况
  if(shSide && shRG && shSide !== '双侧') {
    var unqcLabel = (shSide === '左侧' ? '右侧' : '左侧') + '甲状腺' + (shRG === '部分切除' ? '及峡部' : '')
    var impunqcLabel = '甲状腺' + (shSide === '左侧' ? '右侧叶' : '左侧叶') + (shRG === '部分切除' ? '及峡部' : '');
    $(".unqc-wrap #sejz-rt-95").text(unqcLabel);
    $(".unqc-wrap #sejz-rt-95").attr('data-imp-label', impunqcLabel);
    $(".unqc-wrap").css('display', 'flex');
    $(".unqc-wrap .rt-sr-w").removeClass('rt-hide');
    if(shRG !== '部分切除') {
      $(".sh-xb").hide();
      $(".sh-xb .rt-sr-w").addClass('rt-hide');
    } else {
      $(".sh-xb").show();
      $(".sh-xb .rt-sr-w").removeClass('rt-hide');
    }
  } else {
    $(".unqc-wrap").hide();
    $(".unqc-wrap .rt-sr-w").addClass('rt-hide');
  }
  if(shSide === '双侧' && shRG === '部分切除') {
    $('.cyText').text('残余甲状腺及峡部');
  } else {
    $('.cyText').text('残余甲状腺');
  }
}

// 甲状腺基本信息汇总
function getJzxInfo() {
  var jzxInfoArr = [];
  if($("#sejz-rt-2").is(":checked")) {
    // 甲状腺大小 --start
    var lrSide = ['sejz-rt-11', 'sejz-rt-16', 'sejz-rt-21'];
    var lrSizeArr = [];
    for(var i = 0; i < lrSide.length; i++) {
      var childEl = $('[pid="'+lrSide[i]+'"]');
      var childValArr = [];
      // var childChArr = [];
      var decimLen = 0;
      childEl.each(function(){
        if($(this).val()) {
          sizeVal = $(this).val();
          var [num, decim = ''] = sizeVal.split('.');
          if(decim && decimLen < decim.length) {
            decimLen = decim.length;
          }
          childValArr.push(sizeVal);
          // if($(this).attr('placeholder')) {
          //   childChArr.push($(this).attr('placeholder'));
          // }
        }
      })
      if(childValArr.length) {
        var sizeRes = setDecimCount(decimLen, childValArr, 'mm');
        // (childChArr[chIdx] || '') + 
        sizeRes.forEach(function(sizeItem, chIdx){
          sizeRes[chIdx] = sizeItem;
        })
        var size = sizeRes.join(' x ');   //大小
        var sideName = $('[id="'+lrSide[i]+'"]').text();
        lrSizeArr.push(sideName + size);
      }
    }
    if(lrSizeArr.length) {
      jzxInfoArr.push('甲状腺' + lrSizeArr.join('，'))
    }
    // 甲状腺大小 --end

    // 除大小外的描述拼成一行
    var resetArr = [];
    // 正常/缺如情况 --start
    if($('[name="r-size"]:checked').val()){
      var norVal = $('[name="r-size"]:checked').val();
      // 正常情况不用描述
      if(norVal.indexOf('正常') === -1){
        var norId = $('[name="r-size"]:checked').attr('id');
        var childNorVal = $('[pid="'+norId+'"]:checked').val();
        if(childNorVal || norVal.indexOf('缺如') > -1) {
          if(norVal.indexOf('缺如') > -1) {
            resetArr.push(norVal);
          }
          if(childNorVal && childNorVal.indexOf('正常') === -1) {
            resetArr.push(childNorVal);
          }
        }
      }
    }
    // 正常/缺如情况 --end

    // 甲状腺上动脉流速
    var sdmArr = [];
    var sdmIds = ['sejz-rt-301', 'sejz-rt-304'];
    for(var i = 0; i < sdmIds.length; i++) {
      var sdmId = sdmIds[i];
      var sdmChild = $('[pid="'+sdmId+'"]');
      var text = $('[id="'+sdmId+'"]').attr('data-srname');
      sdmChild.each(function(i, cItem) {
        var sdmVal = $(this).val() || '';
        var sdmValText = $(this).attr('data-srname');
        text += (i > 0 ? '，' : '')+ sdmValText + '=' + sdmVal + `${sdmValText === 'PSV' ? 'cm/s' : ''}`;
      })
      sdmArr.push(text);
    }
    if(sdmArr.length) {
      jzxInfoArr.push(sdmArr.join('，'));
    }

    // 占位性病变
    // var zxbz = getVal('[name="jb-zwx"]:checked');
    // if(zxbz) {
    //   var zxbzText = '';
    //   if(zxbz === '未见明显占位性病变') {
    //     if($('[name="jb-wjc"]:checked').length === 1) {
    //       zxbzText = $('[name="jb-wjc"]:checked').val() + zxbz;
    //     } else {
    //       zxbzText = zxbz;
    //     }
    //   } else {
    //     var posText = getVal('[name="jb-kjc"]:checked', '、');
    //     zxbzText = posText + zxbz;
    //   }
    //   resetArr.push(zxbzText);
    // }

    // 甲状腺上动脉血流速度
    var dmls = getVal('[name="jb-dmls"]:checked');
    if(dmls) {
      var dmlsText = dmls === '甲状腺上动脉血流速度增高' ? getVal('[name="jb-dmlszg"]:checked', '、') : getVal('[name="jb-dmlszc"]:checked', '、');
      resetArr.push(dmlsText + dmls);
    }

    // 其他剩余选项 --start  去掉'sejz-rt-40', 
    var otherId = ['sejz-rt-44', 'sejz-rt-48', 'sejz-rt-53', 'sejz-rt-61', 'sejz-rt-67'];
    otherId.forEach(function(oItem){
      var oChildVal = '';
      if(oItem === 'sejz-rt-67') {
        oChildVal = $('[pid="'+oItem+'"]').val();
        if(oChildVal) {
          resetArr.push('其他征象：' + oChildVal);
        }
      } else {
        // oChildVal = $('[pid="'+oItem+'"]:checked').val();
        oChildVal = getVal('[pid="'+oItem+'"]:not(.rt-hide):checked', '、');
        if(oChildVal) {
          var preTxt = oItem !== 'sejz-rt-61' ? $('[id="'+oItem+'"]').attr('data-srname') : 'CDFI显示：';
          var radioText = preTxt + oChildVal;
          // 腺体回声不均匀
          // if(oItem === 'sejz-rt-53' && oChildVal === '不均匀' && $('[name="r-bjy"]:checked').val()){
          //   var detVal = $('[name="r-bjy"]:checked').val();
          //   if(detVal === '其他') {
          //     if(getVal('[id="sejz-rt-602"]')) {
          //       radioText += '，' + getVal('[id="sejz-rt-602"]');
          //     }
          //   } else {
          //     radioText += '，' + detVal;
          //   }
          // }
          // 血流信号-血供丰富
          // if(oItem === 'sejz-rt-61' && oChildVal === '丰富' && $('[id="sejz-rt-64"]').val()){
          //   radioText += '，甲状腺上动脉流速' + $('[id="sejz-rt-64"]').val() + 'cm/s';
          // }
          resetArr.push(radioText);
        }
      }
    })
    // 其他剩余选项 --end

    if(resetArr.length) {
      jzxInfoArr.push('甲状腺' + resetArr.join('，'));
    }
  }
  if(rtStructure) {
    var jzxInfoStr = jzxInfoArr.length ? (jzxInfoArr.join('。\n') + '。') : '';
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['jzxInfoStr'] = jzxInfoStr;
  }
  // console.log('基本信息->', jzxInfoStr);
}

// 甲状腺术后信息汇总
function getJzxShInfo() {
  var shInfoObj = {};
  var shInfoArr = [];
  let allSizeArr = [], cyJzxArr = [], otherJzxArr = [];
  var shqkTxt = '';  //甲状腺切除情况
  if($("#sejz-rt-3").is(":checked")) {
    var qcbw = $('[name="r-qcbw"]:checked').val();   //切除部位
    var qcqk = $('[name="r-qcbwp"]:checked').val();   //切除情况
    if(qcbw && qcqk) {
      shqkTxt = '甲状腺' + qcbw + '叶' + qcqk + '术后';
      var xths = $('[name="r-rcy-xths"]:checked:not(.rt-hide)').val() || '';  //腺体回声
      var xlxh = $('[name="r-rcy-xlxh"]:checked:not(.rt-hide)').val() || '';  //血流信号
      var ljzxc = $('[name="r-ljzxc"]:checked:not(.rt-hide)').val() || '';  //甲状腺床
      var clXtSize = getVal('[name="cySize"]:not(.rt-hide)', 'mm x ') || '';  //残留腺体大小
      var clXtSize = getVal('[name="cySize"]:not(.rt-hide)', 'mm x ') || '';  //残留腺体大小
      var clZwxbb = getVal(`[name="${qcqk !== '全切' ? 'r-rcy-zwx' : 'r-rcy-qzwx'}"]:checked:not(.rt-hide)`) || '';  //残留腺体大小
      if(clXtSize) {
        clXtSize += 'mm';
      }
      var otherXths = $('[name="r-sh-xths"]:checked:not(.rt-hide)').val() || '';  //剩余部分腺体回声
      shInfoObj = { shqkTxt, xths, xlxh, ljzxc, qcbw, qcqk, otherXths, clXtSize };
      let joinTxtArr = [];
      if(qcqk !== '全切') {
        let cyJzxName = qcbw + '残叶';
        if(xths) {
          cyJzxArr.push('腺体' + xths);
          shInfoObj.hsDetail = xths;
        }
        if(clZwxbb) {
          cyJzxArr.push(clZwxbb);
        }
        if(xlxh) {
          var rpText = 'CDFI显示：' + xlxh.replace('腺体内', '残叶内');
          rpText = rpText.replace('血流', '血流信号');
          if(cyJzxArr.length) {
            cyJzxArr[cyJzxArr.length - 1] = cyJzxArr[cyJzxArr.length - 1] + ('；' + rpText)
          } else {
            cyJzxArr.push(rpText);
          }
        }
        if(clXtSize) {
          allSizeArr.push(cyJzxName + clXtSize);
        }
      } else {
        if(ljzxc === '未见异常') {
          cyJzxArr.push('甲状腺床未见异常');
        } else {
          cyJzxArr.push(ljzxc);
        }
        if(clZwxbb) {
          cyJzxArr.push(clZwxbb);
        }
      }
      if(qcbw !== '双侧') {
        // 另一未手术侧情况
        var resetArr  = [];
        var otherSide = $('#sejz-rt-95').attr('data-imp-label');
        var otherId = ['sejz-rt-96', 'sejz-rt-100', 'sejz-rt-104', 'sejz-rt-108', 'sejz-rt-113', 'sejz-rt-215', 'sejz-rt-121'];
        otherId.forEach(function(oItem){
          var oChildVal = '';
          oChildVal = $('[pid="'+oItem+'"]:checked:not(.rt-hide)').val();
          if(oChildVal) {
            if(['sejz-rt-96', 'sejz-rt-100'].includes(oItem)) {
              // 大小
              if(oItem == 'sejz-rt-96') {
                var shSize = getVal('.sh-size-inp .rt-sr-w', 'mm x ');
                if(shSize) {
                  allSizeArr.push((qcbw === '左侧' ? '右侧叶' : '左侧叶') + shSize + 'mm');
                } else {
                  allSizeArr.push((qcbw === '左侧' ? '右侧叶' : '左侧叶') + oChildVal);
                }
              }
              // 峡部增厚大小
              if(oItem === 'sejz-rt-100') {
                var xbzhSize = curElem.find('#sejz-rt-209').val();
                if(xbzhSize) {
                  // radioText += xbzhSize + 'mm';
                  allSizeArr.push('峡部厚' + xbzhSize + 'mm');
                } else {
                  allSizeArr.push('峡部' + oChildVal);
                }
              }
            } else if(['sejz-rt-104', 'sejz-rt-108'].includes(oItem)) {
              var radioText = $('[id="'+oItem+'"]:not(.rt-hide)').text() + oChildVal;
              otherJzxArr.push(radioText);
            } else {
              if(oItem === 'sejz-rt-113') {
                otherJzxArr.push('腺体' + oChildVal);
              }
              if(oItem === 'sejz-rt-215') {
                otherJzxArr.push(oChildVal);
              }
              if(oItem === 'sejz-rt-121') {
                var rpText = 'CDFI显示：' + oChildVal.replace('腺体内', `${qcbw==='左侧'?'右侧叶内':'左侧叶内'}`);
                rpText = rpText.replace('血流', '血流信号');
                if(otherJzxArr.length) {
                  otherJzxArr[otherJzxArr.length - 1] = otherJzxArr[otherJzxArr.length - 1] + ('；' + rpText)
                } else {
                  otherJzxArr.push(rpText);
                }
              }
            }
          }
        })
      }
    }
  }
  allSizeArr.length && shInfoArr.push(allSizeArr.join('，'));
  cyJzxArr.length && shInfoArr.push(('甲状腺' + qcbw + '残叶') + cyJzxArr.join('，'));
  otherJzxArr.length && shInfoArr.push(otherSide + otherJzxArr.join('，'));
  // 其他征象
  if($('[id="sejz-rt-128"]').val()) {
    var shOther = '其他征象：'+$('[id="sejz-rt-128"]').val();
    shInfoArr.push(shOther);
    shInfoObj['shOther'] = shOther;
  }
  
  if(rtStructure) {
    var shInfoStr = shInfoArr.length ? ((shqkTxt?shqkTxt+'：\n':'') + shInfoArr.join('。\n') + '。') : '';
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['shInfoStr'] = shInfoStr;
    rtStructure.description['shInfoObj'] = shInfoObj;
  }
  // console.log('术后信息->', shInfoStr);
}

// 甲状腺结节信息汇总
function getBzDetailInfo(oldPaneId) {
  vueComp.$nextTick(() => {
    vueComp.bzDetailList = [];
    vueComp.jzxPointList = [];
    var pointStr = [];
    var bzDescription = [];
    if($("#sejz-rt-4").is(":checked")) {
      $('.rt-pane').each(function(i, dom) {
        var paneId = $(dom).attr('paneIdKey');
        var detail = getBzFormVal($(dom), paneId);
        computeScoreHandler($(dom), detail);  //推算分数、等级
        vueComp.bzDetailList.push(detail);
        var pointStyle = setBzPosition(dom);
        var bzTitle = `结节${i+1}：`;
        var bzDesc = '';
        bzDesc += `${detail.posSide?detail.posSide:''}${detail.posOther?detail.posOther:''}`;
        if(bzDesc) {
          bzDesc = '甲状腺' + bzDesc + `可见${detail.bzCount}${detail.jg}${detail.hs || ''}结节\n`;
        }
        // bzDesc += `距包膜最小距离约${detail.bmLen || '-'}\n`;
        if(detail.posTorB) {
          bzDesc += `${detail.bzCount === '多发' ? '大者位于' : '位于'}${detail.posTorB}\n`;
        }
        bzDesc += `${detail.bzCount === '多发' && !detail.posTorB ? '大者约' : '大小约'}${detail.bzSize || '-'}\n`;
        bzDesc += `${detail.fw ? detail.fw : ''}\n`;
        bzDesc += `边缘${detail.by || '-'}\n`;
        if(detail.jzxQhs) {
          if(detail.jzxQhs === '可见') {
            bzDesc += `可见${detail.jzxQhsDetail || '强回声'}\n`;
          } else {
            bzDesc += detail.jzxQhs + '\n';
          }
        }
        // bzDesc += `${detail.jzxQhs ? `${detail.jzxQhs === '无强回声' ? '' : '可见'}`+detail.jzxQhs : ''}\n`;
        bzDesc += `${detail.xl ? ('CDFI显示：结节内' + (detail.xl==='无血流'?'未见明显血流信号' : '可见'+detail.xl)) : ''}\n`;
        // 换行内容
        let lineTwoArr = [];
        detail.yxtHs && lineTwoArr.push('余腺体' + detail.yxtHs);
        detail.xtXl && lineTwoArr.push(detail.xtXl);
        detail.txpg && lineTwoArr.push(detail.txpg);
        // bzDesc += `${detail.yxtHs ? '余腺体' + detail.yxtHs : ''}\n`;
        // bzDesc += `${detail.xtXl || ''}\n`;
        // bzDesc += `${detail.txpg || ''}\n`;
        bzDesc = bzDesc;
        var oneBzDesc = bzDesc.split('\n');
        oneBzDesc = oneBzDesc.filter(oItem => oItem);
        if(detail.level && pointStyle.left && pointStyle.top) {
          bzTitle += 'TI-RADS：' + detail.level + '类；' + detail.jg;
          vueComp.jzxPointList.push({
            style: {
              left: pointStyle.left+'px',
              top: pointStyle.top+'px',
              // background: scoreAndLevelMap[detail.score].color,
              background: levelAndColorMap[detail.level],
            },
            level: detail.level,
            showPointDetail: false,
            bzDesc: (oneBzDesc ? oneBzDesc.join('\n') : '') + (lineTwoArr.length ? '\n' + lineTwoArr.join('\n') : ''),
            bzTitle: bzTitle,
            showDetail: false,
            bzIndex: i+1
          })
          // [x,y,颜色,等级]
          // pointStr.push([pointStyle.left,pointStyle.top,scoreAndLevelMap[detail.score].color,detail.level]);
          pointStr.push([pointStyle.left,pointStyle.top,levelAndColorMap[detail.level],detail.level]);
        }
        oneBzDesc.forEach((one, oI) => {
          if(one[one.length-1] === '，') {
            oneBzDesc[oI] = one.substring(0, one.length-1);
          }
        })
        // 用于描述的内容，除了表格外的数据
        if(detail.otherDesc) {
          lineTwoArr.push(`其他征像：${detail.otherDesc}`)
        }
        let totalOneDesc = oneBzDesc.join('，') + (oneBzDesc.length?'。':'');
        totalOneDesc += (totalOneDesc ? '\n' : '') + lineTwoArr.join('，') + (lineTwoArr.length?'。':'');
        totalOneDesc = totalOneDesc.replace('，CDFI显示：', '；CDFI显示：')
        bzDescription.push(totalOneDesc);
      })
    }
    if(pointStr.length) {
      $("#jzx-pot").text(JSON.stringify(pointStr));
    } else {
      $("#jzx-pot").text('');
    }
    if(rtStructure) {
      !rtStructure.description && (rtStructure.description = {});
      rtStructure.description['bzDetailList'] = vueComp.bzDetailList;
      rtStructure.description['jzxPointList'] = vueComp.jzxPointList;
      rtStructure.description.bzDescription = bzDescription.join('\n');
    }
    // console.log('结节信息-->', rtStructure.description.bzDescription);
    if(!oldPaneId) {
      getImpression();
    }
  })
}

// 颈部淋巴结信息汇总
function getLbjInfo(firstIn) {
  var lbjInfoArr = [];
  var lbjInfoObj = {};
  var lbjHighData = [];
  if($("#sejz-rt-5").is(":checked")) {
    var lbStatus = $('[name="lb-val"]:checked:not(.rt-hide)').val();
    cleanCanvas();
    if(lbStatus === '可见异常') {
      var lbPosId = $('[name="lbj-wz"]:checked:not(.rt-hide)').attr('id');
      // if(lbPosId) {
      lbjInfoObj = {
        lbStatus: lbStatus,
        lbPos: $('[name="lbj-wz"]:checked:not(.rt-hide)').val() || '',  //淋巴位置
        lbCount: $('[contect-id="'+lbPosId+'"] input[type="radio"]:checked:not(.rt-hide)').val() || '',  //淋巴数目
      }
      // 异常区域
      var lbArea = [];
      var dbSideArea = '';
      var dbSideAreaArr = [];
      var rightArea = [], leftArea = [];
      $('[contect-id="'+lbPosId+'"] input[type="checkbox"]:checked:not(.rt-hide)').each(function(iA, area){
        if(lbjInfoObj.lbPos === '双侧颈部') {
          if($(area).val().indexOf('左侧') > -1) {
            dbSideArea = '左侧';
            dbSideAreaArr.push(dbSideArea);
            leftArea.push($(area).val().replace(/左侧/ig, ''));
          } else {
            dbSideArea = '右侧';
            dbSideAreaArr.push(dbSideArea);
            rightArea.push($(area).val().replace(/右侧/ig, ''));
          }
        } 
        lbArea.push($(area).val())
        lbjInfoObj.dbSideAreaArr = dbSideAreaArr;
      })
      if(lbjInfoObj.lbPos === '双侧颈部') {
        if(lbArea.length === 1 && dbSideArea) {
          lbjInfoObj.dbArea = lbArea[0].replace(/左侧|右侧/ig, '') + '('+dbSideArea+'较大)';
        } else {
          var dbArr = [];
          if(leftArea.length) {
            dbArr.push('左侧('+leftArea.join('、')+')');
          }
          if(rightArea.length) {
            dbArr.push('右侧('+rightArea.join('、')+')');
          }
          lbjInfoObj.dbArea = dbArr.join('，');
        }
      }
      lbjInfoObj.lbArea = lbArea.join('、');
      lbjInfoObj.lbAreaArr = lbArea;

      // 淋巴大小
      var sizeTxt = [];
      var decimLen = 0;
      var sizeIds = ['sejz-rt-149', 'sejz-rt-150', 'sejz-rt-151'];
      for(var bIdx = 0; bIdx < sizeIds.length; bIdx++) {
        var sizeVal = $(`[id="${sizeIds[bIdx]}"]:not(.rt-hide)`).val();
        if(sizeVal) {
          var [num, decim = ''] = sizeVal.split('.');
          if(decim && decimLen < decim.length) {
            decimLen = decim.length;
          }
          sizeTxt.push(sizeVal);
        }
      }
      var sizeRes = setDecimCount(decimLen, sizeTxt, 'mm')
      if(sizeRes.length) {
        lbjInfoObj.lbSize = sizeRes.join(' x ');   //大小
      }

      // 其他单选信息
      var otherRadioNames = [];
      $('.lbjI input[name]').each(function(r, rWidget) {
        if($(rWidget).is(':checked')) {
          var name = $(rWidget).attr('name');
          lbjInfoObj[name] = $(rWidget).val();
          otherRadioNames.push(name);
        }
      })
      let impLbAreaStr = lbjInfoObj.dbArea ? lbjInfoObj.dbArea : (lbjInfoObj.lbArea ? lbjInfoObj.lbArea.replace(/区/g, '') + '区' : '');
      lbjInfoArr.push(`${lbjInfoObj.lbPos}${impLbAreaStr}淋巴结回声及结构异常，考虑转移性`)
      // lbjInfoArr.push(`${lbjInfoObj.lbPos}${impLbAreaStr}${lbjInfoObj.lbCount==='多发'?'多发':'可见'}异常淋巴结`)
      // $('[data-lb]:not(.rt-hide):checked').each(function(i, lbHight){
      //   var lbKey = $(lbHight).attr('data-lb');
      //   if(lbjHighCoord[lbKey]) {
      //     lbjHighData.push(lbKey);
      //     highLightOrgan(lbjHighCoord[lbKey]);
      //   }
      // })
      if(!firstIn) {
        lbjHighData = drawLbAreaHandler();
        if(lbjHighData.length) {
          $("#lbj-pot").text(JSON.stringify(lbjHighData));
        } else {
          $("#lbj-pot").text('');
        }
      }
      // }
      //  else {
      //   lbjInfoObj = {lbStatus: lbStatus};
      //   lbjInfoArr = [];
      //   $("#lbj-pot").text('');
      // }
    } else {
      lbjInfoObj = {lbStatus: lbStatus};
      lbjInfoArr.push('颈前部甲状腺周围未探及肿大淋巴结回声');
      $("#lbj-pot").text('');
    }
  }
  if(rtStructure) {
    let { dbArea='',dbSideAreaArr=[],lbArea='',lbAreaArr=[],lbCount='',lbPos='',lbSize='',lbStatus='',lbjBy='',lbjDzgh='',lbjHighData=[],lbjLbm='',lbjPzhd='',lbjPzhs='',lbjXllx='',lbjXt='', lbcdjB = '' } = lbjInfoObj;
    var lbjInfoStr = lbjInfoArr.length ? (lbjInfoArr.join('；')) : '';
    var lbjDesStr = '';
    var lbjDesArr = [];
    if(lbjInfoStr && lbjInfoStr !== '颈前部甲状腺周围未探及肿大淋巴结回声') {
      let impLbAreaStr = dbArea ? dbArea : (lbArea ? lbArea.replace(/区/g, '') + '区' : '');
      let onlyOne = lbAreaArr.length <= 1 && dbSideAreaArr.length <= 1 && lbCount !== '多发';
      lbPos ? lbjDesArr.push(`${lbPos}${impLbAreaStr}探及${onlyOne?'一个':'多个'}淋巴结回声`) : '';
      lbSize ? lbjDesArr.push(`${onlyOne?'大小约':'较大者约'}${lbSize}`) : '';
      // if(lbCount === '多发') {
      //   lbPos ? lbjDesArr.push(lbPos + '可见' + lbCount + '淋巴结') : '';
      //   if(dbArea) {
      //     if(dbSideAreaArr.length>1) {
      //       lbjDesArr.push('分别位于' + dbArea);
      //       lbSize ? lbjDesArr.push('较大者约' + lbSize) : '';
      //     }else {
      //       lbjDesArr.push('较大者位于' + (lbAreaArr.lenght>1 ? dbArea : lbArea));
      //       lbSize ? lbjDesArr.push('大小约' + lbSize) : '';
      //     }
      //   }else {
      //     lbArea ? lbAreaArr.length>1 ? lbjDesArr.push('分别位于' + lbArea) : lbjDesArr.push('位于' + lbArea) : '';
      //     lbSize ? lbjDesArr.push('较大者约' + lbSize) : '';
      //   }
      // }else {
      //   lbPos ? lbjDesArr.push(lbPos + '可见淋巴结') : '';
      //   if(dbArea) {
      //     dbSideAreaArr.length>1 ? lbjDesArr.push('分别位于' + dbArea) : lbjDesArr.push('位于' + (lbAreaArr.lenght>1 ? dbArea : lbArea));
      //   }else {
      //     lbArea ? lbAreaArr.length>1 ? lbjDesArr.push('分别位于' + lbArea) : lbjDesArr.push('位于' + lbArea) : '';
      //   }
      //   lbSize ? lbjDesArr.push('大小约' + lbSize) : '';
      // }
      lbjXt ? lbjDesArr.push('形状：' + lbjXt) : '';
      lbcdjB ? lbjDesArr.push('长短径比：' + lbcdjB.replace('长短径比', '')) : '';
      lbjBy ? lbjDesArr.push('边缘：' + lbjBy) : '';
      lbjLbm ? lbjDesArr.push('淋巴门：' + lbjLbm) : '';
      lbjPzhd ? lbjDesArr.push('皮质厚度：' + lbjPzhd) : '';
      lbjPzhs ? lbjDesArr.push('皮质回声：' + lbjPzhs) : '';
      // lbjDzgh ? lbjDesArr.push(lbjDzgh === '无' ? '未见钙化' : ('可见' + lbjDzgh)) : '';
      lbjDzgh ? lbjDesArr.push('钙化：' + lbjDzgh) : '';
      lbjXllx ? lbjDesArr.push('血流类型：' + lbjXllx + '。') : '';
    }
    lbjDesStr = lbjDesArr.length ? lbjDesArr.join('，') : (lbjInfoStr ? lbjInfoStr + '。' : '');
    !rtStructure.description && (rtStructure.description = {});
    rtStructure.description['lbjInfoStr'] = lbjInfoStr;
    lbjInfoObj['lbjHighData'] = JSON.parse($("#lbj-pot").text() || '[]');
    rtStructure.description['lbjInfoObj'] = lbjInfoObj;
    rtStructure.description['lbjDesStr'] = lbjDesStr;
  }
  // console.log('淋巴结信息-->',lbjDesStr);
}

function drawLbAreaHandler() {
  var lbjHighData = [];  //淋巴结高亮区域
  $('[data-lb]:not(.rt-hide):checked').each(function(i, lbHight){
    var lbKey = $(lbHight).attr('data-lb');
    if(lbjHighCoord[lbKey]) {
      lbjHighData.push(lbKey);
      highLightOrgan(lbjHighCoord[lbKey]);
    }
  })
  return lbjHighData;
}

// 诊断内容
function getImpression(firstIn) {
  if(rtStructure.description) {
    var impressList = [];
    var checkJzx = $('#sejz-rt-2').is(':checked') || $('#sejz-rt-3').is(':checked') || $('#sejz-rt-4').is(':checked');
    var checkLbj = $('#sejz-rt-5').is(':checked');
    let {jzxInfoStr, shInfoStr, shInfoObj, bzDetailList, bzDescription, lbjInfoStr, lbjInfoObj} = rtStructure.description;
    
    // 甲状腺做了检查
    if(checkJzx) {
      var jbXtStatus = getVal('[name="r-xths"]:not(.rt-hide):checked', '、');
      var mmJc = '';
      // 基本信息
      if(jbXtStatus && jbXtStatus.indexOf('实质回声增粗、减低，可见弥漫性分布的蜂窝状低回声灶') > -1) {
        if($('[id="sejz-rt-24"]').is(':checked')) {
          mmJc = '甲状腺弥漫性肿大并血供丰富，考虑桥本氏甲状腺炎可能，请结合甲功';
        } else {
          mmJc = '甲状腺弥漫性改变并血供丰富，考虑桥本氏甲状腺炎可能，请结合甲功';
        }
        impressList.push(mmJc);
      }
      // 血流流速大于50，下结论
      var jbXlFlag = false;
      // if(jzxInfoStr) {
      //   var jbXlVal = getVal($('[id="sejz-rt-64"]:not(.rt-hide)'));
      //   if(jbXlVal && Number(jbXlVal) > 50) {
      //     impressList.push('符合甲亢声像，TI-RADS：2类，请结合甲状腺功能检查');
      //     jbXlFlag = true;
      //   }
      // }
      // 选择基本情况且没有选择结节
      if(jzxInfoStr && !bzDescription) {
        if(!mmJc && !jbXlFlag) {
          impressList.push('甲状腺结构未见明显异常');
        }
      } else {
        // 术后
        if(shInfoObj) {
          let shImpress = '';
          let {shqkTxt = '', xths = '', xlxh = '', ljzxc, qcbw, qcqk, otherXths} = shInfoObj;
          // shqkTxt && impressList.push(shqkTxt);
          if(shqkTxt) {
            shImpress = shqkTxt;
            if(qcqk !== '全切') {
              if(xths === "实质回声均匀") {
                // shImpress += '，' + $('.cyText').text() + '未见异常';
                shImpress += '，残叶结构未见明显异常';
              }
            } else {
              if(ljzxc === '未见异常') {
                // shImpress += '，' + qcbw + '甲状腺床未见异常';
                shImpress += `，甲状腺${qcbw}叶未见明显异常`;
              }
            }
            
            let xthsByText = '';
            if(xths && xths !== '实质回声均匀') {
              xthsByText = qcbw + '残叶';
            }
            let xthsRes = xths && xths.includes('欠均匀') ? '回声欠均匀':'回声不均匀'
            let otherXthsRes = otherXths && otherXths.includes('欠均匀') ? '回声欠均匀':'回声不均匀'
            // 残余和未手术的一致
            if(qcbw !== '双侧' && otherXths && otherXths !== '实质回声均匀' && xthsRes === otherXthsRes) {
              impressList.push(shImpress);
              xthsByText += (xthsByText ? '及' : '') + $("#sejz-rt-95").attr('data-imp-label') + '结构';
              xthsByText += otherXthsRes;
              impressList.push(xthsByText);
            } else {
              if(xths && xths !== '实质回声均匀') {
                xthsByText = '残叶结构' + xthsRes;
                shImpress += '，' + xthsByText;
              }
              impressList.push(shImpress);
              if(otherXths && otherXths !== '实质回声均匀') {
                xthsByText = $("#sejz-rt-95").attr('data-imp-label') + '结构' + otherXthsRes;
                impressList.push(xthsByText);
              }
            }
            if(!bzDescription && qcbw !== '双侧' && (!otherXths || otherXths === '实质回声均匀')) {
              impressList.push($("#sejz-rt-95").attr('data-imp-label') + '结构未见明显异常');
            }
          }

          // 血流流速大于50，下结论
          var ShXlImp = [];
          var xlValKey = ['r-rt-90', 'sejz-rt-125'];
          for(var xlKey of xlValKey) {
            var xlVal = getVal($('[id="'+xlKey+'"]:not(.rt-hide)'));
            if(xlVal && Number(xlVal) > 50) {
              var prefixText = xlKey === 'r-rt-90' ? `${qcbw}残余腺体` : `${qcbw === '右侧' ? '左侧' : '右侧'}`;
              ShXlImp.push(`${prefixText}符合甲亢声像，TI-RADS：2类，请结合甲状腺功能检查`)
            }
          }
          if(ShXlImp.length) {
            if(ShXlImp.length === 1) {
              impressList.push(ShXlImp[0]);
            } else {
              impressList.push('符合甲亢声像，TI-RADS：2类，请结合甲状腺功能检查');
            }
          }
        }

        // 结节
        if(bzDetailList && bzDetailList.length) {
          bzDetailList = JSON.parse(JSON.stringify(bzDetailList));
          let levelOrder = ['5', '4', '3', '2', '1'];
          if(bzDetailList.length > 1) {
            bzDetailList.sort(function(a, b){
              return levelOrder.indexOf(a.level) - levelOrder.indexOf(b.level);
            })
          }
          let has4Level = false;
          let yTxt = '';
          bzDetailList.forEach(function(bzItem) {
            var bzHtml = '';
            if(bzItem.posSide && bzItem.posTorB) {
              if(!has4Level && ['5', '4'].includes(bzItem.level)) {
                has4Level = true;
              }
              // 小于4A后的第一个加上‘余’字
              // if(has4Level && !['6', '5', '4C', '4B', '4A'].includes(bzItem.level) && !yTxt) {
              //   yTxt = '余';
              //   bzHtml += yTxt;
              // }
              var showScore = '';
              if(Number(bzItem.level) >= 4) {
                showScore = '（ACR '+bzItem.score+'分）'
              }
              bzHtml += '甲状腺' + bzItem.posSide + (bzItem.bzCount=='多发'?bzItem.bzCount:'') + bzItem.jg + '结节，TI-RADS '+bzItem.level+'类' + showScore;
              if(bzItem.levelAdvice) {
                bzHtml += '，' + bzItem.levelAdvice;
              }
              impressList.push(bzHtml);
            }
          })
        }
      }
      $("#jzx-pot,#jzx-iName").removeClass('rt-hide');
      $(".jzx-img,.bz-desc").show();
    } else {
      $("#jzx-pot,#jzx-iName").addClass('rt-hide');
      $(".jzx-img,.bz-desc").hide();
    }

    // 淋巴结做了检查
    if(checkLbj) {
      if(lbjInfoStr && lbjInfoStr !== '颈前部甲状腺周围未探及肿大淋巴结回声') {
        impressList.push(lbjInfoStr);
      }
      $("#lbj-pot").removeClass('rt-hide');
      $(".lbj-img").show();
    } else {
      $("#lbj-pot").addClass('rt-hide');
      $(".lbj-img").hide();
    }

    if(rtStructure) {
      if(!firstIn || !rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
        // rtStructure.impression = impressList.join('。\n') + (impressList.length?'。':'');
        rtStructure.impression = impressList.join('\n');
      } else {
        // rtStructure.impression = rtStructure.enterOptions.impression || '';
        var result = rtStructure.enterOptions.impression.replace(/\n\n(.*)/, "");
        rtStructure.impression = result || '';
      }
    }
    // let jzxLevStr = '（甲状腺分类标准：0类：临床疑似病例但超声无异常发现，需要追加其他检查。1类：阴性，超声检查未见异常声像改变。2类：检查所见为良性病变，但需要临床随访。3类：良性病变可能性大，恶性风险＜5%。4类：恶性风险5%-90%。4A类：恶性风险5%-10%；4B类：恶性风险10%-50%；4C类：恶性风险50%-90%。5类：提示癌的风险性＞90%。6类：细胞学检出癌细胞，确诊为癌。）';
    // rtStructure.impression += rtStructure.impression ? '\n\n' + jzxLevStr : '';
    // console.log('impression-->',rtStructure.impression);
  }
}
// 初始化canvas
function initCanvas() {
  canvasLbEle = document.querySelector("#lbjCanvas");
  lbjCtx = canvasLbEle.getContext("2d");
  lbjImage = document.querySelector("#lbjImage");
  lbjImage.onload = () => {
    lbjCtx.drawImage(lbjImage, 0, 0, 180, 180); // 绘制画布
    if($('#sejz-rt-131').is(":checked")) {
      var lbjHighData = drawLbAreaHandler();
      $("#lbj-pot").text(JSON.stringify(lbjHighData));
    }
  }
}

// 高亮淋巴结部位
function highLightOrgan(curOrgan) {
  var ctx = lbjCtx;
  ctx.save();
  // 挡住默认的黑色字体
  if(curOrgan.textCord && curOrgan.textCord.length) {
    ctx.beginPath();
    ctx.fillStyle = '#FFF';
    ctx.rect(curOrgan.textCord[0],curOrgan.textCord[1],10,10);
    ctx.fill();
  }
  
  var coords = curOrgan.coords;
  if(coords && coords.length) {
    // 高亮背景和边框
    ctx.strokeStyle = curOrgan.strokeStyle; // 填充线颜色
    ctx.fillStyle = curOrgan.fillStyle; // 填充区域颜色
    ctx.lineWidth = 2;
    ctx.beginPath();
    for (var i = 0; i < coords.length; i++) {
      ctx.lineTo(coords[i][0], coords[i][1]);
    }
    ctx.closePath();
    ctx.stroke();
    ctx.fill();
  }
  
  // 高亮部位文字
  if(curOrgan.textCord && curOrgan.textCord.length) {
    ctx.beginPath();
    ctx.fillStyle = curOrgan.strokeStyle;
    ctx.font="bold 9px Arial";
    ctx.textBaseline = "top";
    ctx.fillText(curOrgan.text, curOrgan.textCord[0], curOrgan.textCord[1]);
    ctx.restore();
  }
}

// 清除画布之后重绘
function cleanCanvas() {
  lbjCtx.clearRect(0, 0, 180, 180);
  lbjCtx.drawImage(lbjImage, 0, 0, 180, 180); // 绘制画布
}
