#dgem1 {
  width: 780px;
  position: relative;
  font-size: 14px;
  color: #000;
  background: #FFFFFF;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  margin: 0 auto;
}

/*--------字体相关---------*/
#dgem1 .fs-12 {
  font-size: 12px;
}
#dgem1 .fs-16 {
  font-size: 16px;
}
#dgem1 .fs-20 {
  font-size: 20px;
  line-height: 20px;
}

/*--------颜色类---------*/
#dgem1 .text-c1{
  color: #303133;
}
#dgem1 .echart-h {
  background: #191975;
}
#dgem1 .table-h {
  background: #D6DFF7;
  color: #333333!important;
}
#dgem1 .bg-gray {
  background: #EAEAEA;
}
#dgem1 .req-flag {
  color: #E64545;
}

/*--------宽度类---------*/
#dgem1 .wd-20 {
  width: 20px;
}
#dgem1 .wd-134 {
  width: 134px;
}
#dgem1 .wd-150 {
  width: 150px;
}
#dgem1 .wd-162 {
  width: 162px;
}
#dgem1 .wd-182 {
  width: 182px;
}

/*--------间距---------*/
#dgem1 .mt-8 {
  margin-top: 8px;
}
#dgem1 .mb-4 {
  margin-bottom: 4px;
}
#dgem1 .mb-12 {
  margin-bottom: 12px!important;
}
#dgem1 .mb-16 {
  margin-bottom: 16px!important;
}
#dgem1 .mb-24 {
  margin-bottom: 24px!important;
}
#dgem1 .ml-4 {
  margin-left: 4px!important;
}
#dgem1 .ml-12 {
  margin-left: 12px;
}
#dgem1 .clear-pad {
  padding-right: 0!important;
}

/*--------dgem报告页面---------*/
#dgem1 .dgem-header img {
  width: 100%;
}
#dgem1 .dgem-body {
  position: relative;
  padding: 0 50px;
  box-sizing: border-box;
}
#dgem1 .text-mid {
  text-align: center;
}
#dgem1 .header-title {
  font-size: 22px;
}
#dgem1 .con-flex {
  display: flex;
  justify-content: space-between;
}
#dgem1 .row-box {
  position: relative;
  padding: 8px 0;
  box-sizing: border-box;
}
#dgem1 .row-box::after {
  position: absolute;
  bottom: 0;
  content: "";
  width: 100%;
  height: 1px;
  background: #606266;
}
#dgem1 .row-item {
  min-width: 150px;
}
#dgem1 .data-box {
  position: relative;
}
#dgem1 .data-box::after {
  position: absolute;
  content: '';
  width: 100%;
  height: 1px;
  background: #606266;
  left: 0;
  bottom: -12px;
}
#dgem1 .remark-inp {
  width: 100%;
  resize: vertical;
  padding: 0 3px;
}
#dgem1 .empg-box {
  width: 680px;
  border: 1px solid #000000;
}
#dgem1 .box-header {
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-bottom: 1px solid #000000;
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
}
#dgem1 .dot-flag {
  position:absolute;
  left: 6px;
  top: 5px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
}
#dgem1 .echart-data {
  /* width: 290px;
  height: 290px; */
  width: 426px;
  height: 426px;
  margin: 4px auto 8px;
}
#dgem1 .echart-data img {
  width: 100%;
  height: 100%;
}
#dgem1 .table-data tr {
  color: #333333;
  font-weight: 400!important;
}
#dgem1 .table-data thead {
  height: 24px;
}
#dgem1 .table-data thead th {
  font-weight: normal;
}
#dgem1 .table-data thead th:first-child,#dgem1 .table-data tbody tr td:not(:first-child) {
  border-right: 1px solid #000;
}
#dgem1 .table-data thead th:not(:first-child),#dgem1 .table-data tbody tr td:not(:first-child) {
  border-bottom: 1px solid #000;
}
#dgem1 .table-data tbody tr td:first-child {
  line-height: 20px;
  border-right: 1px solid #000;
  text-align: left;
  padding: 0 10px 0 8px;
  box-sizing: border-box;
}
#dgem1 .table-data tbody tr:last-child td {
  border-bottom: none!important;
}
#dgem1 .table-data tbody tr td:last-child {
  border-right: none!important;
}
#dgem1 .table-data tbody tr td:not(:first-child) {
  position: relative;
  padding-right: 8px;
}
#dgem1 .no-tr {
  border-top: none!important;
  border-right: none!important;
}
#dgem1 .no-r {
  border-right: none!important;
}
#dgem1 .dgem-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 50px 43px;
  align-items: center;
}
.dgem-footer .sel-block {
  display: flex;
  align-items: center;
}
[isView="true"] .dgem-footer .sel-block.mt-8 {
  margin-top: 0!important;
}
[isView="true"] .dgem-footer img{
  max-width: unset!important;
  max-height: unset!important;
  width: 100px;
  height: 40px;
  object-fit: contain;
}
#dgem1 .serial-no {
  width: 150px;
  height: 16px;
  position: absolute;
  top: 35%;
  left: 526px;
  transform: rotate(-90deg);
  font-size: 12px;
}
[isView="true"] .exam-people, [isView="true"] .duty-nurse {
  display: none;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}