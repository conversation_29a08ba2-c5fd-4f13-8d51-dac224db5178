$(function() {
  window.initHtmlScript = initHtmlScript;
})
var mmUnit = 'mm', perUnit = '%',mDevSUnit = 'm/s', mmHgUnit = 'mmHg', geUnit = '个',sqcmUnit = 'cm^2';
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  // initTabContentHandler();
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }

  keydown_to_tab('ejb1');

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 获取描述
function getDescription() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let description = '';
  let strList = [];
  let blockStrList1 = [], blockStrList2 = [],blockStrList3 = [];
  let dxStr = '大小',PGStr = 'PG=',MGStr = 'MG=',hsStr = '回声附着',MVAStr = 'MVA';
  $('#ejb1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      blockStrList1.push(parData.val);
      if(pid === 'ejb-rt-18') {
        childD1.map(function(cItem1) {
          if(cItem1.name.includes(dxStr)) {
            blockStrList1.push('大小约' + cItem1.val + mmUnit);
          }else {
            blockStrList1.push(cItem1.val);
          }
        })
      }
      else if(pid === 'ejb-rt-21') {
        if(childD1.length) {
          blockStrList1.splice(blockStrList1.length - 1, 1, parData.val + childD1[0].val);
        }
      }
      else if(pid === 'ejb-rt-28') {
        // childD1.map(function(cItem1) {
        //   blockStrList1.push(cItem1.val + mmUnit);
        // })
        if(childD1.length) {
          blockStrList1.splice(blockStrList1.length - 1, 1, parData.val + childD1[0].val + mmUnit);
        }
      }
      else if(pid === 'ejb-rt-34') {
        let ychsArr = [];
        let ychsType = getVal('[name="yc-xz"]:checked') || '';  // 回声附着类型
        let ychsCount = curKeyValData['ejb-rt-39']?.val || ''; //回声附着个数
        ychsArr.push(ychsType + '回声附着' + (ychsCount ? ychsCount + '个' : ''));
        // 大小1和2
        let size1Arr = [], size2Arr = [], allSize = [];
        curKeyValData['ejb-rt-41']?.val && size1Arr.push(curKeyValData['ejb-rt-41'].val + mmUnit);
        curKeyValData['ejb-rt-42']?.val && size1Arr.push(curKeyValData['ejb-rt-42'].val + mmUnit);
        curKeyValData['ejb-rt-43']?.val && size2Arr.push(curKeyValData['ejb-rt-43'].val + mmUnit);
        curKeyValData['ejb-rt-44']?.val && size2Arr.push(curKeyValData['ejb-rt-44'].val + mmUnit);
        size1Arr.length && allSize.push(size1Arr.join(' * '));
        size2Arr.length && allSize.push(size2Arr.join(' * '));
        if(allSize.length) {
          ychsArr.push('大小' + allSize.join('，'));
        }
        if(ychsArr.length) {
          blockStrList1.splice(blockStrList1.length - 1, 1, parData.val + ychsArr.join('，'));
        }
        // childD1.map(function(cItem1) {
        //   let childD2 = cItem1.child || [];
        //   if(cItem1.name.includes(hsStr)) {
        //     blockStrList1.push(cItem1.val + childD2[0].val + geUnit);
        //   }else if(cItem1.name.includes(dxStr)) {
        //     let inpVal1 = $('#ejb-rt-41').val() || '',inpVal2 = $('#ejb-rt-42').val() || '',inpVal3 = $('#ejb-rt-43').val() || '',inpVal4 = $('#ejb-rt-44').val() || '';
        //     let inpArr1 = [],inpArr2 = [];
        //     let inpStr1 = '',inpStr2 = '';
        //     inpVal1 ? inpArr1.push(inpVal1) : '';
        //     inpVal2 ? inpArr1.push(inpVal2) : '';
        //     inpVal3 ? inpArr2.push(inpVal3) : '';
        //     inpVal4 ? inpArr2.push(inpVal4) : '';
        //     inpStr1 = inpArr1.length ? inpArr1.join('X') + mmUnit + '，' : '';
        //     inpStr2 = inpArr2.length ? inpArr2.join('X') + mmUnit : '';
        //     (inpStr1 || inpStr2) ? blockStrList1.push(cItem1.name + inpStr1 + inpStr2) : '';
        //   }else {
        //     blockStrList1.push(cItem1.val);
        //   }
        // })
      }
      else if(pid === 'ejb-rt-45') {
        let hszq = getVal('[name="hs-zq"]:checked') || '';
        let hsyd = getVal('[name="hs-yd"]:checked') || '';
        let hsArr = [];
        hszq && hsArr.push(hszq);
        hsyd && hsArr.push(hsyd);
        if(hsArr.length) {
          blockStrList1.splice(blockStrList1.length - 1, 1, parData.val + hsArr.join('，'));
        } else {
          blockStrList1.splice(blockStrList1.length - 1, 1);
        }
      }
      else if(pid === 'ejb-rt-53') {
        childD1.map(function(cItem1) {
          let childD2 = cItem1.child || [];
          if(cItem1.name.includes(PGStr)) {
            blockStrList1.push(cItem1.val + childD2[0].val + mmHgUnit);

          }else if(cItem1.name.includes(MGStr)) {
            blockStrList1.push(cItem1.val + childD2[0].val + mmHgUnit);
          }else {
            blockStrList1.push(cItem1.name);
          }
        })
      }
      else if(pid === 'ejb-rt-60') {
        childD1.map(function(cItem1) {
          let childD2 = cItem1.child || [];
          if(cItem1.name.includes(MVAStr)) {
            blockStrList1.push(cItem1.val + childD2[0].val + sqcmUnit);
          }
        })
      }
      else if(pid === 'ejb-rt-62') {
        let ssqtype = getVal('[name="ssqfl-type"]:checked') || '';
        let ssqbz = getVal('[name="ssqfl-bz"]:checked') || '';
        let szqArr = [];
        ssqtype && szqArr.push(ssqtype + (ssqbz?'':'反流'));
        ssqbz && szqArr.push(ssqbz);
        blockStrList1.splice(blockStrList1.length - 1, 1, parData.val + szqArr.join(''));
      }
      else {
        childD1.map(function(cItem1) {
          blockStrList1.push(cItem1.val);
        })
      }
    }
  })
  // strList.push(blockStrList1,blockStrList2,blockStrList3);
  // strList = strList.filter(function(item) { return item.length });
  description = blockStrList1.length ? ('二尖瓣' + blockStrList1.join('，') + '。') : '';
  // console.log('description-->',description);
  return description;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = getDescription();
  // rtStructure.impression = '';
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}