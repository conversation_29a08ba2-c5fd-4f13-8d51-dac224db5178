#wactdbz1 {
  height: 100%;
  color: #000;
  font-size: 14px;
  
  .custom-table {
    input[type="text"]{
      width: 96%;
    }
  }
  input {
    border-radius: 3px;
    border: 1px solid #C0C4CC;
    padding: 4px;
  }

  * {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
  }

  textarea {
    background: #FFFFFF;
    border-radius: 3px;
    border: 1px solid #C0C4CC;
    padding: 6px 12px;
  }
  .distantTransfer {
    display: flex;
    flex-direction: column;
  }
  .container {
    display: flex;
    flex-direction: column;
    height: 100%;

    .container-header {
      height: 40px;
      line-height: 40px;
      background: #E8F3FF;
      box-shadow: inset 1px 0px 0px 0px #C8D7E6, inset -1px -1px 0px 0px #C8D7E6;

      .pat-info {
        width: 960px;
        margin: 0 auto;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .container-content {
      flex: 1;
      overflow: auto;
      width: 960px;
      margin: 0 auto;
      background: #FFFFFF;
      border-right: 1px solid #DCDFE6;
      border-left: 1px solid #DCDFE6;

      .form {
        padding: 12px;

        .form-item {
          display: flex;
          margin-bottom: 20px;

          .form-item-label {
            width: 114px;
            text-align: right;
            color: #303133;
            line-height: 40px;
          }
          .form-item-label-preview{
            line-height: unset;
          }
          .form-item-content {
            flex: 1;
            background: #F5F7FA;
            border: 1px solid #DCDFE6;
            padding: 10px 8px;
          }

          .form-sub-item-label {
            width: 70px;
            text-align: right;
            color: #303133;
          }
        }
      }
    }

    .emvi-container {
      display: flex;
      height: 134px;
      .emvi-radio-group {
        display: flex;
        flex-direction: column;
        width: 128px;
        padding: 8px 0;
        .emvi-radio-group-item {
          padding: 0 8px;
          width: 100%;
        }
      }

      .emvi-radio-child {
        display: flex;
        flex-direction: column;
        padding:0 8px;
        flex: 1;
        background: #EBEEF5;
        border-left: 1px solid #C8D7E6;
        justify-content: space-around;
        label {
          display: flex;
          align-items: center;
          width: 100%;
          input {
            margin-right: 8px;
          }
        }
      }
    }

    .infringement {
      display: flex;

      .infringement-item {
        flex: 1.5;

        .infringement-item-radio {
          display: flex;
          align-items: center;
          padding: 2px;

          .infringement-item-radio-label {
            width: 60px;
            text-align: right;
            color: #303133;
          }

          .infringement-item-radio-content {}
        }
      }

      .infringement-item-child {
        display: flex;
        flex-direction: column;
        flex: 1;
        border-left: 1px solid #C8D7E6;
        padding: 8px;

        :last-child {
          flex: 1;
        }

        textarea {
          height: 100%;
          width: 100%;
        }
      }
    }

    .size-content {
      display: flex;
      padding: 0 !important;
      height: 134px;

      span {
        color: #303133;
      }

      .size-group {
        padding: 8px 0;

        .size-group-item {
          padding-left: 8px;

          &:checked {
            background: #F5F7FA;
          }
        }
      }

      .size-value {
        background: #EBEEF5;
        border-left: 1px solid #DCDFE6;

        padding: 8px 12px;

        .size-value-item {
          input {
            width: 60px;
          }
        }
      }
    }

    .density-group {
      display: flex;
      height: 104px;
      background: #F5F7FA;
      border: 1px solid #DCDFE6;

      .density-group-radio {
        display: flex;
        flex-direction: column;
        width: 240px;
        padding-top: 8px;

        label {
          width: 100%;
          padding-left: 8px;
        }
      }

      .density-group-toggleshow {
        padding: 8px;
        flex: 1;
        background-color: #EBEEF5;
        border-left: 1px solid #C8D7E6;
      }
    }

    .container-footer {
      height: 104px;
      background: #E8F3FF;
      border: 1px solid #DCDFE6;
      padding: 8px 0;

      .footer-impression {
        height: 100%;
        display: flex;
        width: 960px;
        margin: 0 auto;

        .impression-label {
          display: inline-block;
          font-size: 18px;
          font-weight: bold;
          // margin-left: 72px;
          // margin-right: 16px;
          width: 114px;
          text-align: right;
        }
        .impression-label-preview {
          text-align: center;
        }
      }
      .footer-impression-preview {
        border-left: 1px solid #DCDFE6;
        border-right: 1px solid #DCDFE6;
        padding: 8px;
      }
    }
    .container-footer-preview {
      background:#F5F7FA;
      padding: 0;
    }
  }

  .rt-sr-r {
    margin-right: 4px;
  }

  .pl-24 {
    padding-left: 24px;
  }

  .pr-24 {
    padding-right: 24px;
  }

  .pt-8 {
    padding-top: 8px;
  }

  .pb-8 {
    padding-bottom: 8px;
  }

  .mt-12 {
    margin-top: 12px;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .mt-6 {
    margin-top: 6px;
  }

  .mt-4 {
    margin-top: 4px;
  }

  .m-0-6 {
    margin: 0 6px;
  }

  .w-10 {
    width: 10px;
  }

  .w-15 {
    width: 15px;
  }

  .w-20 {
    width: 20px;
  }

  .w-25 {
    width: 25px;
  }

  .w-30 {
    width: 30px;
  }

  .w-35 {
    width: 35px;
  }

  .w-40 {
    width: 40px;
  }

  .w-45 {
    width: 45px;
  }

  .w-50 {
    width: 50px;
  }

  .w-55 {
    width: 55px;
  }

  .w-60 {
    width: 60px;
  }

  .w-65 {
    width: 65px;
  }

  .w-70 {
    width: 70px;
  }

  .w-75 {
    width: 75px;
  }

  .w-80 {
    width: 80px;
  }

  .w-85 {
    width: 85px;
  }

  .w-90 {
    width: 90px;
  }

  .w-95 {
    width: 95px;
  }

  .w-100 {
    width: 100px;
  }

  .w-105 {
    width: 106px;
  }

  .w-110 {
    width: 110px;
  }

  .w-115 {
    width: 115px;
  }

  .w-120 {
    width: 120px;
  }

  .w-125 {
    width: 125px;
  }

  .w-130 {
    width: 130px;
  }

  .w-135 {
    width: 135px;
  }

  .w-140 {
    width: 140px;
  }

  .w-145 {
    width: 145px;
  }

  .w-150 {
    width: 150px;
  }

  .w-155 {
    width: 155px;
  }

  .w-160 {
    width: 160px;
  }

  .w-165 {
    width: 165px;
  }

  .f-1 {
    flex: 1;
  }

  .f-1-5 {
    flex: 1.5;
  }

  .f-1-6 {
    flex: 1.6;
  }

  .f-2 {
    flex: 2;
  }

  .f-3 {
    flex: 3;
  }

  .fw-600 {
    font-weight: 600;
  }

  .a-center {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .a-start {
    display: flex;
    align-items: flex-start;
  }

  .flex {
    display: flex;

    span {
      &:first-child {
        white-space: nowrap;
      }
    }
  }

  .flex-column {
    display: flex;
    flex-direction: column;
  }

  .text-r {
    text-align: right;
  }

  .fs-36 {
    font-size: 36px;
  }

  .fs-24 {
    font-size: 24px;
  }

  .fs-20 {
    font-size: 20px;
  }

  .mr-20 {
    margin-right: 20px;
  }
  .lbj-preview-label{
    width: 130px;
    text-align: right;
    color: #303133;
  }
}