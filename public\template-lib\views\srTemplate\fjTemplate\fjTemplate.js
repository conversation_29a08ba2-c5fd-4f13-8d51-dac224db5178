$(function() {
  window.initHtmlScript = initHtmlScript;
})
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  // initTabContentHandler();
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 获取诊断/印象
function getImpression() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let impression = '';
  let blockStrList1 = [];
  curElem.find('.one-line').each(function(index, line) {
    let lineList = [];
    let id17Dh = true;
    $(line).find('.rt-sr-w:not([pid])').each(function(pIdx, par) {
      let parent = $(par);
      let pid = parent.attr('id');
      let nochildArr = ['fj-rt-12', 'fj-rt-13', 'fj-rt-14', 'fj-rt-15', 'fj-rt-16', 'fj-rt-60'];
      if(curKeyValData[pid]) {
        let parData = curKeyValData[pid];
        let childD1 = parData.child || [];
        // pid !== 'fj-rt-17' ? blockStrList1.push(parData.name) : '';
        if(nochildArr.indexOf(pid) > -1) {
          id17Dh = false;
          lineList.push(parData.val);
        }
        childD1.map(function(cItem1) {
          let preName = pid !== 'fj-rt-17' ? parData.val : '';
          if(pid === 'fj-rt-17' && !id17Dh) {
            // 前面无需加逗号
            lineList.splice(lineList.length - 1, 1, lineList[lineList.length - 1] + cItem1.val);
          } else {
            lineList.push(preName + cItem1.val);
          }
        })
      }
    });
    if(lineList.length) {
      blockStrList1.push(lineList.join('，') + '。');
    }
  })
  impression = blockStrList1.length ? blockStrList1.join('\n'): '';
  return impression;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  // rtStructure.description = '';
  rtStructure.impression = getImpression();
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}