/* 间距 */
.ml-14 {
  margin-left: 14px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-24 {
  margin-left: 24px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-12 {
  margin-bottom: 12px;
}
/* 宽度 */
.wd-200 {
  width: 200px;
}
#hbettap1 {
  width: 100%;
  min-height: 100%;
  font-family: '宋体';
  font-size: 16px;
  color: #000;
  background: #F5F7FA;
  padding: 8px 20px 8px 24px;
  overflow: auto;
}
#hbettap1 * {
  font-family: '宋体';
}
#hbettap1 input[type="checkbox"] {
  vertical-align: middle;
}
#hbettap1 .txtarea {
  width: 100%;
  resize: vertical;
  padding: 9px 11px;
  font-size: 16px;
}
/* 编辑页面 */
#hbettap1 .con-flex {
  display: flex;
}
#hbettap1 .flex-item {
  flex: 1;
}
#hbettap1 .inp-sty {
  background: #FFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 11px;
  font-size: 16px;
}
[isview="true"] #hbettap1 .hbettap-edit {
  display: none;
}
[isview="true"] #hbettap1 .hbettap-view {
  display: block;
}
/* 预览页面 */
#hbettap1 .hbettap-view {
  display: none;
  position: relative;
  margin: 0 auto;
  width: 780px;
  min-height: 1100px;
  background: #FFF;
  border: 1px solid #DCDFE6;
  padding: 37px 56px;
}
#hbettap1 .hbettap-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#hbettap1 .hbettap-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}
#hbettap1 .hbettap-view .hos-tit {
  font-weight: 900;
  font-size: 36px;
  line-height: 50px;
  text-align: center;
}
#hbettap1 .hbettap-view .sub-tit {
  font-weight: bold;
  font-size: 24px;
  line-height: 35px;
  text-align: center;
}
#hbettap1 .hbettap-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
}
#hbettap1 .hbettap-view .logo-tit .code img {
  width: 122px;
  height: 36px;
}
#hbettap1 .hbettap-view .logo-tit .code span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
}
#hbettap1 .hbettap-view .blh-tit {
  font-weight: bold;
  font-size: 16px;
  line-height: 23px;
  text-align: right;
}
#hbettap1 .hbettap-view .patient-info {
  margin-top: 8px;
  padding: 8px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
}
#hbettap1 .hbettap-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  padding: 24px 0 0 34px;
}
#hbettap1 .hbettap-view .item-img {
  width: 280px;
  height: 210px;
  border: 1px solid #eee;
  margin-right: 34px;
  margin-bottom: 8px;
}
#hbettap1 .hbettap-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#hbettap1 .hbettap-view .p-item + .p-item {
  margin-top: 4px!important;
}
#hbettap1 .hbettap-view .p-item .info-i {
  color: #000;
}
#hbettap1 .hbettap-view .item-lb {
  font-weight: bold;
}
#hbettap1 .hbettap-view .item-con {
  white-space: pre-line;
  word-break: break-word;
}
#hbettap1 .hbettap-view .report-wrap {
  padding: 8px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hbettap1 .hbettap-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  line-height: 32px;
}
#hbettap1 .hbettap-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}
#hbettap1 .hbettap-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
}