.jxhem1-pg {padding: 12px 0;}
#jxhem1 {
  font-size: 14px;
  width: 780px;
  min-height: 100%;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0px 4px 6px 0px rgba(48, 45, 45, 0.2);
  position: relative;
  padding-bottom: 70px;
}
#jxhem1 .jxh-logo, #jxhem1 .jxh-logo img {
  width: 100%;
}
#jxhem1 .jxh-head {
  padding: 20px 30px 16px 30px;
  text-align: center;
}
#jxhem1 .jxh-head .lg-tit {
  font-weight: 600;
  color: #000;
  font-size: 26px;
  margin-bottom: 12px;
}
#jxhem1 .jxh-head .sm-tit {
  color: #000;
  font-size: 20px;
}
#jxhem1 .jxh-body {
  padding: 0 30px;
}
#jxhem1 .jxh-body .pat-info {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-top: 1px solid #F2F2F2;
  border-bottom: 1px solid #F2F2F2;
  font-size: 16px;
  color: #000;
}
#jxhem1 .jxh-body .jxh-rpt-img {
  width: 100%;
  height: 250px;
  text-align: center;
  border: 1px solid #ddd;
  margin-top: 12px;
}
#jxhem1 .jxh-body .jxh-rpt-img img {
  /* width: 98%; */
  height: 98%;
  object-fit: contain;
}
#jxhem1 .bold-t {
  font-weight: bold;
}
#jxhem1 .blue {
  color: #1885F2;
}
#jxhem1 .red {
  color: #F56C6C;
}
#jxhem1 table {
  table-layout: fixed;
}
#jxhem1 input, #jxhem1 select{
  width: 100%;
  min-width: unset;
}
#jxhem1 .jxh-body .detail-table {
  border-collapse: collapse;
  border: 1px solid #C8D7E6;
  width: 100%;
  font-size: 14px;
  margin-top: 12px;
  text-align: center;
  color: #303133;
}
#jxhem1 .jxh-body .detail-table .v-l {
  text-align: left;
}
[isview='true'] #jxhem1 .jxh-body .detail-table .v-l {
  text-align: center;
}
#jxhem1 .detail-table tr td {
  padding: 3px 8px;
  height: 26px;
}
#jxhem1 .detail-table td:first-child {
  background: #F2F6FC;
  color: #606266;
}
#jxhem1 .impress-wrap, 
#jxhem1 .advise-wrap {
  position: relative;
  padding-left: 48px;
  margin-top: 12px;
}
#jxhem1 .jxh-body .l-label {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}
#jxhem1 .jxh-body .r-wrap {
  width: 100%;
}
#jxhem1 .impress-wrap table {
  border-collapse: collapse;
  border: 1px solid #C8D7E6;
  width: 100%;
  font-size: 14px;
  text-align: center;
  color: #303133;
}
#jxhem1 .impress-wrap table td {
  padding: 3px 10px;
}
#jxhem1 .impress-wrap tr td:nth-child(odd) {
  background: #F2F6FC;
  color: #606266;
}
#jxhem1 .input-tip {
  color: #999;
}
#jxhem1 .jxh-foot {
  position: absolute;
  bottom: 20px;
  left: 30px;
  right: 30px;
  height: 40px;
  padding-top: 16px;
  border-top: 1px solid #F2F2F2;
  display: flex;
  justify-content: space-between;
}
#jxhem1 .jxh-foot .f-item {
  flex: 1;
  font-size: 16px;
  font-size: #000;
}
#jxhem1 .jxh-foot .f-item + .f-item {
  margin-left: 14px;
}
#jxhem1 .l-b {
  color: #555;
}
[isview='true'] #jxhem1 .view-none {
  display: none;;
}
[isview='true'] #jxhem1 .jxh-rpt-img {
  border: none;
}
/* 下拉选择项 */
#jxhem1 .in-drop {
  position: relative;
}
#jxhem1 .in-drop input {
  padding-right: 18px;
}
#jxhem1 .in-drop.inb {
  display: inline-block;
  width: 48%;
}
[isview='true'] #jxhem1 .in-drop.inb {
  display: block;
  width: 100%;
}
#jxhem1 .in-drop .iarr {
  position: absolute;
  top: 4px;
  right: 8px;
  width: 8px;
  height: 8px;
  z-index: 9;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg);
}
[isview='true'] #jxhem1 .in-drop .iarr, [isview='true'] #jxhem1 .multi-w {
  display: none;
}
/* 多选 */
#jxhem1 .multi-w {
  display: inline-block;
  width: 100%;
  height: 28px;
  border: 1px solid #DCDFE6;
  padding: 0 18px 0 0;
  border-radius: 3px
}
#jxhem1 .multi-w + input {
  display: none;
}
#jxhem1 .in-drop.inb .iarr {
  top: 6px;
}
#jxhem1 xm-select {
  height: 100%;
  min-height: unset;
  line-height: 26px;
  border: none;
}
#jxhem1 xm-select .xm-icon,
#jxhem1 xm-select .xm-label-block {
  display: none;
}
#jxhem1 xm-select .xm-label {
  right: 0;
}
#jxhem1 xm-select .label-content {
  padding: 0;
  line-height: 26px;
}
[isview="true"] #jxhem1 .paste-wrap {
  display: none;
}
[isview="true"] #jxhem1 .paste-wrap {
  border: none;
}