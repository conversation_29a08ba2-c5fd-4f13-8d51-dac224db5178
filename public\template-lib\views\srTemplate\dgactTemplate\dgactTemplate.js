$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var isFillInStatus1 = {}, isFillInStatus2 = {}, isFillInStatus3 = {};
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过
var raidoArr1 = [], radioArr2 = [], textareaIdArr = [];
var radioNameArr = ['mjmzgxs', 'mjmzgas', 'zzhbxs', 'zzhbas', 'zzszxs', 'zzszas', 'znzxs', 'znzas', 'zwzxs', 'zwzas', 'yzxs', 'yzas', 'yqzxs', 'yqzas',
  'yhzxs', 'yhzas', 'pjmxs', 'pjmas', 'cxmxs', 'cxmas', 'gzjmxs', 'gzjmas', 'gjmzxs', 'gjmzas', 'gyjmxs', 'gyjmas', 'zszxs', 'zszas', 'szzxs',
  'szzas', 'yszxs', 'yszas', 'yxgjmxs', 'yxgjmas', 'gsdxs', 'gsdas', 'gpmxs', 'gpmas', 'ghdxs', 'ghdas', 'gxdxs', 'gxdas','gzdmxs','gzdmas',
  'grdmxs','grdmas','ggdmxs','ggdmas']
var gzfd = {
  s1: '尾状叶',
  s2: '左外叶上段',
  s3: '左外叶下段',
  s4: '左内叶',
  s5: '右前叶下段',
  s6: '右后叶下段',
  s7: '右后叶上段',
  s8: '右前叶上段',
}
var mjmxtArr = [
  ['mjmzgbzfj', 'mjmzgsq', 'mjmzgxs', 'mjmzgyxs', 'mjmzgas', 'mjmzgyas'], //门静脉主干
  ['zzhbbzfj', 'zzhbsq', 'zzhbxs', 'zzhbyxs', 'zzhbas', 'zzhbyas'],//左支横部
  ['zzszbzfj', 'zzszsq', 'zzszxs', 'zzszyxs', 'zzszas', 'zzszyas'],//左支矢状部
  ['znzbzfj', 'znzsq', 'znzxs', 'znzyxs', 'znzas', 'znzyas'],//左内支
  ['zwzbzfj', 'zwzsq', 'zwzxs', 'zwzyxs', 'zwzas', 'zwzyas'],//左外支
  ['yzbzfj', 'yzsq', 'yzxs', 'yzyxs', 'yzas', 'yzyas'],//右支
  ['yqzbzfj', 'yqzsq', 'yqzxs', 'yqzyxs', 'yqzas', 'yqzyas'],//右前支
  ['yhzbzfj', 'yhzsq', 'yhzxs', 'yhzyxs', 'yhzas', 'yhzyas'],//右后支
  ['pjmbzfj', 'pjmsq', 'pjmxs', 'pjmyxs', 'pjmas', 'pjmyas'],//脾静脉
  ['cxmbzfj', 'cxmsq', 'cxmxs', 'cxmyxs', 'cxmas', 'cxmyas'],//肠系膜上静脉
]
var gjmArr = [
  ['gzjmbzfj', 'gzjmsq', 'gzjmxs', 'gzjmyxs', 'gzjmas', 'gzjmyas'],//肝左静脉
  ['gjmzbzfj', 'gjmzsq', 'gjmzxs', 'gjmzyxs', 'gjmzas', 'gjmzyas'],//肝中静脉
  ['gyjmbzfj', 'gyjmsq', 'gyjmxs', 'gyjmyxs', 'gyjmas', 'gyjmyas'],//肝右静脉
  ['zszbzfj', 'zszsq', 'zszxs', 'zszyxs', 'zszas', 'zszyas',],//肝左静脉属支
  ['szzbzfj', 'szzsq', 'szzxs', 'szzyxs', 'szzas', 'szzyas',],//肝中静脉属支
  ['yszbzfj', 'yszsq', 'yszxs', 'yszyxs', 'yszas', 'yszyas',],//肝右静脉属支
  ['yxgjmbzfj', 'yxgjmsq', 'yxgjmxs', 'yxgjmyxs', 'yxgjmas', 'yxgjmyas'],//肝右下副肝静脉
]
var xqjmArr = [
  ['gsdbzfj', 'gsdsq', 'gsdxs', 'gsdyxs', 'gsdas', 'gsdyas'],//肝上段
  ['gpmbzfj', 'gpmsq', 'gpmxs', 'gpmyxs', 'gpmas', 'gpmyas'], //第二肝门平面
  ['ghdbzfj', 'ghdsq', 'ghdxs', 'ghdyxs', 'ghdas', 'ghdyas'],//肝后段
  ['gxdbzfj', 'gxdsq', 'gxdxs', 'gxdyxs', 'gxdas', 'gxdyas'],//肝下段
]
var gdmArr = [
  ['gzdmbzfj','gzdmsq','gzdmxs','gzdmyxs','gzdmas','gzdmyas'],//肝左动脉
  ['grdmzfj','grdmsq','grdmxs','grdmyxs','grdmas','grdmyas'],//肝右动脉
  ['ggdmzfj','ggdmsq','ggdmxs','ggdmyxs','ggdmas','ggdmyas'],//肝固有动脉
]
var veinNameData = {
  gdmName: ['肝左动脉', '肝右动脉', '肝固有动脉'],
  mjmName: ['门静脉主干', '左支横部', '左支矢状部', '左内支', '左外支', '右支', '右前支', '右后支', '脾静脉', '肠系膜上静脉'],
  gjmName: ['肝左静脉', '肝中静脉', '肝右静脉', '肝左静脉属支', '肝中静脉属支', '肝右静脉属支', '右下副肝静脉'],
  xqjmName: ['肝上段', '第二肝门平面', '肝后段', '肝下段'],
}
var yfzdxcl = '', zzdxcls = '',yfzsydg1 = '',yfzsydg2 = '',zzsydg1 = '',zzsydg2 = '',yfzxydg1 = '',yfzxydg2 = '',zzxydg1 = '',zzxydg2 = ''
var yfzdq1 = '',yfzdq2 = '',zzdq1 = '',zzdq2 = '',yfzjs01 = '',yfzjs02 = '',zzjs01 = '',zzjs02 = ''
var descriptionStr = '';
var mjmxtStrList = [], gjmStrList = [], xqjmStrList = [], gdmStrList = [];
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#dgact1"),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    initPage();
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView();
    } else {
    }
  }
}

function initView() {
  // console.log('idAndDomMap',idAndDomMap);
  showGmx($('#dga-rt-818').val(),true);
  let bracketArr1 = [], showArr1 = [];
  // bracketArr1.push(initId('132'));
  bracketArr1.push(initId('507'));
  bracketArr1.push(initId('511'));
  bracketArr1.push(initId('513'));
  bracketArr1.push(initId('515'));
  bracketArr1.push(initId('525'));
  bracketArr1.push(initId('527'));
  bracketArr1.push(initId('529'));
  bracketArr1.push(initId('531'));
  bracketArr1.push(initId('533'));
  bracketArr1.push(initId('535'));
  bracketArr1.push(initId('545'));
  bracketArr1.push(initId('547'));
  bracketArr1.push(initId('555'));
  bracketArr1.push(initId('557'));
  bracketArr1.push(initId('559'));
  bracketArr1.push(initId('561'));
  bracketArr1.push(initId('563'));
  bracketArr1.push(initId('590'));
  bracketArr1.push(initId('593'));
  for (let i = 0; i < bracketArr1.length; i++) {
    let val = $('#' + bracketArr1[i]).val();
    if (val) {
      let rtsc = $('#' + bracketArr1[i]).attr('rt-sc');
      let rtscArr = rtsc.split(';');
      let nameVal = ''
      rtscArr.forEach(item => {
        let [key, value] = item.split(':');
        if (key === 'name') {
          nameVal = value;
        }
      })
      $('#' + bracketArr1[i]).parent().html(`${nameVal}（${val}）`)
    }
  }
  curElem.find('.bracket').each(function (i, dom) {
    let val = $(dom).find('.rt-sr-w').val();
    val ? $(dom).html(`（${val}）`) : ''
  })
  showArr1.push(initId('485'));
  showArr1.push(initId('489'));
  showArr1.push(initId('493'));
  showArr1.push(initId('497'));
  showArr1.forEach(obj => {
    let val = getVal($(`[id="${obj}"]:checked`)) || '';
    val ? $('#' + obj).parent().parent().show() : $('#' + obj).parent().parent().hide()
  })
  $('#dga-rt-586').val() ? '' : $('#dga-rt-586').parent().html('无');
  let gdmViewStrList = forJmArrFun3(gdmStrList, 'gdmName');
  if(gdmViewStrList.length > 0){
    $('.gdm-view').html(gdmViewStrList.map(item => `<div>${item}</div>`))
  }
  let mjmViewStrList = forJmArrFun3(mjmxtStrList, 'mjmName');
  let mmzz = '', mmyz = '', sz = '';
  let jmhbArr = [], line = []
  if (mjmViewStrList[1] || mjmViewStrList[2] || mjmViewStrList[3] || mjmViewStrList[4]) {
    jmhbArr = mjmViewStrList.slice(1, 5);
    jmhbArr = jmhbArr.filter(item => item);
    mmzz = `<span style="font-weight: 600">门脉左支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  if (mjmViewStrList[5] || mjmViewStrList[6] || mjmViewStrList[7]) {
    jmhbArr = mjmViewStrList.slice(5, 8);
    jmhbArr = jmhbArr.filter(item => item);
    mmyz = `<span style="font-weight: 600">门脉右支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  mjmViewStrList[0] ? line.push(mjmViewStrList[0]) : '';
  mmzz ? line.push(mmzz) : '';
  mmyz ? line.push(mmyz) : '';
  mjmViewStrList[8] ? line.push(mjmViewStrList[8]) : '';
  mjmViewStrList[9] ? line.push(mjmViewStrList[9]) : '';
  if (line.length > 0) {
    $('.mjm-view').html(line.map(item => `<div>${item}</div>`))
  }
  line = [];
  //=================================================================================
  // console.log('>>>gjmStrList', gjmStrList);
  let gjmViewStrList = forJmArrFun3(gjmStrList, 'gjmName');
  gjmViewStrList[0] ? line.push(gjmViewStrList[0]) : '';
  gjmViewStrList[1] ? line.push(gjmViewStrList[1]) : '';
  gjmViewStrList[2] ? line.push(gjmViewStrList[2]) : '';
  if (gjmViewStrList[3] || gjmViewStrList[4] || gjmViewStrList[5]) {
    jmhbArr = gjmViewStrList.slice(3, 6);
    jmhbArr = jmhbArr.filter(item => item);
    sz = `<span style="font-weight: 600">属支</span>（${jmhbArr.join('；')}）`
    jmhbArr = []
  }
  sz ? line.push(sz) : '';
  gjmViewStrList[6] ? line.push(gjmViewStrList[6]) : '';
  if (line.length > 0) {
    $('.gjm-view').html(line.map(item => `<div>${item}</div>`))
  }
  line = [];
  //=================================================================================
  let xqjmViewStrList = forJmArrFun3(xqjmStrList, 'xqjmName');
  // console.log('>>>xqjmStrList', xqjmStrList);
  if(xqjmViewStrList.length > 0){
    $('.xqjm-view').html(xqjmViewStrList.map(item => `<div>${item}</div>`))
  }
  var zzVal = getVal('[id="dga-rt-17"]:checked') || '';
  if(!zzVal) {
    $('.zdShow').hide()
    $('.zz-block').css('cssText', 'display: none !important');
  }
  var msval = $('#dga-rt-505').val() || '';
  if(msval){
    $('.view-none1').show()
  }
  var fmqdzh = getVal($('input[type=checkbox][name=fmqdzh]:checked')) || ''; //增厚位置
  if(!fmqdzh) {
    $('.qdzh-show').hide()
  }
  var ygqk = $('#dga-rt-564').val() || '';
  if(!ygqk){
    $('.ygqk-show').hide()
  }
  var yfzData = ['dga-rt-20','dga-rt-21','dga-rt-22','dga-rt-23','dga-rt-24','dga-rt-25','dga-rt-26','dga-rt-27']
  var yfzHtml = []
  yfzData.forEach(item => {
    if(idAndDomMap[item].value){
      yfzHtml.push(idAndDomMap[item].value)
    }
  })
  $('.yfz-view').html(yfzHtml.join(',')+'段')
  var zzData = ['dga-rt-75','dga-rt-76','dga-rt-77','dga-rt-78','dga-rt-79','dga-rt-80','dga-rt-81','dga-rt-82']
  var zzHtml = []
  zzData.forEach(item => {
    if(idAndDomMap[item].value){
      zzHtml.push(idAndDomMap[item].value)
    }
  })
  $('.zz-view').html(zzHtml.join(',')+'段')
  $('.yfzdxcl-view').html(yfzdxcl)
  $('.zzdxcl-view').html(zzdxcls)
  $('.yfz-sydg').html(yfzsydg1)
  $('.yfz-gm-sydg').html(yfzsydg2)
  $('.zz-sydg').html(zzsydg1)
  $('.zz-gm-sydg').html(zzsydg2)
  $('.yzf-dgdq').html(yfzdq1)
  $('.yfz-dq').html(yfzdq2)
  $('.zz-dgdq').html(zzdq1)
  $('.zz-dq').html(zzdq2)
  $('.yfz-dgjs').html(yfzjs01)
  $('.yfz-js').html(yfzjs02)
  $('.zz-dqjs').html(zzjs01)
  $('.zz-js').html(zzjs02)
  $('.yfz-xydg').html(yfzxydg1)
  $('.yfz-mb-xydg').html(yfzxydg2)
  $('.zz-xydg').html(zzxydg1)
  $('.zz-mb-xydg').html(zzxydg2)
}

function forJmArrFun3(list, type) {
  let propName = ['与病灶分界', '受侵', '血栓', '', '癌栓', ''];
  let veinNames = [];
  veinNames = veinNameData[type];
  var jmarrData = {
    gdmName: [[], [], []],
    mjmName: [[], [], [], [], [], [], [], [], [], []],
    gjmName: [[], [], [], [], [], [], []],
    xqjmName: [[], [], [], [],],
  }
  let tempResults = jmarrData[type];
  for (let i = 0; i < list.length; i++) {
    for (let j = 0; j < propName.length; j++) {
      let val = list[i][j] || '';
      let prop = propName[j];
      if (val && prop !== '') {
        tempResults[i].push(prop + '：' + val);
      }
      if (j === 3 && val) {
        tempResults[i][j - 1] = tempResults[i][j - 1] + '（可见' + val + '充盈缺损影，增强未见强化）';
      }
      if (j === 5 && val ) {
        tempResults[i][j - 1] = tempResults[i][4 - 1] + '（可见' + val + '充盈缺损影，增强可见强化）';
      }
    }
  }
  let strList = [];
  for (let t = 0; t < tempResults.length; t++) {
    if (tempResults[t].length > 0) {
      if (type === 'mjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        t === 0 || t === 8 || t === 9 ?
          strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`) :
          strList.push(`${veinNameData[type][t]}（${tempResults[t].join('；')}）`)
      }
      if (type === 'gjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        t !== 3 && t !== 4 && t !== 5 ?
          strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`) :
          strList.push(`${veinNameData[type][t]}（${tempResults[t].join('；')}）`)
      }
      if (type === 'xqjmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`)
      }
      if (type === 'gdmName') {
        let stst = tempResults[t].filter(item => item !== '癌栓：有')
        tempResults[t] = stst
        strList.push(`<span style="font-weight: 600;">${veinNameData[type][t]}</span>（${tempResults[t].join('；')}）`)
      }

    } else {
      strList.push('')
    }
  }
  // console.log('>>>strList', strList);
  return strList;
}

// 初始化数据
function initPage() {
  var inp1Arr = [], inp2Arr = [], inp3Arr = [];
  inp1Arr.push(initId('149'))
  inp1Arr.push(initId('150'))
  inp1Arr.push(initId('151'))
  inp1Arr.push(initId('152'))
  inp1Arr.push(initId('153'))
  inp1Arr.push(initId('301'))
  inp1Arr.push(initId('302'))
  inp1Arr.push(initId('303'))
  inp1Arr.push(initId('304'))
  inp1Arr.push(initId('305'))
  inp1Arr.push(initId('407'))
  inp1Arr.push(initId('408'))
  inp1Arr.push(initId('409'))
  inp1Arr.push(initId('410'))
  inp2Arr.push(initId('16'))
  inp2Arr.push(initId('17'))
  inp3Arr.push(initId('168'))
  inp3Arr.push(initId('169'))
  inp3Arr.push(initId('170'))
  inp3Arr.push(initId('171'))
  inp3Arr.push(initId('228'))
  inp3Arr.push(initId('229'))
  inp3Arr.push(initId('230'))
  inp3Arr.push(initId('348'))
  inp3Arr.push(initId('349'))
  inp3Arr.push(initId('350'))
  raidoArr1.push(initId('129'))
  raidoArr1.push(initId('130'))
  raidoArr1.push(initId('131'))
  radioArr2.push(initId('45'))
  radioArr2.push(initId('100'))
  radioArr2.push(initId('468'))
  radioArr2.push(initId('484'))
  radioArr2.push(initId('517'))
  radioArr2.push(initId('537'))
  radioArr2.push(initId('549'))
  radioArr2.push(initId('635'))
  radioArr2.push(initId('646'))
  radioArr2.push(initId('742'))
  radioArr2.push(initId('668'))
  radioArr2.push(initId('753'))
  radioArr2.push(initId('761'))
  radioArr2.push(initId('775'))
  radioArr2.push(initId('783'))
  radioArr2.push(initId('654'))
  radioArr2.push(initId('676'))
  textareaIdArr.push(initId('510'))
  textareaIdArr.push(initId('512'))
  textareaIdArr.push(initId('514'))
  textareaIdArr.push(initId('524'))
  textareaIdArr.push(initId('526'))
  textareaIdArr.push(initId('528'))
  textareaIdArr.push(initId('530'))
  textareaIdArr.push(initId('532'))
  textareaIdArr.push(initId('534'))
  textareaIdArr.push(initId('544'))
  textareaIdArr.push(initId('546'))
  textareaIdArr.push(initId('554'))
  textareaIdArr.push(initId('556'))
  textareaIdArr.push(initId('558'))
  textareaIdArr.push(initId('560'))
  textareaIdArr.push(initId('562'))
  textareaIdArr.push(initId('589'))
  textareaIdArr.push(initId('592'))

  // if (!isSavedReport) {
  //   inp1Arr.forEach(item => {
  //     isFillInStatus1[item] = '1'
  //   })
  // }
  inp1Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus1[item] = '1' : '';
  })

  inp2Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus2[item] = '1' : '';
  })

  inp3Arr.forEach(item => {
    $('#' + item).is(':checked') ? isFillInStatus3[item] = '1' : '';
  })

  radioArr2.forEach(item => {
    $('#' + item).is(':checked') ? $('#' + item + '-cont').show() : $('#' + item + '-cont').hide();
  })
  let yfzdxclSet = getVal($('input[type=radio][name=dxcl]:checked'))
  $('.zkx-t').hide();
  $('.gbpr-t').hide();
  $('.dglx-t').hide();
  $('.hhx-t').hide();
  if(yfzdxclSet === '肿块型') $('.zkx-t').show();
  if(yfzdxclSet === '管壁浸润型') $('.gbpr-t').show();
  if(yfzdxclSet === '导管内型') $('.dglx-t').show();
  if(yfzdxclSet === '混合型') $('.dglx-t').show();

  let zzdxclSet = getVal($('input[type=radio][name=zzdxcl]:checked'))
  $('.zzzkx-t').hide();
  $('.zzgbpr-t').hide();
  $('.zzdglx-t').hide();
  $('.zzhhx-t').hide();
  if(zzdxclSet === '肿块型') $('.zzzkx-t').show();
  if(zzdxclSet === '管壁浸润型') $('.zzgbpr-t').show();
  if(zzdxclSet === '导管内型') $('.zzdglx-t').show();
  if(zzdxclSet === '混合型') $('.zzhhx-t').show();
  
  // $('.g-item').find('.block-ck1')
  showGmx($('#dga-rt-818').val());
  changeBzSmText();
  showGyh();
  showRadioInfo('init');
  showTextarea('init');
  initClickFun();
  getTipText();
  curElem.find('#dgact1 .body-wrap .rt-sr-w').change(function () {
    getTipText();
  })
}

function getTipText() {
  let str = '', str2 = '', str3 = '';
  let impressionArr = [], impressionArr1 = [], impressionArr2 = [];
  // 原发灶
  var yfzVal = getVal('[id="dga-rt-16"]:checked') || '';
  var yfbzsm = getVal($('input[type=radio][name=bzsm]:checked')) || '';//数目
  var yfzwz = getVal($('input[type=checkbox][name=yfzwz]:checked')) || '';//位置
  var yfzwzdz = getVal($('input[type=checkbox][name=yfzwzdz]:checked')) || '';//位置
  var yfzdzgwz = getVal($('input[type=checkbox][name=yfzdzgwz]:checked')) || '';//位置
  var yfzqf = getVal($('input[type=checkbox][name=yfzqf]:checked')) || '';//侵犯
  // var yfzdx1 = $('#dga-rt-28').val();//大小
  // var yfzdx2 = $('#dga-rt-29').val();//大小
  var dxcl = getVal($('input[type=radio][name=dxcl]:checked')) || '';//大小测量
  var yfzxz = getVal($('input[type=radio][name=yfzxz]:checked')) || '';//形状
  var yfzfx = getVal($('input[type=radio][name=yfzfx]:checked')) || '';//分型
  var yfzbj = getVal($('input[type=radio][name=yfzbj]:checked')) || '';//边界
  var yfzpsmd = getVal($('input[type=radio][name=yfzpsmd]:checked')) || '';//平扫密度
  var yfzpsmd2 = getVal($('input[type=radio][name=yfzpsmd2]:checked')) || '';//平扫密度是否均匀
  var yfzpsmdbj = getVal($('input[type=radio][name=yfzpsmdbj]:checked')) || '';//平扫密度不均匀
  var yfzbjyqt = $('#dga-rt-49').val();//平扫密度不均匀其他填写
  var yfzzqdmwq = getVal($('input[type=radio][name=yfzzqdmwq]:checked')) || '';//动脉晚期
  var yfzzqmjmq = getVal($('input[type=radio][name=yfzzqmjmq]:checked')) || '';//门静脉期
  var yfzzqycq = getVal($('input[type=radio][name=yfzzqycq]:checked')) || '';//延迟期
  var yfzzqxs = getVal($('input[type=radio][name=yfzzqxs]:checked')) || '';//增强强化形式
  var yfzzqxsqt = $('#dga-rt-69').val();//增强形式其他填写
  var yfzdgqf = getVal($('input[type=radio][name=yfzdgqf]:checked')) || '';//胆管侵犯是否均匀
  var yfzqfgz = getVal($('input[type=checkbox][name=yfzqfgz]:checked')) || '';//胆管侵犯不均匀
  var yfzsydg = getVal($('input[type=radio][name=yfzsydg]:checked')) || '';//上游胆管扩张
  var yfzsycd = getVal($('input[type=radio][name=yfzsycd]:checked')) || '';//上游肺动脉扩张程度
  var yfzsykzxt = getVal($('input[type=radio][name=yfzsykzxt]:checked')) || '';//上游肺动脉扩张形态
  var yzfkzxt = getVal($('input[type=radio][name=yzfkzxt]:checked')) || '';//上游肺动脉扩张形态
  var yfzkzxt = getVal($('input[type=radio][name=yfzkzxt]:checked')) || '';//上游肺动脉扩张形态
  var yfzdgqfkz = getVal($('input[type=radio][name=yfzdgqfkz]:checked')) || '';
  var yfzsydgcd = getVal($('input[type=radio][name=yfzsydgcd]:checked')) || '';//上游胆管扩张程度
  var yfzxydg = getVal($('input[type=radio][name=yfzxydg]:checked')) || '';//下游胆管扩张
  var yfzxydgkz = getVal($('input[type=radio][name=yfzxydgkz]:checked')) || '';//下游胆管扩张情况
  var yfzxycd = getVal($('input[type=radio][name=yfzxycd]:checked')) || '';//下游肺动脉扩张程度
  var yfzxykzxt = getVal($('input[type=radio][name=yfzxykzxt]:checked')) || '';//下游肺动脉扩张形态
  var yfzxydgcd = getVal($('input[type=radio][name=yfzxydgcd]:checked')) || '';//下游胆管扩张程度
  var yfzdx1 = getVal($('input[type=radio][name=yfzdx1]:checked')) || '';//胆栓
  var yfzdx2 = getVal($('input[type=radio][name=yfzdx2]:checked')) || '';//胆栓
  var yfzjs1 = getVal($('input[type=radio][name=yfzjs1]:checked')) || '';//结节
  var yfzjs2 = getVal($('input[type=radio][name=yfzjs2]:checked')) || '';//结节
  var yfzbmz = getVal($('input[type=radio][name=yfzbmz]:checked')) || '';//包膜征
  var yfzbmzInpVal = $('#dga-rt-72').val();//包膜征有填写
  var yfzxzz = getVal($('input[type=radio][name=yfzxzz]:checked')) || '';//蟹足征
  var yfzljgb = getVal($('input[type=radio][name=yfzljgb]:checked')) || '';//邻近肝包膜轻度内陷
  var yfzqflj = getVal($('input[type=checkbox][name=yfzqflj]:checked')) || '';//侵犯邻近肝实质
  var yfzsezcqb = getVal($('input[type=checkbox][name=yfzsezcqb]:checked')) || '';//侵犯邻近肝实质

  // 子灶
  var zzVal = getVal('[id="dga-rt-17"]:checked') || '';
  var zzsm = getVal($('input[type=radio][name=zzsm]:checked')) || '';//数目
  var zzwz = getVal($('input[type=checkbox][name=zzwz]:checked')) || '';//位置
  var zzwzgzg = getVal($('input[type=checkbox][name=zzwzgzg]:checked')) || '';//位置
  var zzwzgzgsd = getVal($('input[type=checkbox][name=zzwzgzgsd]:checked')) || '';//位置
  var zzqf = getVal($('input[type=checkbox][name=zzqf]:checked')) || '';//侵犯
  // var zzdx1 = $('#dga-rt-83').val();//大小
  // var zzdx2 = $('#dga-rt-84').val();//大小
  var zzdxcl = getVal($('input[type=radio][name=zzdxcl]:checked')) || '';//大小测量
  var zzxz = getVal($('input[type=radio][name=zzxz]:checked')) || '';//形状
  var zzfx = getVal($('input[type=radio][name=zzfx]:checked')) || '';//分型
  var zzbj = getVal($('input[type=radio][name=zzbj]:checked')) || '';//边界
  var zzpsmd = getVal($('input[type=radio][name=zzpsmd]:checked')) || '';//平扫密度
  var zzpsmd2 = getVal($('input[type=radio][name=zzpsmd2]:checked')) || '';//平扫密度是否均匀
  var zzpsmdbj = getVal($('input[type=radio][name=zzpsmdbj]:checked')) || '';//平扫密度不均匀
  var zzsmdbj = getVal($('input[type=radio][name=zzsmdbj]:checked')) || '';//平扫密度不均匀
  var zzbjyqt = $('#dga-rt-104').val();//平扫密度不均匀其他填写
  var zzzqdmwq = getVal($('input[type=radio][name=zzzqdmwq]:checked')) || '';//动脉晚期
  var zzzqmjmq = getVal($('input[type=radio][name=zzzqmjmq]:checked')) || '';//门静脉期
  var zzzqycq = getVal($('input[type=radio][name=zzzqycq]:checked')) || '';//延迟期
  var zzzqxs = getVal($('input[type=radio][name=zzzqxs]:checked')) || '';//增强强化形式
  var zzzqxsqt = $('#dga-rt-124').val();//增强形式其他填写
  var zzbmz = getVal($('input[type=radio][name=zzbmz]:checked')) || '';//包膜征
  var zzbmzInpVal = $('#dga-rt-127').val();//包膜征有填写
  var zzxzz = getVal($('input[type=radio][name=zzxzz]:checked')) || '';//蟹足征
  var zzljgb = getVal($('input[type=radio][name=zzljgb]:checked')) || '';//邻近肝包膜轻度内陷
  var zzzdgqf = getVal($('input[type=radio][name=zzzdgqf]:checked')) || '';//胆管侵犯是否均匀
  var zzgzg = getVal($('input[type=checkbox][name=zzgzg]:checked')) || '';//胆管侵犯不均匀
  var zzyfzsydg = getVal($('input[type=radio][name=zzyfzsydg]:checked')) || '';//上游胆管扩张
  var zzsycd = getVal($('input[type=radio][name=zzsycd]:checked')) || '';//上游肺动脉扩张程度
  var zzsykzxt = getVal($('input[type=radio][name=zzsykzxt]:checked')) || '';//上游肺动脉扩张形态
  var zzkzxt = getVal($('input[type=radio][name=zzkzxt]:checked')) || '';//上游肺动脉扩张形态
  var zzdgqfkz = getVal($('input[type=radio][name=zzdgqfkz]:checked')) || '';
  var zzxydgkz = getVal($('input[type=radio][name=zzxydgkz]:checked')) || '';
  var zzsydgcd = getVal($('input[type=radio][name=zzsydgcd]:checked')) || '';//上游胆管扩张程度
  var zzxydg = getVal($('input[type=radio][name=zzxydg]:checked')) || '';//下游肺动脉扩张
  var zzxycd = getVal($('input[type=radio][name=zzxycd]:checked')) || '';//下游肺动脉扩张程度
  var zzxykzxt = getVal($('input[type=radio][name=zzxykzxt]:checked')) || '';//下游肺动脉扩张形态
  var zzxydgcd = getVal($('input[type=radio][name=zzxydgcd]:checked')) || '';//下游胆管扩张程度
  var zzmbkzxt = getVal($('input[type=radio][name=zzmbkzxt]:checked')) || '';//下游胆管扩张程度
  var zzdx1 = getVal($('input[type=radio][name=zzdx1]:checked')) || '';//胆栓
  var zzdx2 = getVal($('input[type=radio][name=zzdx2]:checked')) || '';//胆栓
  var zzjs1 = getVal($('input[type=radio][name=zzjs1]:checked')) || '';//结节
  var zzjs2 = getVal($('input[type=radio][name=zzjs2]:checked')) || '';//结节
  var zzqflj = getVal($('input[type=checkbox][name=zzqflj]:checked')) || '';//侵犯邻近肝实质
  var zzsezcqb = getVal($('input[type=checkbox][name=zzsezcqb]:checked')) || '';//侵犯邻近肝实质

  //肝背景
  var gyhVal = getVal($('input[type=radio][name=gyh]:checked')) || '';
  var gyhInpVal = $('#dga-rt-132').val() || ''; //肝硬化
  var zfgVal = getVal($('input[type=radio][name=zfg]:checked')) || ''; //脂肪肝
  var zfgqt = $('#dga-rt-590').val();
  var gztgzVal = getVal($('input[type=radio][name= gztgz]:checked')) || ''; //肝脏铁过载
  var tgzqt = $('#dga-rt-593').val();

  //门脉高压征象
  var jmqzVal = getVal($('input[type=checkbox][name=jmqz]:checked')) || '';
  var jmqzInpVal = $('#dga-rt-136').val() || ''; //门脉高压静脉曲张
  var fqjmkfVal = getVal($('input[type=radio][name=fqjmkf]:checked')) || '';
  var fqjmkfInpVal = $('#dga-rt-139').val() || ''; //附脐静脉开放
  var wsflVal = getVal($('input[type=radio][name=wsfl]:checked')) || '';
  var wsflInpVal = $('#dga-rt-142').val() || ''; //胃-肾分流
  var psflVal = getVal($('input[type=radio][name=psfl]:checked')) || '';
  var psflInpVal = $('#dga-rt-145').val() || ''; //脾-肾分流
  var mmgyqtInpVal = $('#dga-rt-147').val() || ''; //门脉高压征象其他

  // 肝动脉
  var dmdmbox = getVal($('input[type=checkbox][name=gdm]:checked')) || '';
  if(dmdmbox){
    gdmStrList = forJmArrFun(gdmArr);
  }
  // 门静脉系统
  var mjmxtCheckbox = getVal($('input[type=checkbox][name=mjmxt]:checked')) || '';
  if (mjmxtCheckbox) {
    // for(let a = 0; a < mjmxtArr.length; a++){
    //   let arr = [];
    //   arr =  mjmxtArr[a].map(item => getVal($(`input[type=radio][name=${item}]:checked`)));
    //   mjmxtStrList.push(arr)
    // }
    mjmxtStrList = forJmArrFun(mjmxtArr);
  }
  // 肝静脉
  var gjmCheckbox = getVal($('input[type=checkbox][name=gjm]:checked')) || '';
  if (gjmCheckbox) {
    gjmStrList = forJmArrFun(gjmArr)
  }
  // 下腔静脉
  var xqjmCheckbox = getVal($('input[type=checkbox][name=xqjm]:checked')) || '';
  if (xqjmCheckbox) {
    xqjmStrList = forJmArrFun(xqjmArr)
  }

  //胆管
  var dgVal = getVal($('input[type=radio][name=dg]:checked')) || '';
  var dgsqVal = getVal($('input[type=radio][name=dgsq]:checked')) || ''; //胆管受侵val
  var dgsqkj = getVal($('input[type=checkbox][name=dgsqkj]:checked')) || '';
  var dgkzVal = getVal($('input[type=radio][name=dgkz]:checked')) || '';
  var dgkzInpVal = $('#dga-rt-479').val() || '';//胆管扩张最宽val
  var dskzVal = getVal($('input[type=radio][name=dskz]:checked')) || '';
  var dskzInpVal = $('#dga-rt-482').val() || '';//胆栓形成val

  //淋巴结
  var lbjVal = getVal($('input[type=radio][name=lbj]:checked')) || '';
  var xgjVal = getVal('[id="dga-rt-485"]:checked') || '';//心膈角val
  var xgjsl = $("#dga-rt-486").val();
  var xgjdj = $("#dga-rt-487").val();
  var xgjzq = $("#dga-rt-488").val();
  var gmbVal = getVal('[id="dga-rt-489"]:checked') || '';//肝门部
  var gmbsl = $("#dga-rt-490").val();
  var gmbdj = $("#dga-rt-491").val();
  var gmbzq = $("#dga-rt-492").val();
  var fmhVal = getVal('[id="dga-rt-493"]:checked') || '';//腹膜后
  var fmhsl = $("#dga-rt-494").val();
  var fmhdj = $("#dga-rt-495").val();
  var fmhzq = $("#dga-rt-496").val();
  var lbjqtVal = getVal('[id="dga-rt-497"]:checked') || '';//其他
  var lbjqtInpVal = $('#dga-rt-498').val() || '';//淋巴结其他val
  var lbjqtsl = $('#dga-rt-499').val();
  var lbjqtdj = $('#dga-rt-500').val();

  //远处转移
  var yczyfb = getVal('[id="dga-rt-501"]:checked') || ''; //远处转移肺部
  var fbwz = getVal($('input[type=radio][name=fb]:checked')) || ''; //远处转移肺部位置
  var fbwzInpVal = $('#dga-rt-505').val() || '';
  var yczygt = getVal('[id="dga-rt-506"]:checked') || ''; //远处转移骨
  var yczygtInpVal = $('#dga-rt-507').val() || '';
  var yczyqt = getVal('[id="dga-rt-508"]:checked') || ''; //远处其他
  var yczyqtInpVal = $('#dga-rt-509').val() || '';//远处转移其他val

  //肝良性占位
  var gnzVal = getVal('[id="dga-rt-510"]:checked') || ''; //肝囊肿val
  var gnzInpVal = $('#dga-rt-511').val() || '';
  var gxglVal = getVal('[id="dga-rt-512"]:checked') || ''; //肝血管瘤val
  var gxglInpVal = $('#dga-rt-513').val() || '';
  var glxqtVal = getVal('[id="dga-rt-514"]:checked') || ''; //肝良性占位其他val
  var glxqInpVal = $('#dga-rt-515').val() || '';

  //胆囊
  var dnVal = getVal($('input[type=radio][name=dn]:checked')) || '';
  var dnxt = getVal($('input[type=radio][name=dnxt]:checked')) || '';//形态
  var dnbzh = getVal($('input[type=radio][name=dnbzh]:checked')) || '';
  var dndx1 = $('#dga-rt-522').val() || ''; //大小
  var dndx2 = $('#dga-rt-523').val() || ''; //大小
  var dnjs = getVal('[id="dga-rt-524"]:checked') || ''; //胆囊结石
  var dnjsInpVal = $('#dga-rt-525').val() || '';
  var dznc = getVal('[id="dga-rt-526"]:checked') || ''; //胆汁粘稠
  var dzncInpVal = $('#dga-rt-527').val() || '';
  var xjz = getVal('[id="dga-rt-528"]:checked') || ''; //腺肌症
  var xjzInpVal = $('#dga-rt-529').val() || '';
  var dny = getVal('[id="dga-rt-530"]:checked') || ''; //胆囊炎
  var dnyInpVal = $('#dga-rt-531').val() || '';
  var xr = getVal('[id="dga-rt-532"]:checked') || ''; //息肉/腺瘤
  var xrInpVal = $('#dga-rt-533').val() || '';
  var dnqt = getVal('[id="dga-rt-534"]:checked') || ''; //胆囊其他
  var dnqtInpVal = $('#dga-rt-535').val() || ''; //胆囊其他

  //脾脏
  var pzVal = getVal($('input[type=radio][name=pz]:checked')) || '';
  var pzxt = getVal($('input[type=radio][name=pzxt]:checked')) || '';//形态
  var pzxtqt = $('#dga-rt-541').val() || '';//形态其他
  var pzdx1 = $('#dga-rt-542').val();
  var pzdx2 = $('#dga-rt-543').val();
  var pnz = getVal('[id="dga-rt-544"]:checked') || ''; //脾囊肿
  var pnzInpVal = $('#dga-rt-545').val() || '';
  var pzqt = getVal('[id="dga-rt-546"]:checked') || ''; //脾脏其他
  var pzqtInpVal = $('#dga-rt-547').val() || '';

  // 胰腺
  var yxVal = getVal($('input[type=radio][name=yx]:checked')) || '';
  var yxxt = getVal($('input[type=radio][name=yxxt]:checked')) || '';//形态
  var yxxtqt = $('#dga-rt-553').val() || '';//形态其他
  var yxnz = getVal('[id="dga-rt-554"]:checked') || ''; //胰腺囊肿
  var yxnzInpVal = $('#dga-rt-555').val() || '';
  var ipmn = getVal('[id="dga-rt-556"]:checked') || ''; //ipmn
  var ipmnInpVal = $('#dga-rt-557').val() || '';
  var yxy = getVal('[id="dga-rt-558"]:checked') || ''; //胰腺炎
  var yxyInpVal = $('#dga-rt-559').val() || '';
  var sjnfb = getVal('[id="dga-rt-560"]:checked') || ''; //神经内分泌肿瘤
  var sjnfbInpVal = $('#dga-rt-561').val() || '';
  var yxqt = getVal('[id="dga-rt-562"]:checked') || ''; //脾脏其他
  var yxqtInpVal = $('#dga-rt-563').val() || '';
  var ygqk = $('#dga-rt-564').val() || ''; //胰管情况

  //腹腔
  var fqzfjx = getVal($('input[type=radio][name=fqzfjx]:checked')) || ''; //腹腔脂肪间隙
  var fs = getVal($('input[type=radio][name=fs]:checked')) || ''; //腹水
  var fsyl = getVal($('input[type=radio][name=fsyl]:checked')) || ''; //腹水
  var fsInpVal = getVal('#dga-rt-573') || ''; //腹水详情
  var fqjx = getVal($('input[type=radio][name=fqjx]:checked')) || "";//积血
  var fqjxqt = $('#dga-rt-576').val();//积血其他
  var fmzh = getVal($('input[type=radio][name=fmzh]:checked')) || ''; //增厚
  var fmqdzh = getVal($('input[type=checkbox][name=fmqdzh]:checked')) || ''; //增厚位置
  var fmzy = getVal($('input[type=radio][name=fmzy]:checked')) || ''; //腹膜种植转移
  var fmzyInpVal = $('#dga-rt-585').val();//腹膜种植转移详情

  //其它征象
  var qtzx = $('#dga-rt-586').val();

  // 印象推导
  $('#dga-rt-587').val('');
  if($('#dga-rt-818').val() === '肝内型' || $('#dga-rt-818').val() === ''){
    yfzVal && yfzwz ? impressionArr1.push(`考虑肝${yfzwz}段${dxcl}胆管癌可能性大`) : ''
    yfzqfgz ? impressionArr1.push(`累及${yfzqfgz}`) : ''
    fqjx && fqjx === '有' ? impressionArr1.push(`破裂出血并腹腔内积血/血肿形成`) : ''
    if((yfzsydg === '可见' && yfzxydg === '可见') && yfzsycd && yfzsycd === yfzxycd){
      impressionArr1.push(`胆管${yfzsycd}扩张`)
    }else {
      yfzsydg === '可见' && yfzsycd ? impressionArr1.push(`上游胆管${yfzsycd}扩张`) : ''
      yfzxydg === '可见' && yfzxycd ? impressionArr1.push(`下游胆管${yfzxycd}扩张`) : ''
    }
    if(yfzdx1 === '有' && yfzjs1 === '有'){
      impressionArr1.push(`可见胆栓、结石形成`)
    }else {
      yfzdx1 === '有' ? impressionArr1.push(`胆管可见胆栓形成`) : ''
      yfzjs1 === '有' ? impressionArr1.push(`胆管可见结石形成`) : ''
    }
    zzVal && zzsm ? impressionArr1.push(`肝内可见${zzsm}子灶`) : ''
  }
  if($('#dga-rt-818').val() === '肝门型'){
    if(yfzwzdz && dxcl){
      let alled = yfzwzdz.split('、')
      let xtst = ''
      if(alled.includes('肝总管') || alled.includes('胆总管上段') || alled.includes('胆总管中段') || alled.includes('胆总管下段')){
        xtst = 'I型'
      }
      alled.includes('左右肝管汇合部') ? xtst = 'II型' : ''
      alled.includes('左肝管（左肝一级胆管）') ? xtst = 'IIIb型' : ''
      alled.includes('右肝管（右肝一级胆管）') ? xtst = 'IIIa型' : ''
      alled.includes('左肝管（左肝一级胆管）') && alled.includes('右肝管（右肝一级胆管）') ? xtst = 'III型' : ''
      alled.includes('左肝二级胆管') ? xtst = 'IVb型' : ''
      alled.includes('右肝二级胆管') ? xtst = 'IVa型' : ''
      alled.includes('左肝二级胆管') && alled.includes('右肝二级胆管') ? xtst = 'IV型' : ''
      alled.includes('左肝三级胆管') ? xtst = 'IV型' : ''
      alled.includes('右肝三级胆管') ? xtst = 'IV型' : ''
      alled.includes('左肝三级胆管') && alled.includes('右肝三级胆管') ? xtst = 'IV型' : ''
      impressionArr1.push(`考虑肝门部${dxcl}胆管癌(Bismuth-Corlette分型：${xtst})可能性大`)
    }
    yfzwzdz ? impressionArr1.push(`累及${yfzwzdz}`) : ''
    if((yfzdgqfkz === '可见' && yfzxydgkz === '可见') && yfzsydgcd && yfzsydgcd === yfzxydgcd){
      impressionArr1.push(`胆管${yfzsydgcd}扩张`)
    }else {
      yfzdgqfkz === '可见' && yfzsydgcd ? impressionArr1.push(`上游胆管${yfzsydgcd}扩张`) : ''
      yfzxydgkz === '可见' && yfzxydgcd ? impressionArr1.push(`下游胆管${yfzxydgcd}扩张`) : ''
    }
    if(yfzdx2 === '有' && yfzjs2 === '有'){
      impressionArr1.push(`可见胆栓、结石形成`)
    }else {
      yfzdx2 === '有' ? impressionArr1.push(`胆管可见胆栓形成`) : ''
      yfzjs2 === '有' ? impressionArr1.push(`胆管可见结石形成`) : ''
    }
    yfzqflj ? impressionArr1.push(`侵犯${yfzqflj}`) : ''
    $('#dga-rt-695').val() ? impressionArr1.push($('#dga-rt-695').val()) : ''
    fqjx && fqjx === '有' ? impressionArr1.push(`破裂出血并腹腔内积血/血肿形成`) : ''
    zzVal && zzsm ? impressionArr1.push(`肝外可见${zzsm}子灶`) : ''
  }
  if($('#dga-rt-818').val() === '肝外胆总管型'){
    let arsts = yfzdzgwz.split('、')
    let directionChars = arsts.map(item => 
      item.replace(/胆总管|段/g, '') // 正则移除固定部分
    ).join('');
    directionChars === '上中' ? directionChars = '中上' : ''
    let astst = `胆总管${directionChars}段`;
    yfzdzgwz ? impressionArr1.push(`考虑${astst}${dxcl}胆管癌可能性大`) : ''
    yfzqf ? impressionArr1.push(`累及${yfzqf}`) : ''
    if((yfzdgqfkz === '可见' && yfzxydgkz === '可见') && yfzsydgcd && yfzsydgcd === yfzxydgcd){
      impressionArr1.push(`胆管${yfzsydgcd}扩张`)
    }else {
      yfzdgqfkz === '可见' && yfzsydgcd ? impressionArr1.push(`上游胆管${yfzsydgcd}扩张`) : ''
      yfzxydgkz === '可见' && yfzxydgcd ? impressionArr1.push(`下游胆管${yfzxydgcd}扩张`) : ''
    }
    if(yfzdx2 === '有' && yfzjs2 === '有'){
      impressionArr1.push(`可见胆栓、结石形成`)
    }else {
      yfzdx2 === '有' ? impressionArr1.push(`胆管可见胆栓形成`) : ''
      yfzjs2 === '有' ? impressionArr1.push(`胆管可见结石形成`) : ''
    }
    yfzsezcqb ? impressionArr1.push(`侵犯${yfzsezcqb}`) : ''

    fqjx && fqjx === '有' ? impressionArr1.push(`破裂出血并腹腔内积血/血肿形成`) : ''
    zzVal && zzsm ? impressionArr1.push(`肝外可见${zzsm}子灶`) : ''
  }
  let gdmsq = isChecked(gdmStrList, 'sq');
  let mjmsq = isChecked(mjmxtStrList, 'sq');
  let gjmsq = isChecked(gjmStrList, 'sq');
  let xqjmsq = isChecked(xqjmStrList, 'sq');
  let gdmsx = isChecked(gdmStrList,'sx');
  let mjmxs = isChecked(mjmxtStrList, 'xs');
  let gjmxs = isChecked(gjmStrList, 'xs');
  let xqjmxs = isChecked(xqjmStrList, 'xs');
  let gdmas = isChecked(gdmStrList, 'as');
  let mjmas = isChecked(mjmxtStrList, 'as');
  let gjmas = isChecked(gjmStrList, 'as');
  let xqjmas = isChecked(xqjmStrList, 'as');
  let sqArr = [], xsArr = [], asArr = [], sameJms = [];
  gdmsq ? sqArr.push('肝动脉') : '';
  mjmsq ? sqArr.push('门静脉') : '';
  gjmsq ? sqArr.push('肝静脉') : '';
  xqjmsq ? sqArr.push('下腔静脉') : '';
  sqArr && sqArr.length > 0 ? impressionArr1.push(`${sqArr.join('、')}受侵`) : '';
  gdmsx ? xsArr.push('肝动脉') : ''
  mjmxs ? xsArr.push('门静脉') : ''
  gjmxs ? xsArr.push('肝静脉') : ''
  xqjmxs ? xsArr.push('下腔静脉') : '';
  gdmas ? asArr.push('肝动脉') : ''
  mjmas ? asArr.push('门静脉') : ''
  gjmas ? asArr.push('肝静脉') : ''
  xqjmas ? asArr.push('下腔静脉') : '';
  sameJms = xsArr.filter(x => asArr.includes(x));
  xsArr = xsArr.filter(x => !sameJms.includes(x));
  asArr = asArr.filter(x => !sameJms.includes(x));
  xsArr && xsArr.length > 0 ? impressionArr1.push(`${xsArr.join('、')}血栓形成`) : '';
  asArr && asArr.length > 0 ? impressionArr1.push(`${asArr.join('、')}癌栓形成`) : '';
  sameJms && sameJms.length ? impressionArr1.push(`${sameJms.join('、')}血栓、癌栓形成`) : '';
  dgsqVal === '可见' ? str += '胆管可见受侵' : ''
  if (dskzVal === '有') {
    str ? str += "、胆栓形成" : '胆管可见胆栓'
  }
  str ? impressionArr1.push(`${str}`) : '';
  str = '';
  let lbjArr = [];
  xgjVal ? lbjArr.push(xgjVal) : '';
  gmbVal ? lbjArr.push(gmbVal) : '';
  fmhVal ? lbjArr.push(fmhVal) : '';
  lbjqtVal ? lbjArr.push(lbjqtInpVal) : '';
  // lbjqtInpVal ? lbjArr.push(lbjqtInpVal) : '';
  lbjArr && lbjArr.length > 0 ? impressionArr1.push(`${lbjArr.join('、')}可见淋巴结转移`) : '';
  let yczyArr = [];
  if (yczyfb) {
    fbwz ? yczyArr.push(fbwz) : yczyArr.push(yczyfb)
  }
  yczygt ? yczyArr.push(yczygt) : ''
  // yczyqtInpVal ? yczyArr.push(yczyqtInpVal) : ''
  yczyArr && yczyArr.length > 0 ? impressionArr1.push(`远处转移：${yczyArr.join('、')}`) : '';
  fmzy && fmzy === '有' ? impressionArr1.push(`腹膜种植转移`) : '';

  gyhVal && gyhVal !== '无' ? impressionArr2.push(gyhVal) : ''
  zfgVal && zfgVal !== '无' ? str += "脂肪肝" : ''
  if (gztgzVal === '有') {
    str ? str += "并铁过载" : str += '铁过载'
  }
  str ? impressionArr2.push(str) : ''
  str = '';
  let mmgyArr = []
  jmqzVal ? mmgyArr.push(jmqzVal + '曲张') : ''
  fqjmkfVal === '是' ? mmgyArr.push('附脐静脉开放') : ''
  wsflVal === '是' ? mmgyArr.push('胃-肾分流') : ''
  psflVal === '是' ? mmgyArr.push('脾-肾分流') : ''
  // mmgyArr && mmgyArr.length > 0 ? impressionArr2.push(mmgyArr.join('，')) : ''

  fsyl ? str += `${fsyl}腹水` : '';
  if (fqzfjx && fqzfjx !== '清晰' || fmzh === '轻度增厚') {
    str ? str += "，腹膜炎" : str = '腹膜炎'
  }
  mmgyArr.length > 0 ? impressionArr2.push(`门脉高压：${mmgyArr.join('，')}${str ? '，'+ str : ''}`) : ''
  impressionArr1.length > 0 ? impressionArr.push(impressionArr1.join('，')) : ''
  impressionArr2.length > 0 ? impressionArr.push(impressionArr2.join('，')) : ''
  mmgyArr.length === 0 && str ? impressionArr.push(str) : ''
  str = '';
  let glxzwArr = [];
  gnzVal ? glxzwArr.push(gnzVal) : ''
  gxglVal ? glxzwArr.push(gxglVal) : ''
  glxzwArr.length > 0 ? impressionArr.push(glxzwArr.join('、')) : ''
  let dnArr = [];
  dnjs ? dnArr.push(dnjs) : ''
  dznc ? dnArr.push(dznc) : ''
  xjz ? dnArr.push(xjz) : ''
  dny ? dnArr.push(dny) : ''
  xr ? dnArr.push(xr) : ''
  dnArr.length > 0 ? impressionArr.push(`胆囊${dnArr.join('、')}`) : ''
  pnz ? impressionArr.push(`脾${pnz}`) : ''
  let yxArr = []
  yxnz ? yxArr.push(yxnz) : '';
  ipmn ? yxArr.push(ipmn) : '';
  yxy ? yxArr.push(yxy) : '';
  sjnfb ? yxArr.push(sjnfb) : '';
  yxArr.length > 0 ? impressionArr.push(`胰腺（${yxArr.join('、')}）`) : ''
  if (impressionArr.length > 0) {
    for (let i = 0; i < impressionArr.length; i++) {
      impressionArr[i] = `${i + 1}.${impressionArr[i]}`
    }
    impressionArr.length > 0 ? $('#dga-rt-587').val(impressionArr.join('\n')) : ''
  }

  // 影像描述推导
  descriptionStr = '';
  let descriptionArr = [], line = [];
  var gztj = getVal($('input[type=radio][name=gztj]:checked')) || ""; //肝脏体积
  var gzxt = getVal($('input[type=radio][name=xt]:checked')) || ""; //形态
  var gybl = getVal($('input[type=radio][name=gybl]:checked')) || ""; //肝叶比例
  var gyblqt = $('#dga-rt-13').val();//肝叶比例其他
  var gl = getVal($('input[type=radio][name=gl]:checked')) || ""; //肝裂
  gztj === '正常' && gzxt === '光滑' ? line.push('肝脏大小、形态未见异常') : line.push(`肝脏整体形态：肝脏体积${gztj}，表面${gzxt}`)
  gyblqt ? str = gyblqt : str = gybl
  line.push(`肝叶比例${str}`)
  str = '';
  line.push(`肝裂${gl}`);
  descriptionArr.push("　　" + line.join('，'));
  line = [];
  //-------------------------------------------------------------------------------
  if (yfzVal) {
    yfbzsm === '单发' ? str = '一' : '';
    yfbzsm === '多发' ? str = '多发' : '';
    if($('#dga-rt-818').val() === '肝内型' || $('#dga-rt-818').val() === ''){
      yfzwz ? line.push(`肝内${yfzwz}段见${str}${yfzxz}病灶`) : '';
    }
    if($('#dga-rt-818').val() === '肝门型'){
      yfzwzdz ? line.push(`肝门部见${str}${yfzxz}病灶，累及${yfzwzdz}`) : '';
    }
    if($('#dga-rt-818').val() === '肝外胆总管型'){
      let arsts = yfzdzgwz.split('、')
      let directionChars = arsts.map(item => 
        item.replace(/胆总管|段/g, '') // 正则移除固定部分
      ).join('');
      directionChars === '上中' ? directionChars = '中上' : ''
      let astst = `胆总管${directionChars}段`;
      yfzdzgwz ? line.push(`${astst}可见${str}${yfzxz}病灶`) : '';
      yfzqf ? line.push(`累及${yfzqf}`) : '';
    }
    str = '';
    if(dxcl === '肿块型'){
      yfzdxcl = `肿块型（最大横断面大小约：${$('#dga-rt-618').val()}mm ${$('#dga-rt-619').val() ? 'x '+$('#dga-rt-619').val()+'mm' : '' }，冠状位上下端长径：${$('#dga-rt-620').val()}mm）`
      line.push(yfzdxcl)
    }
    if(dxcl === '管壁浸润型'){
      yfzdxcl = `管壁浸润型（最大横断面的管壁厚度：${$('#dga-rt-622').val()}mm，冠状位上下端长径：${$('#dga-rt-623').val()}mm）`
      line.push(yfzdxcl)
    }
    if(dxcl === '导管内型'){
      yfzdxcl = `导管内型（最大横断面的管壁厚度：${$('#dga-rt-624').val()}mm，冠状位上下端长径：${$('#dga-rt-625').val()}mm）`
      line.push(yfzdxcl)
    }
    if(dxcl === '混合型'){
      yfzdxcl = `混合型（最大横断面大小约：${$('#dga-rt-626').val()}mm ${$('#dga-rt-627').val() ? 'x '+$('#dga-rt-627').val()+'mm' : ''}，冠状位上下端长径：${$('#dga-rt-628').val()}mm）`
      line.push(yfzdxcl)
    }
    yfzfx ? line.push(`形态呈${yfzfx}`) : '';
    yfzbj ? line.push(`边界${yfzbj}`) : '';
    // yfzdx1 || yfzdx2 ? line.push(`${yfbzsm === '多发' ? '较大者大小约' : '大小约'}（横轴位最大层面测量，${yfzdx1}mmx${yfzdx2}mm）`) : ''
    yfzpsmd ? line.push(`病灶平扫呈${yfzpsmd}`) : '';
    yfzpsmd2 ? str = `密度${yfzpsmd2}` : '';
    if (yfzpsmdbj) {
      str2 = yfzpsmdbj === '其他' ? yfzbjyqt : yfzpsmdbj;
      str += `（${str2}）`;
    }
    str ? line.push(str) : ''
    str = '', str2 = '';
    yfzzqdmwq ? line.push(`增强动脉晚期${yfzzqdmwq}`) : '';
    yfzzqmjmq ? str = `门静脉期${yfzzqmjmq}` : '';
    yfzzqycq ? str2 = `延迟期${yfzzqycq}` : '';
    yfzzqmjmq === yfzzqycq ? str3 = `门静脉期、延迟期${yfzzqmjmq}` : ''
    if (str3) {
      line.push(str3)
    } else {
      str ? line.push(str) : ''
      str2 ? line.push(str2) : ''
    }
    str = '', str2 = '', str3 = '';
    yfzzqxs ? line.push(`呈${yfzzqxs === '其他' ? yfzzqxsqt : yfzzqxs}`) : '';
    yfzbmzInpVal ? line.push(`${yfzbmzInpVal}`) : line.push('未见明显延迟性强化包膜');
    if($('#dga-rt-818').val() === '肝内型' || $('#dga-rt-818').val() === ''){
      yfzxzz === '有' ? line.push(`边缘可见蟹足征`) : line.push(`边缘未见蟹足征`);
      yfzljgb === '有' ? line.push(`邻近肝包膜可见轻度内陷`) : line.push(`邻近肝包膜未见轻度内陷`);
      yfzqfgz ? line.push(`累及${yfzqfgz}`) : '';
      if(yfzsydg === '未见' && yfzxydg === '未见'){
        line.push(`胆管未见扩张`)
      }
      yfzsydg === '可见' && yfzsycd ? line.push(`上游胆管${yfzsycd}扩张`) : ''
      $('#dga-rt-650').val() ? line.push(`最宽处约${$('#dga-rt-650').val()}`) : '';
      yfzsykzxt ? line.push(`呈${yfzsykzxt}`) : '';
      yfzsydg1 = yfzsydg === '可见' ? `${yfzsycd}，最宽处约${$('#dga-rt-650').val()}，${yfzsykzxt ? '扩张形态：'+yfzsykzxt : ''}` : '未见'

      yfzxydg === '可见' && yfzxycd ? line.push(`下游胆管${yfzxycd}扩张`) : ''
      $('#dga-rt-658').val() ? line.push(`最宽处约${$('#dga-rt-658').val()}`) : '';
      yfzxykzxt ? line.push(`呈${yfzxykzxt}`) : '';
      yfzxydg1 = yfzxydg === '可见' ? `${yfzxycd}，最宽处约${$('#dga-rt-658').val()}，${yfzxykzxt ? '扩张形态：'+yfzxykzxt : ''}` : '未见'

      // yfzsycd ? line.push(`上游胆管${yfzsycd}扩张`) : '';
      // yfzxycd ? line.push(`下游胆管${yfzxycd}扩张`) : '';
      yfzdx1 === '有' && $('#dga-rt-664').val() ? line.push($('#dga-rt-664').val()) : '';
      yfzjs1 === '有' && $('#dga-rt-667').val() ? line.push($('#dga-rt-667').val()) : '';

      yfzdq1 = yfzdx1 === '有' ? `有${$('#dga-rt-664').val() ? '（'+$('#dga-rt-664').val()+'）' : ''}` : '无';
      yfzjs01 = yfzjs1 === '有' ? `有${$('#dga-rt-667').val() ? '（'+$('#dga-rt-667').val()+'）' : ''}` : '无';
    }else {
      if(yfzdgqfkz === '未见' && yfzxydgkz === '未见'){
        line.push(`胆管未见扩张`)
      }
      yfzdgqfkz === '可见' && yfzsydgcd ? line.push(`上游胆管${yfzsydgcd}扩张`) : ''
      $('#dga-rt-672').val() ? line.push(`最宽处约${$('#dga-rt-672').val()}`) : '';
      yzfkzxt ? line.push(`呈${yzfkzxt}`) : '';
      yfzsydg2 = yfzdgqfkz === '可见' ? `${yfzsydgcd}，最宽处约${$('#dga-rt-672').val()}${yzfkzxt ? '，扩张形态：'+yzfkzxt : ''}` : '未见'

      yfzxydgkz === '可见' && yfzxydgcd ? line.push(`下游胆管${yfzxydgcd}扩张`) : ''
      $('#dga-rt-680').val() ? line.push(`最宽处约${$('#dga-rt-680').val()}`) : '';
      yfzkzxt ? line.push(`呈${yfzkzxt}`) : '';
      yfzxydg2 = yfzxydgkz === '可见' ? `${yfzxydgcd}，最宽处约${$('#dga-rt-680').val()}${yfzkzxt ? '，扩张形态：'+yfzkzxt : ''}` : '未见'
      // if((yfzdgqfkz === '可见' && yfzxydgkz === '可见') && yfzsydgcd && yfzsydgcd === yfzxydgcd){
      //   line.push(`胆管${yfzsydgcd}扩张`)
      // }else {
      //   yfzdgqfkz === '可见' && yfzsydgcd ? line.push(`上游胆管${yfzsydgcd}扩张`) : ''
      //   yfzxydgkz === '可见' && yfzxydgcd ? line.push(`下游胆管${yfzxydgcd}扩张`) : ''
      // }
      // yfzsydgcd ? line.push(`上游胆管${yfzsydgcd}扩张`) : '';
      // yfzxydgcd ? line.push(`下游胆管${yfzxydgcd}扩张`) : '';
      yfzdx2 === '有' && $('#dga-rt-686').val() ? line.push($('#dga-rt-686').val()) : '';
      yfzjs2 === '有' && $('#dga-rt-690').val() ? line.push($('#dga-rt-690').val()) : '';
      
      yfzdq2 = yfzdx2 === '有' ? `有${$('#dga-rt-686').val() ? '（'+$('#dga-rt-686').val()+'）' : ''}` : '无';
      yfzjs02 = yfzjs2 === '有' ? `有${$('#dga-rt-690').val() ? '（'+$('#dga-rt-690').val()+'）' : ''}` : '无';
    }
    if($('#dga-rt-818').val() === '肝门型'){
      yfzqflj ? line.push(`侵犯${yfzqflj}`) : '';
      $('#dga-rt-695').val() ? line.push($('#dga-rt-695').val()) : '';
    }
    if($('#dga-rt-818').val() === '肝外胆总管型'){
      yfzsezcqb ? line.push(`侵犯${yfzsezcqb}`) : '';
      $('#dga-rt-700').val() ? line.push($('#dga-rt-700').val()) : '';
    }
    
    
    descriptionArr.push("　　原发灶：" + line.join('，'));
    line = [];
  }
  //-------------------------------------------------------------------------------
  if (zzVal) {
    str = '';
    zzsm === '单发' ? str = '一' : '';
    zzsm === '多发' ? str = '多发' : '';
    if($('#dga-rt-818').val() === '肝内型' || $('#dga-rt-818').val() === ''){
      str || zzxz ? line.push(`肝内可见${str}${zzxz}病灶`) : '';
    }
    if($('#dga-rt-818').val() === '肝门型'){
      zzwzgzg ? line.push(`肝门部见${str}${zzxz}病灶，累及${zzwzgzg}`) : '';
    }
    if($('#dga-rt-818').val() === '肝外胆总管型'){
      zzwzgzgsd ? line.push(`${zzwzgzgsd}可见${str}${zzxz}病灶`) : '';
      zzqf ? line.push(`累及${zzqf}`) : '';
    }
    if(zzdxcl === '肿块型'){
      zzdxcls = `肿块型（最大横断面大小约：${$('#dga-rt-725').val()}mm x ${$('#dga-rt-726').val()}mm，冠状位上下端长径：${$('#dga-rt-727').val()}mm）`
      line.push(zzdxcls)
    }
    if(zzdxcl === '管壁浸润型'){
      zzdxcls = `管壁浸润型（最大横断面的管壁厚度：${$('#dga-rt-729').val()}mm，冠状位上下端长径：${$('#dga-rt-730').val()}mm）`
      line.push(zzdxcls)
    }
    if(zzdxcl === '导管内型'){
      zzdxcls = `导管内型（最大横断面的管壁厚度：${$('#dga-rt-731').val()}mm，冠状位上下端长径：${$('#dga-rt-732').val()}mm）`
      line.push(zzdxcls)
    }
    if(zzdxcl === '混合型'){
      zzdxcls = `混合型（最大横断面大小约：${$('#dga-rt-733').val()}mm x ${$('#dga-rt-734').val()}mm，冠状位上下端长径：${$('#dga-rt-735').val()}mm）`
      line.push(zzdxcls)
    }
    zzfx ? line.push(`形态呈${zzfx}`) : '';
    zzbj ? line.push(`边界${zzbj}`) : '';
    zzwz ? line.push(`最大位于${zzwz}段`) : '';
    // zzdx1 || zzdx2 ? line.push(`${zzsm === '多发' ? '较大者大小约' : '大小约'}${zzdx1}mmx${zzdx2}mm`) : ''
    zzpsmd ? line.push(`病灶平扫呈${zzpsmd}`) : '';
    zzpsmd2 ? str = `密度${zzpsmd2}` : '';
    if (zzsmdbj) {
      str2 = zzsmdbj === '其他' ? zzbjyqt : zzsmdbj;
      str += `（${str2}）`;
    }
    str ? line.push(str) : '';
    str = '', str2 = '';
    if (zzzqdmwq === yfzzqdmwq &&
      zzzqmjmq === yfzzqmjmq &&
      zzzqycq === yfzzqycq &&
      zzzqxs === yfzzqxs
    ) {
      line.push('增强强化形式同上述病灶类似')
    } else {
      zzzqdmwq ? line.push(`增强动脉晚期${zzzqdmwq}`) : '';
      zzzqmjmq ? str = `门静脉期${zzzqmjmq}` : '';
      zzzqycq ? str2 = `延迟期${zzzqycq}` : '';
      zzzqmjmq === zzzqycq ? str3 = `门静脉期、延迟期${yfzzqmjmq}` : ''
      if (str3) {
        line.push(str3)
      } else {
        str ? line.push(str) : ''
        str2 ? line.push(str2) : ''
      }
      str = '', str2 = '', str3 = '';
      zzzqxs ? line.push(`呈${zzzqxs === '其他' ? zzzqxsqt : zzzqxs}`) : '';
    }

    zzbmzInpVal ? line.push(`${zzbmzInpVal}`) : line.push('未见明显延迟性强化包膜');
    zzxzz === '有' ? line.push(`边缘可见蟹足征`) : '';
    zzljgb === '有' ? line.push(`邻近肝包膜可见轻度内陷`) : '';
    if($('#dga-rt-818').val() === '肝内型' || $('#dga-rt-818').val() === ''){
      zzgzg ? line.push(`累及${zzgzg}`) : '';
      if(zzyfzsydg === '未见' && zzxydg === '未见'){
        line.push(`胆管未见扩张`)
      }
      zzyfzsydg === '可见' && zzsycd ? line.push(`上游胆管${zzsycd}扩张`) : ''
      $('#dga-rt-757').val() ? line.push(`最宽处约${$('#dga-rt-757').val()}`) : '';
      zzsykzxt ? line.push(`呈${zzsykzxt}`) : '';
      zzsydg1 = zzyfzsydg === '可见' ? `${zzsycd}，最宽处约${$('#dga-rt-757').val()}，${zzsykzxt ? '扩张形态：'+zzsykzxt : ''}` : '未见'

      zzxydg === '可见' && zzxycd ? line.push(`下游胆管${zzxycd}扩张`) : ''
      $('#dga-rt-765').val() ? line.push(`最宽处约${$('#dga-rt-765').val()}`) : '';
      zzxykzxt ? line.push(`呈${zzxykzxt}`) : '';
      zzxydg1 = zzxydg === '可见' ? `${zzxycd}，最宽处约${$('#dga-rt-765').val()}，${zzxykzxt ? '扩张形态：'+zzxykzxt : ''}` : '未见'
      // zzsycd ? line.push(`上游胆管${zzsycd}扩张`) : '';
      // zzxycd ? line.push(`下游胆管${zzxycd}扩张`) : '';
      zzdx1 === '有' && $('#dga-rt-771').val() ? line.push($('#dga-rt-771').val()) : '';
      zzjs1 === '有' && $('#dga-rt-774').val() ? line.push($('#dga-rt-774').val()) : '';
    
      zzdq1 = zzdx1 === '有' ? `有${$('#dga-rt-771').val() ? '（'+$('#dga-rt-771').val()+'）' : ''}` : '无';
      zzjs01 = zzjs1 === '有' ? `有${$('#dga-rt-774').val() ? '（'+$('#dga-rt-774').val()+'）' : ''}` : '无';
    }else {
      if(zzdgqfkz === '未见' && zzxydgkz === '未见'){
        line.push(`胆管未见扩张`)
      }
      zzdgqfkz === '可见' && zzsydgcd ? line.push(`上游胆管${zzsydgcd}扩张`) : ''
      $('#dga-rt-779').val() ? line.push(`最宽处约${$('#dga-rt-779').val()}`) : '';
      zzkzxt ? line.push(`呈${zzkzxt}`) : '';
      zzsydg2 = zzdgqfkz === '可见' ? `${zzsydgcd}，最宽处约${$('#dga-rt-779').val()}${zzkzxt ? '，扩张形态：'+zzkzxt : ''}` : '未见'

      zzxydgkz === '可见' && zzxydgcd ? line.push(`下游胆管${zzxydgcd}扩张`) : ''
      $('#dga-rt-787').val() ? line.push(`最宽处约${$('#dga-rt-787').val()}`) : '';
      zzmbkzxt ? line.push(`呈${zzmbkzxt}`) : '';
      zzxydg2 = zzxydgkz === '可见' ? `${zzxydgcd}，最宽处约${$('#dga-rt-787').val()}${zzmbkzxt ? '，扩张形态：'+zzmbkzxt : ''}` : '未见'
      // if((zzdgqfkz === '可见' && zzxydgkz === '可见') && zzsydgcd && zzsydgcd === zzxydgcd){
      //   line.push(`胆管${zzxydgcd}扩张`)
      // }else {
      //   zzdgqfkz === '可见' && zzsydgcd ? line.push(`上游胆管${zzsydgcd}扩张`) : ''
      //   zzxydgkz === '可见' && zzxydgcd ? line.push(`下游胆管${zzxydgcd}扩张`) : ''
      // }
      // zzsydgcd ? line.push(`上游胆管${zzsydgcd}扩张`) : '';
      // zzxydgcd ? line.push(`下游胆管${zzxydgcd}扩张`) : '';
      zzdx2 === '有' && $('#dga-rt-793').val() ? line.push($('#dga-rt-793').val()) : '';
      zzjs2 === '有' && $('#dga-rt-796').val() ? line.push($('#dga-rt-796').val()) : '';
    
      zzdq2 = zzdx2 === '有' ? `有${$('#dga-rt-793').val() ? '（'+$('#dga-rt-793').val()+'）' : ''}` : '无';
      zzjs02 = zzjs2 === '有' ? `有${$('#dga-rt-796').val() ? '（'+$('#dga-rt-796').val()+'）' : ''}` : '无';
    }
    if($('#dga-rt-818').val() === '肝门型'){
      zzqflj ? line.push(`侵犯${zzqflj}`) : '';
      $('#dga-rt-802').val() ? line.push($('#dga-rt-802').val()) : '';
    }
    if($('#dga-rt-818').val() === '肝外胆总管型'){
      zzsezcqb ? line.push(`侵犯${zzsezcqb}`) : '';
      $('#dga-rt-807').val() ? line.push($('#dga-rt-807').val()) : '';
    }
    descriptionArr.push("　　子灶：" + line.join('，'));
    line = [];
  }
  //-------------------------------------------------------------------------------
  gyhInpVal ? line.push(gyhInpVal) : ""
  zfgqt ? line.push(zfgqt) : ""
  tgzqt ? line.push(tgzqt) : ""
  line.length > 0 ? descriptionArr.push("　　肝背景：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  jmqzInpVal ? line.push(jmqzInpVal) : "";
  fqjmkfInpVal ? line.push(fqjmkfInpVal) : "";
  wsflInpVal ? line.push(wsflInpVal) : "";
  psflInpVal ? line.push(psflInpVal) : "";
  mmgyqtInpVal ? line.push(mmgyqtInpVal) : "";
  line.length > 0 ? descriptionArr.push("　　门脉高压：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  var mjmzgkd = $('#dga-rt-148').val();
  let gdmStr = '';
  gdmStr = forJmArrFun2(gdmStrList, 'gdmName') || ''
  let mjStr = '';
  mjStr = forJmArrFun2(mjmxtStrList, 'mjmName') || ''
  let gjmStr = '';
  gjmStr = forJmArrFun2(gjmStrList, 'gjmName') || ''
  let xqjmStr = "";
  xqjmStr = forJmArrFun2(xqjmStrList, 'xqjmName') || ''
  // console.log('gjmArr2', gjmStr);
  // console.log('xqjmArr2', xqjmStr);
  mjmzgkd ? mjStr = `门脉主干宽约${mjmzgkd}mm。肝内` + mjStr : ''
  if(mjStr.slice(-7) === '尚清，未见受侵'){
    mjStr += '，未见充盈缺损影，增强未见强化'
  }
  if(gjmStr.slice(-7) === '尚清，未见受侵'){
    gjmStr += '，未见充盈缺损影，增强未见强化'
  }
  if(xqjmStr.slice(-7) === '尚清，未见受侵'){
    xqjmStr += '，未见充盈缺损影，增强未见强化'
  }
  if($('#dga-rt-818').val() === '肝门型' || $('#dga-rt-818').val() === '肝外胆总管型'){
    gdmStr ? descriptionArr.push("　　肝动脉：" + gdmStr) : ''
  }
  mjStr ? descriptionArr.push("　　门静脉系统：" + mjStr) : ''
  gjmStr ? descriptionArr.push("　　肝静脉：" + gjmStr) : ''
  xqjmStr ? descriptionArr.push("　　下腔静脉：" + xqjmStr) : ''
  //-------------------------------------------------------------------------------
  if (dgVal && dgVal === '无异常') { 
    line.push('未见异常');
  }else {
    dgsqkj ? line.push(`${dgsqkj}可见受侵`) : ''
    dgkzVal === '可见' ? line.push(`肝内胆管扩张`) : ''
    dgkzInpVal ? line.push(`最宽处约${dgkzInpVal}mm`) : ''
    dskzVal === '有' ? str += '胆管可见胆栓形成' : '';
    dskzInpVal ? str += '，' + dskzInpVal : ''
    str ? line.push(str) : '';
    str = '';
  }
  line.length > 0 ? descriptionArr.push("　　胆管：" + line.join('，')) : ''
  line = []
  //-------------------------------------------------------------------------------
  if (lbjVal && lbjVal === '无异常') { 
    line.push('未见异常');
  }else {
    xgjVal ? str += xgjVal : '' 
    xgjsl ? str += `，${xgjsl}枚` : ''
    xgjdj ? str += `，最大短径约${xgjdj}mm` : '';
    xgjzq ? str += `，增强${xgjzq}` : '';
    str ? line.push(str) : '';
    str = '';
    gmbVal ? str += gmbVal : ''
    gmbsl ? str += `，${gmbsl}枚` : ''
    gmbdj ? str += `，最大短径约${gmbdj}mm` : '';
    gmbzq ? str += `，增强${gmbzq}` : '';
    str ? line.push(str) : '';
    str = '';
    fmhVal ? str += fmhVal : ''
    fmhsl ? str += `，${fmhsl}枚` : ''
    fmhdj ? str += `，最大短径约${fmhdj}mm` : '';
    fmhzq ? str += `，增强${fmhzq}` : '';
    str ? line.push(str) : '';
    str = '';
    lbjqtInpVal ? str += lbjqtInpVal : ''
    lbjqtsl ? str += `，${lbjqtsl}枚` : ''
    lbjqtdj ? str += `，最大短径约${lbjqtdj}mm` : '';
    str ? line.push(str) : '';
    str = '';
    var lbjzs = Number(xgjsl || '0') + Number(gmbsl || '0') + Number(fmhsl || '0') + Number(lbjqtsl || '0');
    var timst = line.length > 1 ? '分别位于' : ''
    lbjzs ? line[0] = `${lbjzs}枚可疑淋巴结转移，${timst}${line[0]}` : ''
  }
  line.length > 0 ? descriptionArr.push("　　淋巴结：" + line.join('；')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  fbwz ? str += fbwz : ''
  fbwzInpVal ? str += `（${fbwzInpVal}）` : '';
  str ? line.push(str) : ''
  str = ''
  yczygt ? str += yczygt : '';
  yczygtInpVal ? str += `（${yczygtInpVal}）` : '';
  str ? line.push(str) : ''
  str = ''
  yczyqtInpVal ? line.push(yczyqtInpVal) : '';
  line.length > 0 ? descriptionArr.push("　　远处转移：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  gnzInpVal ? line.push(gnzInpVal) : ''
  gxglInpVal ? line.push(gxglInpVal) : ''
  glxqInpVal ? line.push(glxqInpVal) : ''
  line.length > 0 ? descriptionArr.push("　　肝良性占位：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (dnVal && dnVal === '无异常') { 
    line.push('大体形态未见异常，壁无增厚，未见异常密度影');
  } else {
    pzxt && dnxt === '增大' ? line.push(`形态${dnxt}`) : '';
    dnbzh ? line.push(`胆囊壁${dnbzh}`) : '';
    dndx1 || dndx2 ? line.push(`大小约：${dndx1}mmx${dndx2}mm`) : '';
    dnjsInpVal ? line.push(dnjsInpVal) : '';
    dzncInpVal ? line.push(dzncInpVal) : '';
    xjzInpVal ? line.push(xjzInpVal) : '';
    dnyInpVal ? line.push(dnyInpVal) : '';
    xrInpVal ? line.push(xrInpVal) : '';
    dnqtInpVal ? line.push(dnqtInpVal) : '';
  }
  line.length > 0 ? descriptionArr.push("　　胆囊：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (pzVal && pzVal === '无异常') {
    line.push('大体形态未见异常，未见异常密度影');
  } else {
    pzxt && pzxt !== '其他' && pzxt !== '正常' ? line.push(`形态${pzxt}`) : '';
    pzxtqt ? line.push(`形态${pzxtqt}`) : '';
    pzdx1 || pzdx2 ? line.push(`大小约：${pzdx1}mmx${pzdx2}mm`) : '';
    pnzInpVal ? line.push(pnzInpVal) : '';
    pzqtInpVal ? line.push(pzqtInpVal) : '';
  }
  line.length > 0 ? descriptionArr.push("　　脾脏：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  if (yxVal && yxVal === '无异常') {
    line.push('大体形态未见异常，未见异常密度影');
  } else {
    yxxt && yxxt !== '其他' && yxxt !== '正常' ? line.push(`形态${yxxt}`) : '';
    yxxtqt ? line.push(`形态${yxxtqt}`) : '';
    yxnzInpVal ? line.push(yxnzInpVal) : '';
    ipmnInpVal ? line.push(ipmnInpVal) : '';
    yxyInpVal ? line.push(yxyInpVal) : '';
    sjnfbInpVal ? line.push(sjnfbInpVal) : '';
    yxqtInpVal ? line.push(yxqtInpVal) : '';
    ygqk ? line.push(ygqk) : '';
  }
  line.length > 0 ? descriptionArr.push("　　胰腺：" + line.join('，')) : ''
  line = [];
  //-------------------------------------------------------------------------------
  fqzfjx && fqzfjx !== '清晰' ? line.push(`腹腔脂肪间隙${fqzfjx}`) : '';
  // fs === "无" ? line.push(`腹腔内无液体密度影`) : line.push(`腹腔内可见${fsyl || ''}液体密度影`);
  fs === "无" ? '' : line.push(`腹腔内可见${fsyl || ''}液体密度影`);
  fsInpVal ? line.push(`${fsInpVal}`) : ''
  fqjxqt ? line.push(fqjxqt) : ''
  fmqdzh ? line.push(fmqdzh + fmzh) : ''
  fmzyInpVal ? line.push(fmzyInpVal) : ''
  line.length > 0 ? descriptionArr.push("　　腹腔：" + line.join('，')) : descriptionArr.push("　　腹腔：未见异常")
  line = [];
  //-------------------------------------------------------------------------------
  qtzx ? descriptionArr.push("　　其他征象：" + qtzx) : '';
  descriptionStr = descriptionArr.join('。\n') + '。';
  console.log('>>>descriptionStr', descriptionStr);
}

function forJmArrFun(list) {
  let arr = []
  for (let i = 0; i < list.length; i++) {
    arr.push(list[i].map(item => getVal($(`input[type=radio][name=${item}]:checked`))));
  }
  return arr;
}

//门静脉、 肝静脉、下腔静脉内容返回
function forJmArrFun2(list, type) {
  let propName = ['病灶分界', '受侵', '血栓', '血栓密度', '癌栓', '癌栓密度'];
  let veinNames = [];
  veinNames = veinNameData[type];
  let tempResults = [];
  for (let i = 0; i < propName.length; i++) {
    let prop = propName[i];
    for (let j = 0; j < list.length; j++) {
      let val = list[j][i] || '';
      if (val) {
        tempResults.push({
          veinNames: veinNames[j],
          keyName: prop,
          value: val,
        })
      }
    }
  }
  let fjArr = [], sqArr = [], xs = [], xsmd = [], as = [], asmd = [];
  fjArr[0] = tempResults.filter(item => item.value === '尚清')
  fjArr[1] = tempResults.filter(item => item.value === '不清')
  sqArr[0] = tempResults.filter(item => item.value === '未见')
  sqArr[1] = tempResults.filter(item => item.value === '可见')
  xs[0] = tempResults.filter(item => item.keyName === '血栓' && item.value === '无')
  xs[1] = tempResults.filter(item => item.keyName === '血栓' && item.value === '有')
  xsmd[0] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '低密度')
  xsmd[1] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '等密度')
  xsmd[2] = tempResults.filter(item => item.keyName === '血栓密度' && item.value === '稍高密度')
  as[0] = tempResults.filter(item => item.keyName === '癌栓' && item.value === '无')
  as[1] = tempResults.filter(item => item.keyName === '癌栓' && item.value === '有')
  asmd[0] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '低密度')
  asmd[1] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '等密度')
  asmd[2] = tempResults.filter(item => item.keyName === '癌栓密度' && item.value === '稍高密度')
  let fjStr = jmStrSet(fjArr, 'fjData');
  let sqStr = jmStrSet(sqArr, 'sqData');
  let xsmdStr = jmStrSet(xsmd, 'mdData', '未');
  let asmdStr = jmStrSet(asmd, 'mdData', '可');
  let strArr = [];
  fjStr ? strArr.push(fjStr) : ''
  // sqStr ? strArr.push(sqStr) : ''
  xsmdStr ? strArr.push(xsmdStr) : ''
  asmdStr ? strArr.push(asmdStr) : ''
  let str = '';
  strArr && strArr.length > 0 ? str = strArr.join('，') : ''
  if(sqStr.length > 1){
    var modifiedStr = str.replace(/尚清/g, '尚清，未见受侵');
    modifiedStr = modifiedStr.replace(/不清/g, '不清，可见受侵');
    str = modifiedStr
  }
  return str;
}
//门静脉、 肝静脉、下腔静脉拼接内容
function jmStrSet(list, type, desc) {
  let keyObj = {
    fjData: { 0: '尚清', 1: '不清' },
    sqData: { 0: '未见', 1: '可见' },
    mdData: { 0: '可见低密度', 1: '可见等密度', 2: '可见稍高密度' }
  }
  let str = '';
  type === 'fjData' ? str = '与病灶分界' : ''
  type === 'sqData' ? str = '受侵' : ''
  type === 'mdData' ? str = `充盈缺损影，增强${desc}见强化` : ''
  let arr = [];
  for (let i = 0; i < list.length; i++) {
    if (list[i] && list[i].length > 0) {
      // type === 'fjData' ? arr.push(list[i].map(item => item.veinNames).join('、')+ str + keyObj[type][i]) : '';
      // type === 'sqData' ? arr.push(list[i].map(item => item.veinNames).join('、') + keyObj[type][i] + str) : '';
      // type === 'mdData' ? arr.push(list[i].map(item => item.veinNames).join('、') + '可见' + keyObj[type][i] + str) : '';
      // arr.push(list[i].map(item => item.veinNames).join('、') + (str === '与病灶分界' ? str : '') + keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      if(str === '与病灶分界' || type === 'mdData') {
        arr.push(list[i].map(item => item.veinNames).join('、') + (str === '与病灶分界' ? str : '') + keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      }else {
        arr.push( keyObj[type][i] + (str !== '与病灶分界' ? str : ''))
      }
    }
  }
  str = ''
  arr && arr.length > 0 ? str = arr.join('，') : '';
  return str;
}
//门静脉、 肝静脉、下腔静脉勾选值获取
function isChecked(list, type) {
  let check = false;
  for (let i = 0; i < list.length; i++) {
    if (type === 'sq' && list[i].indexOf('可见') !== -1) {
      check = true;
      i = list.length;
    }
    if (type === 'xs' && list[i][2] === '有') {
      check = true;
      i = list.length;
    }
    if (type === 'as' && list[i][4] === '有') {
      check = true;
      i = list.length;
    }
  }
  return check;
}

function initId(num) {
  return 'dga-rt-' + num
}

function showRadioInfo(type, radioName) {
  if (type === 'init') {
    for (let i = 0; i < radioNameArr.length; i++) {
      let name = radioNameArr[i];
      let val = getVal($(`input[type=radio][name=${name}]:checked`));
      let secondLastChar = name.charAt(name.length - 2).toUpperCase();
      let id = name.slice(0, name.length - 2) + secondLastChar + name.charAt(name.length - 1);
      val && val === '有' ? $('#' + id).css('display', 'inline-block') : $('#' + id).hide();
    }
  } else {
    let secondLastChar = radioName.charAt(radioName.length - 2).toUpperCase();
    let val = $("input[type='radio'][name='" + radioName + "']:checked").val();
    let id = radioName.slice(0, radioName.length - 2) + secondLastChar + radioName.charAt(radioName.length - 1);
    val && val === '有' ? $('#' + id).css('display', 'inline-block') : $('#' + id).hide();
  }

}

function showTextarea(type, id) {
  if (type === 'init') {
    for (let i = 0; i < textareaIdArr.length; i++) {
      let changeId = Number(textareaIdArr[i].slice(-3));
      changeId = changeId + 1
      $('#' + textareaIdArr[i]).is(':checked') ? $('#dga-rt-' + changeId).show() : $('#dga-rt-' + changeId).hide();
    }
  } else {
    let changeId = Number(id.slice(-3));
    changeId = changeId + 1
    $('#' + id).is(':checked') ? $('#dga-rt-' + changeId).show() : $('#dga-rt-' + changeId).hide();
  }
}

function changeBzSmText() {
  let val1 = $('input[type="radio"][name="bzsm"]:checked').val();
  $('#yfzwzText').text(val1 === '多发' ? '最大位于：' : '位置：');
  $('#yfzdxText').text(val1 === '多发' ? '较大者大小约：' : '大小约：');
  let val2 = $('input[type="radio"][name="zzsm"]:checked').val();
  $('#zzwzText').text(val2 === '多发' ? '最大位于：' : '位置：')
  $('#zzdxText').text(val2 === '多发' ? '较大者大小约：' : '大小约：')
}

function showGyh() {
  for (let i = 0; i < raidoArr1.length; i++) {
    if ($(`#${raidoArr1[i]}`).is(':checked')) {
      $('#dga-rt-132').show();
      i = raidoArr1.length;
      return
    }
    $('#dga-rt-132').hide();
  }
}
function showGmx(text,show) {
  $('.gmx-box').hide();
  $('.gwdzgx-box').hide();
  $('.gnx-box').hide();
  $('.gmxhgwdzgx').hide();
  $('#dga-rt-818').val(text)
  $('label[for="dga-rt-737"]').show()
  $('label[for="dga-rt-630"]').show()
  $('.gnx').css('cssText', 'border-bottom: 1px solid #dcdfe6')
  $('.gmx').css('cssText', 'border-bottom: 1px solid #dcdfe6')
  $('.gwdzgx').css('cssText', 'border-bottom: 1px solid #dcdfe6')
  if(text === '肝内型' || !text){
    $('.gnx-box').show();
    $('.gnx').css('cssText', 'border-bottom: none;background: #fff')
    $('label[for="dga-rt-737"]').hide()
    $('label[for="dga-rt-630"]').hide()
    if(show){
      $('.gmx').hide()
      $('.gwdzgx').hide()
      $('.head-right').css('cssText', 'width: calc(100% - 79px)')
    }
  }
  if(text === '肝门型'){
    $('.gmx-box').show();
    $('.gmxhgwdzgx').show();
    $('.gmx').css('cssText', 'border-bottom: none;background: #fff')
    if(show){
      $('.gnx').hide()
      $('.gwdzgx').hide()
      $('.head-right').css('cssText', 'width: calc(100% - 79px)')
    }
  }
  if(text === '肝外胆总管型'){
    $('.gwdzgx-box').show();
    $('.gmxhgwdzgx').show();
    $('.gwdzgx').css('cssText', 'border-bottom: none;background: #fff')
    if(show){
      $('.gnx').hide()
      $('.gmx').hide()
      $('.head-right').css('cssText', 'width: calc(100% - 122px)')
    }
  }
}
function initClickFun() {

  for (let j = 0; j < radioNameArr.length; j++) {
    $(`input[type="radio"][name="${radioNameArr[j]}"]`).click(function (e) {
      showRadioInfo('click', $(this).attr('name'));
    })
  }

  $('input[type=radio][name=zfg]').click(function (e) {
    let val = $('input[type=radio][name=zfg]:checked');
    val === '有' ? $('#dga-rt-590').show() : $('#dga-rt-590').hide()
  })

  $('input[type=radio][name=gztgz]').click(function (e) {
    let val = $('input[type=radio][name=gztgz]:checked');
    val === '有' ? $('#dga-rt-593').show() : $('#dga-rt-593').hide()
  })
  $('.head-itme').click(function (e) {
    // $('.gnx').css('cssText', 'border-bottom: 1px solid #dcdfe6')
    // $('.gmx').css('cssText', 'border-bottom: 1px solid #dcdfe6')
    // $('.gwdzgx').css('cssText', 'border-bottom: 1px solid #dcdfe6')
    // $(this).css('cssText', 'border-bottom: none;background: #fff')
    showGmx($(this).text())
  })
  $('.bz-item').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus2[id]) {
      e.preventDefault();
    } else {
      // target.prop('checked', true)
    }
    var block = _this.attr('block-name2');
    var radioButtons = $( `.${block} input[type="radio"]`);
    if (target.is(':checked')) {
      isFillInStatus2[id] = '1';
      radioChed(radioButtons)
      // radioButtons.filter('[value="类圆形"]').prop('checked', true);
      // radioButtons.filter('[value="清楚"]').prop('checked', true);
      // radioButtons.filter('[value="均匀"]').prop('checked', true);
      // radioButtons.filter('[value="无"]').prop('checked', true);
    }else {
      radioButtons.filter('[value="类圆形"]').prop('checked', false);
      radioButtons.filter('[value="清楚"]').prop('checked', false);
      radioButtons.filter('[value="均匀"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('visit-status').siblings().removeClass('visit-status');
    $("." + block).show().siblings('.block2').hide();
    block === 'zz-block' ? $('.boryfz').css('cssText', 'border-bottom: 1px solid #dcdfe6') : $('.boryfz').css('cssText', 'border-bottom: none')
    block === 'yfz-block' ? $('.borzd').css('cssText', 'border-bottom: 1px solid #dcdfe6') : $('.borzd').css('cssText', 'border-bottom: none')
    // $("." + block).css('cssText', 'border-bottom: none').siblings('.block2').css('cssText', 'border-bottom: 1px solid #dcdfe6');
  })
  function radioChed(data) {
    if(!data.filter('[value="类圆形"]').is(':checked') && !data.filter('[value="不规则形"]').is(':checked')){
      data.filter('[value="类圆形"]').prop('checked', true);
    }
    if(!data.filter('[value="清楚"]').is(':checked') && !data.filter('[value="欠清"]').is(':checked') && !data.filter('[value="不清楚"]').is(':checked')){
      data.filter('[value="清楚"]').prop('checked', true);
    }
    if(!data.filter('[value="均匀"]').is(':checked') && !data.filter('[value="不均匀"]').is(':checked')){
      data.filter('[value="均匀"]').prop('checked', true);
    }
    if(!data.filter('[value="无"]').is(':checked') && !data.filter('[value="有"]').is(':checked')){
      data.filter('[value="无"]').prop('checked', true);
    }
  }
  $('.ck-inp2').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus2[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus2[id];
      // $(this).parents().find('.block3').hide();
    }
  })

  $("input[type='radio'][name='bzsm']").click(function (e) {
    changeBzSmText();
  })
  $("input[type='radio'][name='zzsm']").click(function (e) {
    changeBzSmText();
  })
  $("input[type='radio'][name='dg']").click(function (e) {
    let val = $('input[type="radio"][name="dg"]:checked').val();
    if(val === '可见异常') {
      $('input[type="radio"][name="dgsq"][value="未见"]').prop('checked', true);
      $('input[type="radio"][name="dgkz"][value="未见"]').prop('checked', true);
      $('input[type="radio"][name="dskz"][value="无"]').prop('checked', true);
    }
  })


  var changeRadio2 = {
    yfzpsmd2: '45',
    yfzdgqf: '635',
    yfzsydg: '646',
    zzdgqf: '742',
    zzpsmd2: '100',
    yfzdgqfkz: '668',
    zzyfzsydg: '753', 
    zzxydg: '761',
    zzdgqfkz: '775',
    zzxydgkz: '783',
    yfzxydg: '654',
    yfzxydgkz: '676',
    dg: '468',
    lbj: '484',
    dn: '517',
    pz: '537',
    yx: '549',
  }
  $("input[type='radio'][name='gyh']").click(function (e) {
    showGyh();
  })
  for (let key in changeRadio2) {
    $(`input[type='radio'][name='${key}']`).click(function (e) {
      $(`#dga-rt-${changeRadio2[key]}`).is(':checked') ? $(`#dga-rt-${changeRadio2[key]}-cont`).show() : $(`#dga-rt-${changeRadio2[key]}-cont`).hide();
    })
  }

  for (let i = 0; i < textareaIdArr.length; i++) {
    $('#' + textareaIdArr[i]).click(function (e) {
      showTextarea('click', e.target.id)
    })
  }

  $('.block-ck1').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus1[id]) {
      e.preventDefault();
    }
    var block = _this.attr('block-name1');
    var radioButtons = $( `.${block} input[type="radio"]`);
    
    
    if (target.is(':checked')) {
      isFillInStatus1[id] = '1'; 
      radioBto(radioButtons)
    }else {
      radioButtons.filter('[value="尚清"]').prop('checked', false);
      radioButtons.filter('[value="未见"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    $("." + block).show().siblings('.block1').hide();
  })
  $('.sel-click').click(function (e) {
    var _this = $(this);
    _this.addClass('sel-light').siblings().removeClass('sel-light');
  })
  $('input[type="radio"][name="dxcl"]').click(function (e) {
    let val = $('input[type="radio"][name="dxcl"]:checked').val();
    console.log(val);
    $('.zkx-t').hide();
    $('.gbpr-t').hide();
    $('.dglx-t').hide();
    $('.hhx-t').hide();
    if(val === '肿块型') $('.zkx-t').show();
    if(val === '管壁浸润型') $('.gbpr-t').show();
    if(val === '导管内型') $('.dglx-t').show();
    if(val === '混合型') $('.hhx-t').show();
  })
  $('input[type="radio"][name="zzdxcl"]').click(function (e) {
    let val = $('input[type="radio"][name="zzdxcl"]:checked').val();
    $('.zzzkx-t').hide();
    $('.zzgbpr-t').hide();
    $('.zzdglx-t').hide();
    $('.zzhhx-t').hide();
    if(val === '肿块型') $('.zzzkx-t').show();
    if(val === '管壁浸润型') $('.zzgbpr-t').show();
    if(val === '导管内型') $('.zzdglx-t').show();
    if(val === '混合型') $('.zzhhx-t').show();
  })
  function radioBto(data) {
    if(!data.filter('[value="尚清"]').is(':checked') && !data.filter('[value="不清"]').is(':checked')){
      data.filter('[value="尚清"]').prop('checked', true);
    }
    if(!data.filter('[value="未见"]').is(':checked') && !data.filter('[value="可见"]').is(':checked')){
      data.filter('[value="未见"]').prop('checked', true);
    }
    if(!data.filter('[value="无"]').is(':checked') && !data.filter('[value="有"]').is(':checked')){
      data.filter('[value="无"]').prop('checked', true);
    }
  }

  $('.ck-inp1').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus1[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus1[id];
      if (id === 'dga-rt-149') {
        let list = radioNameArr.slice(0, 2);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-150') {
        delete isFillInStatus3['dga-rt-168'];
        delete isFillInStatus3['dga-rt-169'];
        delete isFillInStatus3['dga-rt-170'];
        delete isFillInStatus3['dga-rt-171'];
        $('#zzhbXs').hide();
        $('#zzhbAs').hide();
        $('#zzhbXs').hide();
        $('#zzhbAs').hide();
        let list = radioNameArr.slice(2, 10);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-151') {
        delete isFillInStatus3['dga-rt-228'];
        delete isFillInStatus3['dga-rt-229'];
        delete isFillInStatus3['dga-rt-230'];
        let list = radioNameArr.slice(10, 16);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-152') {
        let list = radioNameArr.slice(16, 18);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-152') {
        let list = radioNameArr.slice(18, 20);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-301') {
        let list = radioNameArr.slice(20, 22);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-302') {
        let list = radioNameArr.slice(22, 24);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-303') {
        let list = radioNameArr.slice(24, 26);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-304') {
        delete isFillInStatus3['dga-rt-348'];
        delete isFillInStatus3['dga-rt-349'];
        delete isFillInStatus3['dga-rt-350'];
        let list = radioNameArr.slice(26, 32);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-305') {
        let list = radioNameArr.slice(32, 34);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-407') {
        let list = radioNameArr.slice(34, 36);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-408') {
        let list = radioNameArr.slice(36, 38);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-409') {
        let list = radioNameArr.slice(38, 40);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-410') {
        let list = radioNameArr.slice(40, 42);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
    }
  })

  $('.block-ck3').click(function (e) {
    var _this = $(this);
    var target = $(this).find('input');
    var id = target.attr('id');
    if (isFillInStatus3[id]) {
      e.preventDefault();
    }
    var block = _this.attr('block-name3');
    var radioButtons = $( `.${block} input[type="radio"]`);
    if (target.is(':checked')) {
      isFillInStatus3[id] = '1';
      radioBto(radioButtons)
    }else {
      radioButtons.filter('[value="尚清"]').prop('checked', false);
      radioButtons.filter('[value="未见"]').prop('checked', false);
      radioButtons.filter('[value="无"]').prop('checked', false);
    }
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    $("." + block).show().siblings('.block3').hide();
  })

  $('.ck-inp3').click(function (e) {
    var id = $(this).attr('id');
    if (isFillInStatus3[id]) {
      $(this).prop('checked', false);
    }
    if (!$(this).is(':checked')) {
      delete isFillInStatus3[id];
      // $(this).parents().find('.block3').hide();
      if (id === 'dga-rt-168') {
        let list = radioNameArr.slice(2, 4);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-169') {
        let list = radioNameArr.slice(4, 6);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-170') {
        let list = radioNameArr.slice(6, 8);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-171') {
        let list = radioNameArr.slice(8, 10);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-228') {
        let list = radioNameArr.slice(10, 12);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-229') {
        let list = radioNameArr.slice(12, 14);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-230') {
        let list = radioNameArr.slice(14, 16);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-348') {
        let list = radioNameArr.slice(26, 28);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-349') {
        let list = radioNameArr.slice(28, 30);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
      if (id === 'dga-rt-350') {
        let list = radioNameArr.slice(30, 32);
        list.forEach(item => {
          hideRadioInfo(item)
        })
      }
    }
  })
}

function hideRadioInfo(radioName) {
  let secondLastChar = radioName.charAt(radioName.length - 2).toUpperCase();
  let id = radioName.slice(0, radioName.length - 2) + secondLastChar + radioName.charAt(radioName.length - 1);
  $('#' + id).hide();
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = descriptionStr;
  rtStructure.impression = curElem.find('#dga-rt-587').val();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}