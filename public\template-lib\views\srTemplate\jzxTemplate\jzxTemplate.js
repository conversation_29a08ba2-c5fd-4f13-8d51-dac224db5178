$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;
var rtStructure = null;
var scoreMap = {
  '-1': {tirads: '2', tiradsDesc: '良性', tip: '2类，良性'},
  '0': {tirads: '3', tiradsDesc: '良性可能', tip: '3类，良性可能'},
  '1': {tirads: '4A', tiradsDesc: '低度可疑恶性', tip: '4A类，低度可疑恶性'},
  '2': {tirads: '4B', tiradsDesc: '中度可疑恶性', tip: '4B类，中度可疑恶性'},
  '3': {tirads: '4C', tiradsDesc: '高度可疑恶性', tip: '4C类，高度可疑恶性'},
  '4': {tirads: '4C', tiradsDesc: '高度可疑恶性', tip: '4C类，高度可疑恶性'},
  '5': {tirads: '5', tiradsDesc: '高度提示恶性', tip: '5类，高度提示恶性'},
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
  }
  desctiptHandler();
  curElem.find('.rt-sr-w').change(function() {
    desctiptHandler();
  })
}

function desctiptHandler() {
  var haveTubercle = getVal('[name="RG-0001.001"]:checked');
  var description = '', impression = '';
  var tiradsDesc = '', tirads = '', otherDesc = '';
  if(haveTubercle === '有') {
    var yangxing = [];
    var score = 0;  //分数
    for(var i = 1; i <= 5; i++) {
      if(getVal('[id="jzx-0001.002-cItem--'+i+'"]:checked')) {
        yangxing.push(getVal('[id="jzx-0001.002-cItem--'+i+'"]:checked'))
        score++;
      }
    }
    var yinxing = [];
    for(var j = 1; j <= 1; j++) {
      if(getVal('[id="jzx-0001.003-cItem--'+j+'"]:checked')) {
        yinxing.push(getVal('[id="jzx-0001.003-cItem--'+j+'"]:checked'))
        score--;
      }
    }
    if(yangxing.length || yinxing.length) {
      var zhibiaoStr = '';
      if(yangxing.length) {
        zhibiaoStr = '阳性指标：' + yangxing.join('、') + '；';
      }
      if(yinxing.length) {
        zhibiaoStr += '阴性指标：' + yinxing.join('、') + '；';
      }
      description = '有结节：' + zhibiaoStr;
    } else {
      description = '结节情况：有；'
    }
    otherDesc = '其他征象：' + (getVal('[id="jzx-0001.004"] .rt-sr-w') || '无');
    description = description + '\n' + otherDesc;
    var curScore = scoreMap[score.toString()];
    tirads = curScore.tirads;
    tiradsDesc = '，'+curScore.tiradsDesc+'。'
    impression = 'C-TIRADS分级：'+curScore.tip+'。'
    if(getVal('[id="jzx-0001.004"] .rt-sr-w')) {
      impression += '\n' + otherDesc;
    }
  } else if(haveTubercle === '无') {
    // description = '结节情况：无';
    description = '';
    impression = 'C-TIRADS分级：1类，无结节。'
    tirads = '1';
    tiradsDesc = '，无结节。'
  }
  if(rtStructure) {
    rtStructure.description = description;
    rtStructure.impression = {
      impression: impression,
      tiradsDesc: tiradsDesc,
      tirads: tirads,
      otherDesc: getVal('[id="jzx-0001.004"] .rt-sr-w') ? otherDesc : '',
    };
  }
}

function getVal(selector) {
  var dom = curElem.find(selector);
  var value = '';
  if(dom.length) {
    value = dom.val();
  }
  return value;
}