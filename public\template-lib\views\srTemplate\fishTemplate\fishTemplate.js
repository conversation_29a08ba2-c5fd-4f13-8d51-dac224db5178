$(function() {
  window.initHtmlScript = initHtmlScript;
})
var itemList = [
  // 表格状态
  {
    id:'000',
    itemName:'',
    itemValue:'',
    type:'',
    impression:[],
    defaultValueMap:{
      32:'阳性参考比值',
      136:`本次FLSH实验仅对13/16/18/21/22/X/Y七种染色体数目异常情况进行检测；
存在染色体异常情况的建议遗传咨询；
该结果只针对本样的本次检测。`
    }
  },
  {
    id:'3',
    itemName:'N-MYC扩增基因',
    itemValue:'LSI N-MYC位点特异性探针与CEP2染色体着丝粒特异性探针',
    type:"N-MYC",
    selectOption:[
      {
        id:'1',
        title:'（N-MYC基因无扩增）'
      },
      {
        id:'2',
        title:'（N-MYC基因扩增）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'89,90'
      },
      {
        label:'比值：N-MYC/CEP2= ',
        nodeKey:'91'
      },
      {
        label:'计数细胞数：',
        nodeKey:'92'
      },
      {
        label:'N-MYC信号比值：',
        nodeKey:'93'
      },
      {
        label:'CEP2信号比值：',
        nodeKey:'94'
      },
      // {
      //   label:'注：',
      //   nodeKey:'95'
      // }
    ],
    defaultValueMap:{
      89:'阴性',
      90:'（N-MYC基因无扩增）',
      95:`1.蜡块用于FISH检测，此结果只针对本样本。
2.骨髓液FISH染色无法区分是否存在转移的神经母细胞。
3.本例未见N-MYC阳性细胞，此结果只针对本样本。`,
      92:'50',
      166:`NMYC/CEP信号比值>4为阳性，即NMYC扩增，否则为阴性；
1＜NMYC/CEP 比值＜4，为NMYC获得；
NMYC/CEP比值≤１，为NMYC 正常。`
    },
    yinYangId:'90'
  },
  {
    id:'4',
    itemName:'C-MYC断裂基因',
    itemValue:'GLP C-MYC位点特异性探针',
    type:'C-MYC',
    selectOption:[
      {
        id:'1',
        title:'（C-MYC基因无断裂）'
      },
      {
        id:'2',
        title:'（C-MYC基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'39,40'
      },
      {
        label:'计数细胞数：',
        nodeKey:'41'
      },
      {
        label:'异常细胞数：',
        nodeKey:'42'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'43'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'44'
      },
      // {
      //   label:'注：',
      //   nodeKey:'45'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'46'
      // }
    ],
    defaultValueMap:{
      39:'阴性',
      40:'(C-MYC基因无断裂)',
      45:`蜡块用于FISH检测，此结果只针对本样本。`,
      46:`阳性阈值（计数100个细胞，分析C-MYC基因断裂比值）：
石蜡标本：阈值15%`,
      41:'100'
    },
     yinYangId:'40'
  },
  {
    id:'5',
    itemName:'HER-2扩增基因',
    itemValue:'GLP HER-2位点特异性探针与CSP17染色体着丝粒特异性探针',
    type:'HER-2',
    selectOption:[
      {
        id:'1',
        title:'（HER-2基因无扩增）'
      },
      {
        id:'2',
        title:'（HER-2基因扩增）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'81,82'
      },
      {
        label:'比值：Her-2/CSP17= ',
        nodeKey:'83'
      },
      {
        label:'计数细胞数：',
        nodeKey:'84'
      },
      {
        label:'Her-2信号比值：',
        nodeKey:'85'
      },
      {
        label:'CSP17信号比值：',
        nodeKey:'86'
      },
      // {
      //   label:'注：',
      //   nodeKey:'87'
      // },
      // {
      //   label:'判断标准：中国乳腺癌HER2检测指南（2019版）',
      //   nodeKey:'88'
      // }
    ],
    // 默认值
    defaultValueMap:{
      81:'阴性',
      82:'（HER-2基因无扩增）',
      87:`蜡块用于FISH检测，此结果只针对本样本。`,
      88:`FISH结果阳性：
①Her-2/CSP17≥2.0，Her-2信号比值≥4；
②Her-2/CSP17＜2.0，Her-2信号比值≥6.0。
FISH结果阴性：Her-2信号比值＜4。
FISH结果不确定：Her-2信号比值≥4.0且＜6.0,需结合IHC结果，IHC结果为3+，判FISH为阳性； IHC结果为0、1+或2+，判FISH为阴性。`,
      84:'20'
    },
     yinYangId:'82'
  },
//   {
//     id:'6',
//     itemName:'CIC断裂基因',
//     itemValue:'CIC(19q13)位点特异性探针',
//     type:'CIC',
//     selectOption:[
//       {
//         id:'1',
//         title:'（CIC基因无断裂）'
//       },
//       {
//         id:'2',
//         title:'（CIC基因断裂）'
//       }
//     ],
//     impression:[
//       {
//         label:'FISH诊断：',
//         nodeKey:'49,50'
//       },
//       {
//         label:'计数细胞数：',
//         nodeKey:'51'
//       },
//       {
//         label:'异常细胞数：',
//         nodeKey:'52'
//       },
//       {
//         label:'正常细胞比例：',
//         nodeKey:'53'
//       },
//       {
//         label:'异常细胞比例：',
//         nodeKey:'54'
//       },
//       {
//         label:'注：',
//         nodeKey:'55'
//       },
//       {
//         label:'标准：',
//         nodeKey:'56'
//       }
//     ],
//     defaultValueMap:{
//       49:'阴性',
//       50:'(CIC基因无断裂)',
//       55:`蜡块用于FISH检测，此结果只针对本样本。`,
//       56:`阳性阈值（计数100个细胞，分析CIC基因断裂比值）：
// 石蜡标本：阈值15%`
//     }
//   },
  {
    id:'7',
    itemName:'ETV6断裂基因',
    itemValue:'LSI  ETV6位点特异性探针',
    type:'ETV6',
    selectOption:[
      {
        id:'1',
        title:'（ETV6基因无断裂）'
      },
      {
        id:'2',
        title:'（ETV6基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'57,58'
      },
      {
        label:'计数细胞数：',
        nodeKey:'59'
      },
      {
        label:'异常细胞数：',
        nodeKey:'60'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'61'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'62'
      },
      // {
      //   label:'注：',
      //   nodeKey:'63'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'64'
      // }
    ],
    defaultValueMap:{
      57:'阴性',
      58:'（ETV-6基因无断裂）',
      63:'蜡块用于FISH检测，此结果只针对本样本。',
      64:`阳性阈值（计数100个细胞，分析ETV6基因断裂比值）：
石蜡标本：阈值15%`,
59:'100'
    },
     yinYangId:'58'
  },
  {
    id:'8',
    itemName:'EWSR1断裂基因',
    itemValue:'GLP EWSR1位点特异性探针',
    type:'EWSR1',
    selectOption:[
      {
        id:'1',
        title:'（EWSR1基因无断裂）'
      },
      {
        id:'2',
        title:'（EWSR1基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'65,66'
      },
      {
        label:'计数细胞数：',
        nodeKey:'67'
      },
      {
        label:'异常细胞数：',
        nodeKey:'68'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'69'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'70'
      },
      // {
      //   label:'注：',
      //   nodeKey:'71'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'72'
      // }
    ],
    defaultValueMap:{
      65:'阴性',
      71:'蜡块用于FISH检测，此结果只针对本样本。',
      72:`阳性阈值（计数100个细胞，分析EWSR1基因断裂比值）：
石蜡标本：阈值15%`,
      67:'100',
      66:'（EWSR1基因无断裂）'
    },
     yinYangId:'66'
  },
  {
    id:'9',
    itemName:'FKHR(F0X01)断裂基因',
    itemValue:'FKHR(13q14)基因断裂探针',
    type:'FKHR',
    selectOption:[
      {
        id:'1',
        title:'（FKHR基因无断裂）'
      },
      {
        id:'2',
        title:'（FKHR基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'73,74'
      },
      {
        label:'计数细胞数：',
        nodeKey:'75'
      },
      {
        label:'异常细胞数：',
        nodeKey:'76'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'77'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'78'
      },
      // {
      //   label:'注：',
      //   nodeKey:'79'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'80'
      // }
    ],
    defaultValueMap:{
      73:'阴性',
      79:`蜡块用于FISH检测，此结果只针对本样本。
2.本例红绿信号均扩增，红色信号平均约23.05，绿色信号平均约4.35。无黄色信号，判读为FKHR基因断裂阳性。`,
      80:`阳性阈值（计数100个细胞，分析FKHR基因断裂比值）：
石蜡标本：阈值15%`,
      75:'100',
      74:'（FKHR基因无断裂）'
    },
     yinYangId:'74'
  },
  {
    id:'10',
    itemName:'PLAG1基因',
    itemValue:'PLAG1(8q12)位点特异性探针',
    type:'PLAG1',
    selectOption:[
      {
        id:'1',
        title:'（PLAG1基因无断裂）'
      },
      {
        id:'2',
        title:'（PLAG1基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'96,97'
      },
      {
        label:'计数细胞数：',
        nodeKey:'98'
      },
      {
        label:'异常细胞数：',
        nodeKey:'99'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'100'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'101'
      },
      // {
      //   label:'注：',
      //   nodeKey:'102'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'103'
      // }
    ],
    defaultValueMap:{
      96:'阴性',
      97:'（PLAG1基因无断裂）',
      102:'蜡块用于FISH检测，此结果只针对本样本。',
      103:`阳性阈值（计数100个细胞，分析PLAG1基因断裂比值）：
石蜡标本：阈值15%`,
      98:'100'
    },
    yinYangId:'97'
  },
  {
    id:'11',
    itemName:'SS18(SYT)断裂基因',
    itemValue:'GLP SYT 位点特异性探针',
    type:'SYT',
    selectOption:[
      {
        id:'1',
        title:'（SYT基因无断裂）'
      },
      {
        id:'2',
        title:'（SYT基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'104,105'
      },
      {
        label:'计数细胞数：',
        nodeKey:'106'
      },
      {
        label:'异常细胞数：',
        nodeKey:'107'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'108'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'109'
      },
      // {
      //   label:'注：',
      //   nodeKey:'110'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'111'
      // }
    ],
    defaultValueMap:{
      104:'阴性',
      105:'(SYT基因无断裂)',
      110:'蜡块用于FISH检测，此结果只针对本样本。',
      111:`阳性阈值（计数100个细胞，分析SYT基因断裂比值）：
石蜡标本：阈值15%`,
      106:'100'
    },
    yinYangId:'105'
  },
  {
    id:'12',
    itemName:'USP6断裂基因',
    itemValue:'USP6(17P13)基因断裂位点特异性探针',
    type:'USP6',
    selectOption:[
      {
        id:'1',
        title:'（USP6基因无断裂）'
      },
      {
        id:'2',
        title:'（USP6基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'112,113'
      },
      {
        label:'计数细胞数：',
        nodeKey:'114'
      },
      {
        label:'异常细胞数：',
        nodeKey:'115'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'116'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'117'
      },
      // {
      //   label:'注：',
      //   nodeKey:'118'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'119'
      // }
    ],
    defaultValueMap:{
      112:'阴性',
      113:'(USP6基因无断裂)',
      118:'蜡块用于FISH检测，此结果只针对本样本。',
      119:`阳性阈值（计数100个细胞，分析USP6基因断裂比值）：
石蜡标本：阈值15%`,
      114:'100'
    },
    yinYangId:'113'
  },
  {
    id:'13',
    itemName:'BRAF/KIAA1549融合基因',
    itemValue:'BRAF/KIAA1549融合基因t（7;7）',
    type:'BRAF/KIAA1549',
    selectOption:[
      {
        id:'1',
        title:'（BRAF/KIAA1549基因未发生融合）'
      },
      {
        id:'2',
        title:'（BRAF/KIAA1549基因发生融合）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'128,129'
      },
      {
        label:'计数细胞数：',
        nodeKey:'130'
      },
      {
        label:'异常细胞数：',
        nodeKey:'131'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'132'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'133'
      },
      // {
      //   label:'注：',
      //   nodeKey:'134'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'135'
      // }
    ],
    defaultValueMap:{
      128:'阴性',
      129:'（BRAF/KIAA1549基因未发生融合）',
      134:'蜡块用于FISH检测，此结果只针对本样本。',
      135:`阳性阈值（计数100个细胞，分析CIC基因断裂比值）：
石蜡标本：阈值15%`,
      130:'100'
    },
    yinYangId:'129'
  },
  {
    id:'14',
    itemName:'ALK断裂基因',
    itemValue:'GSP ALK（Centromere）/ GSP ALK（Telomere）探针',
    type:'ALK',
    selectOption:[
      {
        id:'1',
        title:'（ALK基因无断裂）'
      },
      {
        id:'2',
        title:'（ALK基因断裂）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'150,151'
      },
      {
        label:'计数细胞数：',
        nodeKey:'152'
      },
      {
        label:'异常细胞数：',
        nodeKey:'153'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'154'
      },
      {
        label:'异常细胞比例：',
        nodeKey:'155'
      },
      // {
      //   label:'注：',
      //   nodeKey:'156'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'157'
      // }
    ],
    defaultValueMap:{
      150:'阴性',
      151:'（ALK基因无断裂）',
      156:'蜡块用于FISH检测，此结果只针对本样本。',
      157:`阳性阈值（计数100个细胞，分析ALK基因断裂比值）：
石蜡标本：阈值15%`,
      152:'100'
    },
    yinYangId:'151'
  },
  {
    id:'15',
    itemName:'11q23.3/11q24.3基因缺失',
    itemValue:'GSP11q23.3 位点特异性探针(R-红色)GSP11q24.3位点特异性探针（G-绿色）',
    type:'11q23.3/11q24.3',
    selectOption:[
      {
        id:'1',
        title:'（11q23.3/11q24.3基因未出现异常）'
      },
      {
        id:'2',
        title:'（11q23.3基因出现异常）'
      },
      {
        id:'3',
        title:'（11q24.3基因出现异常）'
      },
      {
        id:'4',
        title:'（11q23.3/11q24.3基因出现异常）'
      }
    ],
    impression:[
      {
        label:'FISH诊断：',
        nodeKey:'158,159'
      },
      {
        label:'计数细胞数：',
        nodeKey:'160'
      },
      {
        label:'正常细胞比例：',
        nodeKey:'161'
      },
      {
        label:'1R2G，3R2G信号类型细胞的比例：',
        nodeKey:'162'
      },
      {
        label:'2R1G，2R3G信号类型细胞的比例：',
        nodeKey:'163'
      },
      // {
      //   label:'注：',
      //   nodeKey:'164'
      // },
      // {
      //   label:'标准：',
      //   nodeKey:'165'
      // }
    ],
    defaultValueMap:{
      158:'阴性',
      159:'（11q23.3/11q24.3基因未出现异常）',
      164:'蜡块用于FISH检测，此结果只针对本样本。',
      165:`阳性阈值（计数100个细胞，分析11q23.3/11q24.3基因异常细胞比值）：
石蜡标本：阈值20%`,
      160:'100'
    },
    yinYangId:'159'
  }
]
var sampleTypeList = [
  // {
  //   id:'1',
  //   title:'TCT采集细胞'
  // },
  // {
  //   id:'2',
  //   title:'LCT采集细胞'
  // },
  {
    id:'3',
    title:'石蜡包埋组织'
  },
  // {
  //   id:'4',
  //   title:'生理盐水收集细胞'
  // },
  // {
  //   id:'5',
  //   title:'其他'
  // }
  {
    id:'6',
    title:'骨髓液'
  }
]
var bzArr = [46,56,64,72,80,88,166,103,111,119,135,157,165]
// 获取分子病理原检查医嘱开单信息
function getSampleOrganList() {
  var markInfo = {};
  var params = {
    examNo: publicInfo.examNo,
    orderType:'分子病理'
  }
  fetchAjax({
    url: api.getOrderMarkerInfo,
    data: JSON.stringify(params || {}),
    async: false,
    successFn: function (res) {
      
      if (res.status == '0' && res.result && res.result.length) {
        markInfo = res.result[0];
      }
    },
  })
  return markInfo;
}
var hideClassName = 'rt-hide'
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; // 判断模板是否编辑过
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer:  document.querySelector('#fish1 .preview'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPreview()
    } else {
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function initPage(){
  initDefaultMap()
  initItemListSelect()
  fishLinkage()
  editHeaderChange()
  calcValue()
  $('input,textarea').attr('autocomplete','off')
  if(!isSavedReport){
   let markerInfo =  getSampleOrganList()
   if(markerInfo.candleId){
      let list = ['fish-rt-45','fish-rt-55','fish-rt-63','fish-rt-71','fish-rt-79','fish-rt-87','fish-rt-95','fish-rt-102','fish-rt-110','fish-rt-118','fish-rt-123','fish-rt-127','fish-rt-134']
      list.forEach(item=>{
        let value = $('#'+item).val()
        value = value.replace(/蜡块/,'蜡块'+ markerInfo.candleId)
        $('#'+item).val(value)
      })
   }
  }
}
// 初始化每项下拉框
function initItemListSelect(){
  let arr = ['fish-rt-39','fish-rt-49','fish-rt-57','fish-rt-65','fish-rt-73','fish-rt-81','fish-rt-89','fish-rt-104','fish-rt-112','fish-rt-120','fish-rt-124','fish-rt-128','fish-rt-150','fish-rt-158']
  let initNegative = [
    {
      id:'1',
      title:'阴性'
    },
    {
      id:'2',
      title:'阳性'
    }
  ]
  arr.forEach(item=>{
    initInpAndSel('#'+item,initNegative,initYinYangLinkage)
  })
  let optionList = []
  let optionList2 = []
  itemList.forEach(item=>{
    if(item.id!='000'){
      optionList.push({
        title:item.itemName,
        id:item.id
      })
      optionList2.push({
        title:item.itemValue,
        id:item.id
      })
      let initSelectElem = $(`.edit-content [fish-type="${item.type}"] [initselect="true"]`)
      let id = $(initSelectElem).attr('id')
      if(id){
        initInpAndSel('#'+id,item.selectOption)
      }
    }
  })
  initInpAndSel('#fish-rt-4',sampleTypeList)
  initInpAndSel('#fish-rt-3',optionList,fishLinkage)
  initInpAndSel('#fish-rt-5',optionList2) // 探针
}

function initInpAndSel(idList, optionList, callback) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `${idList}`,
    data: optionList,
    click: function(obj) {
      this.elem.val(obj.title);
      callback && callback(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  });
}
/**
 * @description 检测项目改变时改变检测位点及采用的探针
*/
function fishLinkage(obj){
  if(obj){
    let curObj = itemList.find(item=>item.id===obj.id)
    
    let text = ''
    bzArr.forEach((item)=>{
      if(curObj.defaultValueMap[item]){
        text = curObj.defaultValueMap[item]
        $('#fish-rt-136').val(text)
      }
    })
    $('#fish-rt-5').val(curObj.itemValue)
    $('#fish-rt-47').val(curObj.id)
    $('#fish-rt-48').val(curObj.type)
    fishTypeHandler()
  }
}
// 表格单选框变化
function editHeaderChange(){
    function flagHandler(flag){
      if(flag==='notTable'){
        notTableHandler()
        if(!isSavedReport){
          $('#fish-rt-4').val('石蜡包埋组织')
        }
      }else {
        tableHandler()
        if(!isSavedReport){
          $('#fish-rt-4').val('石蜡包埋组织')
        }
      }
    }
  let val = $('.edit-header input:checked').val()
  flagHandler(val)
  $('.edit-header input').on('change',function(){
    let flag = $(this).val()
    flagHandler(flag)
  })
}
// 不是表格的情况下
function notTableHandler(){
   $('.edit-content>[data-type="isTable"] .rt-sr-w' ).addClass(hideClassName)
   $('.edit-content>[data-type="isTable"]').hide()
   $('.edit-content>[data-type="notTable"]').show()
   fishTypeHandler()
}
// 是表格的情况下
function tableHandler(){
  $('.edit-content>[data-type="notTable"] .rt-sr-w' ).addClass(hideClassName)
  $('.edit-content>[data-type="isTable"] .rt-sr-w' ).removeClass(hideClassName)
  $('.edit-content>[data-type="notTable"]').hide()
  $('.edit-content>[data-type="isTable"]').show()
}
// 获取当前模板的模板类型
function getCurrentDataType(){
  return {
    id:$('#fish-rt-47').val(),
    type:$('#fish-rt-48').val()
  }
}
// 不是表格情况下展示哪个模板
function fishTypeHandler(){
  let currentData = getCurrentDataType()
  $('.edit-content [fish-type]').hide()
  $('.edit-content [fish-type] .rt-sr-w').addClass(hideClassName)
  $('.edit-content [fish-type="'+currentData.type+'"]').show()
  $('.edit-content [fish-type="'+currentData.type+'"] .rt-sr-w').removeClass(hideClassName)
}
function initPreview(){
  curElem.find('.preview [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } 
    }else {
      $(this).hide();
    }
  });
  curElem.find('.preview [data-key]').each(function(){
    var key = $(this).attr('data-key')
    var idAnVal,value;
    if(key==='report-img'){
      if(rptImageList && rptImageList.length){
        rptImageList = rptImageList.slice(0,2)
        let html = ''
        rptImageList.forEach(item=>
            html+=`<img src="${item.src}" alt=""">`
        )
        $(this).html(html)
      }else{
        $(this).hide()
      }
      return
    }
    // 兜底
    idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
    value = publicInfo[key] || idAnVal;
    
    this.style.whiteSpace = 'pre-wrap'
    $(this).html(value)
    addIdToNodeByView(this, key, idAndDomMap);
  })
  isTablePreview()
  // curElem.find('.preview [data-img]').each(function() {
  //   var key = $(this).attr('data-img');
  //   var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
  //   var value = publicInfo[key] || idAnVal;
  //   if (value) {
  //     var src = getSignImgHandler({ staffNo: value });
  //     if (src) {
  //       $(this).attr('src', src);
  //       $(this).show();
  //     } 
  //   }else {
  //     $(this).hide();
  //   }
  // });
}
function getPreviewDataType(){
    return {
      id:idAndDomMap['fish-rt-47'].value,
      type:idAndDomMap['fish-rt-48'].value
    }
}
// 有无表格预览
function isTablePreview(){
  // 1 无表格模板 2有表格
  let flag = ''
  let arr = ['fish-rt-1','fish-rt-2'].forEach(item=>{
    let value = idAndDomMap[item].value
    if(value){
      flag = value
    }
  })
  if(flag==='isTable'){
    $('.preview [view-type="notTable"]').hide()
  }else{
    let currentData = getPreviewDataType()
    $('.preview [view-type="isTable"]').hide()
    // $('.preview [view-fish-type]').hide()
    $('.preview [view-fish-type="'+currentData.type+'"]').show()
  }
}
function getImpression(){
  let flag = $('.edit [pid="fish-rt-38"].rt-checked').val()
  let preFix = '#fish-rt-'
  let defaultMap = {
    [`${preFix}3`]:'检测项目：',
    // [`${preFix}4`]:'样品类型：',
    // [`${preFix}5`]:'检测位点及采用的探针：',
    // [`${preFix}6`]:'厂家：',
  }
  let result = Object.entries(defaultMap).map(([key,value])=>{
    let nodeValue = $(key).val()
    if(nodeValue){
      return value + $(key).val()
    }
  }).filter(Boolean)
  // 不是表格
  if(flag==='notTable'){
    let currentDataType = getCurrentDataType()
    let currentImpression= itemList.find(item=>item.id===currentDataType.id).impression
    currentImpression.forEach(item=>{
      let res = item.label
      item.nodeKey.split(',').forEach(node=>{
        res +=$(preFix+node).val()+ ' '
      })
      result.push(res)
    })
  }else{
    // 表格状态下取值
    // result.push('FISH表现：')
    // let tableRow = [[7,8,9,10,11],[12,13,14,15,16],[17,18,19,20,21],[22,23,24,25,26],[27,28,29,30,31],[32,33,34,35,36]]
    // let rowMap = ['染色体及基因','计数细胞','单体比值','三体比值','四体比值']
    // tableRow.forEach((row,index)=>{
    //  let rowTemplate = row.map((item,itemIndex)=>{
    //     if(index===tableRow.length-1){
    //       if(itemIndex===0){
    //         return rowMap[itemIndex] + $(preFix+item).val() + ':'
    //       }else{
    //         return rowMap[itemIndex] +  $(preFix+item).val()
    //       }
    //     }else{
    //       if(itemIndex===0){
    //         return $(preFix+item).val() + rowMap[itemIndex] + ':'
    //       }else {
    //         return  rowMap[itemIndex] +  $(preFix+item).val()
    //       }
    //     }
    //   })
    //   let firstItem =  rowTemplate.shift()
    //   rowTemplate[0] = firstItem+rowTemplate[0]
    //   result.push(rowTemplate)
    // })
    let isTableMap = {
      [`${preFix}37`]:'诊断分析：',
      // [`${preFix}136`]:'附注：'
    }
    Object.entries(isTableMap).forEach(([key,value])=>{
      let nodeValue =  $(key).val()
      if(nodeValue){
        result.push(value)
        result.push(nodeValue)        
      }
    })
  }
  return result.join('\n')
}
// 初始化默认值，没有保存过值就显示默认值
function initDefaultMap(){
  let preFix = 'fish-rt-'
  itemList.forEach(item=>{
    if(item.id===getCurrentDataType().id){
      bzArr.forEach(text=>{
        item.defaultValueMap[text] && (itemList[0].defaultValueMap[136] = item.defaultValueMap[text])
      })
    }
  })
  itemList.forEach(item=>{
    Object.entries(item.defaultValueMap).forEach(([key,value])=>{
     let serviceValue = idAndDomMap[preFix + key] && idAndDomMap[preFix + key].value
     if(!serviceValue){
        $(`#${preFix + key}`).val(value)
      }
    })
  })
}
// 阴阳性联动效果
function initYinYangLinkage(selectValue){
  let {id,type} = getCurrentDataType()
  let obj = itemList.find(item=>item.id===id)
  if(obj){
    if(obj.id!='15'){
      if(selectValue.id==='1'){
        $(`#fish-rt-${obj.yinYangId}`).val(obj.selectOption[0].title)
      }else{
        $(`#fish-rt-${obj.yinYangId}`).val(obj.selectOption[1].title)
      }
    }else{
      if(selectValue.id==='1'){
        $(`#fish-rt-${obj.yinYangId}`).val(obj.selectOption[0].title)
      }else{
        $(`#fish-rt-${obj.yinYangId}`).val('')
      }
    }
  }
}
// 联动计算
function calcValue(){
  let idList = [{
    start:'#fish-rt-93',
    end:'#fish-rt-94',
    total:'#fish-rt-91'
  },{
    start:'#fish-rt-85',
    end:'#fish-rt-86',
    total:'#fish-rt-83'
  }]
  idList.forEach(item=>{
    let {start,end,total} = item
    function getValue(){
      let startValue = $(start).val()
      let endValue = $(end).val()
      if(startValue.trim()==='' || endValue.trim()===''){
        $(total).val('')
      }else{
        if(isNaN(Number(startValue)) || isNaN(Number(endValue)) ){
          $(total).val('')
        }else{
          $(total).val(((startValue/endValue)).toFixed(2))
        }
      }
    }
    $(start).on('input',function(e){
      getValue()
    })
    $(end).on('input',function(e){
      getValue()      
    })
  })
}