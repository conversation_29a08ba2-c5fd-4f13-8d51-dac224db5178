/*-------公共--------*/
.w-con {
  display: inline-block;
  vertical-align: middle;
}

/*-------边距--------*/
.mb-8 {
  margin-bottom: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-14 {
  margin-left: 14px;
}
.ml-22 {
  margin-left: 22px;
}

/*-------宽度--------*/
.wd-80 {
  width: 80px;
}

/*-------总体布局--------*/
ul {
  overflow: auto;
}
#jzdm1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#jzdm1 .jzdm-content {
  min-height: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#jzdm1 .con-box {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#jzdm1 .flex-sty {
  display: flex;
}
#jzdm1 .box-row {
  padding: 8px 12px;
  box-sizing: border-box;
}
#jzdm1 .split-line {
  position: relative;
}
#jzdm1 .split-line::after {
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: '';
  background: #C8D7E6;
}
#jzdm1 .bor-w {
  border: 1px solid #C8D7E6;
  padding: 0 8px;
  background: #EBEEF5;
}
#jzdm1 .inp-sty {
  height: 28px;
  border: 1px solid #DCDFE6;
}
#jzdm1 [type="checkbox"],#jzdm1 [type="radio"] {
  vertical-align: middle;
}
#jzdm1 [type="text"] {
  padding: 0 2px;
}
#jzdm1 .add-btn {
  display: inline-block;
  padding: 3px 12px;
  color: #fff;
  background: #1885F2;
  border-radius: 4px;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
}