$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; // 报告是否被填写过
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#hbettap1 .hbettap-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    resultData = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.resultData : [];
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon();
    } else {
      initPage();
    }
  }
}
// 初始化
function initPage() {
  setDefaultVal();
  setCheckboxToRadioFun();
}
// 设置初始值
function setDefaultVal() {
  console.log('resultData-->',resultData);
  if(!resultData.length) {
    $('#hbettap-rt-5').val('结合临床体症、病史、影像学或其他肿瘤标志物检查结果分析病因:\n1、TAP凝聚物较大：临床其他检查也已发现肿瘤，应积极治疗。\n2、TAP凝聚物较小：临床其他检查未发现肿瘤、但有普通病症，应关注相关病症并积极诊治。\n3、TAP无明显凝聚物：临床其他检查未发现肿瘤、也无普通病症，建议1-3周复查一次，如果连续复查2次结果均为异常，应引起高度重视。同时应积极进行一些必要的干预措施，如改善生活方式。加强运动，平衡饮食，改变吸烟、饮酒、熬夜不良生活习惯。');
    $('#hbettap-rt-6').val('TAP正常/无明显凝聚物：0~121μ㎡\nTAP异常/较大凝聚物：＞225μ㎡\nTAP异常/较小凝聚物：121~225μ㎡');
    $('#hbettap-rt-7').val('1、慢性病症和异常体征的肿瘤风险评估，普通人群的肿瘤筛查，肿瘤治疗的疗效评估，肿瘤复发转移的动态检测。\n2、本报告仅对本次运送标本负责，仅供送检医师作临床参考，不作其它任何证明。');
  } 
}
// 设置凝聚物复选框为单选效果
function setCheckboxToRadioFun() { 
  $('input[name="njw-type"]').click(function() {
    if($(this).is(':checked')) {
      $('input[name="njw-type"]').prop('checked',false);
      $(this).prop('checked',true);
    }
  })
}
// 获取诊断
function getImpression() {
  let impression = '',strArr = [];
  let njwmjVal = $('#hbettap-rt-1').val() || '';
  let bjwTypeRad = getVal('input[name="njw-type"]:checked');
  njwmjVal && strArr.push(`凝聚物面积：${njwmjVal}μm²`);
  bjwTypeRad && strArr.push(bjwTypeRad);
  impression = strArr.length ? '检测结果：' + strArr.join('；') : '';
  return impression;
}
// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.hbettap-view [data-key]').each(function () {
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      if(publicInfo[key] || idAnVal) {
        var value = publicInfo[key] || idAnVal;
        if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
          value = value + ' ' + publicInfo['affirmTime'];
        }
        if((key === 'hbettap-rt-2' || key === 'hbettap-rt-3' || key === 'hbettap-rt-4') && value) {
          // 特殊处理复选框
          $(this).parent('label').addClass('rt-cus-ckr');
          $(this).parent('label').attr('for','');
          $(this).addClass('rt-checked');
          $(this).siblings('span').addClass('ml-4');
          $(this).after('<span class="rt-cus"></span>');
          $(this).prop('checked',true);
        }else if(value) {
          $(this).html(value);
          $(this).closest('.desc-con').show();
          if($(this).closest('.desc-con').next('.report-wrap').length) {
            $(this).closest('.desc-con').css('border-bottom', 'none');
          }
        }
        break;
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })

  curElem.find('.hbettap-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="item-img">
          <img src="${image.src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find('.hbettap-view .rpt-img-ls').html(imgHtml);
      curElem.find('.hbettap-view .rpt-img-ls').css('display', 'flex');
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}