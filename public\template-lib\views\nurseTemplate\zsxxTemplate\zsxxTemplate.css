#zsxx1 {
  font-size: 14px;
}

.injection {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.layui-item {
  width: 108px;
  text-align: center;
  cursor: pointer;
}

.item-select {
  color: #1885f2;
  height: 40px;
  line-height: 40px;
  border-bottom: 2px solid #1885f2;
}

.injection-information {
  display: flex;
  height: calc(100% - 64px);
}
.injection-information .left-content {
  width: 96px;
  background: #fff;
  box-shadow: -1px 0 0 0 #e4e7ec inset;
}
.injection-information .left-content .left-item {
  width: 96px;
  height: 64px;
  font-size: 14px;
  line-height: 64px;
  color: #303133;
  text-align: center;
  cursor: pointer;
}
.injection-information .left-content .select-left {
  color: #1885f2;
  border-right: 2px solid #1885f2;
}
.injection-information .right-content {
  width: calc(100% - 96px);
  padding: 12px;
  overflow: auto;
}
.injection-information .right-content ::-webkit-scrollbar {
  width: 0;
}
.injection-information .right-content .add-btn {
  width: 100px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  cursor: pointer;
}
.injection-information .right-content .convention-btn {
  margin-left: 12px;
  margin-bottom: 12px;
  margin-top: 12px;
}
.injection-information .right-content .delay-btn {
  margin-left: 12px;
  margin-bottom: 12px;
  margin-top: 12px;
  display: none;
}
.injection-information .right-content .enhance-btn {
  margin-left: 12px;
  margin-bottom: 12px;
  margin-top: 12px;
  display: none;
}
.injection-information .right-content .tags-content {
  width: 100%;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
}
.injection-information .right-content .tags-content .delay-list {
  display: none;
}
.injection-information .right-content .tags-content .enhance-list {
  display: none;
}
.injection-information .right-content .tags-content .tags-top {
  width: 100%;
  height: 40px;
  background: #ffffff;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: start;
}
.injection-information .right-content .tags-content .tags-top .tags-item {
  width: 128px;
  text-align: center;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.injection-information .right-content .tags-content .tags-top .tags-item .tags-num {
  width: 28px;
  height: 20px;
  background: #1885f2;
  border-radius: 3px;
  margin-left: 4px;
  color: #fff;
  line-height: 20px;
  text-align: center;
  font-size: 12px;
}
.injection-information .right-content .tags-content .tags-top .tags-item .delay-num{
  display: none;
}
.injection-information .right-content .tags-content .tags-top .tags-item .enhance-num{
  display: none;
}
.injection-information .right-content .tags-content .tags-top .select-item {
  color: #1885f2;
  height: 40px;
  line-height: 40px;
  border-bottom: 2px solid #1885f2;
}
.injection-information .right-content .tags-content .tags-box {
  padding: 12px 12px 0 12px;
}
.injection-information .right-content .form-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}
.injection-information .right-content .form-content .close-icon {
  margin-left: 5px;
}
.injection-information .right-content .form-content .form-item {
  display: flex;
  align-items: center;
  width: 33.3%;
  margin-bottom: 10px;
}
.injection-information .right-content .form-content .form-item .form-label {
  min-width: 60px;
  text-align: right;
}
.injection-information .right-content .form-content .form-item .form-title {
  min-width: 72px;
  width: 80px;
  text-align: right;
}
.injection-information .right-content .form-content .form-item .form-second {
  min-width: 80px;
  width: 80px;
  text-align: right;
}
.injection-information .right-content .form-content .form-item .radio-label {
  display: flex;
  align-items: center;
  margin-right: 18px;
}
.injection-information .right-content .form-content .form-item .radio-label .rt-sr-lb {
  margin-left: 4px;
}
.injection-information .right-content .form-content .form-item .custom-select {
  width: 100%;
  position: relative;
  background: #fff;
}
.injection-information .right-content .form-content .form-item .custom-select::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 11;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
.injection-information .right-content .form-content .form-item .showInt {
  width: 100%;
  position: relative;
  background: #fff;
}
.injection-information .right-content .form-content .form-item .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 11;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
.injection-information .right-content .form-content .form-item .input-content {
  display: flex;
  align-items: center;
  width: calc(100% - 58px);
}
.injection-information .right-content .form-content .form-item .input-content .unit {
  min-width: 60px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 0 4px 4px 0;
  border-left: 0;
}
.injection-information .right-content .top-content {
  height: 40px;
  padding-left: 12px;
  font-size: 14px;
  font-weight: bold;
  line-height: 40px;
  color: #303133;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 2px;
}
.injection-information .right-content .patient-content {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
}
.injection-information .right-content .patient-content:not(:first-child) {
  margin-top: 12px;
}
.injection-information .right-content .main-content {
  padding: 12px;
}
.injection-information .right-content .form-item {
  display: flex;
  align-items: center;
}
.injection-information .right-content .form-item .label {
  width: 80px;
  color: #606266;
  text-align: right;
}

.action {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 64px;
  line-height: 64px;
  text-align: center;
  border-top: 1px solid #dcdfe6;
}

/*# sourceMappingURL=zsxxTemplate.css.map */
