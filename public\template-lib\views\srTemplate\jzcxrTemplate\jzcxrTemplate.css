/*--------颜色类---------*/
.b-gray {
  background: #F5F7FA;
}
.b-eb {
  background: #EBEEF5;
}
.bor-dc {
  border: 1px solid #DCDFE6;
}
.bor-c8 {
  border: 1px solid #C8D7E6;
}
.borL-c8 {
  border-left: 1px solid #C8D7E6;
}
.borB-c8 {
  border-bottom: 1px solid #C8D7E6;
}
.borT-c8 {
  border-top: 1px solid #C8D7E6;
}
.text-color {
  color: #303133;
}
.text-black {
  color: #000000;
}

/*--------宽度类---------*/
.wd-19 {
  width: 19%;
}
.wd-30 {
  width: 30%;
}
.wd-49 {
  width: 49%;
}

/*--------jzcxr报告页面---------*/
#jzcxr,.jzcxr-checked,.sjc-block,.zc-block {
  font-size: 14px;
}
.jzcxr-page {
  width: 960px;
  padding: 8px 12px;
  box-sizing: border-box;
  background: #fff;
}
.box-pad {
  padding: 8px 12px;
  box-sizing: border-box;
}
.item-label {
  width: 70px;
  line-height: 44px;
  text-align: right;
  color: #303133;
}
.item-content {
  width: 859px;
}
.row-item {
  line-height: 28px;
}
.row-label {
  width: 70px;
  text-align: right;
}
.inp-sty {
  width: 60px;
  border-radius: 3px;
  padding: 3px 10px;
  outline: none;
}
.bw-box {
  font-size: 0;
  background: #f5f7fa;
}
.bw-left {
  width: 94px;
  padding: 8px 0;
  box-sizing: border-box;
}
.bw-right {
  width: calc(100% - 94px);
}
.nm-box {
  width: calc(100% - 75px);
}
.ml-114 {
  margin-left: 114px;
}
[isview="true"] #jzcxr .edit-bw,[isview="true"] #jzcxr .jj-label,#jzcxr .view-bw {
  display: none;
}
[isview="true"] #jzcxr .view-bw,[isview="true"] #jzcxr .hcmd,[isview="true"] #jzcxr .w-con label,[isview="true"] #jzcxr .sjcnm, [isview="true"] #jzcxr .zcnm {
  display: inline-block;
  line-height: 28px;
}
[isview="true"] #jzcxr .jzcxr-item .bor-c8,[isview="true"] #jzcxr .nm-row .bor-c8,[isview="true"] #jzcxr .borT-c8 {
  border: none;
}
[isview="true"] #jzcxr .jzcxr-item .b-gray,[isview="true"] #jzcxr .nm-row .b-gray,[isview="true"] #jzcxr .nm-row .b-eb {
  background: none;
}
[isview="true"] #jzcxr .jzcxr-item .item-content,[isview="true"] #jzcxr .jzcxr-item .row-label {
  width: unset;
}
[isview="true"] #jzcxr .box-pad {
  padding: 0;
}
[isview="true"] #jzcxr .sjc-block,[isview="true"] #jzcxr .zc-block {
  padding: 8px;
}
[isview="true"] #jzcxr .item-label {
  line-height: 28px;
}
[isview="true"] #jzcxr .jzcxr-item.mb-12 {
  margin-bottom: 0!important;
}
