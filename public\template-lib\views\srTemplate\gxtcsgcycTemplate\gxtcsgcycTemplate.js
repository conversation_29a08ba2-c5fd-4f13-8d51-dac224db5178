$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var mmUnit = 'mm',sqcmUnit = 'cm^2',cmDevsUnit = 'cm/s';
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      
    } else {

    }
  }
  initPage();
}
// 页面初始化
function initPage() {
  // 切换单选按钮的选中状态
  toggleRadioCheckStatus();
  changeMainMenu();
  initEnterEv();
  let pageIdList = ['telcns','tepjb','texq','tefb','tejz','tesz'];
  for(let pageId of pageIdList) {
    changeMenuSubItem(pageId);
  }
}
function changeMainMenu() {
  // 主框架
  $('#gxtcsgcyc1 .gxtcsgcyc-h .menu-group').on('click', '.menu-item', function() {
    if($(this).hasClass('act')) {
      return;
    }
    $(this).addClass('act').siblings('.menu-item').removeClass('act');
    $('.gxtcsgcyc-b .menu-con').removeClass('act');
    var tabName = $(this).attr('menu-id');
    $('.gxtcsgcyc-b .menu-con[id="'+tabName+'"]').addClass('act');
  })
}
// 页面内切换超声描述和超声提示
function changeMenuSubItem(pageId) {
  $('#'+pageId+' .menu-sub-group').on('click', '.menu-sub-item', function() {
    if($(this).hasClass('act')) {
      return;
    }
    $(this).addClass('act').siblings('#'+pageId+' .menu-sub-item').removeClass('act');
    $('#'+pageId+' .menu-sub-con').removeClass('act');
    var tabName = $(this).attr('menu-sub-id');
    $('#'+pageId+' .menu-sub-con[id="'+tabName+'"]').addClass('act');
  })
}
// 初始化回车键跳转输入框事件
function initEnterEv() {
  let keyDownTabList = ['tp','lcns','jz','pfj','xxf','gs','qt','ts1'];
  for(let pageId in keyDownTabList) {
    keydown_to_tab(pageId);
  }
}
// 获取选中复选框的值
function getCkValList(idList) {
  var strList = [];
  idList.map(function(id) {
    let isCheck = $('#'+id).is(':checked');
    let val = $('#'+id).val();
    if(isCheck) {
      strList.push(val);
    }
  })
  return strList;
}

// 胎儿颅脑、侧脑室——超声描述1
function getLcnsMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['大小径线','宽度径线','左径线','右径线'];
  $('#telcns-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let mmStrList = [];
      pId === 'gxtyctelcnsms1-rt-1' ? strList.push(parData.name) : str = parData.name;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        let mmValList = [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.name;
          childD2.map(function(cItem2) {
            if(cItem2.name==='左'||cItem2.name==='右') {
              let childD3 = cItem2.child || [];
              str += cItem2.name;
              childD3.map(function(cItem3) {
                mmValList.push(cItem3.val + mmUnit);
              })
              let mmStr = mmValList.length ? str += mmValList.join('x') : '';
              if(mmStr) {
                mmStrList.push(mmStr);
                str = '';
                mmValList = [];
              }
            }else {
              mmValList.push(cItem2.val + mmUnit);
            }
          })
          let mmStr = mmValList.length ? str += mmValList.join('x') : '';
          if(mmStr) {
            mmStrList.push(mmStr);
            str = '';
          }
        }else {
          str += cItem1.name;
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            if(mmUnitNameList.indexOf(cItem2.name)>-1) {
              str += cItem2.name;
              childD3.map(function(cItem3) {
                mmValList.push(cItem3.val + mmUnit);
              })
              let mmStr = mmValList.length ? str += mmValList.join('x') : '';
              if(mmStr) {
                mmStrList.push(mmStr);
                str = '';
              }
            }else {
              str += cItem2.name;
              str ? strList.push(str) : '';
              str = '';
            }
          })
          if(str) {
            strList.push(str);
            str = '';
          }
        }
      })
      mmStrList.length ? str += mmStrList.join('，') : '';
      if(str) {
        strList.push(str);
        str = '';
      }
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿颅脑、侧脑室——超声描述2
function getLcnsMs2Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let cItem1NeedUnit = ['gxtyctelcnsms2-rt-5','gxtyctelcnsms2-rt-11','gxtyctelcnsms2-rt-15'];
  $('#telcns-csms2 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let mmStrList = [];
      str = parData.name;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        let mmValList = [];
        if(cItem1NeedUnit.indexOf(cItem1.id)>-1) {
          str += cItem1.val;
          let unit = cItem1.id==='gxtyctelcnsms2-rt-5' ? sqcmUnit : mmUnit;
          mmValList = [];
          childD2.map(function(cItem2) {
            mmValList.push(cItem2.val + unit);
          })
          let mmStr = mmValList.length ? str += mmValList.join('x') : '';
          mmStr ? mmStrList.push(mmStr) : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
        }
      })
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿皮肤、颈部——超声描述1
function getPfjMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['左眼眶内径','右眼眶内径','宽度径线约','见囊性肿块大小','厚度径线约','范围径线'];
  $('#tepjb-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.name + '：';
          inpValList = [];
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          })
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            str += cItem2.val;
            if(mmUnitNameList.indexOf(cItem2.name)>-1) {
              inpValList = [];
              childD3.map(function(cItem3) {
                inpValList.push(cItem3.val + mmUnit);
              })
              inpValList.length ? str += inpValList.join('x') : '';
            }
          })
          str ? strList.push(str) : '';
          str = '';
        }
      })
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿胸腔——超声描述1
function getXxfMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['径线','最大径线','径线'];
  $('#texq-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(cItem1.name)>-1) {
          str += cItem1.val;
          inpValList = [];
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          })
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            if(mmUnitNameList.indexOf(cItem2.name)>-1) {
              str += cItem2.val;
              inpValList = [];
              childD3.map(function(cItem3) {
                inpValList.push(cItem3.val + mmUnit);
              })
              inpValList.length ? str += inpValList.join('x') : '';
            }
          })
          str ? strList.push(str) : '';
          str = '';
        }
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿腹部——超声描述1
function getFbMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['大小径线','径线','最大内径','最大流速'];
  $('#tefb-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(parData.name) > -1) {
          inpValList.push(cItem1.val + mmUnit);
        }else if(mmUnitNameList.indexOf(cItem1.name) > -1){
          str += cItem1.name;
          let unit = cItem1.name==='最大流速' ? cmDevsUnit : mmUnit;
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + unit);
          });
        }else {
          str += cItem1.val;
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            str += cItem2.val;
            childD3.map(function(cItem3) {
              str += cItem3.val;
            })
          });
          str ? strList.push(str) : '';
          str = '';
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      str ? strList.push(str) : '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿腹部——超声描述2
function getFbMs2Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['大泡径线','小泡径线','最大内径','大小径线','缺损宽约','最大厚径','最大径线','范围径线','肾盂分离厚径','实质厚径','上段肾盂分离','实质厚','下段肾盂分离'];
  let kjStrList = ['内可见','可见'];
  $('#tefb-csms2 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let inpValList = [];
      if(mmUnitNameList.indexOf(parData.name) > -1 || kjStrList.indexOf(parData.name) > -1) {
        str = parData.val;
      }else {
        strList.push(parData.val);
      }
      childD1.map(function(cItem1,cId1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(parData.name) > -1) {
          inpValList.push(cItem1.val + mmUnit);
        }else if(mmUnitNameList.indexOf(cItem1.name) > -1){
          str += cItem1.name;
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          });
          inpValList.length ? str += inpValList.join('x') : '';
          if(pId === 'gxtyctefbcsms2-rt-1' && cId1===childD1.length-1) {
            str += '，两泡间见一管道状结构相连';
          }else if(pId === 'gxtyctefbcsms2-rt-11') {
            str += '，边界清';
          }
          inpValList = [];
          str ? strList.push(str) : '';
          str = '';
        }else {
          str += cItem1.val;
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            str += cItem2.val;
            childD3.map(function(cItem3) {
              str += cItem3.val;
            })
          });
          if(pId === 'gxtyctefbcsms2-rt-23' && cId1===childD1.length-1) {
            str += '等内脏在羊水中漂浮';
          }
          str ? strList.push(str) : '';
          str = '';
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      inpValList = [];
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿腹部——超声描述3
function getFbMs3Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['最宽内径','大小径线','长约'];
  let pIdList = ['gxtyctefbcsms3-rt-13','gxtyctefbcsms3-rt-20','gxtyctefbcsms3-rt-25'];
  let sxdIdList = ['gxtyctefbcsms3-rt-2','gxtyctefbcsms3-rt-3'],sxdStrList = [],sxdList=[]; // 上下段
  sxdList = getCkValList(sxdIdList);
  $('#tefb-csms3 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      let inpValList = [];
      if(pIdList.indexOf(pId) > -1) {
        strList.push(parData.val);
      }else if(sxdIdList.indexOf(pId) > -1) {
        sxdStrList.push(parData.val);
      }else {
        str = parData.val;
      }
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(parData.name) > -1) {
          inpValList.push(cItem1.val + mmUnit);
        }else if(mmUnitNameList.indexOf(cItem1.name) > -1){
          str += cItem1.name;
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          });
          inpValList.length ? str += inpValList.join('x') : '';
          str ? strList.push(str) : '';
          str = '';
          inpValList = [];
        }else {
          str += cItem1.val;
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            str += cItem2.val;
            childD3.map(function(cItem3) {
              str += cItem3.val;
            })
          });
          str ? strList.push(str) : '';
          str = '';
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      if(pId === 'gxtyctefbcsms3-rt-6') {
        str += '呈“钥匙孔”征';
      }
      if(sxdStrList.length === sxdList.length) {
        str = sxdStrList.join('，') + '输尿管扩张';
        sxdStrList = [];
      }
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿脊柱——超声描述1
function getJz1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#tejz-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        let inpValList = [];
        if(pId==='gxtyctejzcsms1-rt-28' || cItem1.id==='gxtyctejzcsms1-rt-54') {
          let childD2 = cItem1.child || [];
          childD2.map(function(cItem2) {
            if(cItem1.id==='gxtyctejzcsms1-rt-54') {
              inpValList.push(cItem1.val + cItem2.val + mmUnit);
            }else {
              inpValList.push(cItem2.val);
            }
          })
        }else {
          str += cItem1.val;
        }
        inpValList.length ? str += inpValList.join('-') : '';
        str ? strList.push(str) : '';
        str = '';
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿脊柱——超声描述2
function getJzMs2Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#tejz-csms2 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        if(parData.name==='大小') {
          inpValList.push(cItem1.val + mmUnit);
        }else if(cItem1.id==='gxtyctejzcsms2-rt-29'){
          let childD2 = cItem1.child || [];
          childD2.map(function(cItem2) {
            str += cItem1.val + cItem2.val + '水平';
          })
        }else {
          str += cItem1.val;
          str ? strList.push(str) : '';
          str = '';
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      str ? strList.push(str) : '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}
// 胎儿四肢——超声描述1
function getSzMs1Des() {
  let description = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let mmUnitNameList = ['长度径线'];
  let bfsUnitNameList = ['同孕周第'];
  let lgfyUnitNameList = ['第'];
  $('#tesz-csms1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let inpValList = [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        if(mmUnitNameList.indexOf(parData.name) > -1) {
          inpValList.push(cItem1.val + mmUnit);
        }else if(bfsUnitNameList.indexOf(parData.name) > -1) {
          inpValList.push(cItem1.val + '百分位数');
        }else if(lgfyUnitNameList.indexOf(parData.name) > -1) {
          inpValList.push(cItem1.val + '肋骨发育不良');
        }else if(mmUnitNameList.indexOf(cItem1.name) > -1){
          str += cItem1.name;
          childD2.map(function(cItem2) {
            inpValList.push(cItem2.val + mmUnit);
          });
        }else {
          str += cItem1.val;
          childD2.map(function(cItem2) {
            let childD3 = cItem2.child || [];
            str += cItem2.val;
            childD3.map(function(cItem3) {
              str += cItem3.val;
            })
          });
          str ? strList.push(str) : '';
          str = '';
        }
      })
      inpValList.length ? str += inpValList.join('x') : '';
      str ? strList.push(str) : '';
    }
  })
  description = strList.length ? strList.join('，') :'';
  return description;
}

// 胎儿颅脑、侧脑室——超声提示
function getLcnsTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#telcns-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      childD1.map(function(cItem1) {
        if(pId === 'gxtyctelcnscsts-rt-11') {
          str = '';
          str = cItem1.val + parData.val;
        }else if(pId === 'gxtyctelcnscsts-rt-15') {
          str = '';
          str = cItem1.val;
        }else {
          str += cItem1.val;
        }
      })
      str ? strList.push(str) : '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 胎儿皮肤、颈部——超声提示
function getPfjTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#tepjb-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      str = parData.val;
      str ? strList.push(str) : '';
      str = '';
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        str = cItem1.val;
        childD2.map(function(cItem2) {
          let childD3 = cItem2.child || [];
          str += cItem2.val;
          childD3.map(function(cItem3) {
            str += cItem3.val;
          })
        })
        str ? strList.push(str) : '';
        str = '';
      })
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 胎儿胸腔——超声提示
function getXxfTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#texq-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      let pId = parData.id;
      str = parData.val;
      if(pId==='gxtyctexqcsts-rt-1') {
        str ? strList.push(str) : '';
        str = '';
      }
      childD1.map(function(cItem1) {
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
        let childD2 = cItem1.child || [];
        childD2.map(function(cItem2) {
          str += cItem2.val;
          str ? strList.push(str) : '';
          str = '';
        })
      })
      str ? strList.push(str) : '';
      str = '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 胎儿腹部——超声提示
function getFbTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  let sxqpcIdList = ['gxtyctefbcsts1-rt-45','gxtyctefbcsts1-rt-46','gxtyctefbcsts1-rt-47'],sxqpcStrList = [],sxqpcList=[]; // 上下脐膨出
  sxqpcList = getCkValList(sxqpcIdList);
  $('#tefb-csts1 .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      str = parData.val;
      if(sxqpcIdList.indexOf(parData.id) > -1) {
        sxqpcStrList.push(str);
        str = '';
      }
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
        childD2.map(function(cItem2) {
          str += cItem2.val;
          str ? strList.push(str) : '';
          str = '';
        })
      })
      if(sxqpcStrList.length === sxqpcList.length) {
        str = sxqpcStrList.join('');
        sxqpcStrList = [];
      }
      str ? strList.push(str) : '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 胎儿脊柱——超声提示
function getJzTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#tejz-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      str = parData.val;
      childD1.map(function(cItem1) {
        let childD2 = cItem1.child || [];
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
        childD2.map(function(cItem2) {
          str += cItem2.val;
          str ? strList.push(str) : '';
          str = '';
        })
      })
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}
// 胎儿四肢——超声提示
function getSzTsImp() {
  let impression = '';
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '',strList = [];
  $('#tesz-csts .rt-sr-w:not([pid])').each(function(pIdx,par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let childD1 = parData.child || [];
      str = parData.val;
      childD1.map(function(cItem1) {
        str += cItem1.val;
        str ? strList.push(str) : '';
        str = '';
      })
      str ? strList.push(str) : '';
    }
  })
  impression = strList.length ? strList.join('，') :'';
  return impression;
}

// 所有描述拼接的内容
function getAllDescContent() {
  var allDesc = '', descList = [];
  // 胎儿颅脑、侧脑室——超声描述1
  var lcnsMs1Des = getLcnsMs1Des();
  lcnsMs1Des && descList.push(lcnsMs1Des);
  // 胎儿颅脑、侧脑室——超声描述2
  var lcnsMs2Des = getLcnsMs2Des();
  lcnsMs2Des && descList.push(lcnsMs2Des);

  // 胎儿皮肤、颈部——超声描述1
  var pfjMs1Des = getPfjMs1Des();
  pfjMs1Des && descList.push(pfjMs1Des);

  // 胎儿胸腔——超声描述1
  var xxfMs1Des = getXxfMs1Des();
  xxfMs1Des && descList.push(xxfMs1Des);

  // 胎儿腹部——超声描述1
  var fbMs1Des = getFbMs1Des();
  fbMs1Des && descList.push(fbMs1Des);
  // 胎儿腹部——超声描述2
  var fbMs2Des = getFbMs2Des();
  fbMs2Des && descList.push(fbMs2Des);
  // 胎儿腹部——超声描述3
  var fbMs3Des = getFbMs3Des();
  fbMs3Des && descList.push(fbMs3Des);

  // 胎儿脊柱——超声描述1
  var jzMs1Des = getJz1Des();
  jzMs1Des && descList.push(jzMs1Des);
  // 胎儿脊柱——超声描述2
  var jzMs2Des = getJzMs2Des();
  jzMs2Des && descList.push(jzMs2Des);

  // 胎儿四肢——超声描述1
  var szMs1Des = getSzMs1Des();
  szMs1Des && descList.push(szMs1Des);

  allDesc = descList.length ? descList.join('。\n') + '。' : '';
  // console.log(allDesc);
  return allDesc;
}

// 所有诊断拼接的内容
function getAllImpContent() {
  var allImp = '', impList = [];
  // 胎儿颅脑、侧脑室——超声提示
  var lcnsTsImp = getLcnsTsImp();
  lcnsTsImp && impList.push(lcnsTsImp);

  // 胎儿皮肤、颈部——超声提示
  var pfjTsImp = getPfjTsImp();
  pfjTsImp && impList.push(pfjTsImp);

  // 胎儿胸腔——超声提示
  var xxfTsImp = getXxfTsImp();
  xxfTsImp && impList.push(xxfTsImp);

  // 胎儿腹部——超声提示
  var fbTsImp = getFbTsImp();
  fbTsImp && impList.push(fbTsImp);

  // 胎儿脊柱——超声提示
  var jzTsImp = getJzTsImp();
  jzTsImp && impList.push(jzTsImp);

  // 胎儿四肢——超声提示
  var fbSzImp = getSzTsImp();
  fbSzImp && impList.push(fbSzImp);

  allImp = impList.length ? impList.join('。\n') + '。' : '';
  // console.log(allImp);
  return allImp;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getAllDescContent();
  rtStructure.impression = getAllImpContent();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}