$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    if(publicInfo.sex !== '女') {
      curElem.find('#mrpdjpg1').html(`<h2 style="padding-left:20px;">此模板为盆底肌评估报告，不适用于男性患者</h2>`);
      return;
    }
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewPageCon();
    } else {
      pageInit();
    }
  }
}

// 初始化页面
function pageInit() {
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);

  // 单选\复选框事件
  var radCkArr = ['pdjrxt-block','qq-block','zq-block','hq-block'];
  for(let key in radCkArr) {
    $(`#mrpdjpg1 .${radCkArr[key]} input`).change(function() {
      getImpContent();
    })
  }

  // 测量
  $("#mrpdjpg1 .measure-inp").on('input',function() {
    measureFun();
  })

  // 其他征象
  $("#mrpdjpg1 #mrpdjpg-rt-147").on('input',function() {
    getImpContent();
  })
  
}

// 盆底肌肉形态改变
function getPdjrxtCon() {
  var description = '',strList = [];
  var kwjRadVal = getVal('[name="kwj-sta"]:checked');
  var cgzcjRadVal = getVal('[name="cgzcj-sta"]:checked');
  var gmwkyjRadVal = getVal('[name="cgzcj-sta"]:checked');
  // 髂尾肌和耻骨直肠肌都属于肛提肌
  var gtjYcStr = getVal('#mrpdjpg1 .gtj-yc input[type="checkbox"]:checked');
  var gtjYcList = gtjYcStr ? gtjYcStr.split('、') : [];
  var gmwkyjYcStr = getVal('#mrpdjpg1 .gmwkyj-yc input[type="checkbox"]:checked');
  if(kwjRadVal === '异常' || cgzcjRadVal === '异常') {
    gtjYcList = [...new Set(gtjYcList)];
    gtjYcList.length ? strList.push('肛提肌' + gtjYcList.join('、')) : '';
  }
  if(gmwkyjRadVal === '异常') {
    gmwkyjYcStr ? strList.push('肛门外括约肌' + gmwkyjYcStr) : '';
  }
  description = strList.length ? '盆底解剖结构：' + strList.join('，') : '';
  return description;
}

// 测量
function measureFun() {
  // H线最大腹压相（或排粪相）测量值≤6cm，肛提肌裂孔自动勾选“正常”。＞6cm，肛提肌裂孔自动勾选“扩大”；
  // M线最大腹压相（或排粪相）测量值≤2cm，肛管直肠交界部（ARJ）自动勾选“正常”。＞2cm，肛管直肠交界部（ARJ）自动勾选“下降”；
  let hLineVal = $('#mrpdjpg-rt-51').val(); // H线
  let mLineVal = $('#mrpdjpg-rt-59').val(); // M线
  if(!hLineVal){

  }else if(Number(hLineVal) <= 6) {
    $('#mrpdjpg-rt-53').click();
  }else if(Number(hLineVal) > 6) {
    $('#mrpdjpg-rt-54').click();
  }
  if(!mLineVal) {

  }else if(Number(mLineVal) <= 2) {
    $('#mrpdjpg-rt-61').click();
  }else if(Number(mLineVal) > 2) {
    $('#mrpdjpg-rt-62').click();
  }
}

// 推算等级
function calLevFun(inpVal) {
  let lev = '';
  if(!inpVal) {
    lev = '';
  }else if(Number(inpVal) > 6) {
    lev = '（3级）';
  }else if(3 <= Number(inpVal) && Number(inpVal) <= 6) {
    lev = '（2级）';
  }else if(Number(inpVal) < 3) {
    lev = '（1级）';
  }
  return lev;
}

// 前腔
function getQqCon() {
  var description = '',desStr = '', strList=[];
  var qqZdfyxVal = getVal('[name="qq-zdfyx"]:checked');
  var qqNdghxVal = getVal('[name="qq-ndghx"]:checked');
  var qqNdnkldVal = getVal('[name="qq-ndnkld"]:checked');
  if(qqZdfyxVal === '下方') {
    desStr = '膀胱脱垂';
    let qqZdfyxInpVal = getVal('#mrpdjpg-rt-73');
    desStr += calLevFun(qqZdfyxInpVal);
    desStr ? strList.push(desStr) : '';
  }
  if(qqNdghxVal === '有') {
    strList.push('尿道高活动性');
  }
  if(qqNdnkldVal === '有') {
    strList.push('尿道内漏斗化');
  }
  description = strList.length ? '前腔：' + strList.join('，') : '';
  return description;
}

// 中腔
function getZqCon() {
  var description = '',desStr = '', strList=[];
  var gjydVal = getVal('[name="zq-gjyd"]:checked');
  var zqZdfyxVal = getVal('[name="zq-fyx-sxf"]:checked');
  var zqSnnrwVal = getVal('[name="zq-snnrw"]:checked');
  var zqFjVal = getVal('[name="zq-fj"]:checked');
  if(zqZdfyxVal === '下方' && gjydVal) {
    desStr += gjydVal + '脱垂';
    let zqZdfyxInpVal = getVal('#mrpdjpg-rt-95');
    desStr += calLevFun(zqZdfyxInpVal);
    desStr ? strList.push(desStr) : '';
  }
  if(zqSnnrwVal && zqFjVal) {
    let str = zqSnnrwVal + '（' + zqFjVal + '）';
    strList.push(str);
  }else if(zqSnnrwVal && !zqFjVal) {
    strList.push(zqSnnrwVal);
  }

  description = strList.length ? '中腔：' + strList.join('，') : '';
  return description;
}

// 后腔
function getHqCon() {
  var description = '',desStr = '', strList=[];
  var hqZcPcVal = getVal('[name="hq-zcpc"]:checked');
  var qhpcVal = getVal('[name="hq-ypc"]:checked');
  // 直肠膨出
  if(hqZcPcVal === '有' && qhpcVal) {
    desStr += '直肠' + qhpcVal;
    var hqPcInpVal = getVal('[id="mrpdjpg-rt-145"]');
    if(!hqPcInpVal) {
      desStr += '';
    }else if(Number(hqPcInpVal) > 4) {
      desStr += '（3级）';
    }else if(Number(hqPcInpVal) >= 2 || Number(hqPcInpVal) <= 4) {
      desStr += '（2级）';
    }else if(Number(hqPcInpVal) < 2) {
      desStr += '（1级）';
    }
    desStr ? strList.push(desStr) : '';
  }
  // 直肠套叠类型组合
  var zctdTypeMap = {
    '黏膜套叠直肠内套叠': '直肠内黏膜套叠',
    '全层套叠直肠内套叠': '直肠内全层套叠',
    '黏膜套叠肛管内套叠': '肛管内黏膜套叠',
    '全层套叠肛管内套叠': '肛管内全层套叠',
    '黏膜套叠直肠脱垂': '直肠黏膜脱垂',
    '全层套叠直肠脱垂': '直肠脱垂'
  };
  var hqYnmtdVal = getVal('[name="hq-ynmtd"]:checked');
  var hqQctdVal = getVal('[name="hq-qctd"]:checked');
  if(hqYnmtdVal && hqQctdVal) {
    let str = zctdTypeMap[hqYnmtdVal+hqQctdVal];
    str ? strList.push(str) : '';
  }

  description = strList.length ? strList.join('，') : '';
  return description;
}

// 后腔——盆底松弛
function getPdscCon() {
  var description = '';
  var hLineVal = getVal('[id="mrpdjpg-rt-54"]:checked');
  var mLineVal = getVal('[id="mrpdjpg-rt-62"]:checked');
  var hqJxtxARAVal = getVal('[name="hq-jxara"]:checked');
  var hqSgARAVal = getVal('[name="hq-sgara"]:checked');
  if((hLineVal && mLineVal) || (hqJxtxARAVal === '增大' && hqSgARAVal === '无')) {
    description = '盆底松弛';
  }
  return description;
}
// 后腔——盆底失驰缓
function getPdschCon() {
  var description = '';
  var hqFyaraVal = getVal('[name="hq-fyara"]:checked');
  var cgzcjYcfhVal = getVal('[id="mrpdjpg-rt-22"]:checked');
  if(hqFyaraVal === '反常性缩小' && cgzcjYcfhVal) {
    description = '盆底失驰缓';
  }
  return description;
}

// 其他征象
function getQtzxCon() {
  var description = '';
  var qtzxVal = getVal('[id="mrpdjpg-rt-147"]');
  description += qtzxVal ? '其他征象：' + qtzxVal : '';
  return description;
}

// 描述——盆底肌肉形态改变
function getPdjrxtgbDesc() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let desc = '',strList = [];
  $('#mrpdjpg1 .pdjpg-edit .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData['mrpdjpg-rt-5'];
    if(pid === 'mrpdjpg-rt-5' && curPid) {
      if(curPid.child) {
        let childD1 = curPid.child;
        childD1.map(cItem1 => {
          let childD2 = cItem1.child || []; // 子标题
          let subStrList = [];
          childD2.map(cItem2 => {
            let childD3 = cItem2.child || []; // 正常、异常
            if(cItem2.val === '正常') {
              subStrList.push(cItem2.val);
            }
            childD3.map(cItem3 => {
              let childD4 = cItem3.child || []; // 撕裂
              if(!childD4.length) {
                subStrList.push(cItem3.val);
              }
              childD4.map(cItem4 => { // 输入框
                str = cItem3.val + '（' + cItem4.val + '）';
                subStrList.push(str);
              })
            })
          })
          subStrList.length ? strList.push(cItem1.val + subStrList.join('，')) : '';
        })
      }
    }
  });
  desc = strList.length ? '盆底肌肉形态改变：' + strList.join('；') : '';
  return desc;
}

// 描述——盆底筋膜缺陷的间接征象
function getPdjmqxDesc() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let desc = '',strList = [];
  $('#mrpdjpg1 .pdjpg-edit .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData['mrpdjpg-rt-33'];
    if(pid === 'mrpdjpg-rt-33' && curPid) {
      if(curPid.child) {
        let childD1 = curPid.child;
        childD1.map(cItem1 => {
          let childD2 = cItem1.child || []; // 子标题
          childD2.map(cItem2 => { // 正常、异常
            strList.push(cItem1.val + cItem2.val);
          })
        })
      }
    }
  });
  desc = strList.length ? '盆底筋膜缺陷的间接征象：' + strList.join('；') : '';
  return desc;
}

// 描述——测量
function getMeasureDesc() {
  var measureList = [],measureText = '',hLineArr = [],mLineArr = [];
  // H线
  var hLineJxtInp = getVal('[id="mrpdjpg-rt-49"]'); // H线-静息态
  var hLineFyxInp = getVal('[id="mrpdjpg-rt-51"]'); // H线-最大腹压相
  var hLineGtjlk = getVal('[name="gtjlk-sta"]:checked'); // H线-肛提肌裂孔
  hLineJxtInp && hLineArr.push('静息态：' + hLineJxtInp + 'cm');
  hLineFyxInp && hLineArr.push('最大腹压相（或排粪相）' + hLineFyxInp + 'cm');
  hLineGtjlk && hLineArr.push('测量值表明：肛提肌裂孔：' + hLineGtjlk);
  hLineArr.length ? measureList.push('H线（正常值≤6cm）：' + hLineArr.join('，') + '。') : '';
  // M线
  var mLineJxtInp = getVal('[id="mrpdjpg-rt-57"]'); // M线-静息态
  var mLineFyxInp = getVal('[id="mrpdjpg-rt-59"]'); // M线-最大腹压相
  var mLineGgzcjj = getVal('[name="ggzcjjb-sta"]:checked'); // M线-肛管直肠交界部(ARJ)
  mLineJxtInp && mLineArr.push('静息态：' + mLineJxtInp + 'cm');
  mLineFyxInp && mLineArr.push('最大腹压相（或排粪相）' + mLineFyxInp + 'cm');
  mLineGgzcjj && mLineArr.push('测量值表明：肛管直肠交界部(ARJ)：' + mLineGgzcjj);
  mLineArr.length ? measureList.push('M线（正常值≤2cm）：' + mLineArr.join('，')) : '';

  measureText = measureList.join('\n');
  return measureText;
}

// 描述——其他解剖异常
function getQtjpycDesc() {
  var description = '';
  var qtjpycVal = getVal('[id="mrpdjpg-rt-64"]');
  description += qtjpycVal ? '其他解剖异常：' + qtjpycVal : '';
  return description;
}

// 描述——前腔
function getQqDesc() {
  var qqList = [], qqFirstList = [];
  var qqPgdbjxt = getVal('[name="qq-pgdbjxt"]:checked');
  var qqPgdbjxtInp = getVal('[id="mrpdjpg-rt-69"]');
  var qqZdfyx = getVal('[name="qq-zdfyx"]:checked');
  var qqZdfyxInp = getVal('[id="mrpdjpg-rt-73"]');
  var qqqghddInp = getVal('[id="mrpdjpg-rt-75"]');
  (qqPgdbjxt || qqPgdbjxtInp) ? qqFirstList.push('膀胱底部静息态PCL' + qqPgdbjxt + (qqPgdbjxtInp ? qqPgdbjxtInp + 'cm' : '')) : '';
  (qqZdfyx || qqZdfyxInp) ? qqFirstList.push('最大腹压相（或排粪相）PCL' + qqZdfyx + (qqZdfyxInp ? qqZdfyxInp + 'cm' : '')) : '';
  qqqghddInp && qqFirstList.push('器官活动度' + qqqghddInp + 'cm');
  qqFirstList.length ? qqList.push(qqFirstList.join('，')) : '';
  
  var qqNdghx = getVal('[name="qq-ndghx"]:checked');
  qqList.push('尿道高活动性：' + qqNdghx);
  var qqNdnkld = getVal('[name="qq-ndnkld"]:checked');
  qqList.push('尿道内口漏斗样改变：' + qqNdnkld);
  var qqZdfynsj = getVal('[name="qq-zdfynsj"]:checked');
  qqList.push('最大腹压相尿失禁现象：' + qqZdfynsj);
  var qqStr = qqList.join('；\n');
  return qqStr;
}
// 描述——中腔
function getZqDesc() {
  var zqList = [], zqFirstList = [], zqGjydStr = '';
  var zqGjydVal = getVal('[name="zq-gjyd"]:checked');
  var zqGjydjxt = getVal('[name="zq-gjyd-sxf"]:checked');
  var zqGjydjxtInp = getVal('[id="mrpdjpg-rt-91"]');
  var zqZdfyx = getVal('[name="zq-fyx-sxf"]:checked');
  var zqZdfyxInp = getVal('[id="mrpdjpg-rt-95"]');
  var zqqghddInp = getVal('[id="mrpdjpg-rt-97"]');
  zqGjydStr = zqGjydVal ? zqGjydVal==='子宫' ? '宫颈' : '阴道顶端' : zqGjydVal;
  (zqGjydStr || zqGjydjxt || zqGjydjxtInp) ? zqFirstList.push(zqGjydStr + (zqGjydjxt || zqGjydjxtInp) ? '静息态PCL' + zqGjydjxt + (zqGjydjxtInp ? zqGjydjxtInp + 'cm' : '') : '') : '';
  (zqZdfyx || zqZdfyxInp) ? zqFirstList.push('最大腹压相（或排粪相）PCL' + zqZdfyx + (zqZdfyxInp ? zqZdfyxInp + 'cm' : '')) : '';
  zqqghddInp ? zqFirstList.push('器官活动度' + zqqghddInp + 'cm') : '';
  zqFirstList.length ? zqList.push(zqFirstList.join('，')) : '';

  var zqYwCs = getVal('[name="zq-yw-cs"]:checked');
  if(zqYwCs) {
    let zqCsStr = '', zqCsList = [];
    zqCsStr = '肠疝或腹膜疝：' + zqYwCs;
    var zqSnnrw = getVal('[name="zq-snnrw"]:checked');
    zqSnnrw ? zqCsList.push('疝囊内容物：' + zqSnnrw) : '';
    var zqPclXfInp = getVal('[id="mrpdjpg-rt-106"]');
    zqPclXfInp ? zqCsList.push('在PCL下方' + zqPclXfInp + 'cm') : '';
    var zqFj = getVal('[name="zq-fj"]:checked');
    zqFj ? zqCsList.push('分级为：' + zqFj) : '';
    zqCsList.length ? zqCsStr += '（' + zqCsList.join('，') + '）' : '';
    zqList.push(zqCsStr);
  }
  var zqStr = zqList.join('；\n');
  return zqStr;
}
// 描述——后腔
function getHqDesc() {
  var hqList = [], hqSecondList = [];
  var hqPkzcnj = getVal('[name="hq-pkzcnj"]:checked');
  hqPkzcnj ? hqList.push('（行排粪造影者）能否充分排空直肠内凝胶：' + hqPkzcnj) : '';

  var hqJxtInp = getVal('[id="mrpdjpg-rt-117"]');
  hqJxtInp && hqSecondList.push('肛管直肠角（ARA）静息态：' + hqJxtInp + '°');
  var hqSgxInp = getVal('[id="mrpdjpg-rt-119"]');
  hqSgxInp && hqSecondList.push('缩肛相：' + hqSgxInp + '°');
  var hqZdfyxInp = getVal('[id="mrpdjpg-rt-121"]');
  hqZdfyxInp && hqSecondList.push('最大腹压相（或排粪相）： ' + hqZdfyxInp + '°');
  var hqJxara = getVal('[name="hq-jxara"]:checked');
  hqSecondList.push('测量值表明：静息态下ARA：' + hqJxara);
  var hqSgara = getVal('[name="hq-sgara"]:checked');
  hqSecondList.push('缩肛相上正常预期的ARA变小：' + hqSgara);
  var hqFyara = getVal('[name="hq-fyara"]:checked');
  hqSecondList.push('最大腹压相（或排粪相）上ARA：' + hqFyara);
  hqList.push(hqSecondList.join('，'));

  var hqZcTdYw = getVal('[name="hq-zctd"]:checked');
  var hqZcnmtd = getVal('[name="hq-ynmtd"]:checked');
  var hqTdType = getVal('[name="hq-qctd"]:checked');
  if(hqZcTdYw === '无') {
    hqList.push('直肠套叠：' + hqZcTdYw);
  }else {
    var tdTypeStr = hqTdType ? '（' + hqTdType + '）' : '';
    hqZcnmtd || tdTypeStr ? hqList.push('直肠套叠：' + hqZcnmtd + tdTypeStr) : '';
  }

  var hqYwZcpc = getVal('[name="hq-zcpc"]:checked');
  var hqYpc = getVal('[name="hq-ypc"]:checked');
  var hqYpcInp = getVal('[id="mrpdjpg-rt-145"]');
  if(hqYwZcpc === '无') {
    hqList.push('直肠膨出：' + hqYwZcpc);
  }else {
    hqYpc || hqYpcInp ? hqList.push('直肠膨出：' + hqYpc + (hqYpcInp ? '，' + hqYpcInp + 'cm' : '')) : '';
  }
  var hqStr = hqList.join('；\n');
  return hqStr;
}


// 获取描述
function getDescContent() {
  var pqjppjArr = [], pdgnpgArr = [], descArr = [];
  // 盆底肌肉形态改变
  var pdjrxtgbDesc = getPdjrxtgbDesc();
  pdjrxtgbDesc && pqjppjArr.push(pdjrxtgbDesc);

  // 盆底筋膜缺陷的间接征象
  var pdjmqxDesc = getPdjmqxDesc();
  pdjmqxDesc && pqjppjArr.push(pdjmqxDesc);

  // 测量
  var measureDesc = getMeasureDesc();
  measureDesc && pqjppjArr.push('测量：' + measureDesc);

  // 其他解剖异常
  var qtjpycDesc = getQtjpycDesc();
  qtjpycDesc && pqjppjArr.push(qtjpycDesc);

  // 前腔
  var qqDesc = getQqDesc();
  qqDesc && pdgnpgArr.push(qqDesc);

  // 中腔
  var zqDesc = getZqDesc();
  zqDesc && pdgnpgArr.push(zqDesc);

  // 后腔
  var hqDesc = getHqDesc();
  hqDesc && pdgnpgArr.push(hqDesc);

  // 其他征象
  var qtzxDesc = getQtzxCon();
  qtzxDesc && pdgnpgArr.push(qtzxDesc);


  descArr.push('盆底解剖评价' + '\n' + pqjppjArr.join('。\n'));
  descArr.push('盆底功能评估（以PCL为参考线）' + '\n' + pdgnpgArr.join('。\n'));
  // console.log(descArr.join('。\n'));
  return descArr.length ? descArr.join('。\n') : '';
}

// 获取印象
function getImpContent() {
  var impression = '';
  var strList = [];

  // 盆底肌肉形态学改变
  var pdjrxtStr = getPdjrxtCon();
  pdjrxtStr ? strList.push(pdjrxtStr) : '';

  // 前腔
  var qqStr = getQqCon();
  qqStr ? strList.push(qqStr) : strList.push('前腔：未见异常');

  // 中腔
  var zqStr = getZqCon();
  zqStr ? strList.push(zqStr) : strList.push('中腔：未见异常');

  // 后腔
  var hqStr = getHqCon();
  var pdscStr = getPdscCon();
  var pdschStr = getPdschCon();
  var hqStrList = [];
  hqStr ? hqStrList.push(hqStr) : '';
  pdscStr ? hqStrList.push(pdscStr) : '';
  pdschStr ? hqStrList.push(pdschStr) : '';
  hqStrList.length ? strList.push('后腔：' + hqStrList.join('，')) : strList.push('后腔：未见异常');

  // 其他征象
  var qtzxStr = getQtzxCon();
  qtzxStr ? strList.push(qtzxStr) : '';

  impression = strList.length ? strList.join('\n') : '';
  $('[id="mrpdjpg-rt-148"]').val(impression);
  // console.log('impression-->',impression);
  return impression;
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescContent();
  rtStructure.impression = getImpContent();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

// 预览页面处理
function initViewPageCon() {
  var idAndDomMap = rtStructure.idAndDomMap;
  var examInfo = rtStructure.examInfo;
  // 患者信息
  $('.pdjpg-view .patient-info [data-id]').each(function(i, dom){
    var id = $(dom).attr('data-id');
    $(dom).text(idAndDomMap && idAndDomMap[id] ? (idAndDomMap[id].value || '') : (examInfo[id]) || '');
  })

  // 盆底肌肉形态改变
  var kwjRadVal = getVal('[name="kwj-sta"]:checked'); // 髂尾肌
  var cgzcjRadVal = getVal('[name="cgzcj-sta"]:checked'); // 耻骨直肠肌
  var gmwkyjRadVal = getVal('[name="cgzcj-sta"]:checked'); // 肛门外括约肌
  // 异常复选框
  var kwjYcStr = getVal('[name="kwj-yc"]:checked');
  var cgzcjYcStr = getVal('[name="cgzcj-yc"]:checked');
  var gmwkyjYcStr = getVal('[name="gmwkyj-yc"]:checked');
  var pdjrxtgbList = [];
  kwjRadVal === '正常' ? pdjrxtgbList.push('髂尾肌：' + kwjRadVal) : kwjRadVal === '异常' ? pdjrxtgbList.push('髂尾肌：' + kwjYcStr) : '';
  cgzcjRadVal === '正常' ? pdjrxtgbList.push('耻骨直肠肌：' + cgzcjRadVal) : cgzcjRadVal === '异常' ? pdjrxtgbList.push('耻骨直肠肌：' + cgzcjYcStr) : '';
  gmwkyjRadVal === '正常' ? pdjrxtgbList.push('肛门外括约肌：' + gmwkyjRadVal) : gmwkyjRadVal === '异常' ? pdjrxtgbList.push('肛门外括约肌：' + gmwkyjYcStr) : '';
  if(pdjrxtgbList.length) {
    var pdjrxtgbText = pdjrxtgbList.join('；\n')+'。';
    $('.pdjpg-view .pdjrxtgb .rpt-desc').html(pdjrxtgbText);
  }else {
    $('.pdjpg-view .pdjrxtgb').hide();
  }

  // 盆底筋膜缺陷的间接征象
  var pejmqxList = [], pejmqxText = '';
  var gtjjxVal = getVal('[name="gtjjx-sta"]:checked'); // 肛提肌间隙
  var cghjxVal = getVal('[name="cghjx-sta"]:checked'); // 耻骨后间隙
  var ydxzVal = getVal('[name="ydxz-sta"]:checked'); // 阴道形态
  var pgxzVal = getVal('[name="pgxz-sta"]:checked'); // 膀胱形态
  gtjjxVal ? pejmqxList.push('肛提肌间隙：'+gtjjxVal) : '';
  cghjxVal ? pejmqxList.push('耻骨后间隙：'+cghjxVal) : '';
  ydxzVal ? pejmqxList.push('阴道形态：'+ydxzVal) : '';
  pgxzVal ? pejmqxList.push('膀胱形态：'+pgxzVal) : '';

  if(pejmqxList.length) {
    pejmqxText = pejmqxList.join('；\n')+'。';
    $('.pdjpg-view .pdjmqx .rpt-desc').html(pejmqxText);
  }else {
    $('.pdjpg-view .pdjmqx').hide();
  }

  // 测量
  var measureText = getMeasureDesc();
  $('.pdjpg-view .measure .rpt-desc').html(measureText);

  // 其他解剖异常
  if(!getVal('[id="mrpdjpg-rt-64"]')) {
    $('.pdjpg-view .qtjpyc').hide();
  } else {
    $('.pdjpg-view .qtjpyc .rpt-desc').html(getVal('[id="mrpdjpg-rt-64"]'));
    $('.pdjpg-view .qtjpyc').show();
  }

  // 前腔
  var qqStr = getQqDesc();
  $('.pdjpg-view .qq .rpt-desc').html(qqStr);

  // 中腔
  var zqStr = getZqDesc();
  $('.pdjpg-view .zq .rpt-desc').html(zqStr);

  // 后腔
  var hqStr = getHqDesc();
  $('.pdjpg-view .hq .rpt-desc').html(hqStr);


  // 其他征象
  if(!getVal('[id="mrpdjpg-rt-147"]')) {
    $('.pdjpg-view .otherdesc').hide();
  } else {
    $('.pdjpg-view .otherdesc .rpt-desc').html(getVal('[id="mrpdjpg-rt-147"]'));
    $('.pdjpg-view .otherdesc').show();
  }
  // 印象
  $('.pdjpg-view .bt-imp').html(idAndDomMap['mrpdjpg-rt-148']?.value || '');
}