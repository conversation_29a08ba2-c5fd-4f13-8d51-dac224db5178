#hlqj1 {
  font-size: 14px;
  color: #303133;
}
#hlqj1 .hlqj-content{
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#hlqj1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#hlqj1 .add-btn{
  font-size: 14px;
  color: #1885F2;
  line-height: 22px;
  font-weight: 400;
  margin-right: 16px;
  cursor: pointer;
}
#hlqj1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#hlqj1 .rt-sr-lb {
  margin-left: 4px;
}
#hlqj1 .item-content{
  padding: 12px 16px;
}
#hlqj1 .form-content{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlqj1 .item-content .gray-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlqj1 .item-content .form-item{
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 12px;
}
#hlqj1 .form-item-title{
  color: #606266;
}
#hlqj1 .input-width{
  width: 204px;
}
#hlqj1 .layui-form{
  display: flex;
  align-items: center;
}
#hlqj1 .form-box{
  border: 1px solid #eee;
}
#hlqj1 .form-box .input-width{
  border: 0;
  width: 59px;
}
#hlqj1 .unit-content{
  width: 61px;
  line-height: 38px;
  text-align: center;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #eee;
  border-left: 0;
}
#hlqj1 .advice-content{
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  margin-bottom: 12px;
}
#hlqj1 .advice-content .advice-title{
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: rgba(255,255,255,0);
  box-shadow: inset 0px -1px 0px 0px #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#hlqj1 .item-content .showInt {
  position: relative;
  background: #fff;
  width: 200px;
}
#hlqj1 .item-content .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#hlqj1 .item-content .result-content{
  width: 49%;
  height: 60px;
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
#hlqj1 .second-content{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  height: 60px;
  background: #FAFAFA;
  border-radius: 4px;
  padding-left: 12px;
  border: 1px solid #EBEEF5;
}
[isview="true"] #hlqj1 .item-content .showInt::after{
  display: none;
}
[isview="true"] #hlqj1 .form-box{
  border: 0px solid #eee;
}