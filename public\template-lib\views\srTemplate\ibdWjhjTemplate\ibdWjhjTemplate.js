$(function () {
  window.initHtmlScript = initHtmlScript;
})
// id列表
var sortIdList = [], //排序
  partDescIdList = [], //食管、胃体、胃窦、十二指肠描述
  yzfbIdList = [], //炎症分布
  hdxIdList = [], //活动性
  ryzIdList = [], //肉芽肿
  ryzPositionIdList = [], //位置
  ryzNumsIdList = [], //数量
  ryzZjIdList = [], //最大直径
  ksrsIdList = [], //抗酸染色
  yxzsIdList = [], //异型增生
  cmvZjIdList = [], //CMV免疫组化或原位杂交
  emrZjIdList = []; //EBER原位杂交
var letterList = ['A', 'B', 'C', 'D',];
var sortSelList = [
  { title: '', id: '0' },
  { title: '①', id: '①' },
  { title: '②', id: '②' },
  { title: '③', id: '③' },
  { title: '④', id: '④' },
]
var sortList = ['①', '②', '③', '④']
var partDescObj = {
  A: [
    { title: '', id: '0' },
    { title: '未见明确异常', id: "未见明确异常" },
    { title: '糜烂性食管炎', id: "糜烂性食管炎" },
    { title: '上皮内淋巴细胞增多', id: '上皮内淋巴细胞增多' },
    { title: '交界性淋巴细胞浸润', id: '交界性淋巴细胞浸润' },
    { title: '其他', id: '其他' }
  ],
  B: [
    { title: '', id: '0' },
    { title: '未见明确异常', id: "未见明确异常" },
    { title: '局灶增强性胃炎', id: "局灶增强性胃炎" },
    { title: '非活动性胃炎', id: '非活动性胃炎' },
    { title: '慢性活动性幽门螺杆菌胃炎', id: '慢性活动性幽门螺杆菌胃炎' },
    { title: '其他', id: '其他' }
  ],
  C: [
    { title: '', id: '0' },
    { title: '未见明确异常', id: "未见明确异常" },
    { title: '局灶增强性胃炎', id: "局灶增强性胃炎" },
    { title: '非活动性胃炎', id: '非活动性胃炎' },
    { title: '慢性活动性幽门螺杆菌胃炎', id: '慢性活动性幽门螺杆菌胃炎' },
    { title: '其他', id: '其他' }
  ],
  D: [
    { title: '', id: '0' },
    { title: '未见明确异常', id: "未见明确异常" },
    { title: '活动性慢性十二指肠炎', id: "活动性慢性十二指肠炎" },
    { title: '慢性十二指肠炎', id: '慢性十二指肠炎' },
    { title: '其他', id: '其他' }
  ]
}
//炎症分布
var yzfb = [
  { title: '', id: '0' },
  { title: '局灶性', id: "局灶性" },
  { title: '弥漫性', id: "弥漫性" }
]
//活动性
var hdx = [
  { title: '', id: '0' },
  { title: '无', id: "无" },
  { title: '上皮内', id: "上皮内" },
  { title: '固有层', id: "固有层" }
]
//肉芽肿
var ryz = [
  { title: '', id: '0' },
  { title: '未见肉芽肿', id: "未见肉芽肿" },
  { title: '可见肉芽肿', id: "可见肉芽肿" },
  { title: '可疑肉芽肿', id: "可疑肉芽肿" },
  { title: '异物肉芽肿', id: "异物肉芽肿" },
  { title: '隐窝破裂性肉芽肿', id: "隐窝破裂性肉芽肿" }
]
//肉芽肿-位置
var ryzPosition = [
  { title: '', id: '0' },
  { title: '黏膜层', id: "黏膜层" },
  { title: '黏膜下层', id: "黏膜下层" }
]
//肉芽肿-数量
var ryzNums = [
  { title: '', id: '0' },
  { title: '单个', id: "单个" },
  { title: '多个', id: "多个" }
]
//肉芽肿-最大直径
var numList = [
  { title: '', id: '0' },
  { title: '1', id: 1 },
  { title: '2', id: 2 },
  { title: '3', id: 3 },
  { title: '4', id: 4 },
  { title: '5', id: 5 },
];
// 异型增生
var yxzs = [
  { title: '', id: '0' },
  { title: '无见异型增生', id: "无见异型增生" },
  { title: '低级别异型增生', id: "低级别异型增生" },
  { title: '高级别异型增生', id: "高级别异型增生" },
  { title: '不确定性异型增生', id: "不确定性异型增生" }
]
// 抗酸染色&CMV免疫组化或原位杂交&EBER原位杂交
var examResult = [
  { title: '', id: '0' },
  { title: '未做', id: "未做" },
  { title: '阴性', id: "阴性" },
  { title: '阳性', id: "阳性" }
]
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#ibdWjhj1 .wjhj-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView()
    } else {
      initPage()
    }
  }
}

// 回显预览内容
function initView() {
  // rptImageList = [{src: 'xxx',},{src: 'xxx',},{src: 'xxx',},{src: 'xxx',},{src: 'xxx',},]
  let hideKey = []
  curElem.find('.wjhj-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'sampleSeen') {
      if (value) {
        $(this).html(value)
      } else {
        $(this).parent().hide();
        hideKey.push(key)
      }
    } else if (key === 'impression') {
      let pathDiagVal = getImpression('view');
      if (pathDiagVal) {
        $(this).html(pathDiagVal)
      } else {
        $(this).parent().hide();
        hideKey.push(key)
      }
    }else{
      value ? $(this).html(value) : $(this).html('')
      if (!value && key === 'clinDiag') {
        $(this).parent().parent().hide()
      }
    }
    addIdToNodeByView(this, key, idAndDomMap);
  })
  hideKey.length > 1 ? $("#wjhjDesc").hide() : ''
  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (let i = 0; i < rptImageList.length; i++) {
      if (rptImageList[i].src && i < 4) {
        imgHtml += `
        <div class="item-img">
          <img src="${rptImageList[i].src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find('.wjhj-view .rpt-img-ls').html(imgHtml);
      curElem.find('.wjhj-view .rpt-img-ls').css('display', 'flex');
    }
  }
  curElem.find('.wjhj-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}

// 初始化编辑页
function initPage() {
  sortIdList = initIdList('4');
  partDescIdList = initIdList('2');
  yzfbIdList = initIdList('6');
  hdxIdList = initIdList('8');
  ryzIdList = initIdList('10');
  ryzPositionIdList = initIdList('12');
  ryzNumsIdList = initIdList('14');
  ryzZjIdList = initIdList('16');
  ksrsIdList = initIdList('18');
  yxzsIdList = initIdList('20');
  cmvZjIdList = initIdList('22');
  emrZjIdList = initIdList('25');
  initInpAndSel(sortIdList, sortSelList, 80);
  for (let p = 0; p < partDescIdList.length; p++) {
    let type = partDescIdList[p].charAt(2)
    initInpAndSel([partDescIdList[p]], partDescObj[type], 340)
  }
  initInpAndSel(yzfbIdList, yzfb, 152);
  initInpAndSel(hdxIdList, hdx, 168);
  initInpAndSel(ryzIdList, ryz, 120);
  initInpAndSel(ryzPositionIdList, ryzPosition, 104);
  initInpAndSel(ryzNumsIdList, ryzNums, 104);
  initInpAndSel(ryzZjIdList, numList, 104);
  initInpAndSel(ksrsIdList, examResult, 104);
  initInpAndSel(yxzsIdList, yxzs, 232);
  initInpAndSel(cmvZjIdList, examResult, 152, 'change');
  initInpAndSel(emrZjIdList, examResult, 152, 'change');
  for (let letter of letterList) {
    changeSel(`wj${letter}-rt-22`);
    changeSel(`wj${letter}-rt-25`);
  }
}
function initIdList(num) {
  let arr = [];
  for (let letter of letterList) {
    arr.push(`wj${letter}-rt-${num}`);
  }
  return arr;
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal, type) {
  let selLen = 100;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  idList.forEach(item => {
    dropdown.render({
      elem: `#${item}`,
      data: optionList,
      className: 'laySelLab',
      click: function (obj) {
        this.elem.val(obj.title);
        type === 'change' ? changeSel(item) : ''
      },
      style: `width: ${selLen}px;`
    });
  });
}
// CMV、EMR
function changeSel(item) {
  let rtType = item.slice(0, -2);
  let idNum = item.slice(-2);
  let iptId = Number(idNum) + 1;
  if ($(`#${item}`).val() !== '阳性') {
    $(`#${rtType}${iptId}`).val('')
    $(`#${rtType}${iptId}`).attr('disabled', 'disabled')
  } else {
    $(`#${rtType}${iptId}`).removeAttr('disabled')
  }
}

// 整理报告内容
function getImpression(type) {
  let strArr = []
  let str = ''
  for (let i = 0; i < letterList.length; i++) {
    let partId = `wj${letterList[i]}-rt-1`;
    let partName = $(`#${partId}`).attr('data-srname') || "";
    let textId = `wj${letterList[i]}-rt-2`;
    let sortId = `wj${letterList[i]}-rt-4`;
    let textIdVal = $(`#${textId}`).val() || "";
    let sortIdVal = $(`#${sortId}`).val() || "";
    if (textIdVal || sortIdVal) {
      let num = sortList.findIndex(item => item === sortIdVal);
      strArr.push({
        sortNum: num !== -1 ? (num + 1) : 0,
        str: `${sortIdVal}${partName}${textIdVal ? '：' + textIdVal : ''}`
      })
    }
  }
  strArr.sort(function (a, b) {
    return a.sortNum - b.sortNum
  })
  strArr && strArr.length ? str = strArr.map(item => item.str).join('。\n') : ""
  if ($('#wjhj-rt-2').val() !== '') {
    str ? str += `。\n${type && type === 'view' ? '备注：' : ''}${$('#wjhj-rt-2').val()}` : str = (type && type === 'view' ? '备注：' : '') + $('#wjhj-rt-2').val()
  }
  return str
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}