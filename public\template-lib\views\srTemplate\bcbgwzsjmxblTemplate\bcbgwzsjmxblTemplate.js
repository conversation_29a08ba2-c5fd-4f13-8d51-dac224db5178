$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;

function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector("#bcbgwzsjmxbl1 .view-wrap"),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    console.log(`idAndDomMap11111111111${idAndDomMap}`);
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initViewCon()
    } else {
      changeTextSize();
      // initSelectData()
      // getRelativeCon()
      // getOrderResultList('免疫组化');
      // for(let id in dbIdMap) {
      //   $('[id="'+id+'"]').on('dblclick',function() {
      //     dblHandler(id);
      //   })
      // }
    }
  }
}

// 获取诊断
function getImpression() {
  var checkboxList = $('#bcbgwzsjmxbl1 [type="radio"]');
  var impression = '';
  var strList = [];
  checkboxList.each(function(i,dom) {
    let isChecked = $(dom).is(':checked');
    if(isChecked) {
      let val = $(dom).val();
      let parentVal = $(dom).parent().parent().find('.title_text').text();
      let str = parentVal + ':' + val;
      strList.push(str);
    }
    
  });
  let textarea = $('.textarea').val();
  if(textarea) {
    strList.push(`评论:${textarea}`)
  }
  impression = strList ? strList.join('；') + '；' : '';
  console.log(impression);
  return impression;
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    rptImageList: rptImageList,   //报告图像
    // sampleSeen: getSampleSeenStr(),  //肉眼所见
    examParam: '',   //特殊检查
  }
  // console.log(rtStructure);
}

// 字号切换
function changeTextSize() {
  var localSize = getOrSetSizeType('get');
  setSizeDom(localSize);
  $('body').on('click', '.text-size img', function () {
    if ($(this).hasClass('on')) {
      return;
    }
    var size = $(this).closest('.img-wrap').attr('data-size');
    setSizeDom(size);
    getOrSetSizeType('set', size);
  })
}

// function initSelectData() {
//   xbbwa_selectData.forEach(item => {
//     initInpAndSel(item.id, item.optionList, item.width)
//   });
// }
// 设置字体大小元素展示
function setSizeDom(size) {
  $('.edit-wrap .on').hide();
  $('.edit-wrap .off').show();
  $('.edit-wrap .img-wrap[data-size="' + size + '"] .on').show();
  $('.edit-wrap .img-wrap[data-size="' + size + '"] .off').hide();
  $('.edit-wrap').removeClass('default large larger');
  $('.edit-wrap').addClass(size);
}

// 获取、设置缓存字体大小数据
function getOrSetSizeType(type, size) {
  var userId = publicInfo && publicInfo.userInfo ? publicInfo.userInfo.staffNo : publicInfo.optId;
  var sizeTypeList = getLocalStorage('rt_size_type') || {};
  if(type === 'get') {
    var sizeType = sizeTypeList[userId] || 'default';
    return sizeType;
  }
  if(type === 'set') {
    sizeTypeList[userId] = size;
    setLocalStorage('rt_size_type', sizeTypeList);
  }
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  curElem.find('.view-wrap [data-key]').each(function(){
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var dataValue = publicInfo[key] || idAnVal;
    if(!dataValue) {
      let className = $(this).parent().attr('class');
      if(className.includes('parent')) {
        $(this).parent().hide()
      }
    }
    if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
      dataValue = dataValue + ' ' + publicInfo['affirmTime'];
    }
    $(this).html(dataValue)
    addIdToNodeByView(this, key, idAndDomMap);
  })
  
  curElem.find('.view-wrap [data-name]').each(function(){
    var key = $(this).attr('data-name');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var dataValue = publicInfo[key] || idAnVal;
    
    if(dataValue) {
      $(this).addClass('checkTheBox'); 
    }
  })

  curElem.find('.view-wrap [data-img]').each(function(){
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if(value) {
      var src = getSignImgHandler({staffNo:value});
      if(src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if(rptImageList && rptImageList.length) {
    var imgHtml = '';
    for(var image of rptImageList) {
      if(image.src) {
        imgHtml += `
        <div class="view_img">
          <img src="${image.src}" alt="">
        </div>
        `
      }
    }
    if(imgHtml) {
      curElem.find('.view-wrap .view_img_box').html(imgHtml);
      curElem.find('.view-wrap .view_img_box').show();
    }
  }
}