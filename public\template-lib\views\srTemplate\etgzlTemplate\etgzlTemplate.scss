#etgzl1 {
  font-size: 14px;
  .mt2 {
    margin-top: 2px;
  }
  .mt4 {
    margin-top: 4px;
  }
  .mt8 {
    margin-top: 8px;
  }
  .ml12 {
    margin-left: 12px;
  }
  .w70r {
    width: 70px;
    text-align: right;
  }
  .w202r {
    width: 202px;
    text-align: right;
  }
  .w146r {
    width: 146px;
    text-align: right;
  }
  .w70 {
    width: 70px;
  }
  .w160 {
    width: 160px;
  }
  .w46 {
    width: 46px;
  }
  .w60 {
    width: 60px;
  }
  .top-bar {
    border-bottom: 1px solid #C8D7E6;
    background: #E8F3FF;
    .bar-inner {
      width: 960px;
      margin: 0 auto;
    }
    span {
      margin-left: 12px;
      font-weight: bold;
      font-size: 18px;
      color: #000000;
      line-height: 39px;
    }
  }
  .body-wrap {
    width: 960px;
    margin: 0 auto;
    padding: 12px 12px 12px 0;
    border-left: 1px solid #DCDFE6;
    border-right: 1px solid #DCDFE6;
    background: white;
    .detail-item {
      overflow: hidden;
      margin-bottom: 8px;
      color: #000;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .lb {
      width: 118px;
      text-align: right;
      margin-top: 3px;
      float: left;
      line-height: 28px;
      color: #303133;
    }
    .slb {
      display: inline-block;
      vertical-align: top;
    }
    .cont {
      padding: 3px 12px;
      margin-left: 118px;
      min-height: 36px;
      line-height: 28px;
      background: #F5F7FA;
      border: 1px solid #DCDFE6;
      overflow: hidden;
      &.with-textarea {
        padding: 0;
        border: none;
      }
      input[type="text"] {
        width: 100px;
        vertical-align: top;
        padding: 0 4px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #C0C4CC;
      }
      textarea {
        width: 100%;
        height: 120px;
        padding: 4px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #C0C4CC;
      }
    }
    label {
      display: inline-block;
      vertical-align: top;
      margin-right: 12px;
      &:last-child {
        margin-right: 0;
      }
      &:last-of-type {
        margin-right: 0;
      }
      input {
        vertical-align: top;
      }
      span {
        vertical-align: top;
      }
    }
    .row {
      &.sbox {
        padding: 8px;
        background: #EBEEF5;
        border: 1px solid #DCDFE6;
      }
    }
    .sub-cont-left {
      float: left;
    }
    .sub-cont-right {
      padding: 3px 12px;
      margin-left: 100px;
      margin-top: -3px;
      margin-bottom: -3px;
      margin-right: -12px;
      border-left: 1px solid #DCDFE6;
      background: #EBEEF5;
    }
  }
  .footer-wrap {
    background: #F5F7FA;
    border-top: 1px solid #DCDFE6;
    .footer-inner {
      width: 960px;
      margin: 0 auto;
      padding: 8px 0;
    }
    .info-item {
      overflow: hidden;
      .lb {
        width: 80px;
        text-align: center;
        font-weight: bold;
        font-size: 18px;
        color: #000000;
        float: left;
      }
      .cont {
        margin-left: 80px;
        line-height: 24px;
      }
      textarea {
        width: 100%;
        height: 150px;
        padding: 0 4px;
        background: #FFFFFF;
        border-radius: 3px;
        line-height: 24px;
        border: 1px solid #C0C4CC;
      }
    }
  }
  .switcher-wrap {
    &.switcher1 {
      .switcher1 {
        display: block;
      }
      .switcher2 {
        display: none;
      }
    }
    &.switcher2 {
      .switcher1 {
        display: none;
      }
      .switcher2 {
        display: block;
      }
    }
  }
}
[isview="true"] #etgzl1 {
  .view-lb-no-width {
    .slb {
      width: auto;
    }
  }
  .switcher-wrap {
    &.switcher1 {
      .sub-cont-left {
        display: none;
      }
      .sub-cont-right {
        margin: 0;
        padding: 0;
        border: none;
        background: none;
      }
    }
  }
  .qtzx-wrap {
    .text-con {
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  .impression-cont {
    font-size: 16px;
    .text-con {
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
  .left-out-tag {
    .out-tag {
      margin-bottom: -28px;
    }
    .row:not(.out-tag) {
      margin-left: 50px;
    }
  }
}