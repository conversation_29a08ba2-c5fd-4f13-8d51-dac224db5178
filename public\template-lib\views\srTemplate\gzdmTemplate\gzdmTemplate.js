$(function() {
  window.initHtmlScript = initHtmlScript;
})
var mmUnit = 'mm', mDevSUnit = 'm/s', mmHgUnit = 'mmHg';
var dplBlockShow = false;
var isFillInStatus1 = {}, isFillInStatus2 = {};
var rtStructure = null;
var resultData = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  // initTabContentHandler();
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }
  // 默认显示第一个
  setInpEnterJump('zgb-block','block1');
  setInpEnterJump('ygb-block','block2');

  resultData = rtStructure ? rtStructure.enterOptions.resultData : [];
  if(resultData.length) {
    $('.dplBlock').css('display','block');
    dplBlockShow = true;
  }
  setDplInp();
  initPage();

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}
// 初始化数据
function initPage() {
  initClickFun();
  showCkData();
}
// 初始化点击事件
function initClickFun() {
  // 左冠状动脉
  $('.block-ck1').click(function(e) {
    var target = $(this).find('input');
    var id = target.attr('id');
    if(isFillInStatus1[id]) {
      e.preventDefault();
    }
    if(target.is(':checked')) {
      isFillInStatus1[id] = '1';
    }
    var _this = $(this);
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    var block = _this.attr('block-name1');
    $("."+block).show().siblings('.block1').hide();
    setInpEnterJump(block,'block1');
  })
  $('.ck-inp1').click(function(e) {
    var id = $(this).attr('id');
    if(isFillInStatus1[id]) {
      $(this).prop('checked', false);
    }
    if(!$(this).is(':checked')) {
      delete isFillInStatus1[id];
      $(this).parents().find('.block1').hide();
    }
  })

  // 右冠状动脉
  $('.block-ck2').click(function(e) {
    var target = $(this).find('input');
    var id = target.attr('id');
    if(isFillInStatus2[id]) {
      e.preventDefault();
    }
    if(target.is(':checked')) {
      isFillInStatus2[id] = '1';
    }
    var _this = $(this);
    _this.addClass('sel-light').siblings().removeClass('sel-light');
    var block = _this.attr('block-name2');
    $("."+block).show().siblings('.block2').hide();
    setInpEnterJump(block,'block2');
  })
  $('.ck-inp2').click(function(e) {
    var id = $(this).attr('id');
    if(isFillInStatus2[id]) {
      $(this).prop('checked', false);
    }
    if(!$(this).is(':checked')) {
      delete isFillInStatus2[id];
      $(this).parents().find('.block2').hide();
    }
  })

  // 多普勒按钮
  $('.dplBtn').click(function() {
    dplBlockShow = !dplBlockShow;
    dplBlockShow ? $('.dplBlock').css('display','block') : $('.dplBlock').css('display','none');
    setDplInp();
  })
}
// 回显数据
function showCkData() {
  var hightCkArr1 = $('.block-ck1').find('.ck-inp1:checked');
  var hightCkArr2 = $('.block-ck2').find('.ck-inp2:checked');
  hightCkArr1.each(function(i, dom) {
    if($(dom).is(":checked")) {
      var id = $(dom).attr('id');
      isFillInStatus1[id] = '1';
      if(i === 0) {
        $(dom).parents('.block-ck1').click();
      }
    }
  })
  hightCkArr2.each(function(i, dom) {
    if($(dom).is(":checked")) {
      var id = $(dom).attr('id');
      isFillInStatus1[id] = '1';
      if(i === 0) {
        $(dom).parents('.block-ck2').click();
      }
    }
  })
}
// 设置输入框是否跳转
function setInpEnterJump(block,siblingBlock) {
  let curEleInp = $("."+block).find('input[type="text"]');
  let siblingEleInp = $("."+block).siblings("."+siblingBlock).find('input[type="text"]');
  curEleInp.each(function(i,dom) {
    $(dom).removeAttr('n-enterjump');
  })
  siblingEleInp.each(function(i,dom) {
    $(dom).attr('n-enterjump','1');
  })
  keydown_to_tab('gzdm1');
}
// 点击多普勒按钮
function setDplInp() {
  let dplInpChild = $('.dplBlock').find('input[type="text"]');
  let dplCRChild = $('.dplBlock').find('input:not([type="text"])');
  if(!dplBlockShow) {
    dplInpChild.each(function(i,dom) {
      $(dom).val('');
      $(dom).attr('n-enterjump','1');
    })
    dplCRChild.each(function(i,dom) {
      $(dom).attr('checked',false);
    })
  }else {
    dplInpChild.each(function(i,dom) {
      $(dom).removeAttr('n-enterjump');
    })
  }
  keydown_to_tab('gzdm1');
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function getLRGzdmDesc(blockParId1,parData) {
  let blockStrList = [];
  let njStr = '内径',dxStr = '大小为',lglrStr = '有瘘管引流入';
  if(blockParId1.includes(parData.id)) {
    blockStrList.push(parData.val);
    let childD1 = parData.child || [];
    childD1.map(function(cItem1) {
      let childD2 = cItem1.child || [];
      // 大小
      if(cItem1.name === dxStr){
        let dxInpStr = '';
        let dxInpList = [];
        childD2.length ? dxInpStr = cItem1.name : '';
        childD2.map(function(cItem2) {
          dxInpList.push(cItem2.val + mmUnit);
        })
        dxInpStr += dxInpList.join('X');
        blockStrList.push(dxInpStr);
      }
      // 内径
      else if(cItem1.name === njStr) {
        let njInpVal = ''; 
        childD2.map(function(cItem2) {
          njInpVal = cItem2.val + mmUnit;
        });
        blockStrList.push(cItem1.name + njInpVal);
      }
      // 有瘘管引流入
      else if(cItem1.name === lglrStr) {
        childD2.map(function(cItem2) {
          blockStrList.push(cItem1.val + cItem2.val);
        });
      }
      else {
        blockStrList.push(cItem1.val);
      }
    })
  }
  return blockStrList;
}

// 获取描述
function getDescription() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let description = '';
  let njStr = '内径',dxStr = '大小为',lglrStr = '有瘘管引流入';
  let strList = [],blockStrList1 = [],blockStrList2 = [],blockStrList3 = [];
  let blockParId1 = ['gzdm-rt-1','gzdm-rt-2','gzdm-rt-3','gzdm-rt-4','gzdm-rt-56','gzdm-rt-57','gzdm-rt-58'];
  let blockParId2 = ['gzdm-rt-110','gzdm-rt-117','gzdm-rt-124'];
  let blockParId3 = ['gzdm-rt-131'];
  // let blockParId1 = ['gzdm-rt-1'];
  // let blockParId2 = ['gzdm-rt-2','gzdm-rt-3','gzdm-rt-4'];
  // let blockParId3 = ['gzdm-rt-56','gzdm-rt-57','gzdm-rt-58'];

  $('#gzdm1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      // 左冠状动脉、右冠状动脉
      // 测试
      // blockStrList1.push(getLRGzdmDesc(blockParId1,parData));
      // blockStrList2.push(getLRGzdmDesc(blockParId2,parData));
      // blockStrList3.push(getLRGzdmDesc(blockParId3,parData));
      if(blockParId1.includes(parData.id)) {
        blockStrList1.push(parData.val);
        let childD1 = parData.child || [];
        childD1.map(function(cItem1) {
          let childD2 = cItem1.child || [];
          // 大小
          if(cItem1.name === dxStr){
            let dxInpStr = '';
            let dxInpList = [];
            childD2.length ? dxInpStr = cItem1.name : '';
            childD2.map(function(cItem2) {
              dxInpList.push(cItem2.val + mmUnit);
            })
            dxInpStr += dxInpList.join('X');
            blockStrList1.push(dxInpStr);
          }
          // 内径
          else if(cItem1.name === njStr) {
            let njInpVal = ''; 
            childD2.map(function(cItem2) {
              njInpVal = cItem2.val + mmUnit;
            });
            blockStrList1.push(cItem1.name + njInpVal);
          }
          // 有瘘管引流入
          else if(cItem1.name === lglrStr) {
            childD2.map(function(cItem2) {
              blockStrList1.push(cItem1.val + cItem2.val);
            });
          }
          else {
            blockStrList1.push(cItem1.val);
          }
        })
      }
      // 第二块
      if(blockParId2.includes(parData.id)) {
        let childD1 = parData.child || [];
        if(!childD1.length) {
          blockStrList2.push(parData.val);
        }else {
          childD1.map(function(cItem1) {
            if(cItem1.child && cItem1.child.length) {
              let childD2 = cItem1.child || [];
              childD2.map(function(cItem2) {
                blockStrList2.push(parData.val + cItem2.name);
              })
            }else {
              blockStrList2.push(parData.val + cItem1.val);
            }
          })
        }
      }
      // 第三块
      if(blockParId3.includes(parData.id)) {
        let childD1 = parData.child || [];
        blockStrList3.push(parData.val);
        childD1.map(function(cItem1) {
          if(cItem1.child && cItem1.child.length) {
            let childD2 = cItem1.child || [];
            childD2.map(function(cItem2) {
              if(cItem1.name.includes('PG') || cItem1.name.includes('MG')) {
                blockStrList3.push(cItem1.val + cItem2.val + mmHgUnit);
              }else if(cItem1.name.includes('Vmax')) {
                blockStrList3.push(cItem1.val + cItem2.val + mDevSUnit);
              }else {
                blockStrList3.push(cItem1.val + cItem2.val + mmUnit);
              }
            })
          }
        })
      }
    }
  })
  // blockStrList2 = blockStrList2.length ? blockStrList2.push('左冠状动脉') : '';
  // blockStrList3 = blockStrList3.length ? blockStrList3.push('右冠状动脉') : '';
  blockStrList2.length ? blockStrList2.unshift('CDFI可在该处探及') : '';
  strList.push(blockStrList1,blockStrList2,blockStrList3);
  strList = strList.filter(function(item) { return item.length });
  description = strList.length ? strList.join('。') + '。' : '';
  // console.log('description-->',description);
  return description;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = getDescription();
  rtStructure.impression = '';
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}