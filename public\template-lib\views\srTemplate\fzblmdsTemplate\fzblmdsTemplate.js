$(function () {
  window.initHtmlScript = initHtmlScript;
})
var hiddenIdList = ['mds-rt-2', 'mds-rt-4', 'mds-rt-28']
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#fzblmds1 .mds-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initViewContainer();
    } else {
      initEditContainer()
    }
  }
}

// 回显预览内容
function initViewContainer() {
  curElem.find('.mds-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      $(this).html(value);
    } else {
      hiddenIdList.findIndex((item) => item === key) !== -1 ? $(this).parent().hide() : $(this).text('');;
    }
    addIdToNodeByView(this, key, idAndDomMap);
  })
  curElem.find('.mds-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
  let showTable = false;
  curElem.find('#examResultTable [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      showTable = true;
    }
  })
  showTable ? "" : $('#examResultTable').hide();
}
// 回显编辑页
function initEditContainer() {
  var typeList = [
    { id: "新鲜组织", title: "新鲜组织" },
    { id: "石蜡包埋组织", title: "石蜡包埋组织" },
    { id: "生理盐水收集细胞", title: "生理盐水收集细胞" },
    { id: "骨髓", title: "骨髓" },
    { id: "其他", title: "其他" },
  ]
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#mds-rt-2`,
    data: typeList,
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style: `width: 320px;`
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '';
  let examResultList = [];
  $('.mds-edit .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length > 0) {
        if (curPid.id !== "mds-rt-5") {
          str += `${curPid.val}${curPid.id === 'mds-rt-27' ? '\n' : ''}${child[0].val}\n`
        } else {
          for (let i = 0; i < child.length; i++) {
            let grandson = child[i].child;
            if (grandson && grandson.length > 0) {
              examResultList.push(grandson.map((item) => item.name + '：' + item.val).join('，'))
            }
          }
          examResultList.length > 0 ? str += '检测结果：\n' + examResultList.join('\n') + '\n' : '';
        }
      }
    }
  })
  str.endsWith('\n') ? str = str.slice(0, -1) : ''
  str ? rtStructure.impression = str : ''
}