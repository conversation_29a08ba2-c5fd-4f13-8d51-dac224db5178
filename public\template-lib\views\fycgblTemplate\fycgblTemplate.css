/*-----------------字体大小、行高-----------------*/

.size-14 {
    font-size: 14px;
    line-height: 14px;
}

.size-18 {
    font-size: 18px;
    line-height: 18px;
}

.size-22 {
    font-size: 22px;
    line-height: 22px;
}


/*-------------------字体加粗---------------------*/

.font-weight-600 {
    font-weight: 600;
}


/*---------------------颜色类-----------------------*/

.f-lightgray {
    color: #606266;
}

.f-black {
    color: #000;
}


/*--------申请单---------*/

.apply {
    width: 780px;
    margin: 0 auto;
    padding: 40px 60px 0;
    display: block;
    border: 1px solid #E6E6E6;
    padding-bottom: 27px;
    background: #fff;
}


/*-------申请单头部--------*/

.apply-header {
    position: relative;
    text-align: center;
    border-bottom: 1px solid #999;
    padding-bottom: 20px;
}

.apply-header p {
    margin-bottom: 12px;
}


/*-------病例学编号----------*/

.case-num {
    position: absolute;
    top: 0;
    right: 0;
    padding-top: 3px;
}

/*------------条形码块---------------*/
.qrcode-num {
    position: absolute;
    top: 0;
    left: 0;
}
/*清浮动系列*/

.clearfix {
    *zoom: 1;
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}


/*-----------------位置类-----------------*/

.t-l {
    text-align: left;
}


/*-------宽度根据内容撑开----------*/

.w-i {
    width: inherit;
    padding-top: 0;
}

.w-con.item {
    vertical-align: top;
    height: 28px;
    line-height: 28px;
}


/*-------浮动--------*/

.flo {
    float: left;
    line-height: 28px;
    width: 25%;
    margin: 0 !important;
}

.flo.w10 {
    width: 10%;
}

.flo.w15 {
    width: 15%;
}

.flo.w20 {
    width: 20%;
}

.flo.w30 {
    width: 30%;
}

.flo.w35 {
    width: 35%;
}

.flo.w40 {
    width: 40%;
}

.flo.w50 {
    width: 50%;
}

.flo.w100 {
    width: 100%;
}

.flo .w-con label input {
    vertical-align: top;
}


/*-----------文本框配置-------------*/

textarea {
    border: 1px solid #DCDFE6;
    outline: none;
    padding: 3px 0 3px 16px;
    box-sizing: border-box;
}

.w42-h30 {
    width: 42px;
    height: 30px;
}

.w56-h30 {
    width: 56px;
    height: 30px;
}

.w72-h30 {
    width: 72px;
    height: 30px;
}

.w89-h30 {
    width: 89px;
    height: 30px;
}

.w120-h30 {
    width: 120px;
    height: 30px;
}

.w203-h32 {
    width: 203px;
    height: 32px;
}


/*p指宽度百分比*/

.w100p-h88 {
    width: 100%;
    height: 88px;
    border: none;
}

.w100p-r2 {
    width: 100%;
    padding: 9px 15px;
    color: #303133;
}


/*------------边框线---------------*/

.bottom {
    border-bottom: 1px solid #999;
    padding-bottom: 8px;
    padding-top: 8px;
    box-sizing: content-box;
}


/*------按钮块------*/

.button {
    display: inline-block;
    width: inherit;
    height: 28px;
    padding-right: 8px;
    background-color: #1885F2;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    padding-left: 8px;
}

.button:hover {
    background-color: #60a7ee;
}


/*--------标本样式------------*/

.options {
    display: inline-block;
    width: inherit;
    height: 28px;
    margin-left: 4px;
    padding: 0 8px;
    background-color: #ECF5FF;
    color: #1885F2;
    border-radius: 3px;
    vertical-align: top;
    border: 1px solid #B3D8FF;
    margin-bottom: 2px;
}

.options span {
    cursor: pointer;
}

/*********传染指标子类换行配置************/
.br {
    display: inline-block;
    width: 50%;
    padding-left: 85px;
}
.laydate-w {
    display: inline-block;
    min-width: 100px;
    font-size: 16px;
    border: 1px solid #DCDFE6;
    border-radius: 2px;
    position: relative;
}
.laydate-w .rt-sr-w {
    border: none;
    outline: none;
    display: block;
    padding: 5px 8px 5px 30px;
    box-sizing: border-box;
    height: 30px;
}
.laydate-w .layui-icon {
    position: absolute;
    left: 5px;
    top: 5px;
    font-size: 18px;
}