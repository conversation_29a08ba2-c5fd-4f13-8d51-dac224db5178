#pacsArms1 {
  width: 100%;
  min-height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#pacsArms1 .box-p h4{
  margin-top: 12px;
  font-weight: 600;
}
.box-p{
  padding: 12px 24px;
}
.ver-t{
  vertical-align: top;
}
.title{
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 12px;
}
.in_b{
  display: inline-block;
  margin-right: 10px;
}
.w135{
  width: 135px;
}
.w160{
  width: 160px;
}
.mb-12{
  margin-bottom: 12px;
}
.mb-8{
  margin-bottom: 8px;
}
.ml-14{
  margin-left: 14px;
}
.fsise{
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}
#pacsArms1 .layui-inline input{
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
}
#pacsArms1 .showInt {
  background: #fff;
}
#pacsArms1 .showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
.p-5{
  padding: 10px;
}
.br-t{
  width: 100%;
  height: 1px;
  background: #C0C4CC;
}
.w-calc{
  width: calc(100% - 85px);
}
.w-120{
  width: 120px;
}
.in-sly{
  height: 36px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
}
.tb-in{
  width: 200px;
  margin: 5px;
}
.m-8{
  margin: 8px;
}
.p-8{
  padding: 8px;
}
.c-r{
  color: red;
}
.t-c{
  text-align: center;
}
.jcjg-table{
  width: calc(100% - 90px);
  border: 1px solid #DCDFE6;
  border-collapse:collapse;
  vertical-align: top;
  background: #fff;
  margin: 5px 0;
}

.jcjg-table th, .jcjg-table td{
  border-color: #DCDFE6;
}
.jcjg-table .layui-inline {
  width: 100%;
}
.layui-menu.layui-dropdown-menu {
  max-height: 200px !important;
  overflow-y: auto;
  overflow-x: hidden;
}
.h-th{
  height: 36px;
  line-height: 36px;
  text-align: center;
}
.dw-80{
  display: inline-block;
  width: 80px;
  text-align: right;
}
#pacsNras{
  display: none;
}
#pacsKRAS{
  display: none;
}
#pacsEGFR{
  display: none;
}
#pacsKNPB{
  display: none;
}
#pacsLMGMET{
  display: none;
}
.pacsBras{
  display: none;
}
#fxjg-rt{
  display: none;
}
#bz-rt{
  display: none;
}
#pacsArms1 .pacsArms-view {
  display: none;
}
[isview="true"] #pacsArms1 .pacsArms-edit {
  display: none;
}
[isview="true"] #pacsArms1 .pacsArms-view {
  display: block;
}
[isview="true"] #pacsArms1 .pacsArms-view [data-key="pacsArms-rt-54"] {
  line-height: 20px;
}
/* 预览部分 */
#pacsArms1 .pacsArms-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1110px;
  background: #fff;
  padding: 30px 56px 20px;
}
#pacsArms1 .pacsArms-view .jcjg-table{
  font-size: 14px;
  border:none;
}
#pacsArms1 .pacsArms-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 24px;
  text-align: center;
}
#pacsArms1 .pacsArms-view .page-tit{
  font-size: 24px;
  font-weight: 600;
  /* letter-spacing: 8px; */
}
#pacsArms1 .pacsArms-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  margin-bottom: 8px;
}
#pacsArms1 .pacsArms-view .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
}
#pacsArms1 .pacsArms-view .gray-txt .bold{
  font-weight: 600;
}
#pacsArms1 .pacsArms-view .black-txt {
  color: #000;
  font-size: 14px;
}
#pacsArms1 .pacsArms-view .bold {
  font-weight: bold;
}
#pacsArms1 .pacsArms-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}
#pacsArms1 .pacsArms-view .info-i + .info-i {
  padding-left: 4px;
}
#pacsArms1 .pacsArms-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#pacsArms1 .pacsArms-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}
#pacsArms1 .pacsArms-view .report-wrap {
  padding: 14px 0;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}
#pacsArms1 .pacsArms-view .reporter-i {
  flex: 1;
  display: flex;
}
#pacsArms1 .pacsArms-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
#pacsArms1 .rt-sr-t {
  padding: 0;
}
#pacsArms1 .pacsArms-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#pacsArms1 .pacsArms-view .tip-wrap {
  font-size: 14px;
  margin-top: 8px;
}
[entry-type="5"] #pacsArms1{
  background: #fff;
  padding: 0;
}
[entry-type="5"] #pacsArms1 .pacsArms-view {
  width: 100%;
  min-height: auto;
  margin: unset;
  padding: 12px;
}
[entry-type="5"] #pacsArms1 .pacsArms-view .view-head,
[entry-type="5"] #pacsArms1 .pacsArms-view .rt-sr-header .view-patient,
[entry-type="5"] #pacsArms1 .pacsArms-view .tip-wrap {
  display: none;
}
[entry-type="5"] #pacsArms1 .pacsArms-view div {
  border-bottom: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}