#mrwx1 {
  font-size: 16px;
}
#mrwx1 .sign-content {
  display: flex;
  align-items: center;
  margin-left: 16px;
  margin-top: 12px;
}
#mrwx1 .metal-item {
  display: flex;
  flex-direction: column;
}
#mrwx1 .sign-content .layui-input {
  width: 152px;
}
#mrwx1 .sign-content .weight {
  margin-right: 20px;
}
#mrwx1 .sign-content .temperature {
  display: flex;
  align-items: center;
}
#mrwx1 .sign-content .unit {
  width: 48px;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #DCDFE6;
  border-left: 0;
  line-height: 38px;
  text-align: center;
  color: #606266;
}
#mrwx1 .sign-content .layui-inline input {
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
}
#mrwx1 .sign-content .showInt {
  position: relative;
  background: #fff;
  width: 240px;
}
#mrwx1 .sign-content .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 30px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}

#mrwx1 .mr-content {
  height: 100%;
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}

#mrwx1 .mr-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  margin-bottom: 4px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}

#mrwx1 .mt-20 {
  margin-top: 20px;
}

#mrwx1 .second-content{
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  margin: 8px 16px;
  padding: 6px 12px 12px 12px;
}

#mrwx1 .allergy-content .allergy-item {
  display: flex;
  align-items: center;
  margin-left: 16px;
}
#mrwx1 .allergy-content .allergy-item .layui-input {
  width: 100%;
;
}

#mrwx1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

#mrwx1 .rt-sr-lb {
  margin-left: 4px;
}

#mrwx1 .gray-item {
  display: flex;
  align-items: center;
}

#mrwx1 .layui-inline {
  display: block;
  width: 100%;
}

#mrwx1 .layui-inline input {
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
  width: 100%;
}

/*# sourceMappingURL=mrwxTemplate.css.map */
