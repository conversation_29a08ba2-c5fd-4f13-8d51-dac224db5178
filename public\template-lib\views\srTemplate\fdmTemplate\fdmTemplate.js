$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }

  initC();

  $('#ssq').click(function () {
    cdfiClick('ssq');
  })
  $('#szq').click(function () {
    cdfiClick('szq');
  })
  $('#sq').click(function () {
    cdfiClick('sq');
  })

  $('#zfdm').click(function () {
    clickFdm('a');
  })
  $('#fdm-l').click(function () {
    clickFdm('b');
  })
  $('#fdm-r').click(function () {
    clickFdm('c');
  })

  $('#dpl-tls').click(function () {
    lsClick('tls');
  })
  $('#dpl-fls').click(function () {
    lsClick('fls');
  })

  keydown_to_tab('fdm1');
  // 收缩期、舒张期、双期
  $('.block-ck3').click(function (e) {
    e.preventDefault();
  })
  $('.ck-inp3').click(function (e) {
    e.stopPropagation();
  })
  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });

  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}

//初始化CDFI可在该处探及
function initC() {

  var mfdmChecked = $('#fdm-rt-1').prop("checked");
  var zfdmChecked = $('#fdm-rt-2').prop("checked");
  var yfdmChecked = $('#fdm-rt-3').prop("checked");

  var ssqChecked = $('#fdm-rt-39').prop("checked");
  var szqChecked = $('#fdm-rt-40').prop("checked");
  var sqChecked = $('#fdm-rt-41').prop("checked");
  var tlsChecked = $('#fdm-rt-61').prop("checked");
  var flsChecked = $('#fdm-rt-62').prop("checked");

  if (mfdmChecked) {
    $('#zfdm').parent().parent().attr('style', 'background: #E4E7ED;');
    $('#fdm-b').attr("style", "display:none;");
    $('#fdm-c').attr("style", "display:none;");
  } else if (zfdmChecked) {
    $('#fdm-l').parent().parent().attr('style', 'background: #E4E7ED;');
    $('#fdm-a').attr("style", "display:none;");
    $('#fdm-b').attr("style", "display:unset;");
    $('#fdm-c').attr("style", "display:none;");
  } else if (yfdmChecked) {
    $('#fdm-r').parent().parent().attr('style', 'background: #E4E7ED;');
    $('#fdm-a').attr("style", "display:none;");
    $('#fdm-b').attr("style", "display:none;");
    $('#fdm-c').attr("style", "display:unset;");
  } else {
    $('#zfdm').parent().parent().attr('style', 'background: #E4E7ED;');
    $('#fdm-b').attr("style", "display:none;");
    $('#fdm-c').attr("style", "display:none;");
  }

  if (ssqChecked) {
    $('#ssq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-szq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  } else if (szqChecked) {
    $('#szq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-ssq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  } else if (sqChecked) {
    $('#sq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-ssq').attr("style", "display:none;");
    $('#dpl-szq').attr("style", "display:none;");
  } else {
    $('#ssq').parent().parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-szq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  }
  if (flsChecked) {
    $('#tls').attr("style", "display:none;");
    $('#fls').attr("style", "display:unset;");
  } else {
    $('#tls').attr("style", "display:unset;");
    $('#fls').attr("style", "display:none;");
  }
}
function cdfiClick(ele) {
  $('#ssq').parent().parent().attr('style', 'background:' + (ele === 'ssq' ? '#E4E7ED;' : 'unset;'))
  $('#szq').parent().parent().attr('style', 'background:' + (ele === 'szq' ? '#E4E7ED;' : 'unset;'))
  $('#sq').parent().parent().attr('style', 'background:' + (ele === 'sq' ? '#E4E7ED;' : 'unset;'))
  $('#dpl-ssq').attr('style', 'display:' + (ele === 'ssq' ? 'unset;' : 'none;'));
  $('#dpl-szq').attr('style', 'display:' + (ele === 'szq' ? 'unset;' : 'none;'));
  $('#dpl-sq').attr('style', 'display:' + (ele === 'sq' ? 'unset;' : 'none;'));
}

function lsClick(ele) {
  $('#tls').attr('style', 'display:' + (ele === 'tls' ? 'unset;' : 'none;'));
  $('#fls').attr('style', 'display:' + (ele === 'fls' ? 'unset;' : 'none;'));
}
function clickFdm(ele) {
  $('#zfdm').parent().parent().attr('style', 'background:' + (ele === 'a' ? '#E4E7ED;' : 'unset;'))
  $('#fdm-l').parent().parent().attr('style', 'background:' + (ele === 'b' ? '#E4E7ED;' : 'unset;'))
  $('#fdm-r').parent().parent().attr('style', 'background:' + (ele === 'c' ? '#E4E7ED;' : 'unset;'))
  $('#fdm-a').attr('style', 'display:' + (ele === 'a' ? 'unset;' : 'none;'));
  $('#fdm-b').attr('style', 'display:' + (ele === 'b' ? 'unset;' : 'none;'));
  $('#fdm-c').attr('style', 'display:' + (ele === 'c' ? 'unset;' : 'none;'));
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '';
  let strList = [], dplCheckList = [], dplTj = [], flsList = [];
  let dxIdList = ['fdm-rt-71', 'fdm-rt-72', 'fdm-rt-73'];
  let bjPid = ['fdm-rt-13', 'fdm-rt-24', 'fdm-rt-35'];
  let tjPid = ['fdm-rt-39', 'fdm-rt-40', 'fdm-rt-41'];
  let tjId = ['fdm-rt-46', 'fdm-rt-47', 'fdm-rt-52', 'fdm-rt-53', 'fdm-rt-58', 'fdm-rt-59'];
  let lskId = ['fdm-rt-63', 'fdm-rt-67'];
  let cwId = ['fdm-rt-64', 'fdm-rt-68'];
  let pgId = ['fdm-rt-65', 'fdm-rt-69'];
  let mgId = ['fdm-rt-66', 'fdm-rt-70'];
  $('#fdmCheck .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      strList.push(curPid.val)
      if (child && child.length) {
        for (var c = 0; c < child.length; c++) {
          if (dxIdList.indexOf(child[c].id) === -1 && bjPid.indexOf(child[c].id) === -1) {
            strList.push(child[c].val)
          }
          if (dxIdList.indexOf(child[c].id) !== -1) {
            let dchild = child[c].child;
            console.log('dchild??', dchild);
            if (dchild && dchild.length) {
              if (child[c].id === 'fdm-rt-71') {
                strList.push(child[c].val + '，大小' + (curKeyValData['fdm-rt-11'] ? curKeyValData['fdm-rt-11'].val : '0') + 'mm * ' + (curKeyValData['fdm-rt-12'] ? curKeyValData['fdm-rt-12'].val : '0') + 'mm')
              }
              if (child[c].id === 'fdm-rt-72') {
                strList.push(child[c].val + '，大小' + (curKeyValData['fdm-rt-22'] ? curKeyValData['fdm-rt-22'].val : '0') + 'mm * ' + (curKeyValData['fdm-rt-23'] ? curKeyValData['fdm-rt-23'].val : '0') + 'mm')
              }
              if (child[c].id === 'fdm-rt-73') {
                strList.push(child[c].val + '，大小' + (curKeyValData['fdm-rt-33'] ? curKeyValData['fdm-rt-33'].val : '0') + 'mm * ' + (curKeyValData['fdm-rt-34'] ? curKeyValData['fdm-rt-34'].val : '0') + 'mm')
              }
            }
          }
          if (bjPid.indexOf(child[c].id) !== -1) {
            let bchild = child[c].child;
            if (bchild && bchild.length) {
              strList.push(child[c].val + bchild[0].val)
            }
          }
        }

      }
    }
  })
  $('#dplCheck').find('.rt-sr-w[pid=fdm-rt-38]').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    var commonTj = function (list, num) {
      let child = list.child;
      let cdfiTjList = [];
      if (child && child.length) {
        if (tjId.indexOf(child[0].id) === -1) {
          let flChild = child[0].child;
          if (flChild && flChild.length) {
            cdfiTjList.push(list.val + flChild[0].val + '分流')
          }
        }
        if (tjId.indexOf(child[0].id) !== -1) {
          cdfiTjList.push(list.val + child[0].val)
        }
      } else {
        cdfiTjList.push(list.val)
      }
      let tjStr = cdfiTjList.join('，')
      return tjStr
    }
    if (curPid) {
      if (tjPid.indexOf(curPid.id) !== -1) {
        dplTj.push(commonTj(curPid))
      }
      if (curPid.id === "fdm-rt-60") {
        let lsChild = curPid.child;
        if (lsChild && lsChild.length) {
          let flsChild = lsChild[0].child;
          if (flsChild && flsChild.length) {
            for (var s = 0; s < flsChild.length; s++) {
              if (lskId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('宽' + flsChild[s].val + 'mm')
              }
              if (cwId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('CW测得Vmax为' + flsChild[s].val + 'm/s')
              }
              if (pgId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('PG为' + flsChild[s].val + 'mmHg')
              }
              if (mgId.indexOf(flsChild[s].id) !== -1) {
                flsList.push('MG为' + flsChild[s].val + 'mmHg')
              }
            }
            dplCheckList.push(lsChild[0].val + flsList.join('，'))
          } else {
            dplCheckList.push(lsChild[0].val)
          }
        }

      }
    }
  })
  dplTj && dplTj.length > 0 ? dplCheckList.unshift('CDFI可在该处探及，' + dplTj.join('，')) : '';
  str = dplCheckList.join('，');
  str ? strList.push('多普勒检查，' + str) : '';
  str = '';
  rtStructure.description = '肺动脉，' + strList.join('，');
}