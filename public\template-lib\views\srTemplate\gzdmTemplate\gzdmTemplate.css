/*-------公共--------*/
.w-con {
  display: inline-block;
  vertical-align: middle;
}
.sel-light {
  background: #E4E7ED;
}

/*-------边距--------*/
.mb-8 {
  margin-bottom: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-14 {
  margin-left: 14px;
}
.ml-22 {
  margin-left: 22px;
}
.ml-174 {
  margin-left: 174px;
}

/*-------宽度--------*/
.wd-80 {
  width: 80px;
}

/*-------总体布局--------*/
ul {
  overflow: auto;
}
#gzdm1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#gzdm1 .gzdm-content {
  min-height: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#gzdm1 .con-box {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#gzdm1 .flex-sty {
  display: flex;
}
#gzdm1 .box-left {
  width: 104px;
  position: relative;
  padding: 11px 0;
  box-sizing: border-box;
  background: #EBEEF5;
}
#gzdm1 .box-left::after {
  width: 1px;
  height: 100%;
  position: absolute;
  content: '';
  right: 0;
  top: 0;
  border-right: 1px solid #C8D7E6;
}
#gzdm1 .box-left label {
  display: inline-block;
  width: 100%;
}
#gzdm1 .box-right {
  flex: 1;
}
#gzdm1 .box-row {
  padding: 8px 12px;
  box-sizing: border-box;
}
#gzdm1 .split-line {
  position: relative;
}
#gzdm1 .split-line::after {
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 0;
  left: 0;
  content: '';
  background: #C8D7E6;
}
#gzdm1 .bor-w {
  border: 1px solid #C8D7E6;
  padding: 0 8px;
  background: #EBEEF5;
}
#gzdm1 .inp-sty {
  height: 28px;
  border: 1px solid #DCDFE6;
}
#gzdm1 [type="checkbox"],#gzdm1 [type="radio"] {
  vertical-align: middle;
}
#gzdm1 [type="text"] {
  padding: 0 2px;
}
#gzdm1 .box-title {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
}
#gzdm1 .add-btn {
  display: inline-block;
  padding: 3px 12px;
  color: #fff;
  background: #1885F2;
  border-radius: 4px;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
}