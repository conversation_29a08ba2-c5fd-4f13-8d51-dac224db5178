$(function() {
  window.initHtmlScript = initHtmlScript;
})

var curElem = null;
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
  }
  desctiptHandler();
  changeNext();
  curElem.find('.rt-sr-w').change(function() {
    desctiptHandler();
  })

  // 初始化实物肿块的相关数据
  initSwAndNzStatus('RG-0001.001.01');
  initSwAndNzStatus('RG-0002.001.01');
  initSwAndNzStatus('RG-0001.002.01');
  initSwAndNzStatus('RG-0002.002.01');

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
    if(rtStructure) {
      rtStructure.setChildrenDisabled(this)
    }
    if(!iput.hasClass('sw-df')) {
      changeSpecialHandler(this);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}

function initSwAndNzStatus(name) {
  var checkedDom = curElem.find('[name="'+name+'"]:checked');
  if(checkedDom.length) {
    if($(checkedDom).val() === '无') {
      changeSpecialHandler(checkedDom[0], true)
    }
  } else {
    changeSpecialHandler(curElem.find('[name="'+name+'"]')[0], true)
  }
}

function desctiptHandler() {
  var sideArr = {'左乳': '0001', '右乳': '0002'};
  var descriptArr = [];
  var impressArr = [];
  var impressObj = {};
  var adviseArr = [];
  for(var key in sideArr) {
    var prefix = sideArr[key];
    var desc = '';
    var impress = '';
    var tps = [];
    var descList = [];
    impressObj[key] = {
      ybName: key === '左乳' ? '左侧腋窝' : '右侧腋窝'
    };
    // 囊肿
    if(getVal('[name="RG-'+prefix+'.001.01"]:checked')) {
      desc += '囊肿：';
      let nzArr = [];
      for(var index = 1; index < 5; index++) {
        if(getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked') === '无') {
          desc += getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked');
        }
        if(getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked') === '有') {
          let ynz = '';
          ynz += getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked');
          var nzW = [];
          if(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--1"]:checked')) {
            nzW.push(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--1"]:checked'))
          }
          if(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--2"]:checked')) {
            nzW.push(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--2"]:checked'))
          }
          if(nzW.length) {
            ynz += nzW.join('、');
          }
          nzArr.push(ynz);
        }
        if(getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked') === '单纯囊肿') {
          let dcnz = '';
          let dcTps = [];
          for(var i = 1; i < 3; i++ ) {
            if(getVal('[id="rxSP-'+prefix+'.001.01.0'+i+'"] .rt-sr-w')){
              dcTps.push(getVal('[id="rxSP-'+prefix+'.001.01.0'+i+'"] .rt-sr-w') + 'mm')
            }
          }
          dcnz += getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked');
          if(dcTps.length) {
            dcnz += '，较大' + dcTps.join(' x ')
          }
          nzArr.push(dcnz);
        }
        if(getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked') === '复杂囊肿') {
          let fznz = '';
          let fzTps = [];
          for(var i = 3; i < 5; i++ ) {
            if(getVal('[id="rxSP-'+prefix+'.001.01.0'+i+'"] .rt-sr-w')){
              fzTps.push(getVal('[id="rxSP-'+prefix+'.001.01.0'+i+'"] .rt-sr-w') + 'mm')
            }
          }
          fznz += getVal('[id="rxSP-'+prefix+'.001.01-rItem--'+index+'"]:checked');
          if(fzTps.length) {
            fznz += '，较大' + fzTps.join(' x ')
          }
          nzArr.push(fznz);
        }
      }
      desc += nzArr.join('。');
      // if(getVal('[name="RG-'+prefix+'.001.01"]:checked') === '单纯囊肿' || getVal('[name="RG-'+prefix+'.001.01"]:checked') === '复杂囊肿') {
      //   for(var i = 1; i < 5; i++ ) {
      //     if(getVal('[id="rxSP-'+prefix+'.001.01.0'+i+'"] .rt-sr-w')){
      //       tps.push(getVal('[id="rxSP-'+prefix+'.001.01.0'+i+'"] .rt-sr-w') + 'mm')
      //     }
      //   }
      // }
      // desc += '囊肿：' + getVal('[name="RG-'+prefix+'.001.01"]:checked');
      // if(getVal('[name="RG-'+prefix+'.001.01"]:checked') === '有') {
      //   // rxSP-0002.001.01.05-cItem--1
      //   var nzW = [];
      //   if(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--1"]:checked')) {
      //     nzW.push(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--1"]:checked'))
      //   }
      //   if(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--2"]:checked')) {
      //     nzW.push(getVal('[id="rxSP-'+prefix+'.001.01.05-cItem--2"]:checked'))
      //   }
      //   if(nzW.length) {
      //     desc += nzW.join('、');
      //   }
      // }
      // if(tps.length) {
      //   desc += '，较大' + tps.join(' x ')
      // }

      // 部位
      // rxSP-0001.018.01-cItem--1
      var bwArr = []
      for(var k = 1; k < 5; k++) {
        if(getVal('[id="rxSP-'+prefix+'.018.01-cItem--'+k+'"]:checked')) {
          bwArr.push(getVal('[id="rxSP-'+prefix+'.018.01-cItem--'+k+'"]:checked'));
        }
      }
      if(bwArr.length) {
        desc += '，部位象限('+ bwArr.join('、') +')';
      }
      descList.push(desc);
    }
    
    desc = '';
    var tempArr = [];
    // 实物肿块
    if(getVal('[name="RG-'+prefix+'.002.01"]:checked')) {
      tps = [];
      if(getVal('[name="RG-'+prefix+'.002.01"]:checked') === '有') {
        if(getVal('[id="rxSP-'+prefix+'.002.01.01-cItem--1"]:checked')) {
          tps.push(getVal('[id="rxSP-'+prefix+'.002.01.01-cItem--1"]:checked'))
        }
        if(getVal('[id="rxSP-'+prefix+'.002.01.01-cItem--2"]:checked')) {
          tps.push(getVal('[id="rxSP-'+prefix+'.002.01.01-cItem--2"]:checked'))
        }
      }
      desc += '实物肿块：' + getVal('[name="RG-'+prefix+'.002.01"]:checked');
      if(tps.length) {
        // desc += '(' + tps.join('、') + ')'
        desc += tps.join('、');
      }
      // desc += '。'
      tempArr.push(desc);
    }
    // 部位：
    desc = '';
    if(getVal('[name="RG-'+prefix+'.003.01"]:checked')) {
      desc += '部位：' + getVal('[name="RG-'+prefix+'.003.01"]:checked');
      if(getVal('[name="RG-'+prefix+'.003.01"]:checked')==='象限法') {
        var xxArr = [];
        for(var i = 1; i < 5; i++) {
          if(getVal('[id="rxSP-'+prefix+'.003.01.01-cItem--'+i+'"]:checked')) {
            xxArr.push(getVal('[id="rxSP-'+prefix+'.003.01.01-cItem--'+i+'"]:checked'));
          }
        }
        if(xxArr.length) {
          desc += '('+xxArr.join('、')+')';
        }
      } else {
        var sizeArr = [];
        if(getVal('[id="rxSP-'+prefix+'.003.01.02"] .rt-sr-w')) {
          sizeArr.push(getVal('[id="rxSP-'+prefix+'.003.01.02"] .rt-sr-w') + '点');
        }
        if(getVal('[name="RG-'+prefix+'.003.01.03"]:checked')) {
          sizeArr.push('距离乳头' + getVal('[name="RG-'+prefix+'.003.01.03"]:checked') + '三分之一');
        }
        if(sizeArr.length) {
          desc += '，'+sizeArr.join('，');
        }
      }
      // desc += '。';
      tempArr.push(desc);
    }

    // 较大
    desc = '';
    if(getVal('[id="rxSP-'+prefix+'.004.01"] .rt-sr-w') || getVal('[id="rxSP-'+prefix+'.004.02"] .rt-sr-w')) {
      tps = [];
      if(getVal('[id="rxSP-'+prefix+'.004.01"] .rt-sr-w')) {
        tps.push(getVal('[id="rxSP-'+prefix+'.004.01"] .rt-sr-w') + 'mm');
      }
      if(getVal('[id="rxSP-'+prefix+'.004.02"] .rt-sr-w')) {
        tps.push(getVal('[id="rxSP-'+prefix+'.004.02"] .rt-sr-w') + 'mm');
      }
      desc += '较大：' + tps.join(' x ');
      tempArr.push(desc);
    }
    if(tempArr.length) {
      descList.push(tempArr.join('，'));
    }
    var radioObj = {
      '005': '形态：',
      '006': '方向：',
      '007': '边界：',
      '008': '边缘：',
      '009': '内部回声：',
      '010': '后方回声：',
      '011': '钙化灶：',
      '012': '血流：',
    }
    desc = '';
    tempArr = [];
    for(var radioKey in radioObj) {
      if(radioKey === '009' || radioKey === '011') {
        if(tempArr.length) {
          descList.push(tempArr.join('，'));
        }
        desc = '';
        tempArr = [];
      }
      // 形态
      if(getVal('[name="RG-'+prefix+'.'+radioKey+'.01"]:checked')) {
        desc = radioObj[radioKey] + getVal('[name="RG-'+prefix+'.'+radioKey+'.01"]:checked');
        tempArr.push(desc);
      } 
      if(radioKey === '012') {
        if(tempArr.length) {
          descList.push(tempArr.join('，'));
        }
      }
    }

    // 其他：
    if(getVal('[id="rxSP-'+prefix+'.013.01"] .rt-sr-w')) {
      desc = '其他：' + getVal('[id="rxSP-'+prefix+'.013.01"] .rt-sr-w');
      descList.push(desc);
    } 

    // 是否结案
    // if(getVal('[name="RG-'+prefix+'.016.01"]:checked')) {
    //   desc += '是否结案：' + getVal('[name="RG-'+prefix+'.016.01"]:checked') + '。';
    // }

    if(descList.length) {
      descriptArr.push(key + '：\n' + descList.join('。\n'));
    }
    

    // 超声提示
    // BI-RADS分级：
    if(getVal('[name="RG-'+prefix+'.014.01"]:checked')) {
      let levelText = getVal('[name="RG-'+prefix+'.014.01"]:checked');
      impressObj[key].level = '声像符合超声BI-RADS ' + levelText + '。\n';
    } 

    // 腋窝淋巴结
    if(getVal('[name="RG-'+prefix+'.017.01"]:checked')) {
      impressObj[key].ywlbj = getVal('[name="RG-'+prefix+'.017.01"]:checked') + '。\n';
    } 

    // 建议
    for(var i = 1; i <= 3; i++) {
      var text = getVal('[id="rxSP-'+prefix+'.015.01-cItem--'+i+'"]:checked');
      if(text && adviseArr.indexOf(text) === -1) {
        adviseArr.push(text);
      }
    }
  }
  var arr = ['level', 'ywlbj'];
  for(var i = 0; i < arr.length; i++) {
    var iKey = arr[i];
    if(iKey === 'ywlbj' && impressObj['左乳'][iKey] && impressObj['左乳'][iKey] === impressObj['右乳'][iKey]) {
      impressArr.push('双侧腋窝' + impressObj['左乳'][iKey]);
      continue;
    }
    for(var key in impressObj) {
      if(!impressObj[key][iKey]) {
        continue
      }
      impressArr.push((iKey === 'ywlbj' ? impressObj[key]['ybName'] : key) + impressObj[key][iKey]);
    }
  }
  if(adviseArr.length) {
    if(adviseArr.indexOf('乳腺X线检查') > -1 || adviseArr.indexOf('活检') > -1) {
      impressArr.push('建议结合其他影像学检查或进一步检查。');
    } else {
      impressArr.push('建议结合临床和定期检查。');
    }
  }
  if(rtStructure) {
    rtStructure.description = descriptArr.join('\n\n');
    rtStructure.impression = impressArr.join('');
  }
}

function getVal(selector) {
  var dom = curElem.find(selector);
  var value = '';
  if(dom.length) {
    value = dom.val();
  }
  return value;
}

function changeNext() {
  var nextDom = curElem.find('[data-next]');
  nextDom.each(function(i, dom) {
    var nextText = $(dom).attr('data-next');
    if($(dom).is(":checked")) {
      $("." + nextText).show();
    } else {
      $("." + nextText).hide();
    }
  })
}

// 判断囊肿和实物肿块的情况,isFirstLoad初始加载
function changeSpecialHandler(vm, isFirstLoad, from) {
  var that = $(vm);
  var part = that.closest('.part');
  var key = part.attr('data-key');
  var isNz = that.attr('name').includes('.001.01');  //true囊肿
  var isSw = that.attr('name').includes('.002.01');  //true实物肿块
  var swDisabled = part.find('.swDisabled');
  var nzDisabled = part.find('.nzDisabled');
  var partNz = part.find('[name="RG-'+key+'.001.01"]:checked').val();
  var partSw = part.find('[name="RG-'+key+'.002.01"]:checked').val();
  let nzCheckbox = part.find('.nzCheckbox input[type="checkbox"]');
  let nzChild = part.find('.nzCheckbox .w-con');
  if(isNz || isSw) {
    if(partNz === '无' && partSw === '无' && !isFirstLoad) {
      part.find('[name="RG-'+key+'.014.01"][value="1类"]').prop('checked', true);  //默认1类
      part.find('[name="RG-'+key+'.017.01"][value="未见明显肿大淋巴结声像"]').prop('checked', true);  //默认未见明显肿大淋巴结声像
      part.find('[id="rxSP-'+key+'.015.01"] .rt-sr-w').prop('checked', false);  //取消其他建议
      part.find('[id="rxSP-'+key+'.015.01"] .rt-sr-w[id="rxSP-'+key+'.015.01-cItem--1"]').prop('checked', true); //默认定期检查
      
    }
    if(isSw) {
      swDisabled.find('.rt-sr-w').each(function(i, dom) {
        if($(dom).hasClass('rt-sr-r') || $(dom).hasClass('rt-sr-ck')) {
          $(dom).prop('checked', false);
        } else {
          $(dom).val('');
        }
        $(dom).attr('disabled', that.val() === '无' || !that.is(':checked'))
      })
      var xxArea = key === '0001' ? '.l-xxArea' : '.r-xxArea';
      var szArea = key === '0001' ? '.l-szArea' : '.r-szArea';
      part.find(xxArea + ' .rt-sr-w').attr('disabled', true);
      part.find(szArea + ' .rt-sr-w').attr('disabled', true);
    }
    //需求6279-1
    if(partSw === '有' && !isFirstLoad && from === 'sw') {
      part.find('.sw-r').prop('checked', true); 
      part.find('.sw-click').click(); 
      part.find('.sw-ck').parent('label').siblings().find('.rt-sr-w').prop('checked', false); 
      part.find('.sw-ck').prop('checked', true);
    }
    if(isNz) {
      //需求7570
      nzCheckbox.each(function(i,dom) {
        if(that.val() === '无' && that.is(':checked') === true) {
          if($(dom).val() !== '无') {
            $(dom).prop('checked', false);
            nzChild.each(function(i,child) {
              $(child).find('.rt-sr-ck').prop('disabled', true);
              $(child).find('.rt-sr-ck').prop('checked', false);
              $(child).find('.rt-sr-r').prop('disabled', true);
              $(child).find('.rt-sr-r').prop('checked', false);
              $(child).find('.rt-sr-t').prop('disabled', true);
              $(child).find('.rt-sr-t').val('');
            })
          }
        }else if(that.val() !== '无'){
          $(dom).val() === '无' ? $(dom).prop('checked', false) : '';
          if(that.val() === '复杂囊肿') {
            if(that.is(':checked')===true) {
              part.find('.class3').prop('checked',true);
              part.find('.checkedNz').prop('checked',true);
            }else if($(dom).val() === '单纯囊肿' && $(dom).is(':checked') === true) {
              part.find('.class2').prop('checked',true);
            }
          }else if(that.val() === '单纯囊肿' && that.is(':checked')===true) {
            if($(dom).val() === '复杂囊肿' && $(dom).is(':checked') === false && !partSw){
              part.find('.class2').prop('checked',true);
            }
            part.find('.checkedNz').prop('checked',true);
          }
        }
      })
      let filterCheckNZ = nzCheckbox.filter((i,dom) => $(dom).is(':checked') === true && $(dom).val() !== '无');
      nzDisabled.find('.rt-sr-w').each(function(i, dom) {
        if(partNz === '无' || !filterCheckNZ.length) {
          if($(dom).hasClass('rt-sr-r') || $(dom).hasClass('rt-sr-ck')) {
            $(dom).prop('checked', false);
          } else {
            $(dom).val('');
          }
        }
        $(dom).attr('disabled', that.val() === '无' || !filterCheckNZ.length)
      })
    }
  }
}