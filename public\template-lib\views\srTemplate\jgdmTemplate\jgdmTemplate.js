$(function() {
  window.initHtmlScript = initHtmlScript;
})
var mmUnit = 'mm', perUnit = '%',cmDevSUnit = 'cm/s', mmHgUnit = 'mmHg',sqcmUnit = 'cm^2', yearUnit = '年', bDevtUnit = '包/天';
var inpEventList = [
  { num: '24', fun: gdmjcInpEvent },
  { num: '31', fun: calFMD },
  { num: '40', fun: calNMD },
];
var rtStructure = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  // 初始化加载页面的所有tab内容
  // initTabContentHandler();
  rtStructure.diffConfig = {
    nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
    noUploadPdf: true,  //确认报告时不上传pdf
  }
  keydown_to_tab('jgdm1');
  // 循环输入框事件
  for(let item of inpEventList) {
    $(`#jgdm-rt-${item.num}`).on('input',function() {
      item.fun();
    });
  }

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });
}
// 获取值
function getIntVal(num) {
  let val = '';
  val = parseFloat($(`#jgdm-rt-${num}`).val());
  return val;
}
// 相除
function divideToRes(num1,num2) {
  let result = (num1 / num2).toFixed(2);
  return result;
}
// 肱动脉基础状态-内径
function gdmjcInpEvent() {
  calFMD();
  calNMD();
}
// FMD = （最大内径 - 内径（肱动脉基础状态）） / 最大内径
function calFMD() {
  let gdmjcVal = getIntVal(24);
  let maxNjVal = getIntVal(31);
  let xjVal = maxNjVal - gdmjcVal;
  let fmdVal = '';
  $('#jgdm-rt-37').val('');
  if(gdmjcVal && maxNjVal) {
    fmdVal = divideToRes(xjVal,maxNjVal);
    $('#jgdm-rt-37').val(fmdVal);
  }
}
// NMD = （内径(NTG后肱动脉状态) - 内径（肱动脉基础状态）） / 内径(NTG后肱动脉状态)
function calNMD() {
  let gdmjcVal = getIntVal(24);
  let ntgNjVal = getIntVal(40);
  let xjVal = ntgNjVal - gdmjcVal;
  let fmdVal = '';
  $('#jgdm-rt-46').val('');
  if(gdmjcVal && ntgNjVal) {
    fmdVal = divideToRes(xjVal,ntgNjVal);
    $('#jgdm-rt-46').val(fmdVal);
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
// 获取描述
function getDescription() {
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let description = '';
  let blockStrList1 = [];
  let yearUnitStrList = ['烟','酒','DM','血脂','HT'], mmUnitStrList = ['内径','最大内径','内径变化值','内膜','B-IMT','分级'],cmDevSUnitStrList = ['流速','最高流速','级流速'],perUnitStrList = ['FMD','NMD'];
  $('#jgdm1 .rt-sr-w:not([pid])').each(function(pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    if(curKeyValData[pid]) {
      let parData = curKeyValData[pid];
      let parVal = parData.val;
      let childD1 = parData.child || [];
      if(pid === 'jgdm-rt-22' || pid === 'jgdm-rt-29' || pid === 'jgdm-rt-38' || pid === 'jgdm-rt-47') {
        let calStrList = [];
        childD1.map(function(cItem1) {
          let childD2 = cItem1.child || [];
          childD2.map(function(cItem2) {
            if(mmUnitStrList.includes(cItem1.name)) {
              calStrList.push(cItem1.name + cItem2.val + mmUnit);
            }else if(cmDevSUnitStrList.includes(cItem1.name)) {
              calStrList.push(cItem1.name + cItem2.val + cmDevSUnit);
            }else if(perUnitStrList.includes(cItem1.name)) {
              calStrList.push(cItem1.name + cItem2.val + perUnit);
            }
          })
        })
        calStrList.length ? blockStrList1.push(parVal + calStrList.join(',')) : '';
      }else {
        let str = '';
        childD1.map(function(cItem1) {
          if(yearUnitStrList.includes(parVal)) {
            if(childD1.length > 1) {
              if(cItem1.id === 'jgdm-rt-2') {
                str += parData.val + cItem1.val + yearUnit + '，';
              }else if(cItem1.id === 'jgdm-rt-3') {
                str += cItem1.val + bDevtUnit;
              }else {
                str = parData.val + cItem1.val + yearUnit;
              }
            }else {
              if(cItem1.id === 'jgdm-rt-2') {
                str += parData.val + cItem1.val + yearUnit;
              }else if(cItem1.id === 'jgdm-rt-3'){
                str += cItem1.val + bDevtUnit;
              }else {
                str = parData.val + cItem1.val + yearUnit;
              }
            }
          }else {
            str = parData.val + cItem1.val;
          }
        })
        str ? blockStrList1.push(str) : '';
      }
    }
  });
  description = blockStrList1.length ? blockStrList1.join('，') + '。': '';
  return description;
}

function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = getDescription();
  // rtStructure.impression = '';
  // rtStructure.recommendation = '';
  // console.log(rtStructure);
}