$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);
  initC();

  $('#ssq').click(function () {
    cdfiClick('ssq');
  })
  $('#szq').click(function () {
    cdfiClick('szq');
  })
  $('#sq').click(function () {
    cdfiClick('sq');
  })


  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }

  keydown_to_tab('fjg1');

  // 收缩期、舒张期、双期
  $('.block-ck3').click(function (e) {
    e.preventDefault();
  })
  $('.ck-inp3').click(function (e) {
    e.stopPropagation();
  })

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });



  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}

//初始化CDFI可在该处探及
function initC() {
  var ssqChecked = $('#fjg-rt-23').prop("checked");
  var szqChecked = $('#fjg-rt-24').prop("checked");
  var sqChecked = $('#fjg-rt-25').prop("checked");
  if (ssqChecked) {
    $('#ssq').parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-szq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  } else if (szqChecked) {
    $('#szq').parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-ssq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  } else if (sqChecked) {
    $('#sq').parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-ssq').attr("style", "display:none;");
    $('#dpl-szq').attr("style", "display:none;");
  } else {
    $('#ssq').parent().attr('style', 'background: #E4E7ED;')
    $('#dpl-szq').attr("style", "display:none;");
    $('#dpl-sq').attr("style", "display:none;");
  }
}
function cdfiClick(ele) {
  $('#ssq').parent().attr('style', 'background:' + (ele === 'ssq' ? '#E4E7ED;' : 'unset;'))
  $('#szq').parent().attr('style', 'background:' + (ele === 'szq' ? '#E4E7ED;' : 'unset;'))
  $('#sq').parent().attr('style', 'background:' + (ele === 'sq' ? '#E4E7ED;' : 'unset;'))
  $('#dpl-ssq').attr('style', 'display:' + (ele === 'ssq' ? 'unset;' : 'none;'));
  $('#dpl-szq').attr('style', 'display:' + (ele === 'szq' ? 'unset;' : 'none;'));
  $('#dpl-sq').attr('style', 'display:' + (ele === 'sq' ? 'unset;' : 'none;'));
}


/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }

  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '';
  let strList = [];
  let fjgZcList = [], fjgNdList = [], dplCheckList = [], dplTj = [], flsList = [];
  var regu = "^[ ]+$";
  var re = new RegExp(regu);

  //房间隔正常
  curKeyValData['fjg-rt-1'] ? strList.push(curKeyValData['fjg-rt-1'].val) : '';
  // 房间隔回声中断
  $('#hszd .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let hszdStr = '';
    let curPid = curKeyValData[pid];
    var childStr = [], wyList = [];
    var handler = function (list) {
      for (var l = 0; l < list.length; l++) {
        if (list[l].id !== 'fjg-rt-8' && list[l].id !== 'fjg-rt-11') {
          wyList.push(list[l].val)
        }
        if (list[l].id === 'fjg-rt-8') {
          let dxc = $('#fjg-rt-9').val()
          let dxk = $('#fjg-rt-10').val()
          // childStr.push(list);
          if (!re.test(dxc) || !re.test(dxk)) {
            hszdStr = '大小' + (!re.test(dxc) ? $('#fjg-rt-9').val() : '0') + 'mm * ' +
              (!re.test(dxk) ? $('#fjg-rt-10').val() : '0') + 'mm'
          }
        }
        if (list[l].id === 'fjg-rt-11') {
          hszdStr = list[l].val;
        }
      }
      if (wyList && wyList.length && hszdStr !== '') {
        childStr.push('有回声中断，位于' + wyList.join('、'))
        childStr.push(hszdStr);
      } else if (wyList && wyList.length) {
        childStr.push('有回声中断，位于' + wyList.join('、'))
      } else if (hszdStr !== '') {
        childStr.push('有中断回声，' + hszdStr);
      }
      return childStr
    }
    if (curPid) {
      if (curPid.child && curPid.child.length > 0) {
        let childList = curPid.child;
        fjgZcList = handler(childList)
      } else {
        fjgZcList.push(curPid.val)
      }
    }
  })
  str = fjgZcList.join('，');
  str ? strList.push(str) : '';
  str = '';
  // 房间隔xxx完整
  curKeyValData['fjg-rt-12'] ? strList.push(curKeyValData['fjg-rt-12'].val) : '';
  curKeyValData['fjg-rt-13'] ? strList.push(curKeyValData['fjg-rt-13'].val) : '';
  $('#zdnd .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    var ndList = [];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length) {
        for (var i = 0; i < child.length; i++) {
          if (child[i].id !== 'fjg-rt-18' && child[i].id !== 'fjg-rt-19') {
            ndList.push(child[i].val)
          }
          if (child[i].id === 'fjg-rt-18') {
            ndList.push('膨出' + child[i].val + 'mm')
          }
          if (child[i].id === 'fjg-rt-19') {
            ndList.push('基底宽约为' + child[i].val + 'mm')
          }
        }
        fjgNdList.push(curPid.val + '，' + ndList.join('，'))
      } else {
        fjgNdList.push(curPid.val)
      }
    }
  })
  str = fjgNdList.join('，');
  str ? strList.push(str) : '';
  str = '';
  // 房间隔血栓附着
  $('#xsfz .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      if (curPid.child && curPid.child.length > 0) {
        let dxc = $('#fjg-rt-21').val()
        let dxk = $('#fjg-rt-22').val()
        if (!re.test(dxc) || !re.test(dxk)) {
          str = curPid.val + '，大小' + (!re.test(dxc) ? $('#fjg-rt-21').val() : '0') + 'mm * ' +
            (!re.test(dxk) ? $('#fjg-rt-22').val() : '0') + 'mm'
        }
      } else {
        str = curPid.val;
      }
    }
  })
  str ? strList.push(str) : '';
  str = '';
  let tjPid = ['fjg-rt-23', 'fjg-rt-24', 'fjg-rt-25']
  let tjId = ['fjg-rt-30', 'fjg-rt-31', 'fjg-rt-36', 'fjg-rt-37', 'fjg-rt-42', 'fjg-rt-43']
  // 房间隔多普勒检查
  $('#dplCheck').find('.rt-sr-w[pid=fjg-rt-50]').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    var commonTj = function (list, num) {
      let child = list.child;
      let cdfiTjList = [];
      if (child && child.length) {
        if (tjId.indexOf(child[0].id) === -1) {
          let flChild = child[0].child;
          if (flChild && flChild.length) {
            cdfiTjList.push(list.val + flChild[0].val + '分流')
          }
        }
        if (tjId.indexOf(child[0].id) !== -1) {
          cdfiTjList.push(list.val + child[0].val)
        }
      } else {
        cdfiTjList.push(list.val)
      }
      let tjStr = cdfiTjList.join('，')
      return tjStr
    }
    if (curPid) {
      if (tjPid.indexOf(curPid.id) !== -1) {
        dplTj.push(commonTj(curPid))
      }
      if (curPid.id === "fjg-rt-44") {
        let flsChild = curPid.child;
        if (flsChild && flsChild.length) {
          for (var s = 0; s < flsChild.length; s++) {
            if (flsChild[s].id === 'fjg-rt-45') {
              flsList.push('宽' + flsChild[s].val + 'mm')
            }
            if (flsChild[s].id === 'fjg-rt-46') {
              flsList.push('CW测得Vmax为' + flsChild[s].val + 'm/s')
            }
            if (flsChild[s].id === 'fjg-rt-47') {
              flsList.push('PG为' + flsChild[s].val + 'mmHg')
            }
            if (flsChild[s].id === 'fjg-rt-48') {
              flsList.push('MG为' + flsChild[s].val + 'mmHg')
            }
          }
          dplCheckList.push(curPid.val + flsList.join('，'))
        } else {
          dplCheckList.push(curPid.val)
        }
      }
      if (curPid.id === "fjg-rt-49") {
        dplCheckList.push(curPid.val)
      }
    }
  })

  dplTj && dplTj.length > 0 ? dplCheckList.unshift('CDFI可在该处探及，' + dplTj.join('，')) : ''
  str = dplCheckList.join('，');
  str ? strList.push('多普勒检查，' + str) : '';
  str = '';
  rtStructure.description = strList ? '房间隔：' + strList.join('，') : '';
}