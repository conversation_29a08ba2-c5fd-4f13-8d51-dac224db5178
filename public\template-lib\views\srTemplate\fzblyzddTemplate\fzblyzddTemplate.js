$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#fzblyzdd1', 'edit');
  // initHtmlScript('#fzblyzdd1', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系

function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#fzblyzdd1'),  //转成pdf的区域，默认是整个页面
      asyncAjax: true
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '', //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '', //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '', //审核医生
    affirmReporterNo: '', //审核医生流水号
    affirmDate: '', //审核日期
    reDiagReporter: '', //复诊报告医生
    reDiagReporterNo: '', //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initPage(type) {
  if (type === 'edit') {
    showPreview();
  }
  if (type === 'view') {
    showPreview();
  }
}
// 回显数据
function showPreview() {
  new Vue({
    el: '#fzblyzdd1',
    data() {
      return {
        publicInfo: publicInfo,
        orgExamInfo: {},
        examRecord: '', // 肉眼所见
        orderDate: '', // 申请时间
        candleId: '', // 蜡块号
        marker: '', // 标记物
        tumorProp: '', // 肿瘤比例
        normalExamRpt: {}, // 常规报告
        extendExamRptList: [], // 补充报告列表
        printTime: '',
      }
    },
    created() {

    },
    mounted() {
      let { patLocalId, outterGmId, examNo } = publicInfo;
      if (!outterGmId) {
        finish();
        return;
      }
      let orgExamInfo = getOrgExamInfo(outterGmId, examNo);
      this.orgExamInfo = orgExamInfo;
      this.examRecord = orgExamInfo.examRecord || '';

      // 测试内容
      // for (let i = 1; i <= 1000; i++) {
      //   this.examRecord += '肉眼所见';
      // }

      let candleDto = orgExamInfo.candleDto || {};
      this.orderDate = candleDto.orderDate || '';
      this.candleId = candleDto.candleId || '';
      let orderList = candleDto.orderList || [];
      let markerList = [];
      let tumorPropList = [];
      orderList.forEach(ord => {
        ord.marker && markerList.push(ord.marker);
        ord.tumorProp && tumorPropList.push(ord.tumorProp);
      });
      this.marker = markerList.join('、');
      this.tumorProp = tumorPropList.join('、');
      let examRptDtoList = orgExamInfo.examRptDtoList || [];
      let normalExamRpt = examRptDtoList.find(rpt => {
        return rpt.rptType === '0';
      });
      this.normalExamRpt = normalExamRpt || {};
      let extendExamRptList = examRptDtoList.filter(rpt => {
        return rpt.rptType === '3';
      });
      this.extendExamRptList = extendExamRptList;
      this.printTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

      // 原病理号条形码
      outterGmId && JsBarcode('#outterGmId-code', outterGmId, {
        font: '微软雅黑',
        textMargin: 0,
        width: 1,
        height: 30,
        margin: 0,
        displayValue: false // 不需要展示文本
      });
      // 病人信息条形码
      JsBarcode('#patLocalId-code', patLocalId, {
        font: '微软雅黑',
        textMargin: 0,
        width: 1,
        height: 30,
        margin: 0,
        displayValue: false // 不需要展示文本
      });

      this.$nextTick(() => {
        finish();
      });
    },
    methods: {

    },
  });
}

function finish() {
  rtStructure.diffConfig.pdfContainer = document.querySelector('#fzblyzdd1');
  window.postMessage({
    message: 'finishAsyncAjax',
    data: {}
  }, '*');
}

// 获取分子医嘱单相关信息
function getOrgExamInfo(outterGmId, examNo) {
  var orgExamInfo = {};
  var params = {
    outterGmId,
    examNo
  };
  fetchAjax({
    url: api.getOrgExamInfo,
    data: JSON.stringify(params),
    async: false,
    successFn: function (res) {
      if (res.status !== '0') {
        return;
      }
      orgExamInfo = res.result || {};
    }
  });
  return orgExamInfo;
}