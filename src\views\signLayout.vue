<template>
  <div class="sign-layout-wrap" :class="signPageMode === '2' && 'patient-mode'" v-loading="loading">
    <div class="sr-wrap">
      <!-- 结构化页面 显示对应知情同意书 -->
      <!-- 预览模式界面，发起端显示，签署端隐藏 -->
      <iframe
        class="top-iframe"
        frameborder="0"
        name="srIframe1"
        :src="rptIframeSrc1"
      ></iframe>
      <!-- 编辑模式界面，发起端隐藏，签署端显示 -->
      <iframe
        class="bottom-iframe"
        frameborder="0"
        name="srIframe2"
        :src="rptIframeSrc2"
      ></iframe>
    </div>
    <div class="control-wrap">
      <div class="side-wrap patient-side-wrap" v-if="signPageMode === '2'">
        <!-- 实行签署端显示的控件 -->
        <div>
          <template v-if="signPannelType === 38 || !signPannelType">
            <el-button type="primary" icon="el-icon-setting" circle title="设置" @click="showSetting"></el-button>
          </template>
          <el-button type="danger"  v-if="signPannelType === 40" @click="patientSignStop">停止签署</el-button>
        </div>
        <div class="ctrl-btns" v-loading="isWaitingSign" element-loading-text="签署中请稍等">
          <el-button type="primary" @click="patientSignStart" :disabled="!curPatternId" v-if="signPannelType === 40">开始签署</el-button>
          <el-button type="primary" @click="patientClearSign" :disabled="!curPatternId">重签</el-button>
          <el-button type="success" @click="patientSaveSign" :disabled="!curPatternId">提交</el-button>
        </div>
      </div>
      <div class="side-wrap doctor-side-wrap" v-else>
        <!-- 发起签署端显示的控件 -->
        <div class="pattern-list">
          <!-- 模板列表 -->
          <template v-for="(pattern, idx) in patternList">
            <el-button
              :key="pattern.patternId"
              :disabled="isWaitingSign"
              @click="handleLoadSrDoctor(pattern)"
            >{{ pattern.showTitle || pattern.patternName }}</el-button>
          </template>
        </div>
        <div class="ctrl-btns" v-loading="isWaitingSign" element-loading-text="签署中请稍等">
          <template>
            <el-button type="success" @click="doctorRequireSign" :disabled="!curPatternId">发起签署</el-button>
            <el-button @click="doctorReloadSign" :disabled="!curPatternId">刷新</el-button>
            <el-button type="primary" @click="doctorClearSign" :disabled="!curPatternId">重新签署</el-button>
          </template>
        </div>
      </div>
    </div>
    <!-- 设置 -->
    <el-dialog
      title="设置"
      custom-class="sign-layout-setting-dialog"
      width="360px"
      :visible.sync="isShowSettingDialog"
    >
      <div>
        <el-form label-suffix=":" label-width="90px" :disabled="!!curPatternId">
          <el-form-item label="设备编码">
            <el-input v-model="clientId"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="isShowSettingDialog = false">取消</el-button>
        <el-button type="primary" @click="saveLocalSetting" :disabled="!!curPatternId">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/config/api.js';
import { request } from '@/utils/common.js';
import dayjs from 'dayjs';
import * as constant from'@/config/constant.js'
import {getOrSetCurReportLocal, initLocalParams} from '@/common/encryptCommonFun.js';

export default {
  name: "signLayout",
  components: {},
  data() {
    return {
      isDevelopment: process.env.NODE_ENV === 'development',
      loading: false,
      signPageMode: '1', // 1发起端 2签署端
      localQueryParams: {},
      rptIframeSrc1: '', // 结构化页面地址
      rptIframeSrc2: '',
      clientId: '', // 连接sse的唯一识别码
      patternList: [], // 模板列表
      curPatternId: '', // 当前模板id
      curPatDocId: '', // 当前文档id
      curOptId: '', // 当前操作人staffNo
      curBusId: '', // 当前业务id
      isWaitingSign: false,
      isShowSettingDialog: false,
      sseInst: null, // sse连接实例
      
      // 签署方案 38=手写签名板方案 40=BJCA(信手书)方案
      // 手写签名板方案区分发起端、签署端
      // 发起端开发测试地址示例 /sreport/#/signLayout?busType=1&busId=**********
      // 手写签名板方案签署端设备打开地址 /sreport/#/signLayout?signPageMode=2
      // BJCA方案开始签署时发起端变为签署端模式，签署人看到的是推送共享的当前画面
      signPannelType: null,
      waitSignDataTT: null, // 轮询获取BJCA签名数据
      patientToSignDocId: '', // 医生签署之后的文档id
      signPlain: '', // 医生签署之后的原文
      curExamInfo: {}, // 当前检查信息
      hostInfo: {}, // PACS传进来的客户端信息
      targetDeviceCode: undefined,
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo || {};
    },
  },
  watch: {
    
  },
  created() {
    this.initPage();
  },
  mounted() {
    
  },
  activated() {

  },
  deactivated() {
    this.unloadPage();
  },
  destroyed() {
    this.unloadPage();
  },
  methods: {
    initPage() {
      initLocalParams(this); // 把参数放到localQueryParams
      this.signPageMode = this.localQueryParams.signPageMode; // 确定哪个端
      this.handleClientId();

      if (this.signPageMode === '2') {
        // 处理签署端初始数据
      } else {
        // 处理发起端初始数据
        this.curOptId = this.isDevelopment ? (this.userInfo.staffNo || '0000') : this.userInfo.staffNo;
        this.curBusId = this.localQueryParams.busId;
        this.dictPatternInfoListPatternsByBus();
        this.loadInitPattern();
      }

      this.sseConnet();
      window.top.addEventListener('message', this.handleMessage);
    },
    unloadPage() {
      window.top.removeEventListener('message', this.handleMessage);
    },
    // 显示设置
    showSetting() {
      this.isShowSettingDialog = true;
    },
    // 保存设置
    saveLocalSetting() {
      if (this.sseInst) {
        this.sseInst.close();
        this.sseInst = null;
      }
      window.localStorage.setItem(constant.SIGN_APP_CLIENT_ID, this.clientId);
      this.sseConnet();
      this.isShowSettingDialog = false;
    },
    // 处理sse clientId
    handleClientId() {
      if (this.signPageMode === '2') {
        // 签署端读取本地
        this.clientId = this.isDevelopment ? 'APP_69375063' : this.getAppClientId();
      } else {
        // 发起端由pacs传进来
        let hostInfo = this.localQueryParams.hostInfo || {};
        this.hostInfo = hostInfo;
        let deviceCode = hostInfo.deviceCode || '';
        this.clientId = this.isDevelopment ? 'TEST_CLIENT_ID' : deviceCode;
        console.log('发起端设备编码', this.clientId);
      }
    },
    // 连接sse
    sseConnet() {
      if (this.sseInst) {
        return;
      }
      if (!this.clientId) {
        this.$message.error('没有设备编码');
        return;
      }
      let sseInst = new EventSource(`${api.sseConnet.replace('{deviceId}', this.clientId)}`);
      sseInst.addEventListener('open', e => {
        this.sseInst = sseInst;
      });
      sseInst.addEventListener('message', e => {
        if (!e.data) {
          return;
        }
        try {
          let d = JSON.parse(e.data);
          console.log(d);
          let {actType, deviceCode, patternId, docId, busId, token, optId, targetDeviceCode} = d;
          switch (actType) {
            case '1': // 发起端 发起签署
              if (this.signPageMode === '2') {
                // 由签署端处理
                this.curBusId = busId;
                this.curOptId = optId;
                this.targetDeviceCode = targetDeviceCode;
                this.$store.commit('setToken', token);
                this.handleLoadSrPatient({patDocId: docId, patternId});
              }
              break;
            case '2': // 签署端 签署完成
              if (this.signPageMode !== '2') {
                // 由发起端处理
                this.$message.success('签署完成');
                this.isWaitingSign = false;
                this.doctorReloadSign();
              }
              break;
          }
        } catch (err) {
          console.log(err);
        }
      });
      sseInst.addEventListener('error', e => {
        console.log('sse error', e);
        this.sseInst = null;
      });
    },
    // 发起端 加载外部传参的模板
    loadInitPattern() {
      if (!this.localQueryParams.patternId) {
        return;
      }
      this.handleLoadSrDoctor(this.localQueryParams);
    },
    // 获取模板列表
    async dictPatternInfoListPatternsByBus() {
      let params = {
        busId: this.curBusId || '',
        busType: this.localQueryParams.busType || '',
        patternType: '3',
      }
      let res = await request(api.dictPatternInfoListPatternsByBus, 'post', params);
      if (!res) {
        this.$message.error('获取模板列表出错');
        return;
      }
      if (res.status !== '0') {
        this.$message.error(res.message);
        return;
      }
      let list = res.result || [];
      this.patternList = list;
    },
    // 发起端加载模板
    async handleLoadSrDoctor(pattern) {
      if (!pattern.patternId) {
        this.$message.error('没有模板id');
        return;
      }
      if (this.signPageMode !== '2' && this.curPatternId && this.curPatternId !== pattern.patternId) {
        let confirm = await this.$confirm('是否切换模板？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }).catch(() => {});
        if (!confirm) {
          return;
        }
      }
      this.loadSrDocPage(pattern, '1', '1');
      this.loadSrDocPage(pattern, '0', '2');
    },
    // 签署端加载模板
    handleLoadSrPatient(pattern) {
      this.loadSrDocPage(pattern, '0', '2');
    },
    // 加载结构化页面
    async loadSrDocPage(pattern, entryType, iframeNo = '1') {
      let srData = {
        entryType, // 0编辑 1预览 2打开痕迹 4打印
        source: '1',
        viewId: '1',
        busType: '1',
        optId: this.curOptId,
        busId: this.curBusId,
        patternId: pattern.patternId,
        patDocId: pattern.patDocId,
      };
      this.curPatternId = pattern.patternId;
      this.curPatDocId = pattern.patDocId;
      let curRptKey = `${srData.busType || '1'}_${srData.busId || srData.examNo||''}_${srData.reportNo||''}_${this.curReportType}_0_${entryType}`;
      let localData = window.localStorage.getItem(constant.SR_QUERY_PARAMS);
      if (!localData) {
        localData = {};
      } else {
        localData = JSON.parse(localData);
      }
      localData[curRptKey] = srData;
      window.localStorage.setItem(constant.SR_QUERY_PARAMS, JSON.stringify(localData));
      let tokenParam = this.token ? `&token=${encodeURIComponent(this.token)}` : '';
      let src = `/sreport/#/srPage?frontendParam=${curRptKey}${tokenParam}&t=${Date.now()}`;
      this['rptIframeSrc' + iframeNo] = src;
      this.loading = true;
    },
    // 处理message事件
    handleMessage(e) {
      if (!e.data) {
        return;
      }
      let { message, data } = e.data;
      // 结构化模板加载完成
      if (message === 'srLoadSuccess') {
        this.handleSrDocLoaded(e);
      }
      // 结构化对外操作方法整体完成的通知回调
      if(message === 'srOuterOptListener') {
        if(data?.reqType === 'rptData') {  //来自AI助手
          return;
        }
        if(!['1', '10', '2', '20', '3', '30', '4', '5', '50'].includes(data?.type)) {  //非报告类型不读
          return;
        }
        // 打印
        if(data.type === '5') {
          return;
        }
        if(data.type !== '30' && data.submitType !== 'pdf') {
          this.finishOperaHandler(data);
        }
      }
    },
    // 结构化模板内容加载完成
    handleSrDocLoaded(e) {
      this.loading = false;
      if (e.source.name === 'srIframe2') {
        this.getSignPannelType();
      }
    },
    // 获取模板签名方案
    getSignPannelType() {
      let signPannelType = window.srIframe2.rtStructure.checkSignPannelType();
      console.log('签名方案：', signPannelType === 38 && '手写签名板' || signPannelType === 40 && 'BJCA方案' || '无');
      this.signPannelType = signPannelType;
    },
    // 发起端 发起签署
    doctorRequireSign() {
      if (!this.signPannelType) {
        this.$message.error('该模板无签名组件');
        return;
      }
      // 保存告知医生签名和签署时间
      window.srIframe2.srCommonOuterOpt(
        {
          type: '13',
          signStaffNo: this.curOptId,
          saveNewDoc: this.signPannelType === 40, // 信手书方案每次都保存为新文档
          param: {
            reportStatus: '60',
          },
        },
        (res) => {
          res && this.doctorSignAfterSave(res);
        }
      );
    },
    // 发起端 重签
    doctorClearSign() {
      if (!this.signPannelType) {
        this.$message.error('该模板无签名组件');
        return;
      }
      window.srIframe2.srCommonOuterOpt(
        {
          type: '14',
          signStaffNo: this.curOptId,
          saveNewDoc: this.signPannelType === 40,
          param: {
            reportStatus: '60',
          },
        },
        (res) => {
          res && this.doctorSignAfterSave(res);
        }
      );
    },
    // 发起端 保存成功后
    async doctorSignAfterSave(res) {
      this.patientToSignDocId = res.result;
      this.doctorReloadSign();
      if (this.signPannelType === 38) {
        // 手写签名方案
        let flag = await this.sseSendSign('1'); // 通知签署端
        if (flag) {
          this.isWaitingSign = true;
        }
      }
      if (this.signPannelType === 40) {
        // BJCA方案
        if (!window.AS_InitSign) {
          // 加载BJCA脚本
          let $script = document.createElement('script');
          $script.src = '/sreport/template-lib/plugins/ASAppComSync.js';
          document.head.appendChild($script);
          $script.onload = () => {
            this.pushWindow();
          };
        } else {
          this.pushWindow();
        }
      }
    },
    // 发起端 刷新
    doctorReloadSign() {
      this.handleLoadSrDoctor({
        patternId: this.curPatternId,
        patDocId: this.curPatDocId,
      });
    },
    // 发起端 信手书方案 推送签署窗口
    pushWindow() {
      var ret = AS_OpenWindowCopy(JSON.stringify({
        // Pos: {},
        Title: this.hostInfo.windowTitle || '知情同意书',
        // ChildrenTitle: '',
        Model: 1,
        StretchModel: 1
      }));
      if (ret != 0) {
        this.handleBjcaRetError(ret, '推送签署窗口');
        return;
      }
      this.signPageMode = '2'; // 变为签署端界面
    },
    // 签署端 信手书方案 开始签署
    async patientSignStart() {
      var ret = AS_InitSign(1); // 初始化 signType=1数据签名
      if (ret != 0) {
        this.handleBjcaRetError(ret, '签署初始化');
        return;
      }
      let businessParam = this.getSignBusinessParam();
      if (!businessParam) {
        this.$message.error('没有渠道号');
        return;
      }
      var ret = AS_SetBusinessParam(2, businessParam); // 设置业务渠道参数
      if (ret != 0) {
        this.handleBjcaRetError(ret, '设置业务渠道号');
        return;
      }
      let signPlain = await this.getSignOrgData(); // 获取数据签名原文
      if (!signPlain) {
        return;
      }
      var ret = AS_SetSignPlain(signPlain); // 设置数据签名原文
      if (ret != 0) {
        this.handleBjcaRetError(ret, '设置签名原文');
        return;
      }
      let patientData = await this.getPatientData();
      var ret = AS_SetSignerInfo(patientData.name, 1, patientData.identityCard); // 设置患者信息 传身份证号
      if (ret != 0) {
        this.handleBjcaRetError(ret, '设置患者信息');
        return;
      }
      var ret = AS_AddSignEvidenceData(); // 打开手写签名控件
      if (ret != 0) {
        this.handleBjcaRetError(ret, '打开手写签名控件');
        return;
      }

      this.isWaitingSign = true;
      this.lookupSignData(); // 轮询签名结果
    },
    // 获取结构化文档CA签名原文
    async getSignOrgData() {
      let params = {
        docId: this.patientToSignDocId,
      };
      let res = await request(api.getSignOrgData, 'post', params);
      if (!res) {
        this.$message.error('获取结构化文档CA签名原文出错');
        return;
      }
      if (res.status !== '0') {
        this.$message.error(res.message);
        return;
      }
      let {docId, signParams} = res.result || {};
      if (!signParams) {
        this.$message.error('获取结构化文档CA签名原文为空');
        return;
      }
      this.signPlain = signParams;
      return signParams;
    },
    // BJCA 轮询签名结果
    lookupSignData() {
      let times = 0;
      this.waitSignDataTT = setInterval(async () => {
        times++;
        if (times > 60) {
        // if (times > 5) {
          // 超时
          clearInterval(this.waitSignDataTT);
          this.isWaitingSign = false;
          let confirm = await this.$confirm('获取签名结果失败', '提示', {confirmButtonText: '再次获取', cancelButtonText: '关闭', type: 'warning',}).catch(() => {});
          if (confirm) {
            this.clickToGetSignBase64();
          }
          return;
        }
        let ret = AS_GetSignEvidenceData(0); // 0获取手写图片 返回值为 base64 编码，如果为空，调用 GetErrorMessage 获取错误码
        if (!ret) {
          console.log('获取签名结果失败', GetErrorMessage(ret));
          return;
        }
        // 获取成功
        clearInterval(this.waitSignDataTT);
        this.isWaitingSign = false;
        this.handleGetSignDataSuccess(ret);
      }, 1000);
    },
    // 主动获取签名图片base64
    clickToGetSignBase64() {
      let ret = AS_GetSignEvidenceData(0); // 0获取手写图片 返回值为 base64 编码，如果为空，调用 GetErrorMessage 获取错误码
      if (!ret) {
        this.handleBjcaRetError(ret, '获取签名结果');
        return;
      }
      this.handleGetSignDataSuccess(ret);
    },
    // 获取签名图base64成功
    handleGetSignDataSuccess(base64) {
      base64 = base64.replace(/[\r\n]/g, ''); // 处理字符
      base64 = 'data:image/gif;base64,' + base64;
      window.srIframe2.rtStructure.setBjcaSignimage(base64); // 显示到模板中
    },
    // 签署端 提交
    patientSaveSign() {
      window.srIframe2.srCommonOuterOpt(
        {
          type: '11',
          saveNewDoc: this.signPannelType === 40,
          param: {
            reportStatus: '60',
          },
        },
        async res => {
          if (res) {
            this.$message.success('签署成功');
            this.creatSrPdfById(res);
            if (this.signPannelType === 38) {
              let flag = await this.sseSendSign('2');
              if (flag) {
                this.patientResetPage();
              }
            }
            if (this.signPannelType === 40) {
              this.loadSrDocPage({
                patternId: this.curPatternId,
                patDocId: this.curPatDocId,
              }, '1', '1'); // 刷新预览界面
              this.saveExamDocSign(); // 信手书方案 保存
            }
          }
        }
      );
    },
    // 签署端 重签
    patientClearSign() {
      window.srIframe2.srCommonOuterOpt(
        {
          type: '12',
        }
      );
      if (this.signPannelType === 40) {
        this.patientSignStart();
      }
    },
    // 签署端 回到等待状态
    patientResetPage() {
      this.curPatternId = '';
      this.rptIframeSrc2 = '';
    },
    // 签署端 信手书方案 停止签署
    patientSignStop() {
      clearInterval(this.waitSignDataTT);
      this.isWaitingSign = false;
      this.signPageMode = '1'; // 变回发起端界面
      var ret = AS_CloseWindowCopy(); // 关闭推送窗口
      if (ret != 0) {
        this.handleBjcaRetError(ret, '关闭推送窗口');
        return;
      }
    },
    // 签署方 信手书方案 提交后保存签名数据
    async saveExamDocSign() {
      var ret = AS_GetBusinessString(); // 获取签名加密包数据 返回加密包数据,加密包格式为 json 格式
      if (!ret) {
        this.handleBjcaRetError(ret, '获取签名加密包数据');
        return;
      }
      var ret = AS_GetSignPackage(ret); // 获得服务端签名包
      if (!ret) {
        this.handleBjcaRetError(ret, '获得服务端签名包');
        return;
      }
      let params = {
        docId: this.patientToSignDocId,
        signParams: this.signPlain,
        signDataStr: ret,
        optId: this.curOptId,
        signerId: this.curExamInfo.sickId,
        signerName: this.curExamInfo.name,
      };
      let res = await request(api.saveExamDocSign, 'post', params);
      if (!res) {
        this.$message.error('保存签名数据失败');
        return;
      }
      if (res.status !== '0') {
        this.$message.error(res.message);
        return;
      }
      this.patientSignStop();
    },
    // 生成pdf
    async creatSrPdfById(res) {
      let params = {
        docId: res.result,
      };
      request(api.creatSrPdfById, 'post', params);
    },
    // 推送签署行为
    async sseSendSign(actType) {
      if (!this.clientId) {
        this.$message.error('没有设备编码');
        return;
      }
      let params = {
        actType,
        deviceCode: this.clientId,
        patternId: this.curPatternId,
        docId: this.curPatDocId,
        optId: this.curOptId,
        busId: this.curBusId,
        targetDeviceCode: this.targetDeviceCode,
      };
      let res = await request(api.sseSendSign, 'post', params);
      if (!res) {
        this.$message.error('发起签署失败');
        return;
      }
      if (res.status !== '0') {
        this.$message.error(res.message);
        return;
      }
      return true;
    },
    // 结构化报告操作完成回调
    async finishOperaHandler(res, reload) {
      
    },
    // 获取签署端的clientId
    getAppClientId() {
      let clientId = window.localStorage.getItem(constant.SIGN_APP_CLIENT_ID);
      if (!clientId) {
        // clientId = 'APP_' + (Math.random() + '').replace('.', '').padStart(8, '0').slice(-8); // 随机生成
        // window.localStorage.setItem(constant.SIGN_APP_CLIENT_ID, clientId);
      }
      return clientId;
    },
    // 处理BJCA接口错误
    handleBjcaRetError(ret, name) {
      let errMsg = name + '失败：' + GetErrorMessage(ret);
      console.log(errMsg);
      this.$message.error(errMsg);
    },
    // 获取BJCA方案模板中签名组件的渠道号
    getSignBusinessParam() {
      let businessParam = window.srIframe2.rtStructure.getSignBusinessParam();
      return businessParam || (this.isDevelopment ? '999999' : '');
    },
    // 获取结构化患者信息
    async getPatientData() {
      this.curExamInfo = window.srIframe2.rtStructure.examInfo || {};
      return {
        name: this.curExamInfo.name,
        identityCard: this.curExamInfo.identityCard,
      };
    },
  }
}
</script>

<style lang="scss">
.sign-layout-wrap {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  &.patient-mode {
    .top-iframe {
      z-index: 1;
    }
    .bottom-iframe {
      z-index: 2;
    }
  }
  * {
    box-sizing: border-box;
  }
  .sr-wrap {
    position: relative;
    height: 0;
    flex: auto;
    iframe {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
  .top-iframe {
    z-index: 2;
  }
  .bottom-iframe {
    z-index: 1;
  }
  .side-wrap {
    padding: 8px;
    display: flex;
    justify-content: space-between;
  }
  .control-wrap {
    background-color: #fff;
    border-top: 1px solid #ddd;
  }
  .pattern-list {
    flex: auto;
    overflow: auto;
  }
  .ctrl-btns {
    margin-left: 8px;
    .circular {
      width: 30px;
      height: 30px;
    }
    .el-loading-text {
      position: absolute;
      right: 30px;
      top: 50%;
      margin-top: -8px;
    }
  }
  .sign-layout-setting-dialog {
    .el-dialog__body {
      padding-top: 10px;
      padding-bottom: 0;
    }
  }
}
.pattern-list-popover {
  .pattern-item {
    cursor: pointer;
    padding: 4px 0;
    &:hover {
      color: rgb(24, 133, 242);
    }
  }
}
</style>