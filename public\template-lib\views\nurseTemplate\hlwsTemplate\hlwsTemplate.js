$(function() {
  window.initHtmlScript = initHtmlScript;
  
})
var liveConClone = null;
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var isSavedReport = false; //模版是否保存
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    layui.use('layer', function() {
      const layer = layui.layer;
      let timer_event, layer_tips, tips_show = false
      
      $('#tips').on({
          'mouseenter': function () {
              clearTimeout(timer_event)
              if(tips_show) return
              layer_tips = layer.tips(`<p id='tips-box' style="white-space:pre-line;word-wrap: break-word;">I级:皮肤发白,水肿范围的最大直径<2.5cm (1英寸)皮温凉，伴有或不伴有疼痛;
  II级:皮肤发白,水肿范围的最大处直径在2.5~15 cm (1-6英寸)，皮肤凉，伴有或不伴有疼痛;
  Ⅲ级:皮肤发白，半透明状,水肿范围的最大直径>15 cm(6 英寸），皮肤发凉,轻到中等程度的疼痛;
  Ⅳ级:皮肤发白,半透明状，皮肤紧惮，有渗出,可凹陷性水肿，皮肤变色,有t伤，肿胀，水肿范围的最小处直经>15cm(6英寸），循环障碍，中、重度程度疼痛,任何容量的血制品，刺激性,腐蚀性液体的渗出。</p>`, $(this), {
                  time:0,
                  closeBtn: 0,
                  tips:[2, '#fff'],
                  area:['360px'],
                  success: function () {
                      tips_show = true
                      $('.layui-layer-tips .layui-layer-content').css({
                        'padding': '10px',
                        'background-color': '#fff', // 设置背景色为白色
                        'color': '#000' // 设置文字颜色为黑色，确保在白色背景上可读
                      })
                      $('#tips-box').on({
                          'mouseenter': function () {
                              clearTimeout(timer_event)
                          },
                          'mouseleave': function () {
                              hideTips()
                          }
                      })
                  }
              }) 
          },
          'mouseleave': function () {
              hideTips()
          }
      })
      
      function hideTips() {
          clearTimeout(timer_event)
          timer_event = setTimeout(()=>{
              layer.close(layer_tips)
              tips_show = false
          }, 200)
      }
    })
    if(rtStructure.enterOptions.type==='view') {
      togglePageType('preview')
      initBzCloneEle()
      displayLiveContent()
    } else {
      togglePageType('edit')
      initPage()
      
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
}
function initPage(){
  initBzCloneEle()
  window.getExamImageAgentList && getExamAgentList() 
  if(window.getCommonUseList){
    getWsSite()
  }
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    curElem.find('.def-ck').click();
    // addLiveHandler();
    addLiveHandler('')
    addDictionaryHandler('','method')
    useServerTime('hlws-rt-0006-002')
  } else {
    displayLiveContent()
  }
  toNurseList()
  layInit()
  wsDetail()
  setWsDetail()
}
function layInit(){
  layui.use('laydate', function(){
    var laydate = layui.laydate;
    //执行一个laydate实例
    laydate.render({
      elem: '#hlws-rt-0001-003', //指定元素
      type:'date'
    });
    // laydate.render({
    //   elem:'#hlws-rt-0006-002',//记录时间
    //   type:'datetime'
    // })
    
  });
  bindTimeFocus('hlws-rt-0001-003',true)
  setupDateSelection('hlws-rt-0001-003')
  // setupDateSelection('hlws-rt-0006-002')
}
// 添加过字典内容
function addDictionaryHandler(oldPaneId,type) {
  var newPaneBlock = '';
  if(type === 'method'){
    newPaneBlock = $('#method');
  }
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = '';
    if(type === 'method'){
      wCon = $('#method').find("[rt-sc]");
    }
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function getWsSite(){
    let nurseList = window.getCommonUseList({name: 'NURSE_SEEP_PROCESS_MODE'});
    let html = '';
    console.log(nurseList);
    if (nurseList && nurseList.length > 0) {
      for (var t = 0; t < nurseList.length; t++) {
        html = html + `<label class="rt-sr-label a-center" for="hlws-rt-0005-${padNumberWithZeros(t+1,3)}">
        <input type="checkbox" class="rt-sr-w" name="site" value="${nurseList[t].value}" pid="hlws-rt-0005" id="hlws-rt-0005-${padNumberWithZeros(t+1,3)}"  rt-sc="pageId:hlws;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;code:3534;" >
        <span class="rt-sr-lb">${nurseList[t].value}</span>
        </label>`
      }
      $('#method').html(html);
    }
    var newPaneBlock = $('#method');
    // console.log('newPaneBlock',newPaneBlock);
    return newPaneBlock;
  
  // getCommonUseList({name: 'NURSE_ALLERGY_SYMPTOMS'})
}
function initBzCloneEle() {
  var liveCon = curElem.find('.table-content .live-con .bz-tr').clone(true);
  liveConClone = '<tr class="bz-tr rt-sr-w" id="hlws-rtlist-000" tab-target="hlws-rtlist-000" pid="hlws-rtlist-1" rt-sc="pageId:hlws1;name:外渗情况;wt:;desc:外渗情况;vt:;pvf:;">'+liveCon.html()+'</tr>';
  curElem.find('.table-content .live-con').html('');
}
// 获取对比信息
function getExamAgentList(){
  let agentRes = window.getExamImageAgentList({examNo: publicInfo.examNo});
  if(agentRes&&agentRes.length){
    $('#hlws-rt-0001-001').val(agentRes[0].agentName);
  }
}
function addLiveHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.table-content .live-con .bz-tr').length;
  var activeTab = 'hlws-rtlist-' + paneId; // 表格每列的id
  var newPaneBlock = appendLiveHtml(paneId, bzLen,oldPaneId);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '外渗情况',
      name: '外渗情况',
      pid: 'hlws-rtlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('外渗情况' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    var wCon = $('.table-content .live-con .bz-tr').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc'); // 获取rt-sc的节点
      var scArr = sc.split(';');//获取属性值
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      }; // 值放在此
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // $('.bz-list .bz-wrap').show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
function initBzTigger(newPaneBlock, oldFlag) {
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    newPaneBlock.find('.def-ck').click();
  }
}
function appendLiveHtml(paneId, bzLen,oldPaneId) {
  var reg = new RegExp('000', 'ig');
  var content = liveConClone.replace(reg, paneId); 
  $('.table-content .live-con').append(content);
  
  $('#hlws-rtlist-'+paneId+'-1').html((bzLen+1));
  // layui.use(['laydate', 'table'], function(){
  //   var laydate = layui.laydate;
  //   laydate.render({
  //     elem: '#hlws-rtlist-'+paneId+'-2',//指定元素
  //     type: 'datetime',
  //     value: oldPaneId ? '' : ''
  //   });
  // })
  // addLayDateTime('hlws-rtlist-'+paneId+'-2')
  // bindTimeFocus('hlws-rtlist-'+paneId+'-2');
  // setupDateSelection('hlws-rtlist-'+paneId+'-2')
  var newPaneBlock =  $('.table-content .live-con .bz-tr[tab-target="hlws-rtlist-'+paneId+'"]');
  return newPaneBlock;
}
function displayLiveContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
  if(allResData && allResData.length) {
    var bzData = allResData.filter(bzItem => bzItem.id === 'hlws-rtlist-1')
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('hlws-rtlist-', '');
        addLiveHandler(paneId)
      })
    }
    var siteList = allResData.filter(bzItem=>bzItem.id === 'hlws-rt-0005')
    if(siteList && siteList.length) {
      var bzList = siteList[0].child || [];
      bzList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList ? addDictionaryHandler(item.id,'method') : '';
      })
    }else{
      window.getCommonUseList ? addDictionaryHandler('','method') : '';
    }
  } else {
    curElem.find('.bz-list .bz-wrap').hide();
  }
  // 监听表单内所有input, textarea, select的input或change事件  
  $('#hlws1').on('input change blur', 'input, textarea, select', function() { 
    // console.log('input change blur', this) 
    // 向父页面发送消息
    window.parent.postMessage({
      message: 'formChangeNotice',
      data: '表单内容已修改'
    }, '*');
  });  
}
function delTrFuc(vm, paneId){
  var allSiblings = $(vm).parent().parent().siblings();
  allSiblings.each(function(index) {
      $(this).children().first().html(index + 1)
  });
  $(vm).parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
}
function toNurseList(){
  let list = window.getNurseList({deptCode: publicInfo.userInfo&&publicInfo.userInfo.param&&publicInfo.userInfo.param.deptCode || ''});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
  
  initInpAndSel('hlws-rt-0006-001', userList,optHandler);
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#hlws-rt-0006-001').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#hlws-rt-0006-001').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList,cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}
function togglePageType(type) {
  $(`[data-type]`).hide()
  $(`[data-type="${type}"]`).show()
  if (type === 'preview') {
    // $(`.live-con[data-type="edit"]`).remove()
    $('[data-key]').each(function () {
      let key = $(this).attr('data-key')
      if (idAndDomMap) {
        let result = []
        Object.keys(idAndDomMap).forEach(idKey => {
          if (idKey.startsWith(key)) {
            let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value : '';
            if (value) {
              result.push(value)
            }
          }
        })
        $(this).html(result.join(','))
        this.style.color = '#000'
      }
    })
    $('.layui-inline, .layui-form, .showInt').removeClass('showInt')
  }else{
    $(`.live-con[data-type="preview"]`).remove()
  }
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }
 function wsDetail(){
  $('tbody[data-type="edit"]')[0].addEventListener('input',setWsDetail)
  $('tbody[data-type="edit"]')[0].addEventListener('click',setWsDetail)
 }
 function setWsDetail(){
  let result = []
  $('tbody[data-type="edit"] tr').each(function(index){
    let strTemp = ''
    $(this).find('td').each(function(cIndex){
      let value = $(this).children().val()
      if(cIndex===0){
        strTemp+=`序号：${index+1},`
      }else if(cIndex===1){
        strTemp+=`记录时长 (h)：${value},`
      }else if(cIndex===2){
        strTemp+=`外渗分级：${value},`
      }else if(cIndex===3){
        strTemp+=`肿胀面积最大直径 (cm)：${value},`
      }else if(cIndex === 4){
        strTemp+=`特殊情况：${value};`
      }
    })
    result.push(strTemp)
  })
  $('#wsDetail').val(result.join('\n'))
 }