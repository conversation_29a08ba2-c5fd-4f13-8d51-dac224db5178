$(function() {
  window.initHtmlScript = initHtmlScript;
})
// 引入图标
var rightIcon1 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAjZJREFUWEftlk1rU1EQhp/ZFMGmJoUGahaNEhSE1E9w1Y24TlQodKlCf4AgiCkoCo0IQn9AQV0WumiTtbiJG0FqbEBQQk0XtZBCkzQRpJsjczlKCM3NvYKeTQIXwmXumee8854zIzj+ieP8DAGGCoRWwBgTA7L2OQectEb+DnwGCvqISCOIwQMDGGOOAfeAB8CJAYu3gOfAkoj89IsNBGCMSQDrwBVvsb29DZaXf7CykqBWi3vvksk6c3M7zM8fZ2Likk36AbghIjv9IAYC2OTvgQSHhzVmZ5sUixd8FchkyqyuRhkZSQKa/Go/CF8AK/s74DKNxidSqSn296NAG3gBFIGqhUkBGeA+EGF8vEm1uk0sdh5QJWaOKscggIdA3tv55GTUJn8L3BXYPkoFA1PAS+CaB7G727RK5ETkWe83fQGs2795hstmy1Z2TX5dwPiVwODdsG88CC1HoaAlU2Oe6j0dfgC3gVee4eJxNZXKnu63814gq0TFK0e9vmGNeUdEXnfH+gGsqYPJ50ssLMwAjwWeBjnbv2MMPAKesLhYIpfTNdZF5GZQgC/AGaant6hUTgMXBcohAVT6j6TTW2xu6hpfReRsUACVfJSxsQ7t9qhKKdAJCaDftYlEOhwc6P+OiET+P4BuotUKDeC8BP/ChGsicitoCZwfQ2277i4ilckY4+4qtgA6A5S8NuyiGVkInQXctOM/V6rLgaQLwt1I1n1unQ2lYXpAmNiBM2GYxf4mdgjwC0oxPDDRy30rAAAAAElFTkSuQmCC';
var rightIcon1_2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAATdJREFUWEftlt0NgjAURr+7gAmyACO4iQykTziQbsIILgAmLlBTqMpPC+13Nb7Y99Jzjqa3gh8vYc83xmzsXhG5s9/o9rObjTEHB3Biv0EDOPurO7jQVKAKOPvKARxFhK6QDDCwzxzADQBdgQGwv32Ftq07gO12B4CukAQwsi/LHuB8tgB0hVSAt32e24OBpqk1FaIBZvaXSw+w39eaCikAU3ub3a5MUyEKIGB/dACVpkIsgM++cAD2QqIrrAKE7AXoLh8D9HDkfyEGwGsvQDeEDGCHEl1hEWDN/jmENBXWABbtBwB0hSBArL22whJAlL22ghcg1V5TIQSQZK+pMANg7dkKPoD5xGMffRGTcgQQnHgsQMTtOAXo7b+zvK+mF4DnrfdpDO+raXUWfJpi+r0/wL/Azws8AHcPDzC4lawXAAAAAElFTkSuQmCC';
var rightIcon2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAiFJREFUWEftlj9oU1EUh7+fUSo61KJTHDKmg5ntIh3TNEMLJdJCHYoiZOhml2IimnbpLEHaSaGINUMrlSSTHTq5ZdQOdVAsVBfBWiHlyE0aeBRq3muTPhDfFsI73/fu+XOPCPlRyHz+C3TsBMzsPPAIuAdcAF4BDyT9+luaOyJgZpeAEpA6AnsmKdtVATO7CrwFbrK7C5kM1Ouwuem43yVd65qAmcWAKhBnexuSSdjagpkZWFhw3A+S+rsiYGY3DuFRajUYGoKdHZifh9nZFvO2pNcdFzCzW8Ab4AobGzAyAnt7sLgIU1OOV3fFKOl5uzkTuAjNbBR4CVykVILJSYhEYGUF0mnH+wlkJJXbwd3/gQTM7D5QBCIUizA9DX19sL4OAwMu3jcgLem9H3ggATPLAU8agfN5KBQgFoNKBfobdfYJSEr66BfuS8DMzgFPgSwHB5DNwtISJBJNeDTq4tTcDJD0NQjcr0Dzy/f3YWICVldhcBDW1qC318V4B4xK+hEU7lfgCxAllWp+8dgYLC9DT49737XYHUm/TwL3K/AZuM7wMJTLoQg8BAqNFIyPN4/+jFMQbhG2cmtmeeBx43cuB3NzzTasViEe714beosr1EHkOYnwRrFHws9ldFfSi3btGeguOJKOBFBpzIjjr2N3KblN6djnxAIuYqgLiScd4a1kHonLh2P57JdSj0R4a3m7Su9aEZ4G3Hr3VF3wTwj8ASwU8CFO2PyNAAAAAElFTkSuQmCC';
var rightIcon2_2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAK1JREFUWEftls0OgCAMg9cnV558xkQw8WA7gpmHeQVm++0HYMkfkv9vJeD/BNx9n6kTANI5SsDdfVIAjX3GpZuGgF0yNLSiNRo7JgBSvFuAYG5OwLZJGfmOgFgSgIaMch010OPdAtobivVd8BCgOmT5miZQAopAOgEzy21D2l6fDaL0UcysX+tYfhmlX8ei875N7RJlFMdeIj0Fq55kQePh7ZRAOGLwQAkoAukEDpy3biEG1hkxAAAAAElFTkSuQmCC';
var rightIcon3 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA9xJREFUWEftlm9I1XcUxj+PFkRpTAlEp2bFtpqLaGMtlKDJYi9szWYQFUwqFGw12xhDqBdro9awjSJZ7FUxTRKTMIeGxjSoWH8GQ2oQ2IZJRs6p/XOUzu84v99NQq/3jwbtRQfum3u/3/M833Oe85wrnnHoGePznEDUFXDOzQJeAjKAJKAL+ANol9QfbUsjJuCcewP4DFgDTAkCNAxUAbsktUdKJCwB51ws8C1Q4iV9+BAuXYIbN6C7G5KTISMDFi2CadPsxBDwpaSvIiERCQED/9QDLiuD8nK4fXts7pQUKC2F4mKY4hXoE0n7w5EIScA59xrQxuCgyMmBs2dh3jwoKID58yEuDrq64Px5qKiAwUEoKYH9Hu4/wCuSOkORCEfgG+BzjhyBjRth2TI4dQqmTx+b88oVyM6Gu3ehrg5WrbIzX0jaNRkC9cBKNm2Cw4fh+HHIz7d8x4Ba4B6QZuUGXuXQIdiyBdavh6NH7dxFSW9NhkAz8A4rVsDp01BTA2tsCGgEvgNaJQ05514GrtHTAwsWQFISWEXgARAvyY1HIlwLfgCK2LkTdu+GxYuhsdEH8KMPaADqAGt8ShCgWZL+niiBt4Gf6e2FJUvg+nUfvKgI1q6FzMxgec0PnjSkLEnXJkTALjnnrNcfeCRszKqq4IFVFn8i8vJg9WrIygKNFLQaKJRkGgkZkfhAHFAB5HmZ7t+H+no4ccKfiHsBjNmzobAQtm2DmTPt5G9AtqSBCYnQOZc94n5+hncBL/NIPHoEra1QWwvV1XDnDsyZAw0Nvk/A95I+miiBXOCnIJebAPu8D2QBZtXQ1+frornZB796FWJizIxelGRiDRrjtsA5Z/P7C7du+e42dy7s3WtJLkt6M6CPROA94EMgh6EhXxe2J1paYPlyr3KSjHDUBBKAXoaHITERBgb8V86Y8S+QOVrZzrkab1OaY5pzVlbChg0GWiDpx6gJBF74p7f3c3P9vh48CFu32k89gdXbAZgv26pe6a3ppUvhwgVoa4OFC+1sriTziugqECDwNVDqCc3cMDbWF5wRGh0myB07YN8+X4jt7aYBW80pkv6aKIFk4HfgBQ4cgO3b/TxmSvbStDS/NR0dcPIknhUnJMC5c74lwzFJ68YDt+8j8QFLUAnE0NQEe/bAmTNjc8bHw+bNPknzBLgJvC6pe1IEAq0wEuWAqR76+/1Xd3aCAaenQ2oqTJ36GOtXIF+SaSRkhK3A49vOOXPEYuBjIDVIVpuOFqAs1NiNvhcxgScvOufiA/+K0wP/CeylN201h3vxUyEQLcikNfA0AZ9X4H9Xgf8As3tFMKMF09cAAAAASUVORK5CYII=';
var rightIcon3_2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABA1JREFUWEftlm1IlWcYx3+X5kFCsbXU0pFMU8OmaQtGH9aHSiIZlVJfCsI50ENBITXW/NDbh5kgLfoQCq1JgUkv5koKVCIjgl6gQ0Ews4a0aVnDzEOTU3qP63ke2dTnvI0gGF3wcOA5z339/9f//l/3dQvvOeQ94/P/JGCMiRGR8UjUjVgBY8xHwDrnyQPSHIB+4AHwi/P4gVagW0Tqw5EIS8AYEw9UA98BSWESDgNPgM948QJ27PBJc3NRqDUhCRhj0oE2YKmV5Pnzh7S2JnD27MfcuxdnvSsoeMOGDX9SVuYnOTnbejc0BCtWgM8HmzY9kObmRcFIBCXggN8E0gkEfmf79iQaGxNdE4lAUxNs2QKvXsGqVXD7NuTmQnc3pKbWi8i3bmtdCTiyXwc+5+XLXgoLM+nriyExEXbtgrVrYcECO19vL4yPw5Il4PfD6tVw4wZkZcG1a5BmWaUD+EpE3kwlEYzA98APVuU5OWkWuEp6/DhkZLir+fo1lJTYFes3Cj5/vn77CMgXkb8iUsBx+2+W4bzeEUt2Be/qApXaLUZHbVU6OyE93QbPzJz4Uo35qYgMRUqgHPjZMlxKSrYl+/37wSsPBKC0FC5dgrlzbQVycuDWLSXxiDlzsoCvRaQpUgLngfU0Ng7g9c5j/37Ys8e98rdvYeNGaGuD5GS4ehXy8mBgwP49eHCAqqp52kkiUhopgV+BHIqLA3R1ebh7FwoLp68dG9MWg9OnYfZsuHIFFi+2v1PFCgq0GwJ0dnqAHhHJjZTACJBAaqphcFAYGYGEhMlr1fXl5XDyJCQl2f5Yah8VVmg36NalpBiePVPj+EXEtYWnucoYE5qAMVBZCceO2cQ6OmDZsskEJwhoEU+fRk0g9BZs2wZHj8LMmXD5MixfPl1ZPQGLiv7zFkw24blzUFZmg+zcCYcOQXw8tLfDypXu5jxwAPbuhYaGCROeFxEnyeQlblvwTxtu3ZpNSwvExkJNDdTWgsdju37NGnfwvj7Iz8fyzuDgQ2c+RNWGOnbtg2hszBAbK1Yr7tsHcXFw5gys06nsEuoPnQPaEVVVIzQ0qPGiO4g0rTHmJ6DCgqirg927bRVOnbL73i208ooKGzwjY5yenn48nk+AGhGpdV+E+5XMGJMP3OHwYQ/V1RATAydOwObNoMeugumRq6HD6MIFqK+3ZVdwn+8xs2bptLoDfCkio1ERsFS4eLEEr7ed/n6xWk6rU4DiYripU9olVPYjR4adyv8AvhAR/Q0aoS8k1dXfsHBhA5WVM6wMw8NPaGmZEfZCYle+Phy4pozkSqZjTe8GaqgpR+K0wtRwdcCPoWT/96qwBBxTxjj3wbCX0mBjN2oPhNq3d/lfRAq8S8CpuT4Q+KDA30xVjzBZpSZSAAAAAElFTkSuQmCC';
var rightIcon4 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAwJJREFUWEftl09IVUEUh7+TQi8lCTG1zEgrFyYmIkSQUAqSERiIIARCUZRBoJBgYKiBGBqBLcwWFhEVhov+gCKIgiSE4CItjCw1lQiRXAj6KHgnzrvPetWrKO7DjbMa7p2Z893fnN+ZucIqN1nl+KwBrCnwWwVUVUMk6AKwTUSW/pS8qpoP9IYY0y0iR4Kf/yuAzT0nIjf+AvAUOOoOgAQYS0rg4UNb8zWQLiKhFEJVdwFv8HqF5GSYn4fCQujqsrn/ocAKQEQETEzA9u220BER6Q6lgqpeB87T3g6nTjlDXAGwhaqqoKnJej0icvhnAFWNAWaBjWRmwuioSwCRkRATA5aXMzMQHW3y7xGRsWAIVa0ErtHXB/n5EB8Pc3MuKODxQGUlNDZCayuUl1vcmyJydgVAVdcB40AqRUXw5AnU10NtrUsA4+OQkgI7d8LYGDhWTBaRTwahqkXAI969g7Q0yMqCq1chL88lgOVlKC2Fjg4noy2x4KKIXAkA9AMHqaiAlha4cwe/C1wFeP4c9u+HggLo6bG4lnApZkvgBYuLkJQEUVEwPQ2Dgy4DWMh9+2BoCF69gnSLSylQAJz0f7kpUFfn7H1/fxgA7t+H48fhzBloazMA89pufD6Pf+/NJfb1CQlhAvjyBXbsgIUFmJ2F2FjHCJb1lv1lZc7+WwuLArZwQwPU1Di2rK52gpnvzf/Dw5CdHWYAq++W4XFxMDnp2NIqX24uDAx8r01hUMALvARy/HXe6v2DB9Db6/Q7O6G42ACeAQfCsQUGcBq4y8gI7N0LGRnw9q2TdFaEIiI+AyeAe+ECsAPnPbDFX2RMZmvNzXDhgvXuAreBvrAAiMgGVb0EXObxYzh2zA4nxxGbNhlADmCQLgHYHcDuBHYOgDcAsBmYwedbz9QU2GG1dau9HxSRA6p6yA/g9cKHD05lTEy09/9xIQk+bwMAgdp/K7DXwSNKRKTzG8CPc/8ZIPXX+aiITAYANgKmRHCbEhGfqnoAvyQ/tSUR+Rj8bO2/YE2Br8xToDCyDYSqAAAAAElFTkSuQmCC';
var rightIcon5 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAhpJREFUWEftl8FLVFEUxn+fGE3NxgkEoXCjKdEqJKSl2EbRYCQR2pil6MKlLqLZTi6cf0BLd4MkZEWW/gWlIjEQhCZC4GaQGCYYVCbsxpkZadDR0UkYgvfgbd497zu/991z7z1PlPlSmfPjAXgOFHXAOecKFOqepEuHnzvn2oAPBeJfS+oqVPD/EYAEPh/s7tqHnOzAwgK0t0MwCHNzFn8ODngAngOeA54DZXTgNyAqK6GiAtJp29nSki4WOAs6gHfMz0NnJ3R3w+yshb2SdL/UsyABBKivh81N2N6G6mrTCkhK5os65x4DL5iYgKEhGBmB8XELmZLUXyrAV+AGbW2wuAhLS9DcbFpBSW8OAUSBB4yOQiQCk5MwMGAhYUmhUgGmgEeEQhAOQ18fTE+b1g8gAhjgZaAV6GdnR9TVQTwOq6vQ1GSxHZLelwpwD3hLMgkNDZBIQDQKPT1H9eykHB7OAv6d/5/ANUmpUgGsZ/gC3Mwk7u2F/X0yU9LSkoVKpWBtDWZmsnVSUwPLy1BbazmfSXp6XPddtCGxF51zt4CPgI9YDAYHYWXlqKYtVRsbG4OqKhuPAXck7f0TQA7iLvASuIJ1aVtbsL4OGxvg90NjY/YOBA5yfQK6JMVP+vc4lQMHAs65q8AT4CHgP0b4e644n0vKbBrnBpAHcgG4DVzPOAK/cqvis6RvxZLmj5/JgbMInzbWAyi7A38ACm1HMC5WYpkAAAAASUVORK5CYII=';
var rightIcon6 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAMdJREFUWEftl00KwjAQRr+PLgrVHsKeRmh3nrM7hZ7GHkIacFFHiiDUJpXE0CDMbPMzLy8DkxCJg4nzQwHUgNOAiEikAt2RNK69/gCAgWUyDEBRTAf/0UDT2O11HTCOQF3bx9sWyPMIAK7LK0vAmBfEegQbODv2PQLIsAS4OOafSN69i9C1QERuAPYfAA+S2TcVtnHvClMANaAG1IAaUANvA30PTK+2qpp6TIJmNG9tmwIcgOWHhuR1k3YckmRtjfd7QAFiG3gCcke2IQRJnXUAAAAASUVORK5CYII=';
var leftIcon1 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAitJREFUWEftls1LVUEYxn/PRmnjx7JWt1UtWoT/g+JC1IUoQQjtaqFCHxBUEBQGQVFKtYvCREQEXQiK/0REm1qELWqrrq5yFxPvuUe4He6Z857j4m7u2Z6ZeX7zzDvPO6LDnzqsTxeg60CuAyGEXmARuAHUgSVJbz1FG0KwdW8DDwBb5zPwTJKt898XA3gF3M2MX5Y0H4NIxd8BdzLjnkt6UgbgL3BxZAQGBmBlBXptL5AL0Sp+cgJTU9BowN5eMu+3pFoZgF/A5elp2NiA0VHY2sqHyIqPj8P+fhPC5gM/JF0tA7AAvDk9hclJ2N3Nh8gTHxuDzU3o6Ulkb0n65AawgSGEJWAuBgEYaHLmZvvZzjPiryXda1c7hTnggPgOXKsibkCFAB4nqoq7AWIQa2tghWoF57W99ShcDpxNaHcc/f1wfFxNvJQDqQsG/M3O/OgIarWm+OAgHBxAX1+C+hUYkhQ8qel2IL1q7y1i63WYmGjabuKHh8U5kQfjAojd89VVmJmJ50TMiUKA1p3nVbsnrCo54Ew4O/PrVSFi3dD+FSYccB+wNh1NzLwuGgOwfv7BGzKOxJyXtOzuBSGEpBtauOzs+O55OwgLqfX1RPanpCtlAP4Al4aHm+8Bq/a0q+U2lmxizs6CObi9Xe098AJ4mCGOimcTMzN3UdKjMg5cAB4DN9M34UtJHz3pljoxZ4WZvgm/AE8lNdwAXqHzjisMovMKFM3vAnTcgX8GNFQwGqdUGAAAAABJRU5ErkJggg==';
var leftIcon1_2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAGpJREFUWEft1zEKACEMRFHn/ofOFrIgKCxxI2Px7WPGZ2FUMy+Z+zcCTAIRESeuRdJS+94A67x5m9czLXBRgN0o/ewFAgRAAAEEEEDALpCfAcaKgtfQFOBf27k6PRHZAlQ3/tqPjwkCdoEHqQGYIbuzaxwAAAAASUVORK5CYII=';
var leftIcon2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAiRJREFUWEftljFIlGEYx39/PDizaKgEg4YctPG2QHCwyZMkhU4jhJpO4mh28STSclFocDiIwiFB6RByOPNGabH5lrvxhm4qzKFI6nri/fy4QML77k79IHy3j5fv/f/e53mf5/+IkJdC1ucMoG4EzOwC8AK4C+wDr4Cnkn4dR/qCALwEkofE3gNjkr61ChEE4Atwqa8PolHIZqGz05P9CNyW5PabXkEAisCNqSlYWICeHsjnobvb0ywBg5LKzRIEAUgAWScwPw/T09DVBVtbEIt5shUgLqnQDERdAHeomT30H19keRkmJ6GjAzY2YGDAk/0K3JH0oVGIQAA+xJAfifO5HIyPQ7UKKyuQcDGCH8B9Se8agQgM4EPcBHLAlZ0dGB6G3V1YWoJUypOtAilJrnICrYYAfIheIA9cLxYhHodyGWZmYHa2pvlE0t+vI1AaBvAhrgKuF8QqlQOIQgGSSchkoK3NU8wAjyX9PioUTQH4EBcBl+9be3swMgLb2zA6Cqur0N7uyaYlPT8RAB8iCrxxXXF/HyYmYH0dhoZgc9OT/STp2v8JYGb/TIFLxdpaLQUzkp4dewTMLLxHaGa1MiyVYHDwoAzTaZibO+EyNLPwGpGZhdeKzewB8Bo4fTMyszHgrctuKHZsZm7o6A1zIPkMXO7vh0gknJHMmcqjQ83EGVFC0vdAntuKG5rZOWARuAf8PPWxvNUb1vu/aTuud3DQ/TOAP8KL8CGZtHYcAAAAAElFTkSuQmCC';
var leftIcon2_2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAJ1JREFUWEftltEKwCAIRfXLqy93jM2XMeZNDGnYa4mnm3pjSl6cnJ8KYB8FRKR76oWZP+NgBUREnACfOWCA1uYA+n1vZo4BIJoD0NNhAKgCY1wPFQ6A1oAKvgLA6oJ23nwZgNUBqlABlAL/VQAwo7VtmD6I0kfxNmakEzPcjtEaeI7sMDsGuuDVLsK+ZJYZeffhL5k3gRVXAKVAugIH6hVoIYRIfRgAAAAASUVORK5CYII=';
var leftIcon3 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA9lJREFUWEftlm1I1mcUxn9HC6I0UgLRqVmxrXIRbayFEjRZ9MFqNoOoYFKhYG+2MYZQH9ZGrWEbRbLYpyJNEpMwh4bGNKiotcGQGgS2YZKRc2pvjtJ5xvnf/7bQx+fNoH3owPPl4b7Pdd3nXOc6f+EFh7xgfF4SiLgCqjodeBXIAJKALuA3oF1E+iNtadgEVPUt4BNgDTAhANAwUAXsEZH2cImEJKCqscDXQIklffwYrl6FW7eguxuSkyEjAxYsgEmTPNgh4HMR+SIcEuEQMPCPDbisDMrL4e7d0alTUqC0FIqLYYKrz0cicjAUiaAEVPUNoG1wEMnJgQsXYPZsKCiAOXMgLg66uuDSJaiogMFBKCmBgw72L+B1EekMRiIUga+AT48dg40bYckSOHsWJk8enfLaNcjOhvv3oa4OVq3yznwmInvGQ6AeWLFpExw9CqdOQX6+l+4kUAs8ANKs3MC8I0dgyxZYvx5OnPDO/Sgi74yHQDPw3rJlcO4c1NTAGpsBaAS+AVpFZEhVXwNu9PTA3LmQlARWEeAREC8iOhaJUC34DijavRv27oWFC6Gx0QH40Qc0AHWAdT4lANB0EfkzWgLvAj/09sKiRXDzpgMvKoK1ayEzM2Ba84NnDSlLRG5ERcAuqar1+gMjYWNWVQWPrLC4icjLg9WrISsL5L96VgOFImIaCRrh+EAcUAHkWaaHD6G+Hk6fdhPxwIeYMQMKC2H7dpg61cP8BcgWkYGoRKiq2U/dz0+wHHCp/XjyBFpbobYWqqvh3j2YORMaGpxPAN+KyNZoCeQC3we43ATY730gCzCrpq/P6aK52YFfvw4xMZ4ZvSIiJtaAMWYLVNXm9/KdO87dZs2C/fu9HD+JyNu+PhKBlcCHQM7QkNOF7YmWFli61Du/XESMcMQEEoDe4WFITISBAffKKVP4G8gcqWxVrbFNaY5pzllZCRs2eJgFInI8YgL+C3+3vZ+b6/p6+DBs2+al6vFXbwdgxmyreoWt6cWL4coVaGuD+fO9s7kiYl4RWQV8Al8CpSY0c8PYWCc4IzQyTJC7dsGBA06I7e2eBmw1p4jIH9ESSAZ+BaYdOgQ7d7o0Zkr20rQ015qODjhzBsyKExLg4kVnybYzRGTdWOD2fzg+YAkqgZimJti3D86fH50yPh42b3YkzROA28CbItI9LgJ+K4xEOWCqp7/fvbqzEww4PR1SU2HixH+hfgbyRcQ0EjRCVuDpbVU1RywGdgCpAbLadLQAZcHGbuS9sAk8e1FV4/2v4nT/m8BeettWc6gXPxcCkYKMWwPPE/BlBf53FfgHVYpFMLakjX4AAAAASUVORK5CYII=';
var leftIcon3_2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABKdJREFUWEftl29IlmcUxn8nR2UtmxgsC2JfguHYaJYbbZVaS6LIsNxwYa1C0P7BJAKL0FZQEBFbVE4pMaOyzZiK+6OVsaJiGyQtmFREQlSzUdmmYWmecZ7nefX19fHt/SLtw55Pr/efc677vq5znVvhJX/ykvPz3wKgqsOBL4DPwAFXDhSJyLPgm1LV94AvgbeB34HPReS3wBpVnQbsHWw+OFa/G1DVnUBBCC11wGIR6bJxVU0DaoCRQev+BhJE5I6qzgJ+AqKD5h8Bb4nIvVDKQwHYgvELFsDjx1BdDePGOVu+Az4BZgeSl5TAtm1w5AjMtlFYB/wCNAJjDh6EoqJ+83kiUhIRgBkz4MIFmDIFGhshNtbZZoE/sJPv3w/rLB3Q0ABz5zo/iz2QcUePwvLl0NPTbz5XREpfBMChoLUVkpPh2jWYNg1On4axY92tBw7A2rXubwOyZk1vyOdAVG0tLFkC3d2waxds3OjMtwFvikjriwCMAGqBtLt3YdYsuHkTpk93T1JRMWhyJ+6ZM2D0PX0KW7bA9u3OsGlnkYj8GJrc/h5Qhqpq4vkeSL192wXR0gKTJ8ONG74ndwYvXXKp6OiA9ethr9UA9ABZIvKtX3JfADaoqqOB88C7t25BUhI8eOCG2LMH8vP7h7tyBVJSoK0NVqyAsjIQQYEcESkbLHk4AL2lFsy5bZg3z62OEUYWcP06zJwJ9++73J84AVFRzlS+iJhXhP38KDBNmw5GBiffvBmstCzRwoVw8iTcuwdWMUaVAaupgeFmZe73F9DsmVRTRBSo6njgDyDWT+1Xr0JqqktHejo0N7u6MBD19TBqlG8aMyEzqT/9ZkONKBf4+tQpSDMSBpYaTU0wZw48srBAYiKcPQsxMX3hfUzK14QGaEBVHQBmPsuWQWEh5NoIdAJ2gjfa212PuHzZTbh0qet2w4b1AfYxqYgB9FIQdF2W3GhJ7OyE+fPdE0+aBA8fggHKyYHSUigu9vWJyCmwpKqa5HW6d7xOZydfbM6WkQF1dTBhApw/74rPAD154vrFuXMDaDPw6SJyKiIRhi5S1dXmvubp2dlw/DjExbmJEhLc1WbT5n7PvIa9b1/vLVhyc8CGcHU46INEVbOBCnNL04FdsQnN9DF1al/IUJ8oKICd1lHAesOn4VxwgAgDYVV1EVAFvGLNZPduiI52S81MJ/AFd0XTweHD0NXltuGtW51V3cDHIlIdMQWq+j7wMzDCmolVgpmLmYyZTeDz84mqKsjKgufPYccO2LTJWW3kZIjID34g/JzwIjDduLSmYrZaWQmZmX3bw/nEsWNuCZtu7OY2bHD2mR6SReTXUBB+AP4BXp040bXaQ4dg5Upnm/X0SiBvEJ/YAhQCMeXlsGqVVZT7flhtUoaLIvJhJACcG7Bat9LzXjsdgPWIFs8TXgsK1Kt2VbUE9cBoE21eHsTHw507zup2ERkTCQB70VrdBpKYkWSKiD3JAj7xVZgXcQpgfEcbVUah92aM7Aa8JK9bDXsPllq/1+xgqvb2fwR8Y03NW2f0pQU/3QP7h+wfE1WNNxf0EtVE1A3DnWqo5obsBiIF/D+AfwECk9cwe1ZOfwAAAABJRU5ErkJggg==';
var leftIcon4 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAwBJREFUWEftl09IVVEQh79JITMSEbOyFLVyYaEiggQFlSAlgYIIghAUhRkECQkGhRpEkRHUomxRIZKSuEgDRRCFSBChRZkY+TeViJBcCPooeBNz7xNf8SqE+3DjrA73njPz3d+ZmXOusM4m6xyfDYANBf6qgKpqiARdAPaIyNK/kldV84HeEHO6RaQw+PlaAWztRRF59B+AV8ApTwAkgFhaCm1tjsuPQIaIhFIIVd0HfPL5kKQkmJ+Hkyehq8tZu3YFVgAiImByEpKTHUeFItIdSgVVfQBcevIEzp1zZ3gCYI6qq+HOHcdnj4ic+BNAVWOAOWBbZiYMD3sEEBkJMTFgaTk7C1u3YvIfEJHRYAhVrQLu9fVBfj4kJMC3bx4oEBUFVVVw6xY8fAiVlU7YxyJyYQVAVTcBY0BaURF0dkJ9PdTWegQwNgapqbB3L4yOgghWikki8t0gVLUIeDkxAenpkJ0Nd+/C8eMeASwvQ1kZvHjhZrQlFnBVRG4HAPqBo5cvw/370NQEVgWeAgwOwqFDUFAAPT0OgCVcqpUl8G5xEXbvhuhomJmBgQGPASxiXh4MDcHICGRYWCgDCoCz9uWmQF2du/f9/WEAaGmB8nKoqIDGRgfAim2/30+U7b1ViX39jh1hAvj5E1JSYGEB5uYgLs6tA8t6y/7Tp939NwuLAub45k24ds0ty5oaN5jVvdX/27eQkxNmAOvvluHx8TA15Zaldb4jR+D169XWFA4FfMAHINf6vPX71lbo7XXH7e1QUuIAvAEOhwvgPND8/j1kZcHBgzA+7iadNaGICH4AZ4Dn4QKwA+czsMuajAUxa2iAK1ecYTPwDOgLC4CIbFHV68CNjg4oLnYOJ6ciYmMdgFzAIL0BsDuA3QnsHAB8AYDtwKzfz+bpabDDKjHReT8gIodV9ZgB+Hzw5YvbGXfudN6v/UKymtOrADZS1aeBvQ6eUioi7SsAf6xdM0BaCAcqIlMBgG2AKRFs0yLiV9UowNXkd1sSka/Bjzb+CzYU+AWaYqAwVFSuxwAAAABJRU5ErkJggg==';
var leftIcon5 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAhpJREFUWEftls9LVFEcxT9HjKZm4wSCULjRlGgVEtJSajOiwUgitDFL0YVLXUSznVw4/4CW7gZJyIos/QtKRWIgCE2EwM0gMUwwqEzYjXtnimEcHXsuhuBdeJt7zzvn3HO/94eoclOV9fEN+AlUTMAYY8oU6oGkC6X9xpgw8L4M/pWknnIF//8YkCAQgP19N48TE1hags5OiERgYcHhz56Ab8BPwE/AT6CaCfwCVFsLNTWQy7mTLSfpfJm7oAt4u7gI3d3Q2wvz8w71UtI9r3dBGgg1N8P2NuzuQn29owpJyhSTGmMeAc+npmBkBMbGYHLSIWYkDXo18AW4Fg7D8jKsrEB7u6OKSHpdYiAB3B8fh3gcpqdhaMghYpKiXg3MAA+jUYjFYGAAZmcd1XcgDliDF4HbwODeHmpqglQK1tehrc1huyS982rgLvAmk4GWFkinIZGAvr6jdPamHB3NGyxa/x/AFUlZrwbsm+EzcN0K9/fD4SHYJenoyJvKZmFjA+bm8nXS0ACrq9DY6CSfSnpSTtz2VXyQWJAx5gbwAQgkkzA8DGtrRyntVrVjExNQV+fGk8AtSQdnMlAwcQd4AVyyj7SdHdjchK0tCAahtTX/hUJ/pT4CPZJSx4mfOoE/BMaYy8Bj4AEQPIb4W6E4n0nKnxontFMtQen/xphzwE3gqk0E+FnYFZ8kfa0kWjzuycC/CFTC+gaqnsBvjm1HMGMTF3EAAAAASUVORK5CYII=';
var leftIcon6 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAMZJREFUWEftl00OgjAQRr8vLEhQDiGnMYGd52SnCaeRQxiauMAxYthgK2ltiouZbX/m9XWSaYmNgxvnhwKoAacBEZFIBbojaVx7/T8AA6tkGICimM79m4GmscvrOmAcgbq2j7ctkOcRAFx3V5aAMW+IlQg2cHZsfASQWQAujvknknfvInQtEJEbgP0C4EEyW1NhG/cuMQVQA2pADagBNTAb6Hvg9WirqqnFpG9Gi86WFOAAfH5oSF6TtOOQJN/WeL8HFCC2gSe6R7Yh4eER+QAAAABJRU5ErkJggg==';
var twoIcon6 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAM1JREFUWEftl00KgzAQRr8PF0LaHKKephB3Pae7FjxNPUQx0IWdYqEbTSpG0S5mtvmZl5eBSYidgzvnhwKogagBEZGVCvRA0sf2+n8AMq1M2raFMaY/+DIDZVkG7dV1ja7r4JwLjldVhTzPlwPE7s5aC+/9B2Iikg1cIxufAWQBgFtk/oXkc3YRxhaIyAPAcQDwIplNqQiNz64wBVADakANqAE18DXQNA36V1tRFH2P2b4ZDTrbpgAnYPyhIXnfpB2nJPm1ZvZ7QAHWNvAGQDPaIb2cUNgAAAAASUVORK5CYII=';
var iconList = {
  'leftEar': {
    '气导': {icon: leftIcon1, coverIcon: leftIcon1_2},
    '骨导': {icon: leftIcon2, coverIcon: leftIcon2_2},
    '声场': {icon: leftIcon3, coverIcon: leftIcon3_2},
    '舒适阈': {icon: leftIcon4},
    '不舒适阈': {icon: leftIcon5},
    'T': {icon: leftIcon6},
    'SolidPoint': {icon: ''},
  },
  'rightEar': {
    '气导': {icon: rightIcon1, coverIcon: rightIcon1_2},
    '骨导': {icon: rightIcon2, coverIcon: rightIcon2_2},
    '声场': {icon: rightIcon3, coverIcon: rightIcon3_2},
    '舒适阈': {icon: rightIcon4},
    '不舒适阈': {icon: rightIcon5},
    'T': {icon: rightIcon6},
    'SolidPoint': {icon: ''},
  },
  'twoEars': {
    'T': {icon: twoIcon6},
    'SolidPoint': {icon: ''},
  }
}
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var staffType16List = []; // 听力师列表
var staffType0List = []; // 审核医师
var userInfo = null; // 当前登录用户
var reqPhysician; // 审核医师名字
var reportData = [];
var tinnitusEvaluate = [];
var reportInfo = {};
var deviceData = {};
var filterList = {};
var earSideList = {
  '右耳': 'right',
  '双耳': 'shuang',
  '左耳': 'left'
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('gdfyempg1'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    userInfo = publicInfo.userInfo;
    reqPhysician = rtStructure.examInfo.reqPhysician;
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    deviceData = rtStructure.enterOptions ? (rtStructure.enterOptions.docOrign || {}) : {};
    // deviceData = deviceData[0]; // 测试
    // console.log('deviceData-->',deviceData);
    reportData = deviceData.reportData || [];
    tinnitusEvaluate = deviceData.tinnitusEvaluate || [];
    tinnitusEvaluate = filterTbData();
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
}
// 初始化编辑页面
function initPage(type) {
  if(type === 'edit') {
    staffType16List = getDictUsersBaseList({staffType: '1,6'});
    staffType16List = transformOptList(staffType16List, 'name');
    staffType0List = getDictUsersBaseList({staffType: '0'});
    staffType0List = transformOptList(staffType0List, 'name');
    initDropdown();
    initDatePicker();
    setCurrUser();

    reportInfo = deviceData.reportInfo || {};
    $('.empg-date').text(reportInfo.examDate);
    // 右上角报告日期
    $('#gdfyempg-rt-2').text(reportInfo.reportDate || '');
  }else if(type === 'view') {
    showCaImg();
  }

  // 来源为住院则显示住院号，其他都是门诊号
  if(rtStructure.enterOptions && rtStructure.enterOptions.publicInfo && 
    rtStructure.enterOptions.publicInfo.patientSource === '住院') {
    $(".inPatNo").show();
    $(".outPatNo").hide();
  } else {
    $(".inPatNo").hide();
    $(".outPatNo").show();
  }

  // 序列号/校准日期
  if(reportInfo.serialNo) {
    $('#gdfyempg1 .serial-no').text('序列号：'+reportInfo.serialNo);
  }

  // 初始化图表
  initEarChart();
  // 初始化表格
  tinnitusEvaluate.map(function(item) {
    initTbOfEmpg(earSideList[item.earSide],item);
  })
}

// 默认当前登录人
function setCurrUser() {
  // 检查者
  if (userInfo && !$('.staffType16').val()) {
    $('.staffType16').val(userInfo.name);
    $('.staffType16StaffNo').text(userInfo.staffNo);
  }

  // 审核医师，默认读取开单医生
  var reqPhysicianStaffNo;
  if (reqPhysician) {
    for (var i = 0; i < staffType0List.length; i++) {
      if (staffType0List[i].name === reqPhysician) {
        reqPhysicianStaffNo = staffType0List[i].staffNo;
        break;
      }
    }
  }
  if (reqPhysician && reqPhysicianStaffNo && !$('.staffType0').val()) {
    $('.staffType0').val(reqPhysician);
    $('.staffType0StaffNo').text(reqPhysicianStaffNo);
  }
}

// 初始化听力师和审核医生下拉
function initDropdown() {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: '.staffType16',
    data: staffType16List,
    className: 'laySelLab',
    click: function(obj) {
      this.elem.val(obj.title);
      $('.staffType16StaffNo').text(obj.staffNo);
    },
  });
  dropdown.render({
    elem: '.staffType0',
    data: staffType0List,
    className: 'laySelLab',
    click: function(obj) {
      this.elem.val(obj.title);
      $('.staffType0StaffNo').text(obj.staffNo);
    },
  });
}

// 转换下拉数据格式
function transformOptList(list, titleName) {
  let arr = [];
  list.forEach((item,index) => {
    if(titleName) {
      arr.push({ ...item,title:item[titleName],id:index,templet: `<span title='${item[titleName]}'>${item[titleName]}</span>` });
    }else {
      arr.push({ title:item,id:index,templet: `<span title='${item}'>${item}</span>` });
    }
  })
  return arr;
}

// 初始化日期时间插件
function initDatePicker() {
  var path = location.href.split('template-lib/')[0];
  layui.config({dir: path + 'template-lib/plugins/layui/'});
  $('.date-wrap01').val(layui.util.toDateString(new Date(), 'yyyy-MM-dd'));// 默认当前时间
  layui.use('laydate', function () {
    var laydate = layui.laydate;
    laydate.render({
      elem: '.date-wrap01',
      type: 'date',
      trigger: 'click',
      format: 'yyyy-MM-dd',
    });
  });
}

// 打印时显示ca签名图像
function showCaImg() {
  console.log(' 16--->', $('.staffType16'));
  $('.staffType16').hide();
  $('.staffType16Ca').attr('src', api.getExamRptSignImage + '?staffNo=' + $('.staffType16StaffNo').text()).show();
  $('.staffType0').hide();
  $('.staffType0Ca').attr('src', api.getExamRptSignImage + '?staffNo=' + $('.staffType0StaffNo').text()).show();
}

// 格式化x轴的坐标数值
function computeX(orignX) {
  let x = Number(orignX/1000).toFixed(3);
  // interval：间距， multiple：临界值相乘的倍数， axis: 坐标实际值，resX: 当前坐标对应的值
  // interval = 坐标对应的值(resX)-上一个的坐标对应的值(resX)/中间间隔的格数，如第二条数据(1-0.5)/5=0.1
  // multiple = 上一个的坐标实际值(axis)/上一个的坐标对应的值(resX)，如第二条数据1/0.5=2
  let map = [
    {axis: 1, resX: 0.125, min: 0, max: 0.125, interval: 0.5, multiple: 0},
    {axis: 6, resX: 0.25, min: 0.125, max: 0.25, interval: 0.025, multiple: 8},
    {axis: 11, resX: 0.5, min: 0.25, max: 0.5, interval: 0.05, multiple: 24},
    {axis: 16, resX: 1, min: 0.5, max: 1, interval: 0.1, multiple: 22},
    {axis: 21, resX: 2, min: 1, max: 2, interval: 0.2, multiple: 16},
    {axis: 26, resX: 4, min: 2, max: 4, interval: 0.4, multiple: 10.5},
    {axis: 31, resX: 8, min: 4, max: 8, interval: 0.8, multiple: 6.5},
    {axis: 36, resX: 16, min: 8, max: 16, interval: 1.6, multiple: 3.875},
  ]
  let result = [x, x]; //原数值大小
  for(let item of map) {
    let {min, max, interval, multiple} = item;
    if(x >= min && x < max) {
      if(x == min) {
        result = [Number(Number(x * multiple).toFixed(3)), orignX];
      } else {
        let muliNum = Number(Number(min * multiple).toFixed(3)) + Number(((x - min)/interval).toFixed(3));
        result = [Number(muliNum.toFixed(3)), orignX];
      }
      break;
    }
  }
  return result;
}

// x网络线的样式
function xSplitLineStyle() {
  let topColors = [];  //实线的颜色
  let bottomColors = [];  //虚线的颜色
  let topList = [1,6,11,16,21,26,31,36];  //这些位置显示上方横坐标网格线
  let bottomList = [14,19,24,29,34,39];  //这些位置显示下方横坐标网格线
  for(let i = 0; i <= 39; i++) {
    if(topList.includes(i)) {
      topColors.push('#e3e3e3');
    } else {
      topColors.push('transparent');
    }
    if(bottomList.includes(i)) {
      bottomColors.push('#e3e3e3');
    } else {
      bottomColors.push('transparent');
    }
  }
  return {topColors, bottomColors};
}
function isEvenOrOdd(num) {
  if (num % 2 === 0) {
    return true;
  } else {
    return false;
  }
}

// 格式化图表数据
function filterEchartData() {
  let series = reportData.map((item) => {
  let earSideCode = item.earSide === '左耳' ? 'leftEar' : item.earSide === '右耳' ? 'rightEar' : 'twoEars';
  let name = item.signalOutput;
  let data = [];
  let markLineData = []; 
  let isNeedSplitLine = false; // 记录：气导：右耳“圆圈”，可掩蔽左耳“正方形”和右耳“三角形”特殊处理，线条不可连接
  let occultation = '';
  item.curve.forEach((cur,index) => {
    // 横坐标是frequency，纵坐标是intensity
    let [x] = computeX(cur.frequency);
    occultation = cur.occultation;
    let symbol = occultation === '1' ? iconList[earSideCode][name].coverIcon : iconList[earSideCode][name].icon;
    let yVal = name === 'T' ? Number(cur.intensity) - 3 : Number(cur.intensity);
    data.push({
      value: [Number(x), yVal],
      symbol: name === 'SolidPoint' ? 'circle' : `image://${symbol}`,
    });
    if(name==='气导') {
      if(item.earSide === '右耳' || (item.earSide === '左耳' && occultation==='1')) {
        isNeedSplitLine = true;
        if(index===0) {
          let xVal1 = Number(x)+0.5;
          markLineData.push(
          {
            value: [xVal1, Number(cur.intensity)],
            symbol: 'none',
          })
        }else {
          let xVal1 = Number(x)-0.5;
          let xVal2 = Number(x)+0.5;
          markLineData.push(
          {
            value: [xVal1, Number(cur.intensity)],
            symbol: 'none',
          },
          {
            value: [xVal2, Number(cur.intensity)],
            symbol: 'none',
          })
        }
      }
    }
    
  });
  let lineColor = item.earSide === '左耳' ? '#6565FF' : item.earSide === '右耳' ? '#FF1D1C' : '#000';
  let series1 = {
    name,
    type: 'line',
    data:data,
    smooth: false,
    symbolSize: name === 'SolidPoint' ? 8 : 20,
    color: lineColor,
    lineStyle: {
      width: 1,
      type: occultation === '1' ? 'dashed' : 'solid', // 可掩蔽为虚线
    },
  };

  let markLineArr = [];
  if(isNeedSplitLine) {
    series1['lineStyle']['opacity'] = 0;// 线条透明度，不可见
    let twoDataLen = [];
    let series2 = {
      name,
      type: 'line',
      smooth: false,
      symbolSize: name === 'SolidPoint' ? 8 : 20,
      color: lineColor,
      lineStyle: {
        width: 1,
        type: occultation === '1' ? 'dashed' : 'solid', // 可掩蔽为虚线
      },
    };
    for(let i=0;i<markLineData.length;i=i+2) {
      twoDataLen.push(markLineData[i],markLineData[i+1]);
      markLineArr.push({...series2,data:twoDataLen});
      twoDataLen = [];
    }
  }

  return [series1, ...markLineArr]
  })
  series = series.flat(1);
  return series;
}

// 耳鸣评估直角坐标系
function initEarChart() {
  let series = filterEchartData();
  var xAxisStyle = xSplitLineStyle();
  let myChart = echarts.init(document.getElementById('empgEchart'),null, {devicePixelRatio: 2});
  // 指定图表的配置项和数据
  let options = {
    animation: false,
    grid: {
      // width: 240,
      // height: 240,
      // right: 10,
      // top: 25,
      width: 376,
      height: 376,
      right: 10,
      top: 25,
      borderColor: '#000',
      borderWidth: 2,
      show: true
    },
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
    },
    xAxis: [
      {
        position: 'top',
        name: 'AC:插入式耳机',
        nameLocation: 'middle',
        nameGap: 14,
        nameTextStyle: {
          color: '#000',
          fontSize: 10,
          padding: [10, 0, 0, 170],
        },
        min: 0,
        max: 39,
        splitNumber: 39,
        axisLabel: {
          formatter:function(v){
            let value = ''
            switch(v) {
              case 1:
                value = 125;
                break;
              case 6:
                value = 250;
                break;
              case 11:
                value = 500;
                break;
              case 16:
                value = '1k';
                break;
              case 21:
                value = '2k';
                break;
              case 26:
                value = '4k';
                break;
              case 31:
                value = '8k';
                break;
              case 36:
                value = '16k';
                break;
            }
            return value;
          },
          color: '#000',
          fontSize: 12,
          margin: 2,
          fontFamily: 'Microsoft YaHei',
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: xAxisStyle.topColors,
            type: 'solid',
          }
        },
      },
      {
        name: '频率(Hz)',
        nameLocation: 'middle',
        nameGap: 14,
        nameTextStyle: {
          color: '#000',
          fontSize: 12,
        },
        min: 0,
        max: 39,
        splitNumber: 39,
        axisLabel: {
          formatter:function(v){
            let value = ''
            switch(v) {
              case 14:
                value = 750;
                break;
              case 19:
                value = '1.5k';
                break;
              case 24:
                value = '3k';
                break;
              case 29:
                value = '6k';
                break;
              case 34:
                value = '12.5k';
                break;
            }
            return value;
          },
          color: '#000',
          fontSize: 12,
          margin: 2,
          fontFamily: 'Microsoft YaHei',
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: xAxisStyle.bottomColors,
            type: 'solid',
          }
        },
      }
    ],
    yAxis: [{
        type: 'value',
        name: '听力级别(dB)',
        nameLocation: 'middle',
        nameGap: 25,
        inverse: true,
        min: -25,
        max: 125,
        splitNumber: 20,
        nameTextStyle: {
          color: '#000',
          fontSize: 12,
        },
        axisLabel: {
          color: '#000',
          fontSize: 12,
          fontFamily: 'Microsoft YaHei',
        },
        splitLine: {
          lineStyle: {
            color: '#ccc'
          }
        },
        axisTick: {
          show: false,
        }
    }],
    series: series,
  };
  myChart.setOption(options);
}

// 格式化表格数据，带上单位
function filterTbData() {
  tinnitusEvaluate.map(function(item) {
    let hlUnitList = ['hearingThreshold','maskingNoiseThreshold'];
    let slUnitList = ['loudness'];
    for(let key in item) {
      // 单位：HL
      if(hlUnitList.indexOf(key) > -1) {
        item[key] = item[key]  ? item[key] + ' HL' : '';
      }
      // 单位：SL
      if(slUnitList.indexOf(key) > -1) {
        item[key] = item[key]  ? item[key] + ' SL' : '';
      }
      // 最低掩蔽级MML
      if(key === 'minimumMaskingLevel' || key === 'maskingDifference') {
        let maskingDifference = item.maskingDifference ? item.maskingDifference + ' SL' : '';
        let minimumMaskingLevel = item.minimumMaskingLevel ? '(' + item.minimumMaskingLevel + ' HL)' : '';
        item['minimumMaskingLevelAndDif'] = maskingDifference + minimumMaskingLevel;
      }
      // 单位：Hz
      if(key === 'pitch') {
        item[key] = item[key] + ' Hz';
      }
      // 单位：s
      if(key === 'residualInhibition') {
        item[key] = item[key]  ? item[key] + ' s' : '';
      }
    }
  })
  return tinnitusEvaluate;
}

// 耳鸣评估表格
function initTbOfEmpg(side,data) {
  var tdName = side + '-td';
  let color = side === 'left' ? '#6565FF' : side === 'right' ? '#FF1D1C' : '#000';
  var table = $('.empg-tb').find(`[${tdName}]`);
  table.each(function(i,dom) {
    let attrName = $(dom).attr(tdName);
    let text = data[attrName] ? data[attrName] : '';
    if((attrName==='pitch' || attrName==='loudness') && text) {
      let htmlStr = '<span style="position:absolute;left: 6px;color:'+color+';">T</span><span>'+text+'</span>'
      $(dom).html(htmlStr);
    }else if(attrName==='hearingThreshold' && text) {
      let htmlStr = '<span class="dot-flag" style="background:'+color+';"></span><span>'+text+'</span>'
      $(dom).html(htmlStr);
    }else {
      $(dom).text(text);
    }
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}