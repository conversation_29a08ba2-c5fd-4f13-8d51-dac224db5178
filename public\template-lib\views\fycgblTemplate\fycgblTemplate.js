// publicCode中没有的但是需要赋默认值的特殊处理
var hisAndFormMap = {
  'cgbl-0025.001': { label: '送检医生', wt: '', hisKey: 'optName' },
  'cgbl-0021.001': { label: '影像学检查结果', wt: '', hisKey: 'relevantImageTest' },
  'cgbl-0018.001': { label: '诊断医院', wt: '', hisKey: 'hospitalName' },
  // 'cgbl-0011': { label: '离体时间', wt: '1', hisKey: 'momentTime' },
  // 'cgbl-0012': { label: '固定时间', wt: '1', hisKey: 'momentTime' },
}
var saveApplyParams = {
  'medRecord': { label: '病史摘要', id: 'cgbl-0014.001'},
  'clinDiag': { label: '临床诊断', id: 'cgbl-0024.001'},
  'gmSampleList': { label: '标本信息', id: ''},
}
function initHtmlScript(ele) {
  // 编辑
  if($(ele).attr('isview') !== 'true') {
    initDatePicker();
    if($(".sampleViewPart").length) {
      specialBlockViewHandler('sample', false)
    }
    setDisabledByConfig && setDisabledByConfig(applyStatusRes);
  } else { // 预览
    if($(".sampleViewPart").length) {
      specialBlockViewHandler('sample', true)
    }
  }
  getGmSamplePartByLevelList();   //检查部位
  getSampleList(applyInfo);  //标本
  setValByHis(hisAndFormMap)
}

function initDatePicker() {
  var path = location.href.split('template-lib/')[0]
  layui.config({dir: path + 'template-lib/plugins/layui/'})
  layui.use('laydate', function () {
    var laydate = layui.laydate;

    laydate.render({
      elem: '.date-wrap01', //指定元素
      type: 'date',
      trigger: 'click',
      format: 'yyyy-MM-dd'
    });
    laydate.render({
      elem: '.date-wrap02', //指定元素
      type: 'date',
      trigger: 'click',
      format: 'yyyy-MM-dd'
    });
    laydate.render({
      elem: '.daytime-wrap01', //指定元素
      type: 'time',
      trigger: 'click',
      format: 'HH时mm分'
    });
    laydate.render({
      elem: '.daytime-wrap02', //指定元素
      type: 'time',
      trigger: 'click',
      format: 'HH时mm分'
    });
  });
}

$(function () {
  window.initHtmlScript = initHtmlScript;
})