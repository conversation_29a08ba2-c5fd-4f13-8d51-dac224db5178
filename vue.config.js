const webpackConfig = require("./webpack.config");
const systemName = "sreport";
const port = 8044;
module.exports = {
	// publicPath: './',
	publicPath: process.env.NODE_ENV === "development" ? `http://192.168.1.18:${port}/${systemName}/` : `/${systemName}/`,
	outputDir: "./dist/webpacs-struct-report-ui",
	// transpileDependencies: ['socket.io-client'],
	devServer: {
		port,
		headers: {
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Headers": "X-Requested-With,content-type,token",
			"Access-Control-Allow-Methods": "PUT,POST,GET,DELETE,OPTIONS",
		},
		proxy: {
			"/nurseApi": {
				// target: "http://192.168.0.120:90",
				// target: 'http://192.168.1.167', // 周颖
				// target: 'http://192.168.0.99',
				target: 'http://192.168.0.6',
				changeOrigin: true,
			},
			"/*": {
				target: "http://192.168.0.6",
				// target: 'http://192.168.1.167', // 周颖
				// target: 'http://192.168.0.99',
				// target: 'http://192.168.3.156',
				// target: 'http://192.168.0.8',
				changeOrigin: true,
			},
		},
	},

	// css在所有环境下，都不单独打包为文件。这样是为了保证最小引入（只引入js）
	css: {
		extract: false,
	},

	configureWebpack: {
		...webpackConfig,
		externals: {
			// 'vue': 'Vue',
			// 'vue-router': 'VueRouter',
			// 'element-ui': 'ElementUI',
			// 'vuex': 'Vuex',
		},
	},

	chainWebpack: config => {
		config.module
			.rule("images")
			.use("url-loader")
			.loader("url-loader")
			.tap(options => Object.assign(options, { limit: 10240 }));
		// 移除 preload(预载) 插件，只加载当前路由文件
		config.plugins.delete("preload");
		// 移除 prefetch(预取) 插件
		config.plugins.delete("prefetch");
	},
};
