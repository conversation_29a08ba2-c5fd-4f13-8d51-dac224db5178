<link rel="stylesheet" href="/template-lib/plugins/elementUI/element.min.css">
<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/digCaseTemplate/gdfycqcszdTemplate/gdfycqcszdTemplate.css">
<script src="/template-lib/plugins/elementUI/vue.min.2.7.16.js"></script>
<script src="/template-lib/plugins/elementUI/element.min.js"></script>
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/plugins/echarts.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/digCaseTemplate/gdfycqcszdTemplate/gdfycqcszdTemplate.js"></script>
<script src="/template-lib/views/digCaseTemplate/gdfycqcszdTemplate/NICHD.js"></script>

<ul class="t-pg" style="height: 100%;overflow: auto;">
  <li class="page" id="gdfycqcszd1">
    <span class="rt-sr-w" id="saveData" rt-sc="pageId:gdfycqcszd1;name:参数保存节点;wt:;desc:参数保存节点;vt:;pvf:1;">{}</span>
    <div class="top-bar">
      <div class="top-inner">
        <span>影像所见</span>
        <span class="rt-sr-w" id="etgzl-rt-1" rt-sc="pageId:etgzl1;name:姓名;wt:;desc:姓名;vt:;pvf:;code:1;"></span>
        <span class="rt-sr-w" id="etgzl-rt-2" rt-sc="pageId:etgzl1;name:性别;wt:;desc:性别;vt:;pvf:;code:2;"></span>
        <span class="rt-sr-w" id="etgzl-rt-3" rt-sc="pageId:etgzl1;name:年龄;wt:;desc:年龄;vt:;pvf:;code:3;"></span>
        <span class="rt-sr-w" id="etgzl-rt-4" rt-sc="pageId:etgzl1;name:病理号;wt:;desc:病理号;vt:;pvf:;code:145;"></span>
      </div>
    </div>
    <div class="main-wrap" id="data-wrap">
      <div class="data-wrap">
        <div class="item-wrap">
          <p class="tl">检查日期</p>
          <div class="text-pan">{{currData.examDateTime}}</div>
        </div>
        <div class="item-wrap">
          <p class="tl">孕周</p>
          <div class="text-pan">{{currData.pregnancyWeeksText}}</div>
        </div>
        <div class="item-wrap">
          <p class="tl">测量值</p>
          <el-table :height="dataType === 1 ? resultFormHeight1 : resultFormHeight2" border :data="currDataList">
            <el-table-column label="参数名称" prop="paramName" show-overflow-tooltip></el-table-column>
            <el-table-column label="测量值" prop="paramValue" show-overflow-tooltip>
              <template v-slot="{row}">
                <!-- 亚裔体重为空时可输入 -->
                <el-input v-if="row.formulaId === '10005' && !row.paramValue" v-model="row.editParamValue" size="mini" @change="saveParam(row)" placeholder="请输入"></el-input>
                <template v-else>
                  <span>{{ row.paramValue }}</span>
                  <span v-if="row.valMark" class="red-mark">{{ row.valMark }}</span>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="单位" prop="unit" show-overflow-tooltip></el-table-column>
            <el-table-column :label="dataType === 1 ? sd2Label1 : sd2Label2" prop="sd2" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
        <div class="item-wrap">
          <p class="tl">显示更多历史数据</p>
          <div class="search-wrap text-pan">
            <div class="row">
              <el-radio label="sickId" v-model="searchType" :disabled="!isEdit">按相同病人ID查询</el-radio>
              <el-radio label="identityCard" v-model="searchType" :disabled="!isEdit">按相同身份证查询</el-radio>
            </div>
            <div class="row">
              <span class="label-text">参数名称：</span>
              <el-select size="mini" style="width: 420px;" multiple clearable v-model="searchNameArr" :disabled="!isEdit">
                <el-option v-for="item in paramCodeList" :label="item.paramName" :value="item.paramCode"></el-option>
              </el-select>
              <el-button v-if="isEdit" type="primary" size="mini" @click="searchHisParams">查询</el-button>
            </div>
          </div>
          <el-table class="search-table" border :data="historyDataList">
            <el-table-column label="序号" type="index" width="50"></el-table-column>
            <el-table-column label="孕周" prop="pregnancyWeeksText" width="80" show-overflow-tooltip></el-table-column>
            <el-table-column label="参数名称" prop="paramName"></el-table-column>
            <el-table-column label="测量值" prop="paramValue" width="70" show-overflow-tooltip>
              <template v-slot="{row}">
                <span>{{ row.paramValue }}</span>
                <span v-if="row.valMark" class="red-mark">{{ row.valMark }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单位" prop="unit" width="50" show-overflow-tooltip></el-table-column>
            <el-table-column :label="dataType === 1 ? sd2Label1 : sd2Label2" prop="sd2"></el-table-column>
            <el-table-column label="检查日期" prop="examDateTime" width="160" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="chart-wrap">
        <div class="action-cont">
          <div>
            <span class="tl">生长曲线</span>
            <label for="gdfycqcs-rt-5">
              <input class="chb rt-sr-w check-handle-all" type="checkbox" id="gdfycqcs-rt-5" rt-sc="pageId:gdfycqcszd1;name:全选生长曲线图加入报告;wt:4;desc:全选生长曲线图加入报告;vt:;pvf:1;">
              <span class="chb-lb">全选生长曲线图加入报告</span>
            </label>
          </div>
          <el-radio-group v-model="dataType" size="mini" @change="changeDataType">
            <el-radio-button :label="2" v-if="isEdit || dataType === 2">NICHD 亚裔</el-radio-button>
            <el-radio-button :label="1" v-if="isEdit || dataType === 1">中国香港</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-sample">
          <div class="sample-item" v-for="item in chartSample[dataType].list" @click="clickSampleItem(item)">
            <span class="pointer" :style="{'background-color': item.color}">
              <span class="line" v-if="item.type === 'line'" :style="{'background-color': item.color}"></span>
            </span>
            <span class="name">{{ item.name }}</span>
          </div>
        </div>
        <div class="chart-list">
          <div class="chart-cont bdb bdr">
            <p class="tl">
              <label for="gdfycqcs-rt-6">
                <input class="chb rt-sr-w check-handle" value="BPD" type="checkbox" id="gdfycqcs-rt-6" rt-sc="pageId:gdfycqcszd1;name:双顶径(BPD);wt:4;desc:双顶径(BPD);vt:;pvf:1;">
                <span class="chb-lb">双顶径(BPD)</span>
              </label>
            </p>
            <div class="chart" id="chart-bpd"></div>
          </div>
          <div class="chart-cont bdb">
            <p class="tl">
              <label for="gdfycqcs-rt-7">
                <input class="chb rt-sr-w check-handle" value="HC" type="checkbox" id="gdfycqcs-rt-7" rt-sc="pageId:gdfycqcszd1;name:头围(HC);wt:4;desc:头围(HC);vt:;pvf:1;">
                <span class="chb-lb">头围(HC)</span>
              </label>
            </p>
            <div class="chart" id="chart-hc"></div>
          </div>
          <div class="chart-cont bdr">
            <p class="tl">
              <label for="gdfycqcs-rt-8">
                <input class="chb rt-sr-w check-handle" value="AC" type="checkbox" id="gdfycqcs-rt-8" rt-sc="pageId:gdfycqcszd1;name:腹围(AC);wt:4;desc:腹围(AC);vt:;pvf:1;">
                <span class="chb-lb">腹围(AC)</span>
              </label>
            </p>
            <div class="chart" id="chart-ac"></div>
          </div>
          <div class="chart-cont">
            <p class="tl">
              <label for="gdfycqcs-rt-9">
                <input class="chb rt-sr-w check-handle" value="FL" type="checkbox" id="gdfycqcs-rt-9" rt-sc="pageId:gdfycqcszd1;name:股骨长(FL);wt:4;desc:股骨长(FL);vt:;pvf:1;">
                <span class="chb-lb">股骨长(FL)</span>
              </label>
            </p>
            <div class="chart" id="chart-fl"></div>
          </div>
          <div class="chart-cont bdr bdt" v-show="dataType === 2">
            <p class="tl">
              <label for="gdfycqcs-rt-10">
                <input class="chb rt-sr-w check-handle" value="EFW" type="checkbox" id="gdfycqcs-rt-10" rt-sc="pageId:gdfycqcszd1;name:体重(EFW);wt:4;desc:体重(EFW);vt:;pvf:1;">
                <span class="chb-lb">体重(EFW)</span>
              </label>
            </p>
            <div class="chart" id="chart-efw"></div>
          </div>
        </div>
        <div class="tip">注：胎儿生长曲线采用中国胎儿生长数据（香港中文大学所发表文献）</div>
      </div>
    </div>
  </li>
</ul>