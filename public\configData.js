window.configData = {
  // 接口名称
  interfaceName: 'cloudpacsApi',
  // 科室可用\n体现换行
  // 纯音科室
  departmentNamecy: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  // 纯音类副标题
  subTitlecy: '纯音测听报告',

  // 言语类科室
  departmentNameyy: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //言语类副标题
  subTitleyy: '言语测听',

  // 鼓膜穿孔报告报告科室
  departmentNameck: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //咽鼓管报告副标题
  subTitleck: '声导抗鼓膜穿孔报告',

  // 鼓膜完整报告科室
  departmentNamewz: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //鼓膜完整报告副标题
  subTitlewz: '声导抗鼓膜完整报告',

  // 声导抗样本科室
  departmentNamesd: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //声导抗样本副标题
  subTitlesd: '中耳分析报告',
 
  // 宽频声导抗科室
  departmentNamekp: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //宽频声导抗副标题
  subTitlekp: '耳声发射报告',

  // 宽频硬化声导抗科室
  departmentNamekpyh: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //宽频硬化声导抗副标题
  subTitlekpyh: '宽频中耳测试报告',
  
  // 精细化耳鸣科室
  departmentNamejxherm: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //精细化耳鸣副标题
  subTitlejxherm: '精细化耳鸣检查报告',

  // 视频眼震电图（VNG）科室
  departmentNameVNG: '耳鼻咽喉头颈外科听力及前庭功能检查中心',
  //视频眼震电图（VNG）副标题
  subTitleVNG: '视频眼震电图（VNG）报告',

  // 设备列表
  deviceList: [
    {name: 'Madsen Astera 1066'},
  ],

  // 检查技师
  examDoctorList: [
    {name: '范素晓', inputCode: 'fsx'},
    {name: '林炎玲', inputCode: 'lyl'},
    {name: '张心苑', inputCode: 'zxy'},
    {name: '李润婷', inputCode: 'lrt'},
    {name: '陈悦玲', inputCode: 'cyl'},
  ],
  // 报告医生
  reportDoctorList: [
    {name: '万建', inputCode: 'wj'},
  ],
  
  // https端口
  httpsPort: '443',
  // http端口
  httpPort: '8000',

  // 听力报告logo
  logoReportTypeMap: {
    // 省二
    'C001': 'pacs-logo.png',
    'C002': 'pacs-logo.png',
    'C003': 'pacs-logo.png',
    'C004': 'pacs-logo.png',

    // 福州市第二总医院 作废，使用V2版本
    'C005': 'pacs-logo2.png',
    'C006': 'pacs-logo2.png',
    'C007': 'pacs-logo2.png',
    'C008': 'pacs-logo2.png',

    // 福州市第二总医院妇幼保健院 作废，使用V2版本
    'C009': 'pacs-logo3.png',
    'C010': 'pacs-logo3.png',
    'C011': 'pacs-logo3.png',
    'C012': 'pacs-logo3.png',
  },
}