#fjg1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#fjg1 .fjg-content{
  min-height: 100%;
  padding: 8px 12px;
  padding-bottom: 0;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#fjg1 .c-item{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
#fjg1 .bro-w{
  display: flex;
  align-items: center;
  border: 1px solid #C8D7E6;
  padding: 4px 10px;
  background: #EBEEF5;
}
#fjg1 input[type="text"]{
  border: 1px solid #DCDFE6;
  width: 60px;
  padding-left: 6px;
}
#fjg1 .bro-n{
  width: 100%;
  margin-top: 8px;
}
#fjg1 .cdfi-title{
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  width: 92px;
  background: #EBEEF5;
  border-right: 1px solid #C8D7E6;
}
#fjg1 .cdfi-title .w-con{
  width: 100%;
  padding-left: 12px;
}
#fjg1 .cdfi-title .w-con:hover{
  background-color: #E4E7ED;
}
#fjg1 .cdfi-content{
  padding:8px 12px;
}
#fjg1 input[type="radio"],#fjg1 .bro-n input[type="checkbox"],#fjg1 .cdfi-title input[type="checkbox"]{
  vertical-align: middle;
}
#fjg1 .to-cd{
  cursor: pointer;
}
#fjg1 label+label{
  margin-left: 30px;
}
#fjg1 .c-item+.c-item{
  margin-top: 12px;
}
#fjg1 .wy label+label{
  margin-left: 30px;
}
#fjg1 label{
  min-width: 80px;
}
#fjg1 .ml-10{
  margin-left: 10px;
}
#fjg1 .ml-14{
  margin-left: 14px;
}
#fjg1 .ml-30{
  margin-left: 30px;
}
#fjg1 .ml-36{
  margin-left: 36px;
}
#fjg1 .ml-44{
  margin-left: 44px;
}
#fjg1 .ml-50{
  margin-left: 50px;
}
#fjg1 .ml-54{
  margin-left: 54px!important;
}
#fjg1 .mtb-12{
  margin: 12px 0;
}