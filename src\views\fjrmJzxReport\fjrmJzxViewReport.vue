<template>
  <div :class="['fjrmJzxViewReport','main-page',`${printFlag ?'print-status':''}`,`${viewFlag==='1' ?'view-status':''}`]" v-loading="createPdfFlag">
    <disease-layout :localQueryParams="localQueryParams" :patientInfo="patientInfo" class="printContent" :showFooter="!printFlag && sourceType !== '1' && !isFromYunPacs" pageTitle="甲状腺超声结构化报告" :readonly="true" footTitle="">
      <div :id="parDomId" slot="body">
        <div class="print-page">
          <!-- 头部区域 -->
          <div class="rx-print-header">
            <div class="fjrmJzx-header">
              <div class="fjrmJzx-hos">福建省人民医院健康体检中心</div>
              <div class="fjrmJzx-rpt-title">甲状腺超声结构化报告</div>
            </div>
            <!-- 患者信息 -->
            <div class="patient-info">
              <div class="pat-item">
                <span class="light-gray-txt">姓名：</span>
                <span class="pat-item-val">{{totalRptInfo.name || patientInfo.name}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt">性别：</span>
                <span class="pat-item-val">{{totalRptInfo.sex || patientInfo.sex}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt">年龄：</span>
                <span class="pat-item-val">{{totalRptInfo.age || patientInfo.age}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt w4t">检查日期：</span>
                <span class="pat-item-val">{{totalRptInfo.examDate || '-'}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt">检查号：</span>
                <span class="pat-item-val">{{totalRptInfo.patLocalId || patientInfo.patLocalId || '-'}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt">条码号：</span>
                <span class="pat-item-val">{{totalRptInfo.outpatientNo || patientInfo.outpatientNo || '-'}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt">检查设备：</span>
                <span class="pat-item-val">{{totalRptInfo.device || '-'}}</span>
              </div>
              <div class="pat-item">
                <span class="light-gray-txt">检查部位：</span>
                <span class="pat-item-val">{{totalRptInfo.examOrganNames || totalRptInfo.organNames || '-'}}</span>
              </div>
              <div class="pat-item" style="width:100%;">
                <span class="light-gray-txt">检查项目：</span>
                <span class="pat-item-val">{{totalRptInfo.examItemNames || '-'}}</span>
              </div>
            </div>
          </div>
          <div class="print-body rx-print-body">
            <!-- 异常且有病灶的区域 -->
            <div class="rx-abnormal">
              <!-- 病灶示意图 -->
              <div class="bz-flag-img">
                <div v-show="allTabData && (allTabData.bzDescription || allTabData.jzxInfoStr || allTabData.shInfoStr)
                  || allTabData && allTabData.lbjInfoStr && lbStatus !=='可见正常'"
                >
                  <span class="bold-txt">病灶示意图：</span>
                </div>
                <div class="bz-img">
                  <!-- 甲状腺 -->
                  <div class="v-jzx-img" v-show="allTabData && (allTabData.bzDescription || allTabData.jzxInfoStr || allTabData.shInfoStr)">
                    <div class="v-jzx-box">
                      <img :src="`${publicPath}template-lib/assets/images/xmJzx/jzx-normal.png`" alt="" id="v-jzx-img" v-if="!allTabData || !allTabData.bzImageName">
                      <img :src="`${publicPath}template-lib/assets/images/xmJzx/${allTabData.bzImageName}`" alt="" id="v-jzx-img" v-else>
                      <span 
                        v-for="(bzItem, bz) in jzxPointList" :key="bz" 
                        v-if="!['0','1'].includes(bzItem.level)"
                        class="v-jzx-point" :style="bzItem.style">{{bzItem.bzIndex}}</span>
                    </div>
                    <img :src="`${publicPath}template-lib/assets/images/xmJzx/jzx-mark.png`" alt="">
                  </div>
                  <!-- 淋巴结 -->
                  <div class="v-lbj-img" v-show="allTabData && allTabData.lbjInfoStr && lbStatus !=='可见正常'">
                    <img id="v-lbjImage" :src="`${publicPath}template-lib/assets/images/xmJzx/lbj.png`" alt="" style="display:none">
                    <canvas id="v-lbjCanvas" width="180" height="180"></canvas>
                  </div>
                </div>
              </div>

              <!-- 超声描述 病灶列表-->
              <div class="bz-list" v-if="(bzFormatList && bzFormatList.length) || (allTabData && allTabData.lbjInfoStr && allTabData.lbjInfoObj)">
                <div class="bold-txt">超声描述：</div>
                <div class="bz-table" v-if="bzFormatList && bzFormatList.length">
                  <table border="1">
                    <thead class="ignore-head">
                      <tr class="follow-break">
                        <th class="follow-break" width="45">病灶编号</th>
                        <th class="follow-break" colspan="3">位置</th>
                        <!-- <th width="78">距包膜最小距离约(mm)</th> -->
                        <th class="follow-break" width="100">大小(mm)<br>长×厚×(宽)</th>
                        <th class="follow-break" width="80">回声</th>
                        <th class="follow-break" width="60">方位</th>
                        <th class="follow-break" width="60">边缘</th>
                        <th class="follow-break" width="110">局灶性强回声</th>
                        <th class="follow-break" width="50">结构</th>
                        <th class="follow-break" width="50">血流</th>
                        <th class="follow-break" width="50">C-TIRADS</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(bzItem, i) in bzFormatList">
                        <td>
                          <span class="bz-circle" :style="{'background':bzItem.bgStyle}">{{i+1}}</span>
                        </td>
                        <td width="50">{{bzItem.posSide}}</td>
                        <td width="50">{{bzItem.posTorB}}</td>
                        <td width="140">{{bzItem.posOther}}</td>
                        <!-- <td>{{bzItem.bmLen?bzItem.bmLen.replace(/mm/ig, ''):''}}</td> -->
                        <td>{{bzItem.bzSize?bzItem.bzSize.replace(/mm/ig, ''):''}}</td>
                        <td>{{bzItem.hs}}</td>
                        <td>{{bzItem.fw}}</td>
                        <td>{{bzItem.by}}</td>
                        <td>{{bzItem.jzxQhs}}</td>
                        <td>{{bzItem.jg}}</td>
                        <td>{{bzItem.xl}}</td>
                        <td>{{bzItem.level || '-'}}类</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="bz-table" v-if="allTabData && allTabData.lbjInfoStr && ['可见异常', '可见正常'].includes(lbStatus)">
                  <table border="1">
                    <thead class="ignore-head">
                      <tr class="follow-break">
                        <th class="follow-break" width="90" colspan="2" rowspan="2">淋巴结位置</th>
                        <th class="follow-break" width="90" rowspan="2">
                          <span v-if="allTabData.lbjInfoObj.lbjHighData.length>1">较大者(mm)</span>
                          <span v-else>大小(mm)</span>
                          <br>长×厚×(宽)
                        </th>
                        <th class="follow-break" width="60" rowspan="2">形态</th>
                        <th class="follow-break" width="60" rowspan="2">边缘</th>
                        <th class="follow-break" width="60" rowspan="2">淋巴门</th>
                        <th class="follow-break" width="110" colspan="2">皮质</th>
                        <th class="follow-break" width="60" rowspan="2">钙化</th>
                        <th class="follow-break" width="60" rowspan="2">血流</th>
                      </tr>
                      <tr class="follow-break">
                        <th class="follow-break" width="50">厚度</th>
                        <th class="follow-break" width="50">回声</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td width="55" :colspan="lbStatus === '可见正常' && 2">{{allTabData.lbjInfoObj.lbPos}}{{allTabData.lbjInfoObj.lbPos === '双侧颈部' || allTabData.lbjInfoObj.lbCount==='多发'?'(多发)':''}}</td>
                        <td v-if="lbStatus === '可见异常'">
                          <span v-if="allTabData.lbjInfoObj.dbArea">{{ allTabData.lbjInfoObj.dbArea }}</span>
                          <span v-else>
                            {{allTabData.lbjInfoObj.lbArea}}
                            <span 
                              v-if="allTabData.lbjInfoObj.lbjHighData.length===1 && 
                                (allTabData.lbjInfoObj.lbCount==='多发' || allTabData.lbjInfoObj.lbPos === '双侧颈部')">
                              (较大)
                            </span>
                          </span>
                        </td>
                        <td>{{allTabData.lbjInfoObj.lbSize?allTabData.lbjInfoObj.lbSize.replace(/mm/ig, ''):'—'}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjXt}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjBy}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjLbm}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjPzhd}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjPzhs}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjDzgh}}</td>
                        <td>{{allTabData.lbjInfoObj.lbjXllx}}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 乳腺描述和诊断 -->
            <div class="rx-content">
              <!-- 超声描述 具体文本 -->
              <div class="fjrmJzx-descript">
                <div class="bold-txt" v-if="!((bzFormatList && bzFormatList.length) || (allTabData && allTabData.lbjInfoStr && ['可见异常', '可见正常'].includes(lbStatus)))">
                  超声描述：
                </div>
                <div class="description" v-if="resetDescription" v-html="resetDescription"></div>
              </div>
              <!-- 超声意见 -->
              <div class="fjrmJzx-impress">
                <div class="bold-txt title-break">超声意见：</div>
                <div class="impression" v-if="impression" v-html="impression"></div>
              </div>
              <!-- 超声图像 -->
              <div class="fjrmJzx-rpt-img" v-if="rptImageList && rptImageList.length">
                <div class="bold-txt title-break avoid-break">超声图像：</div>
                <div class="rpt-img-list">
                  <div class="rpt-img-item load-img" v-for="(img, i) in rptImageList">
                    <!-- <div class="light-gray-txt-sm">病灶{{i+1}}：</div> -->
                    <div class="rpt-img follow-break">
                      <img class="follow-break" :src="img.srcBase64 || img.src" alt="">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 报告日期和医生 -->
          <div class="rpt-date-and-name rx-print-footer">
            <div class="rpt-doctor-wrap">
              <div class="rpt-doctor" v-if="entryPeopleUserId">
                <span class="light-gray-txt">录入员：</span>
                <img class="sign-img" :src="`${getSignImageUrl}?userId=${entryPeopleUserId}`" @error="imgErrorHandler">
              </div>
              <div class="rpt-doctor" v-if="totalRptInfo && totalRptInfo.priReporterUserId">
                <span class="light-gray-txt">报告医生：</span>
                <img class="sign-img" :src="`${getSignImageUrl}?userId=${totalRptInfo.priReporterUserId}`" @error="imgErrorHandler">
              </div>
              <div class="rpt-doctor" v-if="totalRptInfo && totalRptInfo.affirmReporterUserId">
                <span class="light-gray-txt">审核医生：</span>
                <img class="sign-img" :src="`${getSignImageUrl}?userId=${totalRptInfo.affirmReporterUserId}`" @error="imgErrorHandler">
              </div>
            </div>
            <div class="rpt-data-wrap">
              <div>本报告仅供医师诊疗参考，对外不能代作证明书之用</div>
              <div class="rpt-date">
                <span class="light-gray-txt">检查日期：</span>
                <span class="black-txt">{{totalRptInfo.examDate}}</span>
              </div>
              <div class="rpt-date">
                <span class="light-gray-txt">报告日期：</span>
                <span class="black-txt">{{totalRptInfo.reportDate}} {{totalRptInfo.reportTime?totalRptInfo.reportTime.slice(0,5):''}}</span>
              </div>
            </div>
          </div>
        </div>
        <div :id="domId" class="view-none">
          <!-- 主html -->
          <v-html-panel 
            :htmlUrl.sync="jzxHtmlUrl" 
            @loaded="htmlLoadedHandler"
          ></v-html-panel>
        </div>
      </div>
      <div slot="footer-btn" v-if="!createPdfFlag && !isFromYunPacs && !sourceType">
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue ml-12 allow-et"
          icon="el-icon-position"
          @click="submitForm"
        >确认</el-button>
        <el-button
          size="mini"
          type="primary"
          class="btn-reset btn-blue allow-et"
          icon="el-icon-edit"
          @click="editForm"
        >编写</el-button>
      </div>
    </disease-layout>
  </div>
</template>

<script>
import '@/assets/css/diseaseReport.scss'
import api from '@/config/api.js';
import diseaseLayout from '@/components/diseaseLayout.vue';
import vHtmlPanel from '@/components/vHtmlPanel.vue';
import diseaseReportMixin from '@/mixins/newDiseaseReport.js'
import '../../../public/template-lib/utils/structFun2.0.js'
import '../../../public/template-lib/views/srTemplate/fjrmJzxTemplate/fjrmJzxData.js'
import * as constant from '@/config/constant.js'
import { getLocalStorage } from '@/utils/common.js';
import $ from 'jquery'
export default {
  name: "fjrmJzxViewReport",
  components: {diseaseLayout, vHtmlPanel},
  mixins: [diseaseReportMixin],
  data() {
    return {
      parDomId: 'fjrmJzxViewReport',
      domId: 'tempViewReport',
      prefixName: "fjrmJzx-",
      createPdfFlag: false,
      resetDescription: '',
      impression: '',
      resultFormData: [],
      patientInfo: {
        sex: ''
      },
      jzxPointList: [],
      bzFormatList: [],  //处理后的数据
      printFlag: false,  //是否打印
      viewFlag: 0,  //是否只显示报告模板部分
      getSignImageUrl: api.getSignImage,
      publicPath: process.env.BASE_URL,
      print: '',
      allTabData: {},  //所有tab的信息，基本信息、术后、病灶、淋巴
      jzxHtmlUrl: process.env.BASE_URL + 'template-lib/views/srTemplate/fjrmJzxTemplate/fjrmJzxTemplate.html',
      isDevelopment: process.env.NODE_ENV === 'development',
      canvasLbEle: null,
      lbjCtx: null,
      lbjImage: null,
      entryPeopleUserId: '',  //录入人员
      entryPeopleUserName: '',
    }
  },
  watch: {
    $route() {
      window.location.reload();
    },
    printFlag: {
      async handler(newVal) {
        if(newVal) {
          if(this.print !== '2') {
            return;
          }
          this.$nextTick(async() => {
            this.printPageBreakHandler();
            await this.loadAllImages('#'+this.parDomId + ' img');
            window.print();
            // this.toPDF($('#' + this.parDomId)[0], true);
            setTimeout(() => {
              this.resetDocStyle();
              this.printFlag = false;
              if(this.isFromYunPacs) {
                window.top.postMessage({
                  message: 'srPrintFinished',
                  data: {printFlag:false}
                }, '*');
              }
            }, 50);
          });
        }
      },
      immediate: true
    },
    rptImageList: {
      handler(newVal) {
        newVal.forEach(async(img) => {
          this.imgToBase64(img)
        })
      },
      immediate: true,
      deep: true
    },
  },
  computed: {
    token() {
      return this.$store.state.token || getLocalStorage(constant.RT_SESSION_TOKEN) || '';
    },
    rptImageList() {
      return this.$store.state.report.rptImageList;
    },
    ypacsRptInfo() {
      return this.$store.state.report.ypacsRptInfo;
    },
    baseRptInfo() {
      return this.$store.state.report.baseRptInfo || {};
    },
    totalRptInfo() {
      return {...this.baseRptInfo, ...this.ypacsRptInfo}
    },
    isFromYunPacs() {
      return this.$store.state.isFromYunPacs || this.localQueryParams.fromYunPacs==='1' || this.$route.query.fromYunPacs==='1';
    },
    lbStatus() {
      return this.allTabData && this.allTabData.lbjInfoObj ? this.allTabData.lbjInfoObj.lbStatus : '';
    },
  },
  created() {
    this.createPdfFlag = true;
    this.viewFlag = this.localQueryParams.viewRpt || this.localQueryParams.pv || 0;
    this.print = this.localQueryParams.print;
  },
  async mounted() {
    this.initCanvas();
    window.initPageHandler = this.initPageHandler;
    await this.getPatientInfo();
    // setTimeout(() => {
    //   this.pageInit();
    // }, 100);
  },
  // destroyed() {
  //   window.location.reload();
  // },
  methods: {
    htmlLoadedHandler(html) {
      if(window.loadTemplateScriptAndCss) {
        window.loadTemplateScriptAndCss(html);
      }
      setTimeout(() => {
        this.pageInit();
      }, 200)
    },
    async pageInit() {
      if(!this.patientInfo.examNo || this.patientInfo.examNo==='undefined') {
        this.$message.error('examNo必传');
        this.createPdfFlag = false;
        return;
      }
      this.resultFormData = await this.getData(this.patientInfo.examNo, 'view');
      if(!this.resultFormData || this.resultFormData.length === 0) {
        this.createPdfFlag = false;
        return;
      }
      let params = { examNo: this.patientInfo.examNo, fileType: 'RptImg' };
      console.log('view是否嵌入云pacs：', this.isFromYunPacs);
      await this.getExamInfoForSR();
      await this.getReportDoc();
      this.getEnrtyUser();
      await this.$store.dispatch('report/getReportImageList', { params, vm: this });
      if(this.isFromYunPacs) {
        window.top.postMessage({
          message: 'srLoadSuccess',
          data: {}
        }, '*');
      }
      this.$nextTick(async () => {
        let htmlParam = {
          type: 'edit', 
          data: this.resultFormData, 
          mainId: '.fjrmJzxViewReport', 
          srcUrl: !this.isDevelopment ? '/sreport' : ''
        }
        await this.loadedHandler(htmlParam);
      })
    },

    // 提取填过的录入员
    getEnrtyUser() {
      let lryStr = this.findValByIdCommon(this.resultFormData, 'jzx-lry');
      if(lryStr) {
        let [id, name] = lryStr.split('/');
        this.entryPeopleUserId = id;
        this.entryPeopleUserName = name;
      } else {
        this.entryPeopleUserId = this.totalRptInfo.otherInfo ? this.totalRptInfo.otherInfo.entryPeopleUserId : '';
        this.entryPeopleUserName = this.totalRptInfo.otherInfo ? this.totalRptInfo.otherInfo.entryPeople : '';
      }
    },
    
    initPageHandler() {
      this.$nextTick(() => {
        this.detailDataHandler();
        if(this.print !== '1' || this.isFromYunPacs) {
          this.createPdfFlag = false;
        }
        setTimeout(() => {
          if(['1', '3'].includes(this.print)) {
            this.submitForm();
          }
          if(this.print === '2') {
            this.printFlag = true;
          }
        }, 200)
      })
    },
    // 处理详情数据,超声描述、诊断
    detailDataHandler() {
      if(this.allContent && this.allContent.docContent) {
        let {impression = ''} = this.allContent.docContent;
        this.impression = impression;  //诊断
      }
      if(this.rtStructure) {
        this.allTabData = this.rtStructure.description || {};
        // 病灶点高亮
        this.jzxPointList = this.allTabData.jzxPointList || [];
        
        // 病灶列表
        let bzDetailList = this.allTabData.bzDetailList || [];
        if(bzDetailList && bzDetailList.length) {
          bzDetailList.map((bzItem, i) => {
            if((i+1) === this.jzxPointList[i].bzIndex) {
              bzItem.bgStyle = this.jzxPointList[i].style ? this.jzxPointList[i].style.background : '';
            }
          })
        }
        this.bzFormatList = bzDetailList;

        // 淋巴结高亮区域
        let lbjHighData = this.allTabData.lbjInfoObj && this.allTabData.lbjInfoObj.lbjHighData ? 
          this.allTabData.lbjInfoObj.lbjHighData : [];
        // this.initCanvas(lbjHighData);
        this.drawLbAreaHandler(lbjHighData);

        // 除表格以外的描述内容
        let resetArr = [];
        if(this.allTabData.jzxInfoStr) {
          // 甲状腺基本信息
          resetArr.push(this.allTabData.jzxInfoStr);
        }
        if(this.allTabData.shInfoStr) {
          // 甲状腺术后
          resetArr.push(this.allTabData.shInfoStr);
        }
        if(bzDetailList && bzDetailList.length) {
          // 病灶其他征象
          let bzOtherDesc = [];
          bzDetailList.forEach((bzItem, i) => {
            if(bzItem.otherDesc) {
              bzOtherDesc.push(`病灶${i+1}：${bzItem.otherDesc}。`);
            }
          })
          if(bzOtherDesc.length) {
            resetArr.push(bzOtherDesc.join('\n'));
          }
        }
        this.resetDescription = resetArr.join('\n');
        if($('#rmjz-rt-70').val()) {
          this.resetDescription += `${this.resetDescription ? '\n' : ''}` + $('#rmjz-rt-70').val();
        }
      }
    },
    imgToBase64(image) {
      this.blobToBase64(image.blob).then(res => {
        // 转化后的base64
        this.$set(image, 'srcBase64', res)
      })
    },
    // 初始化canvas
    initCanvas() {
      this.canvasLbEle = document.querySelector("#v-lbjCanvas");
      this.lbjCtx = this.canvasLbEle.getContext("2d");
      this.lbjImage = document.querySelector("#v-lbjImage");
      this.lbjImage.onload = () => {
        this.lbjCtx.drawImage(this.lbjImage, 0, 0, 180, 180); // 绘制画布
      }
    },
    drawLbAreaHandler(lbjHighData) {
      if(!lbjHighData || lbjHighData.length === 0) {
        return;
      }
      lbjHighData.forEach((lbjItem, i) => {
        if(lbjHighCoord[lbjItem]) {
          this.highLightOrgan(lbjHighCoord[lbjItem]);
        }
      })
    },

    // 高亮淋巴结部位
    highLightOrgan(curOrgan) {
      var ctx = this.lbjCtx;
      ctx.save();
      // 挡住默认的黑色字体
      if(curOrgan.textCord && curOrgan.textCord.length) {
        ctx.beginPath();
        ctx.fillStyle = '#FFF';
        ctx.rect(curOrgan.textCord[0],curOrgan.textCord[1],10,10);
        ctx.fill();
      }
      
      var coords = curOrgan.coords;
      if(coords && coords.length) {
        // 高亮背景和边框
        ctx.strokeStyle = curOrgan.strokeStyle; // 填充线颜色
        ctx.fillStyle = curOrgan.fillStyle; // 填充区域颜色
        ctx.lineWidth = 2;
        ctx.beginPath();
        for (var i = 0; i < coords.length; i++) {
          ctx.lineTo(coords[i][0], coords[i][1]);
        }
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
      }
      
      // 高亮部位文字
      if(curOrgan.textCord && curOrgan.textCord.length) {
        ctx.beginPath();
        ctx.fillStyle = curOrgan.strokeStyle;
        ctx.font="bold 9px Arial";
        ctx.textBaseline = "top";
        ctx.fillText(curOrgan.text, curOrgan.textCord[0], curOrgan.textCord[1]);
        ctx.restore();
      }
    },
    // 处理打印的分页
    async printPageBreakHandler() {
      let previewPage = $('#' + this.parDomId).find(`.print-page:eq(0)`).clone();  //原页面
      let printHtml = '<div class="print-page">' + previewPage.html() + '</div>';
      let params = {
        printHtml: printHtml,
        pageSelector:'.print-page', 
        headerSelector:'.rx-print-header', 
        footerSelector:'.rx-print-footer', 
        bodySelector:'.rx-print-body',
        showPageCounter: true
      }
      await this.rebuildPdfPage(params);
    },

    // 图像加载失败
    imgErrorHandler(vm) {
      $(vm.target).parents('.rpt-doctor').remove();
    },

    // 提交
    async submitForm(subParams) {
      // this.printPageBreakHandler();
      // return
      this.createPdfFlag = true;
      this.printFlag = true;
      await this.printPageBreakHandler();
      await this.loadAllImages('#'+this.parDomId + ' img');
      let pdfRes = await this.toPDF($('#' + this.parDomId)[0]);
      if(window.srSendMessage && typeof window.srSendMessage === 'function') {
        if(typeof pdfRes === 'object') {
          pdfRes.submitType = 'pdf';
          pdfRes.type = '30';
        }
        window.srSendMessage(pdfRes);
      }
      return 'pdf';
    },

    // 编写
    editForm() {
      this.srCommonOuterOptHandler({type: '4'})
    },

    resetDocStyle() {
      $('#'+this.parDomId).find('.page-clone').remove();
      $('#'+this.parDomId).find('.rt-page-body').removeClass('rt-page-body').removeAttr('total-height');
      $('#'+this.parDomId).find('.rt-print-add-page').removeClass('rt-print-add-page');
      $('#'+this.parDomId).find('.rt-page-counter').removeClass('show');
      $('#'+this.parDomId).find('.print-body').css('height', 'unset');
      this.createPdfFlag = false;
      this.printFlag = false;
    }
  }
}
</script>
<style>
@page{
  margin: 0;
}
@page:first {
  margin-top: 0;
}
</style>
<style lang="scss">
@media print {
  body {
    -webkit-print-color-adjust: exact !important;   /* Chrome, Safari */
    color-adjust: exact !important;                 /*Firefox*/
  }
  html {
    overflow: unset !important;
  }
  #app {
    overflow: unset !important;
  }
}
// break-after: page;
.fjrmJzxViewReport { 
  font-family: PingFangSC-Regular, PingFang SC;
  .disease-body {
    background: #F5F7FA;
  }
  .widthAndAuto {
    width: 780px;
  }
  #fjrmJzxViewReport {
    width: 100%;
    height: 100%;
    * {
      box-sizing: border-box;
      word-break: break-all !important;
    }
    .view-none {
      display: none !important;
    }
    .print-page {
      width: 780px;
      height: unset;
      min-height: 1080px;
      background: #FFF;
      border: 1px solid #E6E6E6;
      position: relative;
      padding-bottom: 90px;
      .rx-print-header {
        padding: 0 56px
      }
      .print-body {
        padding: 0 56px;
      }
      .fjrmJzx-header {
        text-align: center;
        padding-top: 12px;
        margin-bottom: 10px;
        .fjrmJzx-hos {
          font-weight: bold;
          font-size: 24px;
          color: #000;
          margin-bottom: 8px;
        }
        .fjrmJzx-rpt-title {
          font-size: 18px;
          color: #000;
        }
      }
      .patient-info {
        padding-top: 6px;
        border-top: 1px solid #999;
        border-bottom: 1px solid #999;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;
        .pat-item {
          width: 25%;
          margin-bottom: 6px;
          display: flex;
          .pat-item-val {
            flex: 1;
            word-break: break-all;
            font-size: 14px;
            color: #000;
          }
        }
        .w3t {
          display: inline-block;
          width: 56px;
          text-align: right;
        }
        .w4t {
          display: inline-block;
          width: 70px;
          text-align: right;
        }
      }
      .bz-flag-img {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;
        .bz-img {
          display: flex;
          .v-jzx-img {
            display: flex;
            margin-right: 12px;
            img{
              height: 180px;
              position: relative;
              z-index: 2;
            }
          }
          .v-jzx-box{
            position: relative;
            .v-jzx-point{
              position: absolute;
              z-index: 2;
              width: 12px;
              height: 12px;
              background: #000;
              border-radius: 50%;
              border: 1px solid #000;
              text-align: center;
              font-size: 12px;
              line-height: 10px;
            }
          }
          .v-lbj-img{
            height: 180px;
            width: 180px;
            position: relative;
            z-index: 2;
          }
        }
      }
      .bz-list {
        margin-bottom: 8px;
        .bz-table {
          margin-top: 8px;
          table {
            border-collapse: collapse;
            width: 100%;
            border-top: none;
            border-color: #999;
            position: relative;
          }
          thead {
            page-break-inside: avoid;
            tr {
              page-break-inside: avoid;
              th {
                color: #909399;
                font-size: 12px;
                font-weight: normal;
                padding: 2px;
                page-break-inside: avoid;
                height: 22px;
              }
            }
          }
          tbody {
            page-break-inside: avoid;
            tr {
              height: 48px;
              color: #303133;
              font-size: 12px;
              page-break-inside: avoid;
              td {  
                text-align: center;
                padding: 0 3px;
                page-break-inside: avoid;
              }
            }
            .bz-circle {
              width: 24px;
              height: 24px;
              line-height: 24px;
              display: inline-block;
              border-radius: 50%;
              border: 1px solid #000;
            }
            .bz-angle {
              border-radius: 0;
              border-left: 12px transparent solid;
              border-right: 12px transparent solid;
              border-bottom: 24px #303133 solid;
              border-top: 0 transparent solid;
              background: none !important;
              .ft {
                text-align: left;
                margin-left: -3px;
                margin-top: 2px;
                display: block;
              }
            }
          }
        }
      }
      .fjrmJzx-descript {
        margin-bottom: 12px;
        // min-height: 304px;
        .description {
          word-break: break-word !important;
          color: #333;
          white-space: pre-wrap;
          font-size: 14px;
          line-height: 24px;
          // page-break-inside: avoid;
        }
      }
      .fjrmJzx-impress {
        margin-bottom: 12px;
        // min-height: 206px;
        // page-break-inside: avoid;
        .impression {
          margin-top: 8px;
          word-break: break-word !important;
          color: #333;
          white-space: pre-wrap;
          font-size: 14px;
          line-height: 24px;
        }
      }
      .fjrmJzx-rpt-img {
        margin-bottom: 12px;
        .rpt-img-list {
          // display: flex;
          // flex-wrap: wrap;
          .rpt-img-item {
            display: inline-block;
            margin-top: 8px;
            page-break-inside: avoid;
          }
          .rpt-img {
            width: 160px;
            height: 120px;
            margin-right: 32px;
            background: #000;
            margin-top: 8px;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }
      }
      .rpt-date-and-name {
        position: absolute;
        bottom: 10px;
        height: 90px;
        left: 56px;
        right: 56px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .rpt-doctor-wrap {
          display: flex;
          justify-content: flex-end;
          border-bottom: 1px solid #999;
          padding-bottom: 4px;
          margin-bottom: 4px;
          .rpt-doctor {
            width: 32%;
            font-size: 14px !important;
            & + .rpt-doctor {
              margin-left: 12px;
            }
            .sign-img {
              width: 100px;
              height: 32px;
              vertical-align: middle;
              object-fit: contain;
            }
          }
        }
        .rpt-data-wrap {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          .rpt-date {
            span {
              font-size: 12px !important;
            }
            & + .rpt-date {
              margin-left: 12px;
            }
          }
        }
      }
      .bold-txt {
        font-size: 18px;
        font-weight: 600;
        color: #3D3D3D;
      }
      .black-txt {
        font-size: 14px;
        color: #000;
      }
      .black-txt-sm {
        font-size: 12px;
        color: #000;
      }
      .gray-txt {
        font-size: 14px;
        color: #333;
      }
      .light-gray-txt {
        font-size: 14px;
        color: #606266;
      }
      .light-gray-txt-sm {
        font-size: 12px;
        color: #606266;
      }
    }
  }
  &.view-status {
    min-width: unset;
    .printContent {
      overflow: unset;
    }
    .disease-header {
      display: none;
    }
    .disease-body {
      padding: 12px;
      overflow: unset;
    }
  }
  &.print-status {
    min-width: unset;
    .printContent {
      overflow: unset;
    }
    .disease-header {
      display: none;
    }
    .disease-body {
      padding: 0;
      overflow: unset;
      background: #fff;
    }
    .print-page {
      border: none !important;
    }
    .fjrmJzx-logo {
      margin-left: -5px !important;
      margin-right: -5px !important;
      width: 790px !important;
    }
    .bz-table thead th {
      color: #666 !important;
    }
    .page-counter {
      position: absolute;
      font-size:12px;
      color:#000;
      right:20px;
      font-weight: bold;
    }
  }
}
</style>