$(function() {
  
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initVueComp() {
  curElem.find('.tr-content:not(".total-tr")').each(function(i, trItem) {
    let setVal = $(this).find('.table-center .rt-sr-w:checked').length;   //判断是否填了值
    if(!setVal) {
      $(this).hide();
    }
  })
  // curElem.find('.xzcsfj-view [data-key]').each(function(){
  //   var key = $(this).attr('data-key');
  //   var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
  //   if(key === 'r-xz-land'){
  //     var name = ['正常','超过正常值2个标准差(2SD)'][parseInt($('#xzcsfj-rt-4').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-mjb'){
  //     var name = ['<1/3','>1/3且＜0.5','>0.5'][parseInt($('#xzcsfj-rt-9').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-ssgn'){
  //     var name = ['胎儿心室短轴缩短率≥0.3','0.2＜胎儿心空轴缩短率＜0.3','胎儿心空短轴笔短率0.2'][parseInt($('#xzcsfj-rt-14').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-sjbxl'){
  //     var name = ['无反流','反流面积与右房面积比值≤0.25','反流面积与右房面积比值＞0.25'][parseInt($('#xzcsfj-rt-19').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-ejbxl'){
  //     var name = ['无反流','反流面积与左房面积比值≤0.25','反流面积与左房面积比值＞0.25'][parseInt($('#xzcsfj-rt-24').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-sjbsz'){
  //     var name = ['双峰','单峰'][parseInt($('#xzcsfj-rt-28').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-ejbsz'){
  //     var name = ['双峰','单峰'][parseInt($('#xzcsfj-rt-32').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-jmdg'){
  //     var name = ['正常','A 波减低','A 波达基线或反向'][parseInt($('#xzcsfj-rt-37').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-mjmp'){
  //     var name = ['正常','搏动征'][parseInt($('#xzcsfj-rt-41').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-fdm'){
  //     var name = ['内径大于主动脉的内径','内径等于主动脉的内径','内径小于主动脉的内径','右室流出道校阻'][parseInt($('#xzcsfj-rt-47').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-fdmxl'){
  //     var name = ['无反流','有反流'][parseInt($('#xzcsfj-rt-51').text())];
  //     $(this).html(name)
  //   }else if(key === 'r-xz-jdmpp'){
  //     var name = ['正常','舒张期血流减少','舒张期血流缺失或反向'][parseInt($('#xzcsfj-rt-51').text())];
  //     $(this).html(name)
  //   }else{
  //     $(this).html(publicInfo[key] || idAnVal)
  //   }
    
  // })
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#xzcsfj1 .xzcsfj-edit'),  //转成pdf的区域，默认是整个页面
      // dignosisImgs: [], //诊断图像，若不存在该字段，则以整个页面转成图传给pacs
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      // 在预览界面将勾选的诊断图转成base64字符串传入dignosisImgs
      // 示例：rtStructure.diffConfig.dignosisImgs=[{imgContent: "base64字符串", imgName: "勾选的部位图名称.jpg"}]
      initVueComp();
    } else {
      initPage();
    }
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

// 初始化
function initPage() {
  getRadioChecked()
}

/**
 * 获取单选选中内容
 * */

 function getRadioChecked() {
  let target = $('#xzcsfj-rt-57');
  let radioList = $('input[type="radio"]');
  if (target) {
    radioList.on('change', function() {
      if (this.checked) {
        let value = $(this).attr('data-value') || 0;
        $(this).closest('.tr-content').find('.table-right.td-label').text(value);
      }
      let total = parseInt($('#xzcsfj-rt-4').text()) + parseInt($('#xzcsfj-rt-9').text()) + parseInt($('#xzcsfj-rt-14').text()) + parseInt($('#xzcsfj-rt-19').text()) + parseInt($('#xzcsfj-rt-24').text()) + parseInt($('#xzcsfj-rt-28').text()) + parseInt($('#xzcsfj-rt-32').text())+ parseInt($('#xzcsfj-rt-37').text()) + parseInt($('#xzcsfj-rt-41').text()) + parseInt($('#xzcsfj-rt-47').text()) + parseInt($('#xzcsfj-rt-51').text()) + parseInt($('#xzcsfj-rt-56').text());
      $('#xzcsfj-rt-57').text(total + '(分)')
    })
    // radioList.each(function () {
    //   this.addEventListener('change', (e) => {
    //     radioList.each(function (e) {
    //       let checked = this.checked
    //       if (checked) {
    //         // console.log('value',`${this.label}`)

    //         if(this.name === 'r-xz-land'){
    //           $('#xzcsfj-rt-4').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-mjb'){
    //           $('#xzcsfj-rt-9').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-ssgn'){
    //           $('#xzcsfj-rt-14').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-sjbxl'){
    //           $('#xzcsfj-rt-19').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-ejbxl'){
    //           $('#xzcsfj-rt-24').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-sjbsz'){
    //           $('#xzcsfj-rt-28').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-ejbsz'){
    //           $('#xzcsfj-rt-32').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-jmdg'){
    //           $('#xzcsfj-rt-37').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-mjmp'){
    //           $('#xzcsfj-rt-41').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-fdm'){
    //           $('#xzcsfj-rt-47').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-fdmxl'){
    //           $('#xzcsfj-rt-51').text(`${this.value}`);
    //         }else if(this.name === 'r-xz-jdmpp'){
    //           $('#xzcsfj-rt-56').text(`${this.value}`);
    //         }
    //         let total = parseInt($('#xzcsfj-rt-4').text()) + parseInt($('#xzcsfj-rt-9').text()) + parseInt($('#xzcsfj-rt-14').text()) + parseInt($('#xzcsfj-rt-19').text()) + parseInt($('#xzcsfj-rt-24').text()) + parseInt($('#xzcsfj-rt-28').text()) + parseInt($('#xzcsfj-rt-32').text())+ parseInt($('#xzcsfj-rt-37').text()) + parseInt($('#xzcsfj-rt-41').text()) + parseInt($('#xzcsfj-rt-47').text()) + parseInt($('#xzcsfj-rt-51').text()) + parseInt($('#xzcsfj-rt-56').text());
    //         $('#xzcsfj-rt-57').text(total + '(分)')
    //         initVueComp();
    //       }
    //     })
        
    //   })
    // })
  }
}