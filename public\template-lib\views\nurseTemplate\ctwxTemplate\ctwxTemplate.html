<link rel="stylesheet" href="/template-lib/utils/common/common.css">
<link rel="stylesheet" href="/template-lib/utils/layerMsg/layerMsg.css">
<link rel="stylesheet" href="/template-lib/views/nurseTemplate/ctwxTemplate/ctwxTemplate.css">
<link rel="stylesheet" href="/template-lib/plugins/layui/layui.min.css">
<script src="/template-lib/plugins/layui/layui.js"></script>
<script src="/template-lib/plugins/jquery.min.js"></script>
<script src="/template-lib/controls/api.js"></script>
<script src="/template-lib/utils/common/common.js"></script>
<script src="/template-lib/utils/layerMsg/layerMsg.js"></script>
<script src="/template-lib/views/nurseTemplate/ctwxTemplate/ctwxTemplate.js"></script>

<ul class="t-pg" style="height: 100%;overflow: auto;">
  <li class="page" id="ctwx1">
    <div class="ct-content">
      <div class="ct-title">检查病史</div>
      <div class="allergy-content">
        <div class="second-title rt-sr-w" id="ctwx-rt-001" rt-sc="pageId:ctwx1;name:过敏史及禁忌症;wt:;desc:过敏史及禁忌症;vt:;pvf:1;">过敏史</div>
        <div class="allergy-item">
          <label class="radio-label" for="ctwx-rt-1">
            <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-jdmpp" value="无过敏史" data-value="0" id="ctwx-rt-1" pid="ctwx-rt-001" rt-sc="pageId:ctwx1;name:无过敏史;wt:5;desc:无过敏史;vt:;pvf:;" checked="">
            <span class="rt-sr-lb">无</span>
          </label>
          <label class="radio-label" for="ctwx-rt-2">
            <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-jdmpp" value="有过敏史" data-value="1" id="ctwx-rt-2" pid="ctwx-rt-001" rt-sc="pageId:ctwx1;name:有过敏史;wt:5;desc:有过敏史;vt:;pvf:;">
            <span class="rt-sr-lb">有</span>
          </label>
          <label class="radio-label" for="ctwx-rt-49">
            <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-jdmpp" value="不详" data-value="2" pid="ctwx-rt-001" rt-sc="pageId:ctwx1;name:不详;wt:5;desc:不详;vt:;pvf:;" id="ctwx-rt-49">
            <span class="rt-sr-lb">不详</span>
          </label>
        </div>
        <div class="second-content">
          <div class="gray-item">
            <label class="radio-label" for="ctwx-rt-3">
              <input type="checkbox" class="rt-sr-w def-ck" name="ck-jdmpp" value="食物过敏" id="ctwx-rt-3" rt-sc="pageId:ctwx1;name:食物过敏;wt:4;desc:食物过敏;vt:;pvf:;" pid="ctwx-rt-2">
              <span class="rt-sr-lb">食物过敏</span>
            </label>
            <label class="radio-label" for="ctwx-rt-4">
              <input type="checkbox" class="rt-sr-w" name="ck-jdmpp" value="药物过敏" id="ctwx-rt-4" rt-sc="pageId:ctwx1;name:药物过敏;wt:4;desc:药物过敏;vt:;pvf:;" pid="ctwx-rt-2">
              <span class="rt-sr-lb">药物过敏</span>
            </label>
          </div>
          <div class="layui-inline" style="margin-left: 12px;">
            <input type="text" class="rt-sr-w layui-input" id="ctwx-rt-5" placeholder="请输入" rt-sc="pageId:ctwx1;name:过敏史其他;wt:1;desc:过敏史其他;vt:;pvf:;" pid="ctwx-rt-2">
          </div>
        </div>
      </div>
      <div class="allergy-content">
        <div class="second-title">检查史</div>
        <div class="allergy-item">
          <label class="radio-label" for="ctwx-rt-6">
            <input type="radio" class="rt-sr-w" name="r-xz-jcs" value="无检查史" data-value="0" id="ctwx-rt-6" rt-sc="pageId:ctwx1;name:无检查史;wt:5;desc:无检查史;vt:;pvf:;" checked="">
            <span class="rt-sr-lb">无</span>
          </label>
          <label class="radio-label" for="ctwx-rt-7">
            <input type="radio" class="rt-sr-w" name="r-xz-jcs" value="有检查史" data-value="1" id="ctwx-rt-7" rt-sc="pageId:ctwx1;name:有检查史;wt:5;desc:有检查史;vt:;pvf:;">
            <span class="rt-sr-lb">有</span>
          </label>
        </div>
        <div class="second-content">
          <div class="gray-item">
            <label class="radio-label" for="ctwx-rt-8">
              <input type="checkbox" class="rt-sr-w def-ck" name="ck-jcs" value="有增强CT检查" id="ctwx-rt-8" rt-sc="pageId:ctwx1;name:有增强CT检查;wt:4;desc:有增强CT检查;vt:;pvf:;" pid="ctwx-rt-7">
              <span class="rt-sr-lb">有增强CT检查</span>
            </label>
            <label class="radio-label" for="ctwx-rt-9">
              <input type="checkbox" class="rt-sr-w" name="ck-jcs" value="近期胃餐钡剂、骨扫描等" id="ctwx-rt-9" rt-sc="pageId:ctwx1;name:近期胃餐钡剂、骨扫描等;wt:4;desc:近期胃餐钡剂、骨扫描等;vt:;pvf:;" pid="ctwx-rt-7">
              <span class="rt-sr-lb">近期胃餐钡剂、骨扫描等</span>
            </label>
            <label class="radio-label" for="ctwx-rt-10">
              <input type="checkbox" class="rt-sr-w" name="ck-jcs" value="近期大量使用碘对比剂（>100ml)" id="ctwx-rt-10" rt-sc="pageId:ctwx1;name:近期大量使用碘对比剂（%3E100ml);wt:4;desc:近期大量使用碘对比剂（%3E100ml);vt:;pvf:;" pid="ctwx-rt-7">
              <span class="rt-sr-lb">近期大量使用碘对比剂（&gt;100ml)</span>
            </label>
          </div>
          <div class="layui-inline" style="margin-left: 12px;">
            <input type="text" class="rt-sr-w layui-input" id="ctwx-rt-11" placeholder="请输入" rt-sc="pageId:ctwx1;name:检查史其他;wt:1;desc:检查史其他;vt:;pvf:;" pid="ctwx-rt-7">
          </div>
        </div>
      </div>
      <div class="allergy-content">
        <div class="second-title">用药史</div>
        <div class="allergy-item">
          <label class="radio-label" for="ctwx-rt-12">
            <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-yys" value="无用药史" data-value="0" id="ctwx-rt-12" rt-sc="pageId:ctwx1;name:无用药史;wt:5;desc:无用药史;vt:;pvf:;" checked="">
            <span class="rt-sr-lb">无</span>
          </label>
          <label class="radio-label" for="ctwx-rt-13">
            <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-yys" value="有用药史" data-value="1" id="ctwx-rt-13" rt-sc="pageId:ctwx1;name:有用药史;wt:5;desc:有用药史;vt:;pvf:;">
            <span class="rt-sr-lb">有</span>
          </label>
        </div>
        <div class="second-content">
          <div class="gray-item">
            <label class="radio-label" for="ctwx-rt-14">
              <input type="checkbox" class="rt-sr-w def-ck" name="ck-yys" value="近期服用二甲双腻、苯乙双腻等降糖药活其他药物" id="ctwx-rt-14" rt-sc="pageId:ctwx1;name:近期服用二甲双腻、苯乙双腻等降糖药活其他药物;wt:4;desc:近期服用二甲双腻、苯乙双腻等降糖药活其他药物;vt:;pvf:;" pid="ctwx-rt-13">
              <span class="rt-sr-lb">近期服用二甲双腻、苯乙双腻等降糖药活其他药物</span>
            </label>
          </div>
          <div class="layui-inline" style="margin-left: 12px;">
            <input type="text" class="rt-sr-w layui-input" id="ctwx-rt-15" placeholder="请输入" rt-sc="pageId:ctwx1;name:用药史其他;wt:1;desc:用药史其他;vt:;pvf:;" pid="ctwx-rt-13">
          </div>
        </div>
      </div>
      <div class="allergy-content">
        <div class="second-title">既往史</div>
        <div class="allergy-item">
          <label class="radio-label" for="ctwx-rt-16">
            <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-jws" value="无既往史" data-value="0" id="ctwx-rt-16" rt-sc="pageId:ctwx1;name:无既往史;wt:5;desc:无既往史;vt:;pvf:;" checked="">
            <span class="rt-sr-lb">无</span>
          </label>
          <label class="radio-label" for="ctwx-rt-17">
            <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-jws" value="有既往史" data-value="1" id="ctwx-rt-17" rt-sc="pageId:ctwx1;name:有既往史;wt:5;desc:有既往史;vt:;pvf:;">
            <span class="rt-sr-lb">有</span>
          </label>
        </div>
        <div class="second-content">
        <div class="gray-item">
          <label class="radio-label" for="ctwx-rt-18">
            <input type="checkbox" class="rt-sr-w def-ck" name="ck-jws" value="有心脏病" id="ctwx-rt-18" rt-sc="pageId:ctwx1;name:有心脏病;wt:4;desc:有心脏病;vt:;pvf:;" pid="ctwx-rt-17">
            <span class="rt-sr-lb">有心脏病</span>
          </label>
          <label class="radio-label" for="ctwx-rt-19">
            <input type="checkbox" class="rt-sr-w" name="ck-jws" value="哮喘" id="ctwx-rt-19" rt-sc="pageId:ctwx1;name:哮喘;wt:4;desc:哮喘;vt:;pvf:;" pid="ctwx-rt-17">
            <span class="rt-sr-lb">哮喘</span>
          </label>
          <label class="radio-label" for="ctwx-rt-20">
            <input type="checkbox" class="rt-sr-w" name="ck-jws" value="肾功能补不全" id="ctwx-rt-20" rt-sc="pageId:ctwx1;name:肾功能补不全;wt:4;desc:肾功能补不全;vt:;pvf:;" pid="ctwx-rt-17">
            <span class="rt-sr-lb">肾功能补不全</span>
          </label>
        </div>
        <div class="layui-inline" style="margin-left: 12px;">
          <input type="text" class="rt-sr-w layui-input" id="ctwx-rt-21" placeholder="请输入" rt-sc="pageId:ctwx1;name:既往史其他;wt:1;desc:既往史其他;vt:;pvf:;" pid="ctwx-rt-17">
        </div>
        </div>
      </div>
      <div class="allergy-content">
        <div class="second-title">家族史</div>
        <div class="allergy-item">
          <label class="radio-label" for="ctwx-rt-22">
            <input type="radio" bzkey="zc" class="rt-sr-w" name="r-xz-jzs" value="无家族史" data-value="0" id="ctwx-rt-22" rt-sc="pageId:ctwx1;name:无家族史;wt:5;desc:无家族史;vt:;pvf:;" checked="">
            <span class="rt-sr-lb">无</span>
          </label>
          <label class="radio-label" for="ctwx-rt-23">
            <input type="radio" bzkey="xljs" class="rt-sr-w" name="r-xz-jzs" value="有家族史" data-value="1" id="ctwx-rt-23" rt-sc="pageId:ctwx1;name:有家族史;wt:5;desc:有家族史;vt:;pvf:;">
            <span class="rt-sr-lb">有</span>
          </label>
        </div>
        <div class="second-content">
        <div class="gray-item">
          <label class="radio-label" for="ctwx-rt-24">
            <input type="checkbox" class="rt-sr-w def-ck" name="ck-jzs" value="近亲含碘对比剂不良反应病史" id="ctwx-rt-24" rt-sc="pageId:ctwx1;name:近亲含碘对比剂不良反应病史;wt:4;desc:近亲含碘对比剂不良反应病史;vt:;pvf:;" pid="ctwx-rt-23">
            <span class="rt-sr-lb">近亲含碘对比剂不良反应病史</span>
          </label>
          <label class="radio-label" for="ctwx-rt-25">
            <input type="checkbox" class="rt-sr-w" name="ck-jzs" value="近亲含肾病史" id="ctwx-rt-25" rt-sc="pageId:ctwx1;name:近亲含肾病史;wt:4;desc:近亲含肾病史;vt:;pvf:;" pid="ctwx-rt-23">
            <span class="rt-sr-lb">近亲含肾病史</span>
          </label>
        </div>
        <div class="layui-inline" style="margin-left: 12px;">
          <input type="text" class="rt-sr-w layui-input" id="ctwx-rt-26" placeholder="请输入" rt-sc="pageId:ctwx1;name:家族史其他;wt:1;desc:家族史其他;vt:;pvf:;" pid="ctwx-rt-23">
        </div>
        </div>
      </div>
      <div class="ct-title mt-20 rt-sr-w" id="ctwx-rt-002" rt-sc="pageId:ctwx1;name:护理分级;wt:;desc:护理分级;vt:;pvf:1;">护理分级</div>
      <div class="gray-item" style="margin-left: 16px;">
        <label class="radio-label" for="ctwx-rt-45">
          <input type="radio" class="rt-sr-w" name="ck-hlfj" value="重症：一级" id="ctwx-rt-45" pid="ctwx-rt-002" rt-sc="pageId:ctwx1;name:重症：一级;wt:5;desc:重症：一级;vt:;pvf:;">
          <span class="rt-sr-lb">重症：一级</span>
        </label>
        <label class="radio-label" for="ctwx-rt-46">
          <input type="radio" class="rt-sr-w" name="ck-hlfj" value="普通：二级" id="ctwx-rt-46" pid="ctwx-rt-002" rt-sc="pageId:ctwx1;name:普通：二级;wt:5;desc:普通：二级;vt:;pvf:;">
          <span class="rt-sr-lb">普通：二级</span>
        </label>
        <label class="radio-label" for="ctwx-rt-47">
          <input type="radio" class="rt-sr-w" name="ck-hlfj" value="传染病人" id="ctwx-rt-47" pid="ctwx-rt-002" rt-sc="pageId:ctwx1;name:传染病人;wt:5;desc:传染病人;vt:;pvf:;">
          <span class="rt-sr-lb">传染病人</span>
        </label>
      </div>
      <div class="layui-inline" style="margin-left: 16px;margin-bottom: 12px;width: calc(100% - 32px);">
        <input type="text" class="rt-sr-w layui-input" id="ctwx-rt-48" pid="ctwx-rt-002" placeholder="请输入" rt-sc="pageId:ctwx1;name:护理分级其他;wt:1;desc:护理分级其他;vt:;pvf:;">
      </div>
      <div class="ct-title">肠道准备</div>
      <div class="allergy-content">
        <div class="second-title">水化情况</div>
        <div class="hydration-item layui-form sign-content">
          <div class="item-left">
            <div style="margin-left: 16px;margin-right: 5px;">饮用类型</div>
            <div class="layui-input-block" style="margin-left: 0;min-height: 18px;">
              <select name="city" class="rt-sr-w" id="ctwx-rt-27" lay-verify="required" rt-sc="pageId:ctwx1;name:饮用类型1;wt:2;desc:饮用类型1;vt:;pvf:;">
                <option value=""></option>
                <option value="温开水">温开水</option>
                <option value="甘露醇">甘露醇</option>
              </select>
            </div>
            <div style="margin-left: 16px;margin-right: 5px;">饮用毫升</div>
            <div class="temperature"><input type="text" id="ctwx-rt-28" class="rt-sr-w layui-input" rt-sc="pageId:ctwx1;name:饮用毫升1;wt:1;desc:饮用毫升1;vt:;pvf:;"><span data-type="preview">ml</span><div data-type="edit" class="unit">ml</div></div>
          </div>
          <!-- <input type="checkbox" id="ctwx-rt-29" class="rt-sr-w" name="switch" lay-skin="switch" rt-sc="pageId:ctwx1;name:水化情况1;wt:4;desc:水化情况1;vt:;pvf:;"> -->
        </div>
        <div class="hydration-item layui-form sign-content">
          <div class="item-left">
            <div style="margin-left: 16px;margin-right: 5px;">饮用类型</div>
            <div class="layui-input-block" style="margin-left: 0;min-height: 18px;">
              <select name="city" class="rt-sr-w" id="ctwx-rt-30" lay-verify="required" rt-sc="pageId:ctwx1;name:饮用类型2;wt:2;desc:饮用类型2;vt:;pvf:;">
                <option value=""></option>
                <option value="温开水">温开水</option>
                <option value="甘露醇">甘露醇</option>
              </select>
            </div>
            <div style="margin-left: 16px;margin-right: 5px;">饮用毫升</div>
            <div class="temperature"><input type="text" id="ctwx-rt-31" class="rt-sr-w layui-input" rt-sc="pageId:ctwx1;name:饮用毫升2;wt:1;desc:饮用毫升2;vt:;pvf:;"><span data-type="preview">ml</span><div data-type="edit" class="unit">ml</div></div>
          </div>
          <!-- <input type="checkbox" id="ctwx-rt-32" class="rt-sr-w" name="switch" lay-skin="switch" rt-sc="pageId:ctwx1;name:水化情况2;wt:4;desc:水化情况2;vt:;pvf:;"> -->
        </div>
        <!-- <div class="hydration-item layui-form sign-content">
          <div class="item-left">
            <div style="margin-left: 16px;margin-right: 5px;">饮用类型</div>
            <div class="layui-input-block" style="margin-left: 0;">
              <select name="city" class="rt-sr-w" id="ctwx-rt-33" lay-verify="required" rt-sc="pageId:ctwx1;name:饮用类型3;wt:2;desc:饮用类型3;vt:;pvf:;">
                <option value=""></option>
                <option value="温开水">温开水</option>
                <option value="甘露醇">甘露醇</option>
              </select>
            </div>
            <div style="margin-left: 16px;margin-right: 5px;">饮用毫升</div>
            <div class="temperature"><input type="text" id="ctwx-rt-34" class="rt-sr-w layui-input" rt-sc="pageId:ctwx1;name:饮用毫升3;wt:1;desc:饮用毫升3;vt:;pvf:;"><div class="unit">ml</div></div>
          </div>
          <input type="checkbox" id="ctwx-rt-35" class="rt-sr-w" name="switch" lay-skin="switch" rt-sc="pageId:ctwx1;name:水化情况3;wt:4;desc:水化情况3;vt:;pvf:;">
        </div> -->
      </div>
      <div class="allergy-content">
        <div class="second-title">灌肠情况</div>
        <div class="hydration-item layui-form sign-content">
          <div class="item-left">
            <div style="margin-left: 16px;margin-right: 5px;">已保留灌肠</div>
            <div class="temperature"><input type="text" id="ctwx-rt-36" class="rt-sr-w layui-input" rt-sc="pageId:ctwx1;name:已保留灌肠;wt:1;desc:已保留灌肠;vt:;pvf:;"><span data-type="preview">ml</span><div data-type="edit" class="unit">ml</div></div>
          </div>
          <!-- <input type="checkbox" id="ctwx-rt-37" class="rt-sr-w" name="switch" lay-skin="switch" rt-sc="pageId:ctwx1;name:灌肠情况;wt:4;desc:灌肠情况;vt:;pvf:;"> -->
        </div>
      </div>
      <div class="ct-title">生命体征 (选填)</div>
      <div class="allergy-content sign-content" style="margin-top: 10px;">
        <div class="weight">
          <div class="form-title">体重：</div>
          <div class="temperature"><input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="ctwx-rt-38" rt-sc="pageId:ctwx1;name:体重;wt:1;desc:体重;vt:;pvf:;"><span data-type="preview">kg</span><div data-type="edit" class="unit">kg</div></div>
        </div>
        <div class="weight">
          <div class="form-title">血压：</div>
          <div class="temperature">
            <div style="border: 1px solid #eee;display: flex;align-items: center;">
            <input type="text" class="rt-sr-w layui-input" style="border: 0;height: 36px;" placeholder="收缩压" id="ctwx-rt-39" rt-sc="pageId:ctwx1;name:血压;wt:1;desc:血压;vt:;pvf:;">/<input type="text" class="rt-sr-w layui-input" style="border: 0;height: 36px;" placeholder="舒张压" id="ctwx-rt-50" rt-sc="pageId:ctwx1;name:血压;wt:1;desc:血压;vt:;pvf:;"></div><span data-type="preview">mmHg</span><div data-type="edit" class="unit">mmHg</div>
          </div>
        </div>
        <div class="weight">
          <div class="form-title">心率：</div>
          <div class="temperature">
            <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="ctwx-rt-40" rt-sc="pageId:ctwx1;name:心率;wt:1;desc:心率;vt:;pvf:;"><span data-type="preview">次/分</span><div data-type="edit" class="unit">次/分</div>
          </div>
        </div>
      </div>
      <div class="allergy-content sign-content">
        <div class="weight">
          <div class="form-title">呼吸：</div>
          <div class="temperature">
            <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="ctwx-rt-41" rt-sc="pageId:ctwx1;name:呼吸;wt:1;desc:呼吸;vt:;pvf:;"><span data-type="preview">次/分</span><div data-type="edit" class="unit">次/分</div>
          </div>
        </div>
        <div class="weight">
          <div class="form-title">血氧饱和度：</div>
          <div class="temperature">
            <input type="text" class="rt-sr-w layui-input" placeholder="请输入" id="ctwx-rt-42" rt-sc="pageId:ctwx1;name:血氧饱和度;wt:1;desc:血氧饱和度;vt:;pvf:;"><span data-type="preview">%</span><div data-type="edit" class="unit">%</div>
          </div>
        </div>
      </div>
      <div class="ct-title mt-20">记录信息</div>
      <div class="allergy-content sign-content" style="padding-bottom: 20px;">
        <div class="weight">
          <div>记录人：</div>
          <div class="temperature">
            <div class="layui-inline showInt">
              <input class="layui-input rt-sr-w" placeholder="请选择" autocomplete="off" style="margin-left: 0;width: 190px;" id="ctwx-rt-43" rt-sc="pageId:ctwx1;name:记录人;wt:1;desc:记录人;vt:;pvf:;">
            </div>
          </div>
        </div>
        <div class="weight">
          <div>记录时间：</div>
          <div class="temperature"><input type="text" class="layui-input rt-sr-w" id="ctwx-rt-44" placeholder="记录时间" style="display: inline-block; width: 180px;" rt-sc="pageId:ctwx1;name:记录时间;wt:1;desc:记录时间;vt:;pvf:;"></div>
        </div>
      </div>
      <input class="rt-sr-w" id="optInfo" style="display: none;" rt-sc="pageId:ctwx1;name:记录人;wt:1;desc:记录人;vt:;pvf:1;">
    </div>
  </li>
</ul>