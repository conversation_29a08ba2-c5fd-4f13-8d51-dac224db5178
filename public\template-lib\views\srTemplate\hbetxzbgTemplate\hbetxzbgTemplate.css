.singleDisEditReport.main-page{
  min-width: unset;
}
#hbetxzbg1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#hbetxzbg1 .hbetxzbg-edit{
  padding: 8px 12px;
}
#hbetxzbg1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#hbetxzbg1 .black-lb {
  color: #303133;
}
#hbetxzbg1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#hbetxzbg1 .blue-lb:hover {
  opacity: 0.8;
}
#hbetxzbg1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#hbetxzbg1 .editor-area textarea {
  width: 100%;
  height: 100px;
  border: none;
  font-size: 16px;
  padding-bottom: 0;
}
#hbetxzbg1 .p-item + .p-item {
  margin-top: 12px;
}
[isview="true"] #hbetxzbg1 .hbetxzbg-edit {
  display: none;
}
[isview="true"] #hbetxzbg1 .hbetxzbg-view {
  display: block;
}
/* 预览页 */
#hbetxzbg1 .hbetxzbg-view {
  display: none;
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 36px 56px;
  flex-direction: column;
  position: relative;
}
#hbetxzbg1 .hbetxzbg-view .view-head {
  border-bottom: 2px solid #000;
  padding-bottom: 9px;
  text-align: center;
}
#hbetxzbg1 .hbetxzbg-view .logo-tit {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}
#hbetxzbg1 .hbetxzbg-view .logo-tit .code {
  width: 194px;
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  img {
    width: 122px;
    height: 36px;
  }
}
#hbetxzbg1 .hbetxzbg-view .blh-tit {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
#hbetxzbg1 .hbetxzbg-view .hos-tit{
  font-size: 36px;
  font-weight: bold;
  color: #000;
}
#hbetxzbg1 .hbetxzbg-view .sub-tit{
  font-size: 24px;
  color: #000;
  font-weight: bold;
  margin-top: 10px;
}
#hbetxzbg1 .hbetxzbg-view .view-patient {
  padding: 8px 0;
  border-bottom: 2px solid #000;
}
#hbetxzbg1 .hbetxzbg-view .info-i {
  width: 160px;
  display: flex;
  flex-wrap: wrap;
}
#hbetxzbg1 .hbetxzbg-view .info-i + .info-i {
  margin-left: 8px;
}
#hbetxzbg1 .hbetxzbg-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-line;
  word-break: break-all;
}
#hbetxzbg1 .hbetxzbg-view .black-txt {
  color: #000;
  font-size: 16px;
}
#hbetxzbg1 .hbetxzbg-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#hbetxzbg1 .hbetxzbg-view .rt-sr-body {
  padding-top: 8px;
}
#hbetxzbg1 .hbetxzbg-view .desc-con {
  display: flex;
  align-items: baseline;
  margin-bottom: 20px;
}
#hbetxzbg1 .hbetxzbg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#hbetxzbg1 .hbetxzbg-view .bold {
  font-weight: bold;
}
#hbetxzbg1 .hbetxzbg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#hbetxzbg1 .hbetxzbg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#hbetxzbg1 .hbetxzbg-view .reporter-i {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  align-items: center;
}
#hbetxzbg1 .hbetxzbg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#hbetxzbg1 .hbetxzbg-view .tip-wrap {
  margin-top: 8px;
  display: flex;
  font-size: 14px;
  line-height: 16px;
}
#hbetxzbg1 .hbetxzbg-view .tip-wrap .tip-text {
  flex: 1;
}