#lxajs1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
  color: #000;
  padding: 12px 24px;
}

[isview="true"] #lxajs1 {
  padding: 0;
}

[isview="true"] #lxajs1 .ajs-edit {
  display: none;
}

[isview="true"] #lxajs1 .ajs-view, [isview="true"] #lxajs1 .view-container {
  display: block;
}

#lxajs1 input[type="text"] {
  vertical-align: middle;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding-left: 12px;
}

#lxajs1 textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 10px;
}

#lxajs1 .row-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

#lxajs1 .row-item+.row-item {
  margin-top: 12px;
}

#lxajs1 .tbl-title {
  font-weight: 600;
}


#lxajs1 .ajs-view * {
  font-family: '宋体';
}

#lxajs1 .ajs-view {
  display: none;
  position: relative;
  width: 780px;
  margin: 0 auto;
  font-size: 16px;
  min-height: 1100px;
  background: #fff;
  padding: 30px 24px 22px;
  color: #000;
}

#lxajs1 .ajs-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 8px;
  text-align: center;
}

#lxajs1 .ajs-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}

#lxajs1 .ajs-view .hos-tit {
  font-size: 21px;
  margin-bottom: 20px;
  text-align: center;
}

#lxajs1 .ajs-view .hos-tit * {
  font-family: '仿宋' !important;
}

#lxajs1 .ajs-view .sub-tit {
  font-size: 26px;
  line-height: 35px;
  text-align: center;
}

#lxajs1 .ajs-view .logo-tit .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
}

#lxajs1 .ajs-view .logo-tit .code img {
  width: 122px;
  height: 36px;
}

#lxajs1 .ajs-view .logo-tit .code span {
  width: 122px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  margin-top: 2px;
}

#lxajs1 .ajs-view .blh-tit {
  font-size: 16px;
  line-height: 23px;
  text-align: right;
}

#lxajs1 .ajs-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#lxajs1 .ajs-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}

#lxajs1 .ajs-view .rt-sr-footer {
  position: absolute;
  bottom: 36px;
  left: 56px;
  right: 56px;
}

#lxajs1 .ajs-view .report-wrap {
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#lxajs1 .ajs-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
}

#lxajs1 .ajs-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
}

#lxajs1 .ajs-view .reporter-i [data-key] {
  flex: 1;
}

#lxajs1 .ajs-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}

#lxajs1 .ajs-view .reporter-i+.reporter-i {
  margin-left: 8px;
}

#lxajs1 .view-container {
  display: none;
  width: 100%;
  height: 100%;
}

#lxajs1 .text-bold {
  font-weight: bold;
}

.view-second {
  height: 1100px;
  padding: 40px 56px 0 !important;
  line-height: 20px;
  font-size: 14px !important;
}

.view-third {
  padding: 56px 56px 0 !important;
  font-size: 14px !important;
}

.view-title {
  text-align: center;
  font-size: 26px;
  line-height: 26px;
  font-weight: bold;
}

.view-table th, .view-table td {
  text-align: center;
  line-height: 20px;
  height: 28px;
}

.view-third .view-table td:nth-child(1) {
  text-align: center;
}

.view-third .view-table td {
  text-align: left;
  padding: 0 10px;
  height: 136px;
}

.arrow-tip img {
  width: 20px;
  height: 20px;
}

.lbmr-20 {
  display: inline-block;
  margin-right: 20px;
}

.ajs-view .p-item {
  align-items: center;
}

.ajs-view .arrow-tip {
  position: absolute;
  right: 0;
}

.wid-56 {
  width: 56px;
}

.wid-64 {
  width: 64px;
}

.wid-84 {
  width: 84px;
  position: relative;
  text-align: center;
}

.wid-136 {
  width: 136px;
  text-align: center;
}

.wid-150 {
  width: 150px;
  text-align: center;
}

.wid-166 {
  width: 166px;
}

.w100p-366 {
  width: calc(100% - 366px);
  display: flex;
  align-items: center;
}

.w100p-28 {
  width: calc(100% - 28px);
}


.mlf-16 {
  margin-left: -16px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mr-8 {
  margin-right: 8px;
}

.mr-16 {
  margin-right: 16px;
}

.mr-70 {
  margin-right: 70px;
}

.mr-120 {
  margin-right: 120px;
}