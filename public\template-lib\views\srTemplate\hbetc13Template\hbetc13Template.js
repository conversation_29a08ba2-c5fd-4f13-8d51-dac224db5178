$(function() {
  window.initHtmlScript = initHtmlScript;
  
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //报告是否填写过

function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: $('#hbetc131 .preview'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPreview()
    } else {
      initPage()
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescription();
  rtStructure.impression = getImpressionStr();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
function initPage(){
  if(!isSavedReport){
   let value = $('#hbetc13-rt-18').val().replace(/(\+|\-)/g,(keyword)=>{
      if(keyword==='+'){
        return '阳性'
      }else{
        return '阴性'
      }
    })
    $('#hbetc13-rt-18').val(value)
  }
 
  // $('#hbetc13-rt-18').on('input',function(){
  //   let value = $(this).val().replace(/(\+|\-)/g,(keyword)=>{
  //     console.log(keyword);
  //     if(keyword==='+'){
  //       return '阳性'
  //     }else{
  //       return '阴性'
  //     }
  //   })
  //   $(this).val(value)
  // })
}
function initPreview(){
  // 签名
  curElem.find('.preview [data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } 
    }else {
      $(this).hide();
    }
  });
  curElem.find('.preview [data-key]').each(function(){
    var key = $(this).attr('data-key')
    var idAnVal,value;
    // 报告图片
    if(key==='report-img'){
      if(rptImageList && rptImageList.length){
        rptImageList = rptImageList.slice(0,2)
        let html = ''
        rptImageList.forEach(item=>
            html+=`<img src="${item.src}" alt=""">`
        )
        $(this).html(html)
      }else{
        $(this).hide()
      }
      return
    }
    // 兜底
    idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : ''
    value = publicInfo[key] || idAnVal;
    
    this.style.whiteSpace = 'pre-line'
    $(this).html(value)
    addIdToNodeByView(this, key, idAndDomMap);
  })
}
// 存入表格 DESCRIPTION
function getDescription(){
  let preFix = '#hbetc13-rt-';
  let arr = [[7,8,9,10],[12,13,14,15]]
  let result =  arr.map(item=>{
    let Delta = 'Delta';
    let DOB = 'DOB';
    let CO2 = 'CO2';
    let res = []
    let filedIndexValue = $(preFix+item[0]).val()
    console.log(filedIndexValue);
    item.shift()
    item.forEach((item2,index)=>{
      let value = $(preFix+item2).val()
      if(index===0){
        res.push(Delta+filedIndexValue+':'+value)
      }else if(index===1){
        res.push(DOB+filedIndexValue+':'+value)
      }else{
        res.push(CO2+filedIndexValue+':'+value)
      }
    })
    return res.join(';') +';'
  })
  return result.join('\n')
}
// IMPRESSION 存入诊断
function getImpressionStr(){
  let keyObj = {
    '#hbetc13-rt-17':'检测项目：',
    '#hbetc13-rt-18':'检测结果：',
  }
  let result = []
  Object.entries(keyObj).forEach(([key,value],)=>{
    let str =  $(key).val()
    if(str){
      result.push(`${value}${str}`)
    }
  })
  return result.join('\n')
}