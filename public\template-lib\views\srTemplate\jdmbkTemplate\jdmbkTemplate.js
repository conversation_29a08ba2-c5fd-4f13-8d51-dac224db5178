$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var tableList = [];
var paramList = ["CCA", "ICA", "ECA", "VA"];
var fmdmOptList = ["左侧", "右侧"];
// 测量值表格列
var measureParamColumnList = [
  { colCode: '0001', colName: '内中膜', unit: 'mm' },
  { colCode: '0002', colName: '内径', unit: 'mm' },
  { colCode: '0003', colName: 'PSV', unit: 'cm/s' },
  { colCode: '0004', colName: 'EDV', unit: 'cm/s' },
  { colCode: '0005', colName: 'RI', unit: '' },
]
// 测量值表格行
var measureParamRowList = [
  { rowCode: 'CCA', rowName: 'CCA' },
  { rowCode: 'ICA', rowName: 'ICA' },
  { rowCode: 'ECA', rowName: 'ECA' },
  { rowCode: 'VA', rowName: 'VA' },
]
var sideList = ['left', 'right'];
// 通过接口返回的reserve2对应找到paramCode
// `${side}_${row.rowCode}_${col.colCode}`

var isSavedReport = false; //报告是否填写过
// var zcqkIdList = ["66","70","71","72",""]
var resultData = []; //结果集，用于判断是否为新报告
// var bzTitleClone = null;   //作为病灶示例模板的title
// var bzConClone = null;  //作为病灶示例模板具体内容
var bzObject = {
  jdmbk: {
    bzTitleClone: null,
    bzConClone: null
  },
  xzqk: {
    bzTitleClone: null,
    bzConClone: null
  }
}
var bzTypeStr = null;
var bzTypeList = ['jdmbk', 'xzqk'];
var leftImpressStore = {};  //左侧诊断集合
var rightImpressStore = {};  //右侧诊断集合
var radsList = ['1', '2', '3a', '3b', '3c', '4a', '4b', '4c']; //Plaque-RADS正序
var configList = [], paramNormalList = [];
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || {}) : {}; // 结果集
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    pageInit();
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
  $('.block-ck3').click(function (e) {
    var _this = $(this);
    _this.addClass('sel-tab').siblings().removeClass('sel-tab');
    var panel = _this.attr('data-panel');
    $("#" + panel).show().siblings('.tab-panel').hide();
    $('#jdmbk-rt-86').prop('checked') ? $("#haveZh").show() : $("#haveZh").hide()
    e.preventDefault();
  })
  $('.ck-inp3').click(function (e) {
    e.stopPropagation();
  })
  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").parent().click(function (e) {
    let { target = {} } = e;
    let { id = "" } = target;
    _is_checked_ = $(this).prop("checked");
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
    if (id === "jdmbk-rt-45" || id === "jdmbk-rt-46") {
      id === "jdmbk-rt-46" && _is_checked_ ? $("#haveSss").show() : $("#haveSss").hide();
    }
    if (id === "jdmbk-rt-86" || id === "jdmbk-rt-85") {
      id === "jdmbk-rt-86" && _is_checked_ ? $("#haveZh").show() : $("#haveZh").hide();
    }
  });
}
function pageInit() {
  tableList = [];
  $('.tab-item').click(function () {
    var _this = $(this);
    _this.addClass('sel-tab').siblings().removeClass('sel-tab');
    var panel = _this.attr('data-panel');
    $("#" + panel).show().siblings('.tab-panel').hide();
    panel === "ultrasoundDescription" ? $(".rpt-imp").css('display', 'flex') : $(".rpt-imp").hide();
  });
  // $('#jdmbk-rt-67').click((function () {
  //   $('#jdmbk-rt-67').prop('checked') ? '' : $("#haveZh").hide()
  // }))
  isSavedReport ? zdmStatusChange() : $("#jdmbk-rt-66").attr('checked', true);
  curElem.find('#jdmbk1 .rpt-con .rt-sr-w').change(function () {
    getBkxcBz('change');
    getXzqkBz('change');
    getTipText();
  })
  $('#jdmbk-rt-66').click(function () {
    zdmStatusChange($(this));
    getTipText();
  })
  $('#normalConditions').on("change", '.rt-sr-w', function () {
    zdmStatusChange($(this));
    getTipText();
  })
  let sssVal = getVal("[id=jdmbk-rt-46]:checked") || "";
  sssVal ? $("#haveSss").show() : '';
  // for (let i = 0; i < bzTypeList.length; i++) {
  //   // 初始化将病灶参照代码复制存储起来
  //   bzTypeStr = bzTypeList[i]
  //   initBzCloneEle(bzTypeList[i]);
  //   displayBzContent(bzTypeList[i]);
  // }
  initBzCloneEle();
  displayBzContent();
  getBkxcBz('init');
  getXzqkBz('init');
  getTipText();
  //获取模板配置-渲染表格
  getTemplateConfig();
  for (let i = 0; i < fmdmOptList.length; i++) {
    let tableLength = $('#tableBox').find('table').length || 0
    if (tableLength === 2) {
      i = fmdmOptList.length
    } else {
      let type = fmdmOptList[i] === '左侧' ? 'left' : 'right';
      initTableContent(type);
    }
  }
  //给检查值输入框添加事件
  showUpArrow()
  $("#tableBox").find("input[type=text]").change(function () {
    showUpArrow()
  })
  // 初始化浅色和深色块的显示关系
  // toggleHightBlock(curElem, true);
  // initLigthItemChange(curElem);
  // 切换病灶
  toggleBzHandler();
  // 病灶标题名称
  setBzTitle()
}

//颈动脉复选、椎动脉单选切换状态
function zdmStatusChange(clickElm) {
  let id = $(clickElm).attr('id');
  let jdmVal = getVal('[id=jdmbk-rt-70]:checked') || '';
  let zdmVal = getVal('input[type=radio][name=zdm-zt]:checked');
  let jdmStatus = jdmVal !== ''
  let zdmStatus = zdmVal === "椎动脉正常";
  let zdmUnStatus = zdmVal === "椎动脉不对称";
  if (id === "jdmbk-rt-66" && !$(clickElm).prop('checked')) {
    jdmStatus = false
    zdmStatus = false
    zdmUnStatus = false
  }
  if (id === "jdmbk-rt-80" || id === "jdmbk-rt-81") {
    $('#jdmbk-rt-78').click();
    return
  }
  // 颈动脉子节点
  $('#jdmbk-rt-71').prop("checked", jdmStatus);
  $('#jdmbk-rt-71').attr("disabled", jdmStatus);
  // 椎动脉正常子节点
  $("#jdmbk-rt-73").prop("checked", zdmStatus);
  $("#jdmbk-rt-77").prop("checked", zdmStatus);
  $("#jdmbk-rt-73").attr("disabled", zdmStatus);
  $("#jdmbk-rt-77").attr("disabled", zdmStatus);
  let zdmzcIdArr = ['74', '75', '76']
  for (let i = 0; i < zdmzcIdArr.length; i++) {
    zdmStatus ? $(`#jdmbk-rt-${zdmzcIdArr[i]}`).attr("rt-req", "1") : $(`#jdmbk-rt-${zdmzcIdArr[i]}`).removeAttr("rt-req")
    zdmStatus ? $(".sczdm-tip").show() : $(".sczdm-tip").hide()
  }
  // 椎动脉不对称子节点
  $("#jdmbk-rt-79").prop("checked", zdmUnStatus);
  $("#jdmbk-rt-82").prop("checked", zdmUnStatus);
  $("#jdmbk-rt-83").prop("checked", zdmUnStatus);
  $("#jdmbk-rt-79").attr("disabled", zdmUnStatus);
  $("#jdmbk-rt-82").attr("disabled", zdmUnStatus);
  $("#jdmbk-rt-83").attr("disabled", zdmUnStatus);
  if (zdmUnStatus) {
    $("#jdmbk-rt-74").prop("checked", zdmStatus);
    $("#jdmbk-rt-75").prop("checked", zdmStatus);
    $("#jdmbk-rt-76").prop("checked", zdmStatus);
  } else {
    $("#jdmbk-rt-80").val('')
    $("#jdmbk-rt-81").val('')
  }
  // if (zdmVal === '椎动脉不对称') {

  // }
}

//获取模板配置
function getTemplateConfig() {
  let params = {
    paramName: "sr_dict_pattern_measure_code",
    reserve1: "2145",
  }
  window.getSrSettings ? configList = window.getSrSettings(params) : ''
  if (configList.length > 0) {
    let paramCode = configList.map(item => item.paramValue).join(",")
    paramNormalList = window.getMeasureParamList({ paramCode });
    // console.log('>>>tableList', tableList);
  }
}
//渲染表格
function createTableHtml(str) {
  let propStr = `name:${str};wt:1;desc:${str};vt:;pvf:;`
  return propStr;
}
// 初始表格
function initTableContent(type) {
  // console.log('>>>rrrr表格',resultData);
  let noOldTable = true;
  if (resultData && resultData.length) {
    var tableData = resultData.filter(tableItem => tableItem.id === `jdmbk-${type}-1`);
    if (tableData && tableData.length) {
      let tableChild = tableData[0].child || [];
      if (tableChild && tableChild.length) {
        noOldTable = false;
        let paneId = tableChild[0].id.replace('jdmbk-rt-', '') || ''
        addTableContent(type, paneId.slice(0, 4) || "")
        // tableChild.forEach(item => {
        //   var paneId = item.id.replace('jdmbk-rt-', '');
        //   addTableContent(type, paneId.slice(0, 4) || "")
        // })
      }
    }
  }
  if (noOldTable) {
    addTableContent(type)
  }
}
// 添加表格
function addTableContent(type, oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var activeTab = `jdmbk-${type}-1`;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  if (rtStructure) {
    if (rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // rtStructure.idAndDomMap[activeTab] = {
    //   id: activeTab,
    //   desc: `${type === 'left' ? '左侧' : '右侧'}`,
    //   name: `${type === 'left' ? '左侧' : '右侧'}`,
    //   pid: '',
    //   pvf: '',
    //   req: '',
    //   value: `${type === 'left' ? '左侧' : '右侧'}`,
    // }
    if (configList.length > 0) {
      let tableHead = ["<th>测量参数</th>"];
      let tableBody = [];
      for (let h = 0; h < measureParamColumnList.length; h++) {
        let { colCode, colName, unit } = measureParamColumnList[h];
        tableHead.push(`<th>${colName}${unit ? '（' + unit + '）' : ''}</th>`)
      }
      for (let b = 0; b < measureParamRowList.length; b++) {
        let { rowCode, rowName } = measureParamRowList[b];
        let str = '';
        let text = `${type === 'left' ? '左侧' : '右侧'}`
        str = `<td><span class="${type === 'left' ? 'blue' : 'purple'}-color">${text} </span><span>${rowName}</span></td>`
        for (let j = 0; j < measureParamColumnList.length; j++) {
          let { colCode, colName } = measureParamColumnList[j];
          let paramCodeKey = `${type}_${rowCode}_${colCode}`
          let index = configList.findIndex(item => item.reserve2 === paramCodeKey);
          let paramCode = index !== -1 ? configList[index].paramValue || "" : ''
          let item = paramNormalList.find(obj => obj.paramCode === paramCode)
          let normalValue = item ? item.normalValue || "" : "";
          let referenceVal = ''
          referenceVal = textToAnalysis(normalValue);
          let id = `jdmbk-rt-${paneId}-${b + 1}-${j + 1}`;
          var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
          str += `<td><input type="text" autocomplete="off" class="rt-sr-w" id="${id}" pid="${activeTab}" rt-sc="${createTableHtml(text + rowName + " " + colName)}"><span class="arrow-tip"></span></td>`
          let hasDataIndex = tableList.findIndex(item => item.paramCode === paramCode);
          if (hasDataIndex === -1) {
            tableList.push({
              position: text,
              paramStr: rowName,
              idStr: id,
              paramName: colName,
              referenceVal: referenceVal,
              paramCode: paramCode,
              paramFormat: text + rowName + colName
            })
          }
          let scObj = {
            id,
            pid: activeTab,
            rtScPageNo: 1,
            value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
            desc: '',
            pvf: '',
            req: '',
            vt: '',
            wt: '1',
          }
          rtStructure.idAndDomMap[id] = { ...scObj }
        }
        tableBody.push(`<tr>${str}</tr>`)
      }
      let tableHtml = `<table class="table-sty" border="1" cellspacing="0"><thead><tr>${tableHead.join("")}</tr></thead><tbody>${tableBody.join("")}</tbody></table>`
      $("#tableBox").append(tableHtml)
      $("#tableBox").show()
    }
    for (let i = 0; i < tableList.length; i++) {
      let { idStr = '' } = tableList[i];
      if (rtStructure.idAndDomMap[idStr] && rtStructure.idAndDomMap[idStr].value) {
        $('#' + idStr).val(rtStructure.idAndDomMap[idStr].value)
      }
    }
  }
  // console.log('tableList>>,', tableList);
  // console.log('>>>resultData222', resultData);
  // console.log('>>>idAndDomMap222', rtStructure.idAndDomMap);
}
// 是否显示箭头
function showUpArrow() {
  for (let index in tableList) {
    let { idStr = "", referenceVal = "" } = tableList[index];
    let inpVal = Number($(`#${idStr}`).val());
    $(`#${idStr}`).siblings('.arrow-tip').html('');
    typeof referenceVal === "string" ? referenceVal = Number(referenceVal) : ""
    if (referenceVal && inpVal > referenceVal) {
      $(`#${idStr}`).siblings('.arrow-tip').html('<img src="./template-lib/assets/images/zjyy/up_arrow.png">');
    }
  }
}

//判断文字是否需要解析
function textToAnalysis(str) {
  if (str.includes("<")) {
    return extractNumber1(str)
  } else if (str.includes("-")) {
    return extractNumber2(str)
  } else {
    return str;
  }
}

// 解析字符串以获取数值  
function extractNumber1(str) {
  // 使用正则表达式匹配并返回数值部分  
  const match = str.match(/\d+/);
  if (match) {
    return Number(match); // 将匹配到的数字转换为整数  
  }
  return ""; // 如果没有匹配到数字，则返回null  
}
function extractNumber2(str) {
  let list = str.split("-");
  return Number(list[list.length - 1]);
}

//获取有无病灶
function getBkxcBz(type) {
  let hasBk = getVal('input[type=radio][name=ywbk]:checked');
  if (hasBk === '有斑块') {
    $('.jdmbk-bz').css('display', 'flex');
  } else if (type === 'change') {
    $('.jdmbk-bz').hide();
    let bklist = $('.jdmbk-bz .bz-tit-i');
    bklist.each(function (i, item) {
      let paneId = $(item).attr('tab-id').replace('jdmbk-rt-', '');
      willBzVm = $(item)
      removeBzHandler(paneId, 'jdmbk')
    })
  }
}
function getXzqkBz(type) {
  let hasBk = getVal('input[type=radio][name=ywxz]:checked');
  if (hasBk === '有狭窄') {
    $('.xzqk-bz').css('display', 'flex');
  } else if (type === 'change') {
    $('.xzqk-bz').hide();
    $('.xzqk-bz').hide();
    let bklist = $('.xzqk-bz .bz-tit-i');
    bklist.each(function (i, item) {
      let paneId = $(item).attr('tab-id').replace('xzqk-rt-', '');
      willBzVm = $(item)
      removeBzHandler(paneId, 'xzqk')
    })
  }
}

// 切换病灶
function toggleBzHandler() {
  $('#jdmbk1 .bz-wrap').on('click', '.bz-tit-i', function (e) {
    var target = $(this).attr('tab-id');
    $(this).siblings('.bz-tit-i').removeClass('act');
    $(this).addClass('act');
    $('#jdmbk1 .bz-item[tab-target="' + target + '"]').siblings('#jdmbk1 .bz-item').removeClass('act');
    $('#jdmbk1 .bz-item[tab-target="' + target + '"]').addClass('act');
  })
}

// 复制暂存病灶的具体模板示例内容，为后续添加病灶做准备
function initBzCloneEle() {
  console.log('>>>.初始化暂存病灶');
  for (let i = 0; i < bzTypeList.length; i++) {
    let bzType = bzTypeList[i];
    if (!bzObject[bzType].bzTitleClone && !bzObject[bzType].bzConClone) {
      var bzTitle = curElem.find(`#jdmbk1 .${bzType}-bz .bz-wrap .bz-tit-i`).clone(true);  //作为病灶示例模板的title
      bzObject[bzType].bzTitleClone = `<div class="bz-tit-i act" tab-id="${bzType}-rt-000">` + bzTitle.html() + '</div>';
      var bzCon = curElem.find(`#jdmbk1 .${bzType}-bz .bz-wrap .bz-item`).clone(true);
      bzObject[bzType].bzConClone = `<div class="bz-item act" tab-target="${bzType}-rt-000">` + bzCon.html() + '</div>';
      curElem.find(`#jdmbk1 .${bzType}-bz .bz-wrap .bz-tit-ls`).html('');
      curElem.find(`#jdmbk1 .${bzType}-bz .bz-wrap .bz-con`).html('');
    }
  }
}

// 回显病灶内容
function displayBzContent() {
  console.log('>>>.回显病灶内容');
  let bzType = '';
  for (let i = 0; i < bzTypeList.length; i++) {
    bzType = bzTypeList[i];
    if (resultData && resultData.length) {
      var bzData = resultData.filter(bzItem => bzItem.id === `${bzType}-bz-1`);
      if (bzData && bzData.length) {
        var bzList = bzData[0].child || [];
        if (!bzList.length) {
          $(`#jdmbk1 .${bzType}-bz .bz-wrap`).hide();
        }
        bzList.forEach((item) => {
          var paneId = item.id.replace(`${bzType}-rt-`, '');
          addBzHandler(bzType, paneId);
        })
      }
    }else {
      // addBzHandler(bzType);
      $(`#jdmbk1 .${bzType}-bz .bz-wrap`).hide();
    }
  }
}

//修改病灶标题
function setBzTitle() {
  $('.radio-wz').each(function () {
    let value = "", id = "", radioName = "";
    id = $(this).attr("bz-id");
    radioName = $(this).attr("name");
    var checkedRadio = $(`input[type=radio][name=${radioName}]:checked`)
    checkedRadio.length > 0 ? value = checkedRadio.val() : ''
    $(`#${id} .bz-cw`).html("")
    if (value) {
      colorStr = value === '左侧' ? 'blue-color' : 'purple-color'
      let htmlStr = `-<span class="${colorStr}">${value[0]}</span><span>${value[1]}</span>`
      $(`#${id} .bz-cw`).html(htmlStr)
    }
  })
}


// 删除病灶
var willBzVm = null;
function delTab(vm, paneId, type) {
  willBzVm = vm;
  var dialogContent = `<div style="">确认删除该病灶?</div>`;
  dialogContent += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  dialogContent += '<button onclick="removeBzHandler(\'' + paneId + '\', \'' + type + '\')" style="background:#1885F2;padding:5px 15px;border-radius:3px;color:#fff;border:none;cursor:pointer;">确认</button>';
  dialogContent += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">取消</button>';
  dialogContent += '</div>';
  drawDialog({
    title: '提示',
    content: dialogContent,
    modal: true
  })
}
// 确认删除病灶
function removeBzHandler(paneId, type) {
  var vm = willBzVm;
  var idx = $(`.${type}-bz.bz-list .bz-tit-i .close-icon`).index(vm);
  var isAct = $(vm).closest('.bz-tit-i').hasClass('act');
  $(`.${type}-bz` + '.bz-list .bz-tit-i:eq(' + idx + ')').remove();
  $(`.${type}-bz` + '.bz-list .bz-item:eq(' + idx + ')').remove();
  var resetBzLen = $(`.${type}-bz.bz-list .bz-item`).length;
  if (resetBzLen > 0 && isAct) {
    let nextId = idx >= 1 ? idx - 1 : idx;
    $(`.${type}-bz` + '.bz-list .bz-tit-i:eq(' + nextId + ')').addClass('act');
    $(`.${type}-bz` + '.bz-list .bz-item:eq(' + nextId + ')').addClass('act');
  }
  if (rtStructure && rtStructure.idAndDomMap) {
    for (var key in rtStructure.idAndDomMap) {
      if (key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
  }
  removeRtDialog();
  if (resetBzLen > 0) {
    $(`.${type}-bz.bz-list .bz-name`).each(function (i, dom) {
      // $(dom).text('病灶' + (i+1));
      let title = $(dom).text();
      if (title.includes("-")) {
        let titleArr = title.split("-");
        let cwStr = titleArr[titleArr.length - 1];
        let colorStr = cwStr === "左侧" ? "blue-color" : "purple-color"
        $(dom).html('病灶' + (i + 1) + `<span class="bz-cw"> -<span class="${colorStr}">${cwStr[0]}</span>${cwStr[1]}</span>`);
        // $(dom).text('病灶' + (i+1) + titleArr[titleArr.length-1]);
      } else {
        $(dom).html('病灶' + (i + 1) + `<span class="bz-cw"></span>`)
      }
    });
    $(`.${type}-bz.bz-list .bz-wrap`).show();
  } else {
    $(`.${type}-bz.bz-list .bz-wrap`).hide();
  }
  // getAllDescAndImpContent();
}

// 添加病灶,oldPaneId已保存过的
function addBzHandler(bzType, oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  bzTypeStr = bzType
  var activeTab = `${bzTypeStr}-rt-${paneId}`;
  var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
  var { newPaneBlock } = appendBzHtml(paneId);
  // var bzLen = $('#jdmbk1 .jdmbk-bz .bz-tit-ls .bz-tit-i').length;
  var bzLen = $(`#jdmbk1 .${bzTypeStr}-bz .bz-tit-ls`).find('.bz-tit-i').length;
  if (rtStructure) {
    if (rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '病灶',
      name: '病灶title',
      pid: `${bzTypeStr}-bz-1`,
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : '病灶' + bzLen,
      wt: '',
      vt: '',
    }
    // var wCon = $('#jdmbk1 ' + '.' + bzTypeStr + '-bz .bz-item[tab-target="' + activeTab + '"]').find("[rt-sc]");
    var wCon = $(`#jdmbk1 .${bzTypeStr}-bz .bz-item[tab-target=${activeTab}]`).find("[rt-sc]");
    wCon.each(function (wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if (groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function (scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if (key) {
          if (key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="' + id + '"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      rtStructure.idAndDomMap[id] = { ...scObj };
    })
    $(`.${bzType}-bz.bz-list .bz-wrap`).show();
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
    if (document.querySelector(`.${bzType}-bz.bz-list .bz-wrap .bz-tit-ls`)) {
      document.querySelector(`.${bzType}-bz.bz-list .bz-wrap .bz-tit-ls`).scrollLeft = document.querySelector(`.${bzType}-bz.bz-list .bz-wrap .bz-tit-ls`).scrollWidth;
    }
    setScClick()
  }
}

// 病灶的交互
function initBzTigger(newPaneBlock, oldFlag) {
  if (oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function (i, widget) {
      var id = $(widget).attr('id');
      if (rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  } else {
    // newPaneBlock.find('.radio-wz').click();
  }
  // 初始化浅色和深色块的显示关系
  toggleHightBlock(curElem, true);
  initLigthItemChange(curElem);
  //狭窄情况的病灶
  initXzBzTigger();
  // 获取推导结果
  curElem.find('#jdmbk1 .rpt-con .rt-sr-w').change(function () {
    getTipText();
  })
}

// 处理新增病灶的html
function appendBzHtml(paneId) {
  $(`#jdmbk1 .${bzTypeStr}-bz .bz-tit-ls .bz-tit-i`).removeClass('act');
  $(`#jdmbk1 .${bzTypeStr}-bz .bz-con .bz-item`).removeClass('act');
  var reg = new RegExp('000', 'ig');
  var title = bzObject[bzTypeStr].bzTitleClone.replace(reg, paneId);   //作为病灶示例模板的title
  var content = bzObject[bzTypeStr].bzConClone.replace(reg, paneId);  //作为病灶示例模板具体内容
  $(`#jdmbk1 .${bzTypeStr}-bz .bz-tit-ls`).append(title);
  $(`#jdmbk1 .${bzTypeStr}-bz .bz-con`).append(content);
  var newPaneBlock = $(`#jdmbk1 .${bzTypeStr}-bz .bz-item[tab-target=${bzTypeStr}-rt-${paneId}]`)
  // var newPaneBlock = $('#jdmbk1 .jdmbk-bz .bz-item[tab-target="jdmbk-rt-' + paneId + '"]');
  setBzTitleNo(paneId);
  return { newPaneBlock };
}
// 设置病灶标题序号
function setBzTitleNo(paneId) {
  var bzTitleDom = $(`#jdmbk1 .${bzTypeStr}-bz .bz-tit-ls .bz-tit-i`);
  bzTitleDom.each(function (i, dom) {
    var tabId = $(dom).attr('tab-id');
    var titleIndex = i + 1;
    if (tabId === `${bzTypeStr}-rt-` + paneId) {
      $(`#jdmbk1 .${bzTypeStr}-bz .bz-tit-ls .bz-tit-i[tab-id="${bzTypeStr}-rt-${paneId}"] .bz-name`).html('病灶' + titleIndex + "<span class='bz-cw'></span>");
      // $('#jdmbk1 .jdmbk-bz .bz-tit-ls .bz-tit-i[tab-id="jdmbk-rt-' + paneId + '"] .bz-name').html('病灶' + titleIndex + "<span class='bz-cw'></span>");
    }
  })
}

// 设置左右侧点击事件
function setScClick() {
  $('.bz-wrap .radio-wz').each(function () {
    $(this).click(function () {
      setBzTitle()
    })
  })
}

// 获取超声提示结果、描述
function getTipText() {
  //提示区
  let panelList = ['normalConditions', 'nzmzh', 'bkxc', 'narrowConditions']
  var str = '', resultStr = '';
  let strArr1 = [], strArr2 = [], strArr3 = [], strArr4 = []; //提示内容
  let bkBzObj = {}, xzBzObj = {};
  var jdmVal = getVal('[id="jdmbk-rt-70"]:checked') || '';
  var zdmVal = getVal('[id="jdmbk-rt-72"]:checked') || '';
  var zdmbdcVal = getVal('[id="jdmbk-rt-78"]:checked') || '';
  var mczhVal = getVal($('input[type=radio][name=mczh]:checked'));
  var zhwzVal = getVal($('input[type=radio][name=mczhwz]:checked'));
  for (let i = 0; i < panelList.length; i++) {
    if (panelList[i] === "normalConditions") {
      jdmVal ? str = '1、双侧颈动脉未见明显异常' : ''
      jdmVal && zdmVal ? str = '1、双侧颈动脉、椎动脉未见明显异常' : ''
      jdmVal && zdmbdcVal ? str = '1、双侧颈动脉未见明显异常\n2、双侧椎动脉管径不对称，流速及频谱未见明显异常' : ''
      !jdmVal && zdmVal ? str = '1、双侧椎动脉未见明显异常' : ''
      !jdmVal && zdmbdcVal ? str = '1、双侧椎动脉管径不对称，流速及频谱未见明显异常' : ''
      if (str) {
        strArr1.push(str)
        resultStr = str;
      }
      str = '';
    }
    if (panelList[i] === "nzmzh") {
      if (mczhVal === "无") {
        str = '双侧颈动脉未见明显异常'
      } else {
        mczhVal === "有" ? str = '颈动脉内中膜增厚' : ''
        zhwzVal ? str = zhwzVal + '颈动脉内中膜增厚' : ''
      }
      str ? strArr2.push(str) : ''
      str = ''
    }

    if (panelList[i] === "bkxc") {
      let bkxcBzData = getBzDetailInfo('jdmbk');
      bkBzObj = JSON.parse(JSON.stringify(bkxcBzData))
      let { leftBz = [], rightBz = [] } = bkxcBzData;
      if (leftBz.length > 0 || rightBz.length > 0) {
        let lbzsm1 = leftBz.findIndex(item => item.bkbzsm === '多发');
        let rbzsm1 = rightBz.findIndex(item => item.bkbzsm === '多发');
        let lbzsm2 = leftBz.findIndex(item => item.bkbzsm === '单发');
        let rbzsm2 = rightBz.findIndex(item => item.bkbzsm === '单发');
        let sm = '', cw = '';
        lbzsm2 !== -1 || rbzsm2 !== -1 ? sm = '单发' : ''
        lbzsm1 !== -1 || rbzsm1 !== -1 ? sm = '多发' : ''
        leftBz.length > 0 && rightBz.length > 0 ? cw = '双侧' : ''
        !cw && leftBz.length > 0 ? cw = '左侧' : ''
        !cw && rightBz.length > 0 ? cw = '右侧' : ''
        cw && sm ? strArr3.push(`${cw}颈动脉内中膜增厚并斑块形成（${sm}）`) : ''
        let leftRads = [], rightRads = [];
        leftRads = getRads(leftBz);
        rightRads = getRads(rightBz);
        leftRads.length > 0 ? strArr3.push(`${leftRads.join('；')}`) : ''
        rightRads.length > 0 ? strArr3.push(`${rightRads.join('；')}`) : ''
      }
    }
    if (panelList[i] === "narrowConditions") {
      let xzqkBzData = getBzDetailInfo('xzqk');
      xzBzObj = JSON.parse(JSON.stringify(xzqkBzData))
      // console.log('>>> xzqkBzData', xzqkBzData);
      let { leftBz = [], rightBz = [] } = xzqkBzData;
      let leftXz = getXzl(leftBz)
      let rightXz = getXzl(rightBz)
      leftXz.length > 0 ? strArr4.push(leftXz.join('；')) : ''
      rightXz.length > 0 ? strArr4.push(rightXz.join('；')) : ''
    }
  }
  // console.log('>>>strArr1', strArr1, strArr2, strArr3, strArr4);
  if (strArr2.length > 0 && strArr2[0] !== '双侧颈动脉未见明显异常') {
    str = `1、${strArr2[0]}`;
    if (zdmVal) {
      str += '\n2、双侧椎动脉未见明显异常'
    } else if (zdmbdcVal) {
      str += '\n2、双侧椎动脉管径不对称，流速及频谱未见明显异常'
    }
    resultStr = str;
    str = ""
  }
  if (strArr3.length > 0 || strArr4.length > 0) {
    zdmVal ? str = '1、双侧椎动脉未见明显异常' : ''
    zdmbdcVal ? str = '1、双侧椎动脉管径不对称，流速及频谱未见明显异常' : ''
    // str += '\n2、'
    for (let i = 0; i < strArr3.length; i++) {
      str ? str += `\n${i + (zdmVal || zdmbdcVal ? 2 : 1)}、${strArr3[i]}` : str = `1、${strArr3[i]}`
    }
    if (strArr4.length > 0) {
      str ? str += `\n${strArr3.length + (zdmVal || zdmbdcVal ? 2 : 1)}、${strArr4.join('；')}` : str = `1、${strArr4.join('；')}`
    }
    resultStr = str;
    str = ""
  }
  // if (strArr4.length > 0) {
  //   zdmVal ? str = '1、双侧椎动脉未见明显异常' : ''
  //   zdmbdcVal ? str = '1、双侧椎动脉管径不对称，流速及频谱未见明显异常' : ''
  //   for (let i = 0; i < strArr3.length; i++) {
  //     str ? str += `\n${ i + 2 }、${ strArr3[i] }` : str = `1、${ strArr3[i] }`
  //   }
  //   str ? str += `\n${ strArr3.length + 2 }、${ strArr4[0] }` : str = `1、${ strArr4[0] }`
  //   resultStr = str;
  //   str = ""
  // }
  $('#jdmbk-rt-95').val(resultStr)

  // 描述区
  let zcqk = [], nzmzh = [], bkxcArr = [], xzqkArr = [];
  if (jdmVal) {
    zcqk.push($('#jdmbk-rt-71').val());
  }
  if (zdmVal) {
    let zdmRadio = getVal('input[type=radio][name=zdmzc]:checked');
    str = $('#jdmbk-rt-73').val() + (zdmRadio ? zdmRadio + '；' : '') + $('#jdmbk-rt-77').val()
    zcqk.push(str)
    str = ''
  }
  if (zdmbdcVal) {
    let zdmSel1 = $('#jdmbk-rt-80').val() ? $('#jdmbk-rt-80').val() + '椎动脉管径较对侧细，内膜尚光滑，走行正常' : ''
    let zdmSel2 = $('#jdmbk-rt-81').val() ? $('#jdmbk-rt-81').val() + '椎动脉所见段管径正常，管腔透声尚可，走行正常' : ''
    str = `${$('#jdmbk-rt-79').val()}；${zdmSel1 ? zdmSel1 + '，' : ''}${zdmSel2 ? zdmSel2 + '。' : ''}${$('#jdmbk-rt-82').val()} \n${$('#jdmbk-rt-83').val()}`
    zcqk.push(str)
    str = ''
  }
  if (mczhVal === "有") {
    nzmzh.push(`${zhwzVal || ''} 颈总动脉内中膜增厚`)
  }
  let { all = [] } = bkBzObj;
  if (all.length > 0) {
    let bkxcArrStr = [];
    for (let i = 0; i < all.length; i++) {
      //  病灶边侧      病灶位置名称  起始段         病灶壁位     长度范围      MWT厚度      形态       回声        纤维帽薄厚    纤维帽(不)完整 易损斑块特征  溃疡型斑块     斑块内出血     ←&纤维帽破裂     管腔内血栓形成     新生血管形成       CDFI          Plaque-RADS
      let { bkbzwz = '', bkwzmc = '', bkjzdmwz = '', bkwzbw = '', bkcdfw = '', mwthd = '', bkxt = '', bkhs = '', bkxwmqx = '', bkxwmwz = '', ysbktz = '', kybkStr = '', bkncxfw = '', bkxwmplfw = '', bkgqnxsStr = '', bkxsxgxcStr = '', cdfiStr = '', bkplaque = '' } = all[i];
      let xwmStr = '';
      bkxwmqx ? xwmStr = `纤维帽显示${bkxwmqx}` : ''
      bkxwmwz ? xwmStr ? xwmStr += `，${bkxwmwz}` : xwmStr = `纤维帽显示${bkxwmwz}` : ''
      let yxbkStr = '';
      ysbktz === '是' ? yxbkStr = '存在易损斑块特征' : ''
      xwmStr && yxbkStr ? yxbkStr = '，存在易损斑块特征' : ''
      str = `${bkbzwz}位于${bkwzmc}${bkjzdmwz}${bkwzbw}，长度范围${bkcdfw}，MWT厚度${mwthd} mm，形态呈${bkxt}，${bkhs}；${xwmStr}${yxbkStr}${kybkStr ? '，' + kybkStr : ''}${bkncxfw}${bkxwmplfw}${bkgqnxsStr}${bkxsxgxcStr}${cdfiStr ? '\n' + cdfiStr : ''}`
      bkxcArrStr.push(str);
      str = '';
    }
    bkxcArr = setPositionList(bkxcArrStr)
  }
  let xzqkAll = xzBzObj.all || [];
  if (xzqkAll.length > 0) {
    let xzqkArrStr = [];
    for (let j = 0; j < xzqkAll.length; j++) {
      //    位置         位置名称      起始段         壁位         残余管径        原始管径       CDFI1、4项目   CDFI2、3项
      let { xzbzwz = '', xzwzmc = '', xzjzdmwz = '', xzwzbw = '', xznjStr1 = '', xznjStr2 = '', xzcdfi = '', cdfiStr = '' } = xzqkAll[j];
      let cdfi = cdfiStr ? cdfiStr : xzcdfi;
      if ((xznjStr1 && xznjStr2) || (!xznjStr1 && xznjStr2)) {
        cdfi = '，' + cdfi
      }
      str = `${xzbzwz}${xzwzmc}${xzjzdmwz}${xzwzbw} 狭窄，最窄处${xznjStr1 ? xznjStr1 + '，' : ''}${xznjStr2}${cdfi}`
      xzqkArrStr.push(str);
      str = ''
    }
    // console.log('>>>xzqkAll', xzqkAll);
    xzqkArr = setPositionList(xzqkArrStr)
  }
  // console.log('zcqk>>>1', zcqk);
  if (nzmzh.length > 0 || bkxcArr.length > 0 || xzqkArr.length > 0) {
    nzmzh.length > 0 ? zcqk[0] = `双侧颈总动脉、颈内动脉、颈外动脉颅外段走行正常，双侧颈动脉内中膜层增厚，动脉管腔未见明显异常扩张或狭窄。CDFI: 双侧颈动脉管腔内血流充盈好。PW: 双侧颈动脉血流速度及频谱形态正常。` : ''
    bkxcArr.length > 0 || xzqkArr.length > 0 ? zcqk[0] = `双侧颈总动脉、颈内动脉、颈外动脉颅外段走行正常，双侧颈动脉内中膜层增厚，内膜面不光滑，双侧颈动脉${bkxcArr.length > 1 ? '可探及多个强回声及混合回声斑块。' : '可分别探及1个斑块回声。'}` : ''
  }
  // console.log('zcqk>>>2', zcqk);
  // console.log('nzmzh>>>', nzmzh);
  // console.log('bkxcArr>>>', bkxcArr);
  // console.log('xzqkArr>>>', xzqkArr);
  let bkxcStr = bkxcArr.join('\n') || '';
  let xzqkStr = xzqkArr.join('\n') || '';
  if (bkxcStr || xzqkStr) {
    bkxcStr ? zcqk[0] += bkxcStr : '';
    xzqkStr ? zcqk[0] += `${bkxcStr && bkxcStr.charAt(bkxcStr.length - 1) !== '。' ? '，' : ''}${xzqkStr}` : '';
  }
  str = zcqk.join('\n')
  $('#jdmbk-rt-96').val(str)
  str = ''
}
// 去除字符串中的“左侧”或“右侧”并返回剩余部分  
function getCommonPart(str) {
  return str.replace(/^左侧|右侧/, '');
}
// 获取侧标记
function getSide(str) {
  return str.startsWith('左侧') ? '左侧' : (str.startsWith('右侧') ? '右侧' : null);
}
// 整合病灶两侧内容
function setPositionList(list) {
  const leftRightPairs = []; //整合左右侧病灶内容都相同的
  const unmatched = []; // 不同侧病灶不同内容的
  list.forEach(item => {
    let originItem = JSON.parse(JSON.stringify(item))
    const side = getSide(item);
    const commonPart = getCommonPart(item);
    if (side) {
      // 查找是否已经有对应的另一侧元素  
      const opposite = leftRightPairs.find(pair => pair.side === (side === '左侧' ? '右侧' : '左侧') && pair.commonPart === commonPart);
      if (opposite) {
        // 找到匹配的对，整合为双侧并移除这对元素  
        leftRightPairs.splice(leftRightPairs.indexOf(opposite), 1);
        leftRightPairs.push({ side: '双侧', commonPart: `双侧${commonPart}` });
      } else {
        // 没有找到匹配的对，暂时存储为待匹配元素  
        leftRightPairs.push({ side, commonPart });
      }
    } else {
      // 对于没有“左侧”或“右侧”标记的元素，直接添加到不匹配数组  
      unmatched.push(originItem);
    }
  });
  let result = leftRightPairs.map(pair => pair.side + pair.commonPart).concat(unmatched) || [];
  return result
}
// 获取Plaque-RADS值
function getRads(list) {
  let strArr = []
  for (let i = 0; i < list.length; i++) {
    let { bkplaque = '' } = list[i];
    let index = radsList.findIndex(item => item === bkplaque);
    if (index !== -1) {
      strArr.push(`${list[i].bkbzwz}${index > 4 ? '易损' : '最大'}斑块Plaque-RADS：${bkplaque}${index > 4 ? '（高风险）' : ''}`)
    }
  }
  return strArr
}
//获取狭窄提示
function getXzl(list) {
  let strArr = []
  for (let i = 0; i < list.length; i++) {
    let { xzbzwz = '', xzwzmc = '', xzjzdmwz = '', cd = '' } = list[i];
    strArr.push(`${xzbzwz}${xzwzmc}${xzjzdmwz}狭窄` + (cd ? `（${xzjzdmwz ? xzjzdmwz + '，' : ""}狭窄率${cd}）` : ''))
  }
  return strArr
}
//获取测量参数json数组
function getMeasureParam() {
  var measureParam = [];
  for (let i = 0; i < tableList.length; i++) {
    let { position = "", paramStr = "", paramName = "", paramCode = "", idStr = "" } = tableList[i];
    let params = {
      paramValue: $(`#${idStr}`).val(),
      paramCode,
      paramName: `${position}${paramStr} ${paramName}`
    }
    measureParam.push(params)
  }
  return measureParam;
}

//狭窄情况病灶交互
function initXzBzTigger() {
  var bkBzArea = curElem.find('.xzqk-bz .bz-wrap .bz-item');
  bkBzArea.each(function (i, dom) {
    var bzPane = $(dom);
    var paneId = bzPane.attr('tab-target').replace('xzqk-rt-', '');
    $(`input[type=radio][name=cd${paneId}]`).click(function () {
      let value = getVal(`input[type=radio][name=cd${paneId}]:checked`)
      $(`input[type=radio][name=xzcdfi${paneId}]`).each(function () {
        let pVal = $(this).attr('data-pval');
        // $(this).prop("checked", value === pVal)
        if (value === pVal) {
          $(this).click()
        }
      })
    })
    $(`input[type=radio][name=xzcdfi${paneId}]`).click(function (e) {
      let { target = {} } = e;
      let dataPval = $(target).attr('data-pval');

      let ipt1Val = getVal(`[id="xzqk-rt-${paneId}-28"]`, '', bzPane)
      let ipt3Val = getVal(`[id="xzqk-rt-${paneId}-32"]`, '', bzPane)
      let ipt2Val = '';
      if (dataPval === '50%-69%') {
        $(`#xzqk-rt-${paneId}-43`).val(ipt1Val)
        $(`#xzqk-rt-${paneId}-47`).val(ipt3Val)
        if (ipt1Val && ipt3Val) {
          ipt2Val = (ipt1Val / ipt3Val).toFixed(1);
          $(`#xzqk-rt-${paneId}-45`).val(ipt2Val)
        }
        $(`#xzqk-rt-${paneId}-49`).val() ? "" : $(`#xzqk-rt-${paneId}-49`).val('0.')
      }
      if (dataPval === '70-99%') {
        $(`#xzqk-rt-${paneId}-51`).val(ipt1Val)
        $(`#xzqk-rt-${paneId}-55`).val(ipt3Val)
        if (ipt1Val && ipt3Val) {
          ipt2Val = (ipt1Val / ipt3Val).toFixed(1);
          $(`#xzqk-rt-${paneId}-53`).val(ipt2Val)
        }
        $(`#xzqk-rt-${paneId}-57`).val() ? "" : $(`#xzqk-rt-${paneId}-57`).val('0.')
      }
      $(`input[type=radio][name=cd${paneId}]`).each(function () {
        let pVal = $(this).val();
        $(this).prop("checked", dataPval === pVal)
      })
    })
    let iptTextIdArr = ['45', '49', '53', '57']
    for (i = 0; i < iptTextIdArr.length; i++) {
      $(`#xzqk-rt-${paneId}-${iptTextIdArr[i]}`).change(function (elm) {
        let { target = {} } = elm;
        let pid = $(target).attr('pid');
        let ppid = $(`#${pid}`).attr('pid');
        $(`#${ppid}`).click();
      })
    }

  })
}

//整合斑块形成、狭窄情况病灶的各个节点，按左右侧划分
function getBzDetailInfo(type) {
  var bkBzDomData = { //斑块形成下的病灶
    // '左侧': [],
    // '右侧': [],
    // '左侧Bi': [],
    // '右侧Bi': [],
    leftBz: [],
    rightBz: [],
    all: [],
  }
  var bkBzArea = curElem.find(`.${type}-bz .bz-wrap .bz-item`);
  if (type === "jdmbk") {
    bkBzArea.each(function (i, dom) {
      var bzPane = $(dom);
      var paneId = bzPane.attr('tab-target').replace('jdmbk-rt-', '');
      //                                                                                                                              ↓易损斑   ↓是否溃   ↓是否斑块   ↓是否纤维 ↓管腔内     ↓新生
      //              ['病灶位置', '~数目', '位置名称','近中远段',  '位置壁','长度范围', '形态', '回声', '纤维帽情况','清晰',   '完整',     '块特征', '疡斑块','斑块内出血','帽破裂','血栓形成','血管形成',  'Plaque-RADS'];
      var radioName = ['bkbzwz', 'bkbzsm', 'bkwzmc', 'bkjzdmwz', 'bkwzbw', 'bkcdfw', 'bkxt', 'bkhs', 'bkxwmqk', 'bkxwmqx', 'bkxwmwz', 'ysbktz', 'kybk', 'bkncx', 'bkxwmpl', 'bkgqnxs', 'bkxsxgxc', 'bkplaque'];
      var obj = {
        index: i + 1
      }
      radioName.forEach(rName => {
        obj[rName] = getVal(`[name="${rName}${paneId}"]:checked`, '', bzPane) || '';
      })
      if (obj.bkbzwz) {
        //MWT厚度
        obj['mwthd'] = getVal(`[id="jdmbk-rt-${paneId}-30"]`, '', bzPane) || '';
        if (obj['kybk'] && obj['kybk'] === '是') {
          obj['kybkStr'] = '溃疡型斑块：斑块表面纤维帽破裂不连续，形成"火山口"征，"火山口"的长度与深度均≥2.0mm，CDFI：血流向斑块内灌注。'
        }
        if (obj['bkgqnxs'] && obj['bkgqnxs'] === '是') {
          obj['bkgqnxsStr'] = '斑块表面血栓形成：斑块表面弧形线样高回声连续性中断，表面可见细线样/条索样/块状低回声或中强回声；活动性血栓可见随血流摆动。'
        }
        if (obj['bkxsxgxc'] && obj['bkxsxgxc'] === '是') {
          obj['bkxsxgxcStr'] = '斑块内新生血管形成：SMI技术或颈动脉造影技术显示斑块内可见点状增强或短线状增强，造影提示3分时可见斑块内线状增强，可贯穿或大部贯穿斑块，或有血流流动征。'
        }
        //是否斑块内出血范围
        if (obj['bkncx'] && obj['bkncx'] === '是') {
          let fw1 = getVal(`[id="jdmbk-rt-${paneId}-65"]`, '', bzPane) || ''
          let fw2 = getVal(`[id="jdmbk-rt-${paneId}-66"]`, '', bzPane) || ''
          fw1 || fw2 ? obj['bkncxfw'] = `斑块内出血：斑块内可见极低回声区，范围约：${fw1}mm X${fw2} mm，表面纤维帽菲薄或不完整。` : ''
        }
        //是否纤维帽破裂范围
        if (obj['bkxwmpl'] && obj['bkxwmpl'] === '是') {
          let fw1 = getVal(`[id="jdmbk-rt-${paneId}-71"]`, '', bzPane) || ''
          let fw2 = getVal(`[id="jdmbk-rt-${paneId}-72"]`, '', bzPane) || ''
          fw1 || fw2 ? obj['bkxwmplfw'] = `斑块内出血：斑块内可见极低回声区，范围约：${fw1}mm X${fw2} mm，表面纤维帽菲薄或不完整。` : ''
        }
        //CDFI
        obj['cdfi'] = getVal(`[id="jdmbk-rt-${paneId}-82"]:checked`, '', bzPane) || '';
        if (obj['cdfi']) {
          obj['cdfiStr'] = `CDFI：${obj['cdfi']}`
        }
        obj['bkbzwz'] === "左侧" ? bkBzDomData['leftBz'].push(obj) : bkBzDomData['rightBz'].push(obj)
        // bkBzDomData[obj.bkbzwz].push(obj);  //单个病灶的详情
        bkBzDomData['all'].push(obj);
      }
    })
  } else {
    bkBzArea.each(function (i, dom) {
      var bzPane = $(dom);
      var paneId = bzPane.attr('tab-target').replace('xzqk-rt-', '');
      //              ['病灶位置', '位置名称','近中远段', '位置壁', '程度', 'CDFI']
      var radioName = ['xzbzwz', 'xzwzmc', 'xzjzdmwz', 'xzwzbw', 'cd', 'xzcdfi']
      var obj = {
        index: i + 1
      }
      radioName.forEach(rName => {
        obj[rName] = getVal(`[name="${rName}${paneId}"]:checked`, '', bzPane) || '';
      })
      if (obj.xzbzwz) {
        obj['xzbzwz'] === "左侧" ? bkBzDomData['leftBz'].push(obj) : bkBzDomData['rightBz'].push(obj)
        bkBzDomData['all'].push(obj);
      }
      getVal(`[id="xzqk-rt-${paneId}-23"]`, '', bzPane) ? obj['xznjStr1'] = `残余管径${getVal(`[id="xzqk-rt-${paneId}-23"]`, '', bzPane)} mm` : ''
      getVal(`[id="xzqk-rt-${paneId}-25"]`, '', bzPane) ? obj['xznjStr2'] = `原始管径${getVal(`[id="xzqk-rt-${paneId}-25"]`, '', bzPane)} mm` : ''
      if (obj.cd === '50%-69%' || obj.cd === '70-99%') {
        let gdStr = obj.cd === '50%-69%' ? getVal(`[id="xzqk-rt-${paneId}-43"]`, '', bzPane) : getVal(`[id="xzqk-rt-${paneId}-51"]`, '', bzPane)
        let psvStr = obj.cd === '50%-69%' ? getVal(`[id="xzqk-rt-${paneId}-45"]`, '', bzPane) : getVal(`[id="xzqk-rt-${paneId}-53"]`, '', bzPane)
        let yxdlsStr = obj.cd === '50%-69%' ? getVal(`[id="xzqk-rt-${paneId}-47"]`, '', bzPane) : getVal(`[id="xzqk-rt-${paneId}-55"]`, '', bzPane)
        let riStr = obj.cd === '50%-69%' ? getVal(`[id="xzqk-rt-${paneId}-49"]`, '', bzPane) : getVal(`[id="xzqk-rt-${paneId}-57"]`, '', bzPane)
        obj.cdfiStr = `斑块处血流信号充盈缺损，局部收缩期流速升高达${gdStr}cm/s，呈湍流频谱，狭窄段PSV/狭窄远段PSV比值为${psvStr}，远心段流速${yxdlsStr}cm/s，RI：${riStr}，${obj.cd === '50%-69%' ? '流速及频谱形态正常' : '呈低速低阻力改变，加速度时间延长，呈“小慢”波。其余受检双侧颈动脉流速及频谱形态正常'}`
      }
    })
  }
  return bkBzDomData
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = curElem.find('#jdmbk-rt-96').val();
  rtStructure.impression = curElem.find('#jdmbk-rt-95').val();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
    measureParam: getMeasureParam(), // 测量参数
  }
  // console.log(rtStructure);
}