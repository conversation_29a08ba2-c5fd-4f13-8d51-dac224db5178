$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var bzConClone = null;
var nurseList = [];
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //模版是否保存
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      togglePageType('preview')
      initBzCloneEle()
      displayBzContent()
    } else {
      togglePageType('edit')
      this.pageInit()
      echoAllergy()
    }
  }
}
function pageInit() {
  initBzCloneEle();
  window.getNurseList ? toNurseList() : '';
  window.getCommonUseList ? toCommonUseList() : '';
  window.getCommonUseList ? getProcessModeList() : '';
  window.getCommonUseList ? getAllergyList() : '';
  window.getExamImageAgentList ? getExamAgentList() : '';
  window.getCommonUseList ? getAlleryHistoryList() : '';
  // console.log('rtStructure.enterOptions.resultData',rtStructure.enterOptions.resultData)
  if(!rtStructure.enterOptions || !rtStructure.enterOptions.resultData || !rtStructure.enterOptions.resultData.length) {
    useServerTime('hlgm-rt-0008')
    addBzHandler();
    window.getCommonUseList ? addAlleryHandler() : '';
    window.getCommonUseList ? addDictionaryHandler('','allergySymptoms') : '';
    window.getCommonUseList ? addDictionaryHandler('','allergyHowToHandle') : '';
    window.getCommonUseList ? addDictionaryHandler('','nurse_irritability') : '';
  } else {
    displayBzContent();
  }
  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    //执行一个laydate实例
    // laydate.render({
    //   elem: '#hlgm-rt-0009-003',//指定元素
    //   type: 'date',
    //   value: ''
    // });

    // laydate.render({
    //   elem: '#hlgm-rt-0008',//指定元素
    //   type: 'datetime',
    //   value: ''
    // });
    // setupDateSelection('hlgm-rt-0009-003');
    // addLayDateTime('hlgm-rt-0008')
    // setupDateSelection('hlgm-rt-0008');
    // bindTimeFocus('hlgm-rt-0009-003')
    // bindTimeFocus('hlgm-rt-0008')
  })
}
function getAlleryHistoryList(){
  let nurseList = window.getCommonUseList({name: 'nurse_irritability'});
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="hlgm-rt-0001-002-${padNumberWithZeros(t+1,3)}"><input type="checkbox" class="rt-sr-w" name="ck-alery" value="${nurseList[t].value}" id="hlgm-rt-0001-002-${padNumberWithZeros(t+1,3)}" pid="hlgm-rt-0001-002" rt-sc="pageId:hlztpg1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    $('#allergy-history').html(html);
  }
}
function toNurseList(){
  let list = window.getNurseList({deptCode: publicInfo.userInfo&&publicInfo.userInfo.param&&publicInfo.userInfo.param.deptCode || ''});
  let userList = []
  if (list && list.length > 0) {
    for (var t = 0; t < list.length; t++) {
      userList.push({ title: list[t].name, id: list[t].userId })
    }
  }
  nurseList = userList;
  initInpAndSel('hlgm-rt-0007', userList,optHandler);
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    // console.log('publicInfo',publicInfo)
    $('#hlgm-rt-0007').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#hlgm-rt-0007').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList,cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}
function toCommonUseList(){
  let nurseList = window.getCommonUseList({name: 'NURSE_ALLERGY_SYMPTOMS'});
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="hlgm-rt-0003-${padNumberWithZeros(t+1,3)}"><input type="checkbox" class="rt-sr-w" name="symptoms" value="${nurseList[t].value}" id="hlgm-rt-0003-${padNumberWithZeros(t+1,3)}" pid="hlgm-rt-0003" rt-sc="pageId:hlgm1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;code:3511;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    $('#allergy-symptoms').html(html);
  }
  var newPaneBlock = $('#allergy-symptoms');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}
function getProcessModeList(){
  let nurseList = window.getCommonUseList({name: 'NURSE_ALLERGY_PROCESS_MODE'});
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="hlgm-rt-0004-${padNumberWithZeros(t+1,3)}"><input type="checkbox" class="rt-sr-w" name="process" value="${nurseList[t].value}" id="hlgm-rt-0004-${padNumberWithZeros(t+1,3)}" pid="hlgm-rt-0004" rt-sc="pageId:hlgm1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;code:3512;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    $('#allergy-process').html(html);
  }
  var newPaneBlock = $('#allergy-process');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}
function getAllergyList(){
  let nurseList = window.getCommonUseList({name: 'nurse_irritability'});
  let html = '';
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      html = html + `<label class="radio-label" for="hlgm-rt-0001.${padNumberWithZeros(t+1,3)}"><input type="checkbox" class="rt-sr-w" name="irritability" value="${nurseList[t].value}" id="hlgm-rt-0001.${padNumberWithZeros(t+1,3)}" pid="hlgm-rt-0001" rt-sc="pageId:hlgm1;name:${nurseList[t].value};wt:4;desc:${nurseList[t].value};vt:;pvf:;" ><span class="rt-sr-lb">${nurseList[t].value}</span>
      </label>`
    }
    $('#allergy-irritability').html(html);
  }
  var newPaneBlock = $('#allergy-irritability');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}
function getExamAgentList(){
  console.log('publicInfo',publicInfo);
  let agentRes = window.getExamImageAgentList({examNo: publicInfo.examNo});
  if(agentRes&&agentRes.length){
    $('#hlgm-rt-0009-001').val(agentRes[0].agentName);
  }
}
// 添加过敏史及禁忌征
function addAlleryHandler(oldPaneId) {
  var newPaneBlock = $('#allergy-history');
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('#allergy-history').find("[rt-sc]");
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
// 添加过字典内容
function addDictionaryHandler(oldPaneId,type) {
  var newPaneBlock = '';
  if(type === 'allergySymptoms'){
    newPaneBlock = $('#allergy-symptoms');
  }else if(type === 'allergyHowToHandle'){
    newPaneBlock = $('#allergy-process');
  }else if(type === 'nurse_irritability'){
    newPaneBlock = $('#allergy-irritability');
  }
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = '';
    if(type === 'allergySymptoms'){
      wCon = $('#allergy-symptoms').find("[rt-sc]");
    }else if(type === 'allergyHowToHandle'){
      wCon = $('#allergy-process').find("[rt-sc]");
    }else if(type === 'nurse_irritability'){
      wCon = $('#allergy-irritability').find("[rt-sc]");
    }
    // console.log('wCon',wCon);
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('childOldIdAndDom',childOldIdAndDom);
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}
function initBzCloneEle() {
  var bzCon = curElem.find('.advice-list .advice-item').clone(true);
  bzConClone = '<div class="advice-item" tab-target="hlgm-bzlist-000">' + bzCon.html() +'</div>';
  // console.log('bzConClone',bzConClone);
  curElem.find('.advice-list').html('');
}
function displayBzContent() {
  // 回显镇静记录
  allResData = rtStructure.enterOptions 
    && rtStructure.enterOptions.resultData ? 
    rtStructure.enterOptions.resultData : [];
    console.log('allResData',allResData);
  if(allResData && allResData.length) {
    var alleryData = allResData.filter(alleryItem => alleryItem.id === 'hlgm-rt-0001')
    // console.log('alleryData',alleryData);
    if(alleryData && alleryData.length) {
      var alleryList = alleryData[0].child[0].child || [];
      console.log('alleryList',alleryList)
      alleryList.forEach((item) => {
        // console.log('item',item)
        addAlleryHandler(item.id)
      })
    }else{
      addAlleryHandler('')
    }
    var bzData = allResData.filter(bzItem => bzItem.id === 'hlgm-bzlist-1')
    console.log('bzData',bzData);
    if(bzData && bzData.length) {
      var bzList = bzData[0].child || [];
      bzList.forEach((item) => {
        var paneId = item.id.replace('hlgm-bzlist-', '');
        addBzHandler(paneId)
      })
    }
    var symptoms = allResData.filter(bzItem => bzItem.id === 'hlgm-rt-0003')
    // console.log('bzData',bzData);
    if(symptoms && symptoms.length) {
      var bzList = symptoms[0].child || [];
      bzList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList ? addDictionaryHandler(item.id,'allergySymptoms') : '';
      })
    }else{
      window.getCommonUseList ? addDictionaryHandler('','allergySymptoms') : '';
    }
    var handleData = allResData.filter(metalItem => metalItem.id === 'hlgm-rt-0004')
    if(handleData && handleData.length) {
      var metalList = handleData[0].child || [];
      metalList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList ? addDictionaryHandler(item.id,'allergyHowToHandle') : '';
      })
    }else{
      window.getCommonUseList ? addDictionaryHandler('','allergyHowToHandle') : '';
    }
    var irritabilityData = allResData.filter(metalItem => metalItem.id === 'hlgm-rt-0001');
    console.log('irritabilityData',irritabilityData)
    if(irritabilityData && irritabilityData.length&&irritabilityData[0].child) {
      var metalList = irritabilityData[0].child || [];
      metalList.forEach((item) => {
        // console.log('item',item)
        window.getCommonUseList ? addDictionaryHandler(item.id,'nurse_irritability') : '';
      })
    }else{
      window.getCommonUseList ? addDictionaryHandler('','nurse_irritability') : '';
    }
  } else {
    curElem.find('.advice-list .advice-item').hide();
  }
  // 监听表单内所有input, textarea, select的input或change事件  
  $('#hlgm1').on('input change blur', 'input, textarea, select', function() { 
    // console.log('input change blur', this) 
    // 向父页面发送消息
    window.parent.postMessage({
      message: 'formChangeNotice',
      data: '表单内容已修改'
    }, '*');
  });  
}
function delTab(vm, paneId){
  var allSiblings = $(vm).parent().parent().parent().siblings();
  // console.log('allSiblings',allSiblings)
  allSiblings.each(function(index) {
      console.log($(this).children().children()[0],index); // 输出：first 和 third
      $(this).children().children().children().each(function(cIndex) {
        // console.log('$(this)',$(this));
        if(cIndex === 0){
          $(this).html('医嘱记录'+(index + 1))
        }
      })
  });
  $(vm).parent().parent().parent().remove();
  if(rtStructure && rtStructure.idAndDomMap) {
    for(var key in rtStructure.idAndDomMap) {
      if(key.indexOf(paneId) > -1) {
        delete rtStructure.idAndDomMap[key];
      }
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
  }
}
// 添加处理医嘱,oldPaneId已保存过的
function addBzHandler(oldPaneId) {
  var paneId = oldPaneId || createUUidFun();
  var bzLen = $('.advice-content').length;
  console.log('bzLen',bzLen,paneId)
  var activeTab = 'hlgm-bzlist-' + paneId;
  var newPaneBlock = appendBzHtml(paneId, bzLen,oldPaneId);
  // console.log('newPaneBlock',newPaneBlock);
  if(rtStructure) {
    if(rtStructure.idAndDomMap['']) {
      delete rtStructure.idAndDomMap[''];
    }
    var oldIdAndDom = oldPaneId && rtStructure.idAndDomMap[activeTab] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[activeTab])) : {};
    rtStructure.idAndDomMap[activeTab] = {
      id: activeTab,
      desc: '处理医嘱',
      name: '处理医嘱',
      pid: 'hlgm-bzlist-1',
      pvf: '',
      req: '1',
      rtScPageNo: 1,
      value: oldPaneId && oldIdAndDom.value ? oldIdAndDom.value : ('医嘱记录' + (bzLen+1)),
      wt: '',
      vt: '',
      itemList: oldIdAndDom.itemList,
      lastVal: oldIdAndDom.lastVal
    }
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap)
    var wCon = $('.advice-list .advice-item[tab-target="'+activeTab+'"]').find("[rt-sc]");
    wCon.each(function(wIndex, wItem) {
      var node = $(wItem);
      var id = node.attr('id') || '';
      var pid = node.attr('pid') || '';
      var groupId = node.attr('name') || '';
      var sc = node.attr('rt-sc');
      var scArr = sc.split(';');
      var childOldIdAndDom = oldPaneId && rtStructure.idAndDomMap[id] ? JSON.parse(JSON.stringify(rtStructure.idAndDomMap[id])) : {};
      // console.log('oldIdAndDom',childOldIdAndDom)
      var scObj = {
        id: id,
        pid: pid,
        rtScPageNo: 1,
        value: oldPaneId && childOldIdAndDom.value ? childOldIdAndDom.value : '',
        itemList: childOldIdAndDom.itemList,
        lastVal: childOldIdAndDom.lastVal
      };
      if(groupId) {
        scObj['groupId'] = groupId;
      }
      scArr.forEach(function(scItem) {
        var key = scItem.split(':')[0];
        var value = scItem.split(':')[1];
        if(key) {
          if(key === 'code') {
            scObj[key] = value;
            var findCodeNode = $('[id="'+id+'"].rt-sr-w');
            findCodeNode.attr('code', value);
          } else {
            var numberList = ['left', 'top', 'wt'];
            scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
          }
        }
      })
      // console.log('scObj',scObj)
      rtStructure.idAndDomMap[id] = {...scObj};
    })
    // console.log('rtStructure.idAndDomMap',rtStructure.idAndDomMap);
    initBzTigger(newPaneBlock, oldPaneId ? true : false);
    rtStructure.init(true);
    rtStructure.initChildDisabled(newPaneBlock[0]);
  }
}

function initBzTigger(newPaneBlock, oldFlag) {
  // console.log('oldFlag',newPaneBlock);
  if(oldFlag) {
    newPaneBlock.find('.rt-sr-w').each(function(i, widget) {
      var id = $(widget).attr('id');
      if(rtStructure && rtStructure.idAndDomMap && rtStructure.idAndDomMap[id] && rtStructure.idAndDomMap[id].value) {
        rtStructure.setFormItemValue(widget, rtStructure.idAndDomMap[id].value);
      }
    })
  }
}

// 处理新增处理医嘱的html
function appendBzHtml(paneId, bzLen,oldPaneId) {
  console.log('paneId',paneId)
  var reg = new RegExp('000', 'ig');
  var content = bzConClone.replace(reg, paneId);  
  $('.advice-list').append(content);
  $('#hlgm-bzlist-'+paneId+'-1').html('医嘱记录' + (bzLen + 1));
  layui.use(['laydate', 'table'], function(){
    // var laydate = layui.laydate;
    // laydate.render({
    //   elem: '#hlgm-bzlist-'+paneId+'-2',//指定元素
    //   type: 'datetime',
    //   value:  ''
    // });
    bindTimeFocus('hlgm-bzlist-'+paneId+'-2');
    addLayDateTime('hlgm-bzlist-'+paneId+'-2')
    setupDateSelection('hlgm-bzlist-'+paneId+'-2');
  })
  initInpAndSel('hlgm-bzlist-'+paneId+'-3', nurseList);
  initInpAndSel('hlgm-bzlist-'+paneId+'-4', nurseList);
  var newPaneBlock = $('.advice-list .advice-item[tab-target="hlgm-bzlist-'+paneId+'"] .advice-content');
  // console.log('newPaneBlock',newPaneBlock);
  return newPaneBlock;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}
// 回显过敏记录
function echoAllergy() {    
  if(!isSavedReport){
    let { examInfo } = rtStructure
    let {examNo,busId,busType } = examInfo
    window.getResJsonHandler({
      patternId:'7007',examNo,busId,busType
    },{successCb:(success)=>{
      let { result=[] } = success || {}
      if(result.length){
        let { docContent } = result[result.length-1]
        let docObj = JSON.parse(docContent || '{}')
        if(docObj.docContent){
          let { docAttr } = docObj.docContent
          let allergy = docAttr.find(item=>item.id === 'hlztpg-rt-0003')
          let allergyChild = allergy.child[0]
        if(allergyChild){
          if(allergyChild.val==='有'){
            $('#hlgm-rt-0001-002').click()
            if(allergyChild.child.length){
              allergyChild.child.forEach(item=>{
                let idList = item.id.split('-')
                let suffix = idList[idList.length-1]
                if(suffix==='900'){
                  $('#hlgm-rt-0001-002-900').val(item.val)
                }else{
                  $(`#hlgm-rt-0001-002-${suffix}`).click()
                }  
              })
            }
          } else {
            $('#hlgm-rt-0001-001').click()
          } 
        }
        }
      }
    }})
  }
}
function togglePageType(type) {
  $(`[data-type]`).hide()
  $(`[data-type=${type}]`).show()
  if (type === 'preview') {
    $('[data-key]').each(function () {
      let key = $(this).attr('data-key')
      if (idAndDomMap) {
        let result = []
        Object.keys(idAndDomMap).forEach(idKey => {
          if (idKey.startsWith(key)) {
            let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value : '';
            if (value) {
              result.push(value)
            }
          }
        })
        if (key === 'hlhdyy-rt-0006-') {
          $(this).html(result.join(':'))
        } else if (key === 'hlhdyy-rt-0008-' || key === 'hlhdyy-rt-0009-') {
          $(this).html(result.join('、'))
        } else {
          $(this).html(result.join(','))
        }
        this.style.color = '#000'
      }
    })
    $('.layui-inline, .layui-form, .showInt').removeClass('showInt')
  }
  
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }