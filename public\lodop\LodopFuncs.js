//==本JS是加载Lodop插件或Web打印服务CLodop/Lodop7的综合示例，可直接使用，建议理解后融入自己程序==

//用双端口加载主JS文件Lodop.js(或CLodopfuncs.js兼容老版本)以防其中某端口被占:
var MainJS = "CLodopfuncs.js",
  URL_WS1 = "ws://localhost:8000/" + MainJS,                //ws用8000/18000
  URL_WS2 = "ws://localhost:18000/" + MainJS,
  URL_HTTP1 = "http://localhost:8000/" + MainJS,              //http用8000/18000
  URL_HTTP2 = "http://localhost:18000/" + MainJS,
  URL_HTTP3 = "https://localhost.lodop.net:8443/" + MainJS;   //https用8000/8443

var CreatedOKLodopObject, CLodopIsLocal, cLodopLoadJsState;

//==判断是否需要CLodop(那些不支持插件的浏览器):==
function needCLodop() {
  try {
    var ua = navigator.userAgent;
    if (ua.match(/Windows\sPhone/i) ||
      ua.match(/iPhone|iPod|iPad/i) ||
      ua.match(/Android/i) ||
      ua.match(/Edge\D?\d+/i))
      return true;
    var verTrident = ua.match(/Trident\D?\d+/i);
    var verIE = ua.match(/MSIE\D?\d+/i);
    var verOPR = ua.match(/OPR\D?\d+/i);
    var verFF = ua.match(/Firefox\D?\d+/i);
    var x64 = ua.match(/x64/i);
    if ((!verTrident) && (!verIE) && (x64)) return true;
    else if (verFF) {
      verFF = verFF[0].match(/\d+/);
      if ((verFF[0] >= 41) || (x64)) return true;
    } else if (verOPR) {
      verOPR = verOPR[0].match(/\d+/);
      if (verOPR[0] >= 32) return true;
    } else if ((!verTrident) && (!verIE)) {
      var verChrome = ua.match(/Chrome\D?\d+/i);
      if (verChrome) {
        verChrome = verChrome[0].match(/\d+/);
        if (verChrome[0] >= 41) return true;
      }
    }
    return false;
  } catch (err) {
    return true;
  }
}

//==检查加载成功与否，如没成功则用http(s)再试==
//==低版本CLODOP6.561/Lodop7.043及前)用本方法==
function checkOrTryHttp() {
  if (window.getCLodop) {
    cLodopLoadJsState = "complete";
    return true;
  }
  if (cLodopLoadJsState == "loadingB" || cLodopLoadJsState == "complete") return;
  cLodopLoadJsState = "loadingB";
  var head = document.head || document.getElementsByTagName("head")[0] || document.documentElement;
  var JS1 = document.createElement("script")
    , JS2 = document.createElement("script")
    , JS3 = document.createElement("script");
  JS1.src = URL_HTTP1;
  JS2.src = URL_HTTP2;
  JS3.src = URL_HTTP3;
  JS1.onload = JS2.onload = JS3.onload = JS2.onerror = JS3.onerror = function () {
    cLodopLoadJsState = "complete";
  }
  JS1.onerror = function (e) {
    if (window.location.protocol !== 'https:')
      head.insertBefore(JS2, head.firstChild); else
      head.insertBefore(JS3, head.firstChild);
  }
  head.insertBefore(JS1, head.firstChild);
}

//==获取LODOP对象主过程,判断是否安装、需否升级:==
function getLodopFn() {
  var LODOP;
  var htmlStr = '<p>Lodop打印控件未安装，请点击这里'
    + '<a class="download-btn" href="./lodop/CLodop_Setup_for_Win32NT.exe" target="_self">下载安装</a>'
    + '<p>(下载后请点击，点击exe文件开始执行安装)</p>'
    + '</p>'
    + '<form method="dialog" style="text-align:right;"><button class="common-btn btn-primary" onclick="closeLodopDialog(true);">已安装启动</button>'
    + '<form method="dialog" style="text-align:right;"><button class="common-btn" onclick="closeLodopDialog(false, true);">不再提示</button>'
    + '<button class="common-btn" onclick="closeLodopDialog()">取消</button></form>'
  ;

  var dialogContent = document.createElement("dialog");
  dialogContent.id = "dialogContent";
  dialogContent.className = "lodop-dialog";
  dialogContent.innerHTML = htmlStr;
  document.documentElement.appendChild(dialogContent);

  try {
    var isWinIE = (/MSIE/i.test(navigator.userAgent)) || (/Trident/i.test(navigator.userAgent));
    var isWinIE64 = isWinIE && (/x64/i.test(navigator.userAgent));

    if (needCLodop()) {
      try {
        LODOP = window.getCLodop();
        console.log("lodop 注册");
        LODOP.SET_LICENSES("广州易联众睿图信息技术有限公司", "AB6A1D844193D41AED2C0A440D84590C86D", "廣州易聯眾睿圖信息技術有限公司", "7B12FF9A6A6887B0335EBC9EEAEA9DDE862");
        LODOP.SET_LICENSES("THIRD LICENSE", "", "Guangzhou Yilian Zhongruitu Information Technology Co., Ltd", "0C44FA3299415BA08EB2CFE0ECF20DC928C");
        window.winLodop = LODOP;
        return LODOP;
      } catch (err) {
        if (!LODOP) {
          if(!localStorage.getItem('clodopInstallTipNeverShow')) {
            document.getElementById('dialogContent').show();
            // 把 mask 添加到body 里。
            createMask()
          }
          return;
        }
      }
      // if (!LODOP && cLodopLoadJsState !== "complete") {
      //     if (!cLodopLoadJsState)
      //         alert("未曾加载Lodop主JS文件，请先调用loadCLodop过程."); else
      //         alert("网页还没下载完毕，请稍等一下再操作.");
      //     return;
      // }
      // var strAlertMessage;

      // if (!LODOP) {

      //     document.getElementById('dialogContent').show();
      //     // 把 mask 添加到body 里。
      //     createMask()
      //     return;
      // } else {
      // }
    } else {
      //==如果页面有Lodop插件就直接使用,否则新建:==
      if (!CreatedOKLodopObject) {
        LODOP = document.createElement("object");
        LODOP.setAttribute("width", 0);
        LODOP.setAttribute("height", 0);
        LODOP.setAttribute("style", "position:absolute;left:0px;top:-100px;width:0px;height:0px;");
        if (isWinIE)
          LODOP.setAttribute("classid", "clsid:2105C259-1E0C-4534-8141-A753534CB4CA");
        else
          LODOP.setAttribute("type", "application/x-print-lodop");
        document.documentElement.appendChild(LODOP);
        CreatedOKLodopObject = LODOP;
      } else
        LODOP = CreatedOKLodopObject;
      //==Lodop插件未安装时提示下载地址:==
      if ((!LODOP) || (!LODOP.VERSION)) {
        if(!localStorage.getItem('clodopInstallTipNeverShow')) {
          document.getElementById('dialogContent').show();
          // 把 mask 添加到body 里。
          createMask()
        }
        return LODOP;
      }
    }
    //===如下空白位置适合调用统一功能(如注册语句、语言选择等):=======================
    // LODOP.SET_LICENSES("易联众信息技术股份有限公司", "2BCE21A2F9D18B1D8DA44DB77AD304F2", "", "");

    console.log("lodop 注册");
    LODOP.SET_LICENSES("广州易联众睿图信息技术有限公司", "AB6A1D844193D41AED2C0A440D84590C86D", "廣州易聯眾睿圖信息技術有限公司", "7B12FF9A6A6887B0335EBC9EEAEA9DDE862");
    LODOP.SET_LICENSES("THIRD LICENSE", "", "Guangzhou Yilian Zhongruitu Information Technology Co., Ltd", "0C44FA3299415BA08EB2CFE0ECF20DC928C");
    window.winLodop = LODOP;
    //===============================================================================
    return LODOP;

  } catch (err) {
    console.error(window.getCLodop);
    alert("getLodop出错:" + err);
  }
}

// 添加遮罩层
function createMask() {
  // 如果 mask 已经存在了，就不用再创建mask了
  if (document.getElementById("mask")) {
    return;
  }
  var maskHtml = document.createElement("div");
  maskHtml.id = "mask";
  maskHtml.className = "lodop-dialog__wrapper";
  document.documentElement.appendChild(maskHtml);
  // 点击mask，就去掉mask
  // maskHtml.addEventListener("click", deleteMask );
}

// 关闭提示弹框
function closeLodopDialog(refresh, neverShow) {
  document.getElementById('dialogContent').close();
  var mask = document.getElementById("mask");
  // 如果 mask 存在，就删除
  if (mask) {
    // 移除 mask 上的点击事件
    // mask.removeEventListener("click", deleteMask );
    // 删除 mask 标签
    mask.parentNode.removeChild(mask);
  }
  if (refresh) window.location.reload();
  if (neverShow) localStorage.setItem('clodopInstallTipNeverShow', true);
}

//==加载Lodop对象的主过程:==
function loadCLodop() {
  if(window.winLodop) {
    return;
  }
  console.log("%c加载sreport_Lodop对象...", "background: green; color: #fff; line-height: 20px;");
  if (!needCLodop()) return;
  CLodopIsLocal = !!((URL_WS1 + URL_WS2).match(/\/\/localho|\/\/127.0.0./i));
  cLodopLoadJsState = "loadingA";
  if (!window.WebSocket && window.MozWebSocket) window.WebSocket = window.MozWebSocket;
  //ws方式速度快(小于200ms)且可避免CORS错误,但要求Lodop版本足够新:
  try {
    var WSK1 = new WebSocket(URL_WS1);
    WSK1.onopen = function (e) {
      setTimeout("checkOrTryHttp();", 200);
    }
    WSK1.onmessage = function (e) {
      if (!window.getCLodop) eval(e.data);
    }
    WSK1.onerror = function (e) {
      var WSK2 = new WebSocket(URL_WS2);
      WSK2.onopen = function (e) {
        setTimeout("checkOrTryHttp();", 200);
      }
      WSK2.onmessage = function (e) {
        if (!window.getCLodop) eval(e.data);
      }
      WSK2.onerror = function (e) {
        checkOrTryHttp();
      }
    }
  } catch (e) {
    console.error(e);
    checkOrTryHttp();
  }
}

// (function () {
//   loadCLodop();
// })();