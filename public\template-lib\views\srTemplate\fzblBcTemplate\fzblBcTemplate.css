.singleDisEditReport.main-page{
  min-width: unset;
}
#fzblBc1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#fzblBc1 .fzblBc-edit{
  padding: 8px 12px;
}
#fzblBc1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#fzblBc1 .black-lb {
  color: #303133;
}
#fzblBc1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#fzblBc1 .blue-lb:hover {
  opacity: 0.8;
}
#fzblBc1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#fzblBc1 .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#fzblBc1 .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#fzblBc1 .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#fzblBc1 .text-size .on {
  display: none;
}
#fzblBc1 .editor-area textarea {
  width: 100%;
  height: 100px;
  border: none;
  font-size: 16px;
  padding-bottom: 0;
}
#fzblBc1 .p-item + .p-item {
  margin-top: 12px;
}
#fzblBc1 .editor-area.default textarea{
  font-size: 16px;
}
#fzblBc1 .editor-area.large textarea{
  font-size: 18px;
}
#fzblBc1 .editor-area.larger textarea{
  font-size: 20px;
}
#fzblBc1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#fzblBc1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#fzblBc1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#fzblBc1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#fzblBc1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#fzblBc1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

/* 下拉选择项 */
#fzblBc1 .in-drop {
  position: relative;
  width: 110px;
}
#fzblBc1 .in-drop input {
  padding-right: 18px;
  width: 110px;
}
#fzblBc1 .in-drop.inb {
  display: inline-block;
  width: 48%;
}
[isview='true'] #fzblBc1 .in-drop.inb {
  display: block;
  width: 100%;
}
#fzblBc1 .in-drop .iarr {
  position: absolute;
  top: 10px;
  right: 8px;
  width: 6px;
  height: 6px;
  z-index: 9;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg);
}
.layui-dropdown {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}
/* 多选 */
#fzblBc1 .multi-w {
  width: 100%;
  height: 30px;
  border: 1px solid #DCDFE6;
  padding: 0 18px 0 8px;
  border-radius: 3px;
  background: #fff;
}
#fzblBc1 .multi-w + input {
  display: none;
}
#fzblBc1 xm-select {
  height: 100%;
  min-height: unset;
  line-height: 26px;
  border: none;
}
#fzblBc1 xm-select .xm-icon,
#fzblBc1 xm-select .xm-label-block {
  display: none;
}
#fzblBc1 xm-select .xm-label {
  right: 0;
}
#fzblBc1 xm-select .label-content {
  padding: 0;
  line-height: 26px;
}
*:disabled {
  background: #fff;
  color: unset;
}
textarea[readonly] {
  cursor: not-allowed;
}
#fzblBc1 .fzblBc-view {
  display: none;
}
[isview="true"] #fzblBc1 .fzblBc-edit {
  display: none;
}
[isview="true"] #fzblBc1 .fzblBc-view {
  display: block;
}
#fzblBc1 .fzblBc-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 20px 56px;
}
#fzblBc1 .fzblBc-view .view-head {
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#fzblBc1 .fzblBc-view .hos-logo{
  width: 100%;
  text-align: center;
  margin-bottom: 4px;
}
#fzblBc1 .fzblBc-view .hos-logo img{
  width: 284px;
  height: 40px;
}
#fzblBc1 .fzblBc-view .page-tit{
  font-size: 24px;
  font-weight: 600;
}
#fzblBc1 .fzblBc-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#fzblBc1 .fzblBc-view .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
}
#fzblBc1 .fzblBc-view .black-txt {
  color: #000;
  font-size: 14px;
}
#fzblBc1 .fzblBc-view .bold {
  font-weight: bold;
}
#fzblBc1 .fzblBc-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}
#fzblBc1 .fzblBc-view .info-i + .info-i {
  margin-left: 8px;
}
#fzblBc1 .fzblBc-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#fzblBc1 .fzblBc-view .view-patient .p-item {
  margin-top: 8px;
}
#fzblBc1 .fzblBc-view .desc-con {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  display: none;
}
[pattern-editor="true"] #fzblBc1 .fzblBc-view .desc-con {
  display: block;
}
#fzblBc1 .fzblBc-view .report-wrap {
  min-height: 40px;
  padding: 4px 0;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}
#fzblBc1 .fzblBc-view .reporter-i {
  flex: 1;
  display: flex;
  align-items: center;
}
#fzblBc1 .fzblBc-view .reporter-i > span:not([con-data]):not([data-key]) {
  width: 70px;
}
#fzblBc1 .fzblBc-view .reporter-i > span[con-data] {
  flex: 1;
}
#fzblBc1 .fzblBc-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
#fzblBc1 .fzblBc-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#fzblBc1 .fzblBc-view .tip-wrap {
  margin-top: 8px;
}
#fzblBc1 .fzblBc-view .rpt-img-ls {
  display: flex;
  flex-wrap: wrap;
  padding: 32px 0 0 34px;
  display: none;
}
#fzblBc1 .fzblBc-view .item-img {
  width: 265px;
  height: 200px;
  border: 1px solid #eee;
  margin-right: 34px;
  margin-bottom: 8px;
}
#fzblBc1 .fzblBc-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#fzblBc1 .p-item:nth-child(1) .editor-area textarea {
  height: 72px !important;
}
#fzblBc1 .p-item:nth-child(2) .editor-area textarea {
  height: 160px !important;
}
#fzblBc1 .p-item:nth-child(3) .editor-area textarea {
  height: 280px !important;
}
#fzblBc1 .p-item:nth-child(2) .text-size,#fzblBc1 .p-item:nth-child(3) .text-size {
  display: none;
}
#fzblBc1 .fzblBc-view .blh-tit{
  text-align: right;
}
#fzblBc1 .fzblBc-view .desc-item {
  margin-bottom: 12px;
}
#fzblBc1 .fzblBc-view .desc-item.bc-item:not(:last-child) {
  margin-bottom: 0;
}
#fzblBc1 .fzblBc-view .con-reporter {
  display: flex;
  justify-content: space-between;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #fzblBc1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #fzblBc1 .fzblBc-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #fzblBc1 .fzblBc-view .view-head,
[entry-type="5"] #fzblBc1 .fzblBc-view .view-patient,
[entry-type="5"] #fzblBc1 .fzblBc-view .tip-wrap {
  display: none;
}
/* 去掉边框，除表格外 */
[entry-type="5"] #fzblBc1 .fzblBc-view div {
  border-bottom: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}

