$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var allUrlParams = {
  optId: '',
  optName: '',
  patternId: '',
  patDocId: '',
  busId: '',
  busType: '',
  name: '',
  age: '',
  sex: '',
}
var patternInfo = {};
function initHtmlScript(ele) {
  for(var key in allUrlParams) {
    allUrlParams[key] = getParamByName(key) || '';
  }
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //有联动的控件，当父节点值为空时，子节点可编辑
    }
  }
  if(rtStructure) {
    if(rtStructure.enterOptions && rtStructure.enterOptions.patternInfo) {
      patternInfo = rtStructure.enterOptions.patternInfo;
    }
    if(!allUrlParams.patternId) {
      allUrlParams.patternId = patternInfo.patternId;
    }
    if(!allUrlParams.patDocId) {
      allUrlParams.patDocId = patternInfo.patDocId;
    }
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      
    } else {

    }
  }
  $('.dev-img').each(function(dI, ele){
    var imgSrc = $(ele).attr('src');
    if(location.href.indexOf('/sreport') > -1) {
      imgSrc = '/sreport' + imgSrc;
      $(ele).attr('src', imgSrc);
    }
  })
  // getSignResult(loopSignRes, true);
  getDocResult();
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
}
/**
 * CA流程
 * 查询是否有文档数据--有-->查询签名文件--没有-->显示签名按钮
 * 查询是否有文档数据--无-->判断是否已经签名optType3--没有-->显示签名按钮
 */

// 查看是否有文档结果数据
function getDocResult() {
  if(!patternInfo.patternInfo || !patternInfo.patternInfo.flags || 
      patternInfo.patternInfo.flags[1] !== '1' || patternInfo.patternInfo.flags[2] !== '1') {
    toggleBtnFun({tipMsg:'模板配置出错，该模板不要CA签名', isSign: false, isRefresh: false})
    return;
  }
  // 有文档数据，查询签名文件
  if(patternInfo.docId) {
    getCaSignFile();
  } else {
    // 无文档数据，先判断是否已经签名optType3
    sendCaOpt('3')
  }
}

// 查看签名文件
function getCaSignFile() {
  addLoadCoverLayer();
  $("#caResPage").attr('src', '').hide();
  var params = {
    docId: docId,
  };
  fetchAjax({
    url: api.hasCaFile,
    data: JSON.stringify(params),
    // hideErrMsg: true,
    successFn: function(res) {
      if(res.status !== '0' || !res.result || !res.result.fileNo) {
        toggleBtnFun({tipMsg:'没有查到相关文件', isSign: true, isRefresh: true})
        return;
      }
      // 获取文件则显示文件
      var fileNo = res.result.fileNo;
      var iframeSrc = api.getCaSignFile + '?fileNo=' + fileNo + '&busType=' + allUrlParams.busType + '&busId=' + allUrlParams.busId;
      toggleBtnFun({isRefresh:true, iframeSrc: iframeSrc});
    },
    errorFn: function() {
    },
    completeFn: function() {
      removeLoadCoverLayer();
    }
  })
}

// CA操作，0发起签名  3判断是否已经签名
function sendCaOpt(optType) {
  addLoadCoverLayer();
  if(optType === '0') {
    toggleBtnFun({tipMsg:'签署中，可点击刷新查看签署结果', isRefresh: true, isSign: false})
  }
  var params = {
    busId: allUrlParams.busId,
    busType: allUrlParams.busType,
    optType: optType,
    patternId: allUrlParams.patternId,
    optName: allUrlParams.optName,
    optId: allUrlParams.optId,
  };
  fetchAjax({
    url: api.sendCaOpt,
    data: JSON.stringify(params),
    hideErrMsg: true,
    successFn: function(res) {
      if(res.status !== '0') {
        if(optType === '0') {
          toggleBtnFun({tipMsg:'发起签名失败，请重试', isRefresh: true, isSign: true});
        }
        if(optType === '3') {
          toggleBtnFun({tipMsg:'没有查到相关文件', isRefresh: true, isSign: true});
        }
        return;
      }
      if(optType === '3') {
        // 此处应返回docId，用于获取签名文件
        docId = res.result;
        getCaSignFile();
      }
    },
    errorFn: function() {
      if(optType === '0') {
        toggleBtnFun({tipMsg:'发起签名失败，请重试', isRefresh: true, isSign: true});
      }
      if(optType === '3') {
        toggleBtnFun({tipMsg:'没有查到相关文件', isRefresh: true, isSign: true});
      }
    },
    completeFn: function() {
      removeLoadCoverLayer();
    }
  })
}

// 显示隐藏操作按钮,及提示内容
function toggleBtnFun(statusObj) {
  $(".ca1.operaBtn .opt-btn").hide();
  if(statusObj.isSign) {
    $(".ca1.operaBtn #signBtn").show();
  }
  if(statusObj.isRefresh) {
    $(".ca1.operaBtn #refreshBtn").show();
  }
  if(statusObj.iframeSrc) {
    $("#caResPage").attr('src', statusObj.iframeSrc).show();
    $(".no-data").hide();
  } else {
    $(".no-data").show();
    $(".tip-msg").text(statusObj.tipMsg || '');
    $("#caResPage").hide();
  }
}

// 刷新
function refreshSignRes() {
  sendCaOpt('3');
}