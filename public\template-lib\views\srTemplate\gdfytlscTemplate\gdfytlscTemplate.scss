#gdfytlsc1 {
  position: relative;
  padding-bottom: 95px;
  font-size: 14px;
  width: 780px;
  min-height: 100%;
  margin: 0 auto;
  border: 1px solid #F2A7C5;
  .gdfytlsc1-hd {
    text-align: center;
    img {
      margin-left: -1px;
      margin-top: -1px;
    }
    h1 {
      margin-top: 20px;
      font-weight: 600;
      font-size: 24px;
      color: #000000;
    }
    h2 {
      margin-top: 6px;
      font-weight: 400;
      font-size: 18px;
      color: #000000;
    }
  }
  .top-info {
    position: relative;
    margin: 8px 50px 0;
    border-top: 1px solid #F2F2F2;
    border-bottom: 1px solid #F2F2F2;
    line-height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .itm {
      &.out {
        position: absolute;
        right: 0;
        top: -30px;
      }
    }
    .lb {
      color: #606266;
    }
    .val {
      color: #303133;
    }
  }
  .gdfytlsc1-in {
    margin-top: 8px;
    padding: 0 50px;
  }
  table {
    width: 100%;
    border-collapse: collapse;
    border-color: #C8D7E6;
    table-layout: fixed;
  }
  tr {
    height: 30px;
  }
  th {
    padding: 0 4px;
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    color: #606266;
    background: #F5F7FA;
    &.bold {
      font-weight: 600;
      color: #000000;
    }
  }
  td {
    padding: 0 4px;
    color: #303133;
    font-size: 14px;
    input[type="text"] {
      border: none;
      width: 100%;
      height: 21px;
      min-height: 21px;
      color: #303133;
    }
  }
  label {
    margin-left: 4px;
    input {
      vertical-align: middle;
      height: 30px;
      min-height: 30px;
    }
    span {
      vertical-align: middle;
      font-size: 14px;
      color: #303133;
      &.sub {
        color: #606266;
      }
    }
  }
  .sub-h {
    color: #1885F2;
  }
  .ul {
    overflow: hidden;
    .li {
      width: 33.33%;
      float: left;
      span {
        vertical-align: middle;
      }
    }
  }
  .rmk {
    position: relative;
    margin-top: 12px;
    padding-left: 42px;
    b {
      font-size: 14px;
      position: absolute;
      left: 0;
      top: 4px;
      color: #000000;
    }
    input[type="text"] {
      width: 572px;
      height: 36px;
      padding: 0 15px;
      border-radius: 3px;
      font-size: 14px;
      color: #303133;
      border: 1px solid #DCDFE6;
    }
  }
  .btm-info {
    position: absolute;
    left: 50px;
    right: 50px;
    bottom: 40px;
    border-top: 1px solid #F2F2F2;
    padding-top: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .lb {
      color: #606266;
    }
    input[type="text"] {
      height: 36px;
      border-radius: 3px;
      padding: 0 4px;
      font-size: 14px;
      border: 1px solid #DCDFE6;
    }
  }
  .with-clear {
    position: relative;
  }
  .clear-handler {
    position: absolute;
    top: 7px;
    right: 0;
    width: 16px;
    height: 16px;
    cursor: pointer;
    background: url(../../../assets/images/icons/delete-gray-line-icon.png);
    &:hover {
      opacity: 0.8;
    }
    &:active {
      opacity: 0.6;
    }
  }
  .rt-sr-w.rt-sr-inv {
    border: none;
  }
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}
[isview="true"] #gdfytlsc1 {
  min-height: 1100px;
  .rmk {
    b {
      top: 0;
    }
  }
  .clear-handler {
    display: none;
  }
}