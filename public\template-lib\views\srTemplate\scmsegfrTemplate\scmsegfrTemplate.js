$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var resultData = []; //结果集，用于判断是否为新报告
var isSavedReport = false; //报告是否填写过
var xbdnabtSelList = [
  {
    idList: ['scmsegfg-rt-7','scmsegfg-rt-8','scmsegfg-rt-9','scmsegfg-rt-10','scmsegfg-rt-11','scmsegfg-rt-12','scmsegfg-rt-13'],
    lenVal: 224,
    optionList: [
      {title: '否', id: 1},
      {title: '是', id: 2},
    ]
  },
]; 

function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#scmsegfg1 .scmsegfg-view'),  //转成pdf的区域，默认是整个页面
      asyncAjax: true,  //存在异步操作的方法，应等其完成再继续
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    resultData = rtStructure.enterOptions ? (rtStructure.enterOptions.resultData || []) : []; // 结果集
    isSavedReport = rtStructure && rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport : false;
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      // 获取电子签名数据并缩放
      if($('#scmsegfg-sign').length) {
        var data = getSignDataHandler(publicInfo, 'scmsegfg-sign');
        if(data) {
          $('#scmsegfg-sign').find('.scmsegfg-sign-wrap').text(data);
          scaleSignArea('#scmsegfg-sign', '.scmsegfg-sign-lb', '.scmsegfg-sign-wrap');
          $('#scmsegfg1 .scmsegfg-view').css('paddingBottom', '285px');
        }
      }
      initViewCon();
    } else {
      pageInit();
    }
  }
}
// 初始化
function pageInit() {
  initAllSel();
  setDefaultVal();
  uploadImg();
  window.postMessage({
    message: 'finishAsyncAjax',
    data: {}
  }, '*');
}

// 设置初始化默认值
function setDefaultVal() {
  if(!resultData.length) {
    $('#scmsegfg-rt-1').val('石蜡包埋组织');
    $('#scmsegfg-rt-2').val('参见原送检病理报告记录');
    $('#scmsegfg-rt-3').val('PCR-荧光探针法');
    $('#scmsegfg-rt-4').val('EGFR基因突变检测');
    $('#scmsegfg-rt-5').val('合格');
    $('#scmsegfg-rt-14').val('携带EGFR突变(19Del* )的肿瘤可能对EGFR-TKI敏感。\n携带EGFR突变（T790M）的肿瘤可能对一/二代EGFR-TKI耐药，但对三代EGFR-TKI敏感。');
    // “否”默认值id列表
    let wjIdList = ['scmsegfg-rt-7','scmsegfg-rt-8','scmsegfg-rt-9','scmsegfg-rt-10','scmsegfg-rt-11','scmsegfg-rt-12','scmsegfg-rt-13'];
    for(let id in wjIdList) {
      $('#'+wjIdList[id]).val('否');
    }
    $('.jcwd-img').css('display','none');
  }
}

// 初始化所有下拉框
function initAllSel() {
  xbdnabtSelList.map(item => {
    let { idList = [], lenVal = 0, optionList = [] } = item;
    idList.map(id => {
      initInpAndSel(id,optionList,lenVal);
    })
  })
}
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal = 0) {
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
    },
    style:`max-height:200px;overflow:auto;overflow-x:hidden;${lenVal !== 0 ? `width: ${lenVal}px;` : 'width:30%'}`,
  })
}

// 上传图像
function uploadImg() {
  var rtEl=$('.fileids');
  if (!publicInfo.examNo && !publicInfo.busId) {
    return;
  }
  var idAnVal = idAndDomMap['scmsegfg-rt-6'] ? idAndDomMap['scmsegfg-rt-6'].value : '';
  if(idAnVal){
    rtEl.val(idAnVal);
    showImgList(rtEl);
  }

  $('#imageInput').on('input',function(e){
    var file = this.files[0];
    if(!file){
      return;
    }
    imageUploader(publicInfo.examNo,file, rtEl);
    showImgList(rtEl);
  })
}

// 获取图片并回显
function showImgList(rtEl) {
  getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
    console.log(uris);
    if(uris&&uris.length){
      let imgHtml = '';
      var url = uris.pop();
      if (url) {
        imgHtml = `
        <div class="preview">
          <img classs="divisionImg" id="divisionImg" src="${url}" alt=""  onclick="viewImg(this)">
          <img class="divisionPreview" id="divisionPreview" style="display: none;width: 590px;height: 440px;margin-left: 5px;margin-top: 5px;" src="${url}" alt="">
        </div>
        `;
        if(!resultData.length) {
          $('#scmsegfg-rt-15').val('EGFR 19Del* T790M 位点扩增曲线');
        }
        $('#scmsegfg1 .add-btn-txt').text('重传');
        $('#scmsegfg1 .jcwd-img').css('display','flex');
        $('.preview-img').html(imgHtml);
      }else {
        $('.preview-img').html('');
      }
    }
  })
}

// 预览图片
function viewImg(ele) {
  layer.open({
    title: '预览',
    type: 1,
    area: ['600px','500px'],
    content: $('#divisionPreview') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
  });
}

// 获取诊断
function getImpression() {
  var strArr = [], impression = '';
  // 19Del*  L858R  T790M  20INS*   G719X*  S768I  L861Q
  var impMap = {
    'scmsegfg-rt-7': '19Del*',
    'scmsegfg-rt-8': 'L858R',
    'scmsegfg-rt-9': 'T790M',
    'scmsegfg-rt-10': '20INS*',
    'scmsegfg-rt-11': 'G719X*',
    'scmsegfg-rt-12': 'S768I',
    'scmsegfg-rt-13': 'L861Q',
  }
  for(let id in impMap) {
    let eleVal = $(`#${id}`).val() || '';
    let eleStr = eleVal === '否' ? '未检出突变' : '检出突变';
    eleStr && strArr.push(impMap[id] + eleStr);

  }
  impression = strArr.length ? strArr.join('，') : '';
  return impression;
}

// 回显预览内容
function initViewCon() {
  var displayView = publicInfo.entryType === '5';  //显示模板
  if(displayView) {
    publicInfo['affirmDate'] = publicInfo['affirmDate'] || publicInfo['reportDate'];
    publicInfo['affirmTime'] = publicInfo['affirmTime'] || publicInfo['reportTime'];
  }
  var rtEl=$('.fileids');
  $('.preview-img-ls').html('');
  curElem.find('.scmsegfg-view [data-key]').each(function () {
    var keyList = $(this).attr('data-key') ? $(this).attr('data-key').split(',') : '';
    for(let key of keyList) {
      var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
      var value = publicInfo[key] || idAnVal;
      if(key ==='scmsegfg-rt-6') {
        rtEl.val(value);
        getUploadedImageList(publicInfo.examNo,rtEl,function(uris){
          // console.log('预览--->',uris);
          if(uris&&uris.length){
            let imgHtml = '';
            var url = uris.pop();
            if (url) {
              imgHtml = `
              <div class="preview-item-img">
                <img id="divisionPreviewImg" src="${url}" alt="">
              </div>
              `;
            }
            if(imgHtml) {
              $('.preview-img-ls').html(imgHtml);
            }
          }
          window.postMessage({
            message: 'finishAsyncAjax',
            data: {}
          }, '*');
        })
      }
      
      if(key === 'scmsegfg-rt-15') {
        $('#'+key).css('display','none');
      }

      if(displayView && key === 'affirmDate' && publicInfo['affirmTime']) {
        value = value + ' ' + publicInfo['affirmTime'];
      }
      if(value) {
        $(this).html(value);
      }
    }
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
  curElem.find('.scmsegfg-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })

  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (var image of rptImageList) {
      if (image.src) {
        imgHtml += `
        <div class="preview-item-img">
          <img src="${image.src}">
        </div>
        `
      }
    }
    if (imgHtml) {
      $('.preview-img-ls').css('display','flex');
      $('.preview-img-ls').html(imgHtml);
    }
  }
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}