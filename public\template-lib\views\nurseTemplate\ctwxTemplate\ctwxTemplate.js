$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var isSavedReport = false; //模版是否保存
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    isSavedReport = rtStructure.enterOptions ? rtStructure.enterOptions.isSavedReport :false; //是否保存
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      inintPreview('preview')
    } else {
      inintPreview('edit')
      inintPage()
      layui.use(['laydate', 'form'], function(){
        var laydate = layui.laydate;
        var form = layui.form;
        form.render();
        //执行一个laydate实例
        laydate.render({
          elem: '#ctwx-rt-44',//指定元素
          type: 'datetime',
          value: !isSavedReport ? new Date() : ''
        });
      })
    }
  }
}
function inintPreview(type){
  $(`[data-type]`).hide();
  $(`[data-type=${type}]`).show();
  if(type==='preview'){    
    $('[data-key]').each(function(){
      let key = $(this).attr('data-key')
      if(idAndDomMap){
        let result = [],list = []
        Object.keys(idAndDomMap).forEach(idKey=>{
          if(idKey.startsWith(key)){
           let value = idAndDomMap[idKey] ? idAndDomMap[idKey].value:'';
           if(value){
            result.push(value)
           }
          }
        })
        $(this).html(result.join(','))
        
        this.style.color = '#000'  
      }
    })
  }
}

function inintPage(){
  window.getNurseList ? toNurseList() : '';
  if(!isSavedReport){
    $("#ctwx-rt-27").val('温开水')
    $("#ctwx-rt-28").val(600)
  }
}

function toNurseList(){
  let nurseList = window.getNurseList({deptCode: ''});
  let userList = []
  if (nurseList && nurseList.length > 0) {
    for (var t = 0; t < nurseList.length; t++) {
      userList.push({ title: nurseList[t].name, id: nurseList[t].userId })
    }
  }
  initInpAndSel('ctwx-rt-43', userList,optHandler);
  if(!isSavedReport){
    let obj = {
      title:publicInfo && publicInfo.optName,
      id:publicInfo && publicInfo.optId
    }
    optHandler(obj)
    $('#ctwx-rt-43').val(publicInfo&&publicInfo.optName);
  }else{
    let val =  $('#ctwx-rt-43').val()
    if(val){
      let obj =  userList.find(item=>item.title === val) 
      if(obj){
        optHandler(obj)
      }
    }
  }
}
function optHandler(obj){
  let optInfo = JSON.stringify(obj)
  $('#optInfo').val(optInfo)
 }
// 初始化输入选择下拉框
function initInpAndSel(idList, optionList,cb) {
  let dropdown = layui.dropdown;
  // console.log('9999',optionList,idList)
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    click: function (obj) {
      this.elem.val(obj.title);
      cb && cb(obj)
    },
    style:'max-height:200px;overflow:auto;overflow-x:hidden;',
    ready:function(elePanel,elem){
      const width =elem[0] && elem[0].clientWidth
      if(elePanel[0]){
        elePanel[0].style.width = width + 'px'
        elePanel[0].style.maxHeight = '200px'
      }
    }
  })
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}