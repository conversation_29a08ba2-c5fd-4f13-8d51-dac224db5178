@charset "UTF-8";
#fish1 {
  font-size: 16px;
  background-color: #F5F7FA;
  min-height: 100%;
  color: #000;
}
#fish1 table {
  width: 100%;
  background: #fff;
  border-color: #C0C4CC;
  table-layout: fixed;
}
#fish1 table .layui-input, #fish1 table .layui-select, #fish1 table .layui-textarea {
  height: unset;
}
#fish1 table td, #fish1 table th {
  height: 36px;
}
#fish1 table thead th {
  text-align: left;
  padding-left: 12px;
}
#fish1 table tbody tr td {
  padding: 4px;
}
#fish1 .layui-input {
  height: 28px;
}
#fish1 * {
  box-sizing: border-box;
}
#fish1 .edit .edit-header {
  height: 44px;
}
#fish1 .preview {
  display: none;
  flex-direction: column;
  width: 780px;
  min-height: 1100px;
  background-color: #fff;
  margin: 0 auto;
  padding: 32px 56px;
  padding-bottom: 24px;
  word-break: break-all;
}
#fish1 .preview table {
  border-color: #000;
}
#fish1 .preview table thead th {
  text-align: center;
  padding-left: 0;
}
#fish1 .preview table tbody tr td {
  text-align: center;
}
#fish1 .preview .preview-header .logo {
  width: 162px;
  text-align: right;
}
#fish1 .preview .preview-header .title {
  text-align: center;
}
#fish1 .preview .preview-header .code {
  width: 194px;
  text-align: right;
}
#fish1 .preview .preview-header .code img {
  width: 122px;
  height: 36px;
}
#fish1 .preview .preview-content {
  flex: 1;
}
#fish1 .preview .preview-content .report-img {
  display: flex;
  column-gap: 20px;
}
#fish1 .preview .preview-content .report-img img {
  width: 240px;
  height: 180px;
  object-fit: cover;
}
#fish1 .preview .preview-footer .preview-report {
  display: flex;
  align-items: center;
  height: 48px;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
}
#fish1 .preview .preview-footer .preview-report img {
  height: 32px;
  width: 64px;
  object-fit: contain;
}
#fish1 .preview .preview-footer .preview-remark {
  text-align: right;
  margin-top: 4px;
  font-size: 12px;
}
#fish1 .preview .preview-footer .preview-remark div span {
  display: inline-block;
  transform: scale(0.92);
  transform-origin: right;
}
#fish1 .preview * {
  font-family: "宋体";
}
#fish1 .border-t {
  border-top: 1px solid #999;
}
#fish1 .border-b {
  border-bottom: 1px solid #999;
}
#fish1 .rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  width: 100%;
  padding: 4px;
  resize: both;
}
#fish1 .rt-textarea::placeholder {
  color: #000;
}
#fish1 .rt-sr-label {
  margin-right: 8px;
}
#fish1 .custom-select {
  position: relative;
}
#fish1 .custom-select::after {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 4px;
  content: "";
  width: 8px;
  height: 8px;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg) translateY(-100%);
}
#fish1 .custom-select .layui-input {
  height: 28px;
}
#fish1 .rt-sr-r {
  margin-right: 4px;
}
#fish1 .pl-24 {
  padding-left: 24px;
}
#fish1 .pr-24 {
  padding-right: 24px;
}
#fish1 .pt-8 {
  padding-top: 8px;
}
#fish1 .pb-8 {
  padding-bottom: 8px;
}
#fish1 .pb-20 {
  padding-bottom: 20px;
}
#fish1 .mt-16 {
  margin-top: 16px;
}
#fish1 .mt-12 {
  margin-top: 12px;
}
#fish1 .mt-8 {
  margin-top: 8px;
}
#fish1 .mt-6 {
  margin-top: 6px;
}
#fish1 .mt-4 {
  margin-top: 4px;
}
#fish1 .m-0-6 {
  margin: 0 6px;
}
#fish1 .w-10 {
  width: 10px;
}
#fish1 .w-15 {
  width: 15px;
}
#fish1 .w-20 {
  width: 20px;
}
#fish1 .w-25 {
  width: 25px;
}
#fish1 .w-30 {
  width: 30px;
}
#fish1 .w-35 {
  width: 35px;
}
#fish1 .w-40 {
  width: 40px;
}
#fish1 .w-45 {
  width: 45px;
}
#fish1 .w-50 {
  width: 50px;
}
#fish1 .w-55 {
  width: 55px;
}
#fish1 .w-60 {
  width: 60px;
}
#fish1 .w-65 {
  width: 65px;
}
#fish1 .w-70 {
  width: 70px;
}
#fish1 .w-75 {
  width: 75px;
}
#fish1 .w-80 {
  width: 80px;
}
#fish1 .w-85 {
  width: 85px;
}
#fish1 .w-90 {
  width: 90px;
}
#fish1 .w-95 {
  width: 95px;
}
#fish1 .w-100 {
  width: 100px;
}
#fish1 .w-105 {
  width: 106px;
}
#fish1 .w-110 {
  width: 110px;
}
#fish1 .w-115 {
  width: 115px;
}
#fish1 .w-120 {
  width: 120px;
}
#fish1 .w-125 {
  width: 125px;
}
#fish1 .w-130 {
  width: 130px;
}
#fish1 .w-135 {
  width: 135px;
}
#fish1 .w-140 {
  width: 140px;
}
#fish1 .w-145 {
  width: 145px;
}
#fish1 .w-150 {
  width: 150px;
}
#fish1 .w-155 {
  width: 155px;
}
#fish1 .w-160 {
  width: 160px;
}
#fish1 .w-165 {
  width: 165px;
}
#fish1 .f-1 {
  flex: 1;
}
#fish1 .f-2 {
  flex: 2;
}
#fish1 .f-3 {
  flex: 3;
}
#fish1 .fw-600 {
  font-weight: 600;
}
#fish1 .a-center {
  display: flex;
  align-items: center;
}
#fish1 .a-start {
  display: flex;
  align-items: flex-start;
}
#fish1 .flex {
  display: flex;
}
#fish1 .flex-column {
  display: flex;
  flex-direction: column;
}
#fish1 .text-r {
  text-align: right;
}
#fish1 .fs-36 {
  font-size: 36px;
}
#fish1 .fs-24 {
  font-size: 24px;
}
#fish1 .fs-20 {
  font-size: 20px;
}

[isView=true] #fish1 .edit {
  display: none;
}

[isView=true] #fish1 .preview {
  display: flex;
}

[view-fish-type] {
  display: none;
}
