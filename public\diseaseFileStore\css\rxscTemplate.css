#rxscP1 .rt-sr-tit { width: 110px; }
#rxscP1 .sketch-map { 
  background: #fff;
  /* display: flex;
  align-items: center; */
  width: 325px;
  height: 177px;
}
#rxscP1 .map-img {
  position: relative;
  flex: 1;
  width: 320px;
  height: 140px;
}
#rxscP1 .map-img img {
  width: 100%;
  height: 100%;
}
#rxscP1 .map-color {
  padding-left: 8px;
  display: flex;
  align-items: center;
  border-top: 1px solid #C8D7E6;
  padding-top: 8px;
}
#rxscP1 .map-color .map-tl {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
  border-radius: 4px;
  padding: 4px;
  height: 20px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
#rxscP1 .map-color .map-tl + .map-tl {
  margin-left: 4px;
}
#rxscP1 .map-color .c-pot {
  width: 12px;
  height: 12px;
}
#rxscP1 .c-pot {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 1px solid #000;
  border-radius: 50%; 
  line-height: 1;
  text-align: center;
  font-size: 12px;
}
#rxscP1 .c-angle {
  border-radius: 0;
  border-left: 7px transparent solid;
  border-right: 7px transparent solid;
  border-bottom: 14px #303133 solid;
  border-top: 0 transparent solid;
  background: none !important;
}
#rxscP1 .map-color .c-angle {
  border-bottom-width: 12px;
}
#rxscP1 .c-pot.abs {
  position: absolute;
  cursor: pointer;
}
#rxscP1 .c-pot:not(.c-angle).abs:hover,
#rxscP1 .c-pot:not(.c-angle).abs.act {
  border-color: #FFF443;
}
#rxscP1 .c-pot.c-angle.abs:hover,
#rxscP1 .c-pot.c-angle.abs.act {
  border-bottom-color: #000;
}
#rxscP1 .c-angle .ft{
  text-align: left;
  margin-left: -3px;
  margin-top: 2px;
  display: block;
}
#rxscP1 .num-flag {
  color: #000;
  font-size: 12px;
  margin-left: 4px;
}
#rxscP1 .con-i {
  color: #303133;
  cursor: pointer;
  line-height: 22px;
}
#rxscP1 .con-i:hover, #rxscP1 .con-i.act {
  color: #1885F2;
}
#rxscP1 .s-map-con { 
  flex: 1;
  padding: 8px;
  overflow: auto;
  word-break: break-all;
  height: 177px;
  border-left: 1px solid #C8D7E6;
}
.tooltip-wrap {
  line-height: 20px;
  white-space: pre-wrap;
}
#rxscP1 .bz-item {
  display: flex;
  width: 100%;
}
#rxscP1 .bz-item + .bz-item {
  margin-top: 16px;
}
#rxscP1 .bz-side {
  width: 100px;
  text-align: right;
  color: #303133;
  font-weight: bold;
}
#rxscP1 .bz-detail {
  flex: 1;
}
#rxscP1 .bz-title {
  color: #000;
  margin-bottom: 8px;
}
#rxscP1 .bz-table {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#rxscP1 .bz-table-head {
  padding: 4px 8px;
  background: #EBEEF5;
  border-bottom: 1px solid #C8D7E6;
  color: #000;
}
#rxscP1 .bz-table-body {
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
}
#rxscP1 .bz-info {
  margin: 3px 0;
  margin-right: 30px;
  color: #000;
}
.hs-block.w-block {
  display: block !important;
} 
.hs-block.w-block .hight-block{
  border-top: 1px solid #C8D7E6;
  border-left: none;
}
.hs-block.w-block .light-block {
  padding: 4px 8px;
}
.hs-block.w-block .lb-row.lb-active > input:checked::after,
.hs-block.w-block .lb-row.lb-active > input:checked::before {
  content: unset !important;
}
.hs-block.w-block .light-block .w-con:not(.v) .r-i + .r-i {
  margin-left: 0;
}
.level-radio.w-con:not(.v) .r-i + .r-i {
  margin-left: 0;
}
.level-radio.w-con {
  padding-left: 15px;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  height: 28px;
  border-radius: 3px 0 0 3px;
}
.level-radio.w-con .r-i {
  margin-right: 15px;
}
.level-select select.rt-sr-s {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  padding-left: 5px;
}
.rxsc-wrap *:focus {
  box-shadow: 0 0 7px 1px rgb(24,133,242,.7)!important;
}     
.rxsc-wrap label:not(.lb-row) [type="radio"]:focus, 
.rxsc-wrap label:not(.lb-row):not(.lb-new) [type="checkbox"]:focus {
  min-height: unset !important;
  height: 14px !important;
  box-shadow: 0 0 7px 1px rgb(24,133,242,.7)!important;
}
.rxsc-wrap .lb-d .p-lb.w{
  display: inline-block;
  width: 70px;
  text-align: right;
}
.rxsc-wrap .lb-d .rt-sr-lb {
  color: #303133;
}