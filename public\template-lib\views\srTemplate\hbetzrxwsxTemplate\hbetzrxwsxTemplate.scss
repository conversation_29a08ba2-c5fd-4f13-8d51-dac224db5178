#hbetzrxwsx1 {
  font-size: 16px;
  min-height: 100%;
  margin: 0 auto;
  background-color: #F5F7FA;
  * {
    font-family: 宋体;
  }
  input, textarea {
    font-size: 16px;
    border: 1px solid #C0C4CC;
    line-height: 22px;
    border-radius: 3px;
    padding: 2px 8px;
  }
  .input-top-wrap {
    padding: 8px 20px;
    border-bottom: 2px solid #000;
    .row {
      margin-top: 4px;
      &:first-child {
        margin-top: 0;
      }
    }
    .lb {
      max-width: 80px;
      display: inline-block;
      text-align: left;
    }
    input {
      width: 320px;
    }
  }
  .input-body-wrap {
    padding: 8px 20px;
    .row {
      display: flex;
      margin-top: 4px;
      &:first-child {
        margin-top: 0;
      }
      &.hd {
        font-weight: bold;
      }
      &.line4 {
        height: 94px;
      }
      &.line2 {
        height: 50px;
      }
    }
    .cell {
      height: 100%;
      margin-left: 8px;
      &:first-child {
        margin-left: 0;
      }
      &:nth-of-type(1) {
        flex: 200;
      }
      &:nth-of-type(2) {
        flex: 150;
        display: flex;
      }
      &:nth-of-type(3) {
        flex: 200;
      }
      &:nth-of-type(4) {
        flex: 80;
      }
    }
    .over-mark {
      width: 16px;
      flex-shrink: 0;
      flex-grow: 0;
      color: #000;
      display: flex;
      align-items: center;
    }
    input, textarea {
      width: 100%;
      height: 100%;
    }
  }
  .remark-wrap {
    padding: 0 20px;
    p {
      margin-bottom: 8px;
    }
    textarea {
      width: 100%;
      height: 204px;
    }
  }

  .info-top-wrap {
    .tl-cont {
      position: relative;
      text-align: center;
      padding-bottom: 34px;
      border-bottom: 2px solid #000;
      h1, h3, h2 {
        color: #000;
      }
      h1 {
        font-family: 仿宋;
        font-size: 21px;
        line-height: 1;
      }
      h3 {
        margin-top: 3px;
        font-family: 仿宋;
        font-size: 21px;
        line-height: 1;
      }
      h2 {
        margin-top: 20px;
        font-size: 26px;
        line-height: 1;
      }
      .code {
        position: absolute;
        right: 0;
        top: 27px;
        height: 36px;
        img {
          height: 36px;
        }
      }
      .code-text {
        position: absolute;
        right: 0;
        bottom: 8px;
        font-weight: bold;
        font-size: 18px;
      }
    }
    .base-info {
      margin-top: 6px;
      overflow: hidden;
      line-height: 26px;
      .item {
        width: 25%;
        float: left;
        &.per50 {
          width: 50%;
        }
        &.per75 {
          width: 75%;
        }
        &.per100 {
          width: 100%;
        }
      }
      .lb {
        max-width: 80px;
        display: inline-block;
        text-align: left;
      }
    }
  }
  .bottom-info-cont {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    &.name-cont {
      padding: 8px 0;
      border-top: 2px solid #000;
      border-bottom: 2px solid #000;
    }
    &.time-cont {
      margin-top: 8px;
    }
    img {
      vertical-align: middle;
      width: 64px;
      height: 32px;
      object-fit: cover;
    }
  }
  .rt-sr-footer {
    position: absolute;
    left: 50px;
    right: 50px;
    bottom: 30px;
  }
  .rt-sr-header {
    margin-bottom: 16px;
    display: none;
  }
  .rt-sr-footer {
    display: none;
  }
}

[isview="true"] #hbetzrxwsx1 {
  padding: 30px 50px 100px;
  width: 780px;
  min-height: 1100px;
  background: white;
  .input-top-wrap, .input-body-wrap, .remark-wrap {
    padding-left: 0;
    padding-right: 0;
  }
  .rt-sr-header, .rt-sr-footer {
    display: block;
  }
  .result-wrap{
    .text-con {
      width: 67px;
      text-align: right;
      display: inline-block;
    }
  }
  .text-con {
    white-space: pre-line;
    line-height: 22px;
  }
  .input-top-wrap {
    display: flex;
    .row {
      flex: 1;
      margin-top: 0;
    }
  }
  .input-body-wrap {
    padding-top: 10px;
    .row {
      margin-top: 8px;
      padding-left: 8px;
      padding-right: 8px;
      &.hd {
        padding-bottom: 10px;
        border-bottom: 2px solid #000;
      }
      &:first-child {
        margin-top: 0;
      }
    }
  }
  .remark-wrap {
    margin-top: 8px;
    p {
      font-weight: bold;
    }
    .text-con {
      line-height: 28px;
    }
  }
  .over-mark {
    align-items: start;
    margin-left: 36px;
  }
}