$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#gdfyrux1', 'edit');
  // initHtmlScript('#gdfyrux1', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type === 'view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescription();
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    examRptMemo: '',
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initPage(type) {
  if (type === 'edit') {
    // 初始化超声提示单选切换功能
    initUltrasoundTipToggle();
    // 添加表单变化监听，用于实时生成描述
    initDescriptionListener();
    // 初始化病灶数目自动切换功能
    initLesionCountToggle();
  }
  if (type === 'view') {

  }
}

// 初始化描述监听器
function initDescriptionListener() {
  // 监听表单变化，实时生成描述
  $('#gdfyrux1 .body-wrap').on('input', function() {
    console.log('描述：');
    console.log(getDescription());
  });

  $('#gdfyrux1 .footer-wrap').on('input', function() {
    console.log('诊断：');
    console.log(getImpression());
  });
}

// 推导描述
function getDescription() {
  var description = '';

  // 1. 双侧乳腺背景回声
  var backgroundEcho = getVal('input[name="scrxc"]:checked');
  if (backgroundEcho) {
    description += '双侧乳腺呈' + backgroundEcho + '背景回声。\n';
  }

  // 2. 处理左侧病灶
  var leftSideChecked = getVal('input[name="left"]:checked');
  if (leftSideChecked) {
    var leftDescription = generateSideDescription('left', '左侧');
    if (leftDescription) {
      description += leftDescription;
    }
  }

  // 3. 处理右侧病灶
  var rightSideChecked = getVal('input[name="right"]:checked');
  if (rightSideChecked) {
    var rightDescription = generateSideDescription('right', '右侧');
    if (rightDescription) {
      description += rightDescription;
    }
  }

  // 4. 腋窝淋巴结
  var lymphNode = getVal('input[name="yw-lbj"]:checked');
  if (lymphNode) {
    description += lymphNode + '腋窝未探及肿大淋巴结回声。';
  }

  return description;
}

// 生成单侧病灶描述的辅助函数
function generateSideDescription(side, sideName) {
  var sideDesc = '';

  // 获取形态
  var shape = getVal('input[name="' + side + '-xt"]:checked');

  // 获取回声类型
  var echoType = getVal('input[name="' + side + '-hslx"]:checked');

  if (!shape || !echoType) {
    // return '';
  }

  // 获取病灶位置和大小信息
  var lesionDetails = getLesionDetails(side);
  var actualLesionCount = lesionDetails.length;

  if (actualLesionCount === 0) {
    return '';
  }

  // 构建描述
  sideDesc += sideName + '乳腺内可见' + actualLesionCount + '个' + shape + echoType + '区：';

  // 添加大小和位置信息
  var sizeDescriptions = [];
  for (var i = 0; i < lesionDetails.length; i++) {
    var lesion = lesionDetails[i];
    if (lesion.longitudinal && lesion.transverse && lesion.position && lesion.distance) {
      var sizeDesc = lesion.longitudinal + 'mm(纵径)×' + lesion.transverse + 'mm(横径)(位于' + lesion.position + '点钟处，距离乳头' + lesion.distance + 'mm)';
      sizeDescriptions.push(sizeDesc);
    }
  }

  if (sizeDescriptions.length > 0) {
    sideDesc += '大小分别约' + sizeDescriptions.join('、') + '，';
  }

  // 获取边缘特征
  var edge = getVal('input[name="' + side + '-byqxd"]:checked');
  if (edge) {
    sideDesc += '边缘' + edge + '，';
  }

  // 获取内部回声
  var internalEcho = getVal('input[name="' + side + '-nbhs"]:checked');
  if (internalEcho) {
    sideDesc += '内部回声' + internalEcho + '，';
  }

  // 获取内部可见
  var internalVisible = getVal('input[name="' + side + '-nbkj"]:checked');
  if (internalVisible) {
    sideDesc += '内部可见' + internalVisible + '，';
  }

  // 获取后方回声
  var posteriorEcho = getVal('input[name="' + side + '-hfhs"]:checked');
  if (posteriorEcho) {
    sideDesc += '后方回声' + posteriorEcho + '，';
  }

  // 获取CDFI信息
  var cdfiDescription = generateCDFIDescription(side);
  if (cdfiDescription) {
    sideDesc += 'CDFI：' + cdfiDescription + '。';
  }

  sideDesc += '\n';
  return sideDesc;
}

// 获取病灶详细信息（位置和大小）
function getLesionDetails(side) {
  var lesions = [];

  // 根据侧别确定起始ID号
  var startId = side === 'left' ? 22 : 82;

  for (var i = 0; i < 4; i++) {
    // 构建实际的ID
    var positionId = '#gdfyrux-rt-' + (startId + i * 4);
    var distanceId = '#gdfyrux-rt-' + (startId + i * 4 + 1);
    var longitudinalId = '#gdfyrux-rt-' + (startId + i * 4 + 2);
    var transverseId = '#gdfyrux-rt-' + (startId + i * 4 + 3);

    var position = $(positionId).val();
    var distance = $(distanceId).val();
    var longitudinal = $(longitudinalId).val();
    var transverse = $(transverseId).val();

    // 只有当所有字段都有值时才添加到结果中
    if (position && distance && longitudinal && transverse) {
      lesions.push({
        position: position,
        distance: distance,
        longitudinal: longitudinal,
        transverse: transverse
      });
    }
  }

  return lesions;
}

// 生成CDFI描述
function generateCDFIDescription(side) {
  // 获取回声区类型
  var echoArea = getVal('input[name="' + side + '-cdfi-hs"]:checked');

  // 获取位置
  var position = getVal('input[name="' + side + '-cdfi-by"]:checked');

  // 获取血流信号
  var bloodFlow = getVal('input[name="' + side + '-cdfi-blood"]:checked');

  // 获取血流类型
  var bloodType = getVal('input[name="' + side + '-cdfi-xl"]:checked');

  // 按照模板格式组织CDFI描述
  if (echoArea || position || bloodFlow || bloodType) {
    var description = '';

    // 回声区类型
    if (echoArea) {
      if (echoArea === '混合性回声区') {
        description += '混合回声区';
      } else {
        description += echoArea + '区';
      }
    }

    // 位置
    if (position) {
      description += position;
    }

    // 血流信号
    if (bloodFlow) {
      description += bloodFlow;
    }

    // 血流类型
    if (bloodType) {
      description += (description ? '，' : '') + bloodType;
    }

    return description;
  }

  return '';
}

// 推导诊断
function getImpression() {
  var arr = [];
  var leftLevel = getVal('input[name="left-bi-rads"]:checked').replace(/级/g, '/').replace(/、|\/$/g, '');
  var rightLevel = getVal('input[name="right-bi-rads"]:checked').replace(/级/g, '/').replace(/、|\/$/g, '');
  var bothLevel = getVal('input[name="both-bi-rads"]:checked').replace(/级/g, '/').replace(/、|\/$/g, '');
  if (leftLevel) {
    arr.push(`左侧乳腺BI-RADS分级${leftLevel}级。`);
  }
  if (rightLevel) {
    arr.push(`右侧乳腺BI-RADS分级${rightLevel}级。`);
  }
  if (bothLevel) {
    arr.push(`双侧乳腺BI-RADS分级${bothLevel}级。`);
  }
  var impression = arr.join('\n');
  return impression;
}

/**
 * 初始化超声提示单选切换功能
 * 根据选择的左侧/右侧/双侧，显示对应的BI-RADS分级选项
 */
function initUltrasoundTipToggle() {
  // 监听超声提示单选按钮的变化
  $('input[name="result"]').on('change', function() {
    var selectedValue = $(this).val();

    // 隐藏所有BI-RADS分级区域
    $('.result-level').addClass('hide');

    // 根据选择显示对应的BI-RADS分级区域
    switch(selectedValue) {
      case '左侧':
        $('.result-level.left').removeClass('hide');
        break;
      case '右侧':
        $('.result-level.right').removeClass('hide');
        break;
      case '双侧':
        $('.result-level.both').removeClass('hide');
        break;
      default:
        // 如果没有选择或选择了其他值，保持所有区域隐藏
        break;
    }
  });

  // 页面加载时检查是否有已选中的选项，如果有则触发切换
  var checkedResult = $('input[name="result"]:checked');
  if (checkedResult.length > 0) {
    checkedResult.trigger('change');
  }
}

/**
 * 初始化病灶数目自动切换功能
 * 根据时钟定位法区域填写的实际个数，自动切换单发/多发病灶数目
 */
function initLesionCountToggle() {
  // 监听左侧时钟定位法输入框的变化
  initSideLesionCountToggle('left');

  // 监听右侧时钟定位法输入框的变化
  initSideLesionCountToggle('right');

  // 页面加载时检查现有数据并设置初始状态
  updateLesionCount('left');
  updateLesionCount('right');
}

/**
 * 初始化单侧病灶数目切换功能
 * @param {string} side - 侧别 ('left' 或 'right')
 */
function initSideLesionCountToggle(side) {
  // 根据侧别确定起始ID号
  var startId = side === 'left' ? 22 : 82;

  // 为每个病灶的输入框添加监听器
  for (var i = 0; i < 4; i++) {
    // 构建实际的ID
    var positionId = '#gdfyrux-rt-' + (startId + i * 4);
    var distanceId = '#gdfyrux-rt-' + (startId + i * 4 + 1);
    var longitudinalId = '#gdfyrux-rt-' + (startId + i * 4 + 2);
    var transverseId = '#gdfyrux-rt-' + (startId + i * 4 + 3);

    // 为所有相关输入框添加变化监听器
    // 使用闭包来保持side变量的正确值
    (function(currentSide) {
      $(positionId + ',' + distanceId + ',' + longitudinalId + ',' + transverseId).on('input change', function() {
        updateLesionCount(currentSide);
      });
    })(side);
  }
}

/**
 * 更新病灶数目选择
 * @param {string} side - 侧别 ('left' 或 'right')
 */
function updateLesionCount(side) {
  // 获取该侧的病灶详细信息
  var lesionDetails = getLesionDetails(side);
  var actualLesionCount = lesionDetails.length;

  // 根据侧别确定单选按钮的name属性
  var radioName = side + '-bzsm';

  // 清除当前选择
  $('input[name="' + radioName + '"]').prop('checked', false);

  // 根据病灶数量自动选择单发或多发
  if (actualLesionCount === 1) {
    // 单发
    $('input[name="' + radioName + '"][value="单发"]').prop('checked', true);
  } else if (actualLesionCount > 1) {
    // 多发
    $('input[name="' + radioName + '"][value="多发"]').prop('checked', true);
  }
  // 如果actualLesionCount为0，则不选择任何选项

  // 触发change事件以便其他监听器能够响应
  $('input[name="' + radioName + '"]:checked').trigger('change');
}