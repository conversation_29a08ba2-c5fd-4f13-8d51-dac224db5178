$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#hbetcovid19', 'edit');
  // initHtmlScript('#hbetcovid19', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
var optionsMap= {
  options01: [
    {name: '阴性'},
    {name: '阳性'},
    {name: '可疑'},
    {name: ''},
  ],
};
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.getElementById('hbetcovid19'),  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = getImpression();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initPage(type) {
  if (type === 'edit') {
    initDropdown();
  }
  if (type === 'view') {
    initPreview();
  }
}

// 初始化下拉
function initDropdown() {
  for (let optName in optionsMap) {
    let opts = optionsMap[optName];
    opts = transformOptList(opts, 'name');
    layui.dropdown.render({
      elem: '.' + optName,
      data: opts,
      className: 'laySelLab',
      click: function(obj) {
        this.elem.val(obj.title);
      },
      style: 'width: ' + $('.' + optName).innerWidth() + 'px'
    });
  }
}
// 转换下拉数据格式
function transformOptList(list, titleName) {
  let arr = [];
  list.forEach((item,index) => {
    if(titleName) {
      arr.push({ ...item,title:item[titleName],id:index,templet: `<span title='${item[titleName]}'>${item[titleName]}</span>` });
    }else {
      arr.push({ title:item,id:index,templet: `<span title='${item}'>${item}</span>` });
    }
  })
  return arr;
}

function getImpression() {
  var arr = [];
  curElem.find('.input-body-wrap .row:not(.hd)').each(function() {
    var item = getVal($(this).find('.inp:eq(0)'));
    var val = getVal($(this).find('.inp:eq(1)'));
    if (item && val) {
      arr.push(item + '：' + val);
    }
  });
  return arr.join('；');
}

function initPreview(){
  // 签名
  curElem.find('[data-img]').each(function() {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
      } 
    }
  });
  // 文字值
  curElem.find('[data-key]').each(function() {
    var key = $(this).attr('data-key');
    var keyList = key.split(',');
    if (keyList.length > 1) {
      let result = [];
      keyList.forEach(item => {
        result.push(publicInfo[item]);
      });
      $(this).html(result.join(' '));
      return;
    }
    
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'sampleName') {
      value = value.replace(/,/g, ';');
    }
    $(this).html(value);
    addIdToNodeByView(this, keyList, idAndDomMap);
  })
}