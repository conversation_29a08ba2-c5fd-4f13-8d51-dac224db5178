#qjjl1 {
  font-size: 16px;
}
#qjjl1 .qjjl-content {
  height: 100%;
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#qjjl1 .qjjl-content .qjjl-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  margin-bottom: 4px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}
#qjjl1 .qjjl-content .form-title {
  color: #606266;
}
#qjjl1 .qjjl-content .sign-content {
  display: flex;
  align-items: center;
  margin-left: 10px;
  margin-top: 12px;
  margin-bottom: 12px;
}
#qjjl1 .qjjl-content .sign-content .layui-input {
  width: 240px;
}
#qjjl1 .qjjl-content .sign-content .weight {
  margin-right: 20px;
  display: flex;
  align-items: center;
}
#qjjl1 .qjjl-content .sign-content .temperature {
  display: flex;
  align-items: center;
}
#qjjl1 .qjjl-content .allergy-content {
  margin-bottom: 10px;
}
#qjjl1 .qjjl-content .result-content {
  margin-top: 12px;
  display: flex;
  align-items: center;
  margin-left: 12px;
}
#qjjl1 .qjjl-content .result-content .result-item {
  width: calc(50% - 12px);
  display: flex;
  align-items: center;
  height: 60px;
  background: #FAFAFA;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
  padding: 0 12px;
  margin-bottom: 0px;
}
#qjjl1 .qjjl-content .result-content .result-item .checkbox {
  width: 188px;
  height: 36px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  padding: 0 6px 0 8px;
}
#qjjl1 .qjjl-content .result-content .result-item .checkbox .check-label {
  display: flex;
  align-items: center;
  width: 100%;
}
#qjjl1 .qjjl-content .result-content .result-item .checkbox .check-label .rt-sr-lb {
  margin-left: 5px;
}
#qjjl1 .qjjl-content .result-content .result-item .layui-input {
  flex: 1;
}
#qjjl1 .qjjl-content .allergy-item {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}
#qjjl1 .qjjl-content .allergy-item .layui-input {
  width: calc(100% - 62px);
}
#qjjl1 .qjjl-content .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#qjjl1 .qjjl-content .radio-label .rt-sr-lb {
  margin-left: 4px;
}
#qjjl1 .qjjl-content .gray-content {
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  margin: 0 16px;
  padding: 6px 12px 12px 12px;
}
#qjjl1 .qjjl-content .gray-item {
  margin-left: 16px;
  display: flex;
  align-items: center;
}
#qjjl1 .qjjl-content .table-content {
  padding: 0 16px;
}
#qjjl1 .qjjl-content .add-btn {
  width: 100px;
  color: #1885F2;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: normal;
}
#qjjl1 .qjjl-content .layui-form-item {
  padding: 12px 16px 0 16px;
}
#qjjl1 .qjjl-content .drug-content {
  margin-left: 16px;
  margin-top: 10px;
  margin-bottom: 10px;
  width: calc(100% - 32px);
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
}
#qjjl1 .qjjl-content .drug-content .drug-top {
  width: 100%;
  padding: 0 16px;
  height: 40px;
  color: #000;
  border-radius: 6px 6px 0px 0px;
  border: 1px solid #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#qjjl1 .qjjl-content .drug-content .drug-top .close-icon {
  font-size: 14px;
  color: #303133;
  cursor: pointer;
}
#qjjl1 .qjjl-content .drug-content .form-content {
  margin: 12px 16px;
  display: flex;
  align-items: center;
}
#qjjl1 .qjjl-content .drug-content .form-content .temperature {
  margin-top: 5px;
  flex: 1;
}

#qjjl1 .showInt {
  position: relative;
  background: #fff;
  width: 190px;
}
#qjjl1 .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 11;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}

/*# sourceMappingURL=qjjlTemplate.css.map */
