$(function() {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var levelMap = {
  'A+A,B': 'A',   //为所有选项属性data-lv用'+'拼接起来的值
  'B+A,B': 'B',
  'C,D+C': 'C',
  'C,D+D': 'D',
}
function initHtmlScript(ele) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
  }

  $("#flxsg1 .ck-r").change(function() {
    var name = $(this).attr('name');
    var id = $(this).attr('id');
    if($(this).is(":checked")) {
      $('[name="'+name+'"]:not([id="'+id+'"])').prop('checked', false);
    }
  })

  // 确定等级
  confirmLevelFun();
  $("#flxsg1 [data-lv]").change(function() {
    confirmLevelFun();
  })

  // 数量交互
  $('[name="sg-count"]').change(function() {
    var val = $(this).val();
    if($(this).is(":checked")) {
      $('#flxsg-rt-77').val('');
      if(val === '1处') {
        $('.len-txt').text('长度');
      }else {
        $('.len-txt').text('最大长度');
      }
    }
  });
  $('#flxsg-rt-77').change(function() {
    let val = $('#flxsg-rt-77').val();
    if(val) {
      $('[name="sg-count"]').prop('checked',false);
      if(parseInt(val) <= 1) {
        $('.len-txt').text('长度');
      }else {
        $('.len-txt').text('最大长度');
      }
    }
    confirmLevelFun();
  })
}

function confirmLevelFun() {
  var levelList = [];
  var level = '';

  $("#flxsg1 [data-lv]:checked").each(function(i, dom) {
    var levelTxt = $(dom).attr('data-lv');
    levelList.push(levelTxt);
  })
  var levelKey = levelList.join('+');
  if(levelMap[levelKey]) {
    level = 'LA-' + levelMap[levelKey];
  }
  $('.sgLevel').val(level);
  $('.f-level').val(level);
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = getDescriptFun();
  rtStructure.impression = getImpressFun();
}

// 获取诊断
function getImpressFun() {
  var str = '';
  var level = $('.sgLevel').val() || '-';
  str += '1、反流性食管炎（'+level+'）；\n';
  str += '2、慢性非萎缩性胃炎；';
  return str;
}

// 获取描述
function getDescriptFun() {
  var strList = [];
  var excludeLabel = ['形态'];  //不显示的label
  var labelInRight = ['可见', '未见'];  //label默认放在值的左边，该数组的放于右边
  var idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
  var handler = function(data, container, label) {
    var ckBoxlength = [];
    label = label && excludeLabel.indexOf(label) === -1 ? label : '';
    data.each(function(cIdx, child) {
      var id = $(child).attr('id');
      if(idAndDomMap[id]) {
        var childLabel = idAndDomMap[id].name;
        if(idAndDomMap[id].wt !== 0) {
          if([4, 5].indexOf(idAndDomMap[id].wt) > -1) {
            if($(child).is(":checked")) {
              if(labelInRight.indexOf($(child).val()) === -1) {
                ckBoxlength.push(label + $(child).val());
              } else {
                ckBoxlength.push($(child).val() + label);
              }
            }
          } else {
            if(labelInRight.indexOf($(child).val()) === -1) {
              ckBoxlength.push($(child).val() ? label + $(child).val() : '-');
            } else {
              ckBoxlength.push($(child).val() ? $(child).val() + label : '-');
            }
          }
        } else {
          var grand = $('#flxsg1 [pid="'+id+'"]');
          handler(grand, ckBoxlength, childLabel)
        }
      }
    })
    if(ckBoxlength.length) {
      container.push(ckBoxlength.join('，'));
    }
  }
  $('#flxsg1 .rt-sr-w:not([pid]):not(.noxml)').each(function(pIdx, par) {
    var parent = $(par);
    var pid = parent.attr('id');
    var label = idAndDomMap[pid].name;
    // 食管数据特殊处理
    if(pid === 'flxsg-rt-5') {
      var sgList = [];
      var sgNumInp = $('#flxsg-rt-77').val() || '';
      sgNumInp = sgNumInp ? sgNumInp + '处' : '';
      sgList.push('下段近贲门可见' + ($('[name="sg-count"]:checked').val() || sgNumInp || '') + '条状充血糜烂灶');
      if($('[name="sg-long"]:checked').val()) {
        sgList.push('长度' + $('[name="sg-long"]:checked').val());
      }
      if($('[name="sg-rh"]:checked').val()) {
        sgList.push($('[name="sg-rh"]:checked').val());
      }
      if($('[name="sg-zj"]:checked').val()) {
        sgList.push($('[name="sg-zj"]:checked').val() + '周径');
      }
      strList.push(label + '：' + (sgList.length ? sgList.join(',') : '-'));
      return;
    }
    var children = $('#flxsg1 [pid="'+pid+'"]');
    var content = [];
    handler(children, content)
    strList.push(label + '：' + (content.length ? content.join(',') : '-'))
  })
  // console.log('strList', strList);
  console.log(strList.join('；\n') + '。');
  return strList.join('；\n') + '。';
}