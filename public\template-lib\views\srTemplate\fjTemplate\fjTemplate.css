/*-------公共--------*/
.w-con {
  display: inline-block;
  vertical-align: middle;
}

/*-------边距--------*/
.mb-8 {
  margin-bottom: 8px;
}
.mb-12 {
  margin-bottom: 12px;
}
.ml-2 {
  margin-left: 2px;
}
.ml-12 {
  margin-left: 12px;
}
.ml-16 {
  margin-left: 16px;
}
.ml-18 {
  margin-left: 18px;
}
.ml-26 {
  margin-left: 26px;
}
.ml-30 {
  margin-left: 30px;
}
.ml-32 {
  margin-left: 32px;
}
.ml-40 {
  margin-left: 40px;
}
.ml-60 {
  margin-left: 60px;
}
.ml-68 {
  margin-left: 68px;
}
.ml-74 {
  margin-left: 74px;
}


/*-------宽度--------*/
.wd-80 {
  width: 80px;
}

/*-------高度--------*/
.lh-28 {
  line-height: 28px;
}

/*-------总体布局--------*/
ul {
  overflow: auto;
}
#fj1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}
#fj1 .fj-content {
  min-height: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}
#fj1 .con-box {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#fj1 .box-row {
  padding: 8px 12px;
  box-sizing: border-box;
}
#fj1 .bor-w {
  border: 1px solid #C8D7E6;
  padding: 0 8px;
  background: #EBEEF5;
}
#fj1 .flex-sty {
  display: flex;
  justify-content: space-between;
}
#fj1 .flex-item {
  flex: 1;
}
#fj1 .inp-sty {
  height: 28px;
  border: 1px solid #DCDFE6;
}
#fj1 [type="checkbox"],#fj1 [type="radio"] {
  vertical-align: middle;
}
#fj1 .tc {
  text-align: center;
}