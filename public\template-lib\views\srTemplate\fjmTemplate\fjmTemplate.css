#fjm1 {
  font-size: 14px;
  height: 100%;
  width: 960px;
  margin: 0 auto;
}

#fjm1 .fjm-content {
  min-height: 100%;
  padding: 8px 12px;
  padding-bottom: 0;
  box-sizing: border-box;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}

#fjm1 .c-item {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

#fjm1 .bro-w {
  display: flex;
  align-items: center;
  border: 1px solid #C8D7E6;
  padding: 4px 10px;
  background: #EBEEF5;
}

#fjm1 input[type="text"] {
  border: 1px solid #DCDFE6;
  width: 60px;
  padding-left: 6px;
  margin: 0 6px;
}

#fjm1 .cdfi-title {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  width: 92px;
  background: #EBEEF5;
  border-right: 1px solid #C8D7E6;
}

#fjm1 .cdfi-title .w-con {
  width: 100%;
  padding-left: 12px;
}

#fjm1 .cdfi-title .w-con:hover {
  background-color: #E4E7ED;
}

#fjm1 .cdfi-content {
  padding: 8px 12px;
}

#fjm1 .bro-n{
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 8px;
}

#fjm1 input[type="radio"], #fjm1 input[type="checkbox"] {
  vertical-align: middle;
}

#fjm1 .to-cd {
  cursor: pointer;
}

#fjm1 label+label {
  margin-left: 30px;
}

#fjm1 .c-item+.c-item {
  margin-top: 12px;
}
#fjm1 .ml-10{
  margin-left: 10px;
}
#fjm1 .ml-30{
  margin-left: 30px;
}