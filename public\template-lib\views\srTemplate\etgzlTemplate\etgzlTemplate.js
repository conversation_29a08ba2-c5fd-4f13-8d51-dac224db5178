$(function() {
  window.initHtmlScript = initHtmlScript;
  // initHtmlScript('#etgzl1', 'edit');
  // initHtmlScript('#etgzl1', 'view');
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele, dev) {
  curElem = $(ele);
  if(window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }
  if(rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if(rtStructure.enterOptions.type==='view') {
      initPage('view');
    } else {
      initPage('edit');
    }
  }
  dev && initPage(dev);
}
/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = getDescription();
  rtStructure.impression = $('textarea.impression').val().trim();
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '', //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '', //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '', //审核医生
    affirmReporterNo: '', //审核医生流水号
    affirmDate: '', //审核日期
    reDiagReporter: '', //复诊报告医生
    reDiagReporterNo: '', //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
}

function initPage(type) {
  if (type === 'edit') {
    initPan();
    initDropdown();
    initForm();
  }
  if (type === 'view') {
    initPan();
    initPreview();
  }
}

// 初始化板块状态
function initPan() {
  // 初始化板块可见性
  $('.switcher-ctrl:checked').each(function() {
    $(this).parents('.switcher-wrap').addClass($(this).attr('data-class-add'));
    $(this).parents('.switcher-wrap').removeClass($(this).attr('data-class-rmv'));
  });
  
  // 病灶数目
  var bzsm = $('[type="radio"][name="bzsm"]:checked');
  if (bzsm.length > 0 && bzsm.val() === '多发') {
    $('.bzsize-text').text('较大者大小约：');
  }
}

// 初始化表单控制
function initForm() {
  $('input').on('input', function() {
    getImpression();
    // console.log(getDescription());
  });
  // 病灶数目切换
  $('[type="radio"][name="bzsm"]').on('change', function() {
    if (this.value === '多发') {
      $('.bzsize-text').text('较大者大小约：');
    } else {
      $('.bzsize-text').text('大小约：');
    }
  });

  // 板块切换
  $('.switcher-ctrl').on('change', function() {
    $(this).parents('.switcher-wrap').addClass($(this).attr('data-class-add'));
    $(this).parents('.switcher-wrap').removeClass($(this).attr('data-class-rmv'));
  });
}

// 初始化下拉
function initDropdown() {
  var options01 = ['低信号', '稍低信号', '中等信号', '轻度高信号', '中度高信号', '明显高信号', ''];
  var options02 = ['低密度', '等密度', '高密度', ''];
  renderDropdown({
    selector: '.options01',
    dataList: options01
  });
  renderDropdown({
    selector: '.options02',
    dataList: options02
  });
}

// 预览
function initPreview() {
  // 肝脏体积
  if (!getVal('[name="gztj"]:checked')) {
    $('[name="gztj"]').parents('.detail-item').hide();
  }

  // 肝叶比例
  if (!getVal('[type="radio"][name="gybl"]:checked')) {
    $('[name="gybl"]').parents('.detail-item').hide();
  }

  //病灶情况
  var isBzqkFilled;
  if (!getVal('[name="bzqkwz"]:checked')) {
    $('[name="bzqkwz"]').parents('.row').hide();
  } else { isBzqkFilled = true }
  if (!getVal('[name="bzsm"]:checked')) {
    $('[name="bzsm"]').parents('.row').hide();
  } else { isBzqkFilled = true }
  if (!getVal('.bzsize input')) {
    $('.bzsize').hide();
  } else { isBzqkFilled = true }
  if (!getVal('[name="bzxt"]:checked')) {
    $('[name="bzxt"]').parents('.row').hide();
  } else { isBzqkFilled = true }
  if (!getVal('[name="bzby"]:checked')) {
    $('[name="bzby"]').parents('.row').hide();
  } else { isBzqkFilled = true }

  if (!getVal('[name="ctORmr"]:checked')) {
    $('[name="ctORmr"]').parents('.switcher-wrap').hide();
  }
  var isCtOrMrFilled;
  var ctORmr = $('[name="ctORmr"]:checked').attr('data-value');
  if (ctORmr === 'mr') {
    if (!getVal('.bzxhms input')) {
      $('.bzxhms').hide();
    } else { isBzqkFilled = true; isCtOrMrFilled = true; }
    if (!getVal('[name="bzxh"]:checked')) {
      $('[name="bzxh"]').parents('.srow').hide();
    } else { isBzqkFilled = true; isCtOrMrFilled = true; }
    if (!getVal('.bzqhtd input')) {
      $('.bzqhtd').hide();
    } else { isBzqkFilled = true; isCtOrMrFilled = true; }
  }
  if (ctORmr === 'ct') {
    if (!getVal('.mdtext input')) {
      $('.mdtext').hide();
    } else { isBzqkFilled = true; isCtOrMrFilled = true; }
    if (!getVal('[name="bzmdjy"]:checked')) {
      $('[name="bzmdjy"]').parents('.srow').hide();
    } else { isBzqkFilled = true; isCtOrMrFilled = true; }
  }
  if (!isCtOrMrFilled) {
    $('[name="ctORmr"]').parents('.switcher-wrap').hide();
  }

  if (!getVal('[name="bzcxpd"]:checked')) {
    $('[name="bzcxpd"]').parents('.row').hide();
  } else { isBzqkFilled = true }
  if (!getVal('[name="bzghpd"]:checked')) {
    $('[name="bzghpd"]').parents('.row').hide();
  } else { isBzqkFilled = true }
  if (!getVal('[name="bzbmpd"]:checked')) {
    $('[name="bzbmpd"]').parents('.row').hide();
  } else { isBzqkFilled = true }
  if (!isBzqkFilled) {
    $('[name="bzqkwz"]').parents('.detail-item').hide();
  }



  // 病灶性质
  if (!getVal('[name="bzxzpd"]:checked')) {
    $('[name="bzxzpd"]').parents('.detail-item').hide();
  }
  
  // 肝静脉受累 门静脉受累
  // 肝静脉受累
  if (!getVal('[name="gjmypx"]:checked,[name="gjmbr"]:checked,[name="gjmxgnsz"]:checked')) {
    $('[name="gjmypx"]').parents('.detail-item').hide();
  }
  // 门静脉受累
  if (!getVal('[name="mjmzs"]:checked,[name="mjmbr"]:checked,[name="mjmxgnsz"]:checked')) {
    $('[name="mjmzs"]').parents('.detail-item').hide();
  }
  ['gjmypx', 'gjmbr', 'gjmxgnsz', 'mjmzs', 'mjmbr', 'mjmxgnsz'].forEach(function(name) {
    if ($('[name="'+ name +'"]:checked').length === 0) {
      $('[name="'+ name +'"]').parents('.row').hide();
    }
  });

  // 肝外病变
  if (!getVal('[name="gwbbgj"]:checked,[name="gwbbfmjj"]:checked,[name="gwbbqttext"]')) {
    $('[name="gwbbgj"]').parents('.detail-item').hide();
  }
  var gwbbFilled;
  var gwbbarr = [{name: 'gwbbgj', title:'膈肌'}, {name: 'gwbbfmjj', title:'腹膜结节'}];
  gwbbarr.forEach(function(item) {
    var name = item.name;
    var title = item.title;
    var chk = $('[name="'+ name +'"]:checked');
    if (chk.length === 0) {
      $('[name="'+ name +'"]').parents('.row').hide();
    } else {
      var val = chk.val();
      if (val === '无') {
        $('[name="'+ name +'"]').parents('.row').hide();
      }
      if (val === '有') {
        gwbbFilled = true;
        var txt = $('[type="text"][name="'+ name +'"]').val();
        if (txt) {
          $('[name="'+ name +'"]').parents('.row').text(title + '(' + txt + ')');
        } else {
          $('[name="'+ name +'"]').parents('.row').text(title);
        }
      }
    }
  });
  var gwbbqttext = $('[name="gwbbqttext"]');
  var gwbbqttextval = gwbbqttext.val();
  if (gwbbqttextval) {
    gwbbFilled = true;
    gwbbqttext.parents('.row').text(gwbbqttextval);
  } else {
    gwbbqttext.parents('.row').hide();
  }
  if (!gwbbFilled) {
    $('[name="gwbbgj"]').parents('.detail-item').hide();
  }

  // 腹水
  if (!getVal('[name="gfspd"]:checked')) {
    $('[name="gfspd"]').parents('.detail-item').hide();
  }
  var fschk = $('[name="gfspdy"]:checked');
  var fschkval = fschk.val();
  if (fschkval) {

  } else {
    $('.fskh').hide();
  }

  // 淋巴结转移
  // ['lbjzyy1', 'lbjzyy2', 'lbjzyy3', 'lbjzyy4'].forEach(function(name) {
  //   var chk = $('[name="'+ name +'"]:checked');
  //   if (chk.length === 0) {
  //     $('[name="'+ name +'"]').parents('.row').hide();
  //   }
  // });
  var lbjzyy1 = getVal('[type="checkbox"][name="lbjzyy1"]:checked');
  var lbjzyy1count = getVal('.lbjzygmq [type="text"]:eq(0)');
  var lbjzyy1len = getVal('.lbjzygmq [type="text"]:eq(1)');
  if (lbjzyy1) {
    $('.lbjzygmq').text('肝门区，' + lbjzyy1count + '枚，最大短径' + lbjzyy1len + 'mm');
  } else {
    $('.lbjzygmq').hide();
  }
  var lbjzyy2 = getVal('[type="checkbox"][name="lbjzyy2"]:checked');
  var lbjzyy2count = getVal('.lbjzyymqjx [type="text"]:eq(0)');
  var lbjzyy2len = getVal('.lbjzyymqjx [type="text"]:eq(1)');
  if (lbjzyy2) {
    $('.lbjzyymqjx').text('门腔间隙，' + lbjzyy2count + '枚，最大短径' + lbjzyy2len + 'mm');
  } else {
    $('.lbjzyymqjx').hide();
  }
  var lbjzyy3 = getVal('[type="checkbox"][name="lbjzyy3"]:checked');
  var lbjzyy3count = getVal('.lbjzyyfmh [type="text"]:eq(0)');
  var lbjzyy3len = getVal('.lbjzyyfmh [type="text"]:eq(1)');
  if (lbjzyy3) {
    $('.lbjzyyfmh').text('腹膜后，' + lbjzyy3count + '枚，最大短径' + lbjzyy3len + 'mm');
  } else {
    $('.lbjzyyfmh').hide();
  }
  var lbjzyy4 = getVal('[type="checkbox"][name="lbjzyy4"]:checked');
  var lbjzyy4name = getVal('.lbjzyyqt [type="text"]:eq(0)');
  var lbjzyy4count = getVal('.lbjzyyqt [type="text"]:eq(1)');
  var lbjzyy4len = getVal('.lbjzyyqt [type="text"]:eq(2)');
  if (lbjzyy4 && lbjzyy4name) {
    $('.lbjzyyqt').text(lbjzyy4name + '，' + lbjzyy4count + '枚，最大短径' + lbjzyy4len + 'mm');
  } else {
    $('.lbjzyyqt').hide();
  }

  // 远处转移
  var yczyFilled;
  ['yczym1', 'yczym2', 'yczym3', 'yczym4', 'yczym5'].forEach(function(name) {
    var chk = $('[name="'+ name +'"]:checked');
    if (chk.length === 0) {
      $('[name="'+ name +'"]').parents('.row').hide();
    } else {
      yczyFilled = true;
    }
  });
  var yczym1 = getVal('[name="yczym1"]');
  if (yczym1) {
    $('.yczy-wrap .quo').hide();
  }
  if (!yczyFilled) {
    $('.yczy-wrap').parents('.detail-item').hide();
  }

  // 胆道系统
  var dgkzqkchk = $('[type="radio"][name="dgkzqk"]:checked');
  var dgkzqktxt = $('[type="text"][name="dgkzqk"]');
  var dgkzqkStr = '胆管扩张：' + dgkzqkchk.val();
  if (dgkzqktxt.val()) {
    dgkzqkStr += '（内径：' + dgkzqktxt.val() + 'mm）';
  }
  dgkzqktxt.parents('.row').text(dgkzqkStr);


  // 胰腺
  let yxqktext = $('[name="yxqktext"]');
  if (!yxqktext.val()) {
    yxqktext.parents('.row').hide();
  }

  // 其他征像
  $('.with-textarea').removeClass('with-textarea');
}

var pretextMap = {
  'I': ['01000', '00001'],
  'II': ['01100', '00100', '00010', '00011', '10000', '11000', '10100', '11100', '10010', '10011', '10001', '01001'],
  'III': ['01110', '00110', '00111', '11110', '10110', '10111', '00101', '01010', '01011', '01101', '10101', '11001', '11010', '11010', '11110', '11101'],
  'IV': ['11111', '01111']
};

// 推导印象
function getImpression() {
  var strArr = [];
  var str = '肝脏';

  // 位置
  var bzqkwzArr = [];
  var bzqkwzchk = $('[name="bzqkwz"]:checked');
  if (bzqkwzchk.length > 0 ) {
    str += '(';
    bzqkwzchk.each(function() {
      str += this.value + '、';
      bzqkwzArr.push(this.value);
    });
    str = str.slice(0, str.length - 1);
    str += ')';
  }

  // 单发多发
  var bzsmchk = $('[name="bzsm"]:checked');
  if (bzsmchk.val() && bzsmchk.val() !== '单发') {
    str += bzsmchk.val();
  }
  str += '占位性病变，考虑肝肿瘤，性质为';

  // 病灶性质
  var bzxzpdchk = $('[type="radio"][name="bzxzpd"]:checked');
  if (bzxzpdchk.val() === '其他') {
    str += $('[type="text"][name="bzxzpdt"]').val();
  } else {
    str += bzxzpdchk.val() || '';
  }
  str += '。'

  var PRETEXTarr = [];

  // 分期推导
  var pretext = '';
  $('[name="bzqkwz"]').each(function() {
    if (this.checked) {
      pretext += '1';
    } else {
      pretext += '0';
    }
  });
  for (let k in pretextMap) {
    if (pretextMap[k].indexOf(pretext) !== -1) {
      PRETEXTarr.push('PRETEXT '+ k +'期');
    }
  }

  // 肝静脉受累
  var gjmslText = '';
  var gjmsl1 = '';
  $('[name="gjmypx"]:checked').each(function() {
    gjmsl1 += this.value;
  });
  if (gjmsl1.indexOf('下腔静脉肝内段') !== -1 || gjmsl1.indexOf('肝右静脉肝中静脉肝左静脉') !== -1) {
    gjmslText = 'V+';
  }
  var gjmsl2 = '';
  $('[name="gjmbr"]:checked').each(function() {
    gjmsl2 += this.value;
  });
  if (gjmsl2.indexOf('下腔静脉肝内段') !== -1 || gjmsl2.indexOf('肝右静脉肝中静脉肝左静脉') !== -1) {
    gjmslText = 'V+';
  }
  if ($('[name="gjmxgnsz"]:checked').length > 0) {
    gjmslText = 'V+';
  }
  if (gjmslText) {
    PRETEXTarr.push(gjmslText);
  }

  // 门静脉受累
  var mjmslText = '';
  var mjmsl1 = '';
  $('[name="mjmzs"]:checked').each(function() {
    mjmsl1 += this.value;
  });
  if (mjmsl1.indexOf('右主干左主干') !== -1 || mjmsl1.indexOf('门静脉主干') !== -1) {
    mjmslText = 'P+';
  }
  var mjmsl12 = '';
  $('[name="mjmbr"]:checked').each(function() {
    mjmsl12 += this.value;
  });
  if (mjmsl12.indexOf('右主干左主干') !== -1 || mjmsl12.indexOf('门静脉主干') !== -1) {
    mjmslText = 'P+';
  }
  if ($('[name="mjmxgnsz"]:checked').length > 0) {
    mjmslText = 'P+';
  }
  if (mjmslText) {
    PRETEXTarr.push(mjmslText);
  }

  // 肝外病变
  var gwbbText = '';
  if ($('[name="gwbbgj"]:checked').val() === '有') {
    gwbbText = 'E+';
  }
  if ($('[name="gwbbfmjj"]:checked').val() === '有') {
    gwbbText = 'E+';
  }
  if ($('[name="gwbbqttext"]').val()) {
    gwbbText = 'E+';
  }
  if (gwbbText) {
    PRETEXTarr.push(gwbbText);
  }

  // 肿瘤破裂
  if ($('[name="bzbmpd"]:checked').val() === '不光整') {
    PRETEXTarr.push('R+');
  }

  // 淋巴结转移
  if ($('[name="lbjzy"]:checked').val() === '有') {
    PRETEXTarr.push('N+');
  }

  // 远处转移
  if ($('.yczy-wrap input:checked').length > 0) {
    PRETEXTarr.push('M+');
  }

  // 病灶侵犯尾状叶
  if (bzqkwzArr.indexOf('尾状叶') !== -1) {
    PRETEXTarr.push('C+');
  }

  if (PRETEXTarr.length > 0) {
    str += '(' + PRETEXTarr.join('，') + ')';
  }

  strArr.push(str);
  str = '';

  // 出血
  if ($('[name="bzcxpd"]:checked').val() === '是') {
    strArr.push('出血');
  }

  // 腹水
  if ($('[name="gfspd"]:checked').val() === '有') {
    strArr.push('腹水');
  }

  // 脾脏
  var paDnYzArr = [];
  var pzqkyc = getVal('[type="radio"][name="pzqkyc"]:checked');
  if (pzqkyc === '可见异常' && $('[name="pznz1"]:checked').length > 0) {
    paDnYzArr.push('脾囊肿');
  }
  // 胆囊
  var ddxtyc = getVal('[type="radio"][name="ddxtyc"]:checked');
  if (ddxtyc === '可见异常') {
    var dnText = [];
    $('.dnip:checked').each(function() {
      dnText.push(this.value);
    });
    if (dnText.length === 1) {
      paDnYzArr.push('胆囊' + dnText[0]);
    }
    if (dnText.length > 1) {
      paDnYzArr.push('胆囊(' + dnText.join('、') + ')');
    }
  }
  // 胰腺
  var yxqk = getVal('[type="radio"][name="yxqk"]:checked');
  if (yxqk === '可见异常') {
    var yxText = [];
    $('.yximprs:checked').each(function() {
      yxText.push(this.value);
    });
    if (yxText.length === 1) {
      paDnYzArr.push('胰腺' + yxText[0]);
    }
    if (yxText.length > 1) {
      paDnYzArr.push('胰腺(' + yxText.join('、') + ')');
    }
  }
  if (paDnYzArr.length > 0) {
    strArr.push(paDnYzArr.join('，'));
  }

  var impressionStr = '';
  strArr.forEach(function(item, i) {
    impressionStr += (i + 1) + '.' + item + '\n';
  });
  $('textarea.impression').val(impressionStr.trim());
}

// 推导影像所见
function getDescription() {
  var description = '　　肝脏体积' + getVal('[name="gztj"]:checked');

  description += '，肝叶比例';
  var gyblval = getVal('[type="radio"][name="gybl"]:checked');
  var gybltxt = getVal('[type="text"][name="gyblt"]');
  if (gyblval === '协调') {
    description += gyblval;
  } else {
    description += gybltxt;
  }

  var bzsm = getVal('[name="bzsm"]:checked');
  description += '，' + bzsm + '病灶位于' + getVal('[name="bzqkwz"]:checked');
  description += '，';
  if (bzsm === '多发') {
    description += '较大者';
  }
  description += '大小约：' + getVal('.bzsize input', 'mm×') + 'mm';
  description += '，形态呈' + getVal('[name="bzxt"]:checked');
  description += '，边缘' + getVal('[name="bzby"]:checked');
  description += '。';

  var ctORmr = $('[name="ctORmr"]:checked').attr('data-value');
  if (ctORmr === 'mr') {
    description += 'T2WIfs呈' + getVal('input.T2WIfs') + '，';
    description += 'T1WI呈' + getVal('input.T1WI') + '，';
    description += '双回波呈' + getVal('input.shb') + '，';
    description += 'DWI呈' + getVal('input.DWI') + '，';
    description += '信号' + getVal('[name="bzxh"]:checked');
    var qhtd = getVal('.bzqhtd input');
    if (qhtd) {
      description += '，' + qhtd;
    }
    description += '。';
  }
  if (ctORmr === 'ct') {
    description += '病灶平扫呈' + getVal('.mdtext input:eq(0)') + '，';
    description += '增强呈' + getVal('.mdtext input:eq(1)') + '，';
    description += '延迟期呈' + getVal('.mdtext input:eq(2)') + '，';
    description += '密度' + getVal('[name="bzmdjy"]:checked') + '。';
  }

  var bzcxpd = getVal('[type="text"][name="bzcxpdt"], [type="text"][name="bzghpdt"]');
  if (bzcxpd) {
    description += bzcxpd + '。';
  }
  description += '病灶包膜' + getVal('[name="bzbmpd"]:checked') + '。';

  description += '\n　　V(肝静脉)：';
  var gjmArr = [];
  var gjmypx = getVal('[name="gjmypx"]:checked');
  gjmypx && gjmArr.push('压迫性闭塞（' + gjmypx + '）');
  var gjmbr = getVal('[name="gjmbr"]:checked');
  gjmbr && gjmArr.push('包绕血管>50%/包绕血管180°（' + gjmbr + '）');
  var gjmxgnsz = getVal('[name="gjmxgnsz"]:checked');
  gjmxgnsz && gjmArr.push('血管内栓子（' + gjmxgnsz + '）');
  description += gjmArr.join('，') + '。';

  description += '\n　　P(门静脉)：';
  var mjmArr = [];
  var mjmzs = getVal('[name="mjmzs"]:checked');
  mjmzs && mjmArr.push('阻塞静脉（' + mjmzs + '）');
  var mjmbr = getVal('[name="mjmbr"]:checked');
  mjmbr && mjmArr.push('包绕静脉>50%/180°（' + mjmbr + '）');
  var mjmxgnsz = getVal('[name="mjmxgnsz"]:checked');
  mjmxgnsz && mjmArr.push('血管内栓子（' + mjmxgnsz + '）');
  description += mjmArr.join('，') + '。';

  description += '\n　　E(肝外病变)：';
  var gwbbArr = [];
  var gwbbgj = getVal('[type="radio"][name="gwbbgj"]:checked');
  if (gwbbgj === '有') {
    gwbbgj = '膈肌';
    var gwbbgjtxt = getVal('[type="text"][name="gwbbgj"]');
    if (gwbbgjtxt) {
      gwbbgj += '（' + gwbbgjtxt + '）';
    }
  } else {
    gwbbgj = '';
  }
  gwbbgj && gwbbArr.push(gwbbgj);
  var gwbbfmjj = getVal('[type="radio"][name="gwbbfmjj"]:checked');
  if (gwbbfmjj === '有') {
    gwbbfmjj = '腹膜结节';
    var gwbbfmjjtxt = getVal('[type="text"][name="gwbbfmjj"]');
    if (gwbbfmjjtxt) {
      gwbbfmjj += '（' + gwbbfmjjtxt + '）';
    }
  } else {
    gwbbfmjj = '';
  }
  gwbbfmjj && gwbbArr.push(gwbbfmjj);
  var gwbbqttext = getVal('[type="text"][name="gwbbqttext"]');
  gwbbqttext && gwbbArr.push(gwbbqttext);
  description += gwbbArr.join('，') + '。';

  description += '\n　　腹水：';
  var gfspd = getVal('[type="radio"][name="gfspd"]:checked');
  if (gfspd === '无') {
    description += '无。'
  }
  if (gfspd === '有') {
    description += '腹腔内可见' + getVal('[type="radio"][name="gfspdy"]:checked') + '液体';
    if (ctORmr === 'mr') {
      description += '信号';
    }
    if (ctORmr === 'ct') {
      description += '密度';
    }
    description += '影';
    var gfspdytxt = getVal('[type="text"][name="gfspdyt"]');
    if (gfspdytxt) {
      description += '，' + gfspdytxt;
    }
    description += '。';
  }

  description += '\n　　N(淋巴结)：';
  var lbjzy = getVal('[type="radio"][name="lbjzy"]:checked');
  if (lbjzy === '无') {
    description += '未见异常。';
  }
  if (lbjzy === '有') {
    var arr = [];
    var total = 0;
    var lbjzyy1 = getVal('[type="checkbox"][name="lbjzyy1"]:checked');
    var lbjzyy1count = getVal('.lbjzygmq [type="text"]:eq(0)');
    var lbjzyy1len = getVal('.lbjzygmq [type="text"]:eq(1)');
    if (lbjzyy1) {
      arr.push('肝门区，' + lbjzyy1count + '枚，最大短径' + lbjzyy1len + 'mm');
      total += parseInt(lbjzyy1count) || 0;
    }
    var lbjzyy2 = getVal('[type="checkbox"][name="lbjzyy2"]:checked');
    var lbjzyy2count = getVal('.lbjzyymqjx [type="text"]:eq(0)');
    var lbjzyy2len = getVal('.lbjzyymqjx [type="text"]:eq(1)');
    if (lbjzyy2) {
      arr.push('门腔间隙，' + lbjzyy2count + '枚，最大短径' + lbjzyy2len + 'mm');
      total += parseInt(lbjzyy2count) || 0;
    }
    var lbjzyy3 = getVal('[type="checkbox"][name="lbjzyy3"]:checked');
    var lbjzyy3count = getVal('.lbjzyyfmh [type="text"]:eq(0)');
    var lbjzyy3len = getVal('.lbjzyyfmh [type="text"]:eq(1)');
    if (lbjzyy3) {
      arr.push('腹膜后，' + lbjzyy3count + '枚，最大短径' + lbjzyy3len + 'mm');
      total += parseInt(lbjzyy3count) || 0;
    }
    var lbjzyy4 = getVal('[type="checkbox"][name="lbjzyy4"]:checked');
    var lbjzyy4name = getVal('.lbjzyyqt [type="text"]:eq(0)');
    var lbjzyy4count = getVal('.lbjzyyqt [type="text"]:eq(1)');
    var lbjzyy4len = getVal('.lbjzyyqt [type="text"]:eq(2)');
    if (lbjzyy4) {
      arr.push(lbjzyy4name + '，' + lbjzyy4count + '枚，最大短径' + lbjzyy4len + 'mm');
      total += parseInt(lbjzyy4count) || 0;
    }
    if (total > 0) {
      description += total + '枚可疑淋巴结转移，分别位于' + arr.join('，') + '。';
    }
  }

  description += '\n　　M(远处转移)：';
  var yczyArr = [];
  var yczyfb = getVal('[type="radio"][name="yczyfb"]:checked');
  var yczyfbtxt = getVal('[type="text"][name="yczyfbt"]');
  if (yczyfb) {
    if (yczyfbtxt) {
      yczyfb += '（' + yczyfbtxt + '）';
    }
    yczyArr.push(yczyfb);
  }
  ['yczym2', 'yczym3', 'yczym4', 'yczym5'].forEach(function(name) {
    var chk = getVal('[type="checkbox"][name="'+ name +'"]:checked');
    var txt = getVal('[type="text"][name="'+ name +'t"]');
    if (chk) {
      if (chk === '其他') {
        chk = '';
        if (txt) {
          chk += txt;
        }
      } else {
        if (txt) {
          chk += '（' + txt + '）';
        }
      }
      chk && yczyArr.push(chk);
    }
  });
  if (yczyArr.length === 0) {
    description += '未见异常。';
  } else {
    description += yczyArr.join('，') + '。';
  }

  description += '\n　　脾脏：';
  var pzqkyc = getVal('[type="radio"][name="pzqkyc"]:checked');
  if (pzqkyc === '无异常') {
    description += '未见异常。';
  }
  if (pzqkyc === '可见异常') {
    var pzArr = [];
    var pznz = getVal('[type="text"][name="pznz"]');
    pznz && pzArr.push(pznz);
    var pznzqt = getVal('[type="text"][name="pznzqt"]');
    pznzqt && pzArr.push(pznzqt);
    var pzqh = getVal('[type="text"][name="pzqh"]');
    pzqh && pzArr.push(pzqh);
    if (pzArr.length > 0) {
      description += pzArr.join('，') + '。';
    }
  }

  description += '\n　　胆道系统：';
  var ddxtyc = getVal('[type="radio"][name="ddxtyc"]:checked');
  if (ddxtyc === '无异常') {
    description += '未见异常。';
  }
  if (ddxtyc === '可见异常') {
    var ddxtArr = [];
    var dnxt = getVal('[type="radio"][name="dnxt"]:checked');
    var dnxttxt = getVal('[type="text"][name="dnxt"]');
    if (dnxttxt || dnxt === '增大') {
      ddxtArr.push('胆囊形态' + (dnxttxt || dnxt));
    }
    ['dnjs', 'dnxr', 'dnxjz', 'dndny', 'dnqt'].forEach(function(name) {
      var txt = getVal('[type="text"][name="'+ name +'"]');
      txt && ddxtArr.push(txt);
    });
    var dnqh = getVal('[type="radio"][name="dnqh"]:checked');
    var dnqhtxt = getVal('[type="text"][name="dnqh"]');
    if (dnqhtxt || dnqh === '未见异常强化') {
      ddxtArr.push(dnqhtxt || dnqh);
    }
    if (ddxtArr.length > 0) {
      description += ddxtArr.join('，') + '。';
      ddxtArr = [];
    }
    var dgbw = getVal('[type="checkbox"][name="dgbw"]:checked');
    if (dgbw) {
      ddxtArr.push(dgbw + '受累');
    }
    var dgkzqk = getVal('[type="radio"][name="dgkzqk"]:checked');
    var dgkzqktxt = getVal('[type="text"][name="dgkzqk"]');
    if (dgkzqk === '扩张') {
      ddxtArr.push('可见胆管扩张');
    }
    if (dgkzqktxt) {
      ddxtArr.push('内径'+ dgkzqktxt +'mm');
    }
    var dgqhqk = getVal('[type="radio"][name="dgqhqk"]:checked');
    var dgqhqktxt = getVal('[type="text"][name="dgqhqk"]');
    if (dgqhqktxt || dgqhqk === '未见异常强化') {
      ddxtArr.push(dgqhqktxt || dgqhqk);
    }
    if (ddxtArr.length > 0) {
      description += ddxtArr.join('，') + '。';
    }
  }

  description += '\n　　胰腺：';
  var yxqk = getVal('[type="radio"][name="yxqk"]:checked');
  if (yxqk === '无异常') {
    description += '未见异常。';
  }
  if (yxqk === '可见异常') {
    var yxqkArr = [];
    var yxxtqk = getVal('[type="radio"][name="yxxtqk"]:checked');
    var yxxtqktxt = getVal('[type="text"][name="yxxtqk"]');
    if (yxxtqktxt || yxxtqk === '增大') {
      yxqkArr.push('胰腺形态' + (yxxtqktxt || yxxtqk));
    }
    ['yxbwnz', 'yxbwipmn', 'yxbwspn', 'yxbwzyl', 'yxbwyxl', 'yxbwqt', 'yxqktext'].forEach(function(name) {
      var txt = getVal('[type="text"][name="'+ name +'"]');
      txt && yxqkArr.push(txt);
    });
    var yxqhqk = getVal('[type="radio"][name="yxqhqk"]:checked');
    var yxqhqktxt = getVal('[type="text"][name="yxqhqk"]');
    if (yxqhqktxt || yxqhqk === '未见异常强化') {
      yxqkArr.push(yxqhqktxt || yxqhqk);
    }
    if (yxqkArr.length > 0) {
      description += yxqkArr.join('，') + '。';
    }
  }

  description += '\n　　其他征象：';
  var qtzx = getVal('textarea.qtzx');
  qtzx = qtzx || '无'
  description += qtzx + '。';

  return description;
}