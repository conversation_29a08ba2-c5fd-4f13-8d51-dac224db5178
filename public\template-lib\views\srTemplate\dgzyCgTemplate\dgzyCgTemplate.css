.singleDisEditReport.main-page{
  min-width: unset;
}
#dgzyCg1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#dgzyCg1 * {
  font-family: STSongti-SC, STSongti-SC;
}
#dgzyCg1 .dgzyCg-edit{
  padding: 8px 12px;
}
#dgzyCg1 .label-wrap {
  width: 85px;
  text-align: left;
  padding-top: 8px;
}
#dgzyCg1 .black-lb {
  color: #303133;
}
#dgzyCg1 .blue-lb {
  font-size: 16px;
  color: #1885F2;
  cursor: pointer;
  text-decoration: underline;
}
#dgzyCg1 .blue-lb:hover {
  opacity: 0.8;
}
#dgzyCg1 .editor-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 3px;
  flex: 1;
  background: #fff;
}
#dgzyCg1 .text-size, .diag-text-size .text-size {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-bottom: 1px solid #C0C4CC;
}
#dgzyCg1 .text-size img, .diag-text-size .text-size img {
  width: 24px;
  margin-right: 8px;
  vertical-align: middle;
}
#dgzyCg1 .text-size img:hover, .diag-text-size .text-size img:hover {
  cursor: pointer;
  opacity: 0.8;
}
#dgzyCg1 .text-size .on, .diag-text-size .text-size .on {
  display: none;
}
#dgzyCg1 .editor-area {
  /* padding: 4px 0; */
  min-height: 100px;
}
.editor-area textarea {
  width: 100%;
  min-height: 100px;
  border: none;
  font-size: 16px;
  resize: vertical;
}
#dgzyCg1 .p-item + .p-item {
  margin-top: 12px;
}
.editor-area.default textarea{
  font-size: 16px;
}
.editor-area.large textarea{
  font-size: 18px;
}
.editor-area.larger textarea{
  font-size: 20px;
}
#dgzyCg1 .report-people {
  display: flex;
  margin-top: 12px;
  align-items: flex-end;
}
#dgzyCg1 .report-people .flex-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#dgzyCg1 .report-people .flex-item + .flex-item {
  margin-left: 12px;
}
#dgzyCg1 .report-people .label-text {
  font-size: 14px;
  color: #303133;
  text-align: right;
}
#dgzyCg1 .report-people .flex-item select {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
}
#dgzyCg1 .report-people .minW-200 {
  min-width: unset;
  width: 110px;
}

*:disabled {
  background: #fff;
  color: unset;
}
textarea[readonly] {
  cursor: not-allowed;
}
#dgzyCg1 .dgzyCg-view {
  display: none;
}
[isview="true"] #dgzyCg1 .dgzyCg-edit {
  display: none;
}
[isview="true"] #dgzyCg1 .dgzyCg-view {
  display: flex;
}
#dgzyCg1 .dgzyCg-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 115px 56px;
  flex-direction: column;
  position: relative;
}
#dgzyCg1 .dgzyCg-view .view-head {
  border-bottom: 2px solid #000;
  padding-bottom: 9px;
  text-align: center;
}
#dgzyCg1 .dgzyCg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
}
#dgzyCg1 .dgzyCg-view .logo-tit img {
  width: 80px;
  height: 80px;
  margin-right: 16px;
}
#dgzyCg1 .dgzyCg-view .blh-tit {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
}
#dgzyCg1 .dgzyCg-view .blh-tit [data-key="patLocalId"]{
  color: #E64545;
}
#dgzyCg1 .dgzyCg-view .hos-tit{
  font-weight: 900;
  font-size: 40px;
  color: #000;
  font-family: '楷体';
  letter-spacing: 14px;
}
#dgzyCg1 .dgzyCg-view .sub-tit{
  font-size: 28px;
  color: #000;
  font-weight: 900;
  margin-top: 8px;
  font-family: '楷体';
}
#dgzyCg1 .dgzyCg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 2px solid #000;
}
#dgzyCg1 .dgzyCg-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
}
#dgzyCg1 .dgzyCg-view .black-txt {
  color: #000;
  font-size: 16px;
}
#dgzyCg1 .dgzyCg-view .red-txt {
  color: #E64545;
  font-size: 16px;
}
#dgzyCg1 .dgzyCg-view .bold {
  font-weight: bold;
}
#dgzyCg1 .dgzyCg-view .info-i {
  width: 155px;
  display: flex;
  flex-wrap: wrap;
}
#dgzyCg1 .dgzyCg-view .info-i.w170 {
  width: 170px;
}
#dgzyCg1 .dgzyCg-view .info-i + .info-i {
  margin-left: 8px;
}
#dgzyCg1 .dgzyCg-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#dgzyCg1 .dgzyCg-view .view-patient .p-item {
  margin-top: 8px;
}
#dgzyCg1 .dgzyCg-view .body-exam-info {
  border-bottom: 2px solid #000;
  padding: 8px 0;
  font-size: 16px;
  color: #000;
}
#dgzyCg1 .dgzyCg-view .body-exam-info .b-exam-lb {
  font-weight: bold;
}
#dgzyCg1 .dgzyCg-view .body-exam-info .b-exam-info {
  flex: 1;
}
#dgzyCg1 .dgzyCg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}
#dgzyCg1 .dgzyCg-view .desc-con {
  display: none;
  margin-top: 10px;
  line-height: 22px;
}
#dgzyCg1 .dgzyCg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#dgzyCg1 .dgzyCg-view .desc-con {
  display: flex;
  align-items: baseline;
}
#dgzyCg1 .dgzyCg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#dgzyCg1 .dgzyCg-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#dgzyCg1 .dgzyCg-view .reporter-i span:last-child {
  flex: 1;
}
#dgzyCg1 .dgzyCg-view .reporter-i img {
  width: 90px;
  height: 40px;
  object-fit: contain;
  margin-left: 5px;
}
#dgzyCg1 .dgzyCg-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#dgzyCg1 .dgzyCg-view .tip-wrap {
  margin-top: 8px;
  font-weight: 900;
  font-size: 14px;
  color: #E64545;
}
#dgzyCg1 .dgzyCg-view .rpt-img-ls {
  display: none;
  flex-wrap: wrap;
  margin-bottom: 8px;
  padding-left: 70px;
  padding-bottom: 8px;
  border-bottom: 2px solid #000;
}
#dgzyCg1 .dgzyCg-view .item-img {
  display: inline-block;
  width: 240px;
  height: 180px;
  border: 1px solid #eee;
  margin-top: 8px;
}
#dgzyCg1 .dgzyCg-view .item-img:nth-child(odd) {
  margin-right: 20px;
}
#dgzyCg1 .dgzyCg-view .item-img:nth-child(even) {
  margin-left: 20px;
}
#dgzyCg1 .dgzyCg-view .rpt-img-ls[data-len="3"] .item-img{
  width: 210px;
  height: 160px;
}
#dgzyCg1 .dgzyCg-view .rpt-img-ls[data-len="3"],
#dgzyCg1 .dgzyCg-view .rpt-img-ls[data-len="4"] {
  padding-left: 0;
}
#dgzyCg1 .dgzyCg-view .rpt-img-ls[data-len="4"] .item-img {
  width: 158px;
  height: 120px;
  margin: 0 4px 8px 4px;
}
#dgzyCg1 .dgzyCg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#dgzyCg1 .p-item:nth-child(2) .text-size,#dgzyCg1 .p-item:nth-child(3) .text-size,#dgzyCg1 .p-item:nth-child(4) .text-size {
  display: none;
}
#dgzyCg1 .p-item:nth-child(2) .editor-area,#dgzyCg1 .p-item:nth-child(3) .editor-area {
  min-height: 72px;
}
#dgzyCg1 .p-item:nth-child(2) textarea,#dgzyCg1 .p-item:nth-child(3) textarea {
  min-height: 72px;
}
#dgzyCg1 .p-item:nth-child(4) .editor-area {
  min-height: 336px;
}
#dgzyCg1 .p-item:nth-child(4) textarea {
  min-height: 336px;
}
/* 显示模板entry-type="5"样式处理 */
[entry-type="5"] #dgzyCg1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #dgzyCg1 .dgzyCg-view {
  min-height: auto;
  margin: unset;
  padding: 12px;
  width: 100%;
}
[entry-type="5"] #dgzyCg1 .dgzyCg-view .view-head,
[entry-type="5"] #dgzyCg1 .dgzyCg-view .view-patient,
[entry-type="5"] #dgzyCg1 .dgzyCg-view .tip-wrap {
  display: none;
}
[entry-type="5"] #dgzyCg1 .dgzyCg-view div:not(.item-img) {
  border-bottom: none;
  border-top: none;
}
[entry-type="5"] #dgzyCg1 .dgzyCg-view .report-wrap {
  margin-top: 8px;
}
.rt-dialog-con {
  width: 800px!important;
  height: 80%;
  background: #F0F2F5!important;
  margin: 25px auto 0 auto;
}
.rt-dialog-header {
  padding: 16px 20px 0!important;
}
.rt-dialog-title{
  font-size: 18px!important;
  font-weight: bold;
}
.rt-dialog-content {
  height: 85%!important;
  padding: 16px 20px!important;
}
.rt-dialog-content .diag-text-size {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border: none;
  border-radius: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
