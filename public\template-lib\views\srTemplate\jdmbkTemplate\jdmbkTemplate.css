#jdmbk1 * {
  box-sizing: border-box !important;
}

.isk {
  color: #E64545;
}


/* 下拉框、复选框、单选框 */
select, input[type="checkbox"], input[type="radio"], input[type="text"] {
  vertical-align: middle;
}

/* 输入框、多行输入框 */
input[type="text"] {
  padding: 0 8px;
}

input[type="text"], textarea, select {
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
}

textarea {
  height: 80px;
  padding: 4px 10px !important;
}

/* 标签自定义 */
#jwbs .l-label {
  min-width: 112px !important;
  margin: 0;
}

#haveSss .l-label {
  min-width: 214px !important;
  margin: 0;
  text-align: left;
}

.jdmbk-head [data-tit]+[data-tit] {
  margin-left: 8px;
}

.jdmbk-title {
  width: 960px;
  margin: 0 auto;
  padding-left: 12px;
}

#jdmbk1 {
  font-size: 14px;
  height: calc(100% - 40px);
}

.jdmbk-edit {
  height: 100%;
}

.rpt-con {
  width: 100%;
  height: calc(100% - 96px);
  margin: 0 auto;
  overflow: auto;
}

.rpt-form {
  width: 960px;
  margin: 0 auto;
  min-height: 100%;
  padding-bottom: 0;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
}

.header-tab {
  height: 40px;
  background: #F5F7FA;
  margin-bottom: 12px;
  border-bottom: 1px solid #DCDFE6;
}

.box-sty {
  background: #F5F7FA;
  border: 1px solid #DCDFE6;
}

.con-pad {
  padding: 8px 12px;
}

.tab-box {
  display: flex;
  overflow: hidden;
}

.tab-item, .template-tab {
  height: 40px;
  padding: 0 18px;
  line-height: 40px;
  text-align: center;
  cursor: pointer;
  color: #303133;
  border-left: 1px solid #DCDFE6;
  border-right: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}

.tab-item:first-child, .template-tab:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.tab-item:last-child, .template-tab:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right: 1px solid #DCDFE6;
}

.tab-item.sel-tab, .template-tab.sel-tab {
  background: #FFFFFF;
  color: #1885F2;
  border: none;
  font-weight: 600;
}

.sel-radio {
  background-color: #F5F7FA;
  border-top: 1px solid #C8D7E6;
  border-bottom: 1px solid #C8D7E6;
  border-right: unset;
}

.template-tab {
  margin: 0;
  height: 36px;
  line-height: 36px;
}

.tab-content {
  height: calc(100% - 52px);
  padding: 12px;
}

.p-item {
  width: 100%;
}

.flex-box {
  display: flex;
  flex-wrap: wrap;
}

.l-label {
  display: inline-block;
  min-width: 70px;
  margin-top: 6px;
  text-align: right;
}

.l-content {
  width: calc(100% - 70px);
  padding: 4px 12px;
  background-color: #F5F7FA;
  border: 1px solid #C8D7E6;
}

.l-line {
  min-height: 28px;
}

.l-line+.l-line {
  margin-top: 4px;
}

label {
  display: inline-block;
  cursor: pointer;
}

label+label {
  margin-left: 36px;
}

.left-box, .left-box-block {
  border-right: 1px solid #C8D7E6;
}

.right-box {
  padding: 4px 0 4px 12px;
}

.radio-box {
  width: 100%;
  align-items: center;
  justify-content: space-evenly;
}

.radio-box+.radio-box {
  border-top: 1px solid #C8D7E6;
}

.table-box {
  padding: 0 12px;
}

.table-sty {
  max-width: 934px;
  border: 1px solid #C8D7E6;
  font-size: 14px;
  text-align: center;
  margin-top: 8px;
}

.table-sty th {
  /* width: 76px; */
  color: #909399;
  height: 48px;
  padding: 0 12px;
  background-color: #F5F7FA;
}

.table-sty th:first-child {
  width: 86px;
}

.table-sty td {
  color: #303133;
  height: 36px;
  padding: 0 4px;
  position: relative;
}

.table-sty td:first-child {
  padding-left: 12px;
  font-weight: 600;
}

.table-sty input[type="text"], .table-sty img {
  display: inline-block;
}

.table-sty input[type="text"] {
  width: 100%;
}

.table-sty .arrow-tip {
  position: absolute;
  right: 8px;
  top: 8px;
}

.table-sty img {
  width: 8px;
  height: 16px;
}

.temp-content {
  width: 100%;
  padding: 0 12px 12px 18px;
}

.content-100p .l-content {
  width: 100%;
}

.content-100p .l-content+.l-content {
  border-top: unset;
}

.add-btn {
  background: #1885F2;
  color: #FFF;
  height: 28px;
  line-height: 28px;
  padding: 0 12px;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
}

.add-btn:hover {
  opacity: 0.7;
}

[isview="true"] .view-none，[isview="true"] .add-btn{
  display: none;
}

.bz-wrap {
  border: 1px solid #C8D7E6;
  background: #F5F7FA;
  margin-top: 8px;
}

.jdmbk-bz,.xzqk-bz{
  display: none;
}

.bz-list .bz-wrap{
  display: none;
}
.bz-tit-ls {
  width: 100%;
  overflow-x: auto;
  height: 32px;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  display: -webkit-box;
  display: -moz-box;
}

.bz-tit-i {
  padding: 0 12px;
  color: #303133;
  border-right: 1px solid #DCDFE6;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.bz-tit-i .close-icon {
  color: #606266;
  margin-left: 5px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 14px;
  font-size: 18px;
  font-family: "Microsoft YaHei";
}

.bz-tit-i .close-icon:hover {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #e1e1e1;
}

.bz-tit-i.act {
  /* color: #1885F2; */
  color: #1885F2;
  position: relative;
  font-weight: 600;
  background: #F5F7FA;
}

.c-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}

.c-item.flex-at {
  align-items: flex-start;
}

.bz-item {
  padding: 8px;
  display: none;
}

.bz-item.act {
  display: block;
}

.bz-item .l-label {
  width: 90px;
  margin: 0;
}

.hight-item {
  color: #000;
}

.write-type {
  flex: 1;
  background: #F5F7FA;
}

.write-h {
  background: #EBEEF5;
  border: 1px solid #C8D7E6;
  padding: 4px 12px;
}

.left-box-block {
  background: #EBEEF5;
}

.rpt-bot {
  background: #E8F3FF;
  border-top: 1px solid #C8D7E6;
  padding: 8px 0;
  height: 94px;
}

.rpt-bot .rpt-imp {
  display: none;
  width: 922px;
  margin: 0 auto;
  word-break: break-all;
  white-space: pre;
}

.rpt-bot .rpt-imp .imp-lb {
  font-size: 18px;
  color: #000;
  font-weight: bold;
}

/* 颜色 */
.blue-color {
  color: #1885F2 !important;
}

.purple-color {
  color: #945FB9 !important;
}

/* 头部 */
.jdmbk-head {
  height: 40px;
  background: #E8F3FF;
  font-size: 18px;
  color: #000;
  padding: 8px 12px;
  border: 1px solid #C8D7E6;
  border-top: 0;
  font-weight: bold;
  width: 100%;
}

/* label边距 */
.label-ml0 label {
  margin-left: 0;
}

.label-l12 label+label {
  margin-left: 12px;
}

.label-l16 label+label {
  margin-left: 16px;
}

.label-l18 label+label {
  margin-left: 18px;
}

.label-l24 label+label {
  margin-left: 24px;
}

.label-l38 label+label {
  margin-left: 38px;
}

.label-l52 label+label {
  margin-left: 52px;
}

/*-------边距--------*/
.mt-4 {
  margin-top: 4px !important;
}

.mt-8 {
  margin-top: 8px !important;
}

.ml-6 {
  margin-left: 6px !important;
}

.mlburden-8 {
  margin-left: -8px;
}

.ml-12 {
  margin-left: 12px !important;
}

.ml-24 {
  margin-left: 24px !important;
}

.mr-4 {
  margin-right: 4px !important;
}


.mr-8 {
  margin-right: 8px !important;
}

.mr-12 {
  margin-right: 12px !important;
}

.mr-14 {
  margin-right: 14px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mr-24 {
  margin-right: 24px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mr-38 {
  margin-right: 38px !important;
}

.mr-52 {
  margin-right: 52px !important;
}

.mr-96 {
  margin-right: 96px !important;
}

.mr-176 {
  margin-right: 176px !important;
}

.mr-190 {
  margin-right: 190px !important;
}

.mb-4 {
  margin-bottom: 4px !important;
}

.mb-8 {
  margin-bottom: 8px !important;
}

.pt-3 {
  padding-top: 3px !important;
}

.pt-12 {
  padding-top: 12px !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.pl-14 {
  padding-left: 14px !important;
}

.pl-18 {
  padding-left: 18px !important;
}

/* 宽度 */
.w100p-90 {
  width: calc(100% - 90px) !important;
}

.w100p-100 {
  width: calc(100% - 100px) !important;
}

.w-36 {
  width: 36px !important;
}

.w-60 {
  width: 60px !important;
}

.w-70 {
  width: 70px !important;
}

.w-72 {
  width: 72px !important;
}

.w-80 {
  width: 80px !important;
}

.w-98 {
  width: 98px !important;
}

.w-110 {
  width: 110px !important;
}

.w-116 {
  width: 116px !important;
}

.w-328 {
  width: 328px !important;
}

.w-206 {
  width: 206px !important;
}

.w-342 {
  width: 342px !important;
}

.w-240 {
  width: 240px !important;
}

.w-504 {
  width: 504px !important;
}

.w-796 {
  width: 796px !important;
}

.w-838 {
  width: 838px !important;
}

/* 高度 */
.h-half {
  height: 50%;
}