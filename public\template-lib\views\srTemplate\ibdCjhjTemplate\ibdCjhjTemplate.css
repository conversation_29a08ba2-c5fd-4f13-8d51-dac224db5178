.singleDisEditReport.main-page {
  min-width: unset;
}

#ibdCjhj1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  /* background: #F5F7FA; */
  overflow: auto;
}

#ibdCjhj1 * {
  font-family: '宋体';
}

.text-blod {
  font-weight: bold;
}

/*--------宽度类---------*/
.wd-64 {
  width: 64px;
}

.wd-72 {
  width: 72px;
}

.wd-85 {
  width: 85px;
}

.wd-92 {
  width: 92px;
}

.wd-104 {
  width: 104px;
}

.wd-120 {
  width: 120px;
}

.wd-152 {
  width: 152px;
}

.wd-168 {
  width: 168px;
}

.wd-200 {
  width: 200px;
}

.wd-232 {
  width: 232px;
}

/*--------间距类---------*/
.mt-4{
  margin-top: 4px;
}
.mr-4{
  margin-right: 4px;
}
.ml-4 {
  margin-left: 4px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mr-58 {
  margin-right: 58px;
}

.pl-72 {
  padding-left: 72px;
}

.p-item+.p-item {
  margin-top: 4px;
}

.row-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.w-con {
  vertical-align: middle
}

.sel-bor {
  border-radius: 3px;
  border: 1px solid #DCDFE6;
}

.cjhj-content {
  padding: 8px 24px;
  margin-bottom: 8px;
  background: #F5F7FA;
}

.flex-con {
  display: flex;
}

.flex-sty {
  flex: 1;
}

.label-sty {
  display: inline-block;
  text-align: right;
  vertical-align: sub;
}

.row-box {
  position: relative;
  background: #F5F7FA;
  /* border: 1px solid #C8D7E6; */
}

.area-sty {
  height: 80px;
  resize: none;
}

.box-line::after {
  width: 1px;
  height: 107px;
  content: '';
  background: #C8D7E6;
  position: absolute;
  left: 82px;
  top: 0;
}

.cjhj-edit {
  position: relative;
}

.organ-sel, .maxdiam-sel {
  height: 28px;
  padding: 0 16px;
}

.show-or-hide {
  position: relative;
  background: #fff;
}

.show-or-hide input {
  padding: 0 16px;
  position: relative;
  z-index: 10;
  background: transparent;
}

.show-or-hide:hover {
  opacity: .8;
}

.show-or-hide::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}

.show-or-hide.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}

.show-or-hide.hide-more:after {
  transform: rotate(-45deg);
  margin-top: -2px;
}

.laySelLab li {
  height: 26px;
}

#ibdCjhj1 .cjhj-view {
  display: none;
}

[isview="true"] #ibdCjhj1 .cjhj-edit {
  display: none;
}

[isview="true"] #ibdCjhj1 .cjhj-view {
  display: block;
}

#ibdCjhj1 .cjhj-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  font-size: 16px;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 130px;
  color: #000;
}

#ibdCjhj1 .cjhj-view .view-head {
  width: 100%;
  border-bottom: 1px solid #999;
  padding-bottom: 24px;
  text-align: center;
}

#ibdCjhj1 .cjhj-view .head-img {
  margin: 0 auto 10px;
  width: 308px;
  height: 40px;
}

#ibdCjhj1 .cjhj-view .head-img img {
  width: 100%;
  height: 100%;
}

#ibdCjhj1 .cjhj-view .right-num {
  display: flex;
  position: absolute;
  top: 110px;
  right: 56px;
  color: #41898A;
  line-height: 24px;
}

#ibdCjhj1 .cjhj-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}

#ibdCjhj1 .cjhj-view .black-txt {
  color: #000;
  font-size: 16px;
  line-height: 24px;
}

#ibdCjhj1 .cjhj-view .page-tit {
  font-size: 24px;
  font-weight: 800;
}

#ibdCjhj1 .cjhj-view .page-tit.sub-tit {
  font-size: 28px;
}

#ibdCjhj1 .cjhj-view .gray-txt {
  color: #000;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 24px;
}

#ibdCjhj1 .cjhj-view .info-i {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

#ibdCjhj1 .cjhj-view .info-i+.info-i {
  margin-left: 8px;
}

#ibdCjhj1 .cjhj-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

#ibdCjhj1 .cjhj-view .view-patient .p-item {
  align-items: center;
}

#ibdCjhj1 .cjhj-view .rpt-img-ls {
  display: none;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 0;
  justify-content: center;
}

#ibdCjhj1 .cjhj-view .item-img {
  /* display: inline-block; */
  width: 214px;
  height: 160px;
  box-sizing: border-box;
  border: 1px solid #eee;
  margin-right: 12px;
  margin-top: 12px;
}

#ibdCjhj1 .cjhj-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

#ibdCjhj1 .cjhj-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 30px;
  right: 30px;
}

#ibdCjhj1 .cjhj-view .report-wrap {
  padding: 8px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}

#ibdCjhj1 .cjhj-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
#ibdCjhj1 .cjhj-view .reporter-i.flex-1 {
  flex: 1.5;
}

#ibdCjhj1 .cjhj-view .reporter-i > span:first-child {
  width: 80px;
}
#ibdCjhj1 .cjhj-view .reporter-i > span:last-child {
  flex: 1;
}

#ibdCjhj1 .cjhj-view .reporter-i img {
  width: 64px;
  height: 32px;
  object-fit: contain;
}

#ibdCjhj1 .cjhj-view .reporter-i+.reporter-i {
  margin-left: 8px;
}