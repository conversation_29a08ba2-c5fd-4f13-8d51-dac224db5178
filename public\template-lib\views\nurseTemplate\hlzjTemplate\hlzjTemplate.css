#hlzj1 {
  font-size: 14px;
}
#hlzj1 .hlzj-content{
  width: 100%;
  border: 1px solid #DCDFE6;
  border-top: 0;
}
#hlzj1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#hlzj1 .add-btn{
  font-size: 14px;
  color: #1885F2;
  line-height: 22px;
  font-weight: 400;
  margin-right: 16px;
  cursor: pointer;
}
#hlzj1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}
#hlzj1 .rt-sr-lb {
  margin-left: 4px;
}
#hlzj1 .input-width{
  width: 124px;
}
#hlzj1 .width-180{
  width: 186px;
}
#hlzj1 .item-content{
  padding: 12px 16px;
}
#hlzj1 .form-content{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlzj1 .item-content .gray-item{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
#hlzj1 .item-content .form-item{
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 12px;
}
#hlzj1 .layui-form{
  display: flex;
  align-items: center;
}
#hlzj1 .form-box{
  border: 1px solid #eee;
}
#hlzj1 .form-box .input-width{
  border: 0;
  width: 40px;
}
#hlzj1 .unit-content{
  width: 61px;
  line-height: 38px;
  text-align: center;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #eee;
  border-left: 0;
}
#hlzj1 .form-item-title{
  color: #606266;
  min-width: 40px;
  text-align: right;
}
#hlzj1 .width-84{
  min-width: 84px;
}
#hlzj1 .drug-content{
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  margin-top: 8px;
}
#hlzj1 .drug-content .drug-title{
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: rgba(255,255,255,0);
  box-shadow: inset 0px -1px 0px 0px #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#hlzj1 .item-content .showInt {
  position: relative;
  background: #fff;
  width: 186px;
}
#hlzj1 .item-content .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 20px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
[isview="true"] #hlzj1 .item-content .showInt {
  position: relative;
  background: #FAFAFA;
  /* width: 186px; */
}
[isview="true"] #hlzj1 .item-content .showInt::after{
  display: none;
}
[isview="true"] #hlzj1 .form-box{
  border: 0px solid #eee;
}