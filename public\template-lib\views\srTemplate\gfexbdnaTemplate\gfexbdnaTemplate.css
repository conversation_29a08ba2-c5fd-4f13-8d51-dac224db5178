/* 宽度 */
.wd-80 {
  width: 80px!important;
}
.wd-128 {
  width: 128px!important;
}
.wd-160 {
  width: 160px!important;
}
.wd-236 {
  width: 236px!important;
}
.wd-320 {
  width: 320px!important;
}
/* 间距 */
.ml-4 {
  margin-left: 4px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-20 {
  margin-left: 20px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-29 {
  margin-top: 29px;
}
/* 字体 */
.fs-12 {
  font-size: 12px;
}
.wacl{
  width: 100%;
  margin-bottom: 4px;
}

#gfexbdna1 {
  width: 100%;
  min-height: 100%;
  font-family: "宋体";
  font-size: 16px;
  color: #000;
  background: #F5F7FA;
  overflow: auto;
}
#gfexbdna1 * {
  font-family: "宋体";
}
#gfexbdna1 .pattern-type {
  padding: 8px 24px;
  border-bottom: 1px solid #C0C4CC;
}
#gfexbdna1 input[type="text"], #gfexbdna1 .inp-sty {
  height: 28px;
  background: #FFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 15px 0  11px;
  font-size: 16px;
}
#gfexbdna1 input[type="radio"],#gfexbdna1 input[type="checkbox"] {
  vertical-align: middle;
}
#gfexbdna1 .item-box {
  padding: 8px 24px;
}
#gfexbdna1 .row-item {
  display: flex;
  margin-bottom: 5px;
}
#gfexbdna1 .con-flex {
  display: flex;
}
#gfexbdna1 .flex-item {
  flex: 1;
}
#gfexbdna1 .item-tit {
  font-weight: bold;
  margin-bottom: 8px;
}
#gfexbdna1 .item-lb {
  display: inline-block;
  width: 96px;
  text-align: right;
}
#gfexbdna1 .bor-b {
  border-bottom: 1px solid #C0C4CC;
}
#gfexbdna1 .gfexbdna-edit .showInt {
  position: relative;
  background: #fff;
  height: 28px;
}
#gfexbdna1 .gfexbdna-edit .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
/* 细胞DNA倍体检测 */
#gfexbdna1 .gfexbdna-edit .item-lb {
  min-width: 128px!important;
}
.addImg {
  position: relative;
  min-width: 72px;
  height: 72px;
  line-height: 72px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  transition: transform 0.2s;
  font-size: 20px;
  cursor: pointer;
  text-align: center;
}
#gfexbdna1 #imageInput {
  height: 72px;
  width: 72px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  font-size: 0;
  opacity: 0;
  cursor: pointer;
}
#gfexbdna1 .preview-img {
  display: flex;
  flex-wrap: wrap;
}
#gfexbdna1 .preview {
  width: 72px;
  margin-right: 8px;
  margin-bottom: 4px;
  position: relative;
}
#gfexbdna1 .preview img {
  width: 72px;
  height: 72px;
  border-radius: 6px;
  border: 1px solid #C0C4CC;
  position: relative;
}
#gfexbdna1 .preview .delImg {
  width: 18px;
  height: 18px;
  position: absolute;
  top: 4px;
  right: 4px;
  color: #fff;
  font-size: 10px;
  line-height: 14px;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  background: #E64545;
  border-radius: 50%;
  z-index: 1;
}
/* 预览 */
[isview="true"] #gfexbdna1 .gfexbdna-edit {
  display: none;
}
[isview="true"] #gfexbdna1 .gfexbdna-view {
  display: flex;
}
#gfexbdna1 .gfexbdna-view {
  display: none;
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 134px 56px;
  flex-direction: column;
  position: relative;
}
#gfexbdna1 .gfexbdna-view .view-head {
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #999;
  padding-bottom: 9px;
  text-align: center;
}
#gfexbdna1 .gfexbdna-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
}
#gfexbdna1 .gfexbdna-view .logo-tit img {
  width: 68px;
  height: 68px;
  margin-right: 20px;
}
#gfexbdna1 .gfexbdna-view .blh-tit {
  display: flex;
  align-items: end;
  justify-content: flex-end;
  line-height: 22px;
  margin-left: 6px;
}
#gfexbdna1 .gfexbdna-view .hos-tit{
  font-size: 24px;
  color: #000;
  line-height: 32px;
  font-weight: bold;
}
#gfexbdna1 .gfexbdna-view .eng-tit{
  font-size: 14px;
  line-height: 20px;
}
#gfexbdna1 .gfexbdna-view .sub-tit{
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
}
#gfexbdna1 .gfexbdna-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#gfexbdna1 .gfexbdna-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
}
#gfexbdna1 .gfexbdna-view .black-txt {
  color: #000;
  font-size: 16px;
}
#gfexbdna1 .gfexbdna-view .info-i {
  width: 210px;
  display: flex;
  flex-wrap: wrap;
}
#gfexbdna1 .gfexbdna-view .info-i + .info-i {
  margin-left: 8px;
}
#gfexbdna1 .gfexbdna-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#gfexbdna1 .gfexbdna-view .view-patient .p-item {
  margin-top: 8px;
}
#gfexbdna1 .gfexbdna-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#gfexbdna1 .gfexbdna-view .desc-con {
  display: flex;
  align-items: baseline;
  padding-top: 8px;
}
#gfexbdna1 .gfexbdna-view .desc-tit {
  width: 96px;
  text-align: right;
}
#gfexbdna1 .gfexbdna-view .yz-tip {
  margin-left: 96px;
  margin-bottom: 20px;
}
#gfexbdna1 .gfexbdna-view .bold {
  font-weight: bold;
}
#gfexbdna1 .gfexbdna-view .rpt-img-ls {
  display: none;
}
#gfexbdna1 .gfexbdna-view .item-img {
  width: 220px;
  height: 165px;
  border: 1px solid #eee;
  margin-top: 12px;
}
#gfexbdna1 .gfexbdna-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#gfexbdna1 .gfexbdna-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 27px;
  right: 27px;
}
#gfexbdna1 .gfexbdna-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#gfexbdna1 .gfexbdna-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#gfexbdna1 .gfexbdna-view .tip-wrap {
  margin-top: 8px;
  font-size: 12px;
}
#gfexbdna1 .gfexbdna-view .flex-column {
  margin: 8px auto;
  width: 520px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: 10px;
}
#gfexbdna1 .gfexbdna-view .preview-img-ls {
  display: none;
  flex-wrap: wrap;
  padding: 8px 0 0 54px;
}
#gfexbdna1 .gfexbdna-view .preview-img-ls .preview-item-img {
  width: 267px;
  height: 200px;
  margin-right: 25px;
  margin-bottom: 20px;
}
#gfexbdna1 .gfexbdna-view .preview-img-ls .preview-item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#gfexbdna1 .gfexbdna-view .report-img {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0!important;
  margin-top: 0!important;
}
#gfexbdna1 .gfexbdna-view .report-img img {
  height: 205px;
  width: 202px;
  display: block;
  object-fit: contain;
}
/* 细胞学分析下拉框多选 */
#gfexbdna1 .multi-w {
  position: relative;
  flex: 1;
  height: 30px;
  border: 1px solid #DCDFE6;
  padding: 0 18px 0 8px;
  border-radius: 3px;
  background: #fff;
}
#gfexbdna1 .multi-w + input {
  display: none;
}
#gfexbdna1 .multi-w::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#gfexbdna1 xm-select {
  height: 100%;
  min-height: unset;
  line-height: 26px;
  border: none;
}
#gfexbdna1 xm-select .xm-icon,
#gfexbdna1 xm-select .xm-label-block {
  display: none;
}
#gfexbdna1 xm-select .xm-label {
  right: 0;
}
#gfexbdna1 xm-select .label-content {
  padding: 0;
  line-height: 26px;
  font-size: 16px;
}