@charset "UTF-8";
#hbettctbg1 {
  font-size: 16px;
  color: #000;
  background-color: #F5F7FA;
  min-height: 100%;
}
#hbettctbg1 * {
  font-family: "宋体";
}
#hbettctbg1 .edit {
  display: flex;
  flex-direction: column;
  row-gap: 4px;
  padding: 12px 24px;
  padding-left: 8px;
}
#hbettctbg1 .edit .specimen-container .specimen {
  padding-top: 5px;
  padding-left: 12px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  color: #1885F2;
  font-weight: 600;
}
#hbettctbg1 .preview {
  display: none;
  flex-direction: column;
  width: 780px;
  min-height: 1100px;
  background-color: #fff;
  margin: 0 auto;
  padding: 40px 56px;
  padding-top: 25px;
  word-break: break-all;
}
#hbettctbg1 .preview .preview-header .header-title .code {
  width: 122px;
  height: 36px;
}
#hbettctbg1 .preview .preview-header .header-title .code img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
#hbettctbg1 .preview .preview-content .report-img {
  display: flex;
  justify-content: space-between;
}
#hbettctbg1 .preview .preview-content .report-img img {
  width: 100%;
  height: 210px;
  object-fit: cover;
}
#hbettctbg1 .preview .preview-footer .audit {
  height: 48px;
}
#hbettctbg1 .preview .preview-footer .audit img {
  width: 64px;
  height: 32px;
  object-fit: cover;
}
#hbettctbg1 .border-t {
  border-top: 2px solid #000;
}
#hbettctbg1 .border-b {
  border-bottom: 2px solid #000;
}
#hbettctbg1 .rt-textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  flex: 1;
  padding: 4px;
  resize: both;
}
#hbettctbg1 .rt-textarea::placeholder {
  color: #000;
}
#hbettctbg1 .rt-sr-label {
  margin-right: 8px;
}
#hbettctbg1 .custom-select {
  position: relative;
}
#hbettctbg1 .custom-select::after {
  display: inline-block;
  position: absolute;
  top: 50%;
  right: 4px;
  content: "";
  width: 8px;
  height: 8px;
  border-left: 1px solid #999;
  border-bottom: 1px solid #999;
  transform: rotate(-45deg) translateY(-100%);
}
#hbettctbg1 .custom-select .layui-input {
  height: 28px;
}
#hbettctbg1 .rt-sr-r {
  margin-right: 4px;
}
#hbettctbg1 .pl-24 {
  padding-left: 24px;
}
#hbettctbg1 .pr-24 {
  padding-right: 24px;
}
#hbettctbg1 .pt-8 {
  padding-top: 8px;
}
#hbettctbg1 .pb-8 {
  padding-bottom: 8px;
}
#hbettctbg1 .mt-12 {
  margin-top: 12px;
}
#hbettctbg1 .mt-8 {
  margin-top: 8px;
}
#hbettctbg1 .mt-6 {
  margin-top: 6px;
}
#hbettctbg1 .mt-4 {
  margin-top: 4px;
}
#hbettctbg1 .m-0-6 {
  margin: 0 6px;
}
#hbettctbg1 .w-10 {
  width: 10px;
}
#hbettctbg1 .w-15 {
  width: 15px;
}
#hbettctbg1 .w-20 {
  width: 20px;
}
#hbettctbg1 .w-25 {
  width: 25px;
}
#hbettctbg1 .w-30 {
  width: 30px;
}
#hbettctbg1 .w-35 {
  width: 35px;
}
#hbettctbg1 .w-40 {
  width: 40px;
}
#hbettctbg1 .w-45 {
  width: 45px;
}
#hbettctbg1 .w-50 {
  width: 50px;
}
#hbettctbg1 .w-55 {
  width: 55px;
}
#hbettctbg1 .w-60 {
  width: 60px;
}
#hbettctbg1 .w-65 {
  width: 65px;
}
#hbettctbg1 .w-70 {
  width: 70px;
}
#hbettctbg1 .w-75 {
  width: 75px;
}
#hbettctbg1 .w-80 {
  width: 80px;
}
#hbettctbg1 .w-85 {
  width: 85px;
}
#hbettctbg1 .w-90 {
  width: 90px;
}
#hbettctbg1 .w-95 {
  width: 95px;
}
#hbettctbg1 .w-100 {
  width: 100px;
}
#hbettctbg1 .w-105 {
  width: 106px;
}
#hbettctbg1 .w-110 {
  width: 110px;
}
#hbettctbg1 .w-115 {
  width: 115px;
}
#hbettctbg1 .w-120 {
  width: 120px;
}
#hbettctbg1 .w-125 {
  width: 125px;
}
#hbettctbg1 .w-130 {
  width: 130px;
}
#hbettctbg1 .w-135 {
  width: 135px;
}
#hbettctbg1 .w-140 {
  width: 140px;
}
#hbettctbg1 .w-145 {
  width: 145px;
}
#hbettctbg1 .w-150 {
  width: 150px;
}
#hbettctbg1 .w-155 {
  width: 155px;
}
#hbettctbg1 .w-160 {
  width: 160px;
}
#hbettctbg1 .w-165 {
  width: 165px;
}
#hbettctbg1 .f-1 {
  flex: 1;
}
#hbettctbg1 .f-2 {
  flex: 2;
}
#hbettctbg1 .f-3 {
  flex: 3;
}
#hbettctbg1 .fw-600 {
  font-weight: 600;
}
#hbettctbg1 .a-center {
  display: flex;
  align-items: center;
}
#hbettctbg1 .a-start {
  display: flex;
  align-items: flex-start;
}
#hbettctbg1 .flex {
  display: flex;
}
#hbettctbg1 .flex-column {
  display: flex;
  flex-direction: column;
}
#hbettctbg1 .text-r {
  text-align: right;
}
#hbettctbg1 .fs-36 {
  font-size: 36px;
}
#hbettctbg1 .fs-24 {
  font-size: 24px;
}
#hbettctbg1 .fs-20 {
  font-size: 20px;
}
#hbettctbg1 .fs-21 {
  font-size: 21px;
}

[isView=true] #hbettctbg1 .edit {
  display: none;
}

[isView=true] #hbettctbg1 .preview {
  display: flex;
}
