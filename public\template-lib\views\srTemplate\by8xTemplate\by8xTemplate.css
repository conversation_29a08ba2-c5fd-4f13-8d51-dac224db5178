#by8x1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
#by8x1 * {
  font-family: '宋体';
}
.box-p{
  padding: 8px 20px;
}
.title{
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 8px;
}
.cent{
  display: flex;
  flex-wrap: wrap;
}
.init-l{
  width: calc(100% - 304px);
}
.init-c{
  width: 140px;
  margin-left: 12px;
}
.init-r{
  width: 140px;
  margin-left: 12px;
}
.in-sly{
  width: 100%;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
  font-size: 16px;
  margin-bottom: 8px;
}
.mt-8{
  margin-top: 8px;
}
.slt{
  line-height: 28px;
  margin-bottom: 8px;
}
#by8x1 .by8x-view {
  display: none;
}
[isview="true"] #by8x1 .by8x-edit {
  display: none;
}
[isview="true"] #by8x1 .by8x-view {
  display: block;
}
/* 预览部分 */
#by8x1 .by8x-view {
  width: 780px;
  position: relative;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 20px;
}
#by8x1 .by8x-view .view-head {
  border-bottom: 1px solid #999;
  text-align: center;
  position: relative;
}
#by8x1 .by8x-view .head-img {
  position: absolute;
  top: 30px;
  left: 110px;
  width: 64px;
  height: 64px;
}
#by8x1 .by8x-view .hos-tit {
  font-family: 仿宋;
  font-size: 21px;
  line-height: 1;
}
#by8x1 .by8x-view .right-num {
  text-align: right;
  margin: 8px 0;
  font-weight: bold;
}
#by8x1 .by8x-view .right-num .black-txt {
  display: inline-block;
  max-width: 148px;
}
#by8x1 .by8x-view .sub-tit {
  font-family: '宋体';
  font-size: 26px;
  margin-top: 20px;
}
#by8x1 .by8x-view .view-head .code {
  position: absolute;
  right: 0;
  display: flex;
  flex-direction: column;
  width: 194px;
  align-items: flex-end;
  top: 5px;
}
#by8x1 .by8x-view .view-head .code img {
  width: 122px;
  height: 36px;
}
#by8x1 .by8x-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  /* margin-bottom: 8px; */
}
#by8x1 .by8x-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}
#by8x1 .by8x-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}
#by8x1 .by8x-view .info-i + .info-i {
  margin-left: 8px;
}
#by8x1 .by8x-view .info {
  flex: 1;
  white-space: pre-line;
  word-break: break-all;
}
#by8x1 .by8x-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-line;
  word-break: break-all;
  width: 80px;
  text-align: right;
}
#by8x1 .by8x-view .bent{
  display: flex;
  padding-bottom: 8px;
}
#by8x1 .by8x-view .txt{
  flex: 1;
}
.fw-b{
  font-weight: bold;
}
.tc{
  text-align: center;
}
.tc-pr{
  text-align: right;padding-right: 36px;
}
#by8x1 .by8x-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 56px;
  right: 56px;
}
#by8x1 .by8x-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}
#by8x1 .by8x-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#by8x1 .by8x-view .reporter-i img {
  width: 90px;
  height: 40px;
  object-fit: contain;
}
#by8x1 .by8x-view .reporter-i span:last-child {
  flex: 1;
}
#by8x1 .by8x-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#by8x1 .by8x-view .tip-wrap {
  font-size: 12px;
  margin-top: 8px;
}
