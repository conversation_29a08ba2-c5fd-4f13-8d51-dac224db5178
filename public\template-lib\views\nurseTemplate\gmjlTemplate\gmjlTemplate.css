#gmjl1 {
  font-size: 14px;
}
#gmjl1 {
  font-size: 16px;
}
#gmjl1 .ct-title {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  padding-left: 16px;
  margin-bottom: 4px;
  height: 36px;
  line-height: 36px;
  background: #ECF5FF;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
}
#gmjl1 .second-title {
  color: #E68A2E;
  margin-left: 12px;
}

#gmjl1 .mt-20 {
  margin-top: 20px;
}
#gmjl1 .allergy-content .hydration-item {
  margin-left: 16px;
  width: calc(100% - 32px);
  height: 60px;
  background: #FAFAFA;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 16px;
  margin-top: 8px;
}
#gmjl1 .allergy-content{
  flex: 1;
}
#gmjl1 .allergy-content .hydration-item .item-left {
  display: flex;
  align-items: center;
}
#gmjl1 .thirth-content {
  width: 100%;
  background: #F0F2F5;
  border-radius: 6px;
  margin-bottom: 12px;
  margin-top: 6px;
  padding: 12px 0;
}

#gmjl1 .sign-content {
  display: flex;
  align-items: center;
  margin-left: 16px;
  width: 100%;
}
#gmjl1 .physical-content {
  display: flex;
  align-items: start;
  margin-top: 12px;
}
#gmjl1 .physical-content .form-title{
  min-width: 80px;
}
#gmjl1 .physical-content .form-content{
  background: #FAFAFA;
  border-radius: 6px;
  border: 1px solid #EBEEF5;
  padding-top: 12px;
  
  flex: 1;
}
#gmjl1 .sign-content .layui-input {
  flex: 1;
}
#gmjl1 .weight {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

#gmjl1 .sign-content .temperature {
  display: flex;
  align-items: center;
  min-width: 160px;
}
#gmjl1 .sign-content .unit {
  width: 58px;
  height: 38px;
  background: #F5F7FA;
  border-radius: 0px 3px 3px 0px;
  border: 1px solid #DCDFE6;
  border-left: 0;
  line-height: 38px;
  text-align: center;
  color: #606266;
}

#gmjl1 .allergy-content .allergy-item {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

#gmjl1 .radio-label {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

#gmjl1 .rt-sr-lb {
  margin-left: 4px;
}
#gmjl1 .form-title {
  width: 80px;
  text-align: right;
}

#gmjl1 .gray-item {
  margin-left: 12px;
  display: flex;
  align-items: center;
  flex: 1;
}

#gmjl1 .layui-inline {
  display: block;
  width: calc(100% - 32px);
  min-width: 170px;
}

#gmjl1 .layui-inline input {
  position: relative;
  background: #fff;
  z-index: 10;
  padding: 0 16px;
  width: 100%;
}

#gmjl1 .textarea-content{
  flex: 1;
}
#gmjl1 .template-content{
  width: 320px;
  background: #fafafa;
  border-top: 1px solid #eee;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
  height: 270px;
}

#gmjl1 .template-content .template-title{
  width: 100%;
  height: 36px;
  background: rgba(255,255,255,0);
  box-shadow: inset 0px -1px 0px 0px #eee;
  line-height: 36px;
  padding-left: 12px;
}
#gmjl1 .template-content .template-item{
  width: 100%;
  height: 36px;
  line-height: 36px;
  padding-left: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-bottom: 1px solid #C0C4CC;
}