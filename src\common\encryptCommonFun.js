import * as constant from'@/config/constant.js';

export const getOrSetCurReportLocal = (param) => {
  let {vm, type, data, key} = param;
  let localData = JSON.parse(window.localStorage.getItem(constant.SR_QUERY_PARAMS) || '{}');
  if(type === 'get') {
    return localData[vm.localKey] || {};
  }
  if(type === 'set') {
    localData[key || vm.localKey] = data;
    window.localStorage.setItem(constant.SR_QUERY_PARAMS, JSON.stringify(localData));
  }
}
// 移除缓存的结构化数据
export const removeCurReportLocal = (param) => {
  let {key} = param;
  let localData = JSON.parse(window.localStorage.getItem(constant.SR_QUERY_PARAMS) || '{}');
  delete localData[key];
  window.localStorage.setItem(constant.SR_QUERY_PARAMS, JSON.stringify(localData));
}

export const initLocalParams = (vm) => {
  let {frontendParam = ''} = vm.$route.query || {};
  vm.localKey = frontendParam;
  if(frontendParam && vm.isEncryptFun(frontendParam)) {
    vm.localKey = vm.decryptFun(frontendParam);
  }
  // 从缓存里读取报告相关参数数据
  if(process.env.NODE_ENV === 'development') {
    vm.localQueryParams = {...vm.$route.query, ...getOrSetCurReportLocal({vm, type: 'get'})};
  } else {
    // 正式环境不读取地址栏的个别参数
    let temp = {
      busType: vm.$route.query.busType,
      busId: vm.$route.query.busId,
      viewId: vm.$route.query.viewId,
      patDocId: vm.$route.query.patDocId,
      patternId: vm.$route.query.patternId,
      child: vm.$route.query.child,
      trace: vm.$route.query.trace,
      print: vm.$route.query.print,
      preview: vm.$route.query.preview,
      printView: vm.$route.query.printView,  //1为打印预览
      fromYunPacs: vm.$route.query.fromYunPacs,
      pv: vm.$route.query.pv,
      sysCode: vm.$route.query.sysCode,
      webpacs: vm.$route.query.webpacs,
      outParam: vm.$route.query.outParam,
      type: vm.$route.query.type,
      changePattern: vm.$route.query.changePattern,
      newOne: vm.$route.query.newOne,
      entryType: vm.$route.query.entryType,
      winName: vm.$route.query.winName,
      signPageMode: vm.$route.query.signPageMode,
    }
    vm.localQueryParams = {...temp, ...getOrSetCurReportLocal({vm, type: 'get'})};
  }
}
