$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: null,  //转成pdf的区域，默认是整个页面
    }
  }

  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // 补充头部个人信息
    $(".blood-head [data-tit]").each(function (t, tEle) {
      var tKey = $(tEle).attr('data-tit');
      $(tEle).text(getParamByName(tKey));
    })
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {
      initEdit();
    }
  }
  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
    var id = $(e.target).attr('id');
    var score = $(e.target).attr('data-score');
    if ((id === 'kyxjcy-rt-55' || id === 'kyxjcy-rt-62') && _is_checked_) {
      $('#kyxjcy-rt-85').prop('checked', true);
      $('#kyxjcy-rt-84').prop('checked', true);
    }
    if (!_is_checked_ && score) {
      calculateScores()
    }
  });
}
function calculateScores() {
  var scoreDataList = [];
  var jcyScore;
  $("#kyxjcy1 [data-score]:checked").each(function (i, dom) {
    var score = $(dom).attr('data-score');
    var name = $(dom).attr('refer');
    scoreDataList.push({
      zbName: name,
      zbScore: score
    })
  })
  if (scoreDataList && scoreDataList.length > 0) {
    // 使用reduce来构建每个zbName对应的最高zbScore的对象
    var maxScoreObjects = scoreDataList.reduce((acc, obj) => {
      var name = obj.zbName;
      var score = Number(obj.zbScore); // 将zbScore转换为数字
      // 如果累加器中没有当前zbName，则添加它
      if (!acc[name]) {
        acc[name] = { ...obj, zbScore: score };
      } else {
        // 否则，比较当前对象的zbScore和累加器中对象的zbScore
        var currentMaxScore = Number(acc[name].zbScore);
        if (score > currentMaxScore) {
          // 如果当前对象的zbScore更高，则更新累加器中的对象
          acc[name] = { ...obj, zbScore: score };
        }
      }
      return acc;
    }, {});
    // 将累加器的值转换为数组  
    var newScoreDataList = Object.values(maxScoreObjects);
    jcyScore = newScoreDataList.reduce((sum, item) => {
      return sum + item.zbScore
    }, 0)
  } else {
    jcyScore = '';
  }
  $('#kyxjcy-rt-87').val(jcyScore);
}

function initEdit() {
  // $("#kyxjcy1 .ck-r").change(() => {
  //   var name = $(this).attr('name');
  //   var id = $(this).attr('id');
  //   if ($(this).is(":checked")) {
  //     $('[name="' + name + '"]:not([id="' + id + '"])').prop('checked', false);
  //   }
  // })
  calculateScores();
  $('#kyxjcy1 input[type="radio"][data-score], #kyxjcy1 input[type="checkbox"][data-score]').on('change', () => {
    calculateScores();
  })
  // $("#kyxjcy1 [data-score]:checked").change(() => {
  //   console.log('>>>>1111');
  //   calculateScores();
  // })
  document.getElementById('kyxjcy-rt-87').readOnly = true;
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);

  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '', sdbwStr = '', hcmdnmStr = '', hmbStr = '', lwStr = '', bwStr = '', wzStr = '', kjStr = '';
  let strList = [], jjqkList = [], sdbwList = [], hcmdnmList = [], hmbList = [], lwList = [], bwList = [], nmList = [], kjList = [];
  let hasChild = false;
  $('#kyxjcy1 .rt-sr-w:not([pid])').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length > 0) {
        if (curPid.id === "kyxjcy-rt-1") {
          curKeyValData["kyxjcy-rt-2"] ? jjqkList.push(curKeyValData["kyxjcy-rt-3"].val + curKeyValData["kyxjcy-rt-3"].child[0].val) : '';
          curKeyValData["kyxjcy-rt-6"] ? jjqkList.push(curKeyValData["kyxjcy-rt-6"].name + curKeyValData["kyxjcy-rt-6"].child[0].val) : '';
          jjqkList && jjqkList.length > 0 ? strList.push(jjqkList.join('，')) : ''
        }
        if (curPid.id === "kyxjcy-rt-9") {
          for (let i = 0; i < child.length; i++) {
            let son = child[i].child;
            if (child[i].id === 'kyxjcy-rt-12') {
              son && son.length > 0 ? sdbwList.push('约' + son[0].val + 'cm') : ""
            } else {
              sdbwList.push(child[i].val)
            }
          }
          sdbwList && sdbwList.length > 0 ? sdbwStr = '插镜至' + sdbwList.join('') : ''
        }
        if (curPid.id === "kyxjcy-rt-14") {
          curKeyValData["kyxjcy-rt-16"] ? str = curKeyValData["kyxjcy-rt-15"].name + curKeyValData["kyxjcy-rt-16"].val : ''
          if (curKeyValData["kyxjcy-rt-17"]) {
            let son = curKeyValData["kyxjcy-rt-17"].child;
            if (son && son.length) {
              for (let j = 0; j < son.length; j++) {
                hcmdnmList.push(son[j].id === "kyxjcy-rt-18" ? curKeyValData["kyxjcy-rt-17"].val + son[j].val + son[j].name : son[j].val);
              }
            }
          }
          hcmdnmList && hcmdnmList.length > 0 ? hcmdnmStr = '所见' + curPid.name + (str ? str + '，' : '') + hcmdnmList.join('') : (str ? hcmdnmStr = '所见' + curPid.name + str : '')
        }
        str = '';
        if (curPid.id === "kyxjcy-rt-21") {
          for (let k = 0; k < child.length; k++) {
            if (child[k].child && child[k].child.length > 0) {
              let son = child[k].child[0];
              hmbList.push((child[k].name === '形态' ? '' : '所见') + child[k].name + son.val)
            }
          }
          hmbList && hmbList.length > 0 ? hmbStr = curPid.name + hmbList.join('，') : ''
        }
        if (curPid.id === "kyxjcy-rt-26") {
          for (let l = 0; l < child.length; l++) {
            if (child[l].id === "kyxjcy-rt-30") {
              let son = child[l].child;
              for (let n = 0; n < son.length; n++) {
                if (son[n].child && son[n].child.length > 0) {
                  let sChild = son[n].child;
                  for (let s = 0; s < sChild.length; s++) {
                    if (sChild[s].id === "kyxjcy-rt-36") {
                      str = sChild[s].val + sChild[s].name
                    } else {
                      kjList.push(sChild[s].val)
                    }
                  }
                  kjStr = son[n].val + (str ? str : '') + kjList.join('、');
                  str = '';
                } else {
                  nmList.push(son[n].val)
                }
              }
              nmList && nmList.length > 0 ? lwList.push(child[l].name + nmList.join('、') + (kjStr ? '，' + kjStr : '')) : ''
              kjStr = '';
            } else {
              lwList.push(child[l].name + child[l].child[0].val)
            }
          }
          lwList && lwList.length > 0 ? lwStr = curPid.name + lwList.join('，') : ''
          kjList = [];
          nmList = [];
        }
        if (curPid.id === "kyxjcy-rt-51") {
          for (let i = 0; i < child.length; i++) {
            if (child[i].id === "kyxjcy-rt-52") {
              child[i].child && child[i].child.length > 0 ?
                bwList.push(child[i].name + child[i]?.child[0].val + child[i].child[0].name) : ''
            } else if (child[i].id === "kyxjcy-rt-54") {
              let wChild = child[i].child;
              for (let c = 0; c < wChild.length; c++) {
                if (wChild[c].child && wChild[c].child.length > 0) {
                  wzStr += wChild[c].child.map((item) => item.val).join('、')
                } else {
                  wzStr += wChild[c].val + (wChild.length > 1 ? '、' : '');
                }
              }
            } else if (child[i].id === "kyxjcy-rt-63") {
              let son = child[i].child;
              for (let n = 0; n < son.length; n++) {
                if (son[n].child && son[n].child.length > 0) {
                  let sChild = son[n].child;
                  for (let s = 0; s < sChild.length; s++) {
                    if (sChild[s].id === "kyxjcy-rt-69") {
                      str = sChild[s].val + sChild[s].name
                    } else {
                      kjList.push(sChild[s].val)
                    }
                  }
                  kjStr = son[n].val + (str ? str : '') + kjList.join('、');
                  str = '';
                } else {
                  nmList.push(son[n].val)
                }
              }
              nmList && nmList.length > 0 ? bwList.push(child[i].name + (nmList[0].includes('可见') ? nmList.join('、') : '表面见' + nmList.join('、')) + (kjStr ? '，' + kjStr : '')) : ''
              kjStr = '';
            } else if (child[i].id === "kyxjcy-rt-84") {
              let yChild = child[i].child;
              yChild && yChild.length > 0 ? str = child[i].name + yChild[0].val + '黏膜皱襞形态规则，黏膜下血管纹理清晰，未见糜烂、溃疡、肿物' : '';
            } else {
              bwList.push(child[i].name + child[i]?.child[0]?.val)
            }
          }
          bwList && bwList.length > 0 ? bwStr = bwList.join('，') + (str ? '。' + str : '') : ''
          str = '';
          kjList = [];
          nmList = [];
        }
      }
    }
  })
  if (sdbwStr && hcmdnmStr) {
    strList.push(sdbwStr + '，' + hcmdnmStr)
  } else {
    sdbwStr ? strList.push(sdbwStr) : ''
    hcmdnmStr ? strList.push(hcmdnmStr) : ''
  }
  if (hmbStr && lwStr) {
    strList.push(hmbStr + '；' + lwStr);
  } else {
    hmbStr ? strList.push(hmbStr) : ''
    lwStr ? strList.push(lwStr) : ''
  }
  bwStr ? strList.push(bwStr) : ''
  strList && strList.length > 0 ? str = strList.join('；\n') : '';
  str = str.replace(/白苔，/g, '白苔；');
  str = str.replace(/(?<!黏膜下)血管纹理/g, '周围黏膜下血管纹理');
  str ? rtStructure.description = str + '。' : ''
  $('#kyxjcy-rt-87').val() ? rtStructure.impression = `溃疡性结肠炎（UCEIS评分:${$('#kyxjcy-rt-87').val()}分）` : ''
}