
#jycp1 {
  font-size: 16px;
}

.page-edit{
  display: block;
  background: #F5F7FA;
}
#jycp1  .edit-header{
  padding: 12px 24px;
  border-bottom: 1px solid #C0C4CC;
}

#jycp1  .edit-header h1{
  font-size: 20px;
  font-weight: 600;
}
#jycp1  .edit-header h4{
  margin-top: 12px;
  font-weight: 600;

}
#jycp1 input[type="text"] {
  outline: none;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding:6px 12px;
  flex:1 !important;
}
#jycp1 .item {
  display: flex;
  align-items: center;
  margin: 4px 0;
}
#jycp1 .i-label{
  color: #303133;
  width: 90px;
  margin-right: 4px;
  text-align: right;
}
#jycp1 .edit-content{
  padding: 12px 24px;
}
#jycp1 textarea {
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  outline: none;
  flex: 1;
  min-height: 81px;
  padding: 8px 12px;
  resize: none;
}
#jycp1 .tablebox{
  border-collapse: collapse;
  flex: 1;
  background-color: #fff;
}

#jycp1 col {
  width: 16.7%;
}
#jycp1 .tablebox td{
  padding: 0px 6px;
  border: 1px solid #C0C4CC;
  text-align: right;
}

#jycp1 .tablebox td select{
  width: 100%;
  min-width: unset !important;
}
#jycp1 .tablebox tr{
  height: 48px;
}
#jycp1 .tablebox .tbbox td{
  text-align: left;
}
#jycp1 .radio-label{
 margin-right: 36px;
}
#jycp1 .radio-label input[type="radio"]{
  margin-right: 8px;
  vertical-align: middle;
}
#jycp1 .jycp-pv{
  display: none;
  min-height:1100px;
  background-color: #F5F7FA;
}
#jycp1 .pv-warp {
  width:780px;
  min-height: 1100px;
  padding: 40px 60px 0 60px;
  background-color: #fff;
  margin: 0 auto;
}

#jycp1 .pv-warp h1{
  text-align: center;
  font-size: 24px;
  font-weight: bold;
}
#jycp1 .pv-warp h5{
  text-align: right;
  margin-top: 4px;
  font-size: 14px;
  color: #303133;
  font-weight: 400;
}
.pv-warp .pv-head{
    margin-top: 8px;
    border-top: 1px solid #999;
    border-bottom: 1px solid #999;
    padding: 6px 0;
    font-size: 14px !important;
}
.pv-head .h-item{
  display: flex;
  justify-content: space-between;
  margin: 4px 0;
}
.h-content{
  display: flex;
  align-items: baseline;
}
.h-content .h-label{
  color: #303133;
}
.h-content .h-text{
  color: #000;
  /* margin-left: 4px; */
  font-weight: 400;
  flex: 1;
  word-break: break-all;
}
#jycp1 .pv-info {
  padding: 6px 0;
}
#jycp1 .i-item {
  display: flex;
  margin: 4px 0;
}
#jycp1 .i-item .i-label{
  width: 80px;
  font-weight: 600;
  color: #303133;
  margin-right: 0;
}
#jycp1 .i-item .i-text{
  color: #333;
  font-weight: 400;
  flex: 1;
}
#jycp1 .pv-res {
  border-top: 1px solid #999;
  padding: 6px 0;
}
#jycp1 .r-item {
  display: flex;
  margin: 4px 0;
}
#jycp1 .r-item .r-label{
  font-weight: 600;
  color: #303133;
  margin-right: 0;
  width: 87px;
}
#jycp1 #jcpd .r-label {
  text-align: right;
}
#jycp1 .r-item .r-text{
  color: #333;
  flex: 1;
  word-break: break-all;
  font-weight: 400;
}
#jycp1 .r-item .tablebox  tr {
  height: 32px;
}
#jycp1 .r-item .tablebox td{
  text-align: center ;
  color: #333;
  font-size: 14px;
}
#jycp1 .pv-remark{
  display: flex;
  padding: 8px 0;
}
.pv-remark .rm-lb {
  width: 70px;
  color:#303133;
  font-weight: 600;

}
.pv-remark .rm-ct {
  flex:1;
  color: #333;
}
.pv-footer {
  padding: 8px 0;
  border-top: 1px solid #999;
  display: flex;
  font-size: 14px;
}
.pv-footer .f-item {
  display: flex;
  align-items: center;
  flex: 1;
  flex-wrap: wrap;
}
.pv-footer .f-label {
  width: 70px;
}
.pv-footer .f-text {
  flex: 1;
}
.pv-footer .f-item .f-left{
  display: flex;
  align-items: center;
  flex:1;
}
.f-right{
  width: 160px;
}
.f-1 {
  flex: 1;
}
#jycp1 .pv-f-r {
  padding: 6px 0;
  border-top: 1px solid #999;
  font-size: 14px;
}
[isView="true"] #jycp1 .page-edit {
  display: none;
}
[isView="true"] #jycp1 .jycp-pv{
  display: block;
}
#jycp1 .showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 12;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#jycp1 .layui-inline input{
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
  background-color: #fff;
}

#jycp1 .custom-textarea{
  border-radius: 3px;
  border:1px solid #C0C4CC;
  background: #fff;
  width: 100%;
}
/* 显示模板 entry-type="5" 样式处理 */
[entry-type="5"] #jycp1 {
  background: #fff;
  padding: 0;
}
[entry-type="5"] #jycp1 .jycp-pv {
  background-color: #fff;
  padding: 0;
}
[entry-type="5"] #jycp1 .jycp-pv h1,
[entry-type="5"] #jycp1 .jycp-pv h5,
[entry-type="5"] #jycp1 .jycp-pv .pv-head,
[entry-type="5"] #jycp1 .jycp-pv .pv-f-r{
  display: none;
}
[entry-type="5"] #jycp1 .jycp-pv .pv-warp {
  margin: unset;
  padding: 12px;
  min-height: auto;
  background-color: #fff;
  width: 100%;
}
[entry-type="5"] #jycp1 * {
  border: none;
}
#jycp1 .view-head {
  text-align: center;
}
#jycp1 .hos-logo img{
  width: 284px;
  height: 40px;
}
#jycp1 .blh-tit{
  text-align: right;
}
#jycp1 .page-tit{
  font-size: 24px;
  font-weight: bold;
}
#jycp1 .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
}
#jycp1 .black-txt {
  color: #000;
  font-size: 14px;
}
#jycp1 [data-key="jycp-rt-10"], 
#jycp1 [data-key="jycp-rt-51"], 
#jycp1 [data-key="jycp-rt-49"],
#jycp1 .space-break {
  white-space: pre-wrap;
  word-break: break-all;
}
#jycp1 [data-img="affirmReporterNo"] {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
#jycp1 .tablebox td .layui-inline{
  width: 100%;
}
.mr-6 {
  margin-right: 6px;
}