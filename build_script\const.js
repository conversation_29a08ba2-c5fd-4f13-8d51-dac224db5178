const path = require('path');
const fileMapGenerator = require('./config').fileMapGenerator;
/**
 * @description 当前环境,没有默认development
 */
const modeIndex = process.argv.findIndex(argv => argv.includes('mode'));
let nodeMode = process.argv[modeIndex === -1 ? 'development' : modeIndex + 1];
const fileName = `.env.${nodeMode}`;
const fileMap = fileMapGenerator(nodeMode);
/**
 * @description 获取需要执行的脚本
 */
const shellData = process.argv
  .filter((item) => {
    if (path.isAbsolute(item)) {
      return false;
    }
    return true;
  })
  .join(' ');
const SHELL = `cd ${process.cwd()} && ${shellData}`;
module.exports.fileName = fileName;
module.exports.SHELL = SHELL;
module.exports.fileMap = fileMap;
