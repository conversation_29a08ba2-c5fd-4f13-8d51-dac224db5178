$(function () {
  window.initHtmlScript = initHtmlScript;
})

var changeControl = ['ssbbA-rt-17', 'ssbbB-rt-28', 'ssbbB-rt-32', 'ssbbB-rt-36', 'ssbbB-rt-59', 'ssbbB-rt-65']

//溃疡
var kyOrganList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '纵行溃疡', id: '纵行溃疡' },
  { title: '不规则溃疡', id: '不规则溃疡' },
  { title: '环形溃疡', id: '环形溃疡' },
]
//最大直径
var zdzjList = [
  { title: '', id: '0' },
  { title: '1', id: 1 },
  { title: '2', id: 2 },
  { title: '3', id: 3 },
  { title: '4', id: 4 },
  { title: '5', id: 5 },
];

//溃疡数量、肉芽肿数量
var kyNumList = [
  { title: '', id: '0' },
  { title: '单个', id: '单个' },
  { title: '多个', id: '多个' }
]
// 息肉、铺路石改变、肠腔狭窄、肠穿孔、肠粘连、肠壁脂肪包绕、肠壁-透壁性炎、淋巴滤泡串珠、纤维组织增生、神经组织增生
var haveOrNoList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '有', id: '有' }
]
//肠壁------------------------------------------
//活动性炎
var cbHdxList = [
  { title: '', id: '0' },
  { title: '非活动性', id: '非活动性' },
  { title: '表面上皮炎', id: '表面上皮炎' },
  { title: '散在隐窝炎', id: '散在隐窝炎' },
  { title: '明显隐窝炎', id: '明显隐窝炎' },
  { title: '隐窝脓肿', id: '隐窝脓肿' },
  { title: '糜烂', id: '糜烂' },
  { title: '溃疡', id: '溃疡' },
  { title: '裂隙状溃疡', id: '裂隙状溃疡' }
]
//脓肿
var cbNzList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '肠壁', id: '肠壁' },
  { title: '肠旁', id: '肠旁' }
]
//黏膜病变
var cbNmbbList = [
  { title: '', id: '0' },
  { title: '正常', id: '正常' },
  { title: '肠炎', id: '肠炎' },
  { title: '慢性肠炎', id: '慢性肠炎' }
]
// 固有层慢性炎症细胞增多、固有层中性细胞增多
var gyZdList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '轻度增多', id: '轻度增多' },
  { title: '中度增多', id: '中度增多' },
  { title: '重度增多', id: '重度增多' }
]
//炎症分布模式
var yzfbList = [
  { title: '', id: '0' },
  { title: '无法判断', id: '无法判断' },
  { title: '节段性', id: '节段性' },
  { title: '连续性', id: '连续性' }
]
//结构改变
var jggbList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '隐窝分支', id: '隐窝分支' },
  { title: '隐窝缺失', id: '隐窝缺失' },
  { title: '隐窝缩短', id: '隐窝缩短' },
  { title: '隐窝延长', id: '隐窝延长' },
  { title: '异常隐窝形状', id: '异常隐窝形状' },
  { title: '基底浆细胞增多', id: '基底浆细胞增多' },
]
// 程度
var cdList = [
  { title: '', id: '0' },
  { title: '轻度', id: '轻度' },
  { title: '中度', id: '中度' },
  { title: '重度', id: '重度' }
]
//范围
var fwList = [
  { title: '', id: '0' },
  { title: '局限性', id: '局限性' },
  { title: '广泛性', id: '广泛性' },
]
//化生
var hsList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '幽门腺化生', id: '幽门腺化生' },
  { title: '潘氏细胞化生', id: '潘氏细胞化生' },
]
//息肉/假息肉
var xrList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '炎性息肉', id: '炎性息肉' },
  { title: '假息肉', id: '假息肉' },
]
//肌层增生
var jczsList = [
  { title: '', id: '0' },
  { title: '无', id: '无' },
  { title: '黏膜肌层增厚', id: '黏膜肌层增厚' },
  { title: '固有肌层增厚', id: '固有肌层增厚' },
  { title: '固有肌层与黏膜肌层融合', id: '固有肌层与黏膜肌层融合' },
]
//肉芽肿
var ryzList = [
  { title: '', id: '0' },
  { title: '未见肉芽肿', id: "未见肉芽肿" },
  { title: '可见肉芽肿', id: "可见肉芽肿" },
  { title: '可疑肉芽肿', id: "可疑肉芽肿" },
  { title: '异物肉芽肿', id: "异物肉芽肿" },
  { title: '隐窝破裂性肉芽肿', id: "隐窝破裂性肉芽肿" }
];
//肉芽肿-位置
var ryzPositionList = [
  { title: '', id: '0' },
  { title: '黏膜层', id: "黏膜层" },
  { title: '黏膜下层', id: "黏膜下层" },
];
//抗酸染色
var ksrsList = [
  { title: '', id: '0' },
  { title: '未做', id: "未做" },
  { title: '阴性', id: "阴性" },
  { title: '阳性', id: "阳性" }
];
// 异型增生
var yxzsList = [
  { title: '', id: '0' },
  { title: '无见异型增生', id: "无见异型增生" },
  { title: '低级别异型增生', id: "低级别异型增生" },
  { title: '高级别异型增生', id: "高级别异型增生" },
  { title: '不确定性异型增生', id: "不确定性异型增生" }
];
// 肠壁end--------------------------------------------------

//近切缘、远切缘
var qyList = [
  { title: '', id: '0' },
  { title: '未见明显病变', id: "未见明显病变" },
  { title: '黏膜慢性肠炎', id: "黏膜慢性肠炎" },
  { title: '黏膜活动性炎', id: "黏膜活动性炎" },
  { title: '透壁性炎', id: "透壁性炎" },
  { title: '肠壁纤维组织增生', id: "肠壁纤维组织增生" },
  { title: '神经丛炎', id: "神经丛炎" },
  { title: '可见肉芽肿', id: "可见肉芽肿" },
];

//阑尾
var lwList = [
  { title: '', id: '0' },
  { title: '未见明显病变', id: "未见明显病变" },
  { title: '慢性阑尾炎', id: "慢性阑尾炎" },
  { title: '可见肉芽肿', id: "可见肉芽肿" },
  { title: '未送检', id: "未送检" },
  { title: '其他', id: "其他" }
];
//肠系膜血管
var cxmxgList = [
  { title: '', id: '0' },
  { title: '未见明显病变', id: "未见明显病变" },
  { title: '血管壁增厚，管腔狭窄（个别血管/多量血管）', id: "血管壁增厚，管腔狭窄（个别血管/多量血管）" },
  { title: '血管炎', id: "血管炎" },
  { title: '未见肠系膜血管', id: "未见肠系膜血管" },
  { title: '其他', id: "其他" }
];
//淋巴结
var lbjList = [
  { title: '', id: '0' },
  { title: '未见肉芽肿', id: "未见肉芽肿" },
  { title: '可见肉芽肿', id: "可见肉芽肿" },
  { title: '未送检', id: "未送检" },
];
var rtStructure = null;
var curElem = null;
var rptImageList = [];
var publicInfo = {};  //公共报告属性内容
var idAndDomMap = {};  //节点id和值对应的关系
function initHtmlScript(ele) {
  curElem = $(ele);
  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: false,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: false,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
      pdfContainer: document.querySelector('#ibdSsbb1 .ssbb-view'),  //转成pdf的区域，默认是整个页面
    }
  }
  if (rtStructure) {
    rptImageList = rtStructure.enterOptions ? (rtStructure.enterOptions.rptImageList || []) : [];  //报告图像
    publicInfo = rtStructure.enterOptions ? (rtStructure.enterOptions.publicInfo || {}) : {};  //检查信息
    idAndDomMap = rtStructure && rtStructure.idAndDomMap ? rtStructure.idAndDomMap : {};
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {
      initView()
    } else {
      initPage()
    }
  }
}
// 回显预览内容
function initView() {
  let hideKey = []
  curElem.find('.ssbb-view [data-key]').each(function () {
    var key = $(this).attr('data-key');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (key === 'sampleSeen') {
      if (value) {
        $(this).html(value)
      } else {
        $(this).parent().hide();
        hideKey.push(key)
      }
    } else if (key === 'impression') {
      let pathDiagVal = rtStructure.enterOptions ? rtStructure.enterOptions.impression || '' : ''
      if (pathDiagVal) {
        $(this).html(pathDiagVal)
      } else {
        $(this).parent().hide();
        hideKey.push(key)
      }
    }else{
      value ? $(this).html(value) : $(this).html('')
      if (!value && key === 'clinDiag') {
        $(this).parent().parent().hide()
      }
    }
    addIdToNodeByView(this, key, idAndDomMap);
  })
  hideKey.length > 1 ? $("#wjhjDesc").hide() : ''
  if (rptImageList && rptImageList.length) {
    var imgHtml = '';
    for (let i = 0; i < rptImageList.length; i++) {
      if (rptImageList[i].src && i < 4) {
        imgHtml += `
        <div class="item-img">
          <img src="${rptImageList[i].src}" alt="">
        </div>
        `
      }
    }
    if (imgHtml) {
      curElem.find('.ssbb-view .rpt-img-ls').html(imgHtml);
      curElem.find('.ssbb-view .rpt-img-ls').css('display', 'flex');
    }
  }
  curElem.find('.ssbb-view [data-img]').each(function () {
    var key = $(this).attr('data-img');
    var idAnVal = idAndDomMap[key] ? idAndDomMap[key].value : '';
    var value = publicInfo[key] || idAnVal;
    if (value) {
      var src = getSignImgHandler({ staffNo: value });
      if (src) {
        $(this).attr('src', src);
        $(this).show();
      } else {
        $(this).hide();
      }
    } else {
      $(this).hide();
    }
  })
}
function initPage() {
  // 大体描述id
  function initDtIdLsit(num) {
    return `ssbbA-rt-${num}`
  }
  //显微镜描述
  function initXwjIdLsit(num) {
    return `ssbbB-rt-${num}`
  }
  var ky = initDtIdLsit('17');
  var kyNum = initDtIdLsit('19');
  var xr = initDtIdLsit('24');
  var plsgb = initDtIdLsit('26');
  var xqxz = initDtIdLsit('28');
  var cck = initDtIdLsit('30');
  var cnl = initDtIdLsit('32');
  var cbzfbs = initDtIdLsit('34');
  var cbHdx = initXwjIdLsit('4');
  var cbNz = initXwjIdLsit('6');
  var cbNmbb = initXwjIdLsit('8');
  var gyzd = initXwjIdLsit('10');
  var gyzde = initXwjIdLsit('12');
  var yzfb = initXwjIdLsit('14');
  var jggb = initXwjIdLsit('16');
  var cd = initXwjIdLsit('18');
  var fw = initXwjIdLsit('20');
  var hs = initXwjIdLsit('22');
  var cbxr = initXwjIdLsit('24');
  var tbxy = initXwjIdLsit('26');
  var lblp = initXwjIdLsit('28');
  var xwzz = initXwjIdLsit('32');
  var sjzz = initXwjIdLsit('36');
  var jczs = initXwjIdLsit('41');
  var ryz = initXwjIdLsit('43');
  var ryzWz = initXwjIdLsit('45');
  var ryzNum = initXwjIdLsit('47');
  var ryzZdzj = initXwjIdLsit('49');
  var ksrs = initXwjIdLsit('51');
  var yxzs = initXwjIdLsit('53');
  var jqy = initXwjIdLsit('55');
  var yqy = initXwjIdLsit('57');
  var lw = initXwjIdLsit('59');
  var lwzd = initXwjIdLsit('61');
  var cxmxg = initXwjIdLsit('63');
  var lbj = initXwjIdLsit('65');
  var lbjzd = initXwjIdLsit('67');

  initInpAndSel(ky, kyOrganList, 120);
  initInpAndSel(kyNum, kyNumList, 120);
  initInpAndSel(xr, haveOrNoList, 120);
  initInpAndSel(plsgb, haveOrNoList, 120);
  initInpAndSel(xqxz, haveOrNoList, 120);
  initInpAndSel(cck, haveOrNoList, 120);
  initInpAndSel(cnl, haveOrNoList, 120);
  initInpAndSel(cbzfbs, haveOrNoList, 120);
  initInpAndSel(cbHdx, cbHdxList);
  initInpAndSel(cbNz, cbNzList);
  initInpAndSel(cbNmbb, cbNmbbList);
  initInpAndSel(gyzd, gyZdList);
  initInpAndSel(gyzde, gyZdList);
  initInpAndSel(yzfb, yzfbList);
  initInpAndSel(jggb, jggbList);
  initInpAndSel(cd, cdList);
  initInpAndSel(fw, fwList);
  initInpAndSel(hs, hsList);
  initInpAndSel(cbxr, xrList);
  initInpAndSel(tbxy, haveOrNoList);
  initInpAndSel(lblp, haveOrNoList);
  initInpAndSel(xwzz, haveOrNoList);
  initInpAndSel(sjzz, haveOrNoList);
  initInpAndSel(jczs, jczsList);
  initInpAndSel(ryz, ryzList);
  initInpAndSel(ryzWz, ryzPositionList);
  initInpAndSel(ryzNum, kyNumList);
  initInpAndSel(ryzZdzj, zdzjList);
  initInpAndSel(ksrs, ksrsList);
  initInpAndSel(yxzs, yxzsList);
  initInpAndSel(jqy, qyList);
  initInpAndSel(yqy, qyList);
  initInpAndSel(lw, lwList);
  initInpAndSel(lwzd, zdzjList);
  initInpAndSel(cxmxg, cxmxgList, 330);
  initInpAndSel(lbj, lbjList);
  initInpAndSel(lbjzd, zdzjList);
  let idArr = ['ssbbA-rt-17', 'ssbbB-rt-28', 'ssbbB-rt-32', 'ssbbB-rt-36', 'ssbbB-rt-59', 'ssbbB-rt-65']
  idArr.forEach(item => {
    changeSel(item)
  })
}


// 初始化输入选择下拉框
function initInpAndSel(idList, optionList, lenVal) {
  let selLen = 200;
  lenVal ? selLen = lenVal : '';
  let dropdown = layui.dropdown;
  dropdown.render({
    elem: `#${idList}`,
    data: optionList,
    className: 'laySelLab',
    click: function (obj) {
      this.elem.val(obj.title);
      if (changeControl.indexOf(idList) !== -1) {
        changeSel(idList);
      }
    },
    style: `width: ${selLen}px;`
  });
}

function changeSel(id) {
  if (id === 'ssbbA-rt-17') {
    if ($('#' + id).val() === '纵行溃疡') {
      $("#ky-a").css('display', 'unset');
      $("#ky-b").css('display', 'none');
    } else if ($('#' + id).val() === '不规则溃疡') {
      $("#ky-a").css('display', 'unset');
      $("#ky-b").css('display', 'unset');
    } else {
      $("#ky-a").css('display', 'none');
      $("#ky-b").css('display', 'none');
      $('#ssbbA-rt-22').val('')
      $('#ssbbA-rt-21').val('')
    }
  }
  if (id === 'ssbbB-rt-28') {
    if ($('#' + id).val() === '无' || $('#' + id).val() === '') {
      $('#ssbbB-rt-29').attr('disabled', true);
      $('#ssbbB-rt-30').attr('disabled', true);
      $('#ssbbB-rt-29').attr('checked', false);
      $('#ssbbB-rt-30').attr('checked', false);
    } else {
      $('#ssbbB-rt-29').attr('disabled', false);
      $('#ssbbB-rt-30').attr('disabled', false);
    }
  }
  if (id === 'ssbbB-rt-32') {
    if ($('#' + id).val() === '无' || $('#' + id).val() === '') {
      $('#ssbbB-rt-33').attr('disabled', true);
      $('#ssbbB-rt-34').attr('disabled', true);
      $('#ssbbB-rt-33').attr('checked', false);
      $('#ssbbB-rt-34').attr('checked', false);
    } else {
      $('#ssbbB-rt-33').attr('disabled', false);
      $('#ssbbB-rt-34').attr('disabled', false);
    }
  }
  if (id === 'ssbbB-rt-36') {
    if ($('#' + id).val() === '无' || $('#' + id).val() === '') {
      $('#ssbbB-rt-37').attr('disabled', true);
      $('#ssbbB-rt-38').attr('disabled', true);
      $('#ssbbB-rt-39').attr('disabled', true);
      $('#ssbbB-rt-37').attr('checked', false);
      $('#ssbbB-rt-38').attr('checked', false);
      $('#ssbbB-rt-39').attr('checked', false);
    } else {
      $('#ssbbB-rt-37').attr('disabled', false);
      $('#ssbbB-rt-38').attr('disabled', false);
      $('#ssbbB-rt-39').attr('disabled', false);
    }
  }
  if (id === 'ssbbB-rt-59') {
    if ($('#' + id).val() === '未见明显病变' || $('#' + id).val() === '未送检' || $('#' + id).val() === '') {
      $('#ssbbB-rt-61').attr('disabled', true);
      $('#ssbbB-rt-61').val('')
    } else {
      $('#ssbbB-rt-61').attr('disabled', false);
    }
  }
  if (id === 'ssbbB-rt-65') {
    if ($('#' + id).val() === '未见肉芽肿' || $('#' + id).val() === '未送检' || $('#' + id).val() === '') {
      $('#ssbbB-rt-67').attr('disabled', true);
      $('#ssbbB-rt-67').val('');
    } else {
      $('#ssbbB-rt-67').attr('disabled', false);
    }
  }
}

function getImpression(){
  var cbStrList = [];
  var strList = [];
  var remarksStr = '', impression = '';
  var specialIds = ['ssbbB-rt-27', 'ssbbB-rt-28', 'ssbbB-rt-31', 'ssbbB-rt-32', 'ssbbB-rt-35', 'ssbbB-rt-36', 'ssbbB-rt-42', 'ssbbB-rt-44', 'ssbbB-rt-46', 'ssbbB-rt-48', 'ssbbB-rt-54', 'ssbbB-rt-56', 'ssbbB-rt-58', 'ssbbB-rt-62', 'ssbbB-rt-64', 'ssbbB-rt-68']
  var curResultData = JSON.parse(JSON.stringify(rtStructure.curResultData));

  $(".rt-sr-w.rt-sr-xwj").each(function (dI, dom) {
    var id = $(dom).attr('id');
    var title = $(dom).text();
    var childStr = []
    var handler = function (list) {
      for (var l = 0; l < list.length; l++) {
        var child = list[l].child;
        var tempStr = '';
        if (child && child.length > 0) {
          tempStr = handler(child, []);
        } else {
          if (specialIds.indexOf(list[0].pid) !== -1) {

          } else {
            var pid = list[0].pid;
            childStr.push($('#' + pid).text() + list[0].val)
          }
        }
      }
      return childStr;
    }
    if (specialIds.indexOf(id) === -1) {
      for (var i = 0; i < curResultData.length; i++) {
        if (curResultData[i].id === id) {
          var child = curResultData[i].child;
          if (child && child.length) {
            for (var c = 0; c < child.length; c++) {
              if (child[c].desc === '肠壁') {
                cbStrList = handler(child[c].child);
                break;
              }
            }
          }
          curResultData.splice(i, 1);
          break;
        }
      }
    } else {
      if (id === 'ssbbB-rt-27' && $('#ssbbB-rt-28').val() && $('#ssbbB-rt-28').val() !== '') {
        var strLblp = ''
        var wzA = '', wzB = '';
        // if ($('#ssbbB-rt-29').prop('checked')) {
        //   wzA = $('#ssbbB-rt-29').val()
        // }
        // if ($('#ssbbB-rt-30').prop('checked')) {
        //   wzB = $('#ssbbB-rt-30').val()
        // }
        wzA = $('#ssbbB-rt-29').prop('checked') ? $('#ssbbB-rt-29').val() : ''
        wzB = $('#ssbbB-rt-30').prop('checked') ? $('#ssbbB-rt-30').val() : ''
        if (wzA !== '' && wzB !== '') {
          strLblp = '淋巴滤泡串珠：' + $('#ssbbB-rt-28').val() + '（' + wzA + '/' + wzB + ')';
        } else if (wzA !== '' && wzB === '') {
          strLblp = '淋巴滤泡串珠：' + $('#ssbbB-rt-28').val() + '（' + wzA + ')';
        } else if (wzB !== '' && wzA === '') {
          strLblp = '淋巴滤泡串珠：' + $('#ssbbB-rt-28').val() + '（' + wzB + ')';
        } else {
          strLblp = '淋巴滤泡串珠：' + $('#ssbbB-rt-28').val();
        }
        cbStrList.push(strLblp)
      }
      if (id === 'ssbbB-rt-31' && $('#ssbbB-rt-32').val() && $('#ssbbB-rt-32').val() !== '') {
        var strXwzz = ''
        var wzA = '', wzB = '';
        wzA = $('#ssbbB-rt-33').prop('checked') ? $('#ssbbB-rt-33').val() : ''
        wzB = $('#ssbbB-rt-34').prop('checked') ? $('#ssbbB-rt-34').val() : ''

        if (wzA !== '' && wzB !== '') {
          strXwzz = '纤维组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzA + '/' + wzB + ')';
        } else if (wzA !== '' && wzB === '') {
          strXwzz = '纤维组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzA + ')';
        } else if (wzB !== '' && wzA === '') {
          strXwzz = '纤维组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzB + ')';
        } else {
          strXwzz = '纤维组织增生：' + $('#ssbbB-rt-32').val();
        }
        cbStrList.push(strXwzz)
      }
      if (id === 'ssbbB-rt-35' && $('#ssbbB-rt-36').val() && $('#ssbbB-rt-36').val() !== '') {
        var strSjzz = ''
        var wzA = '', wzB = '', wzC = '';
        wzA = $('#ssbbB-rt-37').prop('checked') ? $('#ssbbB-rt-37').val() : '';
        wzB = $('#ssbbB-rt-38').prop('checked') ? $('#ssbbB-rt-38').val() : '';
        wzC = $('#ssbbB-rt-39').prop('checked') ? $('#ssbbB-rt-39').val() : '';
        if (wzA !== '' && wzB !== '' && wzC !== '') {
          strSjzz = '神经组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzA + '/' + wzB + '/' + wzC + ')';
        } else if (wzA !== '' && wzB !== '' && wzC === '') {
          strSjzz = '神经组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzA + '/' + wzB + ')';
        } else if (wzA !== '' && wzB === '' && wzC !== '') {
          strSjzz = '神经组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzA + '/' + wzC + ')';
        } else if (wzA === '' && wzB !== '' && wzC !== '') {
          strSjzz = '神经组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzB + '/' + wzC + ')';
        } else if (wzA !== '' && wzB === '' && wzC === '') {
          strSjzz = '纤维组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzA + ')';
        } else if (wzB !== '' && wzA === '' && wzC === '') {
          strSjzz = '纤维组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzB + ')';
        } else if (wzB === '' && wzA === '' && wzC !== '') {
          strSjzz = '纤维组织增生：' + $('#ssbbB-rt-32').val() + '（' + wzC + ')';
        } else {
          strSjzz = '神经组织增生：' + $('#ssbbB-rt-32').val();
        }
        cbStrList.push(strSjzz)
      }
      if (id === 'ssbbB-rt-42' && $('#ssbbB-rt-43').val() && $('#ssbbB-rt-43').val() !== '') {
        var strRyz = ''
        strRyz = '肉芽肿：' + $('#ssbbB-rt-43').val() + '    位置：' + ($('#ssbbB-rt-45').val() ? $('#ssbbB-rt-45').val() : '无') + '    数量：' + ($('#ssbbB-rt-47').val() ? $('#ssbbB-rt-47').val() : '无') + '    最大直径：' + ($('#ssbbB-rt-49').val() ? `${$('#ssbbB-rt-49').val()}mm` : '无')
        cbStrList.push(strRyz)
      }

      if (id === 'ssbbB-rt-54' && $('#ssbbB-rt-55').val() && $('#ssbbB-rt-55').val() !== '') {
        var jqy = '';
        jqy = '近切缘：' + $('#ssbbB-rt-55').val();
        strList.push(jqy)
      }
      if (id === 'ssbbB-rt-56' && $('#ssbbB-rt-57').val() && $('#ssbbB-rt-57').val() !== '') {
        var yqy = '';
        yqy = '远切缘：' + $('#ssbbB-rt-57').val();
        strList.push(yqy)
      }

      if (id === 'ssbbB-rt-58' && $('#ssbbB-rt-59').val() && $('#ssbbB-rt-59').val() !== '') {
        var strLw = '';
        if ($('#ssbbB-rt-61').val() && $('#ssbbB-rt-61').val() !== '') {
          strLw = '阑尾：' + $('#ssbbB-rt-59').val() + '（最大直径：' + $('#ssbbB-rt-61').val() + 'mm）'
        } else {
          strLw = '阑尾：' + $('#ssbbB-rt-59').val()
        }
        strList.push(strLw)
      }
      if (id === 'ssbbB-rt-62' && $('#ssbbB-rt-63').val() && $('#ssbbB-rt-63').val() !== '') {
        var strCxmxg = '肠系膜血管：' + $('#ssbbB-rt-63').val()
        strList.push(strCxmxg)
      }
      if (id === 'ssbbB-rt-64' && $('#ssbbB-rt-65').val() && $('#ssbbB-rt-65').val() !== '') {
        var strLbj = '';
        if ($('#ssbbB-rt-67').val() && $('#ssbbB-rt-67').val() !== '') {
          strLbj = '淋巴结：' + $('#ssbbB-rt-65').val() + '（最大直径：' + $('#ssbbB-rt-67').val() + 'mm）'
        } else {
          strLbj = '淋巴结：' + $('#ssbbB-rt-65').val()
        }
        strList.push(strLbj)
      }
    }
  })
  var remarks = $('#ssbbB-rt-69').val() ? ('备注：' + $('#ssbbB-rt-69').val()) : '';
  if (remarks !== '') {
    var len = remarks.length;
    var last = remarks.charAt(len - 1);
    remarksStr = remarks + (last === '。' ? '' : '。')
    strList.push(remarksStr);
  }
  if (cbStrList.length || strList.length) {
    return impression = '显微镜下描述：\n' + '肠壁：\n    ' + cbStrList.join('\n    ') + '\n' + strList.join('\n');
  }else{
    return ''
  }
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议  docOrign额外的设备数据
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.docOrign = null;
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    examTechnician: '', //检查技师
    priReporter: '',  //初步报告医生
    priReporterNo: '',  //初步报告医生流水号
    affirmReporter: '',  //审核医生
    affirmReporterNo: '',  //审核医生流水号
    affirmDate: '',  //审核日期
    reDiagReporter: '',  //复诊报告医生
    reDiagReporterNo: '',  //复诊报告医生流水号，多个逗号隔开
    examParam: '',  //检查过程中记录的有关内容，如测量值、特殊检查
    sampleSeen: '',  //肉眼所见
    rptImageList: rptImageList,   //报告图像
  }
  // console.log(rtStructure);
  let str = getImpression() || '';
  str ? rtStructure.impression = str : ''
  // if (cbStrList.length || strList.length) {
  //   rtStructure.impression = '显微镜下描述：\n' + '肠壁：\n    ' + cbStrList.join('\n    ') + '\n' + strList.join('\n');
  // }
}