$(function () {
  window.initHtmlScript = initHtmlScript;
})

var rtStructure = null;
var curElem = null;
function initHtmlScript(ele) {
  curElem = $(ele);

  initC();
  $('#pw-p').click(function () {
    vmaxClick('pw');
  })
  $('#cw-p').click(function () {
    vmaxClick('cw');
  })

  if (window.findCurStructure) {
    rtStructure = window.findCurStructure(ele, window.instanceList)
    rtStructure.createDescAndImpText = createDescAndImpTextHandler;
    rtStructure.diffConfig = {
      nextWidgetEnable: true,  //true有联动的控件，当父节点值为空时，子节点可编辑
      noUploadPdf: true,  //true确认报告时不上传pdf
      checkboxSort: false,  //true多选项时预览下前面加序号(1)(2)...
    }
  }

  keydown_to_tab('fdmb1');

  //radio点击前的选中状态
  var _is_checked_ = false;
  curElem.find("input[type=radio]").mousedown(function () {
    //记录radio点击前的选中状态
    _is_checked_ = $(this).prop("checked");
  }).click(function (e) {
    var iput = $(this);
    if (_is_checked_) {
      //如果radio点击前是选中状态，则取消选中
      iput.prop("checked", false);
    }
  });
  curElem.find("input[type=radio]").parent().click(function (e) {
    //记录radio外label被点时radio的选中状态
    _is_checked_ = $(this).children("input:first").prop("checked");
  });

  if (rtStructure) {
    // view为预览页面
    if (rtStructure.enterOptions.type === 'view') {

    } else {

    }
  }
}

function initC() {
  var pwChecked = $('#fdmb-rt-9').prop("checked");
  var cwChecked = $('#fdmb-rt-10').prop("checked");
  if (pwChecked) {
    $('#cw-child').attr("style", "display:none;background: unset;");
  } else if (cwChecked) {
    $('#pw-child').attr("style", "display:none;background: unset;");
    $('#cw-child').attr("style", "display:unset;background: unset;");
  } else {
    $('#cw-child').attr("style", "display:none;background: unset;");
  }
}

function vmaxClick(ele) {
  $('#pw-child').attr('style', 'display:' + (ele === 'pw' ? 'unset;' : 'none;'));
  $('#cw-child').attr('style', 'display:' + (ele === 'cw' ? 'unset;' : 'none;'));
}

/**
 * 可自定义方法名
 * 将报告模版填写的内容，结合实际业务要求排版，转成相应字符串存到对应字段
 * 用于pacs普通xml模板的展示
 */
function createDescAndImpTextHandler() {
  // description描述  impression诊断/印象  recommendation建议
  rtStructure.description = '';
  rtStructure.impression = '';
  rtStructure.recommendation = '';
  rtStructure.reportInfo = {
    organNames: '',  //检查部位，多个用','号隔开
    reporter: '', //报告医生
    reportDate: '',  //报告日期
    affirmReporter: '',  //审核医生
    affirmDate: '',  //审核日期
    examParam: '',  //检查过程中记录的有关内容，如测量值
  }
  // console.log(rtStructure);
  let curKeyValData = rtStructure && rtStructure.curKeyValData ? rtStructure.curKeyValData : {};
  let str = '';
  let strList = [], wcxqxlList = [], cdfiList = [], dplCheckList = [];
  let idList = ['fdmb-rt-1', 'fdmb-rt-2', 'fdmb-rt-3', 'fdmb-rt-4', 'fdmb-rt-5', 'fdmb-rt-6', 'fdmb-rt-7', 'fdmb-rt-10'];
  for (var i = 0; i < idList.length; i++) {
    if (curKeyValData[idList[i]] && idList[i] !== 'fdmb-rt-7') {
      strList.push(curKeyValData[idList[i]].val);
    } else if (curKeyValData[idList[i]] && idList[i] === 'fdmb-rt-7') {
      let child = curKeyValData[idList[i]].child;
      if (child && child.length) {
        strList.push(curKeyValData[idList[i]].val + '，大小' + (curKeyValData['fdmb-rt-8'] ? curKeyValData['fdmb-rt-8'].val : '0') + 'mm * ' + (curKeyValData['fdmb-rt-9'] ? curKeyValData['fdmb-rt-9'].val : '0') + 'mm')
      } else {
        strList.push(curKeyValData[idList[i]].val);
      }
    }
  }
  $('#dplCheck').find('.rt-sr-w[pid=fdmb-rt-11]').each(function (pIdx, par) {
    let parent = $(par);
    let pid = parent.attr('id');
    let curPid = curKeyValData[pid];
    if (curPid) {
      let child = curPid.child;
      if (child && child.length) {
        if (curPid.id === 'fdmb-rt-13') {
          wcxqxlList.push(curPid.val)
          for (var a = 0; a < child.length; a++) {
            if (child[a].id === 'fdmb-rt-14') {
              wcxqxlList.push('CW测得Vmax为' + child[a].val + 'm/s')
            }
            if (child[a].id === 'fdmb-rt-15') {
              wcxqxlList.push('PG为' + child[a].val + 'mmHg')
            }
            if (child[a].id === 'fdmb-rt-16') {
              wcxqxlList.push('MG为' + child[a].val + 'mmHg')
            }
            if (child[a].id === 'fdmb-rt-17') {
              let pchild = child[a].child;
              for (var p = 0; p < pchild.length; p++) {
                if (pchild[p].id === 'fdmb-rt-18') {
                  cdfiList.push('瓣环直径' + pchild[p].val + 'mm')
                }
                if (pchild[p].id === 'fdmb-rt-19') {
                  cdfiList.push('瓣口直径' + pchild[p].val + 'mm')
                }
              }
            }
          }
          cdfiList && cdfiList.length ? str = cdfiList.join('，') : ''
          str ? wcxqxlList.push('CDFI测得' + str) : '';
          str = '';
          wcxqxlList && wcxqxlList.length ? dplCheckList.push(wcxqxlList.join('，')) : ''
        }
        if (curPid.id === 'fdmb-rt-20') {
          dplCheckList.push(curPid.val + child[0].val + '反流')
        }
        if (curPid.id === 'fdmb-rt-27') {
          for (var b = 0; b < child.length; b++) {
            if (child[b].id === "fdmb-rt-28" || child[b].id === "fdmb-rt-29") {
              if (child[b].child && child[b].child.length) {
                dplCheckList.push('以' + child[b].val + '测得Vmax为' + child[b].child[0].val + 'm/s')
              }
            }
            if (child[b].id === "fdmb-rt-32") {
              dplCheckList.push('PG为' + child[b].val + 'mmHg')
            }
            if (child[b].id === "fdmb-rt-33") {
              dplCheckList.push('估算其PASP为' + child[b].val + 'mmHg')
            }
          }
        }
      } else if (curPid.id !== 'fdmb-rt-27') {
        dplCheckList.push(curPid.val)
      }
    }
  })
  str = dplCheckList.join('，');
  str ? strList.push('多普勒检查，' + str) : '';
  str = '';
  rtStructure.description = '肺动脉瓣，' + strList.join('，');
}