/* 颜色类 */
.bor-b {
  border-bottom: 1px solid #DCDFE6;
}
/* 边距类 */
.mb-12 {
  margin-bottom: 12px;
}
.ml-8 {
  margin-left: 8px;
}
.ml-10 {
  margin-left: 10px;
}
.ml-12 {
  margin-left: 12px;
}
/* 宽度类 */
.wd-72 {
  display: inline-block;
  width: 72px!important;
}
.wd-84 {
  display: inline-block;
  width: 84px!important;
}
.layout-page .main-content {
  width: 1140px;
}
/* 病理检查申请单 */
#bljcsqd1 {
  /* width: 1140px; */
  font-size: 14px;
  color: #606266;
  background: #fff;
  margin: 0 auto;
  border: 1px solid #DCDFE6;
}
#bljcsqd1 input[type="checkbox"],#bljcsqd1 input[type="radio"] {
  vertical-align: middle;
}
.bljcsqd-h {
  height: 48px;
  padding: 13px 20px;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: bold;
  border-radius: 2px 2px 0px 0px;
}
#bljcsqd1 .laydate-w .rt-sr-w {
  height: 28px;
}
.box-title {
  height: 44px;
  padding: 12px 44px;
  background: #F5F7FA;
  border-top: 1px solid #DCDFE6;
  border-bottom: 1px solid #DCDFE6;
  display: flex;
  justify-content: space-between;
}
.box-title .bb-title {
  color: rgba(0,0,0,0.88);
  font-weight: bold;
}
.box-title .span-tit {
  margin-left: 4px;
}
.form-item {
  display: inline-block;
}
.box-con {
  padding: 12px 35px 12px 21px;
}
.row-item {
  display: inline-block;
  width: 360px;
}
.row-item:last-child {
  width: unset;
}
.row-title {
  text-align: right;
}
.row-lab {
  width: 109px;
  display: inline-block;
  text-align: right;
}
.inp-sty {
  width: 246px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
}
.ver-t {
  vertical-align: top;
}
.btn-sty {
  display: inline-block;
  height: 36px;
  line-height: 36px;
  color: #FFF;
  background: #1885F2;
  border-radius: 3px;
  padding: 0 12px;
  cursor: pointer;
}
.add-btn,.del-btn {
  margin-left: 8px;
}
.del-tbtn {
  color: #1885F2;
  cursor: pointer;
}
.bbxx-table {
  border: 1px solid #DCDFE6;
  border-bottom: none;
  border-right: none;
  width: 100%;
  table-layout: fixed;
}
.bbxx-table thead {
  width: 56px;
  height: 48px;
  line-height: 48px;
  font-weight: bold;
  color: #909399;
  background: #F5F7FA;
}
.bbxx-table tr {
  height: 48px;
  line-height: 28px;
  text-align: center;
}
.w-con {
  display: inline-block;
  vertical-align: middle;
}
.show-or-hide {
  position: relative;
}
.show-or-hide input {
  padding: 0 16px 0 4px;
  position: relative;
  background: #fff;
  /* z-index: 2; */
  /* background: transparent; */
}
.bbsl .inp-sty, .bbzl .inp-sty {
  padding: 0 4px;
} 
.show-or-hide:hover {
  opacity: .8;
}
.show-or-hide::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  /* z-index: 1; */
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
}
.show-or-hide.more::after {
  transform: rotate(135deg);
  margin-top: -5px;
}
.show-or-hide.hide-more:after {
  transform: rotate(-45deg);
  margin-top: -2px;
}
.bbmcInpSel {
  height: 250px;
  overflow: auto;
  overflow-x: hidden;
}
.bbmcInpSel .layui-menu-item-none {
  display: none;
}
.laySelLab {
  max-height: 300px;
  overflow: auto;
  overflow-x: hidden;
}
.laySelLab li,.bbmcInpSel li {
  height: 26px;
}
#bljcsqd1 .laydate-w textarea[val-format] {
  height: 29px;
}
.bljcsqdTableBody tr:last-child>td {
  border-bottom: none;
}
[isView="true"] #bljcsqd1 .box-item .box-con .text-con {
  flex: 1;
  white-space: break-spaces;
  word-break: break-all;
}
[isView="true"] #bljcsqd1 .btn-sty,[isView="true"] .bljcsqdTableBody tr>td:nth-child(2) span.text-con:first-child,[isView="true"] #bljcsqd1 .show-or-hide::after {
  display: none;
}
[isView="true"] #bljcsqd1 .del-tbtn {
  pointer-events: none;
}
[isView="true"] #bljcsqd1 .row-item:not(:last-child) .show-or-hide {
  width: calc(100% - 114px);
}
#bljcsqd1 .isk{
  color: #F56C6C;
}
[isView="true"] #bljcsqd1 .isk{
  display: none;
}
[isView="true"] #bljcsqd1 .mb-12{
  display: flex;
}
/* [isView="true"] #bljcsqd1 .row-item{
  flex: 1;
} */
[isView="true"] #bljcsqd1 .row-lab{
  vertical-align: bottom;
}
.laydate-time-list:not(.show-seconds) > li:last-child {
  display: inline-block !important;
}
.laydate-time-list:not(.show-seconds) > li {
  width: 33.3% !important;
}
.laydate-time-list:not(.show-seconds) > li > ol >li {
  padding-left: 33px !important;
}
#bljcsqd1 .form-item .laydate-w input {
  border: none;
  outline: none;
  display: block;
  width: 100%;
  height: 100%;
  padding: 5px 8px 5px 30px;
  box-sizing: border-box;
  height: 30px;
}
#bljcsqd1 .wrap-box {
  display: flex;
  flex-wrap: wrap;
}
#bljcsqd1 .wrap-box .row-item {
  margin-bottom: 12px;
}
#bljcsqd1 .lb-btn {
  display: inline-block;
  vertical-align: top;
}
#bljcsqd1 .btn-txt {
  color: #1885F2;
  cursor: pointer;
  text-decoration-line: underline;
  text-align: right;
}
.bbzl .inp-sty {
  border-right: none;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
#bljcsqd1 .unit-sty {
  display: inline-block;
  padding: 0 8px;
  line-height: 28px;
  background: rgb(245, 247, 250);
  color: rgb(144, 147, 153);
  margin-left: -6px;
  vertical-align: bottom;
  border: 1px solid #DCDFE6;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.rt-dialog-title {
  font-weight: bold;
  font-size: 18px!important;
}
[isView="true"] #bljcsqd1 .tqnjzd-btn {
  display: none;
}
/* 升降序按钮 */
.angle-btn-item {
  position: relative;
  text-align: center;
  color: #5e5e5e;
}
.angle-top {
  content: '';
  width: 0;
  height: 0;
  display: block;
  border-style: solid;
  border-width: 0 5px 5px;
  position: absolute;
  transform: rotate(180deg);
  bottom: 3px;
  right: 34px;
  cursor: pointer;
}
.angle-bottom {
  content: '';
  width: 0;
  height: 0;
  display: block;
  border-style: solid;
  border-width: 0 5px 5px;
  position: absolute;
  top: 3px;
  right: 34px;
  cursor: pointer;
}
.inactive-color {
  border-color: transparent transparent #C0C4CC;
}
.active-color {
  border-color: transparent transparent #1885F2 ;
}