// 获取回溯原因
function openReturnReason(applyNo, e) {
  e.preventDefault();
  e.stopPropagation();
  fetchAjax({
    url: api.getReSaveReasonList,
    data: JSON.stringify({applyNo: applyNo}),
    successFn: function(res) {
      if(res.status == '0') {
        openReasonDialog(res.result || []);
      }
    },
    errorFn: function() {
    }
  })
}

// 打开弹框
function openReasonDialog(result) {
  var tipHtml = '<div>';
  if(!result.length) {
    tipHtml += '<div style="padding: 10px 0">暂无数据</div>';
  } else {
    var html = '<div style="max-height:400px;overflow:auto">';
    for(var i = 0; i < result.length; i++) {
      var item = result[i];
      html += '<div class="reason-box">';
      html += '<div style="font-weight:bold;"><span>'+(item.operateDate || '')+' ' +(item.operateTime || '')+ '</span> <span>'+item.userName+'</span></div>';
      html += '<div style="margin-top:8px;white-space: pre-wrap;word-break: break-all;">'+item.description+'</div>';
      html += '</div>';
    }
    html += '</div>';
    tipHtml += html;
  }
  tipHtml += '<div class="footer" style="text-align: right;padding: 12px 0;">';
  tipHtml += '<button onclick="removeRtDialog()" style="margin-left:8px;background:#fff;padding:5px 15px;border-radius:3px;color:#606266;border:1px solid #DCDFE6;cursor:pointer;">关闭</button>';
  tipHtml += '</div>';
  tipHtml += '</div>';
  drawDialog({
    title: '回溯原因',
    modal: true,
    content: tipHtml,
    style: {
      'width': '500px',
    }
  });
}
$(function(){
  var reqDeptName = getUrlParam('reqDeptName');
  var reqDeptCode = getUrlParam('reqDeptCode');
  console.log(reqDeptCode);
  var reqPhysician = getUrlParam('reqPhysician')
  var optId = getUrlParam('optId')
  var optName = getUrlParam('optName')
  $("#reqSelect").html('<option value="'+reqDeptCode+'">'+reqDeptName+'</option>');
  if(reqPhysician) {
    $("#Physician").html('<option value="">请选择</option>')
    $("#Physician").html('<option value="'+reqPhysician+'" selected>'+reqPhysician+'</option>')
  } else {
    $("#Physician").html('<option value="">请选择</option>')
  }
  var data = [];
  var sickId = '';//病人ID
  var applyNo = '';//申请单号
  var inpatientNo = '';//住院号
  var outpatientNo = '';//门诊号
  var name = '';//患者姓名
  var reqDate = '';//申请时间
  var gmSampleStatus = '';//状态
  var param = {
    reqDept: reqDeptName
  };
  var sourcePrefixUrl = location.href.indexOf('/sreport') > -1 ? '/sreport' : '';
  var gmSampleStatusMap = {
    '10': '已离体',   
    '20': '已固定', 
    '30': '已预检（打印条码）', 
    '40': '已采集（粘贴条码）', 
    '50': '已打包', 
    '60': '已送出', 
    '62': '已转运',   
    '64': '已送达', 
    '70': '已接收',   
    '80': '已退回', 
  }
  // 状态（数据渲染）
  for(k in gmSampleStatusMap){
    $('#gmSampleStatus').append('<option value="'+k+'">'+gmSampleStatusMap[k]+'</option>')
  }

  layui.use(['laydate', 'table'], function(){
    var laydate = layui.laydate;
    var table = layui.table;
    var tableData = [];
    
    //执行一个laydate实例
    laydate.render({
      elem: '#test1',//指定元素
      type: 'date',
      value: new Date()
    });
    //执行渲染
    table.render({
      elem: '#test' //指定原始表格元素选择器（推荐id选择器）
      ,height: 700 //容器高度
      ,page: false
      ,limit: Number.MAX_VALUE
      ,cols: [
        [
          {type:'checkbox', title: '',width:60},
          {type:'numbers',title: '序号',width:60, event:'singleclick'},
          {field:'applyNo', title: '申请单号', event:'singleclick', templet: function(item){
            return String(item.applyNo) || '';
          }},
          {field:'sickId',title: '病人ID', event:'singleclick'},
          {field:'name',title:'姓名', event:'singleclick'},
          {field:'gmSampleNames',title:'标本名称',width:180, event:'singleclick'},
          {field:'reqPhysician',title:'申请医生', event:'singleclick'},
          {field:'reqDeptName',title:'申请科室', event:'singleclick'},
          {field:'reqDate',title:'申请时间', event:'singleclick'},
          {field:'clinDiag',title:'临床诊断', event:'singleclick'},
          {field:'itemNames',title:'检查项目', width:200, event:'singleclick'},
          {field:'outpatientNo',title:'门诊号', event:'singleclick'},
          {field:'inpatientNo',title:'住院号', event:'singleclick'},
          {field:'gmSampleStatus',title:'状态', event:'singleclick', templet: function(item){
            return gmSampleStatusMap[item.gmSampleStatus] || '';
          }},
          {field:'reSaveApplyReason',title:'操作', event:'', templet: function(item){
            return '<span class="ope-txt" onclick="openReturnReason(\''+item.applyNo+'\', event)">回溯原因</span>';
          }}
        ]
      ], //设置表头
      data: tableData ? tableData : [],
      done: function (res, cur, count, params) {
        $('[lay-id="test"] .layui-table-body tr').addClass('not-checked').removeClass('checked');
        $('[lay-id="test"] [data-field="0"]').addClass('not-checked');
        $('[lay-id="test"] [data-field="1"]').addClass('not-checked');
      },
      CSS: '.layui-table-cell{height:auto;overflow:visible;text-overflow:inherit;white-space:normal;word-break: break-all;}'
    });

    function getFilterParams() {
      var one = $("#selectStages").val()
      if(one === '1'){
        sickId = $("#entering").val()
      }else if(one === '2'){
        applyNo = $("#entering").val()
      }else if(one === '3'){
        inpatientNo = $("#entering").val()
      }else if(one === '4'){
        outpatientNo = $("#entering").val()
      }
      name = $("#nameing").val()//患者姓名
      reqDate = $("#test1").val()//申请时间
      reqPhysician = $("#Physician").val()//申请医生
      reqDept = $('#reqSelect').val()//申请科室
      gmSampleStatus = $('#gmSampleStatus').val()//状态
      param = {
        sickId: sickId || '',
        applyNo: applyNo || '',
        inpatientNo: inpatientNo || '',
        outpatientNo: outpatientNo || '',
        name: name || '',
        reqDept: reqDept || '',
        reqDate: reqDate || '',
        reqPhysician: reqPhysician || '',
        gmSampleStatus: gmSampleStatus || '',
      }
    }

    function reloadTableData() {
      getFilterParams()
      fetchAjax({
        url: api.getGmApplyList,
        data: JSON.stringify(param),
        successFn: function(res) {
          if(res.status == '0') {
            tableData = res.result || [];
          }
        },
        completeFn: function() {
          table.reload('test',{
            data: tableData,
          });
        }
      })
    }
    
    reloadTableData();

    // 导出
    $(".Batchexports").on('click', function(){
      var checkStatus = table.checkStatus('test');
      if(!checkStatus.data || checkStatus.data.length === 0){
        $wfMessage({content: '请选择要导出的数据'})
        return;
      }
      $('[lay-id="test"]').table2excel({
        exclude: ".not-checked",
        filename: "申请列表",
        fileext: '.xls'
      })
    })
    // 搜索
    $(".search").on('click',function(){
      reloadTableData()
    })
    table.on('checkbox(test)', function(obj){
      if(obj.checked) {
        // 全选
        if(obj.type === 'all') {
          $('[lay-id="test"]').find('tr').addClass('checked').removeClass('not-checked');
        } else {
          $(obj.tr).addClass('checked').removeClass('not-checked');
        }
      } else {
        if(obj.type === 'all') {
          $('[lay-id="test"]').find('tr').addClass('not-checked').removeClass('checked');
        } else {
          $(obj.tr).addClass('not-checked').removeClass('checked');
        }
      }
    });
    // 单击行
    table.on('tool(test)', function(obj){
      if(obj.event === 'singleclick') {
        var applyLocalData = {
          busId: obj.data.applyNo,
          busType: "2",
          entryType: "0",
          optId: optId,
          optName: optName,
          viewId: "2"
        };
        var applyKey = '2_'+obj.data.applyNo+'_1_2';
        getOrSetCurReportLocal('set', applyLocalData, applyKey)
        var url = sourcePrefixUrl + '/#/srPage?frontendParam='+encryptFun(applyKey);
        window.open(url, obj.data.applyNo)
      }
    });
    
    // 打印
    $('.printApply').on('click',function(){
      var checkStatus = table.checkStatus('test');
      if(!checkStatus.data || checkStatus.data.length === 0){
        $wfMessage({content: '请选择要打印的数据'})
        return;
      }
      var printH = '';
      $(".print-table tbody").html('');
      for(var i = 0; i < checkStatus.data.length; i++) {
        var checkItem = checkStatus.data[i];
        printH += '<tr>';
        printH += '<td>'+checkItem.applyNo+'</td>';
        printH += '<td>'+checkItem.sickId+'</td>';
        printH += '<td>'+checkItem.name+'</td>';
        printH += '<td>'+checkItem.gmSampleNames+'</td>';
        printH += '<td>'+checkItem.reqPhysician+'</td>';
        printH += '<td>'+checkItem.reqDeptName+'</td>';
        printH += '<td>'+checkItem.reqDate+'</td>';
        printH += '<td>'+checkItem.clinDiag+'</td>';
        printH += '<td>'+checkItem.itemNames+'</td>';
        printH += '<td>'+checkItem.outpatientNo+'</td>';
        printH += '<td>'+checkItem.inpatientNo+'</td>';
        printH += '<td>'+(gmSampleStatusMap[checkItem.gmSampleStatus] || '')+'</td>';
        printH += '</tr>';
      }
      $(".print-table tbody").html(printH);
      $(".print-table").show();
      $(".sqlb").hide();
      window.print();
      setTimeout(function() {
        $(".print-table").hide();
        $(".sqlb").show();
      }, 50)
    })
  });
  // 申请医生列表
  function getDictUsers() {
    fetchAjax({
      url: api.getDictUsers,
      data: JSON.stringify({deptCode: reqDeptCode}),
      successFn: function(res) {
        if(res.status == '0') {
          var data = res.result || [];
          for(var i = 0; i<data.length; i++){
            $("#Physician").append('<option value="'+data[i]+'">'+data[i]+'</option>')
          }
        }
      }
    })
  }
  getDictUsers();

})




