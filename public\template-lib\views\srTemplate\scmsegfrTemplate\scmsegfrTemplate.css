/* 宽度 */
.wd-224 {
  width: 224px!important;
}
.wd-352 {
  width: 352px!important;
}
/* 间距 */
.ml-8 {
  margin-left: 8px;
}
.ml-28 {
  margin-left: 28px;
}
.mb-4 {
  margin-bottom: 4px;
}
.mb-8 {
  margin-bottom: 8px;
}
.mt-8 {
  margin-top: 8px;
}
.pt-4 {
  padding-top: 4px;
}
.pt-8 {
  padding-top: 8px;
}

#scmsegfg1 {
  width: 100%;
  min-height: 100%;
  font-family: "宋体";
  font-size: 16px;
  color: #000;
  background: #F5F7FA;
  overflow: auto;
}
#scmsegfg1 input[type="text"], #scmsegfg1 .inp-sty {
  width: 416px;
  height: 28px;
  background: #FFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  padding: 0 15px 0  11px;
  font-size: 16px;
}
#scmsegfg1 input[type="radio"],#scmsegfg1 input[type="checkbox"] {
  vertical-align: middle;
}
#scmsegfg1 .textarea-sty {
  width: 696px;
  height: 84px;
  resize: vertical;
}
#scmsegfg1 .item-box {
  padding: 8px 24px;
}
#scmsegfg1 .row-item {
  display: flex;
  margin-bottom: 5px;
}
#scmsegfg1 .con-flex {
  display: flex;
}
#scmsegfg1 .flex-item {
  flex: 1;
}
#scmsegfg1 .item-tit {
  font-weight: bold;
  margin-bottom: 8px;
}
#scmsegfg1 .item-lb {
  display: inline-block;
  width: 96px;
  text-align: right;
}
#scmsegfg1 .bor-b {
  border-bottom: 1px solid #C0C4CC;
}
#scmsegfg1 .scmsegfg-edit .showInt {
  position: relative;
  background: #fff;
  height: 28px;
}
#scmsegfg1 .scmsegfg-edit .showInt::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
#scmsegfg1 .jcjg-table {
  border: 1px solid #C0C4CC;
  border-bottom: none;
  border-right: none;
  table-layout: fixed;
  background: #fff;
}
#scmsegfg1 .jcjg-table thead {
  width: 56px;
  height: 48px;
  line-height: 48px;
  font-weight: bold;
  color: #000;
}
#scmsegfg1 .jcjg-table tr {
  height: 48px;
  line-height: 28px;
  text-align: center;
}
#scmsegfg1 .jcjg-table tr th, tr td{
  border: 1px solid #C0C4CC;
}


#scmsegfg1 .scmsegfg-edit .item-lb {
  width: unset!important;
}
.addImg {
  position: relative;
  min-width: 76px;
  height: 28px;
  line-height: 72px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  transition: transform 0.2s;
  font-size: 20px;
  cursor: pointer;
  text-align: center;
}
#scmsegfg1 #imageInput {
  height: 28px;
  width: 76px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  font-size: 0;
  opacity: 0;
  cursor: pointer;
}
#scmsegfg1 .add-btn-img {
  position: absolute;
  left: 0;
  width: 76px;
  line-height: 28px;
}
#scmsegfg1 .add-btn-img span {
  vertical-align: text-bottom;
}
#scmsegfg1 .preview-img {
  width: 316px;
  height: 180px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
#scmsegfg1 .jcwd-img {
  /* display: flex; */
  display: none;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 11px;
}
#scmsegfg1 .jcwd-inp {
  width: 300px;
  height: 28px;
  line-height: 28px;
  background: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #C0C4CC;
  font-size: 16px;
  text-align: center;
  margin-top: 8px;
}
#scmsegfg1 .jcwd-txt {
  text-align: center;
}
/* 动态样式 */
#scmsegfg1 .preview {
  width: 360px;
  margin-right: 8px;
  margin-bottom: 4px;
  position: relative;
}
#scmsegfg1 .preview img {
  width: 360px;
  height: 180px;
  border-radius: 6px;
  border: 1px solid #C0C4CC;
  position: relative;
}
#scmsegfg1 .preview .delImg {
  width: 18px;
  height: 18px;
  position: absolute;
  top: 4px;
  right: 4px;
  color: #fff;
  font-size: 10px;
  line-height: 14px;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  background: #E64545;
  border-radius: 50%;
  z-index: 1;
}
/* 预览 */
[isview="true"] #scmsegfg1 .scmsegfg-edit {
  display: none;
}
[isview="true"] #scmsegfg1 .scmsegfg-view {
  display: flex;
}
#scmsegfg1 .scmsegfg-view {
  display: none;
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 32px 56px 220px 56px;
  flex-direction: column;
  position: relative;
}
#scmsegfg1 .scmsegfg-view .view-head {
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #999;
  padding-bottom: 12px;
  text-align: center;
}
#scmsegfg1 .scmsegfg-view .logo-tit {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 98px;
}
#scmsegfg1 .scmsegfg-view .logo-tit img {
  width: 68px;
  height: 68px;
  margin-right: 28px;
}
#scmsegfg1 .scmsegfg-view .blh-tit {
  display: flex;
  align-items: end;
  justify-content: flex-end;
  line-height: 22px;
  margin-left: 6px;
}
#scmsegfg1 .scmsegfg-view .hos-tit{
  font-size: 32px;
  color: #000;
  line-height: 45px;
  font-weight: bold;
  margin-bottom: 8px;
}
#scmsegfg1 .scmsegfg-view .sub-tit{
  font-size: 24px;
  line-height:32px;
}
#scmsegfg1 .scmsegfg-view .view-patient {
  padding-bottom: 8px;
  border-bottom: 1px solid #999;
}
#scmsegfg1 .scmsegfg-view .gray-txt {
  color: #000;
  font-size: 16px;
  white-space: pre-wrap;
  word-break: break-all;
}
#scmsegfg1 .scmsegfg-view .black-txt {
  color: #000;
  font-size: 16px;
}
#scmsegfg1 .scmsegfg-view .info-i {
  width: 210px;
  display: flex;
  flex-wrap: wrap;
}
#scmsegfg1 .scmsegfg-view .info-i + .info-i {
  margin-left: 8px;
}
#scmsegfg1 .scmsegfg-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#scmsegfg1 .scmsegfg-view .view-patient .p-item {
  margin-top: 8px;
}
#scmsegfg1 .scmsegfg-view .desc-con .gray-txt {
  line-height: 22px;
  flex: 1;
}
#scmsegfg1 .scmsegfg-view .desc-con {
  display: flex;
  align-items: baseline;
  padding-top: 8px;
}
#scmsegfg1 .scmsegfg-view .desc-tit {
  width: 96px;
  text-align: right;
}
#scmsegfg1 .scmsegfg-view .yz-tip {
  margin-left: 96px;
  margin-bottom: 20px;
}
#scmsegfg1 .scmsegfg-view .bold {
  font-weight: bold;
}
#scmsegfg1 .scmsegfg-view .rpt-img-ls {
  display: none;
}
#scmsegfg1 .scmsegfg-view .item-img {
  width: 220px;
  height: 165px;
  border: 1px solid #eee;
  margin-top: 12px;
}
#scmsegfg1 .scmsegfg-view .item-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#scmsegfg1 .scmsegfg-view .jcjg-table thead,#scmsegfg1 .scmsegfg-view .jcjg-table tr {
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  font-weight: bold;
}
#scmsegfg1 .scmsegfg-view .rt-sr-footer {
  position: absolute;
  bottom: 30px;
  left: 56px;
  right: 56px;
}
#scmsegfg1 .scmsegfg-view .report-wrap {
  min-height: 48px;
  padding: 3px 0;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}
#scmsegfg1 .scmsegfg-view .reporter-i {
  /* flex: 1; */
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  align-items: center;
}
#scmsegfg1 .scmsegfg-view .reporter-i img {
  /* width: 64px; */
  height: 40px;
  object-fit: contain;
}
#scmsegfg1 .scmsegfg-view .tip-wrap {
  display: flex;
  margin-top: 8px;
}
#scmsegfg1 .scmsegfg-view .flex-column {
  margin: 8px auto;
  width: 520px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  margin-top: 10px;
}
#scmsegfg1 .scmsegfg-view .preview-img-ls {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0 0 54px;
  justify-content: center;
}
#scmsegfg1 .scmsegfg-view .preview-img-ls .preview-item-img {
  width: 360px;
  height: 180px;
  margin-right: 25px;
  margin-bottom: 20px;
}
#scmsegfg1 .scmsegfg-view .preview-img-ls .preview-item-img img {
  width: 360px;
  height: 180px;
  /* object-fit: contain; */
}
#scmsegfg1 .scmsegfg-view .report-img {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0!important;
  margin-top: 0!important;
}
#scmsegfg1 .scmsegfg-view .report-img img {
  width: 360px;
  height: 180px;
  display: block;
  /* object-fit: contain; */
}
/* 调整分页样式 */
#scmsegfg1 .scmsegfg-view .jcwd-txt {
  margin-bottom: 210px;
}
#scmsegfg1 .scmsegfg-view .jcjg-table {
  margin-top: 12px;
}