#pacsMgmt1 {
  width: 100%;
  height: 100%;
  font-size: 16px;
  background: #F5F7FA;
  overflow: auto;
}
.box-p{
  padding: 12px 24px;
}
.title{
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 12px;
}
#pacsMgmt1 .box-p h4{
  margin-top: 12px;
  font-weight: 600;
}
.in_b{
  display: inline-block;
  margin-right: 10px;
}
.w135{
  width: 135px;
}
.mb-8{
  margin-bottom: 8px;
}
.ml-14{
  margin-left: 14px;
}
#pacsMgmt1 .layui-inline input{
  position: relative;
  background: transparent;
  z-index: 10;
  padding: 0 16px;
}
#pacsMgmt1 .showInt {
  background: #fff;
}
#pacsMgmt1 .showInt::after{
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  right: 7px;
  top: 50%;
  z-index: 9;
  border-top: 1px solid #606266;
  border-right: 1px solid #606266;
  transform: rotate(135deg);
  margin-top: -5px;
}
.in-sly{
  height: 36px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 16px;
}
.br-t{
  width: 100%;
  height: 1px;
  background: #C0C4CC;
}
.w-120{
  width: 120px;
}
.w-calc{
  width: calc(100% - 85px);
  background: #fff;
}
.ver-t{
  vertical-align: top;
  line-height: 36px;
}
.dw-80{
  display: inline-block;
  width: 80px;
  text-align: right;
}
#pacsMgmt1 .pacsMgmt-view {
  display: none;
}
[isview="true"] #pacsMgmt1 .pacsMgmt-edit {
  display: none;
}
[isview="true"] #pacsMgmt1 .pacsMgmt-view {
  display: block;
}
/* 预览部分 */
#pacsMgmt1 .pacsMgmt-view {
  width: 780px;
  margin: 0 auto;
  min-height: 1100px;
  background: #fff;
  padding: 30px 56px 20px;
}
#pacsMgmt1 .pacsMgmt-view .view-head {
  position: relative;
  border-bottom: 1px solid #999;
  padding-bottom: 24px;
  text-align: center;
  position: relative;
}
#pacsMgmt1 .pacsMgmt-view .pat-wrap {
  position: absolute;
  right: 0;
  bottom: 8px;
  font-size: 14px;
}
#pacsMgmt1 .pacsMgmt-view .page-tit{
  font-size: 24px;
  font-weight: 600;
  /* letter-spacing: 8px; */
}
#pacsMgmt1 .pacsMgmt-view .view-patient {
  padding: 8px 0;
  border-bottom: 1px solid #999;
}
#pacsMgmt1 .pacsMgmt-view .bl-r{
  position: absolute;
  right: 0;
  bottom: 8px;
}
#pacsMgmt1 .pacsMgmt-view .gray-txt {
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-all;
}
#pacsMgmt1 .pacsMgmt-view .gray-txt .bold{
  font-weight: 600;
}
#pacsMgmt1 .pacsMgmt-view .black-txt {
  color: #000;
  font-size: 14px;
}
#pacsMgmt1 .pacsMgmt-view .bold {
  font-weight: bold;
}
#pacsMgmt1 .pacsMgmt-view .info-i {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
}
#pacsMgmt1 .pacsMgmt-view .info-i + .info-i {
  padding-left: 4px;
}
#pacsMgmt1 .pacsMgmt-view .info {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}
#pacsMgmt1 .pacsMgmt-view .view-patient .p-item+.p-item {
  margin-top: 8px;
}
#pacsMgmt1 .pacsMgmt-view .report-wrap {
  padding: 8px 0;
  border-bottom: 1px solid #999;
  display: flex;
  justify-content: space-between;
}
#pacsMgmt1 .pacsMgmt-view .reporter-i {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}
#pacsMgmt1 .pacsMgmt-view .reporter-i [data-key] {
  flex: 1;
}
#pacsMgmt1 .pacsMgmt-view .reporter-i img {
  width: 100px;
  height: 48px;
  object-fit: contain;
}
#pacsMgmt1 .pacsMgmt-view .reporter-i + .reporter-i {
  margin-left: 8px;
}
#pacsMgmt1 .pacsMgmt-view .tip-wrap {
  margin-top: 8px;
}
.g-w{
  display: inline-block;
  width: 70px;
  position: relative;
}
[entry-type="5"] #pacsMgmt1{
  background: #fff;
  padding: 0;
}
[entry-type="5"] #pacsMgmt1 .pacsMgmt-view {
  width: 100%;
  min-height: auto;
  margin: unset;
  padding: 12px;
}
[entry-type="5"] #pacsMgmt1 .pacsMgmt-view .view-head,
[entry-type="5"] #pacsMgmt1 .pacsMgmt-view .rt-sr-header .view-patient,
[entry-type="5"] #pacsMgmt1 .pacsMgmt-view .tip-wrap {
  display: none;
}
[entry-type="5"] #pacsMgmt1 .pacsMgmt-view div {
  border-bottom: none;
}
[entry-type="5"] .reporter-i {
  flex: unset !important;
}
