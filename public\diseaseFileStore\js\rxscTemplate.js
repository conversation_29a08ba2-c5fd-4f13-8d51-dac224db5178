$(function() {
  window.initHtmlScript = initHtmlScript;
  window.errorCallBack = errorCallBack;
})

var vueComp = null;
var curElem = null;
var rtStructure = null;
var aiValAndSrMap = {
  '椭圆': ['椭圆形'],
  '不规则': ['不规则形'],
  '大分叶': ['分叶状'],
  '平行': ['与皮肤平行'],
  '不平行': ['不与皮肤平行'],
  '清晰': ['边界清楚'],
  '不清': ['边界不清楚'],
  '粗大钙化': ['有钙化'],
  '混合钙化': ['有钙化'],
  '微小钙化': ['微钙化', '有钙化'],
  '囊性混合回声': ['囊性', '混合回声'],
  '无回声透声差': ['无回声', '透声差'],
  '无回声透声好': ['无回声', '透声好'],
  '无回声透声尚可': ['无回声', '透声尚可'],
}
// 错误反馈回调处理
function errorCallBack(id) {
  var curDom = $('[id="'+id+'"]').parents('.rt-tabs');
  if(curDom && curDom.length) {
    var key = id.split('.')[0] + '.001';
    vueComp.activeTab = key;
  }
}
function initVueComp() {
  vueComp = new Vue({
		el: '#vuePg',
		data() {
			return {
        bzTl: [  //病灶图例
          { color: '#303133', levelText: '0', fontColor: '#FFF'},
          { color: '#40C71A', levelText: '2', fontColor: '#000', sameColor: ['2', '3']},
          { color: '#40C71A', levelText: '3', fontColor: '#000', isHide: true},
          { color: '#F3F055', levelText: '4A', fontColor: '#000'},
          { color: '#F5222D', levelText: '4B', fontColor: '#000', sameColor: ['4B', '4C', '5', '6']},
          { color: '#F5222D', levelText: '4C', fontColor: '#000', isHide: true},
          { color: '#F5222D', levelText: '5', fontColor: '#000', isHide: true},
          { color: '#F5222D', levelText: '6', fontColor: '#000', isHide: true},
        ],
        bzLevel: [],
        bzList: [],  //具体病灶情况
        bzPaneList: [],
        activeTab: '',
        bzDescription: []
			}
		},
    computed: {
      hasBzFlag() {
        let level = this.bzTl.map(item => item.levelText);
        this.bzLevel = level;
        return this.bzList.some(item => item.bzDesc && level.includes(item.levelText));
      }
    },
		methods: {
      // 获取病灶的信息
      getBzListInfo(isUpdate) {
        this.$nextTick(() => {
          this.bzList = [];
          !isUpdate && (this.activeTab = '');
          let pane = curElem.find('.pane-title');
          if(pane && pane.length) {
            pane.each((i, paneItem) => {
              let prefix = $(paneItem).attr('data-pre');
              if(!this.activeTab) {
                this.activeTab = prefix + '.001'
              }
              let item = getBzDetail(curElem, prefix);
              item.showDetail = false;
              item.showPointDetail = false;
              let desc = `病灶${i+1}：BI-RADS:${item.levelText}类；${item.propText}\n`;
              if(item.posWay === '象限定位法') {
                desc += `处于：${item.xxPos || '-'}；\n`;
              } else {
                desc += `点/距乳头(cm)：${item.clockPos || '-'}/${item.diffRt || '-'}；\n`;
              }
              if(item.bzSide) {
                desc += '大小约：' + item.bzSide + '；\n';
              }
              // desc += `大小约：${curElem.find(`[id="${prefix}.001.03.04"] .rt-sr-w`).val()}cm×${curElem.find(`[id="${prefix}.001.03.05"] .rt-sr-w`).val()}cm×${curElem.find(`[id="${prefix}.001.03.06"] .rt-sr-w`).val()}cm；\n`;
              desc += `形态：${item.shape || '-'}；\n`;
              desc += `方向：${item.fx || '-'}；\n`;
              desc += `边缘：${item.by || '-'}；\n`;
              desc += `回声类型：${item.hsType || '-'}`;
              let hsOtherArr = [];
              if(['无回声', '低回声'].indexOf(item.hsType) > -1) {
                if(item.hsBjVal) {
                  hsOtherArr.push(item.hsBjVal);
                }
                if(item.hsTsVal) {
                  hsOtherArr.push(item.hsTsVal);
                }
              }
              if(hsOtherArr.length) {
                desc += `(${hsOtherArr.join('、')})`;
              }
              desc += '；\n';
              desc += `后方回声：${item.hfhs || '-'}；\n`;
              desc += `钙化：${item.isGh || '-'}；\n`;
              desc += `血流：${item.isXl || '-'}；`;
              if(item.pos) {
                if(item.posWay === '象限定位法') {
                  if(item.xxPos) {
                    item.bzDesc = desc;
                  }
                } else {
                  if(item.clockPos && item.diffRt) {
                    item.bzDesc = desc;
                  }
                }
              }
              item.style = {
                background: this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ? 
                  this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].color : '#F5F7FA',
                color: this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0] ? 
                  this.bzTl.filter(bItem => bItem.levelText === item.levelText)[0].fontColor : '#FFF',
                top: '',
                left: '',
              }
              curElem.find('.bz-name').eq(i).text(`病灶${i+1}${item.pos ? '-' + item.pos : ''}`);
              // if(item.pos === '双侧') {
              //   item.pos = curElem.find(`[name="${prefix}.001.03.01"]:checked`).val() || item.pos;
              // }
              this.setPointPosition(item);
              this.bzList.push(item);
            })
          }
          if(this.bzList.length) {
            curElem.find("#bz-area").show();
            inpNumberHandler(curElem);
          } else {
            curElem.find("#bz-area").hide();
          }
          if(rtStructure) {
            rtStructure.bzListInfo = this.bzList;
          }
        })
      },

      // 确定点的位置
      setPointPosition(item) {
        let xxObj = {
          '左侧': {
            '外上象限': {clockPos: 1.5, diffRt: 3.5},
            '外下象限': {clockPos: 4.5, diffRt: 3.5},
            '内上象限': {clockPos: 10.5, diffRt: 3.5},
            '内下象限': {clockPos: 7.5, diffRt: 3.5},
            '上象限': {clockPos: 12, diffRt: 3.5},
            '下象限': {clockPos: 6, diffRt: 3.5},
            '内象限': {clockPos: 9, diffRt: 3.5},
            '外象限': {clockPos: 3, diffRt: 3.5},
          },
          '右侧': {
            '外上象限': {clockPos: 10.5, diffRt: 3.5},
            '外下象限': {clockPos: 7.5, diffRt: 3.5},
            '内上象限': {clockPos: 1.5, diffRt: 3.5},
            '内下象限': {clockPos: 4.5, diffRt: 3.5},
            '上象限': {clockPos: 12, diffRt: 3.5},
            '下象限': {clockPos: 6, diffRt: 3.5},
            '内象限': {clockPos: 3, diffRt: 3.5},
            '外象限': {clockPos: 9, diffRt: 3.5},
          }
        }
        //clockPos点的方向, diffRt距离乳头的距离即半径
        let {pos, clockPos, diffRt, posWay = '时钟定位法', xxPos} = item;
        if(posWay === '象限定位法') {
          if(pos && xxPos && xxObj[pos] && xxObj[pos][xxPos]) {
            clockPos = xxObj[pos][xxPos]['clockPos'];
            diffRt = xxObj[pos][xxPos]['diffRt'];
          }
        } else {
          if(Number(diffRt) === 99) {
            diffRt = 3.5;
          }
        }
        
        if(!pos || !clockPos || !diffRt) {
          return;
        }
        // let Ox = pos === '左侧' ? 219 : 79;
        let Ox = pos === '左侧' ? 225 : 81;
        let Oy = 64;  //圆心x和y
        let degreesM = 360/(12*60); //每分钟占的度数
        let totalDegree = degreesM * clockPos * 60;  //总的度数，点(时)->分
        let angle = (2*Math.PI / 360) *(totalDegree);
        let x = Ox + Math.sin(angle) * (diffRt * 7+11);
        let y = Oy - Math.cos(angle) * (diffRt * 7+11);
        item.style.left = x.toFixed(2) + 'px';
        item.style.top = y.toFixed(2) + 'px';
      },

      // 删除病灶
      delTab(e) {
        this.$confirm('确认删除该病灶?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let tabTitle = $(e.target).closest('.el-tabs__item');
          let tabIndex = tabTitle.index();
          let tabContent = tabTitle.parents('#bz-area').find('.el-tabs__content').find('.rt-pane').eq(tabIndex);
          let curAct = tabTitle.attr('aria-controls').replace('pane-', '')
          if(this.activeTab === curAct && this.bzList.length >= 2) {
            let siblingEle = tabTitle.next().length ? tabTitle.next() : tabTitle.prev();
            let actName = siblingEle.attr('aria-controls').replace('pane-', '');
            this.activeTab = actName;
          }
          tabTitle.remove();
          tabContent.remove();
          this.getBzListInfo(true);
          bzDetailHandler();
        })
      },

      // 添加病灶
      addBzHandler(aiData) {
        var html = $(window.rxbzTemplate).find('.t-content').html();
        var flagId = 'rxsc-bz-' + createUUidFun();
        html = html.replace(/rxsc-0003\./g, flagId + '.');
        html = html.replace(/RG-0003\./g, flagId + '.');
        html = html.replace(/data-pre="rxsc-panel"/g, 'data-pre="'+flagId+'"');
        this.bzPaneList.push({
          key: flagId,
          content: html
        })
        this.getBzListInfo(true);
        this.$nextTick(() => {
          initLigthItemChange(curElem);
          this.activeTab = flagId + '.001';

          // 兼容导出数据用到的判断
          if(rtStructure) {
            // var rtStructure = window.findCurStructure(curElem[0], window.instanceList)
            if(rtStructure.idAndDomMap['']) {
              delete rtStructure.idAndDomMap[''];
            }
            rtStructure.idAndDomMap[flagId + '.001'] = {
              id: flagId + '.001',
              desc: '病灶',
              name: '病灶title',
              pageId: rtStructure.idAndDomMap['rxsc-0001'].pageId,
              pid: 'rxsc-0003',
              pvf: '',
              req: '1',
              rtScPageNo: 1,
              value: '病灶' + (this.bzPaneList.length),
              wt: '',
              vt: ''
            }
            var wCon = $(html).find("[rt-sc]");
            wCon.each(function(wIndex, wItem) {
              var node = $(wItem);
              var id = node.attr('id') || '';
              var pid = node.attr('pid') || '';
              var sc = node.attr('rt-sc');
              var scArr = sc.split(';');
              var scObj = {
                id: id,
                pid: pid,
                rtScPageNo: 1,
                value: ''
              };
              scArr.forEach(function(scItem) {
                var key = scItem.split(':')[0];
                var value = scItem.split(':')[1];
                if(key) {
                  if(key === 'itemList') {
                    scObj[key] = eval(decodeURIComponent(value));
                    scObj['groupId'] = id;
                  } else if(key === 'code') {
                    scObj[key] = value;
                    var findCodeNode = $('[id="'+id+'"] .rt-sr-w');
                    findCodeNode.attr('code', value);
                    if(aiData && aiData.length) {
                      for(var k = 0; k < aiData.length; k++) {
                        var aiItem = aiData[k];
                        if(aiItem['code'] === value) {
                          var setValFlag = false;
                          findCodeNode.each(function(j, dom) {
                            if($(dom).hasClass('rt-sr-r') || $(dom).hasClass('rt-sr-ck')) {
                              if(!setValFlag && ($(dom).val() === aiItem['val'] || 
                                (aiValAndSrMap[aiItem['val']] && aiValAndSrMap[aiItem['val']].indexOf($(dom).val()) > -1))) {
                                rtStructure.setFormItemValue(dom, $(dom).val())
                                scObj.value = aiItem['val'];
                                setValFlag = true;
                              }
                            } else {
                              rtStructure.setFormItemValue(dom, aiItem['val'])
                              scObj.value = aiItem['val'];
                            }
                          })
                          break;
                        }
                      }
                    }
                  }else {
                    var numberList = ['left', 'top', 'wt'];
                    scObj[key] = numberList.indexOf(key) > -1 ? Number(value) : decodeURIComponent(value);
                  }
                }
              })
              if(scObj.itemList) {
                scObj.itemList.forEach(function(itemChild) {
                  rtStructure.idAndDomMap[itemChild.id] = {
                    ...scObj,
                    id: itemChild.id,
                    isItemList: true
                  }
                })
              }
              rtStructure.idAndDomMap[id] = {...scObj};
            })
            rtStructure.init(true);
            rtStructure.initChildDisabled(curElem.find('.rt-pane:eq('+(this.bzList.length-1)+')')[0]);
            curElem.find('.rt-pane:eq('+(this.bzList.length-1)+')').find('.level-select').hide();
            toggleHightBlock(curElem)
            document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollLeft = document.querySelector("#bz-tabs .el-tabs__nav-scroll").scrollWidth;
          }
        })
      }
		}
	})
}
function initHtmlScript(ele) {
  curElem = $(ele);
  // 来自报告统计
  if(window.isRptStatistics) {
    let cnotent = curElem.find('#bz-tabs .rt-pane:first-child')[0].innerHTML;
    curElem.find('#bz-tabs .rt-pane:first-child').remove();
    curElem.find('#addBzBtn').remove();
    curElem.find('.el-icon-close').remove();
    window.rxbzTemplate = '<el-tab-pane class="rt-pane" name="rxsc-0003.001">' + cnotent + '</el-tab-pane>';
  }
  initVueComp();
  vueComp.$nextTick(() => {
    if(window.findCurStructure) {
      rtStructure = window.findCurStructure(ele, window.instanceList)
    }
    window.addBzHandler = vueComp.addBzHandler;
    if(!curElem.find('.t-pg').attr('hasEdited')) {
      vueComp.addBzHandler();
    }
    toggleBlock();
    // 初始化浅色和深色块的显示关系
    toggleHightBlock(curElem);
    initLigthItemChange(curElem);
    // 初始化病灶
    initBzTemplate();
    // 病灶位置控制
    // toggleBzSide();
    // 整合病灶的实际描述
    curElem.find("#bz-area, .lbj-wrap").on('change', '.rt-sr-w', function() {
      bzDetailHandler();
    })
    window.addEventListener('keydown', function(e) {
      if (e.keyCode == "13") {
        $(e.target).trigger("click");
      }
    })
    newLightBlockHandler();
    showLbDataWrap();
  })
}

function bzDetailHandler() {
  var isNormal = curElem.find('[name="RG-0001.001"]:checked').val();
  var isView = curElem.find(".t-pg").attr('isView');
  var description = [];
  var impression = '';
  if(isNormal === '可见异常') {
    var allBz = {};
    var pane = curElem.find('.rt-pane');
    var over2Level = false;   //出现超过2类
    var over2LevelList = ['3', '4A', '4B', '4C', '5', '6'];
    pane.each(function(i, dom) {
      var prefix = $(dom).attr('id').split('.')[0].replace('pane-', '');
      var bzItem = getBzDetail($(dom), prefix);
      var html = '';
      var side = bzItem.pos;
      // 勾选了哪侧及等级才进行描述
      if(side && bzItem.levelText) {
        var moreType = bzItem.propText || '';  //性质
        var level = bzItem.levelText || '';
        var type = bzItem.bzCount;  //单多发
        if(!over2Level && over2LevelList.includes(level)) {
          over2Level = true;
        }
        var xzText = $(dom).find('.selBz').val();
        var obj = {
          template: xzText,  //模板
          moreType: moreType, //性质
          type: type, //单多发
          level: level, //等级
          // side: side === '左侧' && lSide > 0 ? 'left' : (side === '右侧' && rSide > 0 ? 'right' : side),
          side: side,
          impText: html,
          sort: i + 1,
          doubleSide: '',
          ...bzItem
        };
        description.push(obj);  //用于描述
        if(allBz[bzItem.levelText]) {
          var sameLevelIndex = allBz[bzItem.levelText].findIndex(item => !item.sameFlag && item.moreType === moreType && item.side === side);
          if(sameLevelIndex > -1) {
            obj.sameFlag = true;  //表已存在相同级别同性质的病灶，后续不需要单独描述
            allBz[bzItem.levelText][sameLevelIndex].moreFlag = true;  //描述为多发
          }
          allBz[bzItem.levelText].push(obj);
        } else {
          allBz[bzItem.levelText] = [obj];
        }
      }
    })
    var impTextArr = [];
    if(JSON.stringify(allBz) !== '{}') {
      var order = ['6', '5', '4C', '4B', '4A', '3', '2', '0'];
      var yTxt = '';  //余字
      var has4Level = false;
      for(var i = 0; i < order.length; i++) {
        var key = order[i];
        if(allBz[key]) {
          for(var j = 0; j < allBz[key].length; j++) {
            var item = allBz[key][j];
            if(item.sameFlag || item.diffSideFlag) {
              continue;
            }
            if(['0', '2', '3'].indexOf(item.levelText) === -1) {
              var html = '<span class="bold-txt">'+item.side+'</span>';
              if(item.type || item.moreType) {
                var xxPos = item.posWay === '象限定位法' ? item.xxPos : item.clockPos + '点'
                html += '<span class="bold-txt">'+ (!item.moreFlag ? xxPos : '') +(item.moreFlag || item.type === '多发' ? '多发' : '')+item.moreType +'</span>';
              }
              html += '病灶，';
              html += 'BI-RADS <input class="rt-sr-t" readonly style="width:50px" value="'+item.level+'"> 类<i></i>';
              impTextArr.push(html);
              has4Level = true;
            }else {
              var html = '';
              if(has4Level && !yTxt) {
                yTxt = '余';
                html += yTxt;
              }
              // 不同侧同级
              var diffLevelIndex = allBz[key].findIndex(all => !all.sameFlag && !all.diffSideFlag && all.moreType === item.moreType && all.side !== item.side);
              if(diffLevelIndex > -1) {
                var diffSideItem = allBz[key][diffLevelIndex];
                allBz[key][diffLevelIndex]['diffSideFlag'] = true;
                item.diffSide = diffSideItem.side;
                item.hasDiffBz = true;   //存在不同侧同级的数据
                if(diffSideItem.type === '多发' || diffSideItem.moreFlag) {
                  item.diffCount = '多发';
                }
              }
              var diffSideText = '';
              html += '<span class="bold-txt">'+(item.hasDiffBz ? '双乳' : item.side)+'</span>';
              if(item.diffCount || item.type || item.moreType) {
                var bzTypeCount = item.moreFlag || item.type === '多发' ? '多发' : '';  //单多发
                var curItemCount = bzTypeCount;
                if(item.hasDiffBz) {
                  bzTypeCount = '';
                  if(item.diffCount === curItemCount) {
                    bzTypeCount = item.diffCount || '';
                  } else {
                    if(item.diffCount === '多发') {
                      diffSideText = '('+item.diffSide+item.diffCount+')';
                    } 
                    
                    if(curItemCount === '多发') {
                      diffSideText = '('+item.side+curItemCount+')';
                    }
                  }
                }
                html += '<span class="bold-txt">'+ bzTypeCount+item.moreType +'</span>';
              }
              html += '病灶'+diffSideText+'，';
              html += 'BI-RADS <input class="rt-sr-t" readonly style="width:50px" value="'+item.level+'"> 类<i></i>';
              impTextArr.push(html);

            }
          }
        }
      }
    }
    if(impTextArr.length) {
      impression += impTextArr.join('；') + '\n'
    }
    var lbText = '';
    lbText += getLbImpHtml('0004');
    if(lbText) {
      impression += lbText + '\n';
    }

    // 特殊情况
    let specialText = curElem.find(`[id="rxsc-0014.01"] .rt-sr-w:not(.rt-hide)`).val()
    if(specialText) {
      impression += `<span class="bold-txt">${specialText}</span>；\n`;
    }

    // 出现2类以上的诊断，超声诊断最后需要加⼀句：建议结合其他检查
    if(over2Level) {
      impression += `<span class="bold-txt">建议结合其他检查。</span>`;
    }
    //结论 --end
  } else {
    impression = '<span class="bold-txt">双侧乳腺未见明显异常声像，</span>';
    impression += 'BI-RADS：<input class="rt-sr-t" readonly style="width:50px" value="'+(curElem.find('[name="RG-0011.001"]:checked').val() || '')+'"> 类, ';
    impression += '<span class="bold-txt">请结合临床</span>；';
    var lbText2 = '';
    lbText2 += getLbImpHtml('0010');
    if(lbText2) {
      impression += '\n'+lbText2;
    }
    if(isView === 'true') {
      // 血流情况
      var xlDesc = curElem.find('[name="RG-0009.01"]:checked').val();
      if(xlDesc === '有血流') {
        var xlCount = curElem.find('[name="RG-0009.01.01"]:checked').val();
        var xlPos = [];
        for(var i = 1; i < 4; i++) {
          if(curElem.find('[id="rxsc-0009.01.02-cItem--'+i+'"]').is(":checked")) {
            xlPos.push(curElem.find('[id="rxsc-0009.01.02-cItem--'+i+'"]').attr('value'));
          }
        }
        if(curElem.find(`[id="rxsc-0009.01.03"] .rt-sr-w`).text()) {
          xlPos.push(curElem.find(`[id="rxsc-0009.01.03"] .rt-sr-w`).text())
        }
        if(xlCount || xlPos.length > 0) {
          xlDesc += '(';
          if(xlCount) {
            xlDesc += xlCount + '；';
          }
          xlDesc += xlPos.join('、');
          xlDesc += ')';
        }
      }
      curElem.find(".xl-desc").html(xlDesc);
    }
  }
  // 淋巴情况
  getLbDescHtml(isNormal === '可见异常' ? '0004' : '0010', '.lbj-wrap')
  impression = impression.replace(/<ty><\/ty>/ig, '');
  if(rtStructure) {
    rtStructure.impression = impression;
  }
  
  // 排序
  // var orderList = ['左侧', 'left', '右侧', 'right']
  // description = description.sort(function(a, b) {
  //   a.type = a.type === '<ty></ty>' ? '' : a.type;
  //   b.type = b.type === '<ty></ty>' ? '' : b.type;
  //   return orderList.indexOf(a.side) - orderList.indexOf(b.side);
  // })
  if(rtStructure) {
    rtStructure.description = description;
  }
  vueComp.bzDescription = description;
}
// 获取淋巴结结论
function getLbImpHtml(lbKey) {
  var lbMap = {
    '0004': '0100',
    '0010': '0101',
  }
  var arr = [];
  for(var i = 1; i < 5; i++) {
    if($('[id="rxsc-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').is(":checked")) {
      var lbName = $('[id="rxsc-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').val();
      for(var j = 1; j < 3; j++) {
        var str = '';
        if($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'-cItem--1"]').is(":checked")) {
          var lbVal = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.01"]:checked').val() || '';
          if(!lbVal || lbVal === '其他') {
            str = `<span class="bold-txt">${j==1?'左侧':'右侧'}</span>${lbName}淋巴结可见异常；`;
          } else {
            str = `<span class="bold-txt">${j==1?'左侧':'右侧'}</span>${lbName}淋巴结转移<span class="bold-txt">${lbVal}</span>；`;
          }
        }
        if(str) {
          arr.push(str);
        }
      }
    }
  }
  return arr.join('');
}
// 获取淋巴结的描述内容
function getLbDescHtml(lbKey, domClass) {
  var lbMap = {
    '0004': '0100',
    '0010': '0101',
  }
  var arr = [];   
  var otherAttrKeys = ['04', '05', '09', '06', '07'];
  var otherAttrObj = {};
  var otherAttrArr = [];
  for(var i = 1; i < 5; i++) {
    if($('[id="rxsc-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').is(":checked")) {
      var isNormal = $('[name="RG-'+lbKey+'.00'+i+'"]:checked').val() || '';  //是否异常
      var lbName = $('[id="rxsc-'+lbMap[lbKey]+'.001-cItem--'+i+'"]').val();
      if(isNormal === '未见异常') {
        arr.push(lbName + '淋巴结未见异常。');
      } else {
        var sideChecked = false;
        for(var j = 1; j < 3; j++) {
          if($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'-cItem--1"]').is(":checked")) {
            sideChecked = true;
            var side = j==1?'左侧':'右侧';
            var lb = side + lbName;
            var strArr = [];
            var lbVal = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.01"]:checked').val() || '';  //转移
            var count = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.10"]:checked').val() || '';  //单发/多发
            if(!lbVal || lbVal === '其他') {
              strArr.push(`可见${count==='多发'?count:''}淋巴结`);
            } else {
              strArr.push(`可见${count==='多发'?count:''}淋巴结转移${lbVal}`);
            }
            var side = [];  //大小
            if($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'.02"] .rt-sr-w').val()) {
              side.push($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'.02"] .rt-sr-w').val() + 'cm');
            }
            if($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'.03"] .rt-sr-w').val()) {
              side.push($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'.03"] .rt-sr-w').val() + 'cm');
            }
            if($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'.08"] .rt-sr-w').val()) {
              side.push($('[id="rxsc-'+lbKey+'.00'+i+'.0'+j+'.08"] .rt-sr-w').val() + 'cm');
            }
            if(side.length) {
              strArr.push(`${count==='多发'?'大者':'大小'}${side.join(' x ')}`);
            }
            var str = strArr.join('，');
            var otherTar = [];
            for(var k = 0; k < otherAttrKeys.length; k++) {
              var tar = otherAttrKeys[k];
              var value = $('[name="RG-'+lbKey+'.00'+i+'.0'+j+'.'+tar+'"]:checked').val() || '';
              if(!value || (value === '无' && tar === '05')) {
                continue;
              }
              if(['09', '06'].indexOf(tar) > -1) {
                otherTar.push(`${tar=='09'?'边缘':'淋巴门'}` + value);
              } else {
                otherTar.push(value);
              }
            }
            if(otherTar.length) {
              var tarStr = otherTar.join('，');
              if(otherAttrArr.indexOf(tarStr) === -1) {
                otherAttrArr.push(tarStr)
              }
              if(str) {
                otherAttrObj[tarStr] ? otherAttrObj[tarStr].push(lb + str) : otherAttrObj[tarStr] = [lb + str];
              }
            } else {
              arr.push(lb + str);
            }
          }
        }
        if(!sideChecked) {
          arr.push(lbName + '淋巴结可见异常。');
        }
      }
    }
  }
  for(var key in otherAttrObj) {
    if(otherAttrObj[key].length > 1) {
      arr.push(otherAttrObj[key].join('，') + `，上述结节` + key + '。');
    } else {
      arr.push(otherAttrObj[key] + '，' + key + '。')
    }
  }
  var lbStr = arr.join('\n');
  var isView = curElem.find(".t-pg").attr('isView');
  $('#lb-desc').text(lbStr);
  if(isView === 'true') {
    curElem.find(domClass).html(lbStr);
    curElem.find(domClass).css({
      'whiteSpace':'pre-wrap',
      'flex':'1'
    });
  }
}
// 获取所有病灶的字段
function getBzDetail(dom, prefix) {
  var byTypes = [];
  for(var i = 1; i < 5; i++) {
    if(dom.find(`[id="${prefix}.001.06.01.01-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      byTypes.push(dom.find(`[id="${prefix}.001.06.01.01-cItem--${i}"]:not(.rt-hide)`).attr('value'));
    }
  }
  var obj = {
    propText: dom.find(`[name="${prefix}.001.13.01"]:checked:not(.rt-hide)`).val() || '',  //性质
    pos: dom.find(`[name="${prefix}.001.02.01"]:checked:not(.rt-hide)`).val(),
    bzCount: dom.find(`[name="${prefix}.001.15.01"]:checked:not(.rt-hide)`).val(),  //多发/单发
    levelText: dom.find(`[name="${prefix}.001.14.01"]:checked:not(.rt-hide)`).val() || dom.find(`[id="${prefix}.001.14.02"] .rt-sr-w:not(.rt-hide)`).val() || '',  //等级
    clockPos: dom.find(`[id="${prefix}.001.03.02"] .rt-sr-w:not(.rt-hide)`).val() || '',  //位于几点方向
    diffRt: dom.find(`[id="${prefix}.001.03.03"] .rt-sr-w:not(.rt-hide)`).val() || '',  //距乳头距离
    posOtherDesc: dom.find(`[id="${prefix}.001.03.07"] .rt-sr-w:not(.rt-hide)`).val() || '',  //位于-其他情况
    bzSide: '',
    // bzSide: dom.find(`[id="${prefix}.001.03.04"] .rt-sr-w:not(.rt-hide)`).val() + 'cm x ' + dom.find(`[id="${prefix}.001.03.05"] .rt-sr-w`).val() + 'cm x ' + dom.find(`[id="${prefix}.001.03.06"] .rt-sr-w`).val() + 'cm',  //病灶大小
    shape: dom.find(`[name="${prefix}.001.04.01"]:checked:not(.rt-hide)`).val() || '',  //形状
    fx: dom.find(`[name="${prefix}.001.05.01"]:checked:not(.rt-hide)`).val() || '',  //方向
    by: dom.find(`[name="${prefix}.001.06.01"]:checked:not(.rt-hide)`).val() || '',  //边缘
    byType: byTypes.length ? byTypes.join('、') : '',  //边缘情况
    hsType: dom.find(`[name="${prefix}.001.07.01"]:checked:not(.rt-hide)`).val() || '',  //回声类型
    hsBjVal: dom.find(`[name="${prefix}.001.07.01.01"]:checked:not(.rt-hide)`).val() || dom.find(`[name="${prefix}.001.07.01.03"]:checked:not(.rt-hide)`).val() || '',  //回声类型-边界
    hsTsVal: dom.find(`[name="${prefix}.001.07.01.02"]:checked:not(.rt-hide)`).val() || '',  //回声类型-透声
    hfhs: dom.find(`[name="${prefix}.001.08.01"]:checked:not(.rt-hide)`).val() || '',  //后方回声
    isGh: dom.find(`[name="${prefix}.001.09.01"]:checked:not(.rt-hide)`).val() || '',  //是否钙化
    ghCount: dom.find(`[name="${prefix}.001.09.01.01"]:checked:not(.rt-hide)`).val() || '',  //钙化数量
    isXl: dom.find(`[name="${prefix}.001.10.01"]:checked:not(.rt-hide)`).val() || '',  //是否有血流
    xlCount: dom.find(`[name="${prefix}.001.10.01.01"]:checked:not(.rt-hide)`).val() || '',  //血流量
    // specialText: dom.find(`[id="${prefix}.001.12.01"] .rt-sr-w:not(.rt-hide)`).val() || '',  //特殊情况
    otherDesc: dom.find(`[id="${prefix}.001.16.01"] .rt-sr-w:not(.rt-hide)`).val() || '',  //其他征象
    posWay: dom.find(`[name="${prefix}.001.03.00"]:checked:not(.rt-hide)`).val(),  //定位法
    xxPos: dom.find(`[name="${prefix}.001.03.10"]:checked:not(.rt-hide)`).val(),  //象限位置
  }
  let sizeTxt = [];
  var decimLen = 0;
  var startIdx = obj.posWay === '象限定位法' ? 11 : 4;
  var endIdx = obj.posWay === '象限定位法' ? 14 : 7;
  for(var bIdx = startIdx; bIdx < endIdx; bIdx++) {
    var bI = bIdx < 9 ? `0${bIdx}` : bIdx;
    var sizeVal = dom.find(`[id="${prefix}.001.03.${bI}"] .rt-sr-w:not(.rt-hide)`).val();
    if(sizeVal) {
      var [num, decim = ''] = sizeVal.split('.');
      if(decim && decimLen < decim.length) {
        decimLen = decim.length;
      }
    }
  }
  for(var bIdx = startIdx; bIdx < endIdx; bIdx++) {
    var bI = bIdx < 9 ? `0${bIdx}` : bIdx;
    var sizeVal = dom.find(`[id="${prefix}.001.03.${bI}"] .rt-sr-w:not(.rt-hide)`).val();
    if(sizeVal) {
      var value = sizeVal;
      if(decimLen > 0) {
        var [num, decim = ''] = sizeVal.split('.');
        var len = decimLen - decim.length;
        if(len > 0) {
          var fillText = new Array(len).fill('0').join('');
          value = num + '.' + decim + fillText;
        }
      }
      sizeTxt.push(`${value}cm`);
    }
  }
  if(sizeTxt.length) {
    obj.bzSide = sizeTxt.join(' x ');
  }
  // 相关特征
  var relateDescList = [];
  if(dom.find(`[name="${prefix}.001.11.01"]:checked:not(.rt-hide)`).val() === '有') {
    relateDescList.push('结构扭曲');
  }
  if(dom.find(`[name="${prefix}.001.11.02"]:checked:not(.rt-hide)`).val() === '有') {
    if(dom.find(`[id="${prefix}.001.11.02.01"] .rt-sr-w:not(.rt-hide)`).val()) {
      relateDescList.push('导管改变(' + dom.find(`[id="${prefix}.001.11.02.01"] .rt-sr-w`).val() + ')');
    } else {
      relateDescList.push('导管改变');
    }
  }
  if(dom.find(`[name="${prefix}.001.11.03"]:checked:not(.rt-hide)`).val() === '有') {
    if(dom.find(`[id="${prefix}.001.11.03.01-cItem--1"]`).is(":checked") || dom.find(`[id="${prefix}.001.11.03.01-cItem--2"]`).is(":checked")) {
      var pf = [];
      if(dom.find(`[id="${prefix}.001.11.03.01-cItem--1"]`).is(":checked")) {
        pf.push('皮肤增厚');
      }
      if(dom.find(`[id="${prefix}.001.11.03.01-cItem--2"]`).is(":checked")) {
        pf.push('皮肤回缩');
      }
      relateDescList.push(pf.join('、'))
    } else {
      relateDescList.push('皮肤改变');
    }
  }
  if(dom.find(`[name="${prefix}.001.11.04"]:checked:not(.rt-hide)`).val() === '有') {
    relateDescList.push('水肿');
  }
  if(dom.find(`[name="${prefix}.001.11.05"]:checked:not(.rt-hide)`).val() || dom.find(`[id="${prefix}.001.11.06"] .rt-sr-w:not(.rt-hide)`).val()) {
    var str = '应变弹性评估：';
    var arr = [];
    if(dom.find(`[name="${prefix}.001.11.05"]:checked`).val()) {
      arr.push(dom.find(`[name="${prefix}.001.11.05"]:checked`).val() + '分');
    }
    if(dom.find(`[id="${prefix}.001.11.06"] .rt-sr-w`).val()) {
      arr.push('应变比：' + dom.find(`[id="${prefix}.001.11.06"] .rt-sr-w`).val());
    }
    relateDescList.push(str + arr.join('，'));
  }
  if(dom.find(`[name="${prefix}.001.11.07"]:checked:not(.rt-hide)`).val()) {
    relateDescList.push('剪切波弹性成像：' + dom.find(`[name="${prefix}.001.11.07"]:checked`).val());
  }
  obj.relateText = relateDescList.join('；');  //相关特征

  // 钙化位置
  var ghPos = [];
  for(var i = 1; i < 4; i++) {
    if(dom.find(`[id="${prefix}.001.09.01.02-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      ghPos.push(dom.find(`[id="${prefix}.001.09.01.02-cItem--${i}"]`).attr('value'));
    }
  }
  obj.ghPos = ghPos.join('、');

  // 钙化形态
  var ghType = [];
  for(var i = 1; i < 5; i++) {
    if(dom.find(`[id="${prefix}.001.09.01.03-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      ghType.push(dom.find(`[id="${prefix}.001.09.01.03-cItem--${i}"]`).attr('value'));
    }
  }
  obj.ghType = ghType.join('、');

  // 血流位置
  var xlPos = [];
  for(var i = 1; i < 4; i++) {
    if(dom.find(`[id="${prefix}.001.10.01.02-cItem--${i}"]:not(.rt-hide)`).is(":checked")) {
      xlPos.push(dom.find(`[id="${prefix}.001.10.01.02-cItem--${i}"]`).attr('value'));
    }
  }
  if(dom.find(`[id="${prefix}.001.10.01.03"] .rt-sr-w`).text()) {
    xlPos.push(dom.find(`[id="${prefix}.001.10.01.03"] .rt-sr-w`).text())
  }
  obj.xlPos = xlPos.join('、');
  return obj;
}

// 相关内容的显示/隐藏
function toggleBlock(fromEdit) {
  var csPerform = $('[name="RG-0001.001"]:checked').val();  //超声表现
  if(!csPerform) {
    $('[id="rxsc-0001.001-rItem--2"]').attr('checked', true);
  }
  if(csPerform === '未见异常') {
    $(".normalArea").show();
    $(".normalArea").find('.rt-sr-w').removeClass('rt-hide');
    $(".abnormalArea").hide();
    $(".abnormalArea").find('.rt-sr-w').addClass('rt-hide');
  } else {
    $(".abnormalArea").show();
    $(".abnormalArea").find('.rt-sr-w').removeClass('rt-hide');
    $(".normalArea").hide();
    $(".normalArea").find('.rt-sr-w').addClass('rt-hide');
    if(curElem.find('.t-pg').attr('hasEdited')) {
      vueComp.getBzListInfo();
    }
  }
  if(fromEdit === true) {
    bzDetailHandler();
  }
}

// 初始化时显示隐藏病灶的双侧对应左右选项 --暂无用到
// function toggleBzSide(){
//   $(".t-content").find(".bz-pos:checked").each(function(i, item) {
//     bzSideChange(item, true)
//   });
// }

// // 选择双侧病灶时显示位于左右单选项 --暂无用到
// function bzSideChange(vm, isFirst) {
//   var value = $(vm).val();
//   var doubleSide = $(vm).closest(".t-content").find('.double-side')
//   if(value === '双侧') {
//     doubleSide.show();
//     doubleSide.find('.rt-sr-w').removeClass('rt-hide');
//   } else {
//     doubleSide.hide();
//     doubleSide.find('.rt-sr-w').removeAttr("checked").addClass('rt-hide');
//   }
//   if(!isFirst) {
//     vueComp.getBzListInfo(true);
//   }
// }
// 初始化模板的内容
function initBzTemplate() {
  curElem.find('.rt-pane .selBz').each(function(i, el) {
    bzTempChange(el, true)
  })
  bzDetailHandler();
}

// 切换模板
function bzTempChange(node, isFirst) {
  var value = $(node).val();
  var type = $(node).find('option[value="'+value+'"]').attr('data-type');  //simple complex RADS3  vicious
  var noShowTemp = $(node).closest('.rt-pane').find('[data-temp]');  //不显示的模块
  var parentNode = $(node).closest(".t-content");
  noShowTemp.each(function(i, temp) {
    $(temp).show();
    $(temp).find('.rt-sr-w').removeClass('rt-hide');
    var tempStr = $(temp).attr('data-temp');
    if(tempStr.indexOf(type) > -1 || (!type && !tempStr)) {
      $(temp).hide();
      $(temp).find('.rt-sr-w').addClass('rt-hide');
    }
  })
  // 手动切换则赋相关表单对应值
  if(!isFirst) {
    parentNode.find(".level-select .rt-sr-w").val('');
    if(['simple', 'complex', 'RADS3'].includes(type)) {
      parentNode.find('[name$=".001.04.01"][value="椭圆形"]').prop("checked", true);
    } else if(['vicious'].includes(type)) {
      parentNode.find('[name$=".001.04.01"][value="不规则形"]').prop("checked", true);
    }
    if(['vicious'].includes(type)) {
      parentNode.find('[name$=".001.05.01"][value="不与皮肤平行"]').prop("checked", true);
      parentNode.find('[name$=".001.06.01"][value="不光整"]').prop("checked", true);
      parentNode.find('[parent-id$=".001.06.01-rItem--2"]').removeAttr('disabled');
    } else {
      parentNode.find('[name$=".001.05.01"][value="与皮肤平行"]').prop("checked", true);
      parentNode.find('[name$=".001.06.01"][value="光整"]').prop("checked", true);
      parentNode.find('[parent-id$=".001.06.01-rItem--2"]').attr('disabled', true).prop("checked", false);
    }
    // 回声类型
    if(['vicious', 'RADS3'].includes(type)) {
      parentNode.find('[name$=".001.07.01"][value="低回声"]').click();
    } else if(['complex'].includes(type)) {
      parentNode.find('[name$=".001.07.01"][value="混合回声"]').click();
    } else {
      parentNode.find('[name$=".001.07.01"][value="无回声"]').click();
    }
    // 后方回声
    if(['vicious'].includes(type)) {
      parentNode.find('[name$=".001.08.01"][value="衰减"]').prop("checked", true);
    } else {
      parentNode.find('[name$=".001.08.01"][value="增强"]').prop("checked", true);
    }
    // 性质
    if(['complex'].includes(type)) {
      parentNode.find('[name$=".001.13.01"][value="囊实性"]').prop("checked", true);
      parentNode.find('[name$=".001.13.01.02"]').removeAttr('disabled');
      parentNode.find('[name$=".001.13.01.01"]').attr('disabled', true).prop("checked", false);
    } else if(['simple'].includes(type)) {
      parentNode.find('[name$=".001.13.01"][value="囊性"]').prop("checked", true);
      parentNode.find('[name$=".001.13.01.01"]').removeAttr('disabled');
      parentNode.find('[name$=".001.13.01.02"]').attr('disabled', true).prop("checked", false);
    } else {
      parentNode.find('[name$=".001.13.01"][value="实性"]').prop("checked", true);
      parentNode.find('[name$=".001.13.01.02"]').attr('disabled', true).prop("checked", false);
      parentNode.find('[name$=".001.13.01.01"]').attr('disabled', true).prop("checked", false);
    }
    // BI-RADS分类
    if(['complex', 'RADS3'].includes(type)) {
      parentNode.find('[name$=".001.14.01"][value="3"]').prop("checked", true);
    } else if(['simple'].includes(type)) {
      parentNode.find('[name$=".001.14.01"][value="2"]').prop("checked", true);
    } else {
      parentNode.find('[name$=".001.14.01"]').prop("checked", false);
    }
    // 有无钙化和血流
    parentNode.find('[id$=".001.09.01.03-cItem--1"]').prop('checked', false);
    if(['complex', 'vicious'].includes(type)) {
      parentNode.find('[id$=".001.09.01-rItem--2"]').click();
      parentNode.find('[id$=".001.10.01-rItem--2"]').click();
      if(['vicious'].includes(type)) {
        parentNode.find('[id$=".001.09.01.03-cItem--1"]').prop('checked', true);
      }
    } else if(['RADS3'].includes(type)) {
      parentNode.find('[id$=".001.09.01-rItem--2"]').click();
      parentNode.find('[id$=".001.10.01-rItem--1"]').click();
    }
    
    // 相关特征
    if(['vicious'].includes(type)) {
      parentNode.find('.relat-box [type="radio"][value="有"]').click();
      parentNode.find('.relat-box [id$=".001.11.03.01-cItem--2"]').prop("checked", true);
    } else {
      parentNode.find('.relat-box [type="radio"][value="无"]').click();
    }
  }
}

// 调整病灶情况
function updateBzInfo(node, type) {
  if(type === "otherType" || type === "levelType") {
    var noShowTemp = $(node).closest('.rt-pane')
    if(type === "otherType") {
      var selectVal = noShowTemp.find(".level-select .rt-sr-w").val();
      if(selectVal) {
        noShowTemp.find('.level-radio [type="radio"]').prop("checked", false);
      } else {
        var type = noShowTemp.find('.selBz option:selected').attr('data-type');
        if(['complex', 'RADS3'].includes(type)) {
          noShowTemp.find('[name$=".001.14.01"][value="3"]').prop("checked", true);
        } else if(['simple'].includes(type)) {
          noShowTemp.find('[name$=".001.14.01"][value="2"]').prop("checked", true);
        } else {
          noShowTemp.find('[name$=".001.14.01"]').prop("checked", false);
        }
      }
    } else if(type === "levelType") {
      noShowTemp.find(".level-select .rt-sr-w").val('');
    }
  } 
  vueComp.$nextTick(() => {
    vueComp.getBzListInfo(true);
  })
}

function changeXlVal(vm, radioName) {
  if($(vm).is(":checked")) {
    $('[name="'+radioName+'"][value="可见血流"]').prop("checked", true)
  }
}

// 特殊处理距离乳头的距离
function inpJLRTNumber(vm) {
  var numReg = /^(\d+)\.?(\d*)$/ig;
  var inpVal = $(vm).val().trim();
  if(inpVal === '') {
    return;
  }
  if(inpVal[inpVal.length - 1] === '.') {
    $(vm).val(inpVal.replace('.', ''));
  }
  var value = Number($(vm).val().trim());
  var min = $(vm).attr('min');
  var max = $(vm).attr('max');
  if(!numReg.test(value)) {
    $(vm).val(min || '');
  } else {
    if(value !== 99) {
      if(max !== undefined && value > Number(max)) {
        $(vm).val(max);
      }
      if(min !== undefined && value < Number(min)) {
        $(vm).val(min);
      }
    }
  }
}
var isFillInStatus = {};
function newLightBlockHandler() {
  $('.lb-new input').click(function(e) {
    var id = $(this).attr('id');
    if(isFillInStatus[id]) {
      $(this).prop('checked', false);
    }
    if(!$(this).is(':checked')) {
      delete isFillInStatus[id];
      $(this).parents().find('.hight-block').hide();
    }
  })
  $('.lb-new').click(function(e) {
    var target = $(this).find('input');
    var id = target.attr('id');
    if(isFillInStatus[id]) {
      e.preventDefault();
    }
    $(this).addClass('lb-new-act');
    $(this).siblings().removeClass('lb-new-act');
    var pid = target.attr("parent-id");
    var wblock = target.parents('.w-block');
    var hightBlock = wblock.find('.new-block[light-id="'+pid+'"]');
    var hightItem = wblock.find('.new-item[contect-id="'+id+'"]');
    hightBlock.show();
    if(!isFillInStatus[id] && target.is(':checked')) {
      $(hightItem).find('.def-ck').click();
    }
    if(target.is(':checked')) {
      isFillInStatus[id] = '1';
    }
    $(this).parents(".lbj-wrap").find('.new-item').hide();
    hightItem.show();
  })
}

// 回显淋巴结的数据
function showLbDataWrap() {
  var lbjWrap = $(".lbj-wrap");
  var hightCk = lbjWrap.find('.lb-new .rt-sr-w');
  var hightCked = lbjWrap.find('.lb-new .rt-sr-w:checked');
  if(!hightCked || hightCked.length===0) {
    $(".ywck").click();
    var id = $(".ywck").attr('id');
    isFillInStatus[id] = '1';
  } else {
    hightCked.each(function(i, dom) {
      if($(dom).is(":checked")) {
        var id = $(dom).attr('id');
        isFillInStatus[id] = '1';
        if(i === 0) {
          $(dom).parents('.lb-new').click();
        }
      }
    })
  }
}
