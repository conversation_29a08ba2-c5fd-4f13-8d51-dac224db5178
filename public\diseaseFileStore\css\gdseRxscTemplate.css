.gdseRxscP {
  padding: 8px 0;
  padding-bottom: 0;
  border-left: 1px solid #C8D7E6;
  border-right: 1px solid #C8D7E6;
  height: 100%;
}
.gdseRxscP .mr-8 {
  margin-right: 8px;
}
#gdseRxscP1 {
  position: relative;
  padding-bottom: 110px;
}
#gdseRxscP1 .sm-text{
  height: 28px;
  width: 40px;
  padding: 4px 2px;
}
#gdseRxscP1 .rt-sr-tit { width: 110px; }
#gdseRxscP1 .sketch-map { 
  background: #fff;
  /* display: flex;
  align-items: center; */
  width: 325px;
  height: 177px;
}
#gdseRxscP1 .map-img {
  position: relative;
  flex: 1;
  width: 320px;
  height: 140px;
}
#gdseRxscP1 .map-img img {
  width: 100%;
  height: 100%;
}
#gdseRxscP1 .map-color {
  padding-left: 8px;
  display: flex;
  align-items: center;
  border-top: 1px solid #C8D7E6;
  padding-top: 8px;
}
#gdseRxscP1 .map-color .map-tl {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
  border-radius: 4px;
  padding: 4px;
  height: 20px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
#gdseRxscP1 .map-color .map-tl + .map-tl {
  margin-left: 4px;
}
#gdseRxscP1 .map-color .c-pot {
  width: 12px;
  height: 12px;
}
#gdseRxscP1 .c-pot {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 1px solid #000;
  border-radius: 50%; 
  line-height: 1;
  text-align: center;
  font-size: 12px;
  z-index: 3;
}
#gdseRxscP1 .c-pot[data-style="mediumn"] {
  width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 18px;
  z-index: 2;
}
#gdseRxscP1 .c-pot[data-style="mediumn"].c-angle {
  border-left-width: 14px;
  border-right-width: 14px;
  border-bottom-width: 28px;
}
#gdseRxscP1 .c-pot[data-style="mediumn"].c-angle .ft {
  margin-left: -5px;
  margin-top: 3px;
}
#gdseRxscP1 .c-pot[data-style="large"] {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 24px;
  z-index: 1;
}
#gdseRxscP1 .c-pot[data-style="large"].c-angle {
  border-left-width: 20px;
  border-right-width: 20px;
  border-bottom-width: 40px;
}
#gdseRxscP1 .c-pot[data-style="large"].c-angle .ft {
  margin-left: -6px;
  margin-top: 5px;
}
#gdseRxscP1 .c-angle {
  border-radius: 0;
  border-left: 7px transparent solid;
  border-right: 7px transparent solid;
  border-bottom: 14px #303133 solid;
  border-top: 0 transparent solid;
  background: none !important;
}
#gdseRxscP1 .map-color .c-angle {
  border-bottom-width: 12px;
}
#gdseRxscP1 .c-pot.abs {
  position: absolute;
  cursor: pointer;
}
#gdseRxscP1 .c-pot:not(.c-angle).abs:hover,
#gdseRxscP1 .c-pot:not(.c-angle).abs.act {
  border-color: #FFF443;
}
#gdseRxscP1 .c-pot.c-angle.abs:hover,
#gdseRxscP1 .c-pot.c-angle.abs.act {
  border-bottom-color: #000;
}
#gdseRxscP1 .c-angle .ft{
  text-align: left;
  margin-left: -3px;
  margin-top: 2px;
  display: block;
}
#gdseRxscP1 .num-flag {
  color: #000;
  font-size: 12px;
  margin-left: 4px;
}
#gdseRxscP1 .con-i {
  color: #303133;
  cursor: pointer;
  line-height: 22px;
}
#gdseRxscP1 .con-i:hover, #gdseRxscP1 .con-i.act {
  color: #1885F2;
}
#gdseRxscP1 .s-map-con { 
  flex: 1;
  padding: 8px;
  overflow: auto;
  word-break: break-all;
  height: 177px;
  border-left: 1px solid #C8D7E6;
}
.tooltip-wrap {
  line-height: 20px;
  white-space: pre-wrap;
}
/* #gdseRxscP1 .hide-in{display: none;} */
#gdseRxscP1 .rxmb-hd{
  background: #F5F7FA;
  border-top: 1px solid #C8D7E6;
  border-bottom: 1px solid #C8D7E6;
  height: 40px;
  display: flex;
  align-items: center;
  color: #303133;
  margin-top: 12px;
}
#gdseRxscP1 .rxmb-hd .rxmb-hd-i{
  padding: 0 12px;
  height: 100%;
  line-height: 40px;
  border-right: 1px solid #C8D7E6;
}
#gdseRxscP1 .rxmb-hd .rxmb-hd-i .rt-sr-w{
  width: unset !important;
  padding-top: 0 !important;
  text-align: left !important;
}
#gdseRxscP1 .rxmb-hd .rxmb-hd-i{
  cursor: pointer;
}
#gdseRxscP1 .rxmb-hd .rxmb-hd-i:hover{
  opacity: 0.7;
}
#gdseRxscP1 .rxmb-hd .rxmb-hd-i.act{
  background: #fff;
  color: #1885F2;
  font-weight: bold;
  position: relative;
}
#gdseRxscP1 .rxmb-hd .rxmb-hd-i.act::after{
  position: absolute;
  content: '';
  bottom: -1px;
  height: 2px;
  width: 100%;
  left: 0;
  background: #fff;
}
#gdseRxscP1 .rxmb-detail{
  padding: 8px;
  display: none;
}
#gdseRxscP1 .bz-item {
  display: flex;
  width: 100%;
}
#gdseRxscP1 .bz-item + .bz-item {
  margin-top: 16px;
}
#gdseRxscP1 .bz-side {
  width: 100px;
  text-align: right;
  color: #303133;
  font-weight: bold;
}
#gdseRxscP1 .bz-detail {
  flex: 1;
}
#gdseRxscP1 .bz-title {
  color: #000;
  margin-bottom: 8px;
}
#gdseRxscP1 .bz-table {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
}
#gdseRxscP1 .bz-table-head {
  padding: 4px 8px;
  background: #EBEEF5;
  border-bottom: 1px solid #C8D7E6;
  color: #000;
}
#gdseRxscP1 .bz-table-body {
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
}
#gdseRxscP1 .bz-info {
  margin: 3px 0;
  margin-right: 30px;
  color: #000;
}
.hs-block.w-block {
  display: block !important;
} 
.hs-block.w-block .hight-block{
  border-top: 1px solid #C8D7E6;
  border-left: none;
}
.hs-block.w-block .light-block {
  padding: 4px 8px;
}
.hs-block.w-block .lb-row.lb-active > input:checked::after,
.hs-block.w-block .lb-row.lb-active > input:checked::before {
  content: unset !important;
}
.hs-block.w-block .light-block .w-con:not(.v) .r-i + .r-i {
  margin-left: 0;
}
.level-radio.w-con:not(.v) .r-i + .r-i {
  margin-left: 0;
}
.level-radio.w-con {
  padding-left: 15px;
  background: #FFFFFF;
  border: 1px solid #DCDFE6;
  height: 28px;
  border-radius: 3px 0 0 3px;
}
.level-radio.w-con .r-i {
  margin-right: 15px;
}
.level-select select.rt-sr-s {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  padding-left: 5px;
}
.rxscSe-wrap *:focus {
  box-shadow: 0 0 7px 1px rgb(24,133,242,.7)!important;
}     
.rxscSe-wrap label:not(.lb-row) [type="radio"]:focus, 
.rxscSe-wrap label:not(.lb-row):not(.lb-new) [type="checkbox"]:focus {
  /* min-height: unset !important; */
  /* height: 14px !important; */
  box-shadow: 0 0 7px 1px rgb(24,133,242,.7)!important;
}
.rxscSe-wrap .lb-d .p-lb.w{
  display: inline-block;
  width: 70px;
  text-align: right;
}
.rxscSe-wrap .lb-d .rt-sr-lb {
  color: #303133;
}
#gdseRxscP1 .p-item + .p-item {
  margin-top: 8px;
}
/* #gdseRxscP1 .rxmb-detail[data-pid="rxscSe-rt-2"] .rt-sr-tit,
#gdseRxscP1 .rxmb-detail[data-pid="rxscSe-rt-3"] .rt-sr-tit {
  width: 130px;
} */
#gdseRxscP1 .w-block .hight-block {
  color: #303133;
}
#gdseRxscP1 .p-lb.w-100 {
  width: 103px;
  text-align: right;
}
#gdseRxscP1 .p-lb.w-86 {
  width: 103px;
  text-align: right;
}
#gdseRxscP1 .w-block .lb-row.lb-active > input:not([type="checkbox"]):checked::after, 
#gdseRxscP1 .w-block .lb-new.lb-new-act > input::after, 
#gdseRxscP1 .w-block .lb-row.lb-active.on > input::after{
  background: #F5F7FA;
}
#gdseRxscP1 .cl-tab {
  flex: 1;
}
#gdseRxscP1 .cl-tab-h {
  display: flex;
}
#gdseRxscP1 .cl-tab-h .cl-tab-i {
  background: #F5F7FA;
  border: 1px solid #C8D7E6;
  color: #303133;
  padding: 3px 12px;
  height: 32px;
  border-bottom: none;
  display: flex;
  cursor: pointer;
  position: relative;
}
#gdseRxscP1 .cl-tab-h .cl-tab-i input[type="checkbox"] {
  min-height: unset;
}
/* #gdseRxscP1 .cl-tab-h .cl-tab-i input[type="radio"] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
} */
#gdseRxscP1 .cl-tab-h .cl-tab-i:hover {
  opacity: 0.8;
}
#gdseRxscP1 .cl-tab-h .cl-tab-i + .cl-tab-i {
  border-left: none;
}
#gdseRxscP1 .cl-tab-h .cl-tab-i.act {
  background: #EBEEF5;
}
#gdseRxscP1 .cl-tab-h .cl-tab-i .rt-sr-tit {
  width: unset;
  min-width: unset;
  padding-top: 0;
}
#gdseRxscP1 .write-flag {
  width: 40px;
  height: 24px;
  line-height: 22px;
  border-radius: 3px;
  font-size: 12px;
  text-align: center;
  margin-left: 5px;
  display: none;
}
#gdseRxscP1 .write-flag.un-write {
  background: #ECF5FF;
  border: 1px solid #B3D8FF;
  color: #1885F2;
}
#gdseRxscP1 .write-flag.has-write {
  background: #F0F9EB;
  border: 1px solid #BCDFA4;
  color: #21A64D;
}
#gdseRxscP1 .cl-tab-con-i[data-cltab] {
  display: none !important;
}
#gdseRxscP1 .cl-tab-con-i[data-cltab].act {
  display: block !important;
}
#gdseRxscP1 .hight-item.hv {
  display: flex;
}
#gdseRxscP1 .spe-box {
  padding: 12px 8px 12px 0;
  border-top: 1px solid #C8D7E6;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
#gdseRxscP1 .spe-box .lb-lg {
  font-size: 18px;
  font-weight: bold;
  margin-right: 5px;
}
#gdseRxscP1 .write-type {
  flex: 1;
  background: #F5F7FA;
}
#gdseRxscP1 .write-type .write-h {
  background: #EBEEF5;
  border-top: 1px solid #C8D7E6;
  border-left: 1px solid #C8D7E6;
  border-right: 1px solid #C8D7E6;
  padding: 3px 8px;
}
#gdseRxscP1 .write-type .write-item {
  display: none;
}
#gdseRxscP1 .write-type .write-item.gray {
  padding: 8px;
  border: 1px solid #C8D7E6;
}
#gdseRxscP1 .write-type .write-item.act {
  display: block;
}
#gdseRxscP1 .frx-wrap {
  border: 1px solid #C8D7E6;
  background: #F5F7FA;
  padding: 12px 8px;
  display: none;
}
#gdseRxscP1 .frx-wrap textarea {
  padding: 4px 0;
}
#gdseRxscP1 .next-wrap .next-item {
  padding: 8px 12px;
  background: #EBEEF5;
  border: 1px solid #C8D7E6;
  margin-top: 8px;
  display: none;
}
#gdseRxscP1 .ty-sel {
  background: #EBEEF5;
  border-bottom: 1px solid #C8D7E6;
  padding: 0 8px;
}
#gdseRxscP1 .ty-con {
  padding: 0 8px;
}
#gdseRxscP1 .other-rx {
  margin-top: 12px;
  display: none;
}
#gdseRxscP1 .other-rx-name {
  font-size: 18px;
  color: #000;
  font-weight: bold;
  margin-bottom: 12px;
}
#gdseRxscP1 .lb-row .rt-sr-lb {
  flex: 1;
}
#gdseRxscP1 .lr-block {
  flex: 1;
}
#gdseRxscP1 .lr-block .tit-block {
  border-bottom: 1px solid #DCDFE6;
  padding: 4px 8px;
}
#gdseRxscP1 .lr-block .by-block {
  padding: 8px;
}
#gdseRxscP1 .lr-block + .lr-block {
  border-left: 1px solid #DCDFE6;
}
#gdseRxscP1 .lb-con .hight-block {
  padding: 8px 2px;
}